import { NextRequest, NextResponse } from 'next/server';
import { handleAPIError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export const runtime = 'nodejs';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: characterId } = params;
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const { sourceSeriesId, targetSeriesId, shareType = 'reference', versionNotes } = body;

    if (!sourceSeriesId || !targetSeriesId) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Verify user owns the source series
    const { data: sourceSeries, error: sourceError } = await supabase
      .from('series')
      .select('id')
      .eq('id', sourceSeriesId)
      .eq('user_id', user.user.id)
      .single();

    if (sourceError || !sourceSeries) {
      return handleAPIError(new AuthorizationError());
    }

    // Verify the character belongs to the source series
    const { data: character, error: charError } = await supabase
      .from('characters')
      .select(`
        id,
        name,
        book:books!inner(
          id,
          series_books!inner(
            series_id
          )
        )
      `)
      .eq('id', characterId)
      .single();

    if (charError || !character) {
      return handleAPIError(new NotFoundError('Resource'));
    }

    // Check if character belongs to source series
    const belongsToSource = character.book.series_books.some(
      (sb: { series_id: string }) => sb.series_id === sourceSeriesId
    );

    if (!belongsToSource) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Create the share using the database function
    const { data: share, error: shareError } = await supabase
      .rpc('share_character_to_series', {
        p_character_id: characterId,
        p_source_series_id: sourceSeriesId,
        p_target_series_id: targetSeriesId,
        p_share_type: shareType,
        p_version_notes: versionNotes
      });

    if (shareError) {
      logger.error('Error sharing character:', shareError);
      return NextResponse.json({ error: 'Failed to share character' }, { status: 500 });
    }

    // Fetch the complete share details
    const { data: shareDetails, error: detailsError } = await supabase
      .from('character_shares')
      .select(`
        *,
        character:characters(
          id,
          name,
          role,
          description
        ),
        source_series:series!character_shares_source_series_id_fkey(
          id,
          title
        ),
        target_series:series!character_shares_target_series_id_fkey(
          id,
          title
        )
      `)
      .eq('id', share)
      .single();

    if (detailsError) {
      logger.error('Error fetching share details:', detailsError);
      return NextResponse.json({ share: { id: share } });
    }

    return NextResponse.json({ share: shareDetails });
  } catch (error) {
    logger.error('Error in POST /api/characters/[id]/share:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: characterId } = params;
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    // Get all shares for this character
    const { data: shares, error } = await supabase
      .from('character_shares')
      .select(`
        *,
        source_series:series!character_shares_source_series_id_fkey(
          id,
          title
        ),
        target_series:series!character_shares_target_series_id_fkey(
          id,
          title
        )
      `)
      .eq('character_id', characterId)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching character shares:', error);
      return NextResponse.json({ error: 'Failed to fetch character shares' }, { status: 500 });
    }

    return NextResponse.json({ shares });
  } catch (error) {
    logger.error('Error in GET /api/characters/[id]/share:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}