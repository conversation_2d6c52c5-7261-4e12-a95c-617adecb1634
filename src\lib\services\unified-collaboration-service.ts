import { BaseService } from './base-service'
import { ServiceResponse } from './types'
import { createClient } from '../supabase/client'
import { createClient as createServerClient } from '../supabase/server'
import type { RealtimeChannel, RealtimePostgresChangesPayload, SupabaseClient } from '@supabase/supabase-js'
import { logger } from './logger'
import { hasRealTimeCollaboration } from '../subscription'
import type { UserSubscription } from '../subscription'
import { conflictResolver, type ConflictResolutionStrategy } from './collaboration-conflict-resolver'

// Unified types for collaboration
export interface CollaborationUser {
  id: string
  name: string
  email: string
  color: string
  role: 'owner' | 'editor' | 'viewer' | 'commenter'
  status: 'online' | 'offline'
  cursor?: {
    line: number
    column: number
  }
  selection?: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
  lastSeen?: number
}

export interface CollaborationSession {
  id: string
  projectId: string
  documentId: string
  ownerId: string
  participants: CollaborationUser[]
  isActive: boolean
  version: number
  conflictStrategy: ConflictResolutionStrategy
  createdAt: Date
  updatedAt: Date
}

export interface CollaborationChange {
  id: string
  sessionId: string
  userId: string
  type: 'insert' | 'delete' | 'replace' | 'format' | 'comment'
  range: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
  text?: string
  data?: Record<string, unknown>
  timestamp: number
  version: number
}

export type CollaborationEventType = 
  | 'user.joined'
  | 'user.left'
  | 'user.updated'
  | 'cursor.moved'
  | 'selection.changed'
  | 'content.changed'
  | 'session.created'
  | 'session.ended'
  | 'lock.acquired'
  | 'lock.released'

export interface CollaborationEvent {
  type: CollaborationEventType
  sessionId: string
  userId: string
  data: unknown
  timestamp: number
}

export interface DocumentLock {
  documentId: string
  section: string
  userId: string
  timestamp: number
  expiresAt: number
}

// Configuration for collaboration features
interface CollaborationConfig {
  enableRealtime: boolean
  conflictStrategy: ConflictResolutionStrategy
  lockTimeout: number
  sessionTimeout: number
  maxParticipants: number
}

/**
 * Unified Collaboration Service
 * 
 * Combines all collaboration functionality into a single service:
 * - Real-time collaboration with Supabase
 * - Conflict resolution
 * - Document locking
 * - Session management
 * - Works in both serverless and server environments
 */
export class UnifiedCollaborationService extends BaseService {
  private static instance: UnifiedCollaborationService
  private channels: Map<string, RealtimeChannel> = new Map()
  private collabConfig: CollaborationConfig = {
    enableRealtime: true,
    conflictStrategy: 'operational-transform',
    lockTimeout: 60000, // 1 minute
    sessionTimeout: 3600000, // 1 hour
    maxParticipants: 10
  }

  constructor() {
    super({
      name: 'UnifiedCollaborationService',
      version: '3.0.0',
      status: 'inactive',
      endpoints: [
        '/api/collaboration/sessions',
        '/api/collaboration/changes',
        '/api/collaboration/presence',
        '/api/collaboration/locks'
      ],
      dependencies: ['supabase'],
      healthCheck: '/api/collaboration/health'
    })
  }

  static getInstance(): UnifiedCollaborationService {
    if (!UnifiedCollaborationService.instance) {
      UnifiedCollaborationService.instance = new UnifiedCollaborationService()
    }
    return UnifiedCollaborationService.instance
  }

  async initialize(): Promise<void> {
    this.isInitialized = true
    this.setStatus('active')
  }

  async shutdown(): Promise<void> {
    // Clean up all channels
    for (const [, channel] of this.channels) {
      await this.supabase?.removeChannel(channel)
    }
    this.channels.clear()
    this.setStatus('inactive')
  }

  // Session Management
  async createSession(
    projectId: string,
    documentId: string,
    ownerId: string,
    ownerInfo: { name: string; email: string }
  ): Promise<ServiceResponse<CollaborationSession>> {
    return this.withErrorHandling(async () => {
      const supabase = this.getSupabaseClient()
      
      // Check subscription for real-time collaboration
      const subscription = await this.getUserSubscription(ownerId)
      if (!hasRealTimeCollaboration(subscription)) {
        throw new Error('Real-time collaboration not available in current subscription')
      }

      const sessionId = `collab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      const session: CollaborationSession = {
        id: sessionId,
        projectId,
        documentId,
        ownerId,
        participants: [{
          id: ownerId,
          name: ownerInfo.name,
          email: ownerInfo.email,
          color: this.generateUserColor(ownerId),
          role: 'owner',
          status: 'online',
          lastSeen: Date.now()
        }],
        isActive: true,
        version: 1,
        conflictStrategy: this.collabConfig.conflictStrategy,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Store in database
      const { error } = await supabase
        .from('collaboration_sessions')
        .insert({
          id: sessionId,
          project_id: projectId,
          document_id: documentId,
          owner_id: ownerId,
          participants: session.participants,
          is_active: true,
          version: 1,
          conflict_strategy: this.collabConfig.conflictStrategy,
          created_at: session.createdAt.toISOString(),
          updated_at: session.updatedAt.toISOString()
        })

      if (error) throw error

      // Set up real-time channel
      if (this.collabConfig.enableRealtime) {
        await this.setupRealtimeChannel(sessionId)
      }

      logger.info('Collaboration session created', { sessionId, projectId, documentId })
      
      return session
    })
  }

  async joinSession(
    sessionId: string,
    userId: string,
    role: string = 'viewer'
  ): Promise<ServiceResponse<CollaborationSession>> {
    return this.withErrorHandling(async () => {
      const supabase = this.getSupabaseClient()
      
      // Get current session
      const { data: session, error } = await supabase
        .from('collaboration_sessions')
        .select('*')
        .eq('id', sessionId)
        .eq('is_active', true)
        .single()

      if (error || !session) {
        throw new Error('Session not found or inactive')
      }

      // Check max participants
      if (session.participants.length >= this.collabConfig.maxParticipants) {
        throw new Error('Session is full')
      }

      // Get user info from profiles
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('full_name, email')
        .eq('id', userId)
        .single()

      if (profileError || !userProfile) {
        throw new Error('User profile not found')
      }

      // Add user to participants
      const newParticipant: CollaborationUser = {
        id: userId,
        name: userProfile.full_name || 'Anonymous',
        email: userProfile.email || '',
        color: this.generateUserColor(userId),
        role: role as 'owner' | 'editor' | 'viewer' | 'commenter',
        status: 'online',
        lastSeen: Date.now()
      }

      const updatedParticipants = [...session.participants, newParticipant]

      // Update database
      const { error: updateError } = await supabase
        .from('collaboration_sessions')
        .update({
          participants: updatedParticipants,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)

      if (updateError) throw updateError

      // Broadcast user joined event
      if (this.collabConfig.enableRealtime) {
        await this.broadcastEvent(sessionId, {
          type: 'user.joined',
          sessionId,
          userId,
          data: newParticipant,
          timestamp: Date.now()
        })
      }

      logger.info('User joined session', { sessionId, userId })

      return {
        ...session,
        participants: updatedParticipants
      }
    })
  }

  // Content Changes
  async submitChange(
    sessionId: string,
    userId: string,
    change: Omit<CollaborationChange, 'id' | 'sessionId' | 'userId' | 'timestamp' | 'version'>
  ): Promise<ServiceResponse<CollaborationChange>> {
    return this.withErrorHandling(async () => {
      const supabase = this.getSupabaseClient()
      
      // Get current session version
      const { data: session, error } = await supabase
        .from('collaboration_sessions')
        .select('version, conflict_strategy')
        .eq('id', sessionId)
        .single()

      if (error || !session) {
        throw new Error('Session not found')
      }

      const fullChange: CollaborationChange = {
        id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        sessionId,
        userId,
        ...change,
        timestamp: Date.now(),
        version: session.version + 1
      }

      // Apply conflict resolution if needed
      if (session.conflict_strategy !== 'last-write-wins') {
        const resolvedChange = await this.resolveConflicts(sessionId, fullChange, session.conflict_strategy)
        if (resolvedChange) {
          Object.assign(fullChange, resolvedChange)
        }
      }

      // Store change
      const { error: insertError } = await supabase
        .from('collaboration_changes')
        .insert({
          id: fullChange.id,
          session_id: sessionId,
          user_id: userId,
          type: fullChange.type,
          range: fullChange.range,
          text: fullChange.text,
          data: fullChange.data,
          timestamp: fullChange.timestamp,
          version: fullChange.version
        })

      if (insertError) throw insertError

      // Update session version
      await supabase
        .from('collaboration_sessions')
        .update({
          version: fullChange.version,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)

      // Broadcast change
      if (this.collabConfig.enableRealtime) {
        await this.broadcastEvent(sessionId, {
          type: 'content.changed',
          sessionId,
          userId,
          data: fullChange,
          timestamp: Date.now()
        })
      }

      return fullChange
    })
  }

  // Document Locking
  async acquireLock(
    documentId: string,
    section: string,
    userId: string
  ): Promise<ServiceResponse<DocumentLock>> {
    return this.withErrorHandling(async () => {
      const supabase = this.getSupabaseClient()
      const now = Date.now()
      const expiresAt = now + this.collabConfig.lockTimeout

      // Check for existing lock
      const { data: existingLock } = await supabase
        .from('document_locks')
        .select('*')
        .eq('document_id', documentId)
        .eq('section', section)
        .gt('expires_at', now)
        .single()

      if (existingLock && existingLock.user_id !== userId) {
        throw new Error('Section is already locked by another user')
      }

      const lock: DocumentLock = {
        documentId,
        section,
        userId,
        timestamp: now,
        expiresAt
      }

      // Upsert lock
      const { error } = await supabase
        .from('document_locks')
        .upsert({
          document_id: documentId,
          section,
          user_id: userId,
          timestamp: now,
          expires_at: expiresAt
        })

      if (error) throw error

      logger.info('Document lock acquired', { documentId, section, userId })

      return lock
    })
  }

  async releaseLock(
    documentId: string,
    section: string,
    userId: string
  ): Promise<ServiceResponse<void>> {
    return this.withErrorHandling(async () => {
      const supabase = this.getSupabaseClient()

      const { error } = await supabase
        .from('document_locks')
        .delete()
        .eq('document_id', documentId)
        .eq('section', section)
        .eq('user_id', userId)

      if (error) throw error

      logger.info('Document lock released', { documentId, section, userId })
    })
  }

  // Cursor and Selection
  async updateCursor(
    sessionId: string,
    userId: string,
    cursor: { line: number; column: number }
  ): Promise<ServiceResponse<void>> {
    return this.withErrorHandling(async () => {
      if (!this.collabConfig.enableRealtime) return

      await this.broadcastEvent(sessionId, {
        type: 'cursor.moved',
        sessionId,
        userId,
        data: cursor,
        timestamp: Date.now()
      })
    })
  }

  async updateSelection(
    sessionId: string,
    userId: string,
    selection: {
      startLine: number
      startColumn: number
      endLine: number
      endColumn: number
    }
  ): Promise<ServiceResponse<void>> {
    return this.withErrorHandling(async () => {
      if (!this.collabConfig.enableRealtime) return

      await this.broadcastEvent(sessionId, {
        type: 'selection.changed',
        sessionId,
        userId,
        data: selection,
        timestamp: Date.now()
      })
    })
  }

  // Private helper methods
  private getSupabaseClient(): SupabaseClient {
    if (typeof window !== 'undefined') {
      return createClient()
    }
    return createServerClient()
  }

  private async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    const supabase = this.getSupabaseClient()
    const { data } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    return data
  }

  private async setupRealtimeChannel(sessionId: string): Promise<void> {
    const supabase = this.getSupabaseClient()
    
    const channel = supabase
      .channel(`collaboration:${sessionId}`)
      .on('presence', { event: 'sync' }, () => {
        logger.debug('Presence sync', { sessionId })
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        logger.debug('User joined presence', { sessionId, key, newPresences })
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        logger.debug('User left presence', { sessionId, key, leftPresences })
      })
      .on('broadcast', { event: 'collaboration' }, ({ payload }) => {
        logger.debug('Collaboration event received', { sessionId, payload })
      })
      .subscribe()

    this.channels.set(sessionId, channel)
  }

  private async broadcastEvent(sessionId: string, event: CollaborationEvent): Promise<void> {
    const channel = this.channels.get(sessionId)
    if (!channel) {
      logger.warn('No channel found for session', { sessionId })
      return
    }

    await channel.send({
      type: 'broadcast',
      event: 'collaboration',
      payload: event
    })
  }

  private generateUserColor(userId: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8',
      '#6C5CE7', '#A8E6CF', '#FFD93D', '#FCB1A6', '#B6E5D8'
    ]
    
    let hash = 0
    for (let i = 0; i < userId.length; i++) {
      hash = userId.charCodeAt(i) + ((hash << 5) - hash)
    }
    
    return colors[Math.abs(hash) % colors.length]
  }

  private async resolveConflicts(
    sessionId: string,
    change: CollaborationChange,
    strategy: ConflictResolutionStrategy
  ): Promise<Partial<CollaborationChange> | null> {
    const supabase = this.getSupabaseClient()
    
    // Get recent changes for conflict detection
    const { data: recentChanges } = await supabase
      .from('collaboration_changes')
      .select('*')
      .eq('session_id', sessionId)
      .gte('timestamp', change.timestamp - 5000) // Last 5 seconds
      .neq('user_id', change.userId)
      .order('timestamp', { ascending: true })

    if (!recentChanges || recentChanges.length === 0) {
      return null // No conflicts
    }

    // Use the conflict resolver
    const resolved = conflictResolver.resolveChange(
      change,
      recentChanges,
      strategy
    )

    return resolved
  }

  // Health check
  async healthCheck(): Promise<ServiceResponse<{
    status: string
    activeSessions: number
    activeChannels: number
    realtimeEnabled: boolean
  }>> {
    return this.withErrorHandling(async () => {
      const supabase = this.getSupabaseClient()
      
      const { count } = await supabase
        .from('collaboration_sessions')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      return {
        status: this.status,
        activeSessions: count || 0,
        activeChannels: this.channels.size,
        realtimeEnabled: this.collabConfig.enableRealtime
      }
    })
  }
}

// Export singleton instance
export const collaborationService = UnifiedCollaborationService.getInstance()
export const collaborationServiceRealtime = collaborationService // Alias for backward compatibility