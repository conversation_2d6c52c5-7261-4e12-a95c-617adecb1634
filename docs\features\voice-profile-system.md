# BookScribe Voice Profile System

## Overview

The Voice Profile System enables authors to maintain consistent character voices, narrative styles, and author voices throughout their writing. It uses AI-powered analysis to extract voice patterns from text samples and provides real-time consistency checking during writing.

## Architecture

### Database Schema

#### Voice Profiles Table
Core table storing voice pattern data:

```sql
voice_profiles:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - name: VARCHAR(255) - Profile name
  - description: TEXT - Profile description
  - type: VARCHAR(50) - author, character, narrator
  - patterns: JSONB - Extracted voice patterns
  - sample_texts: TEXT[] - Training text samples
  - confidence: DECIMAL(3,2) - Model confidence (0.00-1.00)
  - training_method: VARCHAR(50) - manual, ai_extracted, hybrid
  - training_samples_count: INTEGER
  - total_words_analyzed: INTEGER
  - is_global: BOOLEAN - Available across all projects
  - project_id: UUID - Specific to project
  - series_id: UUID - Specific to series
  - character_id: UUID - Linked to character
  - version: INTEGER - Version number
  - parent_profile_id: UUID - For versioning
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Voice Training Sessions Table
Tracks training data and analysis:

```sql
voice_training_sessions:
  - id: UUID (Primary Key)
  - voice_profile_id: UUID - References voice_profiles
  - input_text: TEXT - Training text
  - input_source: VARCHAR(100) - manual_entry, file_upload, project_content
  - analysis_results: JSONB - AI analysis output
  - patterns_extracted: JSONB - Extracted patterns
  - status: VARCHAR(50) - pending, processing, completed, failed
  - error_message: TEXT
  - created_at: TIMESTAMPTZ
```

#### Series Character Continuity Table
Maintains character voice consistency across series:

```sql
series_character_continuity:
  - id: UUID (Primary Key)
  - series_id: UUID - References series
  - character_name: VARCHAR(255)
  - first_appearance_book: INTEGER
  - last_appearance_book: INTEGER
  - status: VARCHAR(50) - active, inactive, deceased, written_out, mentioned_only
  - status_change_book: INTEGER
  - status_change_reason: TEXT
  - voice_profile_id: UUID - References voice_profiles
  - character_states: JSONB - Evolution tracking
  - relationship_changes: JSONB
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
  - UNIQUE(series_id, character_name)
```

#### Voice Consistency Checks Table
Records consistency analysis results:

```sql
voice_consistency_checks:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - chapter_id: UUID - References chapters
  - voice_profile_id: UUID - References voice_profiles
  - consistency_score: DECIMAL(3,2) - 0.00-1.00
  - deviations: JSONB - Detected inconsistencies
  - suggestions: JSONB - AI recommendations
  - content_analyzed: TEXT
  - word_count: INTEGER
  - analysis_timestamp: TIMESTAMPTZ
```

## Voice Profile Types

### 1. Author Voice
Captures the writer's unique style:
- Sentence structure preferences
- Vocabulary choices
- Narrative techniques
- Pacing patterns
- Description style

### 2. Character Voice
Maintains character-specific speech patterns:
- Dialogue patterns
- Speech quirks
- Vocabulary level
- Emotional expression
- Cultural/regional markers

### 3. Narrator Voice
For consistent narrative tone:
- POV consistency
- Narrative distance
- Description style
- Tone and mood
- Pacing control

## Pattern Analysis

### Extracted Patterns Structure
```json
{
  "vocabulary": {
    "common_words": ["specific", "frequently", "used"],
    "unique_phrases": ["signature expressions"],
    "complexity_level": 7.5,
    "formality_score": 0.65
  },
  "syntax": {
    "average_sentence_length": 18.5,
    "sentence_variety": 0.82,
    "clause_patterns": ["compound", "complex"],
    "punctuation_style": {
      "em_dash_frequency": 0.15,
      "semicolon_usage": 0.05
    }
  },
  "tone": {
    "emotional_range": ["witty", "sarcastic", "warm"],
    "formality": "casual",
    "energy_level": "high"
  },
  "dialogue": {
    "tag_preferences": ["said", "asked", "minimal"],
    "contraction_usage": 0.85,
    "interruption_patterns": true,
    "dialect_markers": ["y'all", "gonna"]
  },
  "pacing": {
    "paragraph_length_avg": 125,
    "scene_transition_style": "sharp",
    "description_density": 0.4
  }
}
```

## API Endpoints

### Voice Profile Management

#### GET /api/voice/profiles
List user's voice profiles:
```typescript
// Response
{
  profiles: [
    {
      id: "uuid",
      name: "Detective Morrison",
      type: "character",
      confidence: 0.87,
      training_samples_count: 15,
      project_id: "uuid",
      is_global: false
    }
  ]
}
```

#### POST /api/voice/profiles
Create new voice profile:
```typescript
// Request
{
  name: "Detective Morrison",
  type: "character",
  description: "Gruff detective with a heart of gold",
  project_id: "uuid",
  character_id: "uuid"
}
```

#### POST /api/voice/profiles/{id}/train
Train voice profile with samples:
```typescript
// Request
{
  training_text: "Sample dialogue or narrative text...",
  source: "manual_entry"
}

// Response
{
  session_id: "uuid",
  status: "processing",
  patterns_found: 24
}
```

### Voice Analysis

#### POST /api/voice/analyze
Analyze text for voice consistency:
```typescript
// Request
{
  text: "Chapter content to analyze...",
  voice_profile_id: "uuid",
  project_id: "uuid"
}

// Response
{
  consistency_score: 0.82,
  deviations: [
    {
      type: "vocabulary",
      severity: "minor",
      location: "paragraph 3",
      suggestion: "Character typically uses 'cop' instead of 'police officer'"
    }
  ],
  suggestions: [
    "Consider shorter sentences for this character",
    "Add more emotional descriptors"
  ]
}
```

## Training Process

### 1. Manual Training
User provides text samples:
```typescript
interface ManualTraining {
  method: 'manual';
  samples: string[];
  minimumSamples: 5;
  minimumWords: 500;
}
```

### 2. AI-Extracted Training
Analyze existing content:
```typescript
interface AIExtractedTraining {
  method: 'ai_extracted';
  source: 'project' | 'chapter' | 'character_dialogue';
  sourceId: string;
  extractionRules: {
    characterName?: string;
    dialogueOnly?: boolean;
    narrativeOnly?: boolean;
  };
}
```

### 3. Hybrid Training
Combination of manual and AI:
```typescript
interface HybridTraining {
  method: 'hybrid';
  manualSamples: string[];
  aiExtraction: AIExtractedTraining;
  weightingRatio: number; // 0.0 to 1.0
}
```

## Consistency Checking

### Real-time Analysis
During writing, the system provides:
1. **Inline Suggestions**: Vocabulary alternatives
2. **Style Warnings**: Deviation from established patterns
3. **Consistency Score**: Rolling average for current session
4. **Pattern Matching**: Highlights matching/diverging patterns

### Batch Analysis
For completed chapters:
```typescript
interface BatchAnalysis {
  chapterId: string;
  voiceProfiles: string[];
  analysisDepth: 'quick' | 'standard' | 'deep';
  generateReport: boolean;
}
```

## Series Character Continuity

### Character Evolution Tracking
```json
{
  "character_states": {
    "book_1": {
      "personality": ["naive", "optimistic", "impulsive"],
      "speech_patterns": ["formal", "questioning"],
      "relationships": {
        "mentor": "respectful",
        "rival": "hostile"
      }
    },
    "book_2": {
      "personality": ["cautious", "determined", "strategic"],
      "speech_patterns": ["confident", "commanding"],
      "relationships": {
        "mentor": "equal",
        "rival": "respectful"
      }
    }
  }
}
```

### Continuity Checks
- Character appearance consistency
- Relationship evolution tracking
- Voice pattern progression
- Status change management

## UI Components

### Voice Profile Manager
```tsx
<VoiceProfileManager
  projectId={projectId}
  onProfileSelect={handleProfileSelect}
  allowCreate={true}
  filterType="character"
/>
```

### Voice Training Interface
```tsx
<VoiceTrainer
  profileId={profileId}
  onTrainingComplete={handleComplete}
  minSamples={5}
  showAnalysis={true}
/>
```

### Consistency Monitor
```tsx
<VoiceConsistencyMonitor
  activeProfiles={profiles}
  currentText={editorContent}
  showInlineHints={true}
  sensitivity="medium"
/>
```

## Performance Optimization

### Caching Strategy
- Profile patterns cached in memory
- Analysis results cached for 24 hours
- Training session results permanent

### Indexes
```sql
CREATE INDEX idx_voice_profiles_user_id ON voice_profiles(user_id);
CREATE INDEX idx_voice_profiles_type ON voice_profiles(type);
CREATE INDEX idx_voice_consistency_checks_project_id ON voice_consistency_checks(project_id);
CREATE INDEX idx_series_character_continuity_series_id ON series_character_continuity(series_id);
```

## Security

### Row Level Security
- Users can only access their own voice profiles
- Shared profiles require explicit permissions
- Series continuity restricted to series owners

### API Security
- All endpoints require authentication
- Rate limiting on analysis endpoints
- Training data sanitization

## Future Enhancements

1. **Voice Marketplace**
   - Share voice profiles
   - Download celebrity voices
   - Community ratings

2. **Advanced Analysis**
   - Emotion detection
   - Cultural sensitivity
   - Age-appropriate language

3. **Integration Features**
   - Export to writing tools
   - Voice synthesis
   - Audio narration matching

## Related Systems
- Character Management System
- AI Agent System (uses voice profiles)
- Series Management
- Real-time Collaboration (voice consistency)