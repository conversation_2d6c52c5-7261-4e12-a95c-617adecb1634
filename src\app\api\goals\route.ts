import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

const createGoalSchema = z.object({
  goal_type: z.enum(['word_count', 'streak', 'quality', 'milestone', 'chapter', 'custom']),
  title: z.string().min(3),
  description: z.string().optional(),
  target_value: z.number().min(1),
  unit: z.string().min(1),
  deadline: z.string().optional(),
  difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  is_recommended: z.boolean().optional(),
  project_id: z.string().uuid().optional(),
  metadata: z.record(z.unknown()).optional(),
})

const createMultipleGoalsSchema = z.object({
  goals: z.array(createGoalSchema)
})

export async function GET(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return handleAPIError(new AuthenticationError())
    }

    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    const status = searchParams.get('status') || 'active'

    let query = supabase
      .from('writing_goals')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', status)
      .order('created_at', { ascending: false })

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    const { data: goals, error } = await query

    if (error) {
      logger.error('Error fetching goals:', error)
      return NextResponse.json({ error: 'Failed to fetch goals' }, { status: 500 })
    }

    // Fetch progress for each goal
    const goalsWithProgress = await Promise.all(
      (goals || []).map(async (goal) => {
        const { data: progress } = await supabase
          .from('writing_goal_progress')
          .select('*')
          .eq('goal_id', goal.id)
          .order('progress_date', { ascending: false })
          .limit(7)

        return {
          ...goal,
          progress: progress || []
        }
      })
    )

    return NextResponse.json({ goals: goalsWithProgress })
  } catch (error) {
    logger.error('Error in goals GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()

    // Check if we're creating multiple goals
    if (body.goals && Array.isArray(body.goals)) {
      const validation = createMultipleGoalsSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json({ error: validation.error.issues }, { status: 400 })
      }

      const goalsToInsert = validation.data.goals.map(goal => ({
        ...goal,
        user_id: user.id,
        status: 'active',
        current_value: 0,
      }))

      const { data: createdGoals, error } = await supabase
        .from('writing_goals')
        .insert(goalsToInsert)
        .select()

      if (error) {
        logger.error('Error creating goals:', error)
        return NextResponse.json({ error: 'Failed to create goals' }, { status: 500 })
      }

      return NextResponse.json({ goals: createdGoals })
    } else {
      // Single goal creation
      const validation = createGoalSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json({ error: validation.error.issues }, { status: 400 })
      }

      const { data: goal, error } = await supabase
        .from('writing_goals')
        .insert({
          ...validation.data,
          user_id: user.id,
          status: 'active',
          current_value: 0,
        })
        .select()
        .single()

      if (error) {
        logger.error('Error creating goal:', error)
        return NextResponse.json({ error: 'Failed to create goal' }, { status: 500 })
      }

      return NextResponse.json({ goal })
    }
  } catch (error) {
    logger.error('Error in goals POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    const { goalId, ...updates } = body

    if (!goalId) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const { data: goal, error } = await supabase
      .from('writing_goals')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', goalId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      logger.error('Error updating goal:', error)
      return NextResponse.json({ error: 'Failed to update goal' }, { status: 500 })
    }

    return NextResponse.json({ goal })
  } catch (error) {
    logger.error('Error in goals PATCH:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return handleAPIError(new AuthenticationError())
    }

    const searchParams = request.nextUrl.searchParams
    const goalId = searchParams.get('goalId')

    if (!goalId) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const { error } = await supabase
      .from('writing_goals')
      .delete()
      .eq('id', goalId)
      .eq('user_id', user.id)

    if (error) {
      logger.error('Error deleting goal:', error)
      return NextResponse.json({ error: 'Failed to delete goal' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    logger.error('Error in goals DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}