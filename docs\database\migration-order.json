{"001_drop_existing": ["-- Drop all existing objects in reverse dependency order", "-- This ensures clean slate for consolidated migrations"], "002_extensions": ["CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";", "CREATE EXTENSION IF NOT EXISTS \"pg_trgm\";", "CREATE EXTENSION IF NOT EXISTS \"vector\";"], "003_core_tables": ["users", "projects", "chapters", "characters", "series"], "004_feature_tables": ["voice_profiles", "universes", "story_bible_entries", "timeline_events", "locations", "reference_materials"], "005_collaboration_tables": ["project_collaborators", "project_invitations", "collaboration_sessions"], "006_analytics_tables": ["writing_sessions", "word_count_history", "usage_tracking", "ai_usage_logs", "quality_metrics"], "007_achievement_tables": ["achievements", "user_achievements", "achievement_progress"], "008_indexes": ["-- All performance indexes"], "009_functions": ["-- All stored procedures and functions"], "010_policies": ["-- Row Level Security policies"]}