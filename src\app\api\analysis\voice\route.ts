import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { withRateLimit } from '@/lib/rate-limiter-unified';
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import { z } from 'zod';
import { logger } from '@/lib/services/logger';
import { VoiceAnalyzer } from '@/lib/analysis/voice-analyzer';
import { type StandardResponse } from '@/lib/api/types';
import { TIME_MS } from '@/lib/constants';
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

// Request validation schema
const voiceAnalysisSchema = z.object({
  content: z.string().min(100, 'Content must be at least 100 characters'),
  projectId: z.string().uuid('Invalid project ID'),
  existingProfile: z.any().optional()
});

export const POST = UnifiedAuthService.withAuth(async (request) => {
  const rateLimitResponse = await withRateLimit(request, {
    windowMs: 60 * 60 * TIME_MS.SECOND, // 1 hour
    maxRequests: 10, // 10 analysis requests per hour
    message: 'Too many voice analysis requests. Please wait before trying again.'
  });
  
  if (rateLimitResponse) {
    return rateLimitResponse;
  }
  
  try {
    const user = request.user!;
    const body = await request.json();
    const validatedData = voiceAnalysisSchema.parse(body);
    
    // Verify user owns the project
    const project = await verifyProjectAccess(validatedData.projectId, user.id)
    if (!project) {
      return handleAPIError(new ValidationError(PROJECT_ACCESS_ERROR));
    }
      
      logger.info('Voice analysis requested', {
        projectId: validatedData.projectId,
        contentLength: validatedData.content.length,
        hasExistingProfile: !!validatedData.existingProfile
      });
      
      const voiceAnalyzer = new VoiceAnalyzer();
      
      let profile = validatedData.existingProfile;
      let matches = [];
      
      if (profile) {
        // Analyze current content against existing voice profile
        matches = await voiceAnalyzer.analyzeVoiceMatch(
          validatedData.content, 
          profile
        );
      } else {
        // Create initial voice profile from content
        profile = await voiceAnalyzer.analyzeUserVoice(
          [validatedData.content], 
          validatedData.projectId
        );
      }
      
      return NextResponse.json<StandardResponse>({
        success: true,
        data: {
          profile,
          matches,
          metadata: {
            contentLength: validatedData.content.length,
            wordCount: validatedData.content.split(/\s+/).filter(word => word.length > 0).length,
            timestamp: new Date().toISOString()
          }
        }
      });
      
    } catch (error) {
      if (error instanceof z.ZodError) {
        return handleAPIError(new ValidationError('Invalid request data'));
      }
      
      logger.error('Voice analysis error:', error);
      return NextResponse.json<StandardResponse>(
        { success: false, error: 'Failed to analyze voice' },
        { status: 500 }
      );
    }
  }
);
