import { NextResponse } from 'next/server'
import { z } from 'zod'

// Base response types
export interface APIResponse<T = unknown> {
  success: boolean
  data?: T
  error?: APIErrorResponse
  meta?: ResponseMeta
}

export interface APIErrorResponse {
  message: string
  code: string
  details?: unknown
  userMessage?: string
  retryAfter?: number
}

export interface ResponseMeta {
  requestId?: string
  timestamp: string
  version: string
  pagination?: PaginationMeta
  rateLimit?: RateLimitMeta
  performance?: PerformanceMeta
}

export interface PaginationMeta {
  page: number
  perPage: number
  total: number
  totalPages: number
  hasMore: boolean
}

export interface RateLimitMeta {
  limit: number
  remaining: number
  reset: string
}

export interface PerformanceMeta {
  duration: number
  dbQueries?: number
  cacheHit?: boolean
}

// Response builder class
export class UnifiedResponse {
  private static readonly API_VERSION = '1.0'

  // Success response
  static success<T>(
    data: T,
    meta?: Partial<ResponseMeta>,
    status: number = 200
  ): NextResponse<APIResponse<T>> {
    const response: APIResponse<T> = {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: this.API_VERSION,
        ...meta
      }
    }

    return NextResponse.json(response, { status })
  }

  // Error response
  static error(
    error: {
      message: string
      code: string
      details?: unknown
      userMessage?: string
      retryAfter?: number
    },
    meta?: Partial<ResponseMeta>,
    status: number = 500
  ): NextResponse<APIResponse> {
    const response: APIResponse = {
      success: false,
      error: {
        message: error.message,
        code: error.code,
        details: error.details,
        userMessage: error.userMessage || this.getDefaultUserMessage(error.code),
        retryAfter: error.retryAfter
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: this.API_VERSION,
        ...meta
      }
    }

    const headers: Record<string, string> = {}
    if (error.retryAfter) {
      headers['Retry-After'] = error.retryAfter.toString()
    }

    return NextResponse.json(response, { status, headers })
  }

  // Paginated response
  static paginated<T>(
    data: T[],
    pagination: {
      page: number
      perPage: number
      total: number
    },
    meta?: Partial<ResponseMeta>,
    status: number = 200
  ): NextResponse<APIResponse<T[]>> {
    const totalPages = Math.ceil(pagination.total / pagination.perPage)
    const hasMore = pagination.page < totalPages

    const response: APIResponse<T[]> = {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: this.API_VERSION,
        pagination: {
          page: pagination.page,
          perPage: pagination.perPage,
          total: pagination.total,
          totalPages,
          hasMore
        },
        ...meta
      }
    }

    return NextResponse.json(response, { status })
  }

  // No content response
  static noContent(meta?: Partial<ResponseMeta>): NextResponse {
    return new NextResponse(null, { status: 204 })
  }

  // Created response
  static created<T>(
    data: T,
    location?: string,
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse<T>> {
    const headers: Record<string, string> = {}
    if (location) {
      headers['Location'] = location
    }

    const response: APIResponse<T> = {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: this.API_VERSION,
        ...meta
      }
    }

    return NextResponse.json(response, { status: 201, headers })
  }

  // Accepted response (for async operations)
  static accepted(
    taskId: string,
    statusUrl?: string,
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse<{ taskId: string; statusUrl?: string }>> {
    const response: APIResponse<{ taskId: string; statusUrl?: string }> = {
      success: true,
      data: {
        taskId,
        statusUrl
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: this.API_VERSION,
        ...meta
      }
    }

    return NextResponse.json(response, { status: 202 })
  }

  // Validation error response
  static validationError(
    errors: z.ZodError | Array<{ field: string; message: string }>,
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse> {
    let details: Array<{ field: string; message: string }>

    if (errors instanceof z.ZodError) {
      details = errors.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message
      }))
    } else {
      details = errors
    }

    return this.error(
      {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details,
        userMessage: 'Please check your input and try again'
      },
      meta,
      400
    )
  }

  // Not found response
  static notFound(
    resource: string,
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse> {
    return this.error(
      {
        message: `${resource} not found`,
        code: 'NOT_FOUND',
        userMessage: `The requested ${resource.toLowerCase()} could not be found`
      },
      meta,
      404
    )
  }

  // Unauthorized response
  static unauthorized(
    message: string = 'Authentication required',
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse> {
    return this.error(
      {
        message,
        code: 'UNAUTHORIZED',
        userMessage: 'Please log in to access this resource'
      },
      meta,
      401
    )
  }

  // Forbidden response
  static forbidden(
    message: string = 'Insufficient permissions',
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse> {
    return this.error(
      {
        message,
        code: 'FORBIDDEN',
        userMessage: 'You do not have permission to access this resource'
      },
      meta,
      403
    )
  }

  // Rate limit response
  static rateLimited(
    retryAfter: number,
    limit: number,
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse> {
    return this.error(
      {
        message: 'Rate limit exceeded',
        code: 'RATE_LIMIT_EXCEEDED',
        userMessage: 'Too many requests. Please try again later',
        retryAfter
      },
      {
        ...meta,
        rateLimit: {
          limit,
          remaining: 0,
          reset: new Date(Date.now() + retryAfter * 1000).toISOString()
        }
      },
      429
    )
  }

  // Service unavailable response
  static serviceUnavailable(
    service: string,
    retryAfter?: number,
    meta?: Partial<ResponseMeta>
  ): NextResponse<APIResponse> {
    return this.error(
      {
        message: `${service} is currently unavailable`,
        code: 'SERVICE_UNAVAILABLE',
        userMessage: 'The service is temporarily unavailable. Please try again later',
        retryAfter
      },
      meta,
      503
    )
  }

  // Default user messages for error codes
  private static getDefaultUserMessage(code: string): string {
    const messages: Record<string, string> = {
      VALIDATION_ERROR: 'Please check your input and try again',
      AUTHENTICATION_ERROR: 'Please log in to continue',
      AUTHORIZATION_ERROR: 'You do not have permission to perform this action',
      NOT_FOUND: 'The requested resource could not be found',
      RATE_LIMIT_EXCEEDED: 'Too many requests. Please try again later',
      EXTERNAL_SERVICE_ERROR: 'An external service is currently unavailable',
      DATABASE_ERROR: 'A database error occurred. Please try again',
      INTERNAL_SERVER_ERROR: 'An unexpected error occurred. Please try again',
      TIMEOUT_ERROR: 'The request took too long to complete',
      CONFLICT_ERROR: 'The request conflicts with existing data',
      PAYMENT_ERROR: 'There was an issue processing your payment',
      AI_SERVICE_ERROR: 'The AI service encountered an error',
      CIRCUIT_BREAKER_OPEN: 'This service is temporarily disabled'
    }

    return messages[code] || 'An error occurred. Please try again'
  }
}

// Response schemas for validation
export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  perPage: z.number().int().positive().max(100).default(20)
})

export const sortSchema = z.object({
  field: z.string(),
  order: z.enum(['asc', 'desc']).default('asc')
})

export const filterSchema = z.record(z.union([z.string(), z.number(), z.boolean()]))

// Type guards
export function isAPIResponse<T>(response: unknown): response is APIResponse<T> {
  return (
    typeof response === 'object' &&
    response !== null &&
    'success' in response &&
    typeof (response as any).success === 'boolean'
  )
}

export function isSuccessResponse<T>(
  response: APIResponse<T>
): response is APIResponse<T> & { success: true; data: T } {
  return response.success === true && 'data' in response
}

export function isErrorResponse(
  response: APIResponse
): response is APIResponse & { success: false; error: APIErrorResponse } {
  return response.success === false && 'error' in response
}

// Helper function to extract data from response
export function extractData<T>(response: APIResponse<T>): T | null {
  if (isSuccessResponse(response)) {
    return response.data
  }
  return null
}

// Helper function to extract error from response
export function extractError(response: APIResponse): APIErrorResponse | null {
  if (isErrorResponse(response)) {
    return response.error
  }
  return null
}