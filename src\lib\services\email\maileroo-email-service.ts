import { createServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { MailerooProvider } from '@/lib/email/providers/maileroo'

// Email template types
export const EmailTemplates = {
  WELCOME: 'welcome',
  ACHIEVEMENT_UNLOCKED: 'achievement_unlocked',
  GOAL_REMINDER: 'goal_reminder',
  COLLABORATION_INVITE: 'collaboration_invite',
  EXPORT_READY: 'export_ready',
  SUBSCRIPTION_RENEWED: 'subscription_renewed',
  SUBSCRIPTION_CANCELLED: 'subscription_cancelled',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification',
  WEEKLY_PROGRESS: 'weekly_progress',
  PROJECT_SHARED: 'project_shared',
  COMMENT_NOTIFICATION: 'comment_notification',
  ACCOUNT_DELETED: 'account_deleted'
} as const

export type EmailTemplate = typeof EmailTemplates[keyof typeof EmailTemplates]

// Email data schemas for each template
const emailDataSchemas = {
  welcome: z.object({
    userName: z.string(),
    loginUrl: z.string()
  }),
  achievement_unlocked: z.object({
    userName: z.string(),
    achievementName: z.string(),
    achievementDescription: z.string(),
    achievementTier: z.enum(['bronze', 'silver', 'gold', 'platinum'])
  }),
  goal_reminder: z.object({
    userName: z.string(),
    goalName: z.string(),
    currentProgress: z.number(),
    targetProgress: z.number(),
    dueDate: z.string()
  }),
  collaboration_invite: z.object({
    inviterName: z.string(),
    projectName: z.string(),
    inviteUrl: z.string(),
    role: z.enum(['viewer', 'editor', 'admin'])
  }),
  export_ready: z.object({
    userName: z.string(),
    projectName: z.string(),
    exportFormat: z.string(),
    downloadUrl: z.string(),
    expiresAt: z.string()
  }),
  subscription_renewed: z.object({
    userName: z.string(),
    planName: z.string(),
    nextBillingDate: z.string(),
    amount: z.number()
  }),
  subscription_cancelled: z.object({
    userName: z.string(),
    planName: z.string(),
    expiresAt: z.string()
  }),
  password_reset: z.object({
    resetUrl: z.string()
  }),
  email_verification: z.object({
    verificationUrl: z.string()
  }),
  weekly_progress: z.object({
    userName: z.string(),
    wordsWritten: z.number(),
    chaptersCompleted: z.number(),
    writingStreak: z.number(),
    projectsWorkedOn: z.array(z.string())
  }),
  account_deleted: z.object({
    userName: z.string()
  }),
  project_shared: z.object({
    sharedByName: z.string(),
    projectName: z.string(),
    projectUrl: z.string(),
    message: z.string().optional()
  }),
  comment_notification: z.object({
    commenterName: z.string(),
    projectName: z.string(),
    chapterTitle: z.string(),
    commentPreview: z.string(),
    commentUrl: z.string()
  })
}

export type EmailData<T extends EmailTemplate> = z.infer<typeof emailDataSchemas[T]>

interface EmailQueueItem {
  id: string
  to: string
  template: EmailTemplate
  data: any
  status: 'pending' | 'processing' | 'sent' | 'failed'
  attempts: number
  error?: string
  scheduled_for: Date
  sent_at?: Date
}

// Maileroo API types
interface MailerooSendRequest {
  from: string
  to: string | string[]
  subject: string
  text?: string
  html?: string
  cc?: string | string[]
  bcc?: string | string[]
  reply_to?: string
  tags?: string[]
  tracking?: boolean
  template_id?: string
  template_data?: Record<string, any>
}

interface MailerooResponse {
  message_id: string
  status: 'queued' | 'sent' | 'failed'
  error?: string
}

export class MailerooEmailService {
  private static instance: MailerooEmailService
  private isInitialized = false
  private apiKey: string | null = null
  private baseUrl = 'https://api.maileroo.net/v1'

  private constructor() {}

  static getInstance(): MailerooEmailService {
    if (!MailerooEmailService.instance) {
      MailerooEmailService.instance = new MailerooEmailService()
    }
    return MailerooEmailService.instance
  }

  async initialize() {
    if (this.isInitialized) return

    const apiKey = process.env.MAILEROO_API_KEY
    if (!apiKey) {
      logger.error('Maileroo API key not found')
      throw new Error('Email service not configured')
    }

    this.apiKey = apiKey
    this.isInitialized = true
    logger.info('Maileroo email service initialized')
  }

  async sendEmail<T extends EmailTemplate>(
    to: string | string[],
    template: T,
    data: EmailData<T>,
    options?: {
      scheduledFor?: Date
      userId?: string
    }
  ): Promise<void> {
    try {
      // Validate data against schema
      const schema = emailDataSchemas[template]
      const validatedData = schema.parse(data)

      // Check user email preferences if userId provided
      if (options?.userId) {
        const canSend = await this.checkEmailPreferences(options.userId, template)
        if (!canSend) {
          logger.info(`Email blocked by user preferences: ${template} to ${to}`)
          return
        }
      }

      // Queue the email
      await this.queueEmail(to, template, validatedData, options?.scheduledFor)
    } catch (error) {
      logger.error('Error sending email:', error)
      throw error
    }
  }

  private async checkEmailPreferences(userId: string, template: EmailTemplate): Promise<boolean> {
    const supabase = await createServerClient()
    
    const { data: preferences } = await supabase
      .from('email_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (!preferences) return true // Default to sending if no preferences

    // Map templates to preference categories
    const preferenceMap: Record<EmailTemplate, keyof typeof preferences> = {
      welcome: 'marketing',
      achievement_unlocked: 'achievements',
      goal_reminder: 'progress',
      collaboration_invite: 'collaboration',
      export_ready: 'progress',
      subscription_renewed: 'marketing',
      subscription_cancelled: 'marketing',
      password_reset: 'marketing', // Always send
      email_verification: 'marketing', // Always send
      weekly_progress: 'progress',
      project_shared: 'collaboration',
      comment_notification: 'collaboration',
      account_deleted: 'marketing' // Always send
    }

    // Always send critical emails
    if (['password_reset', 'email_verification', 'account_deleted'].includes(template)) {
      return true
    }

    const category = preferenceMap[template]
    return preferences[category] === true
  }

  private async queueEmail(
    to: string | string[],
    template: EmailTemplate,
    data: any,
    scheduledFor?: Date
  ): Promise<void> {
    const supabase = await createServerClient()
    
    const { error } = await supabase
      .from('email_queue')
      .insert({
        to: Array.isArray(to) ? to.join(',') : to,
        template,
        data,
        scheduled_for: scheduledFor || new Date(),
        status: 'pending'
      })

    if (error) {
      logger.error('Error queueing email:', error)
      throw error
    }
  }

  async processEmailQueue(): Promise<void> {
    const supabase = await createServerClient()
    
    // Get pending emails scheduled for now or earlier
    const { data: emails, error } = await supabase
      .from('email_queue')
      .select('*')
      .eq('status', 'pending')
      .lte('scheduled_for', new Date().toISOString())
      .order('scheduled_for', { ascending: true })
      .limit(10)

    if (error) {
      logger.error('Error fetching email queue:', error)
      return
    }

    if (!emails || emails.length === 0) {
      return
    }

    // Process emails in parallel with rate limiting
    const results = await Promise.allSettled(
      emails.map((email, index) => 
        // Add delay to respect rate limits (adjust based on your Maileroo plan)
        new Promise(resolve => setTimeout(resolve, index * 100))
          .then(() => this.processEmailItem(email as EmailQueueItem))
      )
    )

    // Log any failures
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        logger.error(`Failed to process email ${emails[index].id}:`, result.reason)
      }
    })
  }

  private async processEmailItem(item: EmailQueueItem): Promise<void> {
    const supabase = await createServerClient()
    
    try {
      // Update status to processing
      await supabase
        .from('email_queue')
        .update({ status: 'processing', attempts: item.attempts + 1 })
        .eq('id', item.id)

      // Get email content
      const emailContent = this.getEmailContent(item.template, item.data)
      
      // Send email via Maileroo
      const mailerooRequest: MailerooSendRequest = {
        from: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
        to: item.to.split(','),
        subject: emailContent.subject,
        text: emailContent.text,
        html: emailContent.html,
        tags: [item.template, 'bookscribe'],
        tracking: true
      }

      const response = await this.sendViaMaileroo(mailerooRequest)

      if (response.status === 'failed') {
        throw new Error(response.error || 'Failed to send email')
      }

      // Update status to sent
      await supabase
        .from('email_queue')
        .update({ 
          status: 'sent', 
          sent_at: new Date().toISOString() 
        })
        .eq('id', item.id)

      // Log successful send
      await supabase
        .from('email_logs')
        .insert({
          to: item.to,
          template: item.template,
          provider: 'maileroo',
          provider_id: response.message_id
        })

      logger.info(`Email sent successfully: ${item.template} to ${item.to}`)
    } catch (error) {
      logger.error(`Error processing email ${item.id}:`, error)
      
      // Update with error
      await supabase
        .from('email_queue')
        .update({ 
          status: item.attempts >= 3 ? 'failed' : 'pending',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', item.id)
    }
  }

  private async sendViaMaileroo(request: MailerooSendRequest): Promise<MailerooResponse> {
    if (!this.apiKey) {
      throw new Error('Maileroo API key not configured')
    }

    try {
      const response = await fetch(`${this.baseUrl}/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Maileroo API error: ${response.status} - ${errorText}`)
      }

      const data = await response.json()
      return data as MailerooResponse
    } catch (error) {
      logger.error('Maileroo API request failed:', error)
      throw error
    }
  }

  private getEmailContent(template: EmailTemplate, data: any): {
    subject: string
    text: string
    html: string
  } {
    // Email templates with improved HTML formatting
    const templates = {
      welcome: {
        subject: 'Welcome to BookScribe AI!',
        text: `Hi ${data.userName},\n\nWelcome to BookScribe AI! We're excited to help you write your next masterpiece.\n\nGet started: ${data.loginUrl}\n\nHappy writing!\nThe BookScribe Team`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
          </head>
          <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #F9FAFB; border-radius: 12px; padding: 32px; margin-bottom: 24px;">
              <h1 style="color: #1F2937; margin: 0 0 16px 0; font-size: 28px;">Welcome to BookScribe AI!</h1>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">Hi ${data.userName},</p>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">We're thrilled to have you join our community of writers. BookScribe AI is here to help you bring your stories to life with the power of AI.</p>
              <a href="${data.loginUrl}" style="display: inline-block; background: #4F46E5; color: white; padding: 12px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">Get Started Writing</a>
            </div>
            <p style="color: #6B7280; font-size: 14px; margin: 0;">Happy writing!<br>The BookScribe Team</p>
          </body>
          </html>
        `
      },
      achievement_unlocked: {
        subject: `🏆 Achievement Unlocked: ${data.achievementName}!`,
        text: `Congratulations ${data.userName}!\n\nYou've unlocked the ${data.achievementTier} achievement: ${data.achievementName}\n\n${data.achievementDescription}\n\nKeep up the great work!`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
          </head>
          <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #F9FAFB; border-radius: 12px; padding: 32px; margin-bottom: 24px; text-align: center;">
              <h1 style="color: #1F2937; margin: 0 0 16px 0; font-size: 32px;">🏆 Achievement Unlocked!</h1>
              <p style="color: #4B5563; font-size: 18px; margin: 0 0 24px 0;">Congratulations ${data.userName}!</p>
              <div style="background: white; padding: 24px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin: 0 0 24px 0;">
                <h2 style="color: ${data.achievementTier === 'gold' ? '#F59E0B' : data.achievementTier === 'silver' ? '#6B7280' : data.achievementTier === 'platinum' ? '#7C3AED' : '#92400E'}; margin: 0 0 12px 0; font-size: 24px;">
                  ${data.achievementName}
                </h2>
                <p style="color: #6B7280; font-size: 16px; margin: 0 0 16px 0;">${data.achievementDescription}</p>
                <span style="display: inline-block; background: ${data.achievementTier === 'gold' ? '#FEF3C7' : data.achievementTier === 'silver' ? '#F3F4F6' : data.achievementTier === 'platinum' ? '#EDE9FE' : '#FED7AA'}; color: ${data.achievementTier === 'gold' ? '#92400E' : data.achievementTier === 'silver' ? '#374151' : data.achievementTier === 'platinum' ? '#5B21B6' : '#7C2D12'}; padding: 4px 12px; border-radius: 16px; font-size: 14px; font-weight: 600;">
                  ${data.achievementTier.charAt(0).toUpperCase() + data.achievementTier.slice(1)} Tier
                </span>
              </div>
              <p style="color: #4B5563; font-size: 16px; margin: 0;">Keep up the amazing work! 🎉</p>
            </div>
          </body>
          </html>
        `
      },
      collaboration_invite: {
        subject: `${data.inviterName} invited you to collaborate on "${data.projectName}"`,
        text: `${data.inviterName} has invited you to collaborate on their project "${data.projectName}" as a ${data.role}.\n\nAccept invitation: ${data.inviteUrl}`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
          </head>
          <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #F9FAFB; border-radius: 12px; padding: 32px; margin-bottom: 24px;">
              <h1 style="color: #1F2937; margin: 0 0 16px 0; font-size: 28px;">You've been invited to collaborate!</h1>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">
                <strong>${data.inviterName}</strong> has invited you to collaborate on their project:
              </p>
              <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4F46E5; margin: 0 0 24px 0;">
                <h2 style="color: #1F2937; margin: 0 0 8px 0; font-size: 20px;">"${data.projectName}"</h2>
                <p style="color: #6B7280; font-size: 14px; margin: 0;">
                  <strong>Your role:</strong> ${data.role.charAt(0).toUpperCase() + data.role.slice(1)}
                </p>
              </div>
              <a href="${data.inviteUrl}" style="display: inline-block; background: #4F46E5; color: white; padding: 12px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">Accept Invitation</a>
            </div>
            <p style="color: #6B7280; font-size: 14px; margin: 0;">This invitation will expire in 7 days.</p>
          </body>
          </html>
        `
      },
      export_ready: {
        subject: `Your ${data.exportFormat} export of "${data.projectName}" is ready!`,
        text: `Hi ${data.userName},\n\nYour ${data.exportFormat} export of "${data.projectName}" is ready for download.\n\nDownload: ${data.downloadUrl}\n\nThis link will expire on ${data.expiresAt}.\n\nThe BookScribe Team`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
          </head>
          <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #F9FAFB; border-radius: 12px; padding: 32px; margin-bottom: 24px;">
              <h1 style="color: #1F2937; margin: 0 0 16px 0; font-size: 28px;">Your export is ready! 📚</h1>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">Hi ${data.userName},</p>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">
                Your <strong>${data.exportFormat}</strong> export of <strong>"${data.projectName}"</strong> has been generated and is ready for download.
              </p>
              <a href="${data.downloadUrl}" style="display: inline-block; background: #4F46E5; color: white; padding: 12px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 0 0 16px 0;">Download Your Export</a>
              <p style="color: #EF4444; font-size: 14px; margin: 0;">
                ⚠️ This download link will expire on ${new Date(data.expiresAt).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}.
              </p>
            </div>
          </body>
          </html>
        `
      },
      weekly_progress: {
        subject: `📊 Your Weekly Writing Progress`,
        text: `Hi ${data.userName},\n\nHere's your writing progress for this week:\n\nWords Written: ${data.wordsWritten.toLocaleString()}\nChapters Completed: ${data.chaptersCompleted}\nWriting Streak: ${data.writingStreak} days\nProjects Worked On: ${data.projectsWorkedOn.join(', ')}\n\nKeep up the great work!\n\nThe BookScribe Team`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
          </head>
          <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #F9FAFB; border-radius: 12px; padding: 32px; margin-bottom: 24px;">
              <h1 style="color: #1F2937; margin: 0 0 16px 0; font-size: 28px;">📊 Your Weekly Writing Progress</h1>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">Hi ${data.userName},</p>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">Here's a summary of your writing activity this week:</p>
              
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin: 0 0 24px 0;">
                <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                  <p style="color: #6B7280; font-size: 14px; margin: 0 0 4px 0;">Words Written</p>
                  <p style="color: #1F2937; font-size: 32px; font-weight: 700; margin: 0;">${data.wordsWritten.toLocaleString()}</p>
                </div>
                <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                  <p style="color: #6B7280; font-size: 14px; margin: 0 0 4px 0;">Chapters Completed</p>
                  <p style="color: #1F2937; font-size: 32px; font-weight: 700; margin: 0;">${data.chaptersCompleted}</p>
                </div>
              </div>
              
              <div style="background: white; padding: 20px; border-radius: 8px; margin: 0 0 24px 0;">
                <p style="color: #6B7280; font-size: 14px; margin: 0 0 4px 0;">Writing Streak</p>
                <p style="color: #F59E0B; font-size: 24px; font-weight: 700; margin: 0;">🔥 ${data.writingStreak} days</p>
              </div>
              
              <div style="background: white; padding: 20px; border-radius: 8px;">
                <p style="color: #6B7280; font-size: 14px; margin: 0 0 8px 0;">Projects Worked On</p>
                <ul style="margin: 0; padding-left: 20px;">
                  ${data.projectsWorkedOn.map(project => `<li style="color: #4B5563;">${project}</li>`).join('')}
                </ul>
              </div>
            </div>
            <p style="color: #6B7280; font-size: 14px; margin: 0;">Keep up the amazing work! Every word counts. 💪</p>
          </body>
          </html>
        `
      },
      password_reset: {
        subject: 'Reset Your BookScribe Password',
        text: `You requested a password reset for your BookScribe account.\n\nReset your password: ${data.resetUrl}\n\nIf you didn't request this, please ignore this email.\n\nThe BookScribe Team`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
          </head>
          <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #F9FAFB; border-radius: 12px; padding: 32px; margin-bottom: 24px;">
              <h1 style="color: #1F2937; margin: 0 0 16px 0; font-size: 28px;">Reset Your Password</h1>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">
                You requested a password reset for your BookScribe account. Click the button below to create a new password.
              </p>
              <a href="${data.resetUrl}" style="display: inline-block; background: #4F46E5; color: white; padding: 12px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 0 0 24px 0;">Reset Password</a>
              <p style="color: #6B7280; font-size: 14px; margin: 0;">
                If you didn't request this password reset, please ignore this email. Your password won't be changed.
              </p>
            </div>
            <p style="color: #6B7280; font-size: 14px; margin: 0;">This link will expire in 1 hour for security reasons.</p>
          </body>
          </html>
        `
      },
      email_verification: {
        subject: 'Verify Your BookScribe Email',
        text: `Please verify your email address to complete your BookScribe registration.\n\nVerify email: ${data.verificationUrl}\n\nThe BookScribe Team`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
          </head>
          <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #F9FAFB; border-radius: 12px; padding: 32px; margin-bottom: 24px;">
              <h1 style="color: #1F2937; margin: 0 0 16px 0; font-size: 28px;">Verify Your Email</h1>
              <p style="color: #4B5563; font-size: 16px; margin: 0 0 24px 0;">
                Please verify your email address to complete your BookScribe registration and start writing!
              </p>
              <a href="${data.verificationUrl}" style="display: inline-block; background: #4F46E5; color: white; padding: 12px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">Verify Email Address</a>
            </div>
            <p style="color: #6B7280; font-size: 14px; margin: 0;">This verification link will expire in 24 hours.</p>
          </body>
          </html>
        `
      }
    }

    const content = templates[template] || {
      subject: 'BookScribe AI Notification',
      text: JSON.stringify(data, null, 2),
      html: `<pre>${JSON.stringify(data, null, 2)}</pre>`
    }

    return content
  }

  async testEmailConnection(): Promise<boolean> {
    try {
      await this.initialize()
      
      // Send a test email via Maileroo
      const testRequest: MailerooSendRequest = {
        from: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
        to: process.env.TEST_EMAIL_ADDRESS || '<EMAIL>',
        subject: 'BookScribe Maileroo Email Service Test',
        text: 'This is a test email from BookScribe AI using Maileroo.',
        html: '<p>This is a test email from BookScribe AI using <strong>Maileroo</strong>.</p>',
        tags: ['test', 'bookscribe']
      }

      const response = await this.sendViaMaileroo(testRequest)
      
      if (response.status === 'failed') {
        logger.error('Maileroo test email failed:', response.error)
        return false
      }

      logger.info('Maileroo email connection test successful:', response.message_id)
      return true
    } catch (error) {
      logger.error('Email connection test failed:', error)
      return false
    }
  }

  // Get email delivery statistics from Maileroo
  async getEmailStats(startDate?: Date, endDate?: Date): Promise<any> {
    if (!this.apiKey) {
      throw new Error('Maileroo API key not configured')
    }

    try {
      const params = new URLSearchParams()
      if (startDate) params.append('start_date', startDate.toISOString())
      if (endDate) params.append('end_date', endDate.toISOString())

      const response = await fetch(`${this.baseUrl}/stats?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to get email stats: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      logger.error('Failed to get email stats:', error)
      throw error
    }
  }

  async updateEmailPreferences(userId: string, preferences: Partial<{
    marketing: boolean
    progress: boolean
    achievements: boolean
    collaboration: boolean
    newsletter: boolean
  }>): Promise<void> {
    const supabase = await createServerClient()
    
    const { error } = await supabase
      .from('email_preferences')
      .upsert({
        user_id: userId,
        ...preferences,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      
    if (error) {
      logger.error('Failed to update email preferences:', error)
      throw error
    }
  }

  async getEmailPreferences(userId: string): Promise<any> {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('email_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()
      
    if (error && error.code !== 'PGRST116') { // Not found is ok
      logger.error('Failed to get email preferences:', error)
      throw error
    }
    
    // Return default preferences if none exist
    return data || {
      marketing: true,
      progress: true,
      achievements: true,
      collaboration: true,
      newsletter: true
    }
  }

  // Helper method for sending collaboration invites
  async sendCollaborationInvite(options: {
    to: string
    inviterName: string
    projectName: string
    inviteUrl: string
    role: 'viewer' | 'editor' | 'admin'
    message?: string
  }): Promise<void> {
    return this.sendEmail(
      options.to,
      EmailTemplates.COLLABORATION_INVITE,
      {
        inviterName: options.inviterName,
        projectName: options.projectName,
        inviteUrl: options.inviteUrl,
        role: options.role
      }
    )
  }

  // Generic email send method for backward compatibility
  async sendGenericEmail(options: {
    to: string
    subject: string
    html: string
    text?: string
    from?: string
  }): Promise<void> {
    const apiKey = this.config.mailerooApiKey
    if (!apiKey) {
      throw new Error('Maileroo API key not configured')
    }

    const provider = new MailerooProvider(apiKey)
    const result = await provider.sendEmail({
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
      from: options.from || this.config.defaultFromEmail
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to send email')
    }
  }
}

// Export singleton instance
export const mailerooEmailService = MailerooEmailService.getInstance()

// Export convenience functions for backward compatibility
export const sendEmail = mailerooEmailService.sendGenericEmail.bind(mailerooEmailService)
export const sendCollaborationInvite = mailerooEmailService.sendCollaborationInvite.bind(mailerooEmailService)