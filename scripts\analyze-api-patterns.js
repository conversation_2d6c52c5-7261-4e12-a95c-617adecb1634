#!/usr/bin/env node

/**
 * <PERSON>ript to analyze API response patterns and identify inconsistencies
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Common response patterns to look for
const responsePatterns = [
  // Success responses
  { pattern: /NextResponse\.json\(\s*{\s*success:\s*true/g, type: 'success-true' },
  { pattern: /NextResponse\.json\(\s*{\s*data:/g, type: 'data-wrapper' },
  { pattern: /NextResponse\.json\(\s*\w+\s*\)/g, type: 'direct-data' },
  { pattern: /apiResponse\(/g, type: 'api-response-helper' },
  
  // Error responses
  { pattern: /NextResponse\.json\(\s*{\s*error:/g, type: 'error-object' },
  { pattern: /handleAPIError\(/g, type: 'handle-api-error' },
  { pattern: /handleRouteError\(/g, type: 'handle-route-error' },
  { pattern: /apiError\(/g, type: 'api-error-helper' },
  { pattern: /createErrorResponse\(/g, type: 'create-error-response' },
  
  // Status codes
  { pattern: /status:\s*200/g, type: 'status-200' },
  { pattern: /status:\s*201/g, type: 'status-201' },
  { pattern: /status:\s*400/g, type: 'status-400' },
  { pattern: /status:\s*401/g, type: 'status-401' },
  { pattern: /status:\s*403/g, type: 'status-403' },
  { pattern: /status:\s*404/g, type: 'status-404' },
  { pattern: /status:\s*500/g, type: 'status-500' },
  
  // Pagination patterns
  { pattern: /createPaginatedResponse\(/g, type: 'paginated-response' },
  { pattern: /pagination:\s*{/g, type: 'manual-pagination' },
  
  // Try-catch patterns
  { pattern: /try\s*{\s*[\s\S]*?}\s*catch/g, type: 'try-catch' },
  { pattern: /\.catch\(/g, type: 'promise-catch' }
];

async function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(path.join(__dirname, '../src/app/api'), filePath);
  
  const patterns = {};
  let hasTryCatch = false;
  
  responsePatterns.forEach(({ pattern, type }) => {
    const matches = content.match(pattern);
    if (matches) {
      patterns[type] = matches.length;
      if (type === 'try-catch') hasTryCatch = true;
    }
  });
  
  // Check for consistent error handling
  const hasErrorHandling = patterns['handle-api-error'] || 
                          patterns['handle-route-error'] || 
                          patterns['api-error-helper'] ||
                          patterns['error-object'];
  
  // Check response style
  let responseStyle = 'unknown';
  if (patterns['success-true']) responseStyle = 'success-boolean';
  else if (patterns['data-wrapper']) responseStyle = 'data-wrapper';
  else if (patterns['direct-data']) responseStyle = 'direct';
  else if (patterns['api-response-helper']) responseStyle = 'helper';
  
  return {
    file: relativePath,
    patterns,
    hasTryCatch,
    hasErrorHandling,
    responseStyle,
    methods: extractMethods(content)
  };
}

function extractMethods(content) {
  const methods = [];
  const methodPatterns = [
    /export\s+(?:const|async\s+function)\s+(GET)/g,
    /export\s+(?:const|async\s+function)\s+(POST)/g,
    /export\s+(?:const|async\s+function)\s+(PUT)/g,
    /export\s+(?:const|async\s+function)\s+(PATCH)/g,
    /export\s+(?:const|async\s+function)\s+(DELETE)/g
  ];
  
  methodPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      methods.push(pattern.source.match(/\((.*?)\)/)[1]);
    }
  });
  
  return methods;
}

async function main() {
  console.log('🔍 Analyzing API response patterns...\n');
  
  const apiPath = path.join(__dirname, '../src/app/api');
  const files = glob.sync('**/*.ts', { cwd: apiPath, absolute: true });
  
  const results = [];
  
  for (const file of files) {
    const analysis = await analyzeFile(file);
    results.push(analysis);
  }
  
  // Generate statistics
  const stats = {
    totalFiles: results.length,
    responseStyles: {},
    errorHandling: {
      withTryCatch: 0,
      withErrorHandling: 0,
      noErrorHandling: 0
    },
    statusCodes: {},
    patterns: {}
  };
  
  results.forEach(result => {
    // Count response styles
    stats.responseStyles[result.responseStyle] = 
      (stats.responseStyles[result.responseStyle] || 0) + 1;
    
    // Count error handling
    if (result.hasTryCatch) stats.errorHandling.withTryCatch++;
    if (result.hasErrorHandling) stats.errorHandling.withErrorHandling++;
    else stats.errorHandling.noErrorHandling++;
    
    // Aggregate pattern counts
    Object.entries(result.patterns).forEach(([pattern, count]) => {
      stats.patterns[pattern] = (stats.patterns[pattern] || 0) + count;
    });
  });
  
  // Print summary
  console.log('📊 Summary:\n');
  console.log(`Total API routes analyzed: ${stats.totalFiles}`);
  console.log('\n🎨 Response Styles:');
  Object.entries(stats.responseStyles).forEach(([style, count]) => {
    console.log(`  ${style}: ${count} files (${Math.round(count / stats.totalFiles * 100)}%)`);
  });
  
  console.log('\n⚠️  Error Handling:');
  console.log(`  With try-catch: ${stats.errorHandling.withTryCatch}`);
  console.log(`  With error handler: ${stats.errorHandling.withErrorHandling}`);
  console.log(`  No error handling: ${stats.errorHandling.noErrorHandling}`);
  
  console.log('\n📍 Pattern Usage:');
  Object.entries(stats.patterns)
    .filter(([_, count]) => count > 0)
    .sort(([, a], [, b]) => b - a)
    .forEach(([pattern, count]) => {
      console.log(`  ${pattern}: ${count} occurrences`);
    });
  
  // Find inconsistent files
  console.log('\n⚠️  Files needing attention:');
  
  const inconsistentFiles = results.filter(r => 
    !r.hasTryCatch || 
    !r.hasErrorHandling || 
    r.responseStyle === 'unknown'
  );
  
  inconsistentFiles.forEach(file => {
    const issues = [];
    if (!file.hasTryCatch) issues.push('no try-catch');
    if (!file.hasErrorHandling) issues.push('no error handling');
    if (file.responseStyle === 'unknown') issues.push('unknown response style');
    
    console.log(`  ${file.file} - Issues: ${issues.join(', ')}`);
  });
  
  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    summary: stats,
    files: results,
    recommendations: [
      'Standardize on data-wrapper response style ({ data: ... })',
      'Use handleAPIError for all error responses',
      'Ensure all routes have try-catch blocks',
      'Use consistent status codes (201 for creation, 200 for success)',
      'Consider using unified middleware for common patterns'
    ]
  };
  
  const reportPath = path.join(__dirname, 'api-patterns-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n✅ Detailed report saved to: ${reportPath}`);
}

main().catch(console.error);