import { taskQueueService } from '@/lib/services/task-queue-service'
import { logger } from '@/lib/services/logger'

export class TaskQueueWorker {
  private isRunning = false
  private processInterval: NodeJS.Timeout | null = null
  private readonly PROCESS_INTERVAL_MS = 10000 // 10 seconds
  private readonly CLEANUP_INTERVAL_MS = 3600000 // 1 hour

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Task queue worker is already running')
      return
    }

    this.isRunning = true
    logger.info('Starting task queue worker')

    // Process queue immediately on start
    await this.processQueue()

    // Set up interval for regular processing
    this.processInterval = setInterval(async () => {
      await this.processQueue()
    }, this.PROCESS_INTERVAL_MS)

    // Set up cleanup interval
    setInterval(async () => {
      await this.cleanup()
    }, this.CLEANUP_INTERVAL_MS)
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    this.isRunning = false
    
    if (this.processInterval) {
      clearInterval(this.processInterval)
      this.processInterval = null
    }

    logger.info('Task queue worker stopped')
  }

  private async processQueue(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    try {
      await taskQueueService.processTasks()
    } catch (error) {
      logger.error('Error processing task queue:', error)
    }
  }

  private async cleanup(): Promise<void> {
    try {
      const cleaned = await taskQueueService.cleanupOldTasks(7)
      if (cleaned > 0) {
        logger.info(`Cleaned up ${cleaned} old tasks`)
      }
    } catch (error) {
      logger.error('Error cleaning up old tasks:', error)
    }
  }
}

// Create singleton instance
export const taskQueueWorker = new TaskQueueWorker()