'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, Home, LogIn, ArrowRight } from 'lucide-react'
import { TIME_MS } from '@/lib/constants'

export default function SignedOutPage() {
  const [countdown, setCountdown] = useState(10)
  const router = useRouter()

  useEffect(() => {
    // Countdown timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          router.push('/')
          return 0
        }
        return prev - 1
      })
    }, TIME_MS.SECOND)

    return () => clearInterval(timer)
  }, [router])

  const handleGoHome = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="pb-4">
          <div className="mx-auto mb-4 w-16 h-16 bg-success-light dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-success dark:text-green-400" />
          </div>
          <CardTitle className="text-2xl">Successfully Signed Out</CardTitle>
          <CardDescription className="text-base">
            You have been safely signed out of your BookScribe AI account.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-sm text-muted-foreground">
            <p>Your session has been ended and your data is secure.</p>
            <p className="mt-2">
              Redirecting to homepage in <span className="font-semibold text-primary">{countdown}</span> seconds...
            </p>
          </div>

          <div className="flex flex-col gap-3">
            <Button onClick={handleGoHome} className="w-full">
              <Home className="w-4 h-4 mr-2" />
              Go to Homepage
            </Button>
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/login">
                <LogIn className="w-4 h-4 mr-2" />
                Sign In Again
              </Link>
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-xs text-muted-foreground mb-3">
              Thank you for using BookScribe AI
            </p>
            <div className="flex justify-center gap-4 text-xs">
              <Link 
                href="/about" 
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                About
              </Link>
              <Link 
                href="/pricing" 
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                Pricing
              </Link>
              <Link 
                href="/contact" 
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                Contact
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
