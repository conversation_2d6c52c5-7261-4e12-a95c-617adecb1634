import React from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'

interface BaseFieldProps {
  id: string
  label: string
  error?: string
  required?: boolean
  className?: string
  disabled?: boolean
  description?: string
}

interface InputFieldProps extends BaseFieldProps {
  type?: 'text' | 'email' | 'password' | 'number'
  value: string | number
  onChange: (value: string | number) => void
  onBlur?: () => void
  placeholder?: string
  min?: number
  max?: number
}

interface TextareaFieldProps extends BaseFieldProps {
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  rows?: number
}

interface SelectFieldProps extends BaseFieldProps {
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  options: { value: string; label: string }[]
}

interface CheckboxGroupFieldProps extends BaseFieldProps {
  value: string[]
  onChange: (value: string[]) => void
  options: { value: string; label: string }[]
  columns?: number
}

// Base field wrapper component
const FieldWrapper: React.FC<{
  id: string
  label: string
  error?: string
  required?: boolean
  className?: string
  description?: string
  children: React.ReactNode
}> = ({ id, label, error, required, className, description, children }) => (
  <div className={cn('space-y-2', className)}>
    <Label htmlFor={id} className={cn(error && 'text-destructive')}>
      {label}
      {required && <span className="text-destructive ml-1">*</span>}
    </Label>
    {description && (
      <p className="text-sm text-muted-foreground">{description}</p>
    )}
    {children}
    {error && (
      <p className="text-sm text-destructive" role="alert" aria-live="polite">
        {error}
      </p>
    )}
  </div>
)

// Input field component
export const InputField: React.FC<InputFieldProps> = ({
  id,
  label,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  required,
  className,
  disabled,
  description,
  placeholder,
  min,
  max,
}) => (
  <FieldWrapper
    id={id}
    label={label}
    error={error}
    required={required}
    className={className}
    description={description}
  >
    <Input
      id={id}
      type={type}
      value={value}
      onChange={(e) => {
        const newValue = type === 'number' ? Number(e.target.value) : e.target.value
        onChange(newValue)
      }}
      onBlur={onBlur}
      placeholder={placeholder}
      disabled={disabled}
      min={min}
      max={max}
      className={cn(error && 'border-destructive focus:border-destructive')}
      aria-invalid={!!error}
      aria-describedby={error ? `${id}-error` : undefined}
    />
  </FieldWrapper>
)

// Textarea field component
export const TextareaField: React.FC<TextareaFieldProps> = ({
  id,
  label,
  value,
  onChange,
  onBlur,
  error,
  required,
  className,
  disabled,
  description,
  placeholder,
  rows = 3,
}) => (
  <FieldWrapper
    id={id}
    label={label}
    error={error}
    required={required}
    className={className}
    description={description}
  >
    <Textarea
      id={id}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      onBlur={onBlur}
      placeholder={placeholder}
      disabled={disabled}
      rows={rows}
      className={cn(error && 'border-destructive focus:border-destructive')}
      aria-invalid={!!error}
      aria-describedby={error ? `${id}-error` : undefined}
    />
  </FieldWrapper>
)

// Select field component
export const SelectField: React.FC<SelectFieldProps> = ({
  id,
  label,
  value,
  onChange,
  onBlur,
  error,
  required,
  className,
  disabled,
  description,
  placeholder,
  options,
}) => (
  <FieldWrapper
    id={id}
    label={label}
    error={error}
    required={required}
    className={className}
    description={description}
  >
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger
        className={cn(error && 'border-destructive focus:border-destructive')}
        aria-invalid={!!error}
        aria-describedby={error ? `${id}-error` : undefined}
        onBlur={onBlur}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  </FieldWrapper>
)

// Checkbox group field component
export const CheckboxGroupField: React.FC<CheckboxGroupFieldProps> = ({
  id,
  label,
  value,
  onChange,
  error,
  required,
  className,
  disabled,
  description,
  options,
  columns = 2,
}) => {
  const handleToggle = (optionValue: string) => {
    if (value.includes(optionValue)) {
      onChange(value.filter((v) => v !== optionValue))
    } else {
      onChange([...value, optionValue])
    }
  }

  return (
    <FieldWrapper
      id={id}
      label={label}
      error={error}
      required={required}
      className={className}
      description={description}
    >
      <div className={`grid grid-cols-${columns} gap-3`}>
        {options.map((option) => (
          <label
            key={option.value}
            className={cn(
              'flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors',
              value.includes(option.value)
                ? 'border-info bg-info-light dark:bg-blue-950/20'
                : 'border-slate-200 hover:border-slate-300 dark:border-slate-700',
              disabled && 'opacity-50 cursor-not-allowed',
              error && 'border-destructive'
            )}
          >
            <input
              type="checkbox"
              checked={value.includes(option.value)}
              onChange={() => handleToggle(option.value)}
              disabled={disabled}
              className="rounded"
              aria-describedby={error ? `${id}-error` : undefined}
            />
            <span className="text-sm font-medium">{option.label}</span>
          </label>
        ))}
      </div>
    </FieldWrapper>
  )
}