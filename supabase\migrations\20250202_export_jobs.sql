-- Create export_jobs table for tracking export progress
CREATE TABLE IF NOT EXISTS export_jobs (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_title varchar(255) NOT NULL,
  format varchar(20) NOT NULL CHECK (format IN ('txt', 'markdown', 'docx', 'pdf', 'epub')),
  status varchar(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  progress integer DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  current_step text,
  file_url text,
  file_size integer,
  error text,
  estimated_time integer, -- seconds
  processing_time integer, -- seconds
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for efficient querying
CREATE INDEX idx_export_jobs_user_id ON export_jobs(user_id);
CREATE INDEX idx_export_jobs_project_id ON export_jobs(project_id);
CREATE INDEX idx_export_jobs_status ON export_jobs(status);
CREATE INDEX idx_export_jobs_created_at ON export_jobs(created_at DESC);

-- Add RLS policies
ALTER TABLE export_jobs ENABLE ROW LEVEL SECURITY;

-- Users can read their own export jobs
CREATE POLICY "Users can read own export jobs" ON export_jobs
  FOR SELECT
  USING (user_id = auth.uid());

-- Users can create export jobs for their projects
CREATE POLICY "Users can create export jobs" ON export_jobs
  FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    )
  );

-- Users can update their own export jobs
CREATE POLICY "Users can update own export jobs" ON export_jobs
  FOR UPDATE
  USING (user_id = auth.uid());

-- Users can delete their own export jobs
CREATE POLICY "Users can delete own export jobs" ON export_jobs
  FOR DELETE
  USING (user_id = auth.uid());

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_export_job_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_export_jobs_timestamp
  BEFORE UPDATE ON export_jobs
  FOR EACH ROW
  EXECUTE FUNCTION update_export_job_timestamp();

-- Function to clean up old completed/failed jobs (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_export_jobs()
RETURNS void AS $$
BEGIN
  DELETE FROM export_jobs
  WHERE status IN ('completed', 'failed', 'cancelled')
    AND created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Optional: Create a scheduled job to run cleanup (if using pg_cron)
-- SELECT cron.schedule('cleanup-export-jobs', '0 3 * * *', 'SELECT cleanup_old_export_jobs();');