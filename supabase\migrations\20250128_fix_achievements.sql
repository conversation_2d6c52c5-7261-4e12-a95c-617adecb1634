-- Check and fix achievement system tables

-- First, let's check what exists
DO $$ 
BEGIN
    -- Check if achievements table exists and has data
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'achievements') THEN
        RAISE NOTICE 'achievements table exists';
        
        -- Check if it has the required columns
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'achievements' AND column_name = 'title') THEN
            -- Old schema might have 'name' instead of 'title'
            IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'achievements' AND column_name = 'name') THEN
                ALTER TABLE achievements RENAME COLUMN name TO title;
                RAISE NOTICE 'Renamed name column to title';
            END IF;
        END IF;
    END IF;

    -- Check if user_achievements exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_achievements') THEN
        RAISE NOTICE 'user_achievements table exists';
    END IF;

    -- Check if achievement_progress exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'achievement_progress') THEN
        RAISE NOTICE 'achievement_progress table exists';
    END IF;
END $$;

-- Add missing columns to user_achievements if needed
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'progress') THEN
        ALTER TABLE user_achievements ADD COLUMN progress INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'metadata') THEN
        ALTER TABLE user_achievements ADD COLUMN metadata JSONB;
    END IF;
END $$;

-- Update the user_achievements table to include achievement details
-- This creates a view that joins achievements with user progress
CREATE OR REPLACE VIEW user_achievements_view AS
SELECT 
    ua.id,
    ua.user_id,
    ua.achievement_id,
    a.code,
    a.title,
    a.description,
    a.points,
    a.tier,
    a.category,
    a.icon,
    ua.unlocked_at,
    ua.progress,
    ua.metadata,
    CASE 
        WHEN ua.unlocked_at IS NOT NULL THEN 'unlocked'
        ELSE 'locked'
    END as status
FROM achievements a
LEFT JOIN user_achievements ua ON ua.achievement_id = a.id;

-- Grant permissions on the view
GRANT SELECT ON user_achievements_view TO authenticated;

-- Create or replace the check_and_unlock_achievements function
-- This version handles the case where tables might already have data
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE (newly_unlocked UUID[]) AS $$
DECLARE
  v_newly_unlocked UUID[];
  v_achievement RECORD;
  v_current_progress INTEGER;
  v_already_unlocked BOOLEAN;
BEGIN
  v_newly_unlocked := ARRAY[]::UUID[];
  
  -- Only process if tables exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'achievements') THEN
    RETURN QUERY SELECT v_newly_unlocked;
    RETURN;
  END IF;
  
  -- Check each achievement
  FOR v_achievement IN SELECT * FROM achievements LOOP
    -- Check if already unlocked
    SELECT EXISTS(
      SELECT 1 FROM user_achievements 
      WHERE user_id = p_user_id AND achievement_id = v_achievement.id
    ) INTO v_already_unlocked;
    
    IF NOT v_already_unlocked THEN
      -- Check criteria based on type
      CASE v_achievement.criteria->>'type'
        WHEN 'word_count' THEN
          SELECT COALESCE(SUM(word_count), 0)
          FROM chapters c
          JOIN projects p ON p.id = c.project_id
          WHERE p.user_id = p_user_id
          INTO v_current_progress;
          
        WHEN 'chapters_completed' THEN
          SELECT COUNT(*)
          FROM chapters c
          JOIN projects p ON p.id = c.project_id
          WHERE p.user_id = p_user_id AND c.status = 'published'
          INTO v_current_progress;
          
        WHEN 'projects_created' THEN
          SELECT COUNT(*)
          FROM projects
          WHERE user_id = p_user_id
          INTO v_current_progress;
          
        WHEN 'series_created' THEN
          SELECT COUNT(*)
          FROM series
          WHERE user_id = p_user_id
          INTO v_current_progress;
          
        ELSE
          v_current_progress := 0;
      END CASE;
      
      -- Check if criteria met
      IF v_current_progress >= COALESCE((v_achievement.criteria->>'value')::INTEGER, 0) THEN
        -- Unlock achievement
        INSERT INTO user_achievements (user_id, achievement_id)
        VALUES (p_user_id, v_achievement.id)
        ON CONFLICT (user_id, achievement_id) DO NOTHING;
        
        -- Only add to newly_unlocked if insert succeeded
        IF FOUND THEN
          v_newly_unlocked := array_append(v_newly_unlocked, v_achievement.id);
        END IF;
      END IF;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT v_newly_unlocked;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION check_and_unlock_achievements(UUID) TO authenticated;

-- Insert sample achievements if none exist
INSERT INTO achievements (code, title, description, points, tier, category, criteria, icon) 
SELECT * FROM (VALUES
  ('first_words', 'First Words', 'Write your first 100 words', 10, 'bronze', 'writing', '{"type": "word_count", "value": 100}', 'edit'),
  ('novice_writer', 'Novice Writer', 'Write 1,000 words', 25, 'bronze', 'writing', '{"type": "word_count", "value": 1000}', 'edit-2'),
  ('first_chapter', 'Chapter One', 'Complete your first chapter', 20, 'bronze', 'writing', '{"type": "chapters_completed", "value": 1}', 'bookmark'),
  ('first_project', 'First Project', 'Create your first project', 15, 'bronze', 'exploration', '{"type": "projects_created", "value": 1}', 'folder-plus')
) AS v(code, title, description, points, tier, category, criteria, icon)
WHERE NOT EXISTS (SELECT 1 FROM achievements WHERE code = v.code);

-- Return success message
DO $$ 
BEGIN
    RAISE NOTICE 'Achievement system check completed successfully';
END $$;