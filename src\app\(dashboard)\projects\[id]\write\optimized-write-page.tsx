'use client'

import { useEffect, useState, lazy, Suspense } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import { getBrowserClient } from '@/lib/supabase'
import { useEditorStore } from '@/stores/editor-store'
import { WritePageHeader } from '@/components/editor/write-page-header'
import { WritePageEditor } from '@/components/editor/write-page-editor'
import { SaveStatusIndicator, useOnlineStatus } from '@/components/version-history/save-status-indicator'
import { checkProjectAccess } from '@/lib/auth/collaboration-auth-client'
import { useSubscription } from '@/lib/subscription'
import { useAutoSave, useUnsavedChanges } from '@/hooks/use-auto-save'
import { useChapterGeneration, UserChanges } from '@/hooks/use-chapter-generation'
import { toast } from '@/hooks/use-toast'
import { PanelProvider, usePanelSystem, PanelLayout } from '@/lib/panels'
import { PanelMenu } from '@/components/editor/panel-menu'
import { logger } from '@/lib/services/logger'
import { Database } from '@/lib/db/types'
import { User } from '@supabase/supabase-js'
import { Skeleton } from '@/components/ui/skeleton'

// Lazy load heavy components
const BookImportDialog = lazy(() => import('@/components/import/book-import-dialog').then(mod => ({ 
  default: mod.BookImportDialog 
})))

const CollaborationIndicator = lazy(() => import('@/components/collaboration/collaboration-indicator-enhanced').then(mod => ({ 
  default: mod.CollaborationIndicatorEnhanced 
})))

const TypingIndicator = lazy(() => import('@/components/collaboration/typing-indicator').then(mod => ({ 
  default: mod.TypingIndicator 
})))

// Lazy load collaboration service
const loadCollaborationService = () => import('@/lib/services/unified-collaboration-service').then(mod => ({
  collaborationServiceRealtime: mod.collaborationService
}))

const loadCollaborationTypes = () => import('@/types/collaboration').then(mod => ({
  CollaborationUser: mod.CollaborationUser
}))

type Project = Database['public']['Tables']['projects']['Row']
type Chapter = Database['public']['Tables']['chapters']['Row']

// Loading skeleton for the page
const PageSkeleton = () => (
  <div className="flex flex-col h-screen">
    <div className="border-b p-4">
      <Skeleton className="h-10 w-full" />
    </div>
    <div className="flex-1 p-4">
      <Skeleton className="h-full w-full" />
    </div>
  </div>
)

function OptimizedWritePageContent() {
  const params = useParams()
  const searchParams = useSearchParams()
  const projectId = params.id as string
  const chapterId = searchParams.get('chapter')
  
  const {
    content,
    setContent,
    selectedText,
    currentChapter,
    setCurrentChapter,
    updateChapterList
  } = useEditorStore()

  const [project, setProject] = useState<Project | null>(null)
  const [currentChapterData, setCurrentChapterData] = useState<Chapter | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [originalGeneratedContent, setOriginalGeneratedContent] = useState<string>('')
  const [focusMode, setFocusMode] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [isReviewOpen, setIsReviewOpen] = useState(false)
  
  const supabase = getBrowserClient()
  const isOnline = useOnlineStatus()
  const { setUnsavedChanges } = useUnsavedChanges()
  const subscription = useSubscription()
  
  // Collaboration state (lazy loaded)
  const [collaborationEnabled, setCollaborationEnabled] = useState(false)
  const [collaborationUsers, setCollaborationUsers] = useState<any[]>([])
  const [collaborationService, setCollaborationService] = useState<any>(null)
  
  // Load project and chapter data
  useEffect(() => {
    const loadData = async () => {
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser()
        setUser(user)

        // Load project
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single()

        if (projectError) throw projectError
        setProject(projectData)

        // Load chapters
        const { data: chapters, error: chaptersError } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId)
          .order('chapter_number', { ascending: true })

        if (chaptersError) throw chaptersError
        updateChapterList(chapters || [])

        // Load current chapter if specified
        if (chapterId) {
          const chapter = chapters?.find(c => c.id === chapterId)
          if (chapter) {
            setCurrentChapterData(chapter)
            setContent(chapter.content || '')
            setOriginalGeneratedContent(chapter.generated_content || '')
          }
        }
      } catch (error) {
        logger.error('Error loading project data:', error)
        toast({
          title: 'Error',
          description: 'Failed to load project data',
          variant: 'destructive'
        })
      }
    }

    loadData()
  }, [projectId, chapterId])

  // Load collaboration features for paid users
  useEffect(() => {
    const loadCollaboration = async () => {
      if (user && projectId && (subscription?.plan === 'studio' || subscription?.plan === 'professional')) {
        const hasAccess = await checkProjectAccess(projectId, user.id)
        if (hasAccess) {
          const { collaborationServiceRealtime } = await loadCollaborationService()
          setCollaborationService(collaborationServiceRealtime)
          setCollaborationEnabled(true)
        }
      }
    }
    
    loadCollaboration()
  }, [user, projectId, subscription])

  // Auto-save functionality
  const saveContent = useAutoSave({
    content,
    chapterId,
    projectId,
    onSaveStart: () => setIsSaving(true),
    onSaveComplete: () => {
      setIsSaving(false)
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
    },
    onSaveError: (error) => {
      setIsSaving(false)
      toast({
        title: 'Save failed',
        description: error.message,
        variant: 'destructive'
      })
    }
  })

  // Chapter generation
  const { generateChapter, isGenerating } = useChapterGeneration({
    projectId,
    onSuccess: (generatedContent) => {
      setContent(generatedContent)
      setOriginalGeneratedContent(generatedContent)
      toast({
        title: 'Chapter generated!',
        description: 'AI has created your chapter content.'
      })
    }
  })

  const handleContentChange = (value: string) => {
    setContent(value)
    setHasUnsavedChanges(true)
    setUnsavedChanges(true)
  }

  const handleGenerateChapter = () => {
    if (!currentChapterData) {
      toast({
        title: 'No chapter selected',
        description: 'Please select a chapter first',
        variant: 'destructive'
      })
      return
    }

    generateChapter(currentChapterData.id)
  }

  if (!project) {
    return <PageSkeleton />
  }

  return (
    <PanelProvider>
      <div className="flex flex-col h-screen bg-background">
        {/* Header */}
        <WritePageHeader
          projectId={projectId}
          projectTitle={project.title}
          isSaving={isSaving}
          hasUnsavedChanges={hasUnsavedChanges}
          lastSaved={lastSaved}
          focusMode={focusMode}
          onSave={saveContent}
          onToggleFocusMode={() => setFocusMode(!focusMode)}
          onImportBook={() => setShowImportDialog(true)}
          onGenerateChapter={handleGenerateChapter}
          onToggleReview={() => setIsReviewOpen(!isReviewOpen)}
          isReviewOpen={isReviewOpen}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex overflow-hidden">
          <PanelLayout
            mainContent={
              <WritePageEditor
                projectId={projectId}
                chapterId={chapterId}
                content={content}
                collaborationEnabled={collaborationEnabled}
                collaborationUsers={collaborationUsers}
                currentChapterData={currentChapterData}
                onChange={handleContentChange}
                onSelectionChange={setSelectedText}
                userId={user?.id}
              />
            }
          />
        </div>

        {/* Collaboration Indicators */}
        {collaborationEnabled && (
          <Suspense fallback={null}>
            <CollaborationIndicator
              users={collaborationUsers}
              projectId={projectId}
            />
            <TypingIndicator
              typingUsers={new Set()}
              users={collaborationUsers}
            />
          </Suspense>
        )}

        {/* Import Dialog */}
        {showImportDialog && (
          <Suspense fallback={null}>
            <BookImportDialog
              projectId={projectId}
              onClose={() => setShowImportDialog(false)}
              onImportComplete={() => {
                setShowImportDialog(false)
                toast({
                  title: 'Import complete',
                  description: 'Your book has been imported successfully'
                })
              }}
            />
          </Suspense>
        )}

        {/* Panel Menu */}
        <PanelMenu />
      </div>
    </PanelProvider>
  )
}

export default function OptimizedWritePage() {
  return (
    <Suspense fallback={<PageSkeleton />}>
      <OptimizedWritePageContent />
    </Suspense>
  )
}