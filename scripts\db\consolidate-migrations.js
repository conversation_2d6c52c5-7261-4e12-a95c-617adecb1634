#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Analyzing and consolidating database migrations...\n');

const migrationsDir = path.join(process.cwd(), 'supabase/migrations');
const migrations = [];

// Read all migration files
const files = fs.readdirSync(migrationsDir)
  .filter(f => f.endsWith('.sql') && !f.includes('rollback') && !f.includes('README'))
  .sort();

console.log(`Found ${files.length} migration files\n`);

// Group migrations by category
const categories = {
  core: [],
  features: [],
  fixes: [],
  performance: [],
  analytics: [],
  achievements: [],
  collaboration: []
};

// Categorize migrations
files.forEach(file => {
  const name = file.toLowerCase();
  
  if (name.includes('fix') || name.includes('rollback')) {
    categories.fixes.push(file);
  } else if (name.includes('performance') || name.includes('index')) {
    categories.performance.push(file);
  } else if (name.includes('analytics') || name.includes('usage') || name.includes('tracking')) {
    categories.analytics.push(file);
  } else if (name.includes('achievement')) {
    categories.achievements.push(file);
  } else if (name.includes('collaboration') || name.includes('collaborator')) {
    categories.collaboration.push(file);
  } else if (name.includes('enhanced_schema') || name.includes('missing_tables') || name.includes('critical')) {
    categories.core.push(file);
  } else {
    categories.features.push(file);
  }
});

// Display categorized migrations
console.log('📊 Migration Categories:');
console.log('━'.repeat(60));

Object.entries(categories).forEach(([category, files]) => {
  console.log(`\n${category.toUpperCase()} (${files.length} files):`);
  files.forEach(f => console.log(`  - ${f}`));
});

// Create consolidated migration structure
const consolidatedStructure = `
# Consolidated Migration Plan

## 1. Core Schema (Combine into 001_core_schema.sql)
- Basic tables: users, projects, chapters, characters, series
- Essential relationships and constraints
- Core indexes

## 2. Feature Tables (Combine into 002_features.sql)
- Voice profiles
- Universe/world building
- Story bible
- Timeline events
- Locations
- Reference materials

## 3. Collaboration System (Combine into 003_collaboration.sql)
- Project collaborators
- Invitations
- Real-time collaboration tables
- Permissions

## 4. Analytics & Tracking (Combine into 004_analytics.sql)
- Writing sessions
- Word count history
- Usage tracking
- AI usage logs
- Quality metrics

## 5. Achievement System (Combine into 005_achievements.sql)
- Achievement definitions
- User achievements
- Progress tracking
- Notification triggers

## 6. Performance Optimizations (Combine into 006_performance.sql)
- All indexes
- Materialized views
- Partitioning (if needed)
- Query optimizations

## Benefits of Consolidation:
1. Faster initial setup
2. Clearer dependency order
3. Easier to understand schema
4. Reduced migration conflicts
5. Simpler rollback process
`;

fs.writeFileSync(
  path.join(process.cwd(), 'docs/database/migration-consolidation-plan.md'),
  consolidatedStructure
);

// Analyze migration dependencies
console.log('\n🔍 Analyzing Migration Dependencies...\n');

const dependencies = {
  tables: new Set(),
  indexes: new Set(),
  functions: new Set(),
  triggers: new Set()
};

// Simple pattern matching to find dependencies
files.forEach(file => {
  const content = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
  
  // Find CREATE TABLE statements
  const tableMatches = content.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/gi) || [];
  tableMatches.forEach(match => {
    const table = match.replace(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?/i, '');
    dependencies.tables.add(table);
  });
  
  // Find CREATE INDEX statements
  const indexMatches = content.match(/CREATE\s+(?:UNIQUE\s+)?INDEX\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/gi) || [];
  indexMatches.forEach(match => {
    const index = match.replace(/CREATE\s+(?:UNIQUE\s+)?INDEX\s+(?:IF\s+NOT\s+EXISTS\s+)?/i, '');
    dependencies.indexes.add(index);
  });
  
  // Find CREATE FUNCTION statements
  const functionMatches = content.match(/CREATE\s+(?:OR\s+REPLACE\s+)?FUNCTION\s+(\w+)/gi) || [];
  functionMatches.forEach(match => {
    const func = match.replace(/CREATE\s+(?:OR\s+REPLACE\s+)?FUNCTION\s+/i, '');
    dependencies.functions.add(func);
  });
});

console.log(`Found ${dependencies.tables.size} tables`);
console.log(`Found ${dependencies.indexes.size} indexes`);
console.log(`Found ${dependencies.functions.size} functions`);

// Create migration order recommendation
const migrationOrder = {
  '001_drop_existing': [
    '-- Drop all existing objects in reverse dependency order',
    '-- This ensures clean slate for consolidated migrations'
  ],
  '002_extensions': [
    'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
    'CREATE EXTENSION IF NOT EXISTS "pg_trgm";',
    'CREATE EXTENSION IF NOT EXISTS "vector";'
  ],
  '003_core_tables': [
    'users', 'projects', 'chapters', 'characters', 'series'
  ],
  '004_feature_tables': [
    'voice_profiles', 'universes', 'story_bible_entries', 
    'timeline_events', 'locations', 'reference_materials'
  ],
  '005_collaboration_tables': [
    'project_collaborators', 'project_invitations', 'collaboration_sessions'
  ],
  '006_analytics_tables': [
    'writing_sessions', 'word_count_history', 'usage_tracking',
    'ai_usage_logs', 'quality_metrics'
  ],
  '007_achievement_tables': [
    'achievements', 'user_achievements', 'achievement_progress'
  ],
  '008_indexes': [
    '-- All performance indexes'
  ],
  '009_functions': [
    '-- All stored procedures and functions'
  ],
  '010_policies': [
    '-- Row Level Security policies'
  ]
};

// Ensure directories exist
const docsDbDir = path.join(process.cwd(), 'docs/database');
const consolidatedDir = path.join(process.cwd(), 'supabase/migrations/consolidated');

if (!fs.existsSync(docsDbDir)) {
  fs.mkdirSync(docsDbDir, { recursive: true });
}
if (!fs.existsSync(consolidatedDir)) {
  fs.mkdirSync(consolidatedDir, { recursive: true });
}

// Save migration order
fs.writeFileSync(
  path.join(docsDbDir, 'migration-order.json'),
  JSON.stringify(migrationOrder, null, 2)
);

// Create a sample consolidated migration
const sampleConsolidated = `-- BookScribe Consolidated Core Schema Migration
-- Version: 1.0.0
-- Date: ${new Date().toISOString().split('T')[0]}

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Core Tables
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  username TEXT UNIQUE,
  display_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  is_admin BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  genre TEXT,
  writing_style TEXT,
  pov TEXT,
  tense TEXT,
  status TEXT DEFAULT 'draft',
  target_word_count INTEGER,
  target_chapters INTEGER,
  series_id UUID,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS chapters (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT,
  order_index INTEGER NOT NULL,
  word_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'draft',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(project_id, order_index)
);

CREATE TABLE IF NOT EXISTS characters (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  role TEXT,
  bio TEXT,
  traits JSONB,
  relationships JSONB,
  arc TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS series (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  genre TEXT,
  planned_books INTEGER,
  universe_rules JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key for series
ALTER TABLE projects 
  ADD CONSTRAINT fk_projects_series 
  FOREIGN KEY (series_id) 
  REFERENCES series(id) 
  ON DELETE SET NULL;

-- Core Indexes
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_chapters_project_id ON chapters(project_id);
CREATE INDEX idx_characters_project_id ON characters(project_id);
CREATE INDEX idx_series_user_id ON series(user_id);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE series ENABLE ROW LEVEL SECURITY;

-- Basic RLS Policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own projects" ON projects
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own chapters" ON chapters
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own characters" ON characters
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own series" ON series
  FOR ALL USING (auth.uid() = user_id);
`;

fs.writeFileSync(
  path.join(consolidatedDir, '001_core_schema.sql'),
  sampleConsolidated
);

// Create consolidation summary
console.log('\n📊 Consolidation Summary:');
console.log('━'.repeat(60));
console.log(`Total migrations: ${files.length}`);
console.log(`Can be consolidated into: 6-10 files`);
console.log(`\nRecommended approach:`);
console.log('1. Create new consolidated migrations in supabase/migrations/consolidated/');
console.log('2. Test on a fresh database');
console.log('3. Create rollback scripts');
console.log('4. Archive old migrations');
console.log('5. Update deployment scripts');

console.log('\n✅ Migration analysis complete!');
console.log('📝 See docs/database/migration-consolidation-plan.md for details');