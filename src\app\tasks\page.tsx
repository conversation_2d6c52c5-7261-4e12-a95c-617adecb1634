'use client'

import { useState } from 'react'
import { TaskQueue } from '@/components/tasks/task-queue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/auth-context'
import { Zap, Clock, CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function TasksPage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [processingTest, setProcessingTest] = useState(false)

  const handleTestTask = async () => {
    setProcessingTest(true)
    try {
      const response = await fetch('/api/test-task', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'test' })
      })
      
      if (!response.ok) throw new Error('Failed to create test task')
      
      const { taskId } = await response.json()
      toast({
        title: 'Test Task Created',
        description: `Task ${taskId} has been queued for processing`
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create test task',
        variant: 'destructive'
      })
    } finally {
      setProcessingTest(false)
    }
  }

  return (
    <div className="container max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-literary-display text-foreground mb-2">
          Background Tasks
        </h1>
        <p className="text-lg text-muted-foreground">
          Monitor and manage background processing tasks
        </p>
      </div>

      {/* Info Alert */}
      <Alert className="mb-6">
        <Info className="h-4 w-4" />
        <AlertTitle>About Background Tasks</AlertTitle>
        <AlertDescription>
          BookScribe processes resource-intensive operations in the background to keep your writing experience smooth. 
          Tasks include generating embeddings, exporting documents, analyzing content, and optimizing memory.
        </AlertDescription>
      </Alert>

      {/* Task Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-semibold">0</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Zap className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Processing</p>
                <p className="text-2xl font-semibold">0</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-semibold">0</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <XCircle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-muted-foreground">Failed</p>
                <p className="text-2xl font-semibold">0</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task Tabs */}
      <Tabs defaultValue="my-tasks" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="my-tasks">My Tasks</TabsTrigger>
          <TabsTrigger value="all-tasks">All Tasks</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-tasks">
          <TaskQueue userId={user?.id} />
        </TabsContent>
        
        <TabsContent value="all-tasks">
          <TaskQueue showAllTasks />
        </TabsContent>
      </Tabs>

      {/* Test Section */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Task Testing</CardTitle>
          <CardDescription>
            Create a test task to see how the background processing works
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={handleTestTask} 
            disabled={processingTest}
            variant="outline"
          >
            <Zap className="h-4 w-4 mr-2" />
            {processingTest ? 'Creating...' : 'Create Test Task'}
          </Button>
        </CardContent>
      </Card>

      {/* Task Types Reference */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Task Types</CardTitle>
          <CardDescription>
            Different types of background tasks that BookScribe processes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Content Processing</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Generate Embeddings - Create searchable content vectors</li>
                <li>• Extract Text - Process uploaded documents</li>
                <li>• Analyze Content - AI-powered content analysis</li>
                <li>• Generate Chapters - Create new story content</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Export & Backup</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Export DOCX - Microsoft Word format</li>
                <li>• Export PDF - Portable Document Format</li>
                <li>• Export EPUB - eBook format</li>
                <li>• Backup Project - Complete project backup</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Maintenance</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Compress Memory - Optimize AI context</li>
                <li>• Cleanup Old Data - Remove unused data</li>
                <li>• Generate Analytics - Create usage reports</li>
                <li>• Sync Changes - Collaboration sync</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">User Data</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Export User Data - GDPR compliance export</li>
                <li>• Delete User Data - Account deletion</li>
                <li>• Notify Collaborators - Team notifications</li>
                <li>• Restore Project - Restore from backup</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}