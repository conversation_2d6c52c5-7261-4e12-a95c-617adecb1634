import { NextResponse } from 'next/server'
import { APIError } from '@/lib/api/error-handler'
import { createTypedServerClient } from '@/lib/supabase'
import { format, startOfDay, endOfDay } from 'date-fns'
import { z } from 'zod'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { logger } from '@/lib/services/logger'
import { baseSchemas } from '@/lib/validation/common-schemas'

interface AIUsageData {
  agent: string
  model: string
  tokens: number
  cost: number
  duration: number
  success: boolean
}

// Query validation schema
const querySchema = z.object({
  projectId: baseSchemas.uuid.optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
  groupBy: z.enum(['agent', 'model', 'day']).optional().default('agent')
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before or equal to end date'
})

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId'),
    startDate: searchParams.get('startDate'),
    endDate: searchParams.get('endDate'),
    groupBy: searchParams.get('groupBy')
  };

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    querySchema: querySchema,
    rateLimitKey: 'analytics-ai-usage',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // If projectId provided, verify access
      if (queryParams.projectId) {
        const supabase = await createTypedServerClient();
        const { data: project } = await supabase
          .from('projects')
          .select('id')
          .eq('id', queryParams.projectId)
          .eq('user_id', user.id)
          .single();
        
        if (!project) {
          const { data: collaborator } = await supabase
            .from('project_collaborators')
            .select('role')
            .eq('project_id', queryParams.projectId)
            .eq('user_id', user.id)
            .eq('status', 'active')
            .single();
          
          if (!collaborator) {
            return { valid: false, error: 'Access denied to this project' };
          }
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, startDate, endDate, groupBy } = context.query;

  try {
    const supabase = await createTypedServerClient();

    // Build base query
    let query = supabase
      .from('ai_usage_logs')
      .select('*')
      .eq('user_id', user.id)

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    if (startDate) {
      query = query.gte('created_at', startOfDay(new Date(startDate)).toISOString())
    }

    if (endDate) {
      query = query.lte('created_at', endOfDay(new Date(endDate)).toISOString())
    }

    const { data: usageLogs, error } = await query
      .order('created_at', { ascending: false })
      .limit(1000); // Limit to prevent excessive data transfer

    if (error) {
      logger.error('Failed to fetch AI usage logs:', error, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to fetch AI usage analytics');
    }

    // Aggregate data based on groupBy parameter
    let aggregatedData: any = {}

    switch (groupBy) {
      case 'agent':
        aggregatedData = usageLogs.reduce((acc: any, log: any) => {
          const agent = log.agent_name || 'Unknown'
          if (!acc[agent]) {
            acc[agent] = {
              agent,
              totalCalls: 0,
              totalTokens: 0,
              totalCost: 0,
              totalDuration: 0,
              successRate: 0,
              successCount: 0
            }
          }
          acc[agent].totalCalls++
          acc[agent].totalTokens += log.tokens_used || 0
          acc[agent].totalCost += log.estimated_cost || 0
          acc[agent].totalDuration += log.duration || 0
          if (log.success) acc[agent].successCount++
          return acc
        }, {})

        // Calculate success rates
        Object.values(aggregatedData).forEach((agent: any) => {
          agent.successRate = agent.totalCalls > 0 
            ? Math.round((agent.successCount / agent.totalCalls) * 100) 
            : 0
          agent.avgTokensPerCall = agent.totalCalls > 0
            ? Math.round(agent.totalTokens / agent.totalCalls)
            : 0
          agent.avgDuration = agent.totalCalls > 0
            ? Math.round(agent.totalDuration / agent.totalCalls)
            : 0
        })
        break

      case 'model':
        aggregatedData = usageLogs.reduce((acc: any, log: any) => {
          const model = log.model || 'Unknown'
          if (!acc[model]) {
            acc[model] = {
              model,
              totalCalls: 0,
              totalTokens: 0,
              totalCost: 0,
              avgResponseTime: 0,
              totalDuration: 0
            }
          }
          acc[model].totalCalls++
          acc[model].totalTokens += log.tokens_used || 0
          acc[model].totalCost += log.estimated_cost || 0
          acc[model].totalDuration += log.duration || 0
          return acc
        }, {})

        // Calculate averages
        Object.values(aggregatedData).forEach((model: any) => {
          model.avgResponseTime = model.totalCalls > 0
            ? Math.round(model.totalDuration / model.totalCalls)
            : 0
          model.avgTokensPerCall = model.totalCalls > 0
            ? Math.round(model.totalTokens / model.totalCalls)
            : 0
        })
        break

      case 'day':
        aggregatedData = usageLogs.reduce((acc: any, log: any) => {
          const date = format(new Date(log.created_at), 'yyyy-MM-dd')
          if (!acc[date]) {
            acc[date] = {
              date,
              totalCalls: 0,
              totalTokens: 0,
              totalCost: 0,
              agents: new Set()
            }
          }
          acc[date].totalCalls++
          acc[date].totalTokens += log.tokens_used || 0
          acc[date].totalCost += log.estimated_cost || 0
          acc[date].agents.add(log.agent_name)
          return acc
        }, {})

        // Convert sets to counts
        Object.values(aggregatedData).forEach((day: any) => {
          day.uniqueAgents = day.agents.size
          delete day.agents
        })
        break
    }

    // Calculate totals
    const totals = usageLogs.reduce((acc: any, log: any) => {
      acc.totalCalls++
      acc.totalTokens += log.tokens_used || 0
      acc.totalCost += log.estimated_cost || 0
      acc.totalDuration += log.duration || 0
      if (log.success) acc.successCount++
      return acc
    }, {
      totalCalls: 0,
      totalTokens: 0,
      totalCost: 0,
      totalDuration: 0,
      successCount: 0
    })

    totals.successRate = totals.totalCalls > 0
      ? Math.round((totals.successCount / totals.totalCalls) * 100)
      : 0
    totals.avgTokensPerCall = totals.totalCalls > 0
      ? Math.round(totals.totalTokens / totals.totalCalls)
      : 0
    totals.avgDuration = totals.totalCalls > 0
      ? Math.round(totals.totalDuration / totals.totalCalls)
      : 0

    logger.info('AI usage analytics fetched', {
      userId: user.id,
      projectId,
      groupBy,
      dateRange: { startDate, endDate },
      recordCount: usageLogs.length,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      aggregated: Object.values(aggregatedData),
      totals,
      raw: usageLogs.slice(0, 100), // Return latest 100 for detail view
      period: {
        start: startDate || format(new Date(usageLogs[usageLogs.length - 1]?.created_at || new Date()), 'yyyy-MM-dd'),
        end: endDate || format(new Date(), 'yyyy-MM-dd')
      }
    });

  } catch (error) {
    logger.error('Error in AI usage analytics:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to generate AI usage analytics');
  }
});

// Body validation schema for POST
const postBodySchema = z.object({
  projectId: baseSchemas.uuid,
  agentName: z.string().min(1).max(100),
  model: z.string().min(1).max(100),
  tokensUsed: z.number().int().min(1).max(1000000),
  estimatedCost: z.number().min(0).max(1000),
  duration: z.number().min(0).max(300000), // Max 5 minutes
  success: z.boolean().default(true),
  context: z.record(z.unknown()).optional(),
  error: z.string().max(1000).optional()
})

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: postBodySchema,
    rateLimitKey: 'analytics-ai-usage-log',
    rateLimitCost: 1,
    maxBodySize: 10 * 1024, // 10KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      const body = await req.json();
      const { projectId } = body;
      
      // Verify project access
      const supabase = await createTypedServerClient();
      const { data: project } = await supabase
        .from('projects')
        .select('id')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single();
      
      if (!project) {
        const { data: collaborator } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();
        
        if (!collaborator) {
          return { valid: false, error: 'Access denied to this project' };
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { 
    projectId, 
    agentName, 
    model, 
    tokensUsed, 
    estimatedCost, 
    duration,
    success,
    context: logContext,
    error
  } = context.body;

  try {
    const supabase = await createTypedServerClient();

    // Log AI usage
    const { data: log, error: insertError } = await supabase
      .from('ai_usage_logs')
      .insert({
        user_id: user.id,
        project_id: projectId,
        agent_name: agentName,
        model,
        tokens_used: tokensUsed,
        estimated_cost: estimatedCost,
        duration,
        success,
        context: logContext,
        error_message: error,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      logger.error('Failed to log AI usage:', insertError, {
        userId: user.id,
        projectId,
        agentName,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to log AI usage');
    }

    // Update daily aggregates
    const today = format(new Date(), 'yyyy-MM-dd');
    const { data: existing } = await supabase
      .from('ai_usage_daily')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    if (existing) {
      await supabase
        .from('ai_usage_daily')
        .update({
          total_calls: existing.total_calls + 1,
          total_tokens: existing.total_tokens + tokensUsed,
          total_cost: existing.total_cost + estimatedCost,
          updated_at: new Date().toISOString()
        })
        .eq('id', existing.id)
    } else {
      await supabase
        .from('ai_usage_daily')
        .insert({
          user_id: user.id,
          date: today,
          total_calls: 1,
          total_tokens: tokensUsed,
          total_cost: estimatedCost
        })
    }

    logger.info('AI usage logged successfully', {
      userId: user.id,
      projectId,
      agentName,
      model,
      tokensUsed,
      cost: estimatedCost,
      success,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      log,
      dailyUsage: {
        date: today,
        totalCalls: existing ? existing.total_calls + 1 : 1,
        totalTokens: existing ? existing.total_tokens + tokensUsed : tokensUsed,
        totalCost: existing ? existing.total_cost + estimatedCost : estimatedCost
      }
    });

  } catch (error) {
    logger.error('Error logging AI usage:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to process AI usage log');
  }
});