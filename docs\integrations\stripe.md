# Stripe Integration Documentation

## Overview

BookScribe uses Stripe for subscription management, payment processing, and billing. This integration handles multiple subscription tiers, usage-based billing for AI tokens, and automated customer management.

## Integration Architecture

### System Components

```mermaid
graph TB
    subgraph "BookScribe Backend"
        WebhookHandler[Webhook Handler]
        SubManager[Subscription Manager]
        BillingService[Billing Service]
        UsageTracker[Usage Tracker]
    end
    
    subgraph "Stripe Services"
        Customers[Stripe Customers]
        Subscriptions[Stripe Subscriptions]
        Products[Stripe Products]
        Prices[Stripe Prices]
        Invoices[Stripe Invoices]
        Webhooks[Stripe Webhooks]
    end
    
    subgraph "Database"
        UserTable[users]
        SubTable[subscriptions]
        UsageTable[usage_records]
        InvoiceTable[invoices]
    end
    
    WebhookHandler --> Webhooks
    SubManager --> Subscriptions
    SubManager --> Customers
    BillingService --> Invoices
    UsageTracker --> UsageTable
    
    Subscriptions --> SubTable
    Invoices --> InvoiceTable
```

## Configuration

### Environment Variables
```bash
# Required
STRIPE_SECRET_KEY=sk_live_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...

# Webhook signing secret
STRIPE_WEBHOOK_SECRET=whsec_...

# Optional
STRIPE_API_VERSION=2023-10-16
```

### Stripe Products Configuration
```typescript
// Subscription Products
export const STRIPE_PRODUCTS = {
  free: {
    productId: 'prod_free_tier',
    priceId: 'price_free_monthly',
    features: {
      monthlyTokens: 50000,
      projects: 1,
      collaborators: 0,
      aiModel: 'gpt-o4-mini'
    }
  },
  starter: {
    productId: 'prod_starter',
    priceId: 'price_starter_monthly',
    price: 19.99,
    features: {
      monthlyTokens: 2000000,
      projects: 3,
      collaborators: 2,
      aiModel: 'gpt-o4-mini'
    }
  },
  professional: {
    productId: 'prod_professional',
    priceId: 'price_professional_monthly',
    price: 49.99,
    features: {
      monthlyTokens: 10000000,
      projects: 10,
      collaborators: 5,
      aiModel: 'gpt-4.1'
    }
  },
  premium: {
    productId: 'prod_premium',
    priceId: 'price_premium_monthly',
    price: 99.99,
    features: {
      monthlyTokens: 50000000,
      projects: 'unlimited',
      collaborators: 10,
      aiModel: 'gpt-4.1'
    }
  }
};
```

## Subscription Management

### Creating Subscriptions
```typescript
class StripeSubscriptionManager {
  async createSubscription(
    userId: string,
    priceId: string,
    paymentMethodId?: string
  ): Promise<Subscription> {
    // Get or create Stripe customer
    const customer = await this.getOrCreateCustomer(userId);
    
    // Attach payment method if provided
    if (paymentMethodId) {
      await this.attachPaymentMethod(customer.id, paymentMethodId);
    }
    
    // Create subscription
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        userId,
        environment: process.env.NODE_ENV
      }
    });
    
    // Update database
    await this.updateSubscriptionRecord(userId, subscription);
    
    return subscription;
  }
}
```

### Subscription Lifecycle
```typescript
interface SubscriptionLifecycle {
  // Creation
  onCreate: (subscription: Stripe.Subscription) => Promise<void>;
  
  // Updates
  onUpdate: (subscription: Stripe.Subscription) => Promise<void>;
  
  // Renewal
  onRenewal: (invoice: Stripe.Invoice) => Promise<void>;
  
  // Cancellation
  onCancel: (subscription: Stripe.Subscription) => Promise<void>;
  
  // Reactivation
  onReactivate: (subscription: Stripe.Subscription) => Promise<void>;
}
```

## Webhook Handling

### Webhook Endpoint
```typescript
// app/api/webhooks/stripe/route.ts
export async function POST(req: Request) {
  const body = await req.text();
  const signature = req.headers.get('stripe-signature')!;
  
  let event: Stripe.Event;
  
  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    return new Response('Webhook signature verification failed', { 
      status: 400 
    });
  }
  
  // Handle events
  try {
    await handleStripeEvent(event);
    return new Response('Webhook processed', { status: 200 });
  } catch (error) {
    console.error('Webhook processing failed:', error);
    return new Response('Webhook processing failed', { status: 500 });
  }
}
```

### Event Handlers
```typescript
async function handleStripeEvent(event: Stripe.Event) {
  switch (event.type) {
    // Subscription events
    case 'customer.subscription.created':
      await handleSubscriptionCreated(event.data.object);
      break;
      
    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object);
      break;
      
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object);
      break;
    
    // Payment events
    case 'invoice.payment_succeeded':
      await handlePaymentSucceeded(event.data.object);
      break;
      
    case 'invoice.payment_failed':
      await handlePaymentFailed(event.data.object);
      break;
    
    // Usage events (for metered billing)
    case 'invoice.created':
      await attachUsageRecords(event.data.object);
      break;
      
    default:
      console.log(`Unhandled event type: ${event.type}`);
  }
}
```

## Usage-Based Billing

### Token Usage Tracking
```typescript
class UsageMetering {
  async recordUsage(
    userId: string,
    usage: {
      tokens: number;
      model: string;
      feature: string;
    }
  ): Promise<void> {
    // Record in database
    await this.db.usage_records.create({
      user_id: userId,
      tokens: usage.tokens,
      model: usage.model,
      feature: usage.feature,
      timestamp: new Date()
    });
    
    // Report to Stripe for overage billing
    if (await this.isOverageApplicable(userId)) {
      await this.reportToStripe(userId, usage);
    }
  }
  
  private async reportToStripe(
    userId: string,
    usage: UsageRecord
  ): Promise<void> {
    const subscription = await this.getActiveSubscription(userId);
    
    if (subscription?.meteredItems) {
      await stripe.subscriptionItems.createUsageRecord(
        subscription.meteredItems[0].id,
        {
          quantity: Math.ceil(usage.tokens / 1000), // Per 1k tokens
          timestamp: Math.floor(Date.now() / 1000),
          action: 'increment'
        }
      );
    }
  }
}
```

### Overage Billing Configuration
```typescript
const overagePricing = {
  starter: {
    includedTokens: 2000000,
    overagePrice: 0.01, // per 1k tokens
    meteredPriceId: 'price_starter_overage'
  },
  professional: {
    includedTokens: 10000000,
    overagePrice: 0.008,
    meteredPriceId: 'price_professional_overage'
  },
  premium: {
    includedTokens: 50000000,
    overagePrice: 0.005,
    meteredPriceId: 'price_premium_overage'
  }
};
```

## Customer Portal

### Portal Configuration
```typescript
class StripePortal {
  async createPortalSession(userId: string): Promise<string> {
    const user = await this.getUser(userId);
    
    if (!user.stripe_customer_id) {
      throw new Error('No Stripe customer found');
    }
    
    const session = await stripe.billingPortal.sessions.create({
      customer: user.stripe_customer_id,
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/settings/billing`,
      configuration: process.env.STRIPE_PORTAL_CONFIG_ID
    });
    
    return session.url;
  }
}
```

### Portal Features
- View invoices and receipts
- Update payment methods
- Change subscription plans
- Cancel subscription
- Download tax documents

## Payment Methods

### Payment Method Management
```typescript
interface PaymentMethodManager {
  // Add payment method
  async attachPaymentMethod(
    customerId: string,
    paymentMethodId: string
  ): Promise<Stripe.PaymentMethod>;
  
  // Set default payment method
  async setDefaultPaymentMethod(
    customerId: string,
    paymentMethodId: string
  ): Promise<void>;
  
  // List payment methods
  async listPaymentMethods(
    customerId: string
  ): Promise<Stripe.PaymentMethod[]>;
  
  // Remove payment method
  async detachPaymentMethod(
    paymentMethodId: string
  ): Promise<void>;
}
```

### Payment UI Components
```typescript
// Stripe Elements integration
import { Elements, PaymentElement } from '@stripe/react-stripe-js';

function PaymentForm({ clientSecret }: { clientSecret: string }) {
  return (
    <Elements stripe={stripePromise} options={{ clientSecret }}>
      <PaymentElement 
        options={{
          layout: 'tabs',
          wallets: {
            applePay: 'auto',
            googlePay: 'auto'
          }
        }}
      />
    </Elements>
  );
}
```

## Database Schema

### Subscription Table
```sql
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  stripe_subscription_id TEXT UNIQUE,
  stripe_customer_id TEXT,
  status TEXT NOT NULL,
  tier TEXT NOT NULL,
  current_period_start TIMESTAMP,
  current_period_end TIMESTAMP,
  cancel_at_period_end BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
```

### Usage Records Table
```sql
CREATE TABLE usage_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  subscription_id UUID REFERENCES subscriptions(id),
  tokens INTEGER NOT NULL,
  model TEXT NOT NULL,
  feature TEXT NOT NULL,
  cost DECIMAL(10, 6),
  reported_to_stripe BOOLEAN DEFAULT false,
  timestamp TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_usage_user_period ON usage_records(user_id, timestamp);
```

## Error Handling

### Common Stripe Errors
```typescript
class StripeErrorHandler {
  handle(error: Stripe.StripeError): ErrorResponse {
    switch (error.type) {
      case 'StripeCardError':
        return this.handleCardError(error);
        
      case 'StripeInvalidRequestError':
        return this.handleInvalidRequest(error);
        
      case 'StripeAPIError':
        return this.handleAPIError(error);
        
      case 'StripeConnectionError':
        return this.handleConnectionError(error);
        
      case 'StripeAuthenticationError':
        return this.handleAuthError(error);
        
      default:
        return this.handleGenericError(error);
    }
  }
  
  private handleCardError(error: Stripe.StripeCardError): ErrorResponse {
    const userMessage = {
      insufficient_funds: 'Your card has insufficient funds.',
      card_declined: 'Your card was declined.',
      expired_card: 'Your card has expired.',
      processing_error: 'An error occurred processing your card.'
    }[error.code!] || 'Your card could not be charged.';
    
    return { userMessage, code: error.code, retry: true };
  }
}
```

## Testing

### Test Mode Configuration
```typescript
const stripeTestConfig = {
  // Test API keys
  secretKey: process.env.STRIPE_TEST_SECRET_KEY,
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_TEST_PUBLISHABLE_KEY,
  
  // Test webhook endpoint
  webhookSecret: process.env.STRIPE_TEST_WEBHOOK_SECRET,
  
  // Test cards
  testCards: {
    success: '****************',
    declined: '****************',
    insufficientFunds: '****************',
    expired: '****************'
  }
};
```

### Integration Tests
```typescript
describe('Stripe Integration', () => {
  it('should create subscription', async () => {
    const subscription = await createTestSubscription({
      userId: 'test-user',
      priceId: 'price_starter_monthly',
      paymentMethod: 'pm_card_visa'
    });
    
    expect(subscription.status).toBe('active');
  });
  
  it('should handle webhook events', async () => {
    const event = createMockWebhookEvent('customer.subscription.updated');
    const response = await handleWebhook(event);
    
    expect(response.status).toBe(200);
  });
});
```

## Monitoring & Analytics

### Subscription Metrics
```typescript
interface SubscriptionMetrics {
  // Revenue metrics
  mrr: number; // Monthly Recurring Revenue
  arr: number; // Annual Recurring Revenue
  arpu: number; // Average Revenue Per User
  
  // Growth metrics
  newSubscriptions: number;
  churnedSubscriptions: number;
  churnRate: number;
  retentionRate: number;
  
  // Usage metrics
  totalTokensUsed: number;
  averageTokensPerUser: number;
  overageRevenue: number;
}
```

### Monitoring Setup
```typescript
const stripeMonitoring = {
  // Alert thresholds
  alerts: {
    failedPayments: 5,
    highChurn: 10, // percentage
    webhookFailures: 3
  },
  
  // Metrics collection
  metrics: {
    collectInterval: 300000, // 5 minutes
    retentionPeriod: 90 // days
  },
  
  // Dashboard configuration
  dashboard: {
    revenueCharts: true,
    subscriptionFlow: true,
    paymentSuccess: true
  }
};
```

## Best Practices

### Security
1. Always validate webhook signatures
2. Use idempotency keys for critical operations
3. Never log sensitive payment data
4. Implement proper error handling
5. Use Stripe's test mode for development

### Performance
1. Cache subscription data appropriately
2. Use webhooks for state synchronization
3. Batch usage reporting when possible
4. Implement retry logic with backoff
5. Monitor API rate limits

### User Experience
1. Provide clear pricing information
2. Show usage metrics in real-time
3. Send payment failure notifications
4. Offer grace periods for failures
5. Make cancellation easy

## Troubleshooting

### Common Issues

1. **Webhook Signature Failures**
   - Verify webhook secret is correct
   - Check for middleware modifying body
   - Ensure raw body is used

2. **Subscription Sync Issues**
   - Implement webhook retry handling
   - Add manual sync capability
   - Log all state changes

3. **Payment Failures**
   - Implement retry logic
   - Send customer notifications
   - Provide payment update flow

4. **Usage Reporting Delays**
   - Batch usage reports
   - Implement queuing system
   - Monitor reporting lag

## Future Enhancements

### Planned Features
1. **Revenue Recognition**: Automated accounting
2. **Advanced Analytics**: Cohort analysis
3. **Flexible Billing**: Custom pricing models
4. **Global Payments**: Multi-currency support
5. **Enterprise Features**: Volume discounts, invoicing