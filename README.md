# BookScribe

BookScribe is an AI-powered novel writing IDE that coordinates multiple specialized agents to help authors plan, draft, and refine long-form fiction while maintaining narrative consistency.

## Quick Start

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Create your env file**
   ```bash
   cp .env.example.clean .env.local
   ```
   Fill in at minimum:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   OPENAI_API_KEY=sk-your_openai_key
   ```
   More variables and details are available in the [environment setup guide](docs/configuration/environment-setup.md).

## Running Modes

### Demo Mode
Uses mock services for a quick evaluation.
```bash
npm run dev:demo       # start development server
npm run start:demo     # start built app
```
Alternatively set `NEXT_PUBLIC_DEMO_MODE=true` in `.env.local`.

### Production Mode
Runs against real services.
```bash
npm run dev           # development server
npm run build
npm run start         # production server
```
Ensure all required environment variables are set with real credentials.

## Architecture

BookScribe is built with Next.js 15 on the frontend, Supabase for database/auth/storage, and a multi-agent AI system powered by OpenAI. The system is organized into a Next.js app layer, service layer, and AI agent orchestrator communicating with external services. See the [architecture overview](docs/architecture/overview.md) for diagrams and details.

## Documentation

- [Comprehensive Implementation Guide](COMPREHENSIVE_IMPLEMENTATION_GUIDE.md)
- [Implementation Audit Report](IMPLEMENTATION_AUDIT_REPORT_FINAL.md)
- [Security Audit Report](security-audit-report.md)
- [Full documentation index](docs/README.md)

---

BookScribe is proprietary software. All rights reserved.
