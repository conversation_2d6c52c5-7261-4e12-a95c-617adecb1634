"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Brain, 
  Sparkles, 
  Users, 
  BookOpen, 
  Target,
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  Clock,
  Zap,
  FileText,
  MessageSquare,
  TrendingUp,
  AlertCircle,
  Settings,
  Activity,
  Cpu,
  Database
} from "lucide-react";

const agents = [
  {
    id: 'story-architect',
    name: 'Story Architect',
    role: 'Plot & Structure',
    status: 'active',
    progress: 85,
    currentTask: 'Analyzing chapter pacing and story arc progression',
    avatar: Brain,
    color: 'blue',
    capabilities: ['Plot Development', 'Story Structure', 'Pacing Analysis', 'Arc Management'],
    lastOutput: 'Chapter 12 successfully advances the main plot while maintaining tension. Recommend expanding the crystal vision sequence for better emotional impact.',
    confidence: 0.92,
    processingTime: '2.3s'
  },
  {
    id: 'character-psychologist',
    name: 'Character Psychologist',
    role: 'Character Development',
    status: 'active',
    progress: 78,
    currentTask: 'Evaluating character voice consistency and development arcs',
    avatar: Users,
    color: 'green',
    capabilities: ['Voice Analysis', 'Character Arcs', 'Dialogue Consistency', 'Emotional Depth'],
    lastOutput: 'Aria\'s character growth is well-paced. Marcus shows appropriate mentor restraint. Zara\'s pragmatic voice contrasts nicely with Aria\'s idealism.',
    confidence: 0.89,
    processingTime: '1.8s'
  },
  {
    id: 'world-builder',
    name: 'World Builder',
    role: 'Setting & Lore',
    status: 'processing',
    progress: 65,
    currentTask: 'Cross-referencing crystal lore with established world rules',
    avatar: BookOpen,
    color: 'purple',
    capabilities: ['World Consistency', 'Lore Management', 'Setting Details', 'Magic Systems'],
    lastOutput: 'Crystal chamber description aligns with Chapter 3 references. Suggest adding connection to the Aethermoor creation myth for deeper resonance.',
    confidence: 0.87,
    processingTime: '3.1s'
  },
  {
    id: 'dialogue-specialist',
    name: 'Dialogue Specialist',
    role: 'Conversation & Voice',
    status: 'active',
    progress: 91,
    currentTask: 'Optimizing character speech patterns and dialogue flow',
    avatar: MessageSquare,
    color: 'orange',
    capabilities: ['Speech Patterns', 'Dialogue Flow', 'Character Voice', 'Conversation Dynamics'],
    lastOutput: 'Dialogue feels natural and character-appropriate. Zara\'s final quip maintains her established personality while adding urgency.',
    confidence: 0.94,
    processingTime: '1.2s'
  },
  {
    id: 'prose-enhancer',
    name: 'Prose Enhancer',
    role: 'Style & Flow',
    status: 'queued',
    progress: 45,
    currentTask: 'Waiting for content completion to analyze prose style',
    avatar: Sparkles,
    color: 'pink',
    capabilities: ['Style Analysis', 'Prose Flow', 'Sentence Structure', 'Literary Devices'],
    lastOutput: 'Previous analysis: Sentence variety is good. Consider varying paragraph lengths for better rhythm.',
    confidence: 0.83,
    processingTime: '2.7s'
  },
  {
    id: 'continuity-guardian',
    name: 'Continuity Guardian',
    role: 'Consistency & Logic',
    status: 'active',
    progress: 88,
    currentTask: 'Verifying plot consistency across all chapters',
    avatar: Target,
    color: 'red',
    capabilities: ['Plot Consistency', 'Character Tracking', 'Timeline Management', 'Logic Verification'],
    lastOutput: 'No continuity errors detected. Crystal powers align with established magic system. Character locations and abilities consistent.',
    confidence: 0.96,
    processingTime: '4.2s'
  }
];

const systemMetrics = {
  totalProcessingPower: 87,
  activeAgents: 5,
  queuedTasks: 12,
  completedAnalyses: 247,
  averageResponseTime: '2.1s',
  systemHealth: 'Optimal'
};

const recentOutputs = [
  {
    agent: 'Story Architect',
    timestamp: '2 minutes ago',
    type: 'analysis',
    content: 'Chapter tension curve analysis complete. Peak tension at 78% through chapter aligns perfectly with story structure principles.',
    priority: 'high'
  },
  {
    agent: 'Character Psychologist',
    timestamp: '3 minutes ago',
    type: 'suggestion',
    content: 'Aria\'s internal conflict could be enhanced with a brief moment of doubt before touching the crystal.',
    priority: 'medium'
  },
  {
    agent: 'Continuity Guardian',
    timestamp: '5 minutes ago',
    type: 'verification',
    content: 'Cross-reference complete: Crystal chamber layout matches architectural descriptions from Chapter 3.',
    priority: 'low'
  },
  {
    agent: 'Dialogue Specialist',
    timestamp: '7 minutes ago',
    type: 'enhancement',
    content: 'Suggested dialogue revision for Marcus increases emotional weight while maintaining character voice.',
    priority: 'medium'
  }
];

export function DemoAgentsEnhanced() {
  const [isRunning, setIsRunning] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [agentStates, setAgentStates] = useState(agents);

  useEffect(() => {
    if (isRunning) {
      const interval = setInterval(() => {
        setAgentStates(prev => prev.map(agent => ({
          ...agent,
          progress: Math.min(100, agent.progress + Math.random() * 5),
          status: agent.progress >= 95 ? 'complete' : agent.status
        })));
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isRunning]);

  const startDemo = () => {
    setIsRunning(true);
    setAgentStates(agents.map(agent => ({ ...agent, progress: Math.max(0, agent.progress - 20) })));
  };

  const stopDemo = () => {
    setIsRunning(false);
  };

  const resetDemo = () => {
    setIsRunning(false);
    setAgentStates(agents);
  };

  return (
    <div className="w-full h-full bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Brain className="w-6 h-6 text-primary" />
              <h2 className="text-2xl font-bold">AI Agent Orchestration System</h2>
              <Badge variant="outline" className="border-primary/50 text-primary">
                Demo Mode
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={startDemo}
              disabled={isRunning}
            >
              <Play className="w-4 h-4 mr-2" />
              Start Analysis
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={stopDemo}
              disabled={!isRunning}
            >
              <Pause className="w-4 h-4 mr-2" />
              Pause
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetDemo}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>

        {/* System Metrics */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-6 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{systemMetrics.activeAgents}</div>
            <div className="text-sm text-muted-foreground">Active Agents</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">{systemMetrics.totalProcessingPower}%</div>
            <div className="text-sm text-muted-foreground">Processing Power</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-500">{systemMetrics.queuedTasks}</div>
            <div className="text-sm text-muted-foreground">Queued Tasks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-500">{systemMetrics.completedAnalyses}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-500">{systemMetrics.averageResponseTime}</div>
            <div className="text-sm text-muted-foreground">Avg Response</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">
              <CheckCircle className="w-6 h-6 mx-auto" />
            </div>
            <div className="text-sm text-muted-foreground">{systemMetrics.systemHealth}</div>
          </div>
        </div>
      </div>

      <div className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="overview">Agent Overview</TabsTrigger>
            <TabsTrigger value="outputs">Live Outputs</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {agentStates.map((agent) => {
                const IconComponent = agent.avatar;
                return (
                  <Card 
                    key={agent.id} 
                    className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                      selectedAgent === agent.id 
                        ? 'border-primary/50 bg-primary/10 shadow-lg' 
                        : 'hover:border-primary/30'
                    } ${isRunning && agent.status === 'active' ? 'animate-pulse' : ''}`}
                    onClick={() => setSelectedAgent(selectedAgent === agent.id ? null : agent.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg bg-${agent.color}-500/20`}>
                            <IconComponent className={`w-5 h-5 text-${agent.color}-500`} />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{agent.name}</CardTitle>
                            <p className="text-sm text-muted-foreground">{agent.role}</p>
                          </div>
                        </div>
                        <Badge 
                          variant={
                            agent.status === 'active' ? 'default' : 
                            agent.status === 'processing' ? 'secondary' : 
                            agent.status === 'complete' ? 'outline' : 'destructive'
                          }
                          className={
                            agent.status === 'active' ? 'bg-green-500/20 text-green-700 border-green-500/50' :
                            agent.status === 'processing' ? 'bg-blue-500/20 text-blue-700 border-blue-500/50' :
                            agent.status === 'complete' ? 'bg-gray-500/20 text-gray-700 border-gray-500/50' :
                            'bg-yellow-500/20 text-yellow-700 border-yellow-500/50'
                          }
                        >
                          {agent.status}
                        </Badge>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">Progress</span>
                          <span className="text-sm text-muted-foreground">{Math.round(agent.progress)}%</span>
                        </div>
                        <Progress value={agent.progress} className="h-2" />
                      </div>

                      <div>
                        <p className="text-sm font-medium mb-1">Current Task:</p>
                        <p className="text-sm text-muted-foreground">{agent.currentTask}</p>
                      </div>

                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{agent.processingTime}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <TrendingUp className="w-3 h-3" />
                          <span>{Math.round(agent.confidence * 100)}% confidence</span>
                        </div>
                      </div>

                      {selectedAgent === agent.id && (
                        <div className="mt-4 pt-4 border-t border-border space-y-3">
                          <div>
                            <p className="text-sm font-medium mb-2">Capabilities:</p>
                            <div className="flex flex-wrap gap-1">
                              {agent.capabilities.map((capability, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {capability}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <p className="text-sm font-medium mb-2">Latest Output:</p>
                            <div className="p-3 rounded bg-accent text-sm">
                              {agent.lastOutput}
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="outputs" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Live Agent Outputs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[500px]">
                    <div className="space-y-4">
                      {recentOutputs.map((output, idx) => (
                        <div key={idx} className="p-4 rounded border">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{output.agent}</Badge>
                              <Badge 
                                variant={
                                  output.priority === 'high' ? 'destructive' :
                                  output.priority === 'medium' ? 'default' : 'secondary'
                                }
                                className="text-xs"
                              >
                                {output.priority}
                              </Badge>
                            </div>
                            <span className="text-xs text-muted-foreground">{output.timestamp}</span>
                          </div>
                          <p className="text-sm">{output.content}</p>
                          <div className="flex items-center gap-2 mt-2">
                            {output.type === 'analysis' && <Brain className="w-3 h-3 text-blue-500" />}
                            {output.type === 'suggestion' && <Lightbulb className="w-3 h-3 text-yellow-500" />}
                            {output.type === 'verification' && <CheckCircle className="w-3 h-3 text-green-500" />}
                            {output.type === 'enhancement' && <Sparkles className="w-3 h-3 text-purple-500" />}
                            <span className="text-xs text-muted-foreground capitalize">{output.type}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Cpu className="w-5 h-5" />
                    System Performance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">CPU Usage</span>
                      <span className="text-sm text-muted-foreground">{systemMetrics.totalProcessingPower}%</span>
                    </div>
                    <Progress value={systemMetrics.totalProcessingPower} className="h-2" />
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Memory Usage</span>
                      <span className="text-sm text-muted-foreground">67%</span>
                    </div>
                    <Progress value={67} className="h-2" />
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Network I/O</span>
                      <span className="text-sm text-muted-foreground">23%</span>
                    </div>
                    <Progress value={23} className="h-2" />
                  </div>

                  <div className="pt-4 border-t border-border">
                    <h4 className="font-medium mb-3">Agent Status Distribution</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Active</span>
                        <Badge variant="default" className="bg-green-500/20 text-green-700">4</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Processing</span>
                        <Badge variant="secondary" className="bg-blue-500/20 text-blue-700">1</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Queued</span>
                        <Badge variant="outline" className="bg-yellow-500/20 text-yellow-700">1</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6 text-center">
                  <Database className="w-8 h-8 text-primary mx-auto mb-2" />
                  <div className="text-2xl font-bold">247</div>
                  <div className="text-sm text-muted-foreground">Total Analyses</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold">94.2%</div>
                  <div className="text-sm text-muted-foreground">Accuracy Rate</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold">2.1s</div>
                  <div className="text-sm text-muted-foreground">Avg Response</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <Zap className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold">156</div>
                  <div className="text-sm text-muted-foreground">Suggestions Applied</div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Agent Performance Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {agentStates.map((agent) => (
                    <div key={agent.id} className="flex items-center justify-between p-3 rounded border">
                      <div className="flex items-center gap-3">
                        <agent.avatar className="w-5 h-5 text-primary" />
                        <span className="font-medium">{agent.name}</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="text-sm font-medium">{Math.round(agent.confidence * 100)}%</div>
                          <div className="text-xs text-muted-foreground">Confidence</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{agent.processingTime}</div>
                          <div className="text-xs text-muted-foreground">Speed</div>
                        </div>
                        <Progress value={agent.progress} className="w-20 h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
