import { NextRequest } from 'next/server'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { contentIndexingService } from '@/lib/services/content-indexing-service'
import { logger } from '@/lib/services/logger'

// This endpoint should be called by a cron job
// Run every 15 minutes: */15 * * * *
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return UnifiedResponse.error('Unauthorized', 401)
    }

    const supabase = await createTypedServerClient()
    const startTime = Date.now()
    let processedCount = 0
    let errorCount = 0

    // Get unprocessed content (content without embeddings)
    const { data: pendingContent, error: fetchError } = await supabase
      .from('chapters')
      .select('id, project_id, content')
      .is('embedding_generated', false)
      .not('content', 'is', null)
      .limit(10) // Process in batches to avoid timeout

    if (fetchError) {
      logger.error('Failed to fetch pending content:', fetchError)
      return UnifiedResponse.error('Failed to fetch pending content')
    }

    if (!pendingContent || pendingContent.length === 0) {
      logger.info('No pending content to process')
      return UnifiedResponse.success({
        message: 'No pending content to process',
        processedCount: 0
      })
    }

    // Process each chapter
    for (const chapter of pendingContent) {
      try {
        // Skip if content is too short
        if (!chapter.content || chapter.content.length < 100) {
          continue
        }

        // Generate embedding using content indexing service
        await contentIndexingService.indexChapter(chapter.id, chapter.project_id, chapter.content)
        
        // Mark as processed
        await supabase
          .from('chapters')
          .update({ 
            embedding_generated: true,
            embedding_generated_at: new Date().toISOString()
          })
          .eq('id', chapter.id)

        processedCount++
        
        // Log progress
        logger.info('Generated embedding for chapter', {
          chapterId: chapter.id,
          projectId: chapter.project_id
        })
      } catch (error) {
        errorCount++
        logger.error('Failed to generate embedding for chapter:', {
          chapterId: chapter.id,
          error
        })
      }
    }

    // Also process story bible entries
    const { data: pendingStoryBible, error: storyBibleError } = await supabase
      .from('story_bible')
      .select('id, project_id, type, data')
      .is('embedding_generated', false)
      .not('data', 'is', null)
      .limit(10)

    if (!storyBibleError && pendingStoryBible) {
      for (const entry of pendingStoryBible) {
        try {
          // Extract text content from data JSON
          const content = extractTextFromStoryBibleData(entry.data, entry.type)
          
          if (content && content.length > 50) {
            await contentIndexingService.indexStoryBibleEntry(
              entry.id, 
              entry.project_id, 
              entry.type,
              content
            )
            
            // Mark as processed
            await supabase
              .from('story_bible')
              .update({ 
                embedding_generated: true,
                embedding_generated_at: new Date().toISOString()
              })
              .eq('id', entry.id)

            processedCount++
          }
        } catch (error) {
          errorCount++
          logger.error('Failed to generate embedding for story bible entry:', {
            entryId: entry.id,
            error
          })
        }
      }
    }

    const duration = Date.now() - startTime

    logger.info('Embedding generation completed', {
      processedCount,
      errorCount,
      duration
    })

    return UnifiedResponse.success({
      message: 'Embedding generation completed',
      processedCount,
      errorCount,
      duration,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Error in embedding generation cron:', error)
    return UnifiedResponse.error('Failed to generate embeddings')
  }
}

function extractTextFromStoryBibleData(data: any, type: string): string {
  try {
    switch (type) {
      case 'character':
        return [
          data.name,
          data.role,
          data.description,
          data.personality,
          data.backstory,
          data.goals,
          data.conflicts,
          data.relationships
        ].filter(Boolean).join(' ')
        
      case 'location':
        return [
          data.name,
          data.type,
          data.description,
          data.significance,
          data.atmosphere,
          data.history
        ].filter(Boolean).join(' ')
        
      case 'worldbuilding':
        return [
          data.category,
          data.title,
          data.description,
          data.details,
          data.implications
        ].filter(Boolean).join(' ')
        
      case 'theme':
        return [
          data.name,
          data.description,
          data.exploration,
          data.symbolism
        ].filter(Boolean).join(' ')
        
      case 'plotpoint':
        return [
          data.title,
          data.description,
          data.impact,
          data.foreshadowing,
          data.resolution
        ].filter(Boolean).join(' ')
        
      default:
        // Generic extraction
        return Object.values(data)
          .filter(v => typeof v === 'string')
          .join(' ')
    }
  } catch (error) {
    logger.error('Failed to extract text from story bible data:', error)
    return ''
  }
}