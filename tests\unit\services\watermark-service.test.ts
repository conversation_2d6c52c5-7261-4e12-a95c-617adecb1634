import { PDFDocument } from 'pdf-lib'
import { watermarkService } from '@/lib/services/watermark-service'

// Mock the logger
jest.mock('@/lib/services/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}))

global.fetch = jest.fn()

describe('WatermarkService', () => {
  it('embeds logo image when logoUrl is provided', async () => {
    const pdfDoc = await PDFDocument.create()
    pdfDoc.addPage()
    const pdfBytes = await pdfDoc.save()

    const pngBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z/C/HwAF/gL+Xx9n5QAAAABJRU5ErkJggg=='
    const buffer = Buffer.from(pngBase64, 'base64')

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      arrayBuffer: async () => buffer,
      headers: { get: () => 'image/png' }
    })

    const result = await watermarkService.addWatermark(pdfBytes.buffer, {
      logoUrl: 'https://example.com/logo.png'
    })

    const pdfString = Buffer.from(result).toString('latin1')
    expect(pdfString).toContain('/Subtype /Image')
  })
})
