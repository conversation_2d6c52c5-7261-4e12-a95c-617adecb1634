#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const glob = require('glob');

async function fixErrorHandlerImports() {
  console.log('🔄 Fixing error handler imports...');
  
  // Find all TypeScript/TSX files that use error handlers
  const files = glob.sync('src/**/*.{ts,tsx}', {
    ignore: [
      '**/node_modules/**', 
      '**/dist/**', 
      '**/build/**'
    ]
  });
  
  let updatedCount = 0;
  const updates = [];
  
  for (const file of files) {
    try {
      let content = await fs.readFile(file, 'utf-8');
      let hasChanges = false;
      const originalContent = content;
      
      // Check if file uses error types without importing them
      const errorTypes = ['ValidationError', 'AuthenticationError', 'AuthorizationError', 'NotFoundError'];
      const usedTypes = [];
      
      errorTypes.forEach(type => {
        if (content.includes(`new ${type}(`) && !content.includes(`${type} from`)) {
          usedTypes.push(type);
        }
      });
      
      if (usedTypes.length > 0 && content.includes("from '@/lib/api/error-handler'")) {
        // Update the import to include used types
        content = content.replace(
          /import\s*{\s*handleAPIError\s*}\s*from\s*'@\/lib\/api\/error-handler';?/,
          `import { handleAPIError, ${usedTypes.join(', ')} } from '@/lib/api/error-handler';`
        );
        hasChanges = true;
      }
      
      if (hasChanges && content !== originalContent) {
        await fs.writeFile(file, content);
        updatedCount++;
        updates.push(`${file}: added ${usedTypes.join(', ')}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log(`\n✅ Fixed ${updatedCount} files`);
  
  if (updates.length > 0) {
    console.log('\n📝 Files updated:');
    updates.forEach(update => console.log(`  - ${update}`));
  }
}

// Run the fix
fixErrorHandlerImports().catch(console.error);