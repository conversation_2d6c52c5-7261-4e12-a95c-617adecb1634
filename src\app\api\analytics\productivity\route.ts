import { NextResponse, NextRequest } from 'next/server'
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, differenceInDays } from 'date-fns'
import { logger } from '@/lib/services/logger'
import type { Database } from '@/lib/db/types'
import { TIME_MS } from '@/lib/constants'
import { z } from 'zod'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { baseSchemas } from '@/lib/validation/common-schemas'

type WritingSession = Database['public']['Tables']['writing_sessions']['Row']

// Query validation schema
const productivityQuerySchema = z.object({
  projectId: baseSchemas.uuid.optional(),
  timeframe: z.enum(['week', 'month', 'quarter', 'year']).optional().default('week'),
  insights: z.enum(['true', 'false']).transform(val => val === 'true').optional().default('false')
})

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId'),
    timeframe: searchParams.get('timeframe'),
    insights: searchParams.get('insights')
  };

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    querySchema: productivityQuerySchema,
    rateLimitKey: 'analytics-productivity',
    rateLimitCost: 3,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // If projectId provided, verify access
      if (queryParams.projectId) {
        const supabase = await createTypedServerClient();
        const { data: project } = await supabase
          .from('projects')
          .select('id')
          .eq('id', queryParams.projectId)
          .eq('user_id', user.id)
          .single();
        
        if (!project) {
          const { data: collaborator } = await supabase
            .from('project_collaborators')
            .select('role')
            .eq('project_id', queryParams.projectId)
            .eq('user_id', user.id)
            .eq('status', 'active')
            .single();
          
          if (!collaborator) {
            return { valid: false, error: 'Access denied to this project' };
          }
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, timeframe, insights: includeInsights } = context.query;

  try {
    const supabase = await createTypedServerClient();

    // Calculate date range based on timeframe
    const now = new Date()
    let startDate: Date
    let endDate = now

    switch (timeframe) {
      case 'week':
        startDate = startOfWeek(now)
        endDate = endOfWeek(now)
        break
      case 'month':
        startDate = startOfMonth(now)
        endDate = endOfMonth(now)
        break
      case 'quarter':
        startDate = subDays(now, 90)
        break
      case 'year':
        startDate = subDays(now, 365)
        break
      default:
        startDate = subDays(now, 7)
    }

    // Fetch writing sessions
    let sessionsQuery = supabase
      .from('writing_sessions')
      .select('*')
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())

    if (projectId) {
      sessionsQuery = sessionsQuery.eq('project_id', projectId)
    }

    const { data: sessions, error: sessionsError } = await sessionsQuery
      .order('created_at', { ascending: false })
      .limit(1000); // Limit to prevent excessive data
      
    if (sessionsError) {
      logger.error('Failed to fetch writing sessions:', sessionsError, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to fetch productivity data');
    }

    // Fetch daily analytics
    let analyticsQuery = supabase
      .from('user_analytics')
      .select('*')
      .eq('user_id', user.id)
      .gte('date', format(startDate, 'yyyy-MM-dd'))
      .lte('date', format(endDate, 'yyyy-MM-dd'))

    const { data: dailyAnalytics, error: analyticsError } = await analyticsQuery
      .order('date', { ascending: false });
      
    if (analyticsError) {
      logger.error('Failed to fetch daily analytics:', analyticsError, {
        userId: user.id,
        clientIP: context.clientIP
      });
      // Continue without daily analytics - not critical
    }

    // Calculate productivity metrics
    const totalDays = differenceInDays(endDate, startDate) + 1
    const activeDays = new Set(sessions.map(s => format(new Date(s.created_at), 'yyyy-MM-dd'))).size
    const totalWords = sessions.reduce((sum, s) => sum + (s.word_count || 0), 0)
    const totalTime = sessions.reduce((sum, s) => sum + (s.duration || 0), 0)
    const totalSessions = sessions.length

    // Daily averages
    const avgWordsPerDay = activeDays > 0 ? Math.round(totalWords / activeDays) : 0
    const avgTimePerDay = activeDays > 0 ? Math.round(totalTime / activeDays / 60) : 0 // in minutes
    const avgSessionsPerDay = activeDays > 0 ? (totalSessions / activeDays).toFixed(1) : '0'

    // Session averages
    const avgWordsPerSession = totalSessions > 0 ? Math.round(totalWords / totalSessions) : 0
    const avgSessionDuration = totalSessions > 0 ? Math.round(totalTime / totalSessions / 60) : 0 // in minutes

    // Productivity score (0-100)
    const productivityScore = calculateProductivityScore({
      activeDays,
      totalDays,
      avgWordsPerDay,
      avgSessionDuration,
      totalSessions
    })

    // Time patterns
    const hourlyPattern = analyzeHourlyPattern(sessions)
    const weekdayPattern = analyzeWeekdayPattern(sessions)

    // Build response
    const response: any = {
      overview: {
        totalWords,
        totalTime: Math.round(totalTime / 60), // in minutes
        totalSessions,
        activeDays,
        totalDays,
        productivityScore
      },
      averages: {
        wordsPerDay: avgWordsPerDay,
        timePerDay: avgTimePerDay,
        sessionsPerDay: parseFloat(avgSessionsPerDay),
        wordsPerSession: avgWordsPerSession,
        sessionDuration: avgSessionDuration
      },
      patterns: {
        hourly: hourlyPattern,
        weekday: weekdayPattern
      }
    }

    // Generate insights if requested
    if (includeInsights) {
      response.insights = generateProductivityInsights({
        activeDays,
        totalDays,
        avgWordsPerDay,
        avgSessionDuration,
        hourlyPattern,
        weekdayPattern,
        sessions
      })
    }

    // Calculate trends
    if (timeframe === 'week' || timeframe === 'month') {
      const previousStartDate = subDays(startDate, differenceInDays(endDate, startDate) + 1)
      const previousEndDate = subDays(startDate, 1)

      let prevSessionsQuery = supabase
        .from('writing_sessions')
        .select('*')
        .eq('user_id', user.id)
        .gte('created_at', previousStartDate.toISOString())
        .lte('created_at', previousEndDate.toISOString())

      if (projectId) {
        prevSessionsQuery = prevSessionsQuery.eq('project_id', projectId)
      }

      const { data: prevSessions } = await prevSessionsQuery

      if (prevSessions) {
        const prevTotalWords = prevSessions.reduce((sum, s) => sum + (s.word_count || 0), 0)
        const prevActiveDays = new Set(prevSessions.map(s => format(new Date(s.created_at), 'yyyy-MM-dd'))).size

        response.trends = {
          wordsTrend: prevTotalWords > 0 
            ? Math.round(((totalWords - prevTotalWords) / prevTotalWords) * 100)
            : 0,
          daysTrend: prevActiveDays > 0
            ? Math.round(((activeDays - prevActiveDays) / prevActiveDays) * 100)
            : 0
        }
      }
    }

    logger.info('Productivity analytics fetched', {
      userId: user.id,
      projectId,
      timeframe,
      totalSessions: sessions.length,
      activeDays: response.overview.activeDays,
      productivityScore: response.overview.productivityScore,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success(response);

  } catch (error) {
    logger.error('Productivity analytics error:', error, {
      userId: user.id,
      projectId,
      timeframe,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to generate productivity analytics');
  }
});

function calculateProductivityScore(metrics: {
  activeDays: number
  totalDays: number
  avgWordsPerDay: number
  avgSessionDuration: number
  totalSessions: number
}): number {
  const { activeDays, totalDays, avgWordsPerDay, avgSessionDuration, totalSessions } = metrics

  // Consistency score (40% weight)
  const consistencyScore = Math.min((activeDays / totalDays) * 100, 100) * 0.4

  // Volume score (30% weight) - based on average words per day
  const volumeScore = Math.min((avgWordsPerDay / TIME_MS.SECOND) * 100, 100) * 0.3

  // Focus score (20% weight) - based on session duration
  const focusScore = Math.min((avgSessionDuration / 60) * 100, 100) * 0.2

  // Frequency score (10% weight) - based on total sessions
  const frequencyScore = Math.min((totalSessions / (totalDays * 2)) * 100, 100) * 0.1

  return Math.round(consistencyScore + volumeScore + focusScore + frequencyScore)
}

function analyzeHourlyPattern(sessions: WritingSession[]): Array<{ hour: number; words: number; sessions: number }> {
  const hourlyData = new Array(24).fill(0).map((_, hour) => ({
    hour,
    words: 0,
    sessions: 0
  }))

  sessions.forEach(session => {
    const hour = new Date(session.created_at).getHours()
    hourlyData[hour].words += session.word_count || 0
    hourlyData[hour].sessions += 1
  })

  return hourlyData
}

function analyzeWeekdayPattern(sessions: WritingSession[]): Array<{ day: string; words: number; sessions: number }> {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  const weekdayData = days.map(day => ({
    day,
    words: 0,
    sessions: 0
  }))

  sessions.forEach(session => {
    const dayIndex = new Date(session.created_at).getDay()
    weekdayData[dayIndex].words += session.word_count || 0
    weekdayData[dayIndex].sessions += 1
  })

  return weekdayData
}

function generateProductivityInsights(data: {
  activeDays: number
  totalDays: number
  avgWordsPerDay: number
  avgSessionDuration: number
  hourlyPattern: Array<{ hour: number; words: number; sessions: number }>
  weekdayPattern: Array<{ day: string; words: number; sessions: number }>
  sessions: WritingSession[]
}): Array<{ type: string; title: string; message: string; priority: 'high' | 'medium' | 'low' }> {
  const insights = []

  // Consistency insight
  const consistencyRate = (data.activeDays / data.totalDays) * 100
  if (consistencyRate < 50) {
    insights.push({
      type: 'consistency',
      title: 'Improve Writing Consistency',
      message: `You've written on ${data.activeDays} out of ${data.totalDays} days (${Math.round(consistencyRate)}%). Try to write more regularly to build momentum.`,
      priority: 'high' as const
    })
  } else if (consistencyRate > 80) {
    insights.push({
      type: 'consistency',
      title: 'Excellent Consistency!',
      message: `You've maintained a ${Math.round(consistencyRate)}% writing consistency. Keep up the great work!`,
      priority: 'low' as const
    })
  }

  // Peak productivity hours
  const sortedHours = [...data.hourlyPattern].sort((a, b) => b.words - a.words).slice(0, 3)
  if (sortedHours[0].words > 0) {
    const peakHours = sortedHours
      .filter(h => h.words > 0)
      .map(h => {
        const hour = h.hour
        const period = hour < 12 ? 'AM' : 'PM'
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
        return `${displayHour}${period}`
      })
      .join(', ')
    
    insights.push({
      type: 'timing',
      title: 'Peak Writing Hours',
      message: `Your most productive hours are ${peakHours}. Consider scheduling important writing sessions during these times.`,
      priority: 'medium' as const
    })
  }

  // Session duration insight
  if (data.avgSessionDuration < 30) {
    insights.push({
      type: 'focus',
      title: 'Extend Your Writing Sessions',
      message: `Your average session is ${data.avgSessionDuration} minutes. Try aiming for 30-60 minute sessions for better flow and productivity.`,
      priority: 'medium' as const
    })
  }

  // Words per day insight
  if (data.avgWordsPerDay < 500 && data.activeDays > 0) {
    insights.push({
      type: 'volume',
      title: 'Increase Daily Word Count',
      message: `You're averaging ${data.avgWordsPerDay} words per writing day. Set a goal to reach 500-1000 words daily.`,
      priority: 'medium' as const
    })
  } else if (data.avgWordsPerDay > 2000) {
    insights.push({
      type: 'volume',
      title: 'Outstanding Output!',
      message: `You're averaging ${data.avgWordsPerDay} words per writing day. That's professional-level productivity!`,
      priority: 'low' as const
    })
  }

  // Best writing day
  const sortedWeekdays = [...data.weekdayPattern].sort((a, b) => b.words - a.words)
  if (sortedWeekdays[0].words > 0) {
    insights.push({
      type: 'pattern',
      title: 'Best Writing Day',
      message: `${sortedWeekdays[0].day} is your most productive day with an average of ${Math.round(sortedWeekdays[0].words / Math.max(sortedWeekdays[0].sessions, 1))} words per session.`,
      priority: 'low' as const
    })
  }

  return insights
}