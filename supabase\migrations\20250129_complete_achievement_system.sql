-- Complete Achievement System Implementation
-- This creates a full achievement system with all tables, functions, and initial data

-- 1. Drop everything first to start fresh
DROP TRIGGER IF EXISTS check_achievements_on_chapter_update ON chapters CASCADE;
DROP TRIGGER IF EXISTS check_achievements_on_project_create ON projects CASCADE;
DROP TRIGGER IF EXISTS check_achievements_on_ai_usage ON ai_usage_logs CASCADE;
DROP FUNCTION IF EXISTS check_achievements_on_update() CASCADE;
DROP FUNCTION IF EXISTS check_and_unlock_achievements(UUID) CASCADE;
DROP FUNCTION IF EXISTS track_achievement_progress(UUID, VARCHAR, VARCHAR, INTEGER) CASCADE;
DROP TABLE IF EXISTS user_achievements CASCADE;
DROP TABLE IF EXISTS achievement_progress CASCADE;
DROP TABLE IF EXISTS achievements CASCADE;

-- 2. Create achievements table with correct schema
CREATE TABLE achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL, -- Using 'title' not 'name'
  description TEXT,
  points INTEGER DEFAULT 10,
  tier VARCHAR(20) CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')) DEFAULT 'bronze',
  category VARCHAR(50) CHECK (category IN ('writing', 'streak', 'community', 'exploration', 'mastery')),
  criteria JSONB NOT NULL,
  icon VARCHAR(50),
  target_value INTEGER GENERATED ALWAYS AS ((criteria->>'value')::INTEGER) STORED,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create user achievements tracking
CREATE TABLE user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ,
  current_value INTEGER DEFAULT 0,
  progress INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, achievement_id)
);

-- 4. Create achievement progress tracking
CREATE TABLE achievement_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_code VARCHAR(100) NOT NULL,
  progress_key VARCHAR(100) NOT NULL,
  progress_value INTEGER DEFAULT 0,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, achievement_code, progress_key)
);

-- 5. Create indexes for performance
CREATE INDEX idx_achievements_code ON achievements(code);
CREATE INDEX idx_achievements_category ON achievements(category);
CREATE INDEX idx_user_achievements_user ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_unlocked ON user_achievements(unlocked_at DESC);
CREATE INDEX idx_user_achievements_achievement ON user_achievements(achievement_id);
CREATE INDEX idx_achievement_progress_user ON achievement_progress(user_id);

-- 6. Enable RLS
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievement_progress ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS Policies
CREATE POLICY "Achievements are public" ON achievements
  FOR SELECT USING (true);

CREATE POLICY "Users can view their own achievements" ON user_achievements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own achievements" ON user_achievements
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own achievements" ON user_achievements
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own progress" ON achievement_progress
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own progress" ON achievement_progress
  FOR ALL USING (auth.uid() = user_id);

-- 8. Insert comprehensive achievement definitions
INSERT INTO achievements (code, title, description, points, tier, category, criteria, icon) VALUES
  -- Writing achievements
  ('first_words', 'First Words', 'Write your first 100 words', 10, 'bronze', 'writing', '{"type": "word_count", "value": 100}', 'edit'),
  ('novice_writer', 'Novice Writer', 'Write 1,000 words', 25, 'bronze', 'writing', '{"type": "word_count", "value": 1000}', 'edit-2'),
  ('apprentice_writer', 'Apprentice Writer', 'Write 10,000 words', 50, 'silver', 'writing', '{"type": "word_count", "value": 10000}', 'edit-3'),
  ('journeyman_writer', 'Journeyman Writer', 'Write 50,000 words', 100, 'gold', 'writing', '{"type": "word_count", "value": 50000}', 'book'),
  ('master_writer', 'Master Writer', 'Write 100,000 words', 200, 'platinum', 'writing', '{"type": "word_count", "value": 100000}', 'book-open'),
  ('epic_writer', 'Epic Writer', 'Write 250,000 words', 500, 'platinum', 'writing', '{"type": "word_count", "value": 250000}', 'scroll'),
  
  -- Chapter achievements
  ('chapter_one', 'Chapter One', 'Complete your first chapter', 20, 'bronze', 'writing', '{"type": "chapters_completed", "value": 1}', 'bookmark'),
  ('five_chapters', 'Five Chapters', 'Complete 5 chapters', 50, 'silver', 'writing', '{"type": "chapters_completed", "value": 5}', 'bookmarks'),
  ('ten_chapters', 'Ten Chapters', 'Complete 10 chapters', 100, 'gold', 'writing', '{"type": "chapters_completed", "value": 10}', 'library'),
  ('twenty_chapters', 'Twenty Chapters', 'Complete 20 chapters', 150, 'gold', 'writing', '{"type": "chapters_completed", "value": 20}', 'archive'),
  ('fifty_chapters', 'Fifty Chapters', 'Complete 50 chapters', 300, 'platinum', 'writing', '{"type": "chapters_completed", "value": 50}', 'database'),
  
  -- Project achievements
  ('first_project', 'First Project', 'Create your first project', 15, 'bronze', 'exploration', '{"type": "projects_created", "value": 1}', 'folder-plus'),
  ('multi_project', 'Multi-Project Author', 'Create 5 projects', 40, 'silver', 'exploration', '{"type": "projects_created", "value": 5}', 'folders'),
  ('prolific_creator', 'Prolific Creator', 'Create 10 projects', 80, 'gold', 'exploration', '{"type": "projects_created", "value": 10}', 'hard-drive'),
  
  -- Series achievements
  ('series_starter', 'Series Starter', 'Create your first series', 30, 'silver', 'exploration', '{"type": "series_created", "value": 1}', 'link'),
  ('series_master', 'Series Master', 'Create 3 series', 100, 'gold', 'mastery', '{"type": "series_created", "value": 3}', 'link-2'),
  
  -- Daily writing streaks
  ('three_day_streak', 'Getting Started', 'Write for 3 consecutive days', 15, 'bronze', 'streak', '{"type": "daily_streak", "value": 3}', 'flame'),
  ('week_streak', 'Week Warrior', 'Write for 7 consecutive days', 30, 'bronze', 'streak', '{"type": "daily_streak", "value": 7}', 'calendar'),
  ('two_week_streak', 'Committed Writer', 'Write for 14 consecutive days', 60, 'silver', 'streak', '{"type": "daily_streak", "value": 14}', 'calendar-check'),
  ('month_streak', 'Monthly Marathon', 'Write for 30 consecutive days', 100, 'gold', 'streak', '{"type": "daily_streak", "value": 30}', 'calendar-days'),
  ('quarter_streak', 'Quarterly Champion', 'Write for 90 consecutive days', 300, 'platinum', 'streak', '{"type": "daily_streak", "value": 90}', 'trophy'),
  
  -- Word count in a day achievements
  ('daily_hundred', 'Daily Hundred', 'Write 100 words in a day', 5, 'bronze', 'writing', '{"type": "daily_words", "value": 100}', 'zap'),
  ('daily_thousand', 'Daily Thousand', 'Write 1,000 words in a day', 20, 'silver', 'writing', '{"type": "daily_words", "value": 1000}', 'zap'),
  ('daily_five_thousand', 'Speed Demon', 'Write 5,000 words in a day', 50, 'gold', 'writing', '{"type": "daily_words", "value": 5000}', 'rocket'),
  
  -- AI usage achievements
  ('ai_explorer', 'AI Explorer', 'Use an AI agent for the first time', 10, 'bronze', 'exploration', '{"type": "ai_uses", "value": 1}', 'cpu'),
  ('ai_collaborator', 'AI Collaborator', 'Use AI agents 50 times', 40, 'silver', 'exploration', '{"type": "ai_uses", "value": 50}', 'bot'),
  ('ai_master', 'AI Master', 'Use AI agents 200 times', 100, 'gold', 'mastery', '{"type": "ai_uses", "value": 200}', 'brain'),
  
  -- Character achievements
  ('character_creator', 'Character Creator', 'Create your first character', 10, 'bronze', 'exploration', '{"type": "characters_created", "value": 1}', 'user-plus'),
  ('cast_builder', 'Cast Builder', 'Create 10 characters', 30, 'silver', 'exploration', '{"type": "characters_created", "value": 10}', 'users'),
  ('population_master', 'Population Master', 'Create 50 characters', 80, 'gold', 'mastery', '{"type": "characters_created", "value": 50}', 'users-2'),
  
  -- Export achievements
  ('first_export', 'First Export', 'Export your first project', 15, 'bronze', 'exploration', '{"type": "exports", "value": 1}', 'download'),
  ('export_veteran', 'Export Veteran', 'Export 10 times', 40, 'silver', 'mastery', '{"type": "exports", "value": 10}', 'file-output'),
  
  -- Universe achievements
  ('world_builder', 'World Builder', 'Create your first universe', 25, 'silver', 'exploration', '{"type": "universes_created", "value": 1}', 'globe'),
  ('multiverse_creator', 'Multiverse Creator', 'Create 3 universes', 75, 'gold', 'mastery', '{"type": "universes_created", "value": 3}', 'globe-2');

-- 9. Create the main achievement checking function
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE (newly_unlocked UUID[]) AS $$
DECLARE
  v_newly_unlocked UUID[];
  v_achievement RECORD;
  v_current_value INTEGER;
  v_progress INTEGER;
  v_user_achievement_id UUID;
  v_table_exists BOOLEAN;
BEGIN
  v_newly_unlocked := ARRAY[]::UUID[];
  
  -- Check each achievement
  FOR v_achievement IN 
    SELECT a.* 
    FROM achievements a
    LEFT JOIN user_achievements ua ON ua.achievement_id = a.id AND ua.user_id = p_user_id
    WHERE ua.unlocked_at IS NULL OR ua.id IS NULL
  LOOP
    v_current_value := 0;
    
    -- Calculate current value based on achievement type
    CASE v_achievement.criteria->>'type'
      WHEN 'word_count' THEN
        SELECT COALESCE(SUM(word_count), 0)
        FROM chapters c
        JOIN projects p ON p.id = c.project_id
        WHERE p.user_id = p_user_id
        INTO v_current_value;
        
      WHEN 'chapters_completed' THEN
        SELECT COUNT(*)
        FROM chapters c
        JOIN projects p ON p.id = c.project_id
        WHERE p.user_id = p_user_id 
        AND c.status IN ('published', 'completed')
        INTO v_current_value;
        
      WHEN 'projects_created' THEN
        SELECT COUNT(*)
        FROM projects
        WHERE user_id = p_user_id
        INTO v_current_value;
        
      WHEN 'series_created' THEN
        -- Check if series table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'series'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM series
          WHERE user_id = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'characters_created' THEN
        -- Check if characters table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'characters'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM characters c
          JOIN books b ON b.id = c.book_id
          JOIN projects p ON p.id = b.project_id
          WHERE p.user_id = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'universes_created' THEN
        -- Check if universes table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'universes'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM universes
          WHERE created_by = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'daily_streak' THEN
        -- Check if writing_sessions table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'writing_sessions'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          -- Calculate consecutive days streak
          WITH consecutive_days AS (
            SELECT 
              session_date,
              session_date - INTERVAL '1 day' * ROW_NUMBER() OVER (ORDER BY session_date) AS grp
            FROM writing_sessions
            WHERE user_id = p_user_id
            AND words_written > 0
            ORDER BY session_date DESC
          ),
          streaks AS (
            SELECT 
              COUNT(*) as streak_length,
              MAX(session_date) as last_date
            FROM consecutive_days
            GROUP BY grp
            ORDER BY MAX(session_date) DESC
            LIMIT 1
          )
          SELECT COALESCE(MAX(streak_length), 0)::INTEGER
          FROM streaks
          WHERE last_date >= CURRENT_DATE - INTERVAL '1 day'
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'daily_words' THEN
        -- Check if writing_sessions table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'writing_sessions'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COALESCE(MAX(words_written), 0)
          FROM writing_sessions
          WHERE user_id = p_user_id
          AND session_date = CURRENT_DATE
          INTO v_current_value;
        ELSE
          -- Fall back to checking today's chapter updates
          SELECT COALESCE(SUM(word_count), 0)
          FROM chapters c
          JOIN projects p ON p.id = c.project_id
          WHERE p.user_id = p_user_id
          AND DATE(c.updated_at) = CURRENT_DATE
          INTO v_current_value;
        END IF;
        
      WHEN 'ai_uses' THEN
        -- Check if ai_usage_logs table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'ai_usage_logs'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM ai_usage_logs
          WHERE user_id = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'exports' THEN
        -- For now, return 0 (would need export_logs table)
        v_current_value := 0;
        
      ELSE
        v_current_value := 0;
    END CASE;
    
    -- Calculate progress percentage
    IF v_achievement.target_value > 0 THEN
      v_progress := LEAST(100, (v_current_value * 100 / v_achievement.target_value));
    ELSE
      v_progress := 0;
    END IF;
    
    -- Insert or update user achievement record
    INSERT INTO user_achievements (user_id, achievement_id, current_value, progress)
    VALUES (p_user_id, v_achievement.id, v_current_value, v_progress)
    ON CONFLICT (user_id, achievement_id) 
    DO UPDATE SET 
      current_value = EXCLUDED.current_value,
      progress = EXCLUDED.progress,
      updated_at = NOW()
    RETURNING id INTO v_user_achievement_id;
    
    -- Check if achievement should be unlocked
    IF v_current_value >= v_achievement.target_value THEN
      UPDATE user_achievements 
      SET unlocked_at = NOW()
      WHERE id = v_user_achievement_id 
      AND unlocked_at IS NULL;
      
      IF FOUND THEN
        v_newly_unlocked := array_append(v_newly_unlocked, v_achievement.id);
      END IF;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT v_newly_unlocked;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Grant permissions
GRANT EXECUTE ON FUNCTION check_and_unlock_achievements(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_and_unlock_achievements(UUID) TO anon;

-- 11. Create helper function to get user achievement stats
CREATE OR REPLACE FUNCTION get_user_achievement_stats(p_user_id UUID)
RETURNS TABLE (
  total_achievements INTEGER,
  unlocked_achievements INTEGER,
  total_points INTEGER,
  bronze_count INTEGER,
  silver_count INTEGER,
  gold_count INTEGER,
  platinum_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(DISTINCT a.id)::INTEGER as total_achievements,
    COUNT(DISTINCT CASE WHEN ua.unlocked_at IS NOT NULL THEN ua.achievement_id END)::INTEGER as unlocked_achievements,
    COALESCE(SUM(CASE WHEN ua.unlocked_at IS NOT NULL THEN a.points ELSE 0 END), 0)::INTEGER as total_points,
    COUNT(DISTINCT CASE WHEN ua.unlocked_at IS NOT NULL AND a.tier = 'bronze' THEN a.id END)::INTEGER as bronze_count,
    COUNT(DISTINCT CASE WHEN ua.unlocked_at IS NOT NULL AND a.tier = 'silver' THEN a.id END)::INTEGER as silver_count,
    COUNT(DISTINCT CASE WHEN ua.unlocked_at IS NOT NULL AND a.tier = 'gold' THEN a.id END)::INTEGER as gold_count,
    COUNT(DISTINCT CASE WHEN ua.unlocked_at IS NOT NULL AND a.tier = 'platinum' THEN a.id END)::INTEGER as platinum_count
  FROM achievements a
  LEFT JOIN user_achievements ua ON ua.achievement_id = a.id AND ua.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION get_user_achievement_stats(UUID) TO authenticated;

-- 12. Create view for easier querying
CREATE OR REPLACE VIEW user_achievements_view AS
SELECT 
  ua.id,
  ua.user_id,
  ua.achievement_id,
  a.code,
  a.title,
  a.description,
  a.points,
  a.tier,
  a.category,
  a.icon,
  a.target_value,
  ua.current_value,
  ua.progress,
  ua.unlocked_at,
  ua.created_at,
  ua.updated_at,
  CASE 
    WHEN ua.unlocked_at IS NOT NULL THEN 'unlocked'
    WHEN ua.progress > 0 THEN 'in_progress'
    ELSE 'locked'
  END as status
FROM user_achievements ua
JOIN achievements a ON a.id = ua.achievement_id;

-- Grant permissions on the view
GRANT SELECT ON user_achievements_view TO authenticated;

-- 13. Success message
DO $$ 
BEGIN
    RAISE NOTICE 'Achievement system installed successfully!';
    RAISE NOTICE 'Total achievements created: %', (SELECT COUNT(*) FROM achievements);
END $$;