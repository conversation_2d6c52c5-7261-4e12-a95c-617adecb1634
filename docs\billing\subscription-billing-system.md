# BookScribe Subscription & Billing System Documentation

## Overview

BookScribe implements a comprehensive subscription billing system using Stripe for payment processing, with multiple tiers designed for different user needs. The system handles subscription management, usage tracking, payment processing, and automated billing with webhook integration for real-time updates.

## Architecture

### System Components

```mermaid
graph TB
    subgraph "Billing Core"
        SubscriptionManager[Subscription Manager]
        PaymentProcessor[Payment Processor]
        UsageTracker[Usage Tracker]
        BillingEngine[Billing Engine]
    end
    
    subgraph "External Services"
        Stripe[Stripe API]
        StripeWebhooks[Stripe Webhooks]
        CustomerPortal[Stripe Customer Portal]
    end
    
    subgraph "Data Layer"
        UserSubscriptions[User Subscriptions]
        PaymentHistory[Payment History]
        UsageMetrics[Usage Metrics]
        InvoiceRecords[Invoice Records]
    end
    
    subgraph "Features"
        TierManagement[Tier Management]
        QuotaEnforcement[Quota Enforcement]
        Proration[Proration Logic]
        Analytics[Billing Analytics]
    end
    
    SubscriptionManager --> Stripe
    PaymentProcessor --> Stripe
    StripeWebhooks --> BillingEngine
    UsageTracker --> QuotaEnforcement
```

## Subscription Tiers

### Tier Structure

```typescript
interface PricingTier {
  id: 'free' | 'novelist' | 'professional' | 'enterprise'
  name: string
  description: string
  monthlyPrice: number // in cents
  yearlyPrice: number // in cents
  stripePriceId: {
    monthly: string
    yearly: string
  }
  features: TierFeature[]
  limits: TierLimits
  highlighted?: boolean
  badge?: string
}

interface TierFeature {
  name: string
  included: boolean
  value?: string | number
}

interface TierLimits {
  projects: number
  aiWordsPerMonth: number
  collaborators: number
  storage: number // GB
  exportFormats: string[]
}
```

### Available Tiers

#### 1. Starter (Free)
```typescript
{
  id: 'free',
  name: 'Starter',
  monthlyPrice: 0,
  features: [
    '1 Active Project',
    'Basic AI Writing Assistant',
    '10,000 AI Words/month',
    'Character & World Building',
    'Chapter Planning',
    'Export to TXT/Markdown',
    'Community Support'
  ],
  limits: {
    projects: 1,
    aiWordsPerMonth: 10000,
    collaborators: 0,
    storage: 1
  }
}
```

#### 2. Novelist ($29/month)
```typescript
{
  id: 'novelist',
  name: 'Novelist',
  monthlyPrice: 2900,
  yearlyPrice: 27900, // 20% discount
  features: [
    '5 Active Projects',
    'All AI Writing Agents',
    '100,000 AI Words/month',
    'Advanced Character Development',
    'Voice Profile System',
    'Writing Analytics',
    'Export to DOCX/PDF',
    'Email Support'
  ],
  limits: {
    projects: 5,
    aiWordsPerMonth: 100000,
    collaborators: 0,
    storage: 10
  }
}
```

#### 3. Professional ($59/month)
```typescript
{
  id: 'professional',
  name: 'Professional',
  monthlyPrice: 5900,
  yearlyPrice: 56900, // 20% discount
  features: [
    'Unlimited Projects',
    'All AI Writing Agents',
    '500,000 AI Words/month',
    'Series & Universe Management',
    'Real-time Collaboration (5 users)',
    'Advanced Analytics & Insights',
    'Export to All Formats',
    'Voice Cloning (Beta)',
    'Priority Email Support'
  ],
  limits: {
    projects: -1, // unlimited
    aiWordsPerMonth: 500000,
    collaborators: 5,
    storage: 100
  }
}
```

#### 4. Enterprise (Custom Pricing)
```typescript
{
  id: 'enterprise',
  name: 'Enterprise',
  monthlyPrice: -1, // custom
  features: [
    'Everything in Professional',
    'Unlimited AI Words',
    'Unlimited Team Members',
    'Custom AI Model Training',
    'White-label Options',
    'API Access',
    'Dedicated Infrastructure',
    'SLA & Uptime Guarantee',
    '24/7 Phone Support',
    'Dedicated Account Manager'
  ],
  limits: {
    projects: -1,
    aiWordsPerMonth: -1,
    collaborators: -1,
    storage: -1
  }
}
```

## Subscription Management

### Subscription Service

```typescript
// src/lib/billing/subscription-service.ts
export class SubscriptionService {
  private stripe: Stripe
  private supabase: SupabaseClient
  
  async createSubscription(
    userId: string,
    tierId: string,
    billingPeriod: 'monthly' | 'yearly'
  ): Promise<SubscriptionResult> {
    // Get user and validate
    const user = await this.getUser(userId)
    if (!user) throw new Error('User not found')
    
    // Get or create Stripe customer
    const customerId = await this.ensureStripeCustomer(user)
    
    // Get the selected tier
    const tier = SUBSCRIPTION_TIERS.find(t => t.id === tierId)
    if (!tier) throw new Error('Invalid tier')
    
    // Create subscription in Stripe
    const subscription = await this.stripe.subscriptions.create({
      customer: customerId,
      items: [{
        price: billingPeriod === 'monthly' 
          ? tier.stripePriceId.monthly 
          : tier.stripePriceId.yearly
      }],
      payment_behavior: 'default_incomplete',
      payment_settings: {
        save_default_payment_method: 'on_subscription'
      },
      expand: ['latest_invoice.payment_intent']
    })
    
    // Save subscription to database
    await this.saveSubscription({
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: customerId,
      tier_id: tierId,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000),
      current_period_end: new Date(subscription.current_period_end * 1000),
      cancel_at_period_end: subscription.cancel_at_period_end
    })
    
    return {
      subscriptionId: subscription.id,
      clientSecret: subscription.latest_invoice.payment_intent.client_secret
    }
  }
  
  async cancelSubscription(
    userId: string,
    immediately: boolean = false
  ): Promise<void> {
    const subscription = await this.getUserSubscription(userId)
    if (!subscription) throw new Error('No active subscription')
    
    // Cancel in Stripe
    const updated = await this.stripe.subscriptions.update(
      subscription.stripe_subscription_id,
      {
        cancel_at_period_end: !immediately,
        ...(immediately && { cancel_at: 'now' })
      }
    )
    
    // Update database
    await this.updateSubscriptionStatus(
      subscription.id,
      immediately ? 'canceled' : 'active',
      !immediately
    )
    
    // Log cancellation
    await this.logSubscriptionEvent({
      user_id: userId,
      event_type: 'subscription_canceled',
      metadata: {
        immediate: immediately,
        reason: 'user_requested'
      }
    })
  }
  
  async updateSubscription(
    userId: string,
    newTierId: string,
    billingPeriod?: 'monthly' | 'yearly'
  ): Promise<void> {
    const subscription = await this.getUserSubscription(userId)
    if (!subscription) throw new Error('No active subscription')
    
    const newTier = SUBSCRIPTION_TIERS.find(t => t.id === newTierId)
    if (!newTier) throw new Error('Invalid tier')
    
    // Get new price ID
    const priceId = billingPeriod === 'yearly' 
      ? newTier.stripePriceId.yearly 
      : newTier.stripePriceId.monthly
    
    // Update subscription in Stripe
    const stripeSubscription = await this.stripe.subscriptions.retrieve(
      subscription.stripe_subscription_id
    )
    
    await this.stripe.subscriptions.update(
      subscription.stripe_subscription_id,
      {
        items: [{
          id: stripeSubscription.items.data[0].id,
          price: priceId
        }],
        proration_behavior: 'create_prorations'
      }
    )
    
    // Update database
    await this.updateSubscriptionTier(subscription.id, newTierId)
  }
}
```

### Usage Tracking

```typescript
// src/lib/billing/usage-tracker.ts
export class UsageTracker {
  async trackUsage(
    userId: string,
    usageType: 'ai_generation' | 'storage' | 'projects',
    amount: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    const currentPeriod = this.getCurrentBillingPeriod(userId)
    
    // Update usage in database
    await this.supabase
      .from('usage_tracking')
      .upsert({
        user_id: userId,
        period_start: currentPeriod.start,
        period_end: currentPeriod.end,
        [`${usageType}_count`]: amount,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,period_start',
        ignoreDuplicates: false
      })
    
    // Check if user exceeded limits
    const limits = await this.checkUsageLimits(userId)
    if (limits.exceeded) {
      await this.handleLimitExceeded(userId, usageType, limits)
    }
    
    // Log usage event
    await this.logUsageEvent({
      user_id: userId,
      usage_type: usageType,
      amount,
      metadata,
      timestamp: new Date().toISOString()
    })
  }
  
  async checkUsageLimits(userId: string): Promise<UsageLimits> {
    const subscription = await this.getUserSubscription(userId)
    const tier = this.getTierById(subscription?.tier_id || 'free')
    const usage = await this.getCurrentUsage(userId)
    
    return {
      ai_words: {
        used: usage.ai_words,
        limit: tier.limits.aiWordsPerMonth,
        percentage: (usage.ai_words / tier.limits.aiWordsPerMonth) * 100,
        exceeded: usage.ai_words >= tier.limits.aiWordsPerMonth
      },
      projects: {
        used: usage.projects,
        limit: tier.limits.projects,
        percentage: tier.limits.projects === -1 ? 0 : 
          (usage.projects / tier.limits.projects) * 100,
        exceeded: tier.limits.projects !== -1 && 
          usage.projects >= tier.limits.projects
      },
      storage: {
        used: usage.storage_gb,
        limit: tier.limits.storage,
        percentage: tier.limits.storage === -1 ? 0 : 
          (usage.storage_gb / tier.limits.storage) * 100,
        exceeded: tier.limits.storage !== -1 && 
          usage.storage_gb >= tier.limits.storage
      }
    }
  }
}
```

## Payment Processing

### Payment Intent Creation

```typescript
// src/app/api/billing/payments/create-payment-intent/route.ts
export async function POST(request: Request) {
  const { amount, currency = 'usd', metadata } = await request.json()
  const user = await authenticateUser(request)
  
  // Create payment intent
  const paymentIntent = await stripe.paymentIntents.create({
    amount,
    currency,
    customer: user.stripe_customer_id,
    metadata: {
      user_id: user.id,
      ...metadata
    },
    automatic_payment_methods: {
      enabled: true
    }
  })
  
  return NextResponse.json({
    clientSecret: paymentIntent.client_secret
  })
}
```

### Stripe Webhook Handler

```typescript
// src/app/api/billing/webhooks/stripe/route.ts
export async function POST(request: Request) {
  const body = await request.text()
  const signature = request.headers.get('stripe-signature')!
  
  let event: Stripe.Event
  
  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (err) {
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    )
  }
  
  // Handle different event types
  switch (event.type) {
    case 'checkout.session.completed':
      await handleCheckoutCompleted(event.data.object)
      break
      
    case 'customer.subscription.created':
    case 'customer.subscription.updated':
      await handleSubscriptionUpdate(event.data.object)
      break
      
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object)
      break
      
    case 'invoice.payment_succeeded':
      await handlePaymentSucceeded(event.data.object)
      break
      
    case 'invoice.payment_failed':
      await handlePaymentFailed(event.data.object)
      break
      
    default:
      console.log(`Unhandled event type: ${event.type}`)
  }
  
  return NextResponse.json({ received: true })
}

async function handleSubscriptionUpdate(
  subscription: Stripe.Subscription
) {
  const userId = subscription.metadata.user_id
  if (!userId) return
  
  // Update subscription in database
  await supabase
    .from('user_subscriptions')
    .upsert({
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      status: subscription.status,
      tier_id: getTierFromPriceId(subscription.items.data[0].price.id),
      current_period_start: new Date(subscription.current_period_start * 1000),
      current_period_end: new Date(subscription.current_period_end * 1000),
      cancel_at_period_end: subscription.cancel_at_period_end,
      canceled_at: subscription.canceled_at 
        ? new Date(subscription.canceled_at * 1000) 
        : null
    })
  
  // Reset usage for new billing period
  if (subscription.status === 'active') {
    await resetUsageForNewPeriod(userId)
  }
}
```

## Quota Enforcement

### Quota Check Middleware

```typescript
// src/lib/middleware/quota-check.ts
export async function checkQuota(
  userId: string,
  resource: 'ai_generation' | 'projects' | 'storage'
): Promise<QuotaCheckResult> {
  const limits = await usageTracker.checkUsageLimits(userId)
  const resourceLimit = limits[resource]
  
  if (resourceLimit.exceeded) {
    return {
      allowed: false,
      reason: 'quota_exceeded',
      limit: resourceLimit.limit,
      used: resourceLimit.used,
      upgradeUrl: '/billing?upgrade=true'
    }
  }
  
  // Warn when approaching limit
  if (resourceLimit.percentage >= 80) {
    return {
      allowed: true,
      warning: true,
      message: `You've used ${resourceLimit.percentage.toFixed(0)}% of your ${resource} quota`,
      limit: resourceLimit.limit,
      used: resourceLimit.used
    }
  }
  
  return {
    allowed: true,
    remaining: resourceLimit.limit - resourceLimit.used
  }
}

// Usage in API routes
export async function POST(request: Request) {
  const user = await authenticateUser(request)
  
  // Check quota before AI generation
  const quotaCheck = await checkQuota(user.id, 'ai_generation')
  if (!quotaCheck.allowed) {
    return NextResponse.json({
      error: 'Quota exceeded',
      details: quotaCheck
    }, { status: 402 }) // Payment Required
  }
  
  // Proceed with AI generation
  const result = await generateContent(request)
  
  // Track usage
  await usageTracker.trackUsage(
    user.id,
    'ai_generation',
    result.wordCount
  )
  
  return NextResponse.json(result)
}
```

## Billing UI Components

### Pricing Page

```typescript
// src/components/billing/pricing-page.tsx
export function PricingPage() {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly')
  const { user } = useAuth()
  const { subscription } = useSubscription()
  
  return (
    <div className="pricing-page">
      <BillingToggle
        value={billingPeriod}
        onChange={setBillingPeriod}
      />
      
      <div className="pricing-grid">
        {SUBSCRIPTION_TIERS.map(tier => (
          <PricingCard
            key={tier.id}
            tier={tier}
            billingPeriod={billingPeriod}
            currentTier={subscription?.tier_id}
            onSelect={() => handleTierSelect(tier.id)}
          />
        ))}
      </div>
      
      {user && subscription && (
        <CurrentPlanSection
          subscription={subscription}
          onManage={() => router.push('/billing')}
        />
      )}
    </div>
  )
}
```

### Billing Dashboard

```typescript
// src/app/(dashboard)/billing/page.tsx
export default function BillingPage() {
  const { subscription, usage, invoices } = useBilling()
  
  return (
    <div className="billing-dashboard">
      {/* Current Plan */}
      <SubscriptionCard
        subscription={subscription}
        onUpgrade={() => setShowUpgradeModal(true)}
        onCancel={() => setShowCancelModal(true)}
        onReactivate={() => handleReactivate()}
      />
      
      {/* Usage Overview */}
      <UsageCard
        usage={usage}
        limits={subscription.tier.limits}
        showUpgradePrompt={usage.ai_words > limits.aiWordsPerMonth * 0.8}
      />
      
      {/* Payment Method */}
      <PaymentMethodCard
        paymentMethod={subscription.payment_method}
        onUpdate={() => redirectToStripePortal()}
      />
      
      {/* Billing History */}
      <InvoiceHistory
        invoices={invoices}
        onDownload={(invoiceId) => downloadInvoice(invoiceId)}
      />
      
      {/* Upgrade Modal */}
      <UpgradeModal
        isOpen={showUpgradeModal}
        currentTier={subscription.tier_id}
        onClose={() => setShowUpgradeModal(false)}
        onConfirm={(tierId) => handleUpgrade(tierId)}
      />
    </div>
  )
}
```

### Usage Tracking Display

```typescript
// src/components/billing/usage-card.tsx
export function UsageCard({ usage, limits }: UsageCardProps) {
  const usageMetrics = [
    {
      name: 'AI Words Generated',
      used: usage.ai_words,
      limit: limits.aiWordsPerMonth,
      icon: Wand2,
      color: 'blue'
    },
    {
      name: 'Active Projects',
      used: usage.projects,
      limit: limits.projects,
      icon: FolderOpen,
      color: 'green'
    },
    {
      name: 'Storage Used',
      used: usage.storage_gb,
      limit: limits.storage,
      unit: 'GB',
      icon: HardDrive,
      color: 'purple'
    }
  ]
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Usage This Month</CardTitle>
        <CardDescription>
          Billing period: {formatDate(usage.period_start)} - {formatDate(usage.period_end)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {usageMetrics.map(metric => (
            <UsageMetric
              key={metric.name}
              {...metric}
              percentage={(metric.used / metric.limit) * 100}
              isUnlimited={metric.limit === -1}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
```

## Database Schema

### Subscription Tables

```sql
-- User subscriptions
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  stripe_subscription_id TEXT UNIQUE,
  stripe_customer_id TEXT,
  tier_id TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN (
    'active', 'past_due', 'canceled', 'incomplete', 
    'incomplete_expired', 'trialing', 'paused'
  )),
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT false,
  canceled_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, status) WHERE status = 'active'
);

-- Usage tracking
CREATE TABLE usage_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  ai_words_count INTEGER DEFAULT 0,
  projects_count INTEGER DEFAULT 0,
  storage_bytes BIGINT DEFAULT 0,
  collaborators_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, period_start)
);

-- Payment history
CREATE TABLE payment_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  stripe_payment_intent_id TEXT UNIQUE,
  stripe_invoice_id TEXT,
  amount INTEGER NOT NULL, -- in cents
  currency TEXT DEFAULT 'usd',
  status TEXT NOT NULL,
  description TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscription events log
CREATE TABLE subscription_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  subscription_id UUID REFERENCES user_subscriptions(id),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Security Considerations

### Payment Security

1. **PCI Compliance**
   - Never store credit card details
   - Use Stripe's secure payment elements
   - Implement 3D Secure when required
   - Regular security audits

2. **Webhook Security**
   ```typescript
   // Verify webhook signatures
   const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET
   
   try {
     event = stripe.webhooks.constructEvent(
       request.body,
       sig,
       endpointSecret
     )
   } catch (err) {
     return res.status(400).send(`Webhook Error: ${err.message}`)
   }
   ```

3. **Access Control**
   - Verify user ownership for all billing operations
   - Implement proper RLS policies
   - Audit all payment-related actions
   - Secure API endpoints with authentication

## Best Practices

### Subscription Management

1. **Grace Periods**
   - Allow 3-day grace period for failed payments
   - Send reminders before suspension
   - Preserve user data during grace period
   - Easy reactivation process

2. **Proration Handling**
   ```typescript
   // Calculate proration for upgrades/downgrades
   const proration = calculateProration({
     oldPrice: currentTier.monthlyPrice,
     newPrice: newTier.monthlyPrice,
     daysRemaining: daysUntilPeriodEnd,
     totalDays: 30
   })
   ```

3. **Communication**
   - Clear upgrade/downgrade explanations
   - Transparent pricing display
   - Email notifications for billing events
   - In-app notifications for usage warnings

### Error Handling

```typescript
// Comprehensive error handling for payments
try {
  const result = await processPayment(paymentData)
  return { success: true, result }
} catch (error) {
  if (error.type === 'StripeCardError') {
    return { 
      success: false, 
      error: 'Your card was declined',
      code: error.code 
    }
  } else if (error.type === 'StripeInvalidRequestError') {
    return { 
      success: false, 
      error: 'Invalid payment details' 
    }
  } else {
    logger.error('Payment processing error:', error)
    return { 
      success: false, 
      error: 'Payment processing failed' 
    }
  }
}
```

## Testing Strategies

### Test Card Numbers

```typescript
const TEST_CARDS = {
  success: '4242 4242 4242 4242',
  decline: '4000 0000 0000 0002',
  insufficient_funds: '4000 0000 0000 9995',
  expired: '4000 0000 0000 0069',
  authentication_required: '4000 0025 0000 3155'
}
```

### Webhook Testing

```bash
# Use Stripe CLI for local webhook testing
stripe listen --forward-to localhost:3000/api/billing/webhooks/stripe

# Trigger test events
stripe trigger payment_intent.succeeded
stripe trigger customer.subscription.updated
```

## Analytics & Reporting

### Revenue Metrics

```typescript
interface RevenueMetrics {
  mrr: number // Monthly Recurring Revenue
  arr: number // Annual Recurring Revenue
  churnRate: number
  ltv: number // Lifetime Value
  arpu: number // Average Revenue Per User
  growthRate: number
}

async function calculateRevenueMetrics(): Promise<RevenueMetrics> {
  const activeSubscriptions = await getActiveSubscriptions()
  
  const mrr = activeSubscriptions.reduce((total, sub) => {
    return total + (sub.tier.monthlyPrice / 100)
  }, 0)
  
  const arr = mrr * 12
  const churnRate = await calculateChurnRate()
  const ltv = (arpu / churnRate) || 0
  
  return { mrr, arr, churnRate, ltv, arpu, growthRate }
}
```

## Future Enhancements

### Planned Features

1. **Advanced Billing Options**
   - Team billing with seat management
   - Usage-based pricing tiers
   - Prepaid credits system
   - Multi-currency support
   - Regional pricing

2. **Enhanced Analytics**
   - Cohort analysis
   - Revenue forecasting
   - Churn prediction
   - Usage pattern analysis
   - A/B testing for pricing

3. **Enterprise Features**
   - Invoice-based billing
   - Purchase orders
   - Custom contracts
   - Volume discounts
   - SLA management

4. **Integrations**
   - Accounting software sync
   - CRM integration
   - Tax automation
   - Dunning management
   - Affiliate program