'use client'

import { ReactNode } from 'react'
import { AuthProvider } from '@/contexts/auth-context'
import { CelebrationProvider } from '@/contexts/celebration-context'
import { SettingsProvider } from '@/components/settings/settings-provider'
import { Toaster } from '@/components/ui/toaster'

export function DashboardProvider({ children }: { children: ReactNode }) {
  return (
    <AuthProvider>
      <CelebrationProvider>
        <SettingsProvider>
          {children}
          <Toaster />
        </SettingsProvider>
      </CelebrationProvider>
    </AuthProvider>
  )
}