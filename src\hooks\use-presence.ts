import { useState, useEffect, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { logger } from '@/lib/services/logger'
import type { RealtimeChannel } from '@supabase/supabase-js'

export interface PresenceUser {
  user_id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: string
  status: 'online' | 'away' | 'busy' | 'offline'
  last_seen: string
  current_page: string | null
  current_section: string | null
  is_writing: boolean
  is_idle: boolean
  cursor_position: { line: number; column: number } | null
  selection_range: { 
    start: { line: number; column: number }
    end: { line: number; column: number }
  } | null
}

export interface PresenceUpdate {
  status?: 'online' | 'away' | 'busy' | 'offline'
  currentPage?: string
  currentSection?: string
  isWriting?: boolean
  chapterId?: string
  cursorPosition?: { line: number; column: number }
  selectionRange?: {
    start: { line: number; column: number }
    end: { line: number; column: number }
  }
  viewport?: {
    top: number
    bottom: number
    scrollTop: number
  }
}

export interface UsePresenceOptions {
  projectId: string
  enabled?: boolean
  updateInterval?: number
}

export interface UsePresenceReturn {
  users: PresenceUser[]
  isLoading: boolean
  error: string | null
  updatePresence: (update: PresenceUpdate) => Promise<void>
  setOffline: () => Promise<void>
  currentUser: PresenceUser | null
  onlineCount: number
  writingUsers: PresenceUser[]
}

export function usePresence({
  projectId,
  enabled = true,
  updateInterval = 30000 // 30 seconds
}: UsePresenceOptions): UsePresenceReturn {
  const { user } = useAuth()
  const [users, setUsers] = useState<PresenceUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createClient()
  const channelRef = useRef<RealtimeChannel | null>(null)
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const sessionIdRef = useRef<string>(Math.random().toString(36))

  const fetchPresence = useCallback(async () => {
    if (!user || !enabled) return

    try {
      const response = await fetch(`/api/collaboration/presence?projectId=${projectId}`)
      if (!response.ok) throw new Error('Failed to fetch presence')
      
      const data = await response.json()
      setUsers(data.data.presence || [])
      setError(null)
    } catch (err) {
      logger.error('Error fetching presence', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch presence')
    } finally {
      setIsLoading(false)
    }
  }, [user, enabled, projectId])

  const updatePresence = useCallback(async (update: PresenceUpdate) => {
    if (!user || !enabled) return

    try {
      const response = await fetch('/api/collaboration/presence', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          sessionId: sessionIdRef.current,
          ...update
        })
      })

      if (!response.ok) throw new Error('Failed to update presence')
      
      // Refresh presence data after update
      await fetchPresence()
    } catch (err) {
      logger.error('Error updating presence', err)
      setError(err instanceof Error ? err.message : 'Failed to update presence')
    }
  }, [user, enabled, projectId, fetchPresence])

  const setOffline = useCallback(async () => {
    if (!user) return

    try {
      await fetch(`/api/collaboration/presence?projectId=${projectId}`, {
        method: 'DELETE'
      })
    } catch (err) {
      logger.error('Error setting offline', err)
    }
  }, [user, projectId])

  // Initialize presence when user enters
  useEffect(() => {
    if (!user || !enabled) return

    const initializePresence = async () => {
      await updatePresence({ 
        status: 'online',
        currentPage: window.location.pathname 
      })
    }

    initializePresence()
  }, [user, enabled, updatePresence])

  // Set up realtime subscription
  useEffect(() => {
    if (!user || !enabled) return

    const channel = supabase
      .channel(`presence:${projectId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence',
          filter: `project_id=eq.${projectId}`
        },
        (payload) => {
          logger.debug('Presence change detected', payload)
          fetchPresence()
        }
      )
      .subscribe()

    channelRef.current = channel

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current)
        channelRef.current = null
      }
    }
  }, [user, enabled, projectId, supabase, fetchPresence])

  // Periodic presence updates
  useEffect(() => {
    if (!user || !enabled) return

    const scheduleUpdate = () => {
      updateTimeoutRef.current = setTimeout(async () => {
        await updatePresence({ status: 'online' })
        scheduleUpdate()
      }, updateInterval)
    }

    scheduleUpdate()

    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
      }
    }
  }, [user, enabled, updateInterval, updatePresence])

  // Handle page visibility changes
  useEffect(() => {
    if (!user || !enabled) return

    const handleVisibilityChange = () => {
      if (document.hidden) {
        updatePresence({ status: 'away' })
      } else {
        updatePresence({ status: 'online' })
      }
    }

    const handleBeforeUnload = () => {
      setOffline()
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [user, enabled, updatePresence, setOffline])

  // Initial fetch
  useEffect(() => {
    fetchPresence()
  }, [fetchPresence])

  const currentUser = users.find(u => u.user_id === user?.id) || null
  const onlineCount = users.filter(u => u.status === 'online').length
  const writingUsers = users.filter(u => u.is_writing && u.status === 'online')

  return {
    users,
    isLoading,
    error,
    updatePresence,
    setOffline,
    currentUser,
    onlineCount,
    writingUsers
  }
}