'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Globe, 
  Layers, 
  BookOpen, 
  Users, 
  Map as MapIcon,
  ZoomIn,
  ZoomOut,
  Maximize2
} from 'lucide-react'
import Link from 'next/link'
import type { Universe } from '@/lib/db/types'

interface UniverseMapProps {
  universes: Array<Universe & {
    series?: Array<{
      id: string
      title: string
      projects?: Array<{
        project: {
          id: string
          title: string
        }
      }>
    }>
  }>
}

interface Node {
  id: string
  type: 'universe' | 'series' | 'book'
  label: string
  x: number
  y: number
  universe?: Universe
  series?: any
  book?: any
}

interface Edge {
  source: string
  target: string
  type: 'contains' | 'connected'
}

export function UniverseMap({ universes }: UniverseMapProps) {
  const [zoom, setZoom] = useState(1)
  const [nodes, setNodes] = useState<Node[]>([])
  const [edges, setEdges] = useState<Edge[]>([])
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)

  useEffect(() => {
    generateMapData()
  }, [universes])

  const generateMapData = () => {
    const newNodes: Node[] = []
    const newEdges: Edge[] = []
    
    // Create universe nodes
    universes.forEach((universe, uIndex) => {
      const universeNode: Node = {
        id: `universe-${universe.id}`,
        type: 'universe',
        label: universe.name,
        x: 400 + (uIndex % 3) * 400,
        y: 200 + Math.floor(uIndex / 3) * 400,
        universe
      }
      newNodes.push(universeNode)
      
      // Create series nodes
      universe.series?.forEach((series, sIndex) => {
        const seriesNode: Node = {
          id: `series-${series.id}`,
          type: 'series',
          label: series.title,
          x: universeNode.x - 150 + (sIndex % 3) * 150,
          y: universeNode.y + 150 + Math.floor(sIndex / 3) * 100,
          series
        }
        newNodes.push(seriesNode)
        
        // Connect universe to series
        newEdges.push({
          source: universeNode.id,
          target: seriesNode.id,
          type: 'contains'
        })
        
        // Create book nodes
        series.projects?.forEach((project, pIndex) => {
          const bookNode: Node = {
            id: `book-${project.project.id}`,
            type: 'book',
            label: project.project.title,
            x: seriesNode.x - 50 + (pIndex % 2) * 100,
            y: seriesNode.y + 100,
            book: project.project
          }
          newNodes.push(bookNode)
          
          // Connect series to book
          newEdges.push({
            source: seriesNode.id,
            target: bookNode.id,
            type: 'contains'
          })
        })
      })
    })
    
    setNodes(newNodes)
    setEdges(newEdges)
  }

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 2))
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.5))
  const handleResetZoom = () => setZoom(1)

  const getNodeColor = (type: string) => {
    switch (type) {
      case 'universe': return '#6366f1'
      case 'series': return '#ec4899'
      case 'book': return '#10b981'
      default: return '#6b7280'
    }
  }

  const getNodeSize = (type: string) => {
    switch (type) {
      case 'universe': return 60
      case 'series': return 40
      case 'book': return 30
      default: return 30
    }
  }

  return (
    <div className="space-y-4">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={handleZoomOut}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={handleZoomIn}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={handleResetZoom}>
            <Maximize2 className="h-4 w-4" />
          </Button>
          <span className="text-sm text-muted-foreground ml-2">
            Zoom: {Math.round(zoom * 100)}%
          </span>
        </div>
        
        <div className="flex items-center gap-4 sm:gap-5 lg:gap-6 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full" style={{ backgroundColor: getNodeColor('universe') }} />
            <span>Universe</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full" style={{ backgroundColor: getNodeColor('series') }} />
            <span>Series</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full" style={{ backgroundColor: getNodeColor('book') }} />
            <span>Book</span>
          </div>
        </div>
      </div>

      {/* Map View */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
        <div className="lg:col-span-3">
          <Card className="relative overflow-hidden">
            <CardContent className="p-0">
              <ScrollArea className="h-[600px]">
                <div 
                  className="relative"
                  style={{ 
                    width: '2000px', 
                    height: '2000px',
                    transform: `scale(${zoom})`,
                    transformOrigin: 'top left'
                  }}
                >
                  {/* SVG for edges */}
                  <svg 
                    className="absolute inset-0 pointer-events-none"
                    width="2000"
                    height="2000"
                  >
                    {edges.map((edge, idx) => {
                      const sourceNode = nodes.find(n => n.id === edge.source)
                      const targetNode = nodes.find(n => n.id === edge.target)
                      if (!sourceNode || !targetNode) return null
                      
                      return (
                        <line
                          key={idx}
                          x1={sourceNode.x}
                          y1={sourceNode.y}
                          x2={targetNode.x}
                          y2={targetNode.y}
                          stroke="#e5e7eb"
                          strokeWidth="2"
                          strokeDasharray={edge.type === 'connected' ? '5,5' : '0'}
                        />
                      )
                    })}
                  </svg>
                  
                  {/* Nodes */}
                  {nodes.map(node => {
                    const size = getNodeSize(node.type)
                    const color = getNodeColor(node.type)
                    
                    return (
                      <div
                        key={node.id}
                        className="absolute cursor-pointer transition-all hover:scale-110"
                        style={{
                          left: node.x - size / 2,
                          top: node.y - size / 2,
                          width: size,
                          height: size
                        }}
                        onClick={() => setSelectedNode(node)}
                      >
                        <div
                          className="w-full h-full rounded-full flex items-center justify-center text-white font-medium shadow-lg"
                          style={{ backgroundColor: color }}
                        >
                          {node.type === 'universe' && <Globe className="h-6 w-6" />}
                          {node.type === 'series' && <Layers className="h-5 w-5" />}
                          {node.type === 'book' && <BookOpen className="h-4 w-4" />}
                        </div>
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 whitespace-nowrap">
                          <Badge 
                            variant="secondary" 
                            className="text-xs shadow-sm"
                          >
                            {node.label}
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Selected Node Details */}
        <div>
          {selectedNode ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  {selectedNode.type === 'universe' && <Globe className="h-5 w-5" />}
                  {selectedNode.type === 'series' && <Layers className="h-5 w-5" />}
                  {selectedNode.type === 'book' && <BookOpen className="h-5 w-5" />}
                  {selectedNode.label}
                </CardTitle>
                <CardDescription>
                  {selectedNode.type.charAt(0).toUpperCase() + selectedNode.type.slice(1)}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedNode.type === 'universe' && selectedNode.universe && (
                  <>
                    <p className="text-sm text-muted-foreground">
                      {selectedNode.universe.description || 'No description'}
                    </p>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Series</span>
                        <span className="font-medium">{selectedNode.universe.series?.length || 0}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Total Books</span>
                        <span className="font-medium">
                          {selectedNode.universe.series?.reduce(
                            (sum, s) => sum + (s.projects?.length || 0), 0
                          ) || 0}
                        </span>
                      </div>
                    </div>
                    <Button size="sm" className="w-full" asChild>
                      <Link href={`/universes/${selectedNode.universe.id}`}>
                        View Universe
                      </Link>
                    </Button>
                  </>
                )}
                
                {selectedNode.type === 'series' && selectedNode.series && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Books</span>
                        <span className="font-medium">{selectedNode.series.projects?.length || 0}</span>
                      </div>
                    </div>
                    <Button size="sm" className="w-full" asChild>
                      <Link href={`/series/${selectedNode.series.id}`}>
                        View Series
                      </Link>
                    </Button>
                  </>
                )}
                
                {selectedNode.type === 'book' && selectedNode.book && (
                  <>
                    <div className="space-y-2">
                      <Badge variant="outline" className="text-xs">
                        {selectedNode.book.status}
                      </Badge>
                    </div>
                    <Button size="sm" className="w-full" asChild>
                      <Link href={`/projects/${selectedNode.book.id}`}>
                        View Book
                      </Link>
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Select a Node</CardTitle>
                <CardDescription>
                  Click on any node in the map to view details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-6 sm:py-8 lg:py-10 text-muted-foreground">
                  <MapIcon className="h-12 w-12 mx-auto mb-4 opacity-20" />
                  <p className="text-sm">
                    Explore your literary universes by clicking on the nodes
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}