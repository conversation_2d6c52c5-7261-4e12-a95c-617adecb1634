'use client'

import { useState, useEffect } from 'react'
import { useSelectiveSubscriptions } from '@/hooks/use-selective-subscriptions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { 
  Wifi, 
  WifiOff, 
  Activity, 
  Settings, 
  RefreshCw, 
  AlertTriangle,
  TrendingUp,
  Clock,
  Users
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface SubscriptionMonitorProps {
  projectId?: string
  chapterId?: string
  variant?: 'minimal' | 'detailed' | 'debug'
}

export function SubscriptionMonitor({ 
  projectId, 
  chapterId, 
  variant = 'minimal' 
}: SubscriptionMonitorProps) {
  const {
    isConnected,
    activeSubscriptions,
    metrics,
    errors,
    getMetrics,
    updatePriority,
    cleanup
  } = useSelectiveSubscriptions({
    projectId,
    chapterId,
    enablePresence: true,
    enableCollaboration: true,
    enableSessions: true
  })

  const [refreshedMetrics, setRefreshedMetrics] = useState(metrics)

  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshedMetrics(getMetrics())
    }, 2000) // Update every 2 seconds

    return () => clearInterval(interval)
  }, [getMetrics])

  const totalMessages = refreshedMetrics.reduce((sum, metric) => sum + metric.messageCount, 0)
  const totalErrors = refreshedMetrics.reduce((sum, metric) => sum + metric.errors, 0)
  const avgLatency = refreshedMetrics.length > 0 
    ? refreshedMetrics.reduce((sum, metric) => sum + metric.latency, 0) / refreshedMetrics.length 
    : 0

  const handlePriorityChange = (subscriptionId: string, priority: 'high' | 'medium' | 'low') => {
    updatePriority(subscriptionId, priority)
  }

  const handleCleanup = async () => {
    await cleanup()
  }

  // Minimal variant - just connection status
  if (variant === 'minimal') {
    return (
      <div className="flex items-center gap-2">
        {isConnected ? (
          <div className="flex items-center gap-1 text-green-600">
            <Wifi className="h-3 w-3" />
            <span className="text-xs">Live</span>
          </div>
        ) : (
          <div className="flex items-center gap-1 text-red-600">
            <WifiOff className="h-3 w-3" />
            <span className="text-xs">Offline</span>
          </div>
        )}
        
        {activeSubscriptions.length > 0 && (
          <Badge variant="outline" className="text-xs">
            {activeSubscriptions.length}
          </Badge>
        )}

        {totalErrors > 0 && (
          <Badge variant="destructive" className="text-xs">
            {totalErrors} errors
          </Badge>
        )}
      </div>
    )
  }

  // Detailed variant - comprehensive dashboard
  if (variant === 'detailed') {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Real-time Connections</CardTitle>
            <div className="flex items-center gap-2">
              {isConnected ? (
                <Badge variant="outline" className="text-green-600">
                  <Wifi className="h-3 w-3 mr-1" />
                  Connected
                </Badge>
              ) : (
                <Badge variant="destructive">
                  <WifiOff className="h-3 w-3 mr-1" />
                  Disconnected
                </Badge>
              )}
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setRefreshedMetrics(getMetrics())}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Metrics
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleCleanup} className="text-red-600">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Cleanup All
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <CardDescription>
            {activeSubscriptions.length} active subscription{activeSubscriptions.length !== 1 ? 's' : ''}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Connection Statistics */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-sm font-medium">
                <Activity className="h-4 w-4" />
                {totalMessages}
              </div>
              <p className="text-xs text-muted-foreground">Messages</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-sm font-medium">
                <TrendingUp className="h-4 w-4" />
                {avgLatency.toFixed(0)}ms
              </div>
              <p className="text-xs text-muted-foreground">Avg Latency</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-sm font-medium">
                <AlertTriangle className="h-4 w-4" />
                {totalErrors}
              </div>
              <p className="text-xs text-muted-foreground">Errors</p>
            </div>
          </div>

          {/* Error Messages */}
          {errors.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-red-600">Recent Errors</h4>
              {errors.slice(-3).map((error, index) => (
                <div key={index} className="text-xs text-red-600 bg-red-50 p-2 rounded">
                  {error}
                </div>
              ))}
            </div>
          )}

          {/* Active Subscriptions */}
          {refreshedMetrics.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium">Active Subscriptions</h4>
              {refreshedMetrics.map((metric) => (
                <div key={metric.subscriptionId} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex-1">
                    <div className="text-xs font-medium">
                      {metric.subscriptionId.replace(/-\w{8}$/, '')}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {metric.messageCount} msg • {metric.errors} errors • {metric.retries} retries
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(metric.lastActivity, { addSuffix: true })}
                    </span>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Settings className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handlePriorityChange(metric.subscriptionId, 'high')}>
                          Set High Priority
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handlePriorityChange(metric.subscriptionId, 'medium')}>
                          Set Medium Priority
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handlePriorityChange(metric.subscriptionId, 'low')}>
                          Set Low Priority
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  // Debug variant - raw metrics display
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-sm">Subscription Debug Info</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="text-xs font-medium mb-2">Raw Metrics</h4>
            <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(refreshedMetrics, null, 2)}
            </pre>
          </div>
          
          <div>
            <h4 className="text-xs font-medium mb-2">Active Subscriptions</h4>
            <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-20">
              {JSON.stringify(activeSubscriptions, null, 2)}
            </pre>
          </div>
          
          {errors.length > 0 && (
            <div>
              <h4 className="text-xs font-medium mb-2">Errors</h4>
              <pre className="text-xs bg-red-50 p-2 rounded overflow-auto max-h-20">
                {JSON.stringify(errors, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}