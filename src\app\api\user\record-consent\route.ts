import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'

const consentRecordSchema = z.object({
  consents: z.record(z.string(), z.boolean())
})

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const body = await request.json()
    
    // Validate input
    const validatedData = consentRecordSchema.parse(body)
    const supabase = await createTypedServerClient()

    // Create consent records
    const consentRecords = Object.entries(validatedData.consents).map(([type, granted]) => ({
      user_id: user.id,
      consent_type: type,
      granted,
      granted_at: new Date().toISOString(),
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined
    }))

    // Insert consent records
    const { error } = await supabase
      .from('user_consent_history')
      .insert(consentRecords)

    if (error) {
      throw error
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error recording consent:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid consent data', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to record consent' },
      { status: 500 }
    )
  }
})