import * as React from "react"
import { cn } from "@/lib/utils"
import { SR_ONLY } from "@/lib/utils/accessibility-utils"

export interface AccessibleIconProps {
  label: string
  children: React.ReactNode
  className?: string
}

/**
 * Accessible icon component that provides screen reader text
 * Use this wrapper when icons convey meaning without accompanying text
 */
export function AccessibleIcon({ label, children, className }: AccessibleIconProps) {
  return (
    <span className={cn("inline-flex items-center", className)}>
      <span aria-hidden="true">{children}</span>
      <span className={SR_ONLY}>{label}</span>
    </span>
  )
}

export interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  label: string
  icon: React.ReactNode
}

/**
 * Accessible icon button component
 * Ensures icon-only buttons have proper labels for screen readers
 */
export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ label, icon, className, ...props }, ref) => {
    return (
      <button
        ref={ref}
        aria-label={label}
        className={cn(
          "inline-flex items-center justify-center",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          className
        )}
        {...props}
      >
        <span aria-hidden="true">{icon}</span>
      </button>
    )
  }
)
IconButton.displayName = "IconButton"