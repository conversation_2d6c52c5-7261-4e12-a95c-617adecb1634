/**
 * Development Configuration
 * Settings and constants for development environment
 */

export const DEVELOPMENT_CONFIG = {
  // Mock user for development bypass
  MOCK_USER: {
    ID: 'dev-user-123',
    EMAIL: '<EMAIL>',
    NAME: 'Development User',
  },
  
  // Feature flags
  FEATURES: {
    AUTH_BYPASS: false, // Disabled by default for security
    DEMO_MODE: process.env.NEXT_PUBLIC_DEMO_MODE === 'true',
    MOCK_AI_RESPONSES: process.env.NEXT_PUBLIC_MOCK_AI === 'true',
    DISABLE_RATE_LIMITS: process.env.NODE_ENV === 'development',
  },
  
  // Development tools
  TOOLS: {
    SHOW_DEBUG_INFO: process.env.NODE_ENV === 'development',
    ENABLE_CONSOLE_LOGS: process.env.NODE_ENV === 'development',
    SHOW_PERFORMANCE_METRICS: process.env.NEXT_PUBLIC_SHOW_PERF === 'true',
  },
  
  // Test data
  TEST_ACCOUNTS: {
    ADMIN: {
      EMAIL: '<EMAIL>',
      PASSWORD: 'test123456',
    },
    USER: {
      EMAIL: '<EMAIL>',
      PASSWORD: 'test123456',
    },
    COLLABORATOR: {
      EMAIL: '<EMAIL>',
      PASSWORD: 'test123456',
    },
  },
  
  // API mocking
  MOCK_DELAYS: {
    MIN: 300, // ms
    MAX: 1500, // ms
    TYPICAL: 800, // ms
  },
  
  // Error simulation
  ERROR_SIMULATION: {
    ENABLED: false,
    RATE: 0.1, // 10% chance of error
    TYPES: ['network', 'auth', 'validation', 'server'],
  },
} as const;

// Helper functions
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

export function isDemo(): boolean {
  return DEVELOPMENT_CONFIG.FEATURES.DEMO_MODE;
}

export function shouldBypassAuth(): boolean {
  return isDevelopment() && DEVELOPMENT_CONFIG.FEATURES.AUTH_BYPASS;
}

export function getMockDelay(): number {
  const { MIN, MAX } = DEVELOPMENT_CONFIG.MOCK_DELAYS;
  return Math.floor(Math.random() * (MAX - MIN) + MIN);
}