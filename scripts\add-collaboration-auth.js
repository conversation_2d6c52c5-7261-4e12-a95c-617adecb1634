#!/usr/bin/env node
/**
 * <PERSON><PERSON><PERSON> to add authorization checks to all collaboration endpoints
 * This script updates the collaboration API routes to include proper permission checks
 */

const fs = require('fs').promises;
const path = require('path');

const COLLABORATION_ENDPOINTS = [
  '/src/app/api/collaboration/cursor/route.ts',
  '/src/app/api/collaboration/join/route.ts',
  '/src/app/api/collaboration/leave/route.ts',
  '/src/app/api/collaboration/lock/route.ts',
  '/src/app/api/collaboration/unlock/route.ts',
];

const IMPORT_STATEMENTS = `import { checkProjectAccess } from '@/lib/auth/collaboration-auth'
import { createClient } from '@/lib/supabase/server'`;

const AUTHORIZATION_CHECK = `
    // Get project ID from session to check authorization
    const supabase = await createClient()
    const { data: session } = await supabase
      .from('collaboration_sessions')
      .select('project_id')
      .eq('id', sessionId)
      .single()

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      )
    }

    // Check if user has appropriate access to the project
    const permissions = await checkProjectAccess(authResult.user.id, session.project_id)
    if (!permissions.canView) {
      return NextResponse.json(
        { error: 'You do not have permission to access this project' },
        { status: 403 }
      )
    }`;

const EDIT_AUTHORIZATION_CHECK = `
    // Get project ID from session to check authorization
    const supabase = await createClient()
    const { data: session } = await supabase
      .from('collaboration_sessions')
      .select('project_id')
      .eq('id', sessionId)
      .single()

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      )
    }

    // Check if user has edit access to the project
    const permissions = await checkProjectAccess(authResult.user.id, session.project_id)
    if (!permissions.canEdit) {
      return NextResponse.json(
        { error: 'You do not have permission to make changes to this project' },
        { status: 403 }
      )
    }`;

async function updateEndpoint(filePath, requiresEditAccess = false) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    let content = await fs.readFile(fullPath, 'utf8');
    
    // Check if already has authorization
    if (content.includes('checkProjectAccess')) {
      console.log(`✓ ${filePath} already has authorization checks`);
      return;
    }
    
    // Add imports if not present
    if (!content.includes('checkProjectAccess')) {
      // Find the last import statement
      const importRegex = /^import .+ from .+$/gm;
      let lastImportMatch;
      let match;
      while ((match = importRegex.exec(content)) !== null) {
        lastImportMatch = match;
      }
      
      if (lastImportMatch) {
        const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
        content = content.slice(0, insertPosition) + '\n' + IMPORT_STATEMENTS + content.slice(insertPosition);
      }
    }
    
    // Add authorization check after getting sessionId
    const sessionIdPattern = /const\s+{\s*sessionId[^}]*}\s*=\s*body/;
    const sessionIdMatch = content.match(sessionIdPattern);
    
    if (sessionIdMatch) {
      // Find the next validation or business logic
      const afterSessionId = content.indexOf(sessionIdMatch[0]) + sessionIdMatch[0].length;
      const nextSection = content.indexOf('\n\n', afterSessionId);
      
      if (nextSection !== -1) {
        const authCheck = requiresEditAccess ? EDIT_AUTHORIZATION_CHECK : AUTHORIZATION_CHECK;
        content = content.slice(0, nextSection) + authCheck + content.slice(nextSection);
      }
    }
    
    await fs.writeFile(fullPath, content);
    console.log(`✅ Updated ${filePath} with authorization checks`);
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
  }
}

async function main() {
  console.log('Adding authorization checks to collaboration endpoints...\n');
  
  // Update endpoints that need edit access
  await updateEndpoint('/src/app/api/collaboration/cursor/route.ts', false);
  await updateEndpoint('/src/app/api/collaboration/lock/route.ts', true);
  await updateEndpoint('/src/app/api/collaboration/unlock/route.ts', true);
  
  // Update endpoints that need view access
  await updateEndpoint('/src/app/api/collaboration/join/route.ts', false);
  await updateEndpoint('/src/app/api/collaboration/leave/route.ts', false);
  
  console.log('\n✨ Authorization updates complete!');
}

main().catch(console.error);