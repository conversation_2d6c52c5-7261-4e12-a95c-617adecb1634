'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Settings, 
  Save, 
  RotateCcw,
  Info,
  Zap,
  Archive,
  Clock
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CompressionSettings {
  autoCompress: boolean
  compressionLevel: 'low' | 'medium' | 'high' | 'aggressive'
  compressionThreshold: number // Token count threshold
  autoArchiveAge: number // Days
  preserveRecentChapters: number
  enableSmartPruning: boolean
  pruningImportanceThreshold: number // 0-100
}

interface ContextCompressionSettingsProps {
  projectId: string
  onSettingsChange?: () => void
}

const DEFAULT_SETTINGS: CompressionSettings = {
  autoCompress: true,
  compressionLevel: 'medium',
  compressionThreshold: 100000,
  autoArchiveAge: 30,
  preserveRecentChapters: 5,
  enableSmartPruning: false,
  pruningImportanceThreshold: 30
}

export function ContextCompressionSettings({ 
  projectId, 
  onSettingsChange 
}: ContextCompressionSettingsProps) {
  const [settings, setSettings] = useState<CompressionSettings>(DEFAULT_SETTINGS)
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const { toast } = useToast()

  // Load settings
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/memory/settings?projectId=${projectId}`)
        if (response.ok) {
          const data = await response.json()
          if (data.settings) {
            setSettings(data.settings)
          }
        }
      } catch (error) {
        console.error('Failed to load compression settings:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [projectId])

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/memory/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId, settings })
      })

      if (!response.ok) {
        throw new Error('Failed to save settings')
      }

      toast({
        title: 'Settings saved',
        description: 'Memory compression settings have been updated'
      })

      setHasChanges(false)
      onSettingsChange?.()
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = () => {
    setSettings(DEFAULT_SETTINGS)
    setHasChanges(true)
  }

  const updateSetting = <K extends keyof CompressionSettings>(
    key: K,
    value: CompressionSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    setHasChanges(true)
  }

  const getCompressionLevelDescription = (level: string) => {
    switch (level) {
      case 'low':
        return 'Minimal compression, preserves most context details'
      case 'medium':
        return 'Balanced compression, good for most projects'
      case 'high':
        return 'Aggressive compression, may lose some nuance'
      case 'aggressive':
        return 'Maximum compression, only essential information retained'
      default:
        return ''
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <Settings className="h-8 w-8 mx-auto mb-2 animate-spin" />
            <p>Loading settings...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Automatic Compression</CardTitle>
          <CardDescription>
            Configure how BookScribe automatically manages memory to stay within token limits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Auto Compression Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-compress">Enable Auto-Compression</Label>
              <p className="text-sm text-muted-foreground">
                Automatically compress memory when approaching limits
              </p>
            </div>
            <Switch
              id="auto-compress"
              checked={settings.autoCompress}
              onCheckedChange={(checked) => updateSetting('autoCompress', checked)}
            />
          </div>

          {/* Compression Level */}
          <div className="space-y-2">
            <Label htmlFor="compression-level">Compression Level</Label>
            <Select
              value={settings.compressionLevel}
              onValueChange={(value) => updateSetting('compressionLevel', value as CompressionSettings['compressionLevel'])}
            >
              <SelectTrigger id="compression-level">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-green-600">Low</Badge>
                    <span>Minimal</span>
                  </div>
                </SelectItem>
                <SelectItem value="medium">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-yellow-600">Medium</Badge>
                    <span>Balanced</span>
                  </div>
                </SelectItem>
                <SelectItem value="high">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-orange-600">High</Badge>
                    <span>Aggressive</span>
                  </div>
                </SelectItem>
                <SelectItem value="aggressive">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-red-600">Max</Badge>
                    <span>Maximum</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              {getCompressionLevelDescription(settings.compressionLevel)}
            </p>
          </div>

          {/* Compression Threshold */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="compression-threshold">Compression Threshold</Label>
              <span className="text-sm font-medium">
                {settings.compressionThreshold.toLocaleString()} tokens
              </span>
            </div>
            <Slider
              id="compression-threshold"
              min={50000}
              max={200000}
              step={10000}
              value={[settings.compressionThreshold]}
              onValueChange={([value]) => updateSetting('compressionThreshold', value)}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Start compression when total tokens exceed this threshold
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Archive Settings</CardTitle>
          <CardDescription>
            Automatically archive old content to reduce active memory usage
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Auto Archive Age */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="archive-age">Auto-Archive After</Label>
              <span className="text-sm font-medium">
                {settings.autoArchiveAge} days
              </span>
            </div>
            <Slider
              id="archive-age"
              min={7}
              max={90}
              step={1}
              value={[settings.autoArchiveAge]}
              onValueChange={([value]) => updateSetting('autoArchiveAge', value)}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Archive chapters not accessed for this many days
            </p>
          </div>

          {/* Preserve Recent Chapters */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="preserve-chapters">Keep Recent Chapters</Label>
              <span className="text-sm font-medium">
                {settings.preserveRecentChapters} chapters
              </span>
            </div>
            <Slider
              id="preserve-chapters"
              min={1}
              max={20}
              step={1}
              value={[settings.preserveRecentChapters]}
              onValueChange={([value]) => updateSetting('preserveRecentChapters', value)}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Always keep this many recent chapters uncompressed
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Smart Pruning</CardTitle>
          <CardDescription>
            Intelligently remove low-importance content to save memory
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable Smart Pruning */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="smart-pruning">Enable Smart Pruning</Label>
              <p className="text-sm text-muted-foreground">
                Remove low-importance content automatically
              </p>
            </div>
            <Switch
              id="smart-pruning"
              checked={settings.enableSmartPruning}
              onCheckedChange={(checked) => updateSetting('enableSmartPruning', checked)}
            />
          </div>

          {settings.enableSmartPruning && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="pruning-threshold">Importance Threshold</Label>
                <span className="text-sm font-medium">
                  {settings.pruningImportanceThreshold}%
                </span>
              </div>
              <Slider
                id="pruning-threshold"
                min={10}
                max={50}
                step={5}
                value={[settings.pruningImportanceThreshold]}
                onValueChange={([value]) => updateSetting('pruningImportanceThreshold', value)}
                className="w-full"
              />
              <p className="text-sm text-muted-foreground">
                Remove content with importance score below this threshold
              </p>
              
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Smart pruning permanently removes content. Use with caution.
                </AlertDescription>
              </Alert>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleReset}
          disabled={isSaving}
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setHasChanges(false)}
            disabled={!hasChanges || isSaving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {/* Information Card */}
      <Card className="bg-muted/50">
        <CardContent className="pt-6">
          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <Zap className="h-4 w-4 mt-0.5 text-yellow-600" />
              <p>
                <strong>Compression</strong> reduces token usage by summarizing content while preserving key information
              </p>
            </div>
            <div className="flex items-start gap-2">
              <Archive className="h-4 w-4 mt-0.5 text-blue-600" />
              <p>
                <strong>Archiving</strong> moves old content to cold storage, freeing up active memory
              </p>
            </div>
            <div className="flex items-start gap-2">
              <Clock className="h-4 w-4 mt-0.5 text-green-600" />
              <p>
                <strong>Recent chapters</strong> are always kept in full detail for immediate access
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}