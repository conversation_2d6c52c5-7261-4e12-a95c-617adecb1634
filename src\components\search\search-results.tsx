'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  FileText, 
  Users, 
  MapPin, 
  BookOpen, 
  Hash,
  Clock,
  ChevronRight,
  Eye
} from 'lucide-react'
import type { SearchResult } from './content-search-interface'
import { cn } from '@/lib/utils'

interface SearchResultsProps {
  results: SearchResult[]
  isLoading?: boolean
  onResultClick?: (result: SearchResult) => void
  onLoadMore?: () => void
  hasMore?: boolean
  className?: string
  viewMode?: 'compact' | 'detailed'
}

export function SearchResults({
  results,
  isLoading = false,
  onResultClick,
  onLoadMore,
  hasMore = false,
  className,
  viewMode = 'detailed'
}: SearchResultsProps) {
  const [expandedResults, setExpandedResults] = useState<Set<string>>(new Set())

  const toggleExpanded = (resultId: string) => {
    const newExpanded = new Set(expandedResults)
    if (newExpanded.has(resultId)) {
      newExpanded.delete(resultId)
    } else {
      newExpanded.add(resultId)
    }
    setExpandedResults(newExpanded)
  }

  const getResultIcon = (type: SearchResult['type']) => {
    const iconMap = {
      chapter: FileText,
      character: Users,
      location: MapPin,
      story_bible: BookOpen,
      note: Hash
    }
    return iconMap[type] || FileText
  }

  const getResultColor = (type: SearchResult['type']) => {
    const colorMap = {
      chapter: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      character: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      location: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      story_bible: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
      note: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
    }
    return colorMap[type] || 'bg-gray-100 text-gray-800'
  }

  const renderCompactResult = (result: SearchResult) => {
    const Icon = getResultIcon(result.type)
    
    return (
      <button
        key={result.id}
        className="w-full text-left p-3 hover:bg-accent rounded-lg transition-colors group"
        onClick={() => onResultClick?.(result)}
      >
        <div className="flex items-center gap-3">
          <Icon className="w-4 h-4 text-muted-foreground flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium truncate group-hover:text-primary transition-colors">
              {result.title}
            </h4>
          </div>
          <Badge 
            variant="outline" 
            className={cn("capitalize flex-shrink-0", getResultColor(result.type))}
          >
            {result.type.replace('_', ' ')}
          </Badge>
        </div>
      </button>
    )
  }

  const renderDetailedResult = (result: SearchResult) => {
    const Icon = getResultIcon(result.type)
    const isExpanded = expandedResults.has(result.id)
    
    return (
      <Card key={result.id} className="overflow-hidden">
        <CardContent className="p-0">
          <button
            className="w-full text-left p-4 hover:bg-accent transition-colors"
            onClick={() => onResultClick?.(result)}
          >
            <div className="space-y-3">
              {/* Header */}
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  <Icon className="w-5 h-5 text-muted-foreground" />
                </div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-start justify-between gap-2">
                    <h4 className="font-medium line-clamp-1">
                      {result.title}
                    </h4>
                    <Badge 
                      variant="outline" 
                      className={cn("capitalize flex-shrink-0", getResultColor(result.type))}
                    >
                      {result.type.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  {/* Excerpt */}
                  <p className={cn(
                    "text-sm text-muted-foreground",
                    isExpanded ? "" : "line-clamp-2"
                  )}>
                    {result.excerpt}
                  </p>
                  
                  {/* Highlights */}
                  {result.highlights.length > 0 && (
                    <div className={cn(
                      "space-y-1 mt-2",
                      !isExpanded && "hidden"
                    )}>
                      {result.highlights.map((highlight, idx) => (
                        <div
                          key={idx}
                          className="text-sm p-2 bg-muted/50 rounded"
                          dangerouslySetInnerHTML={{ __html: highlight }}
                        />
                      ))}
                    </div>
                  )}
                  
                  {/* Metadata */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground pt-2">
                    {result.metadata.chapterNumber && (
                      <span>Chapter {result.metadata.chapterNumber}</span>
                    )}
                    {result.metadata.characterRole && (
                      <span className="capitalize">{result.metadata.characterRole}</span>
                    )}
                    {result.metadata.locationType && (
                      <span className="capitalize">{result.metadata.locationType}</span>
                    )}
                    {result.metadata.wordCount && (
                      <span>{result.metadata.wordCount.toLocaleString()} words</span>
                    )}
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {new Date(result.metadata.lastModified).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Expand/Collapse button */}
              {result.highlights.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={(e) => {
                    e.stopPropagation()
                    toggleExpanded(result.id)
                  }}
                >
                  {isExpanded ? 'Show less' : 'Show more highlights'}
                  <ChevronRight className={cn(
                    "w-3 h-3 ml-1 transition-transform",
                    isExpanded && "rotate-90"
                  )} />
                </Button>
              )}
            </div>
          </button>
          
          {/* Quick Actions */}
          <div className="border-t px-4 py-2 bg-muted/30">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">
                Relevance score: {result.relevanceScore}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 text-xs"
                onClick={() => onResultClick?.(result)}
              >
                <Eye className="w-3 h-3 mr-1" />
                View
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isLoading && results.length === 0) {
    return (
      <div className={cn("space-y-3", className)}>
        {Array.from({ length: 3 }).map((_, idx) => (
          <Card key={idx}>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <Skeleton className="w-5 h-5 rounded" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-2/3" />
                  </div>
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (results.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <div className="text-muted-foreground">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-1">No results found</p>
            <p className="text-sm">Try adjusting your search terms or filters</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-3", className)}>
      <ScrollArea className="h-full">
        <div className="space-y-3">
          {viewMode === 'compact' 
            ? results.map(renderCompactResult)
            : results.map(renderDetailedResult)
          }
        </div>
      </ScrollArea>
      
      {/* Load More */}
      {hasMore && onLoadMore && (
        <div className="pt-4 text-center">
          <Button
            variant="outline"
            onClick={onLoadMore}
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Load More Results'}
          </Button>
        </div>
      )}
    </div>
  )
}