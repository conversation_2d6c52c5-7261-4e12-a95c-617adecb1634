'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface SixBoxGridProps {
  children: React.ReactNode
  className?: string
  gap?: 'default' | 'tight' | 'loose'
}

export function SixBoxGrid({ children, className, gap = 'default' }: SixBoxGridProps) {
  const gapClasses = {
    tight: 'gap-4',
    default: 'gap-4 sm:gap-5 lg:gap-6',
    loose: 'gap-6 sm:gap-8 lg:gap-10'
  }

  return (
    <>
      {/* Custom styles for responsive 6-box grid */}
      <style jsx>{`
        .six-box-grid {
          display: grid;
          grid-template-columns: 1fr;
        }
        
        /* 2 columns on small screens */
        @media (min-width: 640px) {
          .six-box-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }
        
        /* 2x3 layout on medium screens */
        @media (min-width: 1024px) {
          .six-box-grid {
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
          }
        }
        
        /* 1x6 layout on very wide screens */
        @media (min-width: 1920px) {
          .six-box-grid {
            grid-template-columns: repeat(6, 1fr);
            grid-template-rows: 1fr;
          }
        }
        
        /* Ensure all grid items are equal height */
        .six-box-grid > * {
          min-height: 0;
          height: 100%;
        }
      `}</style>
      
      <div className={cn('six-box-grid', gapClasses[gap], className)}>
        {children}
      </div>
    </>
  )
}

// Mobile-optimized horizontal scroll variant
interface SixBoxScrollProps {
  children: React.ReactNode
  className?: string
}

export function SixBoxScroll({ children, className }: SixBoxScrollProps) {
  return (
    <>
      <style jsx>{`
        .six-box-scroll {
          display: flex;
          gap: 1rem;
          overflow-x: auto;
          scroll-snap-type: x mandatory;
          -webkit-overflow-scrolling: touch;
          scrollbar-width: none;
          -ms-overflow-style: none;
          padding-bottom: 1rem;
        }
        
        .six-box-scroll::-webkit-scrollbar {
          display: none;
        }
        
        .six-box-scroll > * {
          flex: 0 0 280px;
          scroll-snap-align: start;
        }
        
        @media (min-width: 640px) {
          .six-box-scroll > * {
            flex: 0 0 320px;
          }
        }
      `}</style>
      
      <div className={cn('six-box-scroll', className)}>
        {children}
      </div>
    </>
  )
}