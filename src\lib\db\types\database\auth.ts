/**
 * Authentication and user-related table types
 */

import type { SubscriptionStatus } from '../enums'

export interface AuthTables {
  profiles: {
    Row: {
      id: string
      email: string | null
      full_name: string | null
      avatar_url: string | null
      stripe_customer_id: string | null
      created_at: string
      updated_at: string
    }
    Insert: {
      id: string
      email?: string | null
      full_name?: string | null
      avatar_url?: string | null
      stripe_customer_id?: string | null
      created_at?: string
      updated_at?: string
    }
    Update: {
      id?: string
      email?: string | null
      full_name?: string | null
      avatar_url?: string | null
      stripe_customer_id?: string | null
      created_at?: string
      updated_at?: string
    }
  }
  user_subscriptions: {
    Row: {
      id: string
      user_id: string
      tier_id: string
      status: SubscriptionStatus
      stripe_subscription_id: string | null
      stripe_customer_id: string | null
      current_period_start: string | null
      current_period_end: string | null
      cancel_at_period_end: boolean
      created_at: string
      updated_at: string
    }
    Insert: {
      id?: string
      user_id: string
      tier_id: string
      status: SubscriptionStatus
      stripe_subscription_id?: string | null
      stripe_customer_id?: string | null
      current_period_start?: string | null
      current_period_end?: string | null
      cancel_at_period_end?: boolean
      created_at?: string
      updated_at?: string
    }
    Update: {
      id?: string
      user_id?: string
      tier_id?: string
      status?: SubscriptionStatus
      stripe_subscription_id?: string | null
      stripe_customer_id?: string | null
      current_period_start?: string | null
      current_period_end?: string | null
      cancel_at_period_end?: boolean
      created_at?: string
      updated_at?: string
    }
  }
}