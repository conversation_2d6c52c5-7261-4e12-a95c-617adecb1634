# API Error Handling & Resilience Guide

## Overview

BookScribe now includes a comprehensive error handling system with retry logic, circuit breakers, and enhanced middleware for all API routes.

## Key Components

### 1. Enhanced Error Handler (`/src/lib/api/error-handler.ts`)
- Standardized error types and responses
- Integration with Sentry monitoring
- Support for retry-after headers
- User-friendly error messages

### 2. <PERSON><PERSON> Handler (`/src/lib/api/retry-handler.ts`)
- Exponential backoff with jitter
- Configurable retry strategies
- Specialized handlers for different scenarios (AI, database, external APIs)

### 3. Circuit Breaker (`/src/lib/services/circuit-breaker.ts`)
- Prevents cascade failures
- Automatic recovery testing
- Health monitoring and metrics
- Service-specific configurations

### 4. Enhanced Middleware (`/src/lib/api/enhanced-middleware.ts`)
- Combines all protection layers
- Request ID tracking
- CORS handling
- Performance monitoring

## Migration Guide

### Basic Migration

Replace the old middleware:

```typescript
// OLD
export const POST = withMiddleware(
  async (request: NextRequest) => {
    // handler code
  },
  {
    auth: true,
    rateLimit: { type: 'ai-generation' }
  }
)

// NEW
import { Middleware } from '@/lib/api/enhanced-middleware'

export const POST = Middleware.ai(
  async (request: NextRequest) => {
    // handler code
  }
)
```

### Custom Configuration

```typescript
import { withEnhancedMiddleware } from '@/lib/api/enhanced-middleware'

export const POST = withEnhancedMiddleware(
  async (request: NextRequest) => {
    // handler code
  },
  {
    auth: true,
    rateLimit: {
      type: 'custom',
      requests: 50,
      window: 60000 // 1 minute
    },
    retry: {
      enabled: true,
      type: 'external'
    },
    circuitBreaker: {
      enabled: true,
      name: 'my-service',
      type: 'custom'
    },
    timeout: 30000, // 30 seconds
    cors: {
      origins: ['https://bookscribe.ai'],
      methods: ['GET', 'POST'],
      headers: ['Content-Type', 'Authorization']
    }
  }
)
```

## Middleware Presets

### 1. Public Endpoints
```typescript
export const GET = Middleware.public(async (request) => {
  // No auth, no rate limiting
})
```

### 2. AI Endpoints
```typescript
export const POST = Middleware.ai(async (request) => {
  // Full protection: auth, rate limit, retry, circuit breaker
})
```

### 3. Database Endpoints
```typescript
export const POST = Middleware.database(async (request) => {
  // Database-specific retry and circuit breaker
})
```

### 4. External API Endpoints
```typescript
export const POST = Middleware.external(async (request) => {
  // External API retry and circuit breaker
})
```

### 5. Webhook Endpoints
```typescript
export const POST = Middleware.webhook(async (request) => {
  // No auth, custom rate limiting
})
```

## Error Handling Best Practices

### 1. Use Typed Errors

```typescript
import { ApiErrors } from '@/lib/api/error-handler'

// Validation error
throw ApiErrors.validation('Invalid input', { field: 'email' })

// Not found
throw ApiErrors.notFound('Project')

// Rate limit
throw ApiErrors.rateLimit(60) // Retry after 60 seconds

// Service unavailable
throw ApiErrors.externalService('OpenAI', { reason: 'timeout' })
```

### 2. Handle Circuit Breaker States

```typescript
import { circuitBreakerManager } from '@/lib/services/circuit-breaker'

// Check circuit breaker health
const health = circuitBreakerManager.getAllBreakerStates()

// Manual circuit control (admin endpoints)
circuitBreakerManager.getBreaker('openai').forceReset()
circuitBreakerManager.resetBreaker('openai') // Alternative method
```

### 3. Custom Retry Logic

```typescript
import { withRetry } from '@/lib/api/retry-handler'

const result = await withRetry(
  async () => {
    // Your operation
  },
  {
    maxAttempts: 5,
    initialDelay: 500,
    shouldRetry: (error) => {
      // Custom retry logic
      return !error.message.includes('fatal')
    }
  }
)
```

## Monitoring & Observability

### Request Tracking
Every request gets a unique ID accessible via:
- Request header: `x-request-id`
- Response header: `x-request-id`
- Logs: All log entries include the request ID

### Performance Metrics
Response times are included in:
- Response header: `x-response-time`
- Logs: Duration tracked for all requests

### Circuit Breaker Metrics
Monitor circuit breaker health:
```typescript
GET /api/admin/circuit-breakers

// Example implementation
export const GET = Middleware.public(async () => {
  const states = circuitBreakerManager.getAllBreakerStates()
  return NextResponse.json(states)
})
```

## Configuration

### Environment Variables

```env
# Retry Configuration
AI_RETRY_MAX=3
AI_RETRY_DELAY=1000
AI_RETRY_MAX_DELAY=30000
AI_RETRY_BACKOFF=2

# Timeouts
TIMEOUT_API_DEFAULT=30000
TIMEOUT_AI_GENERATION=120000
TIMEOUT_FILE_UPLOAD=300000

# Circuit Breaker
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RESET_TIMEOUT=60000
CIRCUIT_BREAKER_MONITORING_WINDOW=60000
```

## Testing

### Test Retry Logic
```typescript
// Force failures to test retry
const handler = Middleware.ai(async () => {
  if (Math.random() < 0.5) {
    throw new Error('Temporary failure')
  }
  return NextResponse.json({ success: true })
})
```

### Test Circuit Breaker
```typescript
// Simulate failures to trip circuit
for (let i = 0; i < 10; i++) {
  try {
    await handler(request)
  } catch (error) {
    // Circuit should open after threshold
  }
}
```

## Common Patterns

### 1. Graceful Degradation
```typescript
try {
  const aiResult = await generateWithAI(prompt)
  return NextResponse.json(aiResult)
} catch (error) {
  if (error.type === ErrorType.CIRCUIT_BREAKER) {
    // Return cached or simplified response
    return NextResponse.json(getCachedResponse())
  }
  throw error
}
```

### 2. Batch Operations with Retry
```typescript
import { batchWithRetry } from '@/lib/api/retry-handler'

const results = await batchWithRetry(
  operations.map(op => () => processOperation(op)),
  { concurrency: 3 }
)

const successful = results.filter(r => r.success)
const failed = results.filter(r => !r.success)
```

### 3. Health Check Endpoint
```typescript
export const GET = Middleware.public(async () => {
  const health = {
    status: 'healthy',
    circuitBreakers: circuitBreakerRegistry.getHealthStatus(),
    timestamp: new Date().toISOString()
  }
  
  return NextResponse.json(health)
})
```

## Troubleshooting

### Circuit Breaker Won't Close
1. Check failure threshold settings
2. Verify reset timeout is appropriate
3. Check if service is actually recovered
4. Use force reset as last resort

### Too Many Retries
1. Adjust retry configuration
2. Implement better error detection
3. Use circuit breakers to prevent retry storms

### Rate Limiting Issues
1. Check client IP detection
2. Verify rate limit windows
3. Consider user-based limits for authenticated endpoints

## Next Steps

1. Migrate all API routes to use enhanced middleware
2. Set up monitoring dashboards for circuit breakers
3. Configure alerts for circuit breaker state changes
4. Implement caching for graceful degradation
5. Add integration tests for error scenarios