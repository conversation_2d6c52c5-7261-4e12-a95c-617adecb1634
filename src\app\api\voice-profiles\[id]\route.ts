import { NextResponse, NextRequest } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';
import { logger } from '@/lib/services/logger';
import { createTypedServerClient } from '@/lib/supabase';

export const GET = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params;
    const supabase = await createTypedServerClient();
    const user = request.user!;

    const { data: profile, error } = await supabase
      .from('voice_profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return UnifiedResponse.error({
          message: 'Voice profile not found',
          code: 'NOT_FOUND'
        }, undefined, 404);
      }
      throw error;
    }

    // Check if user has access to this profile
    if (profile.user_id !== user.id && !profile.is_global) {
      return UnifiedResponse.error({
        message: 'Not authorized to access this voice profile',
        code: 'FORBIDDEN'
      }, undefined, 403);
    }

    return UnifiedResponse.success({ profile });
  } catch (error) {
    logger.error('Error fetching voice profile:', error);
    return UnifiedResponse.error({
      message: 'Failed to fetch voice profile',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

export const PUT = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params;
    const supabase = await createTypedServerClient();
    const user = request.user!;

    const body = await request.json();
    const { name, description, isGlobal } = body;

    // First check if user owns this profile
    const { data: existing, error: checkError } = await supabase
      .from('voice_profiles')
      .select('user_id')
      .eq('id', id)
      .single();

    if (checkError || !existing || existing.user_id !== user.id) {
      return UnifiedResponse.error({
        message: 'Not authorized to update this voice profile',
        code: 'FORBIDDEN'
      }, undefined, 403);
    }

    const { data: profile, error } = await supabase
      .from('voice_profiles')
      .update({
        name,
        description,
        is_global: isGlobal,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return UnifiedResponse.success({ profile });
  } catch (error) {
    logger.error('Error updating voice profile:', error);
    return UnifiedResponse.error({
      message: 'Failed to update voice profile',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

export const DELETE = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params;
    const supabase = await createTypedServerClient();
    const user = request.user!;

    // First check if user owns this profile
    const { data: existing, error: checkError } = await supabase
      .from('voice_profiles')
      .select('user_id')
      .eq('id', id)
      .single();

    if (checkError || !existing || existing.user_id !== user.id) {
      return UnifiedResponse.error({
        message: 'Not authorized to delete this voice profile',
        code: 'FORBIDDEN'
      }, undefined, 403);
    }

    const { error } = await supabase
      .from('voice_profiles')
      .delete()
      .eq('id', id);

    if (error) throw error;

    return UnifiedResponse.success({ message: 'Voice profile deleted successfully' });
  } catch (error) {
    logger.error('Error deleting voice profile:', error);
    return UnifiedResponse.error({
      message: 'Failed to delete voice profile',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});