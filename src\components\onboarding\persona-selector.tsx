'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { 
  <PERSON>ather, 
  BookOpen, 
  Library, 
  FileText,
  Sparkles,
  Target,
  Users,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'

export interface WriterPersona {
  id: string
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
  features: string[]
  recommendedPlan?: string
}

const personas: WriterPersona[] = [
  {
    id: 'first-time',
    icon: Sparkles,
    title: 'First-Time Novelist',
    description: 'New to novel writing and excited to learn',
    features: [
      'Guided story structure',
      'Writing tutorials',
      'AI brainstorming help',
      'Progress tracking'
    ],
    recommendedPlan: 'starter'
  },
  {
    id: 'experienced',
    icon: Feather,
    title: 'Experienced Author',
    description: 'Published or completed multiple novels',
    features: [
      'Advanced AI agents',
      'Custom workflows',
      'Batch operations',
      'Export options'
    ],
    recommendedPlan: 'professional'
  },
  {
    id: 'series',
    icon: Library,
    title: 'Series Writer',
    description: 'Creating connected stories and universes',
    features: [
      'Series bible',
      'Universe mapping',
      'Character continuity',
      'Timeline tracking'
    ],
    recommendedPlan: 'writer'
  },
  {
    id: 'short-story',
    icon: FileText,
    title: 'Short Story Writer',
    description: 'Focused on shorter fiction and anthologies',
    features: [
      'Quick templates',
      'Story collections',
      'Submission tracking',
      'Word count goals'
    ],
    recommendedPlan: 'starter'
  }
]

interface PersonaSelectorProps {
  onSelect: (persona: WriterPersona) => void
  selectedPersona?: string
}

export function PersonaSelector({ onSelect, selectedPersona }: PersonaSelectorProps) {
  const [hoveredPersona, setHoveredPersona] = useState<string | null>(null)

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">What kind of writer are you?</h2>
        <p className="text-muted-foreground">
          This helps us personalize your BookScribe experience
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {personas.map((persona) => {
          const Icon = persona.icon
          const isSelected = selectedPersona === persona.id
          const isHovered = hoveredPersona === persona.id

          return (
            <Card
              key={persona.id}
              className={cn(
                'relative p-6 cursor-pointer transition-all duration-200',
                'hover:shadow-lg hover:border-primary/50',
                isSelected && 'border-primary shadow-lg bg-primary/5'
              )}
              onClick={() => onSelect(persona)}
              onMouseEnter={() => setHoveredPersona(persona.id)}
              onMouseLeave={() => setHoveredPersona(null)}
            >
              <div className="flex items-start gap-4">
                <div className={cn(
                  'w-12 h-12 rounded-lg flex items-center justify-center transition-colors',
                  isSelected || isHovered ? 'bg-primary/20' : 'bg-muted'
                )}>
                  <Icon className={cn(
                    'w-6 h-6 transition-colors',
                    isSelected || isHovered ? 'text-primary' : 'text-muted-foreground'
                  )} />
                </div>
                
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-1">{persona.title}</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    {persona.description}
                  </p>
                  
                  <div className="space-y-1">
                    {persona.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className={cn(
                          'w-1.5 h-1.5 rounded-full transition-colors',
                          isSelected || isHovered ? 'bg-primary' : 'bg-muted-foreground'
                        )} />
                        <span className={cn(
                          'transition-colors',
                          isSelected || isHovered ? 'text-foreground' : 'text-muted-foreground'
                        )}>
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {isSelected && (
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                    <Sparkles className="w-3 h-3 text-primary-foreground" />
                  </div>
                </div>
              )}
            </Card>
          )
        })}
      </div>
    </div>
  )
}

export function OnboardingGoals({ persona }: { persona?: WriterPersona }) {
  const [selectedGoals, setSelectedGoals] = useState<string[]>([])

  const goals = [
    { id: 'finish-novel', icon: Target, label: 'Finish my first novel' },
    { id: 'improve-craft', icon: Sparkles, label: 'Improve my writing craft' },
    { id: 'build-habit', icon: Zap, label: 'Build a daily writing habit' },
    { id: 'connect-readers', icon: Users, label: 'Connect with readers' },
  ]

  const toggleGoal = (goalId: string) => {
    setSelectedGoals(prev =>
      prev.includes(goalId)
        ? prev.filter(id => id !== goalId)
        : [...prev, goalId]
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">What are your writing goals?</h2>
        <p className="text-muted-foreground">
          Select all that apply - we'll help you achieve them
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {goals.map((goal) => {
          const Icon = goal.icon
          const isSelected = selectedGoals.includes(goal.id)

          return (
            <Button
              key={goal.id}
              variant={isSelected ? 'default' : 'outline'}
              size="lg"
              className={cn(
                'h-auto p-4 justify-start',
                isSelected && 'border-primary'
              )}
              onClick={() => toggleGoal(goal.id)}
            >
              <Icon className="w-5 h-5 mr-3 shrink-0" />
              <span className="text-left">{goal.label}</span>
            </Button>
          )
        })}
      </div>

      {persona && persona.id === 'first-time' && selectedGoals.length > 0 && (
        <div className="mt-6 p-4 bg-primary/10 rounded-lg border border-primary/20">
          <p className="text-sm">
            <span className="font-medium">Great choices!</span> As a first-time novelist,
            we'll provide extra guidance and celebrate your milestones along the way.
          </p>
        </div>
      )}
    </div>
  )
}