import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { UnifiedRateLimiter } from '@/lib/rate-limiter-unified';
import { createClient } from '@/lib/supabase/server';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/services/logger');

describe('UnifiedRateLimiter', () => {
  let mockSupabase: any;
  let rateLimiter: UnifiedRateLimiter;
  
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
    };
    
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
    
    rateLimiter = new UnifiedRateLimiter();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('checkLimit', () => {
    it('should allow requests within limit', async () => {
      const result = await rateLimiter.checkLimit('user-123', 'api', {
        requests: 10,
        windowMs: 60000,
      });

      expect(result.allowed).toBe(true);
      expect(result.remaining).toBeDefined();
      expect(result.resetAt).toBeDefined();
    });

    it('should use different limits for different operations', async () => {
      // API limit
      const apiResult = await rateLimiter.checkLimit('user-123', 'api');
      expect(apiResult.allowed).toBe(true);
      
      // Auth limit (stricter)
      const authResult = await rateLimiter.checkLimit('user-123', 'auth');
      expect(authResult.allowed).toBe(true);
      
      // AI limit (most expensive)
      const aiResult = await rateLimiter.checkLimit('user-123', 'ai');
      expect(aiResult.allowed).toBe(true);
    });

    it('should track requests in memory', async () => {
      // Make multiple requests
      for (let i = 0; i < 5; i++) {
        const result = await rateLimiter.checkLimit('user-123', 'api', {
          requests: 10,
          windowMs: 60000,
        });
        expect(result.allowed).toBe(true);
        expect(result.remaining).toBe(9 - i);
      }
    });
  });

  describe('checkTierLimit', () => {
    it('should apply different limits based on user tier', async () => {
      // Free tier
      mockSupabase.single.mockResolvedValue({
        data: { tier: 'free' },
        error: null,
      });
      
      const freeResult = await rateLimiter.checkTierLimit('free-user', 'api');
      expect(freeResult).toBeDefined();

      // Premium tier
      mockSupabase.single.mockResolvedValue({
        data: { tier: 'premium' },
        error: null,
      });
      
      const premiumResult = await rateLimiter.checkTierLimit('premium-user', 'api');
      expect(premiumResult).toBeDefined();
    });

    it('should cache tier information', async () => {
      mockSupabase.single.mockResolvedValue({
        data: { tier: 'standard' },
        error: null,
      });
      
      // First call - fetches from database
      await rateLimiter.checkTierLimit('user-123', 'api');
      expect(mockSupabase.from).toHaveBeenCalledTimes(1);
      
      // Second call - uses cache
      await rateLimiter.checkTierLimit('user-123', 'api');
      expect(mockSupabase.from).toHaveBeenCalledTimes(1);
    });
  });

  describe('IP-based rate limiting', () => {
    it('should limit by IP address', async () => {
      const result = await rateLimiter.checkIPLimit('***********', 'api');
      
      expect(result.allowed).toBe(true);
    });

    it('should have stricter limits for authentication endpoints', async () => {
      const result = await rateLimiter.checkIPLimit('***********', 'auth');
      
      expect(result).toBeDefined();
    });
  });

  describe('resetLimit', () => {
    it('should reset rate limit for a key', async () => {
      await rateLimiter.resetLimit('user-123', 'api');
      
      // After reset, should allow requests
      const result = await rateLimiter.checkLimit('user-123', 'api');
      expect(result.allowed).toBe(true);
    });

    it('should reset all limits for a user', async () => {
      await rateLimiter.resetAllLimits('user-123');
      
      // All operations should be allowed after reset
      const apiResult = await rateLimiter.checkLimit('user-123', 'api');
      const authResult = await rateLimiter.checkLimit('user-123', 'auth');
      
      expect(apiResult.allowed).toBe(true);
      expect(authResult.allowed).toBe(true);
    });
  });

  describe('Custom limits', () => {
    it('should apply custom limits when provided', async () => {
      const customLimits = {
        requests: 5,
        windowMs: 30000, // 30 seconds
      };
      
      const result = await rateLimiter.checkLimit('user-123', 'custom', customLimits);
      
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(4);
    });
  });

  describe('Burst protection', () => {
    it('should prevent burst requests', async () => {
      // Simulate burst of requests
      const promises = [];
      for (let i = 0; i < 20; i++) {
        promises.push(rateLimiter.checkLimit('user-123', 'api', {
          requests: 10,
          windowMs: 60000,
        }));
      }
      
      const results = await Promise.all(promises);
      
      const allowed = results.filter(r => r.allowed).length;
      const blocked = results.filter(r => !r.allowed).length;
      
      expect(allowed).toBeLessThanOrEqual(10);
      expect(blocked).toBeGreaterThanOrEqual(10);
    });
  });
});