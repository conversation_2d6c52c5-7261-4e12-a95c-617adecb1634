import { Skeleton } from '@/components/ui/skeleton'

export default function WritePageLoading() {
  return (
    <div className="flex flex-col h-screen">
      {/* Header skeleton */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-24" />
          </div>
        </div>
      </div>

      {/* Main editor area skeleton */}
      <div className="flex-1 flex overflow-hidden">
        {/* Editor skeleton */}
        <div className="flex-1 p-4">
          <div className="h-full bg-muted/20 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Skeleton className="h-8 w-8 mx-auto mb-4 rounded-full animate-spin" />
              <Skeleton className="h-4 w-32 mx-auto" />
            </div>
          </div>
        </div>

        {/* Side panel skeleton (optional) */}
        <div className="hidden lg:flex w-80 border-l p-4">
          <div className="w-full space-y-4">
            <Skeleton className="h-6 w-32" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom status bar skeleton */}
      <div className="border-t p-2">
        <div className="flex items-center justify-between px-4">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>
    </div>
  )
}