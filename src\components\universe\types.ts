export interface Universe {
  id: string
  name: string
  description?: string
  rules: Record<string, unknown>
  created_at: string
  created_by: string
  series?: Array<{
    id: string
    title: string
    description?: string
  }>
  timeline_events?: Array<{
    id: string
    date: string
    title: string
    description: string
    type: 'historical' | 'story' | 'character' | 'world'
  }>
  locations?: Array<{
    id: string
    name: string
    description: string
    parent_id?: string
  }>
  shared_characters?: Array<{
    id: string
    name: string
    description: string
    appearances: number
  }>
}

export interface UniverseFormData {
  name: string
  description: string
  genre: string
  magicSystem?: string
  technologyLevel?: string
  politicalSystems?: string
  geography?: string
  history?: string
  cultures?: string
  languages?: string
  religions?: string
  economy?: string
  conflicts?: string
  naturalLaws?: string
}

export interface UniverseFilters {
  search: string
  sortBy: 'name' | 'created' | 'series_count'
  sortOrder: 'asc' | 'desc'
}