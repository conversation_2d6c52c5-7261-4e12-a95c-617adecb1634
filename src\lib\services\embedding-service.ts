import OpenAI from 'openai'
import { createServerClient } from '@/lib/supabase'
import { logger } from './logger'
import type { SupabaseClient } from '@supabase/supabase-js'

interface EmbeddingContent {
  id: string
  type: string
  text: string
  metadata?: Record<string, any>
}

interface EmbeddingResult {
  contentId: string
  embedding: number[]
  model: string
  dimensions: number
}

export class EmbeddingService {
  private openai: OpenAI | null = null
  private supabase: SupabaseClient | null = null
  private readonly MODEL = 'text-embedding-3-small'
  private readonly DIMENSIONS = 1536
  private readonly BATCH_SIZE = 100

  private async getOpenAI() {
    if (!this.openai) {
      const apiKey = process.env.OPENAI_API_KEY
      if (!apiKey) {
        throw new Error('OpenAI API key not configured')
      }
      this.openai = new OpenAI({ apiKey })
    }
    return this.openai
  }

  private async getSupabase() {
    if (!this.supabase) {
      this.supabase = await createServerClient()
    }
    return this.supabase
  }

  /**
   * Generate embeddings for a single piece of content
   */
  async generateEmbedding(text: string): Promise<number[]> {
    try {
      const openai = await this.getOpenAI()
      
      // Truncate text if too long (OpenAI has token limits)
      const truncatedText = this.truncateText(text, 8000)
      
      const response = await openai.embeddings.create({
        model: this.MODEL,
        input: truncatedText,
        dimensions: this.DIMENSIONS
      })

      return response.data[0].embedding
    } catch (error) {
      logger.error('Failed to generate embedding:', error)
      throw error
    }
  }

  /**
   * Generate embeddings for multiple pieces of content
   */
  async generateEmbeddings(contents: EmbeddingContent[]): Promise<EmbeddingResult[]> {
    try {
      const openai = await this.getOpenAI()
      const results: EmbeddingResult[] = []

      // Process in batches to avoid rate limits
      for (let i = 0; i < contents.length; i += this.BATCH_SIZE) {
        const batch = contents.slice(i, i + this.BATCH_SIZE)
        const texts = batch.map(c => this.truncateText(c.text, 8000))

        const response = await openai.embeddings.create({
          model: this.MODEL,
          input: texts,
          dimensions: this.DIMENSIONS
        })

        response.data.forEach((embedding, idx) => {
          results.push({
            contentId: batch[idx].id,
            embedding: embedding.embedding,
            model: this.MODEL,
            dimensions: this.DIMENSIONS
          })
        })

        // Add a small delay between batches to avoid rate limits
        if (i + this.BATCH_SIZE < contents.length) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      return results
    } catch (error) {
      logger.error('Failed to generate embeddings:', error)
      throw error
    }
  }

  /**
   * Store embeddings in the database
   */
  async storeEmbeddings(projectId: string, embeddings: EmbeddingResult[]): Promise<void> {
    try {
      const supabase = await this.getSupabase()
      
      // Prepare data for insertion
      const embeddingData = embeddings.map(e => ({
        content_id: e.contentId,
        project_id: projectId,
        embedding: e.embedding,
        model: e.model,
        dimensions: e.dimensions,
        created_at: new Date()
      }))

      // Batch insert embeddings
      const { error } = await supabase
        .from('content_embeddings')
        .upsert(embeddingData, {
          onConflict: 'content_id'
        })

      if (error) {
        throw error
      }

      logger.info(`Stored ${embeddings.length} embeddings for project ${projectId}`)
    } catch (error) {
      logger.error('Failed to store embeddings:', error)
      throw error
    }
  }

  /**
   * Search for similar content using embeddings
   */
  async searchSimilar(
    projectId: string,
    query: string,
    limit: number = 10,
    threshold: number = 0.7
  ): Promise<Array<{
    contentId: string
    similarity: number
    metadata: Record<string, any>
  }>> {
    try {
      const supabase = await this.getSupabase()
      
      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query)
      
      // Search for similar embeddings using cosine similarity
      const { data, error } = await supabase.rpc('search_similar_content', {
        p_project_id: projectId,
        p_query_embedding: queryEmbedding,
        p_limit: limit,
        p_threshold: threshold
      })

      if (error) {
        // If function doesn't exist, return empty array
        if (error.code === '42883') {
          logger.warn('search_similar_content function not found')
          return []
        }
        throw error
      }

      return data || []
    } catch (error) {
      logger.error('Failed to search similar content:', error)
      throw error
    }
  }

  /**
   * Generate embeddings for all content in a project
   */
  async embedProject(projectId: string): Promise<void> {
    try {
      const supabase = await this.getSupabase()
      
      // Fetch all content that needs embeddings
      const { data: contentIndex, error } = await supabase
        .from('content_index')
        .select('id, content_type, title, content')
        .eq('project_id', projectId)
        .is('embedding_generated', false)

      if (error) throw error

      if (!contentIndex || contentIndex.length === 0) {
        logger.info('No content to embed for project', projectId)
        return
      }

      // Prepare content for embedding
      const contents: EmbeddingContent[] = contentIndex.map(item => ({
        id: item.id,
        type: item.content_type,
        text: `${item.title}\n\n${item.content}`,
        metadata: {
          contentType: item.content_type
        }
      }))

      // Generate embeddings
      logger.info(`Generating embeddings for ${contents.length} items in project ${projectId}`)
      const embeddings = await this.generateEmbeddings(contents)
      
      // Store embeddings
      await this.storeEmbeddings(projectId, embeddings)
      
      // Mark content as embedded
      const contentIds = contents.map(c => c.id)
      const { error: updateError } = await supabase
        .from('content_index')
        .update({ embedding_generated: true, updated_at: new Date() })
        .in('id', contentIds)

      if (updateError) {
        logger.error('Failed to mark content as embedded:', updateError)
      }

      logger.info(`Completed embedding generation for project ${projectId}`)
    } catch (error) {
      logger.error('Failed to embed project:', error)
      throw error
    }
  }

  /**
   * Update embeddings for specific content
   */
  async updateEmbedding(contentId: string, projectId: string, text: string): Promise<void> {
    try {
      // Generate new embedding
      const embedding = await this.generateEmbedding(text)
      
      // Store the embedding
      await this.storeEmbeddings(projectId, [{
        contentId,
        embedding,
        model: this.MODEL,
        dimensions: this.DIMENSIONS
      }])

      logger.info(`Updated embedding for content ${contentId}`)
    } catch (error) {
      logger.error('Failed to update embedding:', error)
      throw error
    }
  }

  /**
   * Delete embeddings for content
   */
  async deleteEmbedding(contentId: string): Promise<void> {
    try {
      const supabase = await this.getSupabase()
      
      const { error } = await supabase
        .from('content_embeddings')
        .delete()
        .eq('content_id', contentId)

      if (error) {
        throw error
      }

      logger.info(`Deleted embedding for content ${contentId}`)
    } catch (error) {
      logger.error('Failed to delete embedding:', error)
      throw error
    }
  }

  // Private helper methods
  private truncateText(text: string, maxChars: number): string {
    if (text.length <= maxChars) {
      return text
    }
    
    // Try to truncate at a sentence boundary
    const truncated = text.slice(0, maxChars)
    const lastPeriod = truncated.lastIndexOf('.')
    const lastQuestion = truncated.lastIndexOf('?')
    const lastExclamation = truncated.lastIndexOf('!')
    
    const lastSentenceEnd = Math.max(lastPeriod, lastQuestion, lastExclamation)
    
    if (lastSentenceEnd > maxChars * 0.8) {
      return truncated.slice(0, lastSentenceEnd + 1)
    }
    
    return truncated + '...'
  }
}

// Export singleton instance
export const embeddingService = new EmbeddingService()