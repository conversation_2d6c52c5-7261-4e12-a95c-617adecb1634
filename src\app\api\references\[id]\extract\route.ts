import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { TextExtractionService } from '@/lib/services/text-extraction-service'

export const POST = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: materialId } = await params
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify ownership
    const { data: material, error } = await supabase
      .from('reference_materials')
      .select('*, projects!inner(user_id)')
      .eq('id', materialId)
      .eq('projects.user_id', user.id)
      .single()

    if (error || !material) {
      return UnifiedResponse.error('Reference material not found', 404)
    }

    // Check if already processed
    if (material.is_processed && material.content) {
      return UnifiedResponse.success({
        message: 'Text already extracted',
        content: material.content,
        wordCount: material.content.split(/\s+/).filter(Boolean).length
      })
    }

    // Extract text
    logger.info(`Manually extracting text from material ${materialId}`)
    const result = await TextExtractionService.extractFromMaterial(materialId)

    if (!result.success) {
      return UnifiedResponse.error(result.error || 'Text extraction failed', 400)
    }

    // Get updated material
    const { data: updatedMaterial } = await supabase
      .from('reference_materials')
      .select('*')
      .eq('id', materialId)
      .single()

    return UnifiedResponse.success({
      message: 'Text extracted successfully',
      content: result.text,
      wordCount: result.wordCount,
      material: {
        id: updatedMaterial.id,
        projectId: updatedMaterial.project_id,
        type: updatedMaterial.type,
        title: updatedMaterial.title,
        description: updatedMaterial.description,
        fileUrl: updatedMaterial.file_url,
        fileSize: updatedMaterial.file_size,
        mimeType: updatedMaterial.mime_type,
        content: updatedMaterial.content,
        tags: updatedMaterial.tags || [],
        aiSummary: updatedMaterial.ai_summary,
        createdAt: new Date(updatedMaterial.created_at),
        updatedAt: new Date(updatedMaterial.updated_at),
      }
    })
  } catch (error) {
    logger.error('Text extraction error:', error)
    return UnifiedResponse.error('Failed to extract text')
  }
})