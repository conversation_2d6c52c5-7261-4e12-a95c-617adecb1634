"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Settings, 
  BookOpen, 
  Target, 
  Palette,
  FileText,
  Users,
  Globe,
  Sparkles,
  CheckCircle,
  Clock,
  Plus,
  Edit,
  Save,
  Wand2,
  Brain,
  Lightbulb,
  Star,
  Zap,
  Crown,
  Heart,
  Sword,
  Shield,
  ChevronRight
} from "lucide-react";

const projectTemplates = [
  {
    id: 'fantasy-epic',
    name: 'Epic Fantasy',
    description: 'Multi-book fantasy series with complex world-building',
    features: ['Character Arcs', 'Magic Systems', 'World Building', 'Political Intrigue'],
    icon: Crown,
    color: 'purple'
  },
  {
    id: 'sci-fi-space',
    name: 'Space Opera',
    description: 'Galaxy-spanning adventure with advanced technology',
    features: ['Technology Systems', 'Alien Cultures', 'Space Politics', 'Hero\'s Journey'],
    icon: Star,
    color: 'blue'
  },
  {
    id: 'mystery-thriller',
    name: 'Mystery Thriller',
    description: 'Suspenseful mystery with complex plot twists',
    features: ['Clue Tracking', 'Character Motives', 'Timeline Management', 'Red Herrings'],
    icon: Lightbulb,
    color: 'amber'
  },
  {
    id: 'romance-contemporary',
    name: 'Contemporary Romance',
    description: 'Character-driven romance in modern settings',
    features: ['Relationship Arcs', 'Emotional Beats', 'Character Chemistry', 'Conflict Resolution'],
    icon: Heart,
    color: 'pink'
  },
  {
    id: 'custom',
    name: 'Custom Project',
    description: 'Start from scratch with your own structure',
    features: ['Flexible Setup', 'Custom Templates', 'Personalized Workflow', 'Adaptive Tools'],
    icon: Wand2,
    color: 'green'
  }
];

const wizardSteps = [
  { id: 1, title: 'Project Basics', completed: true },
  { id: 2, title: 'Genre & Style', completed: true },
  { id: 3, title: 'Structure & Pacing', completed: false },
  { id: 4, title: 'Characters & World', completed: false },
  { id: 5, title: 'Themes & Content', completed: false },
  { id: 6, title: 'Technical Specs', completed: false }
];

const genreOptions = [
  'Fantasy', 'Science Fiction', 'Mystery', 'Romance', 'Thriller', 'Horror',
  'Historical Fiction', 'Literary Fiction', 'Young Adult', 'Children\'s',
  'Non-Fiction', 'Biography', 'Self-Help', 'Other'
];

const toneOptions = [
  'Epic & Grand', 'Dark & Gritty', 'Light & Humorous', 'Mysterious & Suspenseful',
  'Romantic & Emotional', 'Action-Packed', 'Contemplative', 'Satirical'
];

export function DemoProjectSetupEnhanced() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>('fantasy-epic');
  const [currentStep, setCurrentStep] = useState(2);
  const [activeTab, setActiveTab] = useState("template");
  const [projectData, setProjectData] = useState({
    title: 'The Crystal Saga',
    description: 'An epic fantasy series following Aria Moonwhisper as she discovers her crystal magic powers and faces the ancient Shadow King.',
    genre: 'Fantasy',
    tone: 'Epic & Grand',
    targetLength: '80000',
    chapters: '18',
    series: true,
    seriesBooks: '3'
  });

  const [aiSettings, setAiSettings] = useState({
    writingStyle: 'descriptive',
    assistanceLevel: 'balanced',
    focusAreas: ['character-development', 'world-building', 'dialogue'],
    creativityLevel: 75,
    consistencyChecking: true,
    realTimeAnalysis: true
  });

  const currentTemplate = projectTemplates.find(t => t.id === selectedTemplate);
  const progressPercentage = (currentStep / wizardSteps.length) * 100;

  return (
    <div className="w-full h-full bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <div className="border-b border-border bg-gradient-to-r from-card/60 to-card/40 backdrop-blur-sm p-6 lg:p-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-center gap-3">
              <Sparkles className="w-7 h-7 md:w-8 md:h-8 text-primary animate-pulse" />
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">Create New Project</h2>
              <Badge variant="outline" className="border-primary/50 text-primary text-sm md:text-base px-4 py-2">
                Interactive Demo
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="hover:scale-105 transition-transform">
              <Save className="w-4 h-4 mr-2" />
              Save Progress
            </Button>
            <Button variant="outline" size="sm" className="hover:scale-105 transition-transform">
              <Brain className="w-4 h-4 mr-2" />
              AI Suggestions
            </Button>
          </div>
        </div>

        {/* Wizard Progress */}
        <div className="mt-8">
          <Progress value={progressPercentage} className="h-3 shadow-inner" />
          <div className="flex items-center justify-between mt-3 text-sm md:text-base text-muted-foreground font-medium">
            <span>Step {currentStep} of {wizardSteps.length}</span>
            <span>{wizardSteps[currentStep - 1]?.title}</span>
          </div>
        </div>
      </div>

      <div className="p-6 lg:p-8 xl:p-10 2xl:p-12">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full max-w-3xl lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl mx-auto grid-cols-4 p-1.5 h-auto">
            <TabsTrigger value="template" className="py-3 text-sm md:text-base">Project Basics</TabsTrigger>
            <TabsTrigger value="details" className="py-3 text-sm md:text-base">Genre & Style</TabsTrigger>
            <TabsTrigger value="structure" className="py-3 text-sm md:text-base">Structure & Pacing</TabsTrigger>
            <TabsTrigger value="ai-config" className="py-3 text-sm md:text-base">Characters & World</TabsTrigger>
          </TabsList>

          <TabsContent value="template" className="space-y-8 max-w-6xl mx-auto mt-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold mb-4">Project Basics</h3>
              <p className="text-muted-foreground text-base md:text-lg mb-8 max-w-3xl">
                Start by giving your project a title and description. You can also choose from our
                pre-built templates to get started faster.
              </p>
            </div>

            {/* Project Title and Description */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
              <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
                <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                  <CardTitle className="text-xl md:text-2xl">Project Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6 p-6 md:p-8">
                  <div>
                    <label className="text-base md:text-lg font-semibold mb-3 block">Story Title</label>
                    <Input
                      value={projectData.title}
                      onChange={(e) => setProjectData({...projectData, title: e.target.value})}
                      placeholder="Enter your story title"
                      className="h-12 md:h-14 text-base md:text-lg px-4 md:px-6 border-2 focus:border-primary transition-colors"
                    />
                  </div>
                  <div>
                    <label className="text-base md:text-lg font-semibold mb-3 block">Description</label>
                    <Textarea
                      value={projectData.description}
                      onChange={(e) => setProjectData({...projectData, description: e.target.value})}
                      placeholder="Describe your story..."
                      rows={5}
                      className="text-base md:text-lg p-4 md:p-6 border-2 focus:border-primary transition-colors resize-none"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
                <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                  <CardTitle className="text-xl md:text-2xl">Quick Start Templates</CardTitle>
                </CardHeader>
                <CardContent className="p-6 md:p-8">
                  <p className="text-base md:text-lg text-muted-foreground mb-6">
                    Choose a template to pre-fill your project with genre-specific settings.
                  </p>
                  <div className="space-y-4">
                    {projectTemplates.slice(0, 3).map((template) => {
                      const IconComponent = template.icon;
                      return (
                        <div
                          key={template.id}
                          className={`flex items-center gap-4 p-4 rounded-lg cursor-pointer transition-all ${
                            selectedTemplate === template.id
                              ? 'bg-primary/20 border-2 border-primary shadow-md'
                              : 'hover:bg-muted/50 border-2 border-transparent'
                          }`}
                          onClick={() => setSelectedTemplate(template.id)}
                        >
                          <input
                            type="radio"
                            name="template"
                            value={template.id}
                            checked={selectedTemplate === template.id}
                            onChange={() => setSelectedTemplate(template.id)}
                            className="h-5 w-5 text-primary"
                          />
                          <IconComponent className={`w-6 h-6 text-${template.color}-500`} />
                          <div>
                            <span className="text-base md:text-lg font-medium">{template.name}</span>
                            <p className="text-sm text-muted-foreground mt-1">{template.description}</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <h4 className="text-xl md:text-2xl font-semibold mb-6">Or Choose a Template</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {projectTemplates.slice(0, 3).map((template) => {
                  const IconComponent = template.icon;
                  return (
                    <Card
                      key={template.id}
                      className={`cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-2xl ${
                        selectedTemplate === template.id
                          ? 'border-2 border-primary bg-gradient-to-br from-primary/20 to-primary/10 shadow-xl scale-105'
                          : 'hover:border-primary/50 border-2 border-transparent'
                      }`}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <CardHeader className="pb-4">
                        <div className="flex items-center gap-3">
                          <div className={`p-3 rounded-xl bg-${template.color}-500/20`}>
                            <IconComponent className={`w-6 h-6 md:w-7 md:h-7 text-${template.color}-500`} />
                          </div>
                          <div>
                            <CardTitle className="text-lg md:text-xl">{template.name}</CardTitle>
                            {selectedTemplate === template.id && (
                              <Badge variant="default" className="text-sm mt-2">
                                Selected
                              </Badge>
                            )}
                          </div>
                      </div>
                    </CardHeader>

                      <CardContent className="pt-2">
                        <p className="text-sm md:text-base text-muted-foreground">{template.description}</p>
                      </CardContent>
                  </Card>
                );
              })}
            </div>
            </div>

            <div className="flex justify-end pt-8 border-t border-border">
              <Button 
                onClick={() => setActiveTab('details')}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base md:text-lg px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                Continue to Genre & Style
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-8 max-w-6xl mx-auto mt-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold mb-4">Genre & Style</h3>
              <p className="text-muted-foreground text-base md:text-lg mb-8 max-w-3xl">
                Define your story's genre and tone to help the AI provide better assistance.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 xl:gap-10">
              <div className="space-y-8">
                <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
                  <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                    <CardTitle className="flex items-center gap-3 text-xl md:text-2xl">
                      <BookOpen className="w-6 h-6 md:w-7 md:h-7" />
                      Basic Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6 p-6 md:p-8">
                    <div>
                      <label className="text-base md:text-lg font-semibold mb-3 block">Project Title</label>
                      <Input 
                        value={projectData.title}
                        onChange={(e) => setProjectData({...projectData, title: e.target.value})}
                        placeholder="Enter your book/series title"
                        className="h-12 md:h-14 text-base md:text-lg px-4 md:px-6 border-2 focus:border-primary transition-colors"
                      />
                    </div>

                    <div>
                      <label className="text-base md:text-lg font-semibold mb-3 block">Description</label>
                      <Textarea 
                        value={projectData.description}
                        onChange={(e) => setProjectData({...projectData, description: e.target.value})}
                        placeholder="Brief description of your story"
                        rows={4}
                        className="text-base md:text-lg p-4 md:p-6 border-2 focus:border-primary transition-colors resize-none"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label className="text-base md:text-lg font-semibold mb-3 block">Genre</label>
                        <select 
                          value={projectData.genre}
                          onChange={(e) => setProjectData({...projectData, genre: e.target.value})}
                          className="w-full h-12 md:h-14 px-4 text-base md:text-lg border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                        >
                          {genreOptions.map(genre => (
                            <option key={genre} value={genre}>{genre}</option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="text-base md:text-lg font-semibold mb-3 block">Tone</label>
                        <select 
                          value={projectData.tone}
                          onChange={(e) => setProjectData({...projectData, tone: e.target.value})}
                          className="w-full h-12 md:h-14 px-4 text-base md:text-lg border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                        >
                          {toneOptions.map(tone => (
                            <option key={tone} value={tone}>{tone}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
                  <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                    <CardTitle className="flex items-center gap-3 text-xl md:text-2xl">
                      <Target className="w-6 h-6 md:w-7 md:h-7" />
                      Structure & Goals
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6 p-6 md:p-8">
                    <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Target Word Count</label>
                        <Input 
                          value={projectData.targetLength}
                          onChange={(e) => setProjectData({...projectData, targetLength: e.target.value})}
                          placeholder="80000"
                          type="number"
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-2 block">Planned Chapters</label>
                        <Input 
                          value={projectData.chapters}
                          onChange={(e) => setProjectData({...projectData, chapters: e.target.value})}
                          placeholder="18"
                          type="number"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="series"
                          checked={projectData.series}
                          onChange={(e) => setProjectData({...projectData, series: e.target.checked})}
                          className="rounded border-border"
                        />
                        <label htmlFor="series" className="text-sm font-medium">
                          This is part of a series
                        </label>
                      </div>

                      {projectData.series && (
                        <div>
                          <label className="text-sm font-medium mb-2 block">Number of Books in Series</label>
                          <Input 
                            value={projectData.seriesBooks}
                            onChange={(e) => setProjectData({...projectData, seriesBooks: e.target.value})}
                            placeholder="3"
                            type="number"
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-8">
                <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow bg-gradient-to-br from-card via-card to-primary/5">
                  <CardHeader className="bg-gradient-to-r from-primary/10 to-accent/10 pb-6">
                    <CardTitle className="flex items-center gap-3 text-xl md:text-2xl">
                      <Brain className="w-6 h-6 md:w-7 md:h-7 animate-pulse" />
                      AI-Generated Suggestions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6 p-6 md:p-8">
                    <div className="p-5 rounded-xl bg-gradient-to-r from-primary/15 to-primary/10 border-2 border-primary/30">
                      <div className="flex items-center gap-3 mb-3">
                        <Sparkles className="w-5 h-5 text-primary animate-pulse" />
                        <span className="text-base md:text-lg font-semibold">Story Structure Suggestion</span>
                      </div>
                      <p className="text-sm md:text-base text-muted-foreground leading-relaxed">
                        Based on your epic fantasy genre, consider using the Hero's Journey structure 
                        with a three-act format. This works well for character growth and world-building.
                      </p>
                    </div>

                    <div className="p-4 rounded bg-blue-500/10 border border-blue-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Lightbulb className="w-4 h-4 text-blue-500" />
                        <span className="text-sm font-medium">Chapter Length Tip</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        For an 80,000-word book with 18 chapters, aim for 4,400 words per chapter. 
                        Vary between 3,000-6,000 for better pacing.
                      </p>
                    </div>

                    <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="w-4 h-4 text-green-500" />
                        <span className="text-sm font-medium">Character Development</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Epic fantasy benefits from detailed character arcs. Consider creating 
                        character sheets for your main cast and tracking their growth.
                      </p>
                    </div>

                    <Button variant="outline" size="sm" className="w-full">
                      <Plus className="w-4 h-4 mr-2" />
                      Get More AI Suggestions
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      Project Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Estimated completion:</span>
                        <span className="text-sm font-medium">6-8 months</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Recommended daily goal:</span>
                        <span className="text-sm font-medium">500-750 words</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Writing sessions per week:</span>
                        <span className="text-sm font-medium">5-6 sessions</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex items-center justify-between pt-8 border-t border-border">
              <Button 
                variant="outline" 
                onClick={() => setActiveTab('template')}
                className="text-base px-6 py-6 hover:scale-105 transition-transform"
              >
                Back to Templates
              </Button>
              <Button 
                onClick={() => setActiveTab('structure')}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                Continue to Structure Setup
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="structure" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Project Structure Setup</h3>
              <p className="text-muted-foreground mb-6">
                Configure how your project will be organized and what tools you'll have access to.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Workspace Layout</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border-2 border-dashed border-border rounded-lg">
                    <div className="text-center text-muted-foreground">
                      <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Interactive workspace preview</p>
                      <p className="text-sm">Shows editor, sidebar panels, and tool arrangement</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Panel Configuration</label>
                    <div className="space-y-2">
                      {['Story Bible', 'AI Assistant', 'Chapter Outline', 'Character Tracker'].map((panel) => (
                        <div key={panel} className="flex items-center gap-2">
                          <input type="checkbox" defaultChecked className="rounded border-border" />
                          <span className="text-sm">{panel}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Chapter Organization</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {Array.from({length: 6}, (_, i) => (
                      <div key={i} className="flex items-center justify-between p-2 rounded border">
                        <span className="text-sm">Chapter {i + 1}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {i < 2 ? 'Complete' : i < 4 ? 'In Progress' : 'Planned'}
                          </Badge>
                          <Button variant="ghost" size="sm">
                            <Edit className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    <div className="text-center">
                      <Button variant="outline" size="sm">
                        <Plus className="w-4 h-4 mr-2" />
                        Add More Chapters
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex items-center justify-between pt-6 border-t border-border">
              <Button variant="outline" onClick={() => setActiveTab('details')}>
                Back to Details
              </Button>
              <Button onClick={() => setActiveTab('ai-config')}>
                Continue to AI Configuration
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="ai-config" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">AI Assistant Configuration</h3>
              <p className="text-muted-foreground mb-6">
                Customize how the AI will assist you throughout your writing process.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Writing Style Preferences</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Writing Style</label>
                      <select 
                        value={aiSettings.writingStyle}
                        onChange={(e) => setAiSettings({...aiSettings, writingStyle: e.target.value})}
                        className="w-full p-2 border border-border rounded-md bg-background"
                      >
                        <option value="descriptive">Descriptive & Rich</option>
                        <option value="concise">Concise & Direct</option>
                        <option value="lyrical">Lyrical & Poetic</option>
                        <option value="conversational">Conversational</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Assistance Level</label>
                      <select 
                        value={aiSettings.assistanceLevel}
                        onChange={(e) => setAiSettings({...aiSettings, assistanceLevel: e.target.value})}
                        className="w-full p-2 border border-border rounded-md bg-background"
                      >
                        <option value="minimal">Minimal - Only when asked</option>
                        <option value="balanced">Balanced - Regular suggestions</option>
                        <option value="active">Active - Frequent assistance</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Creativity Level: {aiSettings.creativityLevel}%</label>
                      <input 
                        type="range" 
                        min="0" 
                        max="100" 
                        value={aiSettings.creativityLevel}
                        onChange={(e) => setAiSettings({...aiSettings, creativityLevel: parseInt(e.target.value)})}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>Conservative</span>
                        <span>Creative</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Focus Areas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {[
                        { id: 'character-development', label: 'Character Development' },
                        { id: 'world-building', label: 'World Building' },
                        { id: 'dialogue', label: 'Dialogue Enhancement' },
                        { id: 'pacing', label: 'Pacing & Structure' },
                        { id: 'description', label: 'Descriptive Writing' },
                        { id: 'consistency', label: 'Consistency Checking' }
                      ].map((area) => (
                        <div key={area.id} className="flex items-center gap-2">
                          <input 
                            type="checkbox" 
                            id={area.id}
                            checked={aiSettings.focusAreas.includes(area.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setAiSettings({
                                  ...aiSettings, 
                                  focusAreas: [...aiSettings.focusAreas, area.id]
                                });
                              } else {
                                setAiSettings({
                                  ...aiSettings, 
                                  focusAreas: aiSettings.focusAreas.filter(f => f !== area.id)
                                });
                              }
                            }}
                            className="rounded border-border"
                          />
                          <label htmlFor={area.id} className="text-sm">{area.label}</label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Real-time Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium">Consistency Checking</span>
                        <p className="text-xs text-muted-foreground">Auto-detect plot holes and inconsistencies</p>
                      </div>
                      <input 
                        type="checkbox" 
                        checked={aiSettings.consistencyChecking}
                        onChange={(e) => setAiSettings({...aiSettings, consistencyChecking: e.target.checked})}
                        className="rounded border-border"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium">Real-time Analysis</span>
                        <p className="text-xs text-muted-foreground">Live feedback as you write</p>
                      </div>
                      <input 
                        type="checkbox" 
                        checked={aiSettings.realTimeAnalysis}
                        onChange={(e) => setAiSettings({...aiSettings, realTimeAnalysis: e.target.checked})}
                        className="rounded border-border"
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Configuration Preview</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="p-3 rounded bg-accent text-sm">
                      <strong>Style:</strong> {aiSettings.writingStyle} writing with {aiSettings.assistanceLevel} assistance
                    </div>
                    <div className="p-3 rounded bg-accent text-sm">
                      <strong>Focus:</strong> {aiSettings.focusAreas.length} areas selected
                    </div>
                    <div className="p-3 rounded bg-accent text-sm">
                      <strong>Creativity:</strong> {aiSettings.creativityLevel}% creative freedom
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-primary/50 bg-primary/5">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="w-5 h-5 text-primary" />
                      <span className="font-medium">Ready to Create Project</span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Your project is configured and ready to be created with all the AI tools and 
                      settings you've selected.
                    </p>
                    <Button className="w-full">
                      <Sparkles className="w-4 h-4 mr-2" />
                      Create Project & Start Writing
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex items-center justify-between pt-6 border-t border-border">
              <Button variant="outline" onClick={() => setActiveTab('structure')}>
                Back to Structure
              </Button>
              <Button>
                Complete Setup
                <CheckCircle className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
