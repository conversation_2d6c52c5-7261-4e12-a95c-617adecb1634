"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Settings, 
  BookOpen, 
  Target, 
  Palette,
  FileText,
  Users,
  Globe,
  Sparkles,
  CheckCircle,
  Clock,
  Plus,
  Edit,
  Save,
  Wand2,
  Brain,
  Lightbulb,
  Star,
  Zap,
  Crown,
  Heart,
  Sword,
  Shield,
  ChevronRight
} from "lucide-react";

const projectTemplates = [
  {
    id: 'fantasy-epic',
    name: 'Epic Fantasy',
    description: 'Multi-book fantasy series with complex world-building',
    features: ['Character Arcs', 'Magic Systems', 'World Building', 'Political Intrigue'],
    icon: Crown,
    color: 'purple'
  },
  {
    id: 'sci-fi-space',
    name: 'Space Opera',
    description: 'Galaxy-spanning adventure with advanced technology',
    features: ['Technology Systems', 'Alien Cultures', 'Space Politics', 'Hero\'s Journey'],
    icon: Star,
    color: 'blue'
  },
  {
    id: 'mystery-thriller',
    name: 'Mystery Thriller',
    description: 'Suspenseful mystery with complex plot twists',
    features: ['Clue Tracking', 'Character Motives', 'Timeline Management', 'Red Herrings'],
    icon: Lightbulb,
    color: 'amber'
  },
  {
    id: 'romance-contemporary',
    name: 'Contemporary Romance',
    description: 'Character-driven romance in modern settings',
    features: ['Relationship Arcs', 'Emotional Beats', 'Character Chemistry', 'Conflict Resolution'],
    icon: Heart,
    color: 'pink'
  },
  {
    id: 'custom',
    name: 'Custom Project',
    description: 'Start from scratch with your own structure',
    features: ['Flexible Setup', 'Custom Templates', 'Personalized Workflow', 'Adaptive Tools'],
    icon: Wand2,
    color: 'green'
  }
];

const setupSteps = [
  { id: 1, title: 'Project Basics', completed: true },
  { id: 2, title: 'Genre & Style', completed: true },
  { id: 3, title: 'Structure Setup', completed: false },
  { id: 4, title: 'AI Configuration', completed: false },
  { id: 5, title: 'Final Review', completed: false }
];

const genreOptions = [
  'Fantasy', 'Science Fiction', 'Mystery', 'Romance', 'Thriller', 'Horror',
  'Historical Fiction', 'Literary Fiction', 'Young Adult', 'Children\'s',
  'Non-Fiction', 'Biography', 'Self-Help', 'Other'
];

const toneOptions = [
  'Epic & Grand', 'Dark & Gritty', 'Light & Humorous', 'Mysterious & Suspenseful',
  'Romantic & Emotional', 'Action-Packed', 'Contemplative', 'Satirical'
];

export function DemoProjectSetupEnhanced() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>('fantasy-epic');
  const [currentStep, setCurrentStep] = useState(2);
  const [activeTab, setActiveTab] = useState("template");
  const [projectData, setProjectData] = useState({
    title: 'The Crystal Saga',
    description: 'An epic fantasy series following Aria Moonwhisper as she discovers her crystal magic powers and faces the ancient Shadow King.',
    genre: 'Fantasy',
    tone: 'Epic & Grand',
    targetLength: '80000',
    chapters: '18',
    series: true,
    seriesBooks: '3'
  });

  const [aiSettings, setAiSettings] = useState({
    writingStyle: 'descriptive',
    assistanceLevel: 'balanced',
    focusAreas: ['character-development', 'world-building', 'dialogue'],
    creativityLevel: 75,
    consistencyChecking: true,
    realTimeAnalysis: true
  });

  const currentTemplate = projectTemplates.find(t => t.id === selectedTemplate);
  const progressPercentage = (currentStep / setupSteps.length) * 100;

  return (
    <div className="w-full h-full bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-center gap-2">
              <Settings className="w-6 h-6 text-primary" />
              <h2 className="text-2xl font-bold">AI-Guided Project Setup</h2>
              <Badge variant="outline" className="border-primary/50 text-primary">
                Interactive Demo
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Save className="w-4 h-4 mr-2" />
              Save Progress
            </Button>
            <Button variant="outline" size="sm">
              <Brain className="w-4 h-4 mr-2" />
              AI Suggestions
            </Button>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Setup Progress</span>
            <span className="text-sm text-muted-foreground">Step {currentStep} of {setupSteps.length}</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          
          <div className="flex items-center justify-between mt-4">
            {setupSteps.map((step) => (
              <div key={step.id} className="flex items-center gap-2">
                <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center ${
                  step.completed ? 'bg-primary border-primary' :
                  step.id === currentStep ? 'bg-primary/20 border-primary' :
                  'bg-background border-border'
                }`}>
                  {step.completed ? (
                    <CheckCircle className="w-4 h-4 text-white" />
                  ) : (
                    <span className={`text-sm ${step.id === currentStep ? 'text-primary' : 'text-muted-foreground'}`}>
                      {step.id}
                    </span>
                  )}
                </div>
                <span className={`text-sm ${step.id === currentStep ? 'font-medium' : 'text-muted-foreground'}`}>
                  {step.title}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full max-w-2xl lg:max-w-3xl xl:max-w-4xl grid-cols-4">
            <TabsTrigger value="template">Template</TabsTrigger>
            <TabsTrigger value="details">Project Details</TabsTrigger>
            <TabsTrigger value="structure">Structure</TabsTrigger>
            <TabsTrigger value="ai-config">AI Config</TabsTrigger>
          </TabsList>

          <TabsContent value="template" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Choose Your Project Template</h3>
              <p className="text-muted-foreground mb-6">
                Select a template that matches your story type. Each template comes with pre-configured 
                tools, structures, and AI settings optimized for that genre.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
              {projectTemplates.map((template) => {
                const IconComponent = template.icon;
                return (
                  <Card 
                    key={template.id}
                    className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                      selectedTemplate === template.id 
                        ? 'border-primary/50 bg-primary/10 shadow-lg' 
                        : 'hover:border-primary/30'
                    }`}
                    onClick={() => setSelectedTemplate(template.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <div className={`p-3 rounded-lg bg-${template.color}-500/20`}>
                          <IconComponent className={`w-6 h-6 text-${template.color}-500`} />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                          {selectedTemplate === template.id && (
                            <Badge variant="default" className="text-xs mt-1">
                              Selected
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <p className="text-sm text-muted-foreground">{template.description}</p>
                      
                      <div>
                        <p className="text-sm font-medium mb-2">Included Features:</p>
                        <div className="flex flex-wrap gap-1">
                          {template.features.map((feature, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {selectedTemplate === template.id && (
                        <div className="pt-3 border-t border-border">
                          <Button size="sm" className="w-full">
                            <Zap className="w-4 h-4 mr-2" />
                            Configure This Template
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {currentTemplate && (
              <Card className="border-primary/50 bg-primary/5">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <currentTemplate.icon className="w-5 h-5 text-primary" />
                    {currentTemplate.name} Template Selected
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    This template will set up your project with specialized tools and AI configurations 
                    optimized for {currentTemplate.name.toLowerCase()} writing.
                  </p>
                  <Button onClick={() => setActiveTab('details')}>
                    Continue to Project Details
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="details" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Project Details</h3>
              <p className="text-muted-foreground mb-6">
                Tell us about your story so we can customize the AI tools and workspace for your needs.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5" />
                      Basic Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Project Title</label>
                      <Input 
                        value={projectData.title}
                        onChange={(e) => setProjectData({...projectData, title: e.target.value})}
                        placeholder="Enter your book/series title"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Description</label>
                      <Textarea 
                        value={projectData.description}
                        onChange={(e) => setProjectData({...projectData, description: e.target.value})}
                        placeholder="Brief description of your story"
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Genre</label>
                        <select 
                          value={projectData.genre}
                          onChange={(e) => setProjectData({...projectData, genre: e.target.value})}
                          className="w-full p-2 border border-border rounded-md bg-background"
                        >
                          {genreOptions.map(genre => (
                            <option key={genre} value={genre}>{genre}</option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-2 block">Tone</label>
                        <select 
                          value={projectData.tone}
                          onChange={(e) => setProjectData({...projectData, tone: e.target.value})}
                          className="w-full p-2 border border-border rounded-md bg-background"
                        >
                          {toneOptions.map(tone => (
                            <option key={tone} value={tone}>{tone}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Structure & Goals
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Target Word Count</label>
                        <Input 
                          value={projectData.targetLength}
                          onChange={(e) => setProjectData({...projectData, targetLength: e.target.value})}
                          placeholder="80000"
                          type="number"
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-2 block">Planned Chapters</label>
                        <Input 
                          value={projectData.chapters}
                          onChange={(e) => setProjectData({...projectData, chapters: e.target.value})}
                          placeholder="18"
                          type="number"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="series"
                          checked={projectData.series}
                          onChange={(e) => setProjectData({...projectData, series: e.target.checked})}
                          className="rounded border-border"
                        />
                        <label htmlFor="series" className="text-sm font-medium">
                          This is part of a series
                        </label>
                      </div>

                      {projectData.series && (
                        <div>
                          <label className="text-sm font-medium mb-2 block">Number of Books in Series</label>
                          <Input 
                            value={projectData.seriesBooks}
                            onChange={(e) => setProjectData({...projectData, seriesBooks: e.target.value})}
                            placeholder="3"
                            type="number"
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="w-5 h-5" />
                      AI-Generated Suggestions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 rounded bg-primary/10 border border-primary/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Sparkles className="w-4 h-4 text-primary" />
                        <span className="text-sm font-medium">Story Structure Suggestion</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Based on your epic fantasy genre, consider using the Hero's Journey structure 
                        with a three-act format. This works well for character growth and world-building.
                      </p>
                    </div>

                    <div className="p-4 rounded bg-blue-500/10 border border-blue-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Lightbulb className="w-4 h-4 text-blue-500" />
                        <span className="text-sm font-medium">Chapter Length Tip</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        For an 80,000-word book with 18 chapters, aim for 4,400 words per chapter. 
                        Vary between 3,000-6,000 for better pacing.
                      </p>
                    </div>

                    <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="w-4 h-4 text-green-500" />
                        <span className="text-sm font-medium">Character Development</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Epic fantasy benefits from detailed character arcs. Consider creating 
                        character sheets for your main cast and tracking their growth.
                      </p>
                    </div>

                    <Button variant="outline" size="sm" className="w-full">
                      <Plus className="w-4 h-4 mr-2" />
                      Get More AI Suggestions
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      Project Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Estimated completion:</span>
                        <span className="text-sm font-medium">6-8 months</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Recommended daily goal:</span>
                        <span className="text-sm font-medium">500-750 words</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Writing sessions per week:</span>
                        <span className="text-sm font-medium">5-6 sessions</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex items-center justify-between pt-6 border-t border-border">
              <Button variant="outline" onClick={() => setActiveTab('template')}>
                Back to Templates
              </Button>
              <Button onClick={() => setActiveTab('structure')}>
                Continue to Structure Setup
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="structure" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Project Structure Setup</h3>
              <p className="text-muted-foreground mb-6">
                Configure how your project will be organized and what tools you'll have access to.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Workspace Layout</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border-2 border-dashed border-border rounded-lg">
                    <div className="text-center text-muted-foreground">
                      <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Interactive workspace preview</p>
                      <p className="text-sm">Shows editor, sidebar panels, and tool arrangement</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Panel Configuration</label>
                    <div className="space-y-2">
                      {['Story Bible', 'AI Assistant', 'Chapter Outline', 'Character Tracker'].map((panel) => (
                        <div key={panel} className="flex items-center gap-2">
                          <input type="checkbox" defaultChecked className="rounded border-border" />
                          <span className="text-sm">{panel}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Chapter Organization</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {Array.from({length: 6}, (_, i) => (
                      <div key={i} className="flex items-center justify-between p-2 rounded border">
                        <span className="text-sm">Chapter {i + 1}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {i < 2 ? 'Complete' : i < 4 ? 'In Progress' : 'Planned'}
                          </Badge>
                          <Button variant="ghost" size="sm">
                            <Edit className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    <div className="text-center">
                      <Button variant="outline" size="sm">
                        <Plus className="w-4 h-4 mr-2" />
                        Add More Chapters
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex items-center justify-between pt-6 border-t border-border">
              <Button variant="outline" onClick={() => setActiveTab('details')}>
                Back to Details
              </Button>
              <Button onClick={() => setActiveTab('ai-config')}>
                Continue to AI Configuration
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="ai-config" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">AI Assistant Configuration</h3>
              <p className="text-muted-foreground mb-6">
                Customize how the AI will assist you throughout your writing process.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Writing Style Preferences</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Writing Style</label>
                      <select 
                        value={aiSettings.writingStyle}
                        onChange={(e) => setAiSettings({...aiSettings, writingStyle: e.target.value})}
                        className="w-full p-2 border border-border rounded-md bg-background"
                      >
                        <option value="descriptive">Descriptive & Rich</option>
                        <option value="concise">Concise & Direct</option>
                        <option value="lyrical">Lyrical & Poetic</option>
                        <option value="conversational">Conversational</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Assistance Level</label>
                      <select 
                        value={aiSettings.assistanceLevel}
                        onChange={(e) => setAiSettings({...aiSettings, assistanceLevel: e.target.value})}
                        className="w-full p-2 border border-border rounded-md bg-background"
                      >
                        <option value="minimal">Minimal - Only when asked</option>
                        <option value="balanced">Balanced - Regular suggestions</option>
                        <option value="active">Active - Frequent assistance</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Creativity Level: {aiSettings.creativityLevel}%</label>
                      <input 
                        type="range" 
                        min="0" 
                        max="100" 
                        value={aiSettings.creativityLevel}
                        onChange={(e) => setAiSettings({...aiSettings, creativityLevel: parseInt(e.target.value)})}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>Conservative</span>
                        <span>Creative</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Focus Areas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {[
                        { id: 'character-development', label: 'Character Development' },
                        { id: 'world-building', label: 'World Building' },
                        { id: 'dialogue', label: 'Dialogue Enhancement' },
                        { id: 'pacing', label: 'Pacing & Structure' },
                        { id: 'description', label: 'Descriptive Writing' },
                        { id: 'consistency', label: 'Consistency Checking' }
                      ].map((area) => (
                        <div key={area.id} className="flex items-center gap-2">
                          <input 
                            type="checkbox" 
                            id={area.id}
                            checked={aiSettings.focusAreas.includes(area.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setAiSettings({
                                  ...aiSettings, 
                                  focusAreas: [...aiSettings.focusAreas, area.id]
                                });
                              } else {
                                setAiSettings({
                                  ...aiSettings, 
                                  focusAreas: aiSettings.focusAreas.filter(f => f !== area.id)
                                });
                              }
                            }}
                            className="rounded border-border"
                          />
                          <label htmlFor={area.id} className="text-sm">{area.label}</label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Real-time Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium">Consistency Checking</span>
                        <p className="text-xs text-muted-foreground">Auto-detect plot holes and inconsistencies</p>
                      </div>
                      <input 
                        type="checkbox" 
                        checked={aiSettings.consistencyChecking}
                        onChange={(e) => setAiSettings({...aiSettings, consistencyChecking: e.target.checked})}
                        className="rounded border-border"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium">Real-time Analysis</span>
                        <p className="text-xs text-muted-foreground">Live feedback as you write</p>
                      </div>
                      <input 
                        type="checkbox" 
                        checked={aiSettings.realTimeAnalysis}
                        onChange={(e) => setAiSettings({...aiSettings, realTimeAnalysis: e.target.checked})}
                        className="rounded border-border"
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Configuration Preview</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="p-3 rounded bg-accent text-sm">
                      <strong>Style:</strong> {aiSettings.writingStyle} writing with {aiSettings.assistanceLevel} assistance
                    </div>
                    <div className="p-3 rounded bg-accent text-sm">
                      <strong>Focus:</strong> {aiSettings.focusAreas.length} areas selected
                    </div>
                    <div className="p-3 rounded bg-accent text-sm">
                      <strong>Creativity:</strong> {aiSettings.creativityLevel}% creative freedom
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-primary/50 bg-primary/5">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="w-5 h-5 text-primary" />
                      <span className="font-medium">Ready to Create Project</span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Your project is configured and ready to be created with all the AI tools and 
                      settings you've selected.
                    </p>
                    <Button className="w-full">
                      <Sparkles className="w-4 h-4 mr-2" />
                      Create Project & Start Writing
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex items-center justify-between pt-6 border-t border-border">
              <Button variant="outline" onClick={() => setActiveTab('structure')}>
                Back to Structure
              </Button>
              <Button>
                Complete Setup
                <CheckCircle className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
