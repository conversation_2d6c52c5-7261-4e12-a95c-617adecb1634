import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'

interface WritingSession {
  id: string
  project_id: string
  start_time: string
  end_time: string
  words_written: number
  mode?: string
  actions?: Record<string, number>
}

interface BehavioralData {
  peakHours: Array<{ hour: number; productivity: number }>
  writingModes: Array<{ mode: string; percentage: number }>
  frequentActions: Array<{ action: string; count: number }>
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    const timeframe = searchParams.get('timeframe') || '30days'

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    
    switch (timeframe) {
      case '7days':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30days':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90days':
        startDate.setDate(endDate.getDate() - 90)
        break
      default:
        startDate.setDate(endDate.getDate() - 30)
    }

    // Build query for writing sessions
    let query = supabase
      .from('writing_sessions')
      .select('*')
      .eq('user_id', user.id)
      .gte('start_time', startDate.toISOString())
      .lte('start_time', endDate.toISOString())
      .order('start_time', { ascending: false })

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    const { data: sessions, error } = await query

    if (error) {
      logger.error('Error fetching writing sessions:', error)
      return NextResponse.json({ error: 'Failed to fetch behavioral data' }, { status: 500 })
    }

    // Analyze behavioral patterns
    const behavioralData = analyzeBehavioralPatterns(sessions || [])

    return NextResponse.json(behavioralData)
  } catch (error) {
    logger.error('Error in behavioral analytics GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

function analyzeBehavioralPatterns(sessions: WritingSession[]): BehavioralData {
  // Initialize data structures
  const hourlyProductivity = new Map<number, { totalWords: number; sessionCount: number }>()
  const writingModes = new Map<string, number>()
  const actionCounts = new Map<string, number>()

  // Initialize all hours
  for (let hour = 0; hour < 24; hour++) {
    hourlyProductivity.set(hour, { totalWords: 0, sessionCount: 0 })
  }

  // Process sessions
  sessions.forEach(session => {
    // Analyze peak hours
    const startTime = new Date(session.start_time)
    const hour = startTime.getHours()
    const hourData = hourlyProductivity.get(hour) || { totalWords: 0, sessionCount: 0 }
    hourData.totalWords += session.words_written || 0
    hourData.sessionCount++
    hourlyProductivity.set(hour, hourData)

    // Analyze writing modes
    const mode = session.mode || 'Standard Writing'
    writingModes.set(mode, (writingModes.get(mode) || 0) + 1)

    // Analyze actions
    if (session.actions) {
      Object.entries(session.actions).forEach(([action, count]) => {
        actionCounts.set(action, (actionCounts.get(action) || 0) + count)
      })
    }
  })

  // Calculate peak hours productivity
  const maxWordsPerHour = Math.max(...Array.from(hourlyProductivity.values()).map(d => d.totalWords))
  const peakHours = Array.from(hourlyProductivity.entries())
    .map(([hour, data]) => ({
      hour,
      productivity: maxWordsPerHour > 0 ? Math.round((data.totalWords / maxWordsPerHour) * 100) : 0
    }))
    .filter(h => h.productivity > 0) // Only include hours with activity
    .sort((a, b) => b.productivity - a.productivity)

  // Calculate writing mode percentages
  const totalSessions = sessions.length
  const writingModesArray = Array.from(writingModes.entries())
    .map(([mode, count]) => ({
      mode,
      percentage: Math.round((count / totalSessions) * 100)
    }))
    .sort((a, b) => b.percentage - a.percentage)

  // Add default modes if not present
  const defaultModes = ['Sprint Writing', 'Deep Focus', 'Editing', 'Planning']
  defaultModes.forEach(mode => {
    if (!writingModesArray.find(m => m.mode === mode)) {
      writingModesArray.push({ mode, percentage: 0 })
    }
  })

  // Sort frequent actions
  const frequentActions = Array.from(actionCounts.entries())
    .map(([action, count]) => ({ action, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10) // Top 10 actions

  // Add default actions if empty
  if (frequentActions.length === 0) {
    frequentActions.push(
      { action: 'Words Written', count: sessions.reduce((sum, s) => sum + (s.words_written || 0), 0) },
      { action: 'Sessions Started', count: sessions.length },
      { action: 'Characters Created', count: 0 },
      { action: 'Chapters Completed', count: 0 }
    )
  }

  return {
    peakHours: peakHours.slice(0, 24), // All active hours
    writingModes: writingModesArray.slice(0, 4), // Top 4 modes
    frequentActions
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { projectId, startTime, endTime, wordsWritten, mode, actions } = body

    if (!projectId || !startTime) {
      return NextResponse.json({ 
        error: 'Project ID and start time are required' 
      }, { status: 400 })
    }

    // Create or update writing session
    const sessionData = {
      user_id: user.id,
      project_id: projectId,
      start_time: startTime,
      end_time: endTime || new Date().toISOString(),
      words_written: wordsWritten || 0,
      mode: mode || 'Standard Writing',
      actions: actions || {}
    }

    const { data, error } = await supabase
      .from('writing_sessions')
      .insert(sessionData)
      .select()
      .single()

    if (error) {
      logger.error('Error creating writing session:', error)
      return NextResponse.json({ 
        error: 'Failed to create writing session' 
      }, { status: 500 })
    }

    return NextResponse.json({ session: data })
  } catch (error) {
    logger.error('Error in behavioral analytics POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}