import { NextRequest, NextResponse } from 'next/server';
import { handleAPIError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const { 
      universe_id,
      event_name,
      description,
      event_date,
      relative_date,
      event_type,
      importance,
      affected_series
    } = body;

    if (!universe_id || !event_name) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Verify user owns this universe
    const { data: universe, error: universeError } = await supabase
      .from('universes')
      .select('created_by')
      .eq('id', universe_id)
      .single();

    if (universeError || !universe || universe.created_by !== user.user.id) {
      return handleAPIError(new NotFoundError('Resource'));
    }

    // Create the timeline event
    const { data: event, error } = await supabase
      .from('universe_timeline_events')
      .insert({
        universe_id,
        event_name,
        description,
        event_date,
        relative_date,
        event_type: event_type || 'historical',
        importance: importance || 'major',
        affected_series: affected_series || []
      })
      .select()
      .single();

    if (error) {
      logger.error('Error creating timeline event:', error);
      return UnifiedResponse.error({
        message: 'Failed to create timeline event',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500);
    }

    return UnifiedResponse.created({ event });
  } catch (error) {
    logger.error('Error in POST /api/universes/timeline-events:', error);
    return UnifiedResponse.error({
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      details: error
    }, undefined, 500);
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    const { searchParams } = new URL(request.url);
    const universeId = searchParams.get('universe_id');

    if (!universeId) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Verify user has access to this universe
    const { data: universe, error: universeError } = await supabase
      .from('universes')
      .select('created_by')
      .eq('id', universeId)
      .single();

    if (universeError) {
      return handleAPIError(new NotFoundError('Resource'));
    }

    // Check if user owns the universe or has a series in it
    const hasAccess = universe.created_by === user.user.id || 
      await checkUserHasSeriesInUniverse(supabase, user.user.id, universeId);

    if (!hasAccess) {
      return handleAPIError(new AuthorizationError());
    }

    // Get timeline events
    const { data: events, error } = await supabase
      .from('universe_timeline_events')
      .select('*')
      .eq('universe_id', universeId)
      .order('event_date', { ascending: true, nullsFirst: false })
      .order('created_at', { ascending: true });

    if (error) {
      logger.error('Error fetching timeline events:', error);
      return UnifiedResponse.error({
        message: 'Failed to fetch timeline events',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500);
    }

    return UnifiedResponse.success({ events });
  } catch (error) {
    logger.error('Error in GET /api/universes/timeline-events:', error);
    return UnifiedResponse.error({
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      details: error
    }, undefined, 500);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const { id, ...updates } = body;

    if (!id) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Verify user owns the universe this event belongs to
    const { data: event, error: eventError } = await supabase
      .from('universe_timeline_events')
      .select('universe_id')
      .eq('id', id)
      .single();

    if (eventError || !event) {
      return handleAPIError(new NotFoundError('Resource'));
    }

    const { data: universe } = await supabase
      .from('universes')
      .select('created_by')
      .eq('id', event.universe_id)
      .single();

    if (!universe || universe.created_by !== user.user.id) {
      return handleAPIError(new AuthorizationError());
    }

    // Update the event
    const { data: updatedEvent, error: updateError } = await supabase
      .from('universe_timeline_events')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      logger.error('Error updating timeline event:', updateError);
      return UnifiedResponse.error({
        message: 'Failed to update timeline event',
        code: 'DATABASE_ERROR',
        details: updateError
      }, undefined, 500);
    }

    return UnifiedResponse.success({ event: updatedEvent });
  } catch (error) {
    logger.error('Error in PUT /api/universes/timeline-events:', error);
    return UnifiedResponse.error({
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      details: error
    }, undefined, 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const { id } = body;

    if (!id) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Verify user owns the universe this event belongs to
    const { data: event, error: eventError } = await supabase
      .from('universe_timeline_events')
      .select('universe_id')
      .eq('id', id)
      .single();

    if (eventError || !event) {
      return handleAPIError(new NotFoundError('Resource'));
    }

    const { data: universe } = await supabase
      .from('universes')
      .select('created_by')
      .eq('id', event.universe_id)
      .single();

    if (!universe || universe.created_by !== user.user.id) {
      return handleAPIError(new AuthorizationError());
    }

    // Delete the event
    const { error: deleteError } = await supabase
      .from('universe_timeline_events')
      .delete()
      .eq('id', id);

    if (deleteError) {
      logger.error('Error deleting timeline event:', deleteError);
      return UnifiedResponse.error({
        message: 'Failed to delete timeline event',
        code: 'DATABASE_ERROR',
        details: deleteError
      }, undefined, 500);
    }

    return UnifiedResponse.noContent();
  } catch (error) {
    logger.error('Error in DELETE /api/universes/timeline-events:', error);
    return UnifiedResponse.error({
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      details: error
    }, undefined, 500);
  }
}

async function checkUserHasSeriesInUniverse(
  supabase: ReturnType<typeof createTypedServerClient>,
  userId: string,
  universeId: string
): Promise<boolean> {
  const { data } = await supabase
    .from('series')
    .select('id')
    .eq('user_id', userId)
    .eq('universe_id', universeId)
    .limit(1);

  return data && data.length > 0;
}