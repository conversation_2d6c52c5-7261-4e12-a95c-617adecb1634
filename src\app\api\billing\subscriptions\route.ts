import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { stripe } from '@/lib/stripe'
import { SUBSCRIPTION_TIERS, getUserTier } from '@/lib/subscription'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
<<<<<<< HEAD
import { UnifiedResponse } from '@/lib/api/unified-response'
import { createServerClient } from '@/lib/supabase'
=======
import { createTypedServerClient } from '@/lib/supabase'
>>>>>>> dd974a56edaaeb3bb72610137e1e155511ba61d9
import { z } from 'zod'

// Validation schemas
const subscriptionActionSchema = z.object({
  tierId: z.string(),
  action: z.enum(['create_subscription', 'cancel_subscription', 'update_subscription', 'reactivate_subscription'])
})

export async function GET(request: NextRequest) {
  try {
    const authResult = await UnifiedAuthService.authenticateUser(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }
    const user = authResult
    const supabase = await createTypedServerClient()

    // Get user's current subscription
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single()

    // Get usage data
    const { data: usage } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('period_start', new Date().toISOString().slice(0, 7)) // Current month
      .single()

    const currentTier = getUserTier(subscription)
    
    return NextResponse.json({
      subscription,
      currentTier,
      usage: usage || {
        ai_generations: 0,
        projects: 0
      },
      availableTiers: SUBSCRIPTION_TIERS
    })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }
    const user = authResult
    const supabase = await createTypedServerClient()

    const body = await request.json()
    
    // Validate request body
    const validationResult = subscriptionActionSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validationResult.error.errors 
      }, { status: 400 })
    }
    
    const { tierId, action } = validationResult.data

    if (action === 'create_subscription') {
      const tier = SUBSCRIPTION_TIERS.find(t => t.id === tierId)
      if (!tier || tier.id === 'free') {
        return handleAPIError(new ValidationError('Invalid request'))
      }

      // Get or create Stripe customer
      const { data: profile } = await supabase
        .from('profiles')
        .select('stripe_customer_id')
        .eq('id', user.id)
        .single()

      let customerId = profile?.stripe_customer_id

      if (!customerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          metadata: {
            userId: user.id
          }
        })
        customerId = customer.id

        await supabase
          .from('profiles')
          .upsert({
            id: user.id,
            stripe_customer_id: customerId
          })
      }

      // Create checkout session
      const session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: tier.stripePriceId,
            quantity: 1
          }
        ],
        mode: 'subscription',
        success_url: `${request.nextUrl.origin}/dashboard?subscription=success`,
        cancel_url: `${request.nextUrl.origin}/pricing?subscription=canceled`,
        metadata: {
          userId: user.id,
          tierId: tier.id
        }
      })

      return NextResponse.json({ sessionUrl: session.url })
    }

    if (action === 'cancel_subscription') {
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('stripe_subscription_id')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single()

      if (!subscription?.stripe_subscription_id) {
        return UnifiedResponse.error('No active subscription found', 404);
      }

      // Cancel at period end
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        cancel_at_period_end: true
      })

      // Update local record
      await supabase
        .from('user_subscriptions')
        .update({ cancel_at_period_end: true })
        .eq('user_id', user.id)
        .eq('status', 'active')

      return NextResponse.json({ success: true })
    }

    if (action === 'reactivate_subscription') {
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('stripe_subscription_id')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single()

      if (!subscription?.stripe_subscription_id) {
        return UnifiedResponse.error('No active subscription found', 404);
      }

      // Reactivate subscription
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        cancel_at_period_end: false
      })

      // Update local record
      await supabase
        .from('user_subscriptions')
        .update({ cancel_at_period_end: false })
        .eq('user_id', user.id)
        .eq('status', 'active')

      return NextResponse.json({ success: true })
    }

    return handleAPIError(new ValidationError('Invalid request'))
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}