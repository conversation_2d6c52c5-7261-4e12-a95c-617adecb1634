'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle2, XCircle, Users, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { logger } from '@/lib/services/logger'
import { useToast } from '@/hooks/use-toast'

interface InvitationDetails {
  id: string
  project_id: string
  project_title: string
  inviter_name: string
  inviter_email: string
  role: 'editor' | 'viewer'
  status: 'pending' | 'accepted' | 'expired'
  expires_at: string
  created_at: string
}

export default function InvitationAcceptPage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string
  
  const [loading, setLoading] = useState(true)
  const [accepting, setAccepting] = useState(false)
  const [invitation, setInvitation] = useState<InvitationDetails | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [accepted, setAccepted] = useState(false)
  
  const supabase = createClient()
  const { toast } = useToast()
  
  useEffect(() => {
    loadInvitation()
  }, [token])
  
  const loadInvitation = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Verify invitation token
      const { data: inviteData, error: inviteError } = await supabase
        .from('project_invitations')
        .select(`
          *,
          project:projects(title),
          inviter:users!inviter_id(email, profiles(display_name))
        `)
        .eq('token', token)
        .single()
      
      if (inviteError || !inviteData) {
        setError('Invalid or expired invitation link')
        return
      }
      
      // Check if invitation is expired
      if (new Date(inviteData.expires_at) < new Date()) {
        setError('This invitation has expired')
        return
      }
      
      // Check if already accepted
      if (inviteData.status === 'accepted') {
        setError('This invitation has already been accepted')
        return
      }
      
      setInvitation({
        id: inviteData.id,
        project_id: inviteData.project_id,
        project_title: inviteData.project.title,
        inviter_name: inviteData.inviter.profiles?.display_name || inviteData.inviter.email,
        inviter_email: inviteData.inviter.email,
        role: inviteData.role,
        status: inviteData.status,
        expires_at: inviteData.expires_at,
        created_at: inviteData.created_at
      })
      
    } catch (err) {
      logger.error('Error loading invitation', err)
      setError('Failed to load invitation details')
    } finally {
      setLoading(false)
    }
  }
  
  const acceptInvitation = async () => {
    if (!invitation) return
    
    try {
      setAccepting(true)
      
      // Check if user is authenticated
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        // Store invitation token and redirect to login
        localStorage.setItem('pendingInvitation', token)
        router.push('/login?redirect=/invite/' + token)
        return
      }
      
      // Accept the invitation
      const { error: acceptError } = await supabase
        .from('project_invitations')
        .update({ 
          status: 'accepted',
          accepted_at: new Date().toISOString(),
          user_id: user.id
        })
        .eq('token', token)
      
      if (acceptError) throw acceptError
      
      // Add user as collaborator
      const { error: collabError } = await supabase
        .from('project_collaborators')
        .insert({
          project_id: invitation.project_id,
          user_id: user.id,
          role: invitation.role,
          status: 'active',
          invited_by: invitation.inviter_email
        })
      
      if (collabError) throw collabError
      
      setAccepted(true)
      
      toast({
        title: "Invitation accepted!",
        description: `You now have ${invitation.role} access to "${invitation.project_title}"`,
      })
      
      // Redirect to project after 2 seconds
      setTimeout(() => {
        router.push(`/projects/${invitation.project_id}`)
      }, 2000)
      
    } catch (err) {
      logger.error('Error accepting invitation', err)
      toast({
        title: "Error",
        description: "Failed to accept invitation. Please try again.",
        variant: "destructive"
      })
    } finally {
      setAccepting(false)
    }
  }
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <p className="text-muted-foreground">Loading invitation...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <XCircle className="h-5 w-5" />
              Invalid Invitation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <div className="mt-6">
              <Link href="/">
                <Button variant="outline" className="w-full">
                  Go to Home
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (accepted) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <CheckCircle2 className="h-12 w-12 text-success" />
              <h2 className="text-xl font-semibold">Invitation Accepted!</h2>
              <p className="text-center text-muted-foreground">
                You now have {invitation?.role} access to "{invitation?.project_title}"
              </p>
              <p className="text-sm text-muted-foreground">
                Redirecting to project...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <CardTitle>You're Invited!</CardTitle>
          <CardDescription>
            {invitation?.inviter_name} has invited you to collaborate
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="rounded-lg border p-4 space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Project</p>
                <p className="font-medium">{invitation?.project_title}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Your Role</p>
                <p className="font-medium capitalize">{invitation?.role}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Invited By</p>
                <p className="font-medium">{invitation?.inviter_name}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">As {invitation?.role === 'editor' ? 'an' : 'a'} {invitation?.role}, you'll be able to:</h3>
              {invitation?.role === 'editor' ? (
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• View and edit all chapters</li>
                  <li>• Access the Story Bible</li>
                  <li>• Use AI-powered writing tools</li>
                  <li>• Track character development</li>
                  <li>• Export the manuscript</li>
                </ul>
              ) : (
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Read all chapters</li>
                  <li>• View the Story Bible</li>
                  <li>• Leave comments and feedback</li>
                  <li>• Export for offline reading</li>
                  <li>• View project analytics</li>
                </ul>
              )}
            </div>
            
            <Button 
              onClick={acceptInvitation}
              disabled={accepting}
              className="w-full"
              size="lg"
            >
              {accepting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Accepting...
                </>
              ) : (
                <>
                  Accept Invitation
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
            
            <p className="text-xs text-center text-muted-foreground">
              This invitation expires on {new Date(invitation?.expires_at || '').toLocaleDateString()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}