'use client'

import { useEffect, useCallback, useRef, useState } from 'react'
import { subscriptionManager, type SubscriptionConfig, type SubscriptionMetrics } from '@/lib/services/selective-subscription-manager'
import { useAuth } from '@/contexts/auth-context'
import { logger } from '@/lib/services/logger'

export interface UseSelectiveSubscriptionsOptions {
  projectId?: string
  chapterId?: string
  enablePresence?: boolean
  enableCollaboration?: boolean
  enableSessions?: boolean
  priority?: 'high' | 'medium' | 'low'
  autoCleanup?: boolean
}

export interface SelectiveSubscriptionState {
  isConnected: boolean
  activeSubscriptions: string[]
  metrics: SubscriptionMetrics[]
  errors: string[]
}

/**
 * Hook for managing selective real-time subscriptions with resource optimization
 */
export function useSelectiveSubscriptions(options: UseSelectiveSubscriptionsOptions = {}) {
  const {
    projectId,
    chapterId,
    enablePresence = true,
    enableCollaboration = true,
    enableSessions = true,
    priority = 'medium',
    autoCleanup = true
  } = options

  const { user } = useAuth()
  const [state, setState] = useState<SelectiveSubscriptionState>({
    isConnected: false,
    activeSubscriptions: [],
    metrics: [],
    errors: []
  })
  
  const subscriptionIds = useRef<Set<string>>(new Set())
  const eventListeners = useRef<Map<string, Function>>(new Map())

  // Subscribe to a specific resource
  const subscribe = useCallback(async (config: SubscriptionConfig) => {
    if (!user) {
      logger.warn('Cannot subscribe without authenticated user')
      return null
    }

    try {
      const subscriptionId = await subscriptionManager.subscribe(config)
      subscriptionIds.current.add(subscriptionId)
      
      // Update state
      setState(prev => ({
        ...prev,
        activeSubscriptions: Array.from(subscriptionIds.current),
        isConnected: subscriptionIds.current.size > 0
      }))
      
      return subscriptionId
    } catch (error) {
      logger.error('Failed to create subscription', error)
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, `Failed to subscribe: ${error instanceof Error ? error.message : 'Unknown error'}`]
      }))
      return null
    }
  }, [user])

  // Unsubscribe from a resource
  const unsubscribe = useCallback(async (subscriptionId: string) => {
    try {
      await subscriptionManager.unsubscribe(subscriptionId)
      subscriptionIds.current.delete(subscriptionId)
      
      // Update state
      setState(prev => ({
        ...prev,
        activeSubscriptions: Array.from(subscriptionIds.current),
        isConnected: subscriptionIds.current.size > 0
      }))
    } catch (error) {
      logger.error('Failed to unsubscribe', error)
    }
  }, [])

  // Subscribe to chapter updates
  const subscribeToChapter = useCallback(async (chapterId: string, customPriority?: 'high' | 'medium' | 'low') => {
    return subscribe({
      id: `chapter-${chapterId}-${user?.id}`,
      type: 'chapter',
      target: chapterId,
      priority: customPriority || priority,
      autoReconnect: true
    })
  }, [subscribe, user?.id, priority])

  // Subscribe to project-wide updates
  const subscribeToProject = useCallback(async (projectId: string, customPriority?: 'high' | 'medium' | 'low') => {
    return subscribe({
      id: `project-${projectId}-${user?.id}`,
      type: 'project',
      target: projectId,
      priority: customPriority || priority,
      autoReconnect: true
    })
  }, [subscribe, user?.id, priority])

  // Subscribe to presence updates
  const subscribeToPresence = useCallback(async (targetId: string, customPriority?: 'high' | 'medium' | 'low') => {
    return subscribe({
      id: `presence-${targetId}-${user?.id}`,
      type: 'presence',
      target: targetId,
      priority: customPriority || priority,
      autoReconnect: true
    })
  }, [subscribe, user?.id, priority])

  // Subscribe to collaboration updates
  const subscribeToCollaboration = useCallback(async (projectId: string, customPriority?: 'high' | 'medium' | 'low') => {
    return subscribe({
      id: `collaboration-${projectId}-${user?.id}`,
      type: 'collaboration',
      target: projectId,
      priority: customPriority || priority,
      autoReconnect: true
    })
  }, [subscribe, user?.id, priority])

  // Subscribe to writing sessions
  const subscribeToSessions = useCallback(async (projectId: string, customPriority?: 'high' | 'medium' | 'low') => {
    return subscribe({
      id: `sessions-${projectId}-${user?.id}`,
      type: 'session',
      target: projectId,
      priority: customPriority || priority,
      autoReconnect: true
    })
  }, [subscribe, user?.id, priority])

  // Add event listener
  const addEventListener = useCallback((event: string, callback: Function) => {
    subscriptionManager.on(event, callback)
    eventListeners.current.set(event, callback)
  }, [])

  // Remove event listener
  const removeEventListener = useCallback((event: string) => {
    const callback = eventListeners.current.get(event)
    if (callback) {
      subscriptionManager.off(event, callback)
      eventListeners.current.delete(event)
    }
  }, [])

  // Update subscription priority
  const updatePriority = useCallback((subscriptionId: string, newPriority: 'high' | 'medium' | 'low') => {
    subscriptionManager.updatePriority(subscriptionId, newPriority)
  }, [])

  // Get metrics
  const getMetrics = useCallback(() => {
    return subscriptionManager.getMetrics()
  }, [])

  // Cleanup all subscriptions
  const cleanup = useCallback(async () => {
    const subscriptions = Array.from(subscriptionIds.current)
    await Promise.all(subscriptions.map(id => unsubscribe(id)))
    
    // Remove all event listeners
    eventListeners.current.forEach((callback, event) => {
      subscriptionManager.off(event, callback)
    })
    eventListeners.current.clear()
  }, [unsubscribe])

  // Auto-subscribe based on options
  useEffect(() => {
    if (!user) return

    const setupSubscriptions = async () => {
      try {
        // Subscribe to chapter if provided
        if (chapterId) {
          await subscribeToChapter(chapterId)
        }

        // Subscribe to project if provided
        if (projectId) {
          await subscribeToProject(projectId)

          // Optional subscriptions
          if (enablePresence) {
            await subscribeToPresence(projectId)
          }

          if (enableCollaboration) {
            await subscribeToCollaboration(projectId)
          }

          if (enableSessions) {
            await subscribeToSessions(projectId)
          }
        }
      } catch (error) {
        logger.error('Failed to setup auto-subscriptions', error)
      }
    }

    setupSubscriptions()
  }, [
    user,
    projectId,
    chapterId,
    enablePresence,
    enableCollaboration,
    enableSessions,
    subscribeToChapter,
    subscribeToProject,
    subscribeToPresence,
    subscribeToCollaboration,
    subscribeToSessions
  ])

  // Update metrics periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setState(prev => ({
        ...prev,
        metrics: subscriptionManager.getMetrics(),
        activeSubscriptions: subscriptionManager.getActiveSubscriptions()
      }))
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autoCleanup) {
        cleanup()
      }
    }
  }, [cleanup, autoCleanup])

  // Auto-cleanup stale subscriptions
  useEffect(() => {
    if (!autoCleanup) return

    const cleanupInterval = setInterval(() => {
      subscriptionManager.cleanup()
    }, 10 * 60 * 1000) // Every 10 minutes

    return () => clearInterval(cleanupInterval)
  }, [autoCleanup])

  return {
    // State
    ...state,
    
    // Subscription management
    subscribe,
    unsubscribe,
    subscribeToChapter,
    subscribeToProject,
    subscribeToPresence,
    subscribeToCollaboration,
    subscribeToSessions,
    
    // Event management
    addEventListener,
    removeEventListener,
    
    // Utilities
    updatePriority,
    getMetrics,
    cleanup,
    
    // Convenience
    isSubscribedToChapter: chapterId ? subscriptionIds.current.has(`chapter-${chapterId}-${user?.id}`) : false,
    isSubscribedToProject: projectId ? subscriptionIds.current.has(`project-${projectId}-${user?.id}`) : false,
  }
}