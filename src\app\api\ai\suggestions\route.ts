import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { baseSchemas } from '@/lib/validation/common-schemas'

// Validation schemas
const suggestionQuerySchema = z.object({
  project_id: baseSchemas.uuid.optional(),
  chapter_id: baseSchemas.uuid.optional(),
  suggestion_type: z.enum(['plot', 'character', 'dialogue', 'description', 'pacing', 'style', 'general']).optional(),
  accepted: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).pipe(z.number().positive().max(100)).default('50').optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).pipe(z.number().min(0)).default('0').optional()
})

const createSuggestionSchema = z.object({
  project_id: baseSchemas.uuid.optional(),
  chapter_id: baseSchemas.uuid.optional(),
  suggestion_type: z.enum(['plot', 'character', 'dialogue', 'description', 'pacing', 'style', 'general']),
  content: baseSchemas.description.min(1).max(5000),
  context: z.record(z.unknown()).optional(),
  metadata: z.record(z.unknown()).optional()
})

const updateSuggestionSchema = z.object({
  accepted: z.boolean().optional(),
  feedback: baseSchemas.description.max(1000).optional(),
  rating: z.number().min(1).max(5).optional(),
  applied_at: z.string().datetime().optional()
})

export const GET = UnifiedAuthService.withAuth(async (request) => {
  const { searchParams } = new URL(request.url)
  
  // Parse query parameters
  const queryParams = {
    project_id: searchParams.get('project_id') || undefined,
    chapter_id: searchParams.get('chapter_id') || undefined,
    suggestion_type: searchParams.get('suggestion_type') || undefined,
    accepted: searchParams.get('accepted') || undefined,
    limit: searchParams.get('limit') || '50',
    offset: searchParams.get('offset') || '0'
  }

  // Validate query parameters
  const parseResult = suggestionQuerySchema.safeParse(queryParams)
  if (!parseResult.success) {
    return NextResponse.json({ 
      error: 'Invalid query parameters',
      details: parseResult.error.errors
    }, { status: 400 })
  }

  const validatedQuery = parseResult.data

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'authenticated',
    rateLimitCost: 1,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user
      if (!user) return { valid: false, error: 'Authentication required' }

      // Verify project ownership if project_id is provided
      if (validatedQuery.project_id) {
        const supabase = await createTypedServerClient()
        const { data: project, error } = await supabase
          .from('projects')
          .select('id')
          .eq('id', validatedQuery.project_id)
          .eq('user_id', user.id)
          .single()

        if (error || !project) {
          return { valid: false, error: 'Project not found or unauthorized' }
        }
      }

      return { valid: true }
    }
  })

  if (validationResult instanceof NextResponse) {
    return validationResult
  }

  const { context } = validationResult
  const user = request.user!

  try {
    const supabase = await createTypedServerClient()
    
    // Build query
    let query = supabase
      .from('ai_suggestions')
      .select(`
        *,
        projects (
          id,
          title
        ),
        chapters (
          id,
          title,
          chapter_number
        )
      `, { count: 'exact' })
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    // Apply filters
    if (validatedQuery.project_id) {
      query = query.eq('project_id', validatedQuery.project_id)
    }
    if (validatedQuery.chapter_id) {
      query = query.eq('chapter_id', validatedQuery.chapter_id)
    }
    if (validatedQuery.suggestion_type) {
      query = query.eq('suggestion_type', validatedQuery.suggestion_type)
    }
    if (validatedQuery.accepted !== undefined) {
      query = query.eq('accepted', validatedQuery.accepted)
    }

    // Apply pagination
    const limit = validatedQuery.limit || 50
    const offset = validatedQuery.offset || 0
    query = query.range(offset, offset + limit - 1)

    const { data: suggestions, error, count } = await query

    if (error) {
      logger.error('Error fetching AI suggestions:', error, {
        userId: user.id,
        clientIP: context.clientIP
      })
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Calculate statistics
    const stats = {
      total: count || 0,
      accepted: suggestions?.filter(s => s.accepted === true).length || 0,
      rejected: suggestions?.filter(s => s.accepted === false).length || 0,
      pending: suggestions?.filter(s => s.accepted === null).length || 0,
      avgRating: suggestions?.length 
        ? suggestions.reduce((sum, s) => sum + (s.rating || 0), 0) / suggestions.filter(s => s.rating).length || 0
        : 0
    }

    logger.info('AI suggestions retrieved', {
      userId: user.id,
      count: suggestions?.length || 0,
      clientIP: context.clientIP
    })

    return NextResponse.json({
      success: true,
      data: {
        suggestions: suggestions || [],
        stats,
        pagination: {
          total: count || 0,
          limit,
          offset,
          hasMore: (count || 0) > offset + limit
        }
      }
    })

  } catch (error) {
    logger.error('Error in AI suggestions GET:', error, {
      userId: user.id,
      clientIP: context.clientIP
    })
    
    return NextResponse.json(
      { error: 'Failed to fetch AI suggestions' },
      { status: 500 }
    )
  }
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: createSuggestionSchema,
    rateLimitKey: 'ai-suggestions',
    rateLimitCost: 3,
    maxBodySize: 10 * 1024, // 10KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const body = await req.json()
      const user = req.user
      
      if (!user) return { valid: false, error: 'Authentication required' }

      const supabase = await createTypedServerClient()

      // Verify project ownership if project_id is provided
      if (body.project_id) {
        const { data: project, error } = await supabase
          .from('projects')
          .select('id')
          .eq('id', body.project_id)
          .eq('user_id', user.id)
          .single()

        if (error || !project) {
          return { valid: false, error: 'Project not found or unauthorized' }
        }
      }

      // If chapter_id is provided, verify it belongs to the project
      if (body.chapter_id && body.project_id) {
        const { data: chapter, error } = await supabase
          .from('chapters')
          .select('id')
          .eq('id', body.chapter_id)
          .eq('project_id', body.project_id)
          .single()

        if (error || !chapter) {
          return { valid: false, error: 'Chapter not found or does not belong to project' }
        }
      }

      return { valid: true }
    }
  })

  if (validationResult instanceof NextResponse) {
    return validationResult
  }

  const { context } = validationResult
  const user = request.user!
  const validatedData = context.body

  try {
    const supabase = await createTypedServerClient()

    // Create AI suggestion
    const { data: suggestion, error } = await supabase
      .from('ai_suggestions')
      .insert({
        ...validatedData,
        user_id: user.id,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      logger.error('Error creating AI suggestion:', error, {
        userId: user.id,
        clientIP: context.clientIP
      })
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    logger.info('AI suggestion created', {
      suggestionId: suggestion.id,
      userId: user.id,
      type: validatedData.suggestion_type,
      clientIP: context.clientIP
    })

    return NextResponse.json({
      success: true,
      data: suggestion
    }, { status: 201 })

  } catch (error) {
    logger.error('Error in AI suggestions POST:', error, {
      userId: user.id,
      clientIP: context.clientIP
    })
    
    return NextResponse.json(
      { error: 'Failed to create AI suggestion' },
      { status: 500 }
    )
  }
})

export const PATCH = UnifiedAuthService.withAuth(async (request) => {
  const searchParams = request.nextUrl.searchParams
  const suggestionId = searchParams.get('id')

  if (!suggestionId || !baseSchemas.uuid.safeParse(suggestionId).success) {
    return NextResponse.json(
      { error: 'Valid suggestion ID required' },
      { status: 400 }
    )
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: updateSuggestionSchema,
    rateLimitKey: 'authenticated',
    rateLimitCost: 2,
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user
      if (!user) return { valid: false, error: 'Authentication required' }

      // Verify user owns the suggestion
      const supabase = await createTypedServerClient()
      const { data: suggestion, error } = await supabase
        .from('ai_suggestions')
        .select('id')
        .eq('id', suggestionId)
        .eq('user_id', user.id)
        .single()

      if (error || !suggestion) {
        return { valid: false, error: 'Suggestion not found or unauthorized' }
      }

      return { valid: true }
    }
  })

  if (validationResult instanceof NextResponse) {
    return validationResult
  }

  const { context } = validationResult
  const user = request.user!
  const validatedData = context.body

  try {
    const supabase = await createTypedServerClient()

    // If accepting the suggestion, set applied_at
    if (validatedData.accepted === true && !validatedData.applied_at) {
      validatedData.applied_at = new Date().toISOString()
    }

    const { data: suggestion, error } = await supabase
      .from('ai_suggestions')
      .update(validatedData)
      .eq('id', suggestionId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      logger.error('Error updating AI suggestion:', error, {
        suggestionId,
        userId: user.id,
        clientIP: context.clientIP
      })
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    logger.info('AI suggestion updated', {
      suggestionId,
      userId: user.id,
      accepted: validatedData.accepted,
      clientIP: context.clientIP
    })

    return NextResponse.json({
      success: true,
      data: suggestion
    })

  } catch (error) {
    logger.error('Error in AI suggestions PATCH:', error, {
      suggestionId,
      userId: user.id,
      clientIP: context.clientIP
    })
    
    return NextResponse.json(
      { error: 'Failed to update AI suggestion' },
      { status: 500 }
    )
  }
})

export const DELETE = UnifiedAuthService.withAuth(async (request) => {
  const searchParams = request.nextUrl.searchParams
  const suggestionId = searchParams.get('id')

  if (!suggestionId || !baseSchemas.uuid.safeParse(suggestionId).success) {
    return NextResponse.json(
      { error: 'Valid suggestion ID required' },
      { status: 400 }
    )
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'authenticated',
    rateLimitCost: 1,
    maxRequestSize: 1024,
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user
      if (!user) return { valid: false, error: 'Authentication required' }

      // Verify user owns the suggestion
      const supabase = await createTypedServerClient()
      const { data: suggestion, error } = await supabase
        .from('ai_suggestions')
        .select('id')
        .eq('id', suggestionId)
        .eq('user_id', user.id)
        .single()

      if (error || !suggestion) {
        return { valid: false, error: 'Suggestion not found or unauthorized' }
      }

      return { valid: true }
    }
  })

  if (validationResult instanceof NextResponse) {
    return validationResult
  }

  const { context } = validationResult
  const user = request.user!

  try {
    const supabase = await createTypedServerClient()

    const { error } = await supabase
      .from('ai_suggestions')
      .delete()
      .eq('id', suggestionId)
      .eq('user_id', user.id)

    if (error) {
      logger.error('Error deleting AI suggestion:', error, {
        suggestionId,
        userId: user.id,
        clientIP: context.clientIP
      })
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    logger.info('AI suggestion deleted', {
      suggestionId,
      userId: user.id,
      clientIP: context.clientIP
    })

    return NextResponse.json({
      success: true,
      message: 'Suggestion deleted successfully'
    })

  } catch (error) {
    logger.error('Error in AI suggestions DELETE:', error, {
      suggestionId,
      userId: user.id,
      clientIP: context.clientIP
    })
    
    return NextResponse.json(
      { error: 'Failed to delete AI suggestion' },
      { status: 500 }
    )
  }
})