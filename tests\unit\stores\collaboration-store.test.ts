import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { act, renderHook } from '@testing-library/react';
import { useCollaborationStore } from '@/stores/collaboration-store';
import { createClient } from '@/lib/supabase/client';

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(),
}));

// Mock RealtimeChannel
class MockRealtimeChannel {
  private callbacks: { [key: string]: Function[] } = {};

  on(event: string, callback: Function) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
    return this;
  }

  subscribe(callback?: Function) {
    if (callback) callback({ status: 'SUBSCRIBED' });
    return this;
  }

  unsubscribe() {
    return Promise.resolve({ error: null });
  }

  track(payload: any) {
    return this;
  }

  send(event: { type: string; event: string; payload: any }) {
    return this;
  }

  // Helper method to trigger events in tests
  trigger(event: string, payload: any) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(cb => cb(payload));
    }
  }
}

describe('CollaborationStore', () => {
  let mockSupabase: any;
  let mockChannel: MockRealtimeChannel;

  beforeEach(() => {
    // Reset store before each test
    useCollaborationStore.setState({
      collaborators: new Map(),
      presence: new Map(),
      conflicts: [],
      isConnected: false,
      channel: null,
    });

    // Setup mocks
    mockChannel = new MockRealtimeChannel();
    mockSupabase = {
      channel: jest.fn().mockReturnValue(mockChannel),
      removeChannel: jest.fn().mockResolvedValue({ error: null }),
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        }),
      },
    };

    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useCollaborationStore());

      expect(result.current.collaborators.size).toBe(0);
      expect(result.current.presence.size).toBe(0);
      expect(result.current.conflicts).toEqual([]);
      expect(result.current.isConnected).toBe(false);
      expect(result.current.channel).toBeNull();
    });
  });

  describe('joinProject', () => {
    it('should establish real-time connection', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      expect(mockSupabase.channel).toHaveBeenCalledWith('project:project-123');
      expect(result.current.isConnected).toBe(true);
      expect(result.current.channel).toBe(mockChannel);
    });

    it('should setup presence tracking', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Simulate presence sync
      act(() => {
        mockChannel.trigger('presence', {
          event: 'sync',
          payload: {
            'user-456': {
              user_id: 'user-456',
              user_email: '<EMAIL>',
              cursor: { line: 10, ch: 5 },
            },
          },
        });
      });

      expect(result.current.presence.has('user-456')).toBe(true);
      expect(result.current.presence.get('user-456')).toEqual({
        user_id: 'user-456',
        user_email: '<EMAIL>',
        cursor: { line: 10, ch: 5 },
      });
    });

    it('should handle broadcast events', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Simulate cursor update
      act(() => {
        mockChannel.trigger('broadcast', {
          event: 'cursor',
          payload: {
            user_id: 'user-789',
            position: { line: 20, ch: 10 },
          },
        });
      });

      expect(result.current.presence.get('user-789')).toEqual({
        cursor: { line: 20, ch: 10 },
      });
    });
  });

  describe('leaveProject', () => {
    it('should cleanup connection and state', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      // First join
      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Add some presence data
      act(() => {
        result.current.presence.set('user-456', { cursor: { line: 1, ch: 1 } });
      });

      // Then leave
      await act(async () => {
        await result.current.leaveProject();
      });

      expect(mockSupabase.removeChannel).toHaveBeenCalled();
      expect(result.current.isConnected).toBe(false);
      expect(result.current.channel).toBeNull();
      expect(result.current.presence.size).toBe(0);
    });
  });

  describe('updateCursor', () => {
    it('should broadcast cursor position', async () => {
      const { result } = renderHook(() => useCollaborationStore());
      const sendSpy = jest.spyOn(mockChannel, 'send');

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      act(() => {
        result.current.updateCursor({ line: 15, ch: 20 });
      });

      expect(sendSpy).toHaveBeenCalledWith({
        type: 'broadcast',
        event: 'cursor',
        payload: {
          user_id: 'user-123',
          position: { line: 15, ch: 20 },
        },
      });
    });
  });

  describe('sendEdit', () => {
    it('should broadcast edit operation', async () => {
      const { result } = renderHook(() => useCollaborationStore());
      const sendSpy = jest.spyOn(mockChannel, 'send');

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      const edit = {
        id: 'edit-123',
        type: 'insert' as const,
        position: { line: 5, ch: 10 },
        content: 'Hello',
        timestamp: Date.now(),
      };

      act(() => {
        result.current.sendEdit(edit);
      });

      expect(sendSpy).toHaveBeenCalledWith({
        type: 'broadcast',
        event: 'edit',
        payload: {
          user_id: 'user-123',
          edit,
        },
      });
    });
  });

  describe('conflict detection', () => {
    it('should detect conflicts in concurrent edits', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Simulate receiving conflicting edit
      act(() => {
        mockChannel.trigger('broadcast', {
          event: 'edit',
          payload: {
            user_id: 'user-456',
            edit: {
              id: 'edit-456',
              type: 'insert',
              position: { line: 5, ch: 10 },
              content: 'World',
              timestamp: Date.now() - 100,
            },
          },
        });
      });

      // Send local edit at same position
      act(() => {
        result.current.sendEdit({
          id: 'edit-789',
          type: 'insert',
          position: { line: 5, ch: 10 },
          content: 'Hello',
          timestamp: Date.now(),
        });
      });

      expect(result.current.conflicts.length).toBeGreaterThan(0);
      expect(result.current.conflicts[0]).toMatchObject({
        type: 'position',
        position: { line: 5, ch: 10 },
      });
    });
  });

  describe('conflict resolution', () => {
    it('should resolve conflicts with selected option', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      // Add a conflict
      act(() => {
        useCollaborationStore.setState({
          conflicts: [{
            id: 'conflict-123',
            type: 'position',
            position: { line: 5, ch: 10 },
            localEdit: {
              id: 'edit-local',
              type: 'insert',
              content: 'Local',
              position: { line: 5, ch: 10 },
              timestamp: Date.now(),
            },
            remoteEdit: {
              id: 'edit-remote',
              type: 'insert',
              content: 'Remote',
              position: { line: 5, ch: 10 },
              timestamp: Date.now() - 100,
            },
          }],
        });
      });

      const onResolved = jest.fn();

      act(() => {
        result.current.resolveConflict('conflict-123', 'local', onResolved);
      });

      expect(result.current.conflicts.length).toBe(0);
      expect(onResolved).toHaveBeenCalledWith({
        id: 'edit-local',
        type: 'insert',
        content: 'Local',
        position: { line: 5, ch: 10 },
        timestamp: expect.any(Number),
      });
    });
  });

  describe('collaborator management', () => {
    it('should track active collaborators', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Simulate collaborator joining
      act(() => {
        mockChannel.trigger('presence', {
          event: 'join',
          key: 'user-456',
          currentPresences: [],
          newPresences: [{
            user_id: 'user-456',
            user_email: '<EMAIL>',
            color: '#FF5733',
          }],
        });
      });

      expect(result.current.collaborators.has('user-456')).toBe(true);
      expect(result.current.collaborators.get('user-456')).toEqual({
        id: 'user-456',
        email: '<EMAIL>',
        color: '#FF5733',
        isOnline: true,
      });
    });

    it('should handle collaborator leaving', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // First add collaborator
      act(() => {
        result.current.collaborators.set('user-456', {
          id: 'user-456',
          email: '<EMAIL>',
          color: '#FF5733',
          isOnline: true,
        });
      });

      // Simulate collaborator leaving
      act(() => {
        mockChannel.trigger('presence', {
          event: 'leave',
          key: 'user-456',
          currentPresences: [],
          leftPresences: [{
            user_id: 'user-456',
          }],
        });
      });

      const collaborator = result.current.collaborators.get('user-456');
      expect(collaborator?.isOnline).toBe(false);
    });
  });

  describe('performance optimizations', () => {
    it('should throttle cursor updates', async () => {
      const { result } = renderHook(() => useCollaborationStore());
      const sendSpy = jest.spyOn(mockChannel, 'send');

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Send multiple cursor updates rapidly
      act(() => {
        for (let i = 0; i < 10; i++) {
          result.current.updateCursor({ line: i, ch: i });
        }
      });

      // Should be throttled
      expect(sendSpy).toHaveBeenCalledTimes(1);
    });

    it('should batch presence updates', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Simulate multiple presence updates
      act(() => {
        mockChannel.trigger('presence', {
          event: 'sync',
          payload: {
            'user-1': { cursor: { line: 1, ch: 1 } },
            'user-2': { cursor: { line: 2, ch: 2 } },
            'user-3': { cursor: { line: 3, ch: 3 } },
          },
        });
      });

      expect(result.current.presence.size).toBe(3);
    });
  });

  describe('error handling', () => {
    it('should handle connection failures', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      mockChannel.subscribe = jest.fn((callback) => {
        if (callback) callback({ status: 'CHANNEL_ERROR' });
        return mockChannel;
      });

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      expect(result.current.isConnected).toBe(false);
    });

    it('should retry on disconnection', async () => {
      const { result } = renderHook(() => useCollaborationStore());

      await act(async () => {
        await result.current.joinProject('project-123');
      });

      // Simulate disconnection
      act(() => {
        mockChannel.trigger('system', { status: 'DISCONNECTED' });
      });

      expect(result.current.isConnected).toBe(false);

      // Should attempt reconnection
      expect(mockSupabase.channel).toHaveBeenCalledTimes(2);
    });
  });
});