-- Create location_positions table for storing map coordinates
-- This table stores the visual positioning of locations on interactive maps

CREATE TABLE IF NOT EXISTS location_positions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  location_id UUID NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  
  -- Map position data
  x_coordinate DECIMAL(10, 6) NOT NULL,
  y_coordinate DECIMAL(10, 6) NOT NULL,
  z_index INTEGER DEFAULT 0, -- For layering overlapping locations
  
  -- Optional styling
  marker_style JSONB DEFAULT '{}', -- Custom marker styling (color, icon, size, etc.)
  
  -- Metadata
  is_default BOOLEAN DEFAULT false, -- Project-wide default position
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Unique constraint: one position per location per user
  UNIQUE(location_id, user_id)
);

-- Create indexes for performance
CREATE INDEX idx_location_positions_location_id ON location_positions(location_id);
CREATE INDEX idx_location_positions_user_id ON location_positions(user_id);
CREATE INDEX idx_location_positions_project_id ON location_positions(project_id);
CREATE INDEX idx_location_positions_is_default ON location_positions(is_default);
CREATE INDEX idx_location_positions_composite_project_user ON location_positions(project_id, user_id);

-- Add RLS policies
ALTER TABLE location_positions ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own positions and default positions in their projects
CREATE POLICY "Users can view relevant location positions" ON location_positions
  FOR SELECT
  USING (
    auth.uid() = user_id 
    OR (
      is_default = true 
      AND project_id IN (
        SELECT id FROM projects WHERE user_id = auth.uid()
        UNION
        SELECT project_id FROM project_collaborators WHERE user_id = auth.uid()
      )
    )
  );

-- Policy: Users can insert positions for locations in their projects
CREATE POLICY "Users can create location positions" ON location_positions
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id
    AND project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
      UNION
      SELECT project_id FROM project_collaborators WHERE user_id = auth.uid() AND role IN ('editor', 'admin')
    )
  );

-- Policy: Users can update their own positions
CREATE POLICY "Users can update their location positions" ON location_positions
  FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own positions
CREATE POLICY "Users can delete their location positions" ON location_positions
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_location_positions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER location_positions_updated_at_trigger
  BEFORE UPDATE ON location_positions
  FOR EACH ROW
  EXECUTE FUNCTION update_location_positions_updated_at();

-- Add comments for documentation
COMMENT ON TABLE location_positions IS 'Stores map coordinates and visual positioning for locations';
COMMENT ON COLUMN location_positions.x_coordinate IS 'Horizontal position on map (0-100 scale or pixel coordinates)';
COMMENT ON COLUMN location_positions.y_coordinate IS 'Vertical position on map (0-100 scale or pixel coordinates)';
COMMENT ON COLUMN location_positions.z_index IS 'Layer order for overlapping locations';
COMMENT ON COLUMN location_positions.marker_style IS 'JSON object with custom styling (color, icon, size, etc.)';
COMMENT ON COLUMN location_positions.is_default IS 'Whether this is the project-wide default position for this location';