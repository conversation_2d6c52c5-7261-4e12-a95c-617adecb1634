'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  CloudOff, 
  Save, 
  Clock, 
  CheckCircle, 
  Loader2
} from 'lucide-react'
import { TIME_MS } from '@/lib/constants'

interface SaveStatusIndicatorProps {
  isSaving?: boolean
  lastSaved?: Date | null
  hasUnsavedChanges?: boolean
  isOnline?: boolean
  onManualSave?: () => void
}

export function SaveStatusIndicator({ 
  isSaving = false, 
  lastSaved = null, 
  hasUnsavedChanges = false,
  isOnline = true,
  onManualSave 
}: SaveStatusIndicatorProps) {
  const [timeAgo, setTimeAgo] = useState<string>('')

  useEffect(() => {
    if (!lastSaved) return

    const updateTimeAgo = () => {
      const now = new Date()
      const diffMs = now.getTime() - lastSaved.getTime()
      const diffMins = Math.floor(diffMs / (TIME_MS.SECOND * 60))
      const diffSecs = Math.floor(diffMs / TIME_MS.SECOND)

      if (diffSecs < 10) {
        setTimeAgo('just now')
      } else if (diffSecs < 60) {
        setTimeAgo(`${diffSecs}s ago`)
      } else if (diffMins < 60) {
        setTimeAgo(`${diffMins}m ago`)
      } else {
        setTimeAgo(lastSaved.toLocaleTimeString([], { 
          hour: '2-digit', 
          minute: '2-digit' 
        }))
      }
    }

    updateTimeAgo()
    const interval = setInterval(updateTimeAgo, 10000) // Update every 10 seconds

    return () => clearInterval(interval)
  }, [lastSaved])

  const getStatusIcon = () => {
    if (!isOnline) {
      return <CloudOff className="h-3 w-3 text-error" />
    }
    
    if (isSaving) {
      return <Loader2 className="h-3 w-3 animate-spin text-info" />
    }
    
    if (hasUnsavedChanges) {
      return <Clock className="h-3 w-3 text-warning" />
    }
    
    return <CheckCircle className="h-3 w-3 text-success" />
  }

  const getStatusText = () => {
    if (!isOnline) {
      return 'Offline'
    }
    
    if (isSaving) {
      return 'Saving...'
    }
    
    if (hasUnsavedChanges) {
      return 'Unsaved changes'
    }
    
    if (lastSaved) {
      return `Saved ${timeAgo}`
    }
    
    return 'Not saved'
  }

  const getStatusVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    if (!isOnline) return 'destructive'
    if (isSaving) return 'default'
    if (hasUnsavedChanges) return 'secondary'
    return 'outline'
  }

  return (
    <div className="flex items-center gap-2">
      <Badge variant={getStatusVariant()} className="gap-1 text-xs">
        {getStatusIcon()}
        {getStatusText()}
      </Badge>
      
      {hasUnsavedChanges && onManualSave && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onManualSave}
          disabled={isSaving || !isOnline}
          className="h-6 px-2 text-xs"
        >
          <Save className="h-3 w-3 mr-1" />
          Save Now
        </Button>
      )}
    </div>
  )
}

// Hook to detect online status
export function useOnlineStatus() {
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  )

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return isOnline
}