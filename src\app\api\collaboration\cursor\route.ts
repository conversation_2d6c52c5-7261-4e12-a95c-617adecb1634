import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyCollaborationAccess } from '@/lib/api/collaboration-middleware'
import { ServiceManager } from '@/lib/services/service-manager'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, position } = body

    if (!sessionId || !position) {
      return NextResponse.json(
        { error: 'Session ID and position are required' },
        { status: 400 }
      )
    }

    if (typeof position.line !== 'number' || typeof position.column !== 'number') {
      return NextResponse.json(
        { error: 'Invalid position format' },
        { status: 400 }
      )
    }

    // Verify user has view access to the collaboration session
    const authResult = await verifyCollaborationAccess(sessionId, 'view')
    if (!authResult.success) {
      return authResult.response!
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationService = await serviceManager.getCollaborationHub()
    if (!collaborationService) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationService.updateCursor(
      sessionId,
      authResult.user!.id,
      position
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to update cursor' },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    logger.error('Collaboration cursor error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}