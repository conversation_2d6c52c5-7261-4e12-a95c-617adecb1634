/**
 * Unified Validation Schemas
 * Consolidates all validation schemas into a single source of truth
 */

import { z } from 'zod'
import { 
  CONTENT_LIMITS,
  VALIDATION_RULES,
  SESSION_CONFIG,
  STORAGE_CONFIG,
  COLLABORATION_CONFIG
} from '@/lib/config/app-constants'
import { ERROR_MESSAGES } from '@/lib/config/error-messages'
import { SIZE_LIMITS } from '@/lib/constants'

// ===== BASE SCHEMAS =====

// ID Schemas
export const uuid = z.string().uuid('Invalid ID format')
export const id = z.string().min(1).max(100)

// Common ID types with semantic meaning
export const ids = {
  user: uuid,
  project: uuid,
  chapter: uuid,
  character: uuid,
  series: uuid,
  session: uuid,
  team: uuid,
  profile: uuid,
  achievement: uuid,
  template: uuid
} as const

// ===== STRING SCHEMAS =====

// Basic strings
export const strings = {
  // Short strings
  title: z.string()
    .min(1, 'Title is required')
    .max(CONTENT_LIMITS.MAX_TITLE_LENGTH)
    .trim(),
  
  name: z.string()
    .min(1, 'Name is required')
    .max(100)
    .trim(),
  
  description: z.string()
    .max(CONTENT_LIMITS.MAX_DESCRIPTION_LENGTH)
    .trim()
    .optional(),
  
  shortText: z.string()
    .min(1)
    .max(500)
    .trim(),
  
  // Long content
  content: z.string()
    .min(1)
    .max(1000000), // 1MB limit
  
  chapterContent: z.string()
    .min(CONTENT_LIMITS.MIN_CHAPTER_WORDS * 4) // Rough char to word conversion
    .max(CONTENT_LIMITS.MAX_CHAPTER_WORDS * 6),
  
  // Special formats
  email: z.string()
    .email(ERROR_MESSAGES.VALIDATION.INVALID_EMAIL)
    .toLowerCase()
    .trim(),
  
  url: z.string()
    .url(ERROR_MESSAGES.VALIDATION.INVALID_URL)
    .trim(),
  
  phone: z.string()
    .regex(/^\+?[1-9]\d{1,14}$/, ERROR_MESSAGES.VALIDATION.INVALID_PHONE)
    .trim()
    .optional(),
  
  slug: z.string()
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .min(3)
    .max(50)
} as const

// ===== USER SCHEMAS =====

export const user = {
  username: z.string()
    .min(VALIDATION_RULES.MIN_USERNAME_LENGTH)
    .max(VALIDATION_RULES.MAX_USERNAME_LENGTH)
    .regex(VALIDATION_RULES.USERNAME_PATTERN, 
      'Username can only contain letters, numbers, underscores, and hyphens')
    .trim(),
  
  password: z.string()
    .min(VALIDATION_RULES.MIN_PASSWORD_LENGTH)
    .max(VALIDATION_RULES.MAX_PASSWORD_LENGTH)
    .refine(
      (password) => !VALIDATION_RULES.PASSWORD_REQUIRE_UPPERCASE || /[A-Z]/.test(password),
      'Password must contain at least one uppercase letter'
    )
    .refine(
      (password) => !VALIDATION_RULES.PASSWORD_REQUIRE_LOWERCASE || /[a-z]/.test(password),
      'Password must contain at least one lowercase letter'
    )
    .refine(
      (password) => !VALIDATION_RULES.PASSWORD_REQUIRE_NUMBER || /[0-9]/.test(password),
      'Password must contain at least one number'
    )
    .refine(
      (password) => !VALIDATION_RULES.PASSWORD_REQUIRE_SPECIAL || /[^A-Za-z0-9]/.test(password),
      'Password must contain at least one special character'
    ),
  
  displayName: z.string()
    .min(1)
    .max(100)
    .trim(),
  
  bio: z.string()
    .max(500)
    .trim()
    .optional()
} as const

// ===== PROJECT SCHEMAS =====

export const project = {
  name: z.string()
    .min(VALIDATION_RULES.MIN_PROJECT_NAME_LENGTH)
    .max(VALIDATION_RULES.MAX_PROJECT_NAME_LENGTH)
    .regex(VALIDATION_RULES.PROJECT_NAME_PATTERN, 'Project name contains invalid characters')
    .trim(),
  
  genre: z.enum([
    'fantasy',
    'science-fiction',
    'mystery',
    'thriller',
    'romance',
    'literary-fiction',
    'historical-fiction',
    'young-adult',
    'horror',
    'contemporary',
    'dystopian',
    'magical-realism',
    'adventure',
    'crime',
    'comedy',
    'other'
  ]),
  
  writingStyle: z.enum([
    'literary',
    'commercial',
    'minimalist',
    'descriptive',
    'dialogue-heavy',
    'action-packed',
    'introspective',
    'humorous',
    'poetic',
    'journalistic',
    'experimental'
  ]),
  
  pov: z.enum([
    'first-person',
    'third-person-limited',
    'third-person-omniscient',
    'second-person',
    'multiple-pov'
  ]),
  
  tense: z.enum(['past', 'present', 'future', 'mixed']),
  
  status: z.enum(['draft', 'in-progress', 'review', 'completed', 'published', 'archived']),
  
  targetWordCount: z.number()
    .int()
    .min(1000)
    .max(1000000)
    .optional(),
  
  targetChapters: z.number()
    .int()
    .min(1)
    .max(200)
    .optional()
} as const

// ===== CHARACTER SCHEMAS =====

export const character = {
  name: z.string()
    .min(1)
    .max(CONTENT_LIMITS.MAX_CHARACTER_NAME_LENGTH)
    .trim(),
  
  role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']),
  
  bio: z.string()
    .max(CONTENT_LIMITS.MAX_CHARACTER_BIO_LENGTH)
    .optional(),
  
  traits: z.array(z.string().max(50))
    .max(20)
    .optional(),
  
  age: z.number()
    .int()
    .min(0)
    .max(200)
    .optional()
} as const

// ===== NUMERIC SCHEMAS =====

export const numbers = {
  // Pagination
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  
  // AI parameters
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().int().min(1).max(SIZE_LIMITS.EMBEDDING_TEXT_LIMIT).default(2000),
  topP: z.number().min(0).max(1).default(1),
  frequencyPenalty: z.number().min(-2).max(2).default(0),
  presencePenalty: z.number().min(-2).max(2).default(0),
  
  // General
  percentage: z.number().min(0).max(100),
  currency: z.number().min(0).multipleOf(0.01), // For cents
  rating: z.number().min(0).max(5).multipleOf(0.5)
} as const

// ===== DATE SCHEMAS =====

export const dates = {
  date: z.string().datetime(),
  dateOnly: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  futureDate: z.string().datetime().refine(
    (date) => new Date(date) > new Date(),
    'Date must be in the future'
  ),
  pastDate: z.string().datetime().refine(
    (date) => new Date(date) < new Date(),
    'Date must be in the past'
  ),
  dateRange: z.object({
    startDate: z.string().datetime(),
    endDate: z.string().datetime()
  }).refine(
    (data) => new Date(data.endDate) > new Date(data.startDate),
    'End date must be after start date'
  )
} as const

// ===== FILE SCHEMAS =====

export const file = {
  upload: z.object({
    filename: z.string().max(255),
    mimeType: z.string().max(100),
    size: z.number().int().min(1).max(STORAGE_CONFIG.MAX_FILE_SIZE),
    data: z.string().optional() // Base64 encoded
  }),
  
  image: z.object({
    filename: z.string().max(255),
    mimeType: z.enum(['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
    size: z.number().int().min(1).max(10 * 1024 * 1024), // 10MB
    data: z.string().optional()
  }),
  
  document: z.object({
    filename: z.string().max(255),
    mimeType: z.enum(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']),
    size: z.number().int().min(1).max(50 * 1024 * 1024), // 50MB
    data: z.string().optional()
  })
} as const

// ===== ENUMS =====

export const enums = {
  role: z.enum(['viewer', 'commenter', 'editor', 'admin', 'owner']),
  
  subscriptionTier: z.enum(['free', 'starter', 'professional', 'studio', 'enterprise']),
  
  taskStatus: z.enum(['pending', 'in-progress', 'completed', 'failed', 'cancelled']),
  
  collaboratorStatus: z.enum(['pending', 'active', 'inactive', 'removed']),
  
  exportFormat: z.enum(['epub', 'pdf', 'docx', 'txt', 'md', 'html']),
  
  aiModel: z.enum([
    'gpt-4.1-2025-04-14',
    'gpt-4o-mini',
    'text-embedding-3-small',
    'claude-3-opus',
    'claude-3-sonnet',
    'gemini-pro'
  ]),
  
  sortOrder: z.enum(['asc', 'desc']),
  
  theme: z.enum(['light', 'dark', 'system', 'writers-sanctuary', 'forest-manuscript', 'evening-study', 'midnight-ink'])
} as const

// ===== COMPOSITE SCHEMAS =====

export const composite = {
  // Pagination
  pagination: z.object({
    page: numbers.page,
    limit: numbers.limit,
    sortBy: z.string().optional(),
    sortOrder: enums.sortOrder.default('desc')
  }),
  
  // Metadata
  metadata: z.record(z.string(), z.unknown()).optional(),
  
  // Tags
  tags: z.array(z.string().max(50)).max(20).optional(),
  
  // AI Generation Options
  aiOptions: z.object({
    model: enums.aiModel.optional(),
    temperature: numbers.temperature,
    maxTokens: numbers.maxTokens.optional(),
    topP: numbers.topP,
    frequencyPenalty: numbers.frequencyPenalty,
    presencePenalty: numbers.presencePenalty,
    systemPrompt: z.string().optional(),
    stream: z.boolean().default(false)
  }),
  
  // Search
  search: z.object({
    query: z.string().min(1).max(200),
    filters: z.record(z.string(), z.unknown()).optional(),
    page: numbers.page,
    limit: numbers.limit
  })
} as const

// ===== UTILITY FUNCTIONS =====

/**
 * Make a schema optional
 */
export function optional<T extends z.ZodType>(schema: T) {
  return schema.optional()
}

/**
 * Make a schema nullable
 */
export function nullable<T extends z.ZodType>(schema: T) {
  return schema.nullable()
}

/**
 * Make a schema both optional and nullable
 */
export function maybe<T extends z.ZodType>(schema: T) {
  return schema.optional().nullable()
}

/**
 * Create an array schema with min/max constraints
 */
export function array<T extends z.ZodType>(
  schema: T,
  options?: { min?: number; max?: number }
) {
  let arraySchema = z.array(schema)
  if (options?.min) arraySchema = arraySchema.min(options.min)
  if (options?.max) arraySchema = arraySchema.max(options.max)
  return arraySchema
}

/**
 * Create a paginated response schema
 */
export function paginatedSchema<T extends z.ZodType>(itemSchema: T) {
  return z.object({
    data: z.array(itemSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasMore: z.boolean()
    })
  })
}

// ===== EXPORTS =====

// Export all schemas as a namespace
export const schemas = {
  // Base
  uuid,
  id,
  ids,
  
  // Types
  strings,
  user,
  project,
  character,
  numbers,
  dates,
  file,
  enums,
  composite,
  
  // Utilities
  optional,
  nullable,
  maybe,
  array,
  paginatedSchema
} as const

// Default export for convenience
export default schemas