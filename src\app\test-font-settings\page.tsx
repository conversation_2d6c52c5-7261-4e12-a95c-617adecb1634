'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTypographySettings } from '@/lib/settings/settings-store';
import { LazyMonacoEditor } from '@/components/editor/lazy-monaco-editor';

export default function TestFontSettingsPage() {
  const { typography } = useTypographySettings();

  return (
    <div className="container-wide mx-auto py-6 sm:py-8 lg:py-10 space-y-8">
      <h1 className="text-3xl font-bold">Font Settings Test Page</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Current Typography Settings</CardTitle>
          <CardDescription>These are your current font settings</CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="text-sm bg-muted p-4 rounded">
            {JSON.stringify(typography, null, 2)}
          </pre>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Font Test Areas</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">UI Font (using font-ui class)</h3>
            <p className="font-ui">This text should use the UI font from your settings.</p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Editor Font (using font-editor class)</h3>
            <p className="font-editor">This text should use the Editor font from your settings.</p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Reading Font (using font-reading class)</h3>
            <p className="font-reading">This text should use the Reading font from your settings.</p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Mono Font (using font-mono class)</h3>
            <p className="font-mono">This text should use the Editor font from your settings.</p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Headers (should use reading font)</h3>
            <h1>H1 Header</h1>
            <h2>H2 Header</h2>
            <h3>H3 Header</h3>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Monaco Editor Test</CardTitle>
          <CardDescription>This editor should use your font settings</CardDescription>
        </CardHeader>
        <CardContent>
          <LazyMonacoEditor
            initialContent={`// This Monaco editor should use your font settings
function testFonts() {
  const editorFont = 'Should match your editor font setting';
  const fontSize = 'Should match your text size setting';
  const lineHeight = 'Should match your line height setting';
  const letterSpacing = 'Should match your letter spacing setting';
  
  return {
    message: 'Try changing your font settings and see if this editor updates!'
  };
}`}
            height="300px"
            showToolbar={false}
            showStats={false}
            showAISuggestions={false}
          />
        </CardContent>
      </Card>

      <div className="text-center">
        <Button 
          onClick={() => window.location.href = '/settings'}
          size="lg"
        >
          Go to Settings to Change Fonts
        </Button>
      </div>
    </div>
  );
}