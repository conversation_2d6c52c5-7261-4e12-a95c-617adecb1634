/**
 * Settings Modal Component
 * Main settings interface for BookScribe AI
 */

'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs as _Tabs, TabsContent as _TabsContent, TabsList as _TabsList, TabsTrigger as _TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Palette as _Palette, 
  Edit3, 
  Eye, 
  X,
  RotateCcw,
  User
} from 'lucide-react';

import { EditorSettingsSection } from './editor-settings-section';
import { AccessibilitySettingsSection } from './accessibility-settings-section';
import { ProfileSettingsSection } from './profile-settings-section';
import { useSettingsStore } from '@/lib/settings/settings-store';

interface SettingsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SettingsModal({ open, onOpenChange }: SettingsModalProps) {
  const [activeTab, setActiveTab] = useState('profile');
  const resetSettings = useSettingsStore((state) => state.resetSettings);

  const handleResetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to their defaults? This action cannot be undone.')) {
      resetSettings();
    }
  };

  const tabs = [
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      description: 'Account and writing preferences',
    },
    {
      id: 'editor',
      label: 'Editor',
      icon: Edit3,
      description: 'Writing and editing preferences',
    },
    {
      id: 'accessibility',
      label: 'Accessibility',
      icon: Eye,
      description: 'Accessibility and usability options',
    },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] p-0">
        <DialogHeader className="px-4 sm:px-6 lg:px-8 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                <Settings className="w-4 h-4 text-primary" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold">Settings</DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Customize your writing preferences and accessibility options
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetSettings}
                className="text-xs"
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                Reset All
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                aria-label="Close settings"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 border-r border-border bg-muted/30">
            <ScrollArea className="h-full">
              <div className="p-4">
                <nav className="space-y-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    const isActive = activeTab === tab.id;
                    
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-start gap-3 p-3 rounded-lg text-left transition-colors ${
                          isActive
                            ? 'bg-primary/10 text-primary border border-primary/20'
                            : 'hover:bg-accent text-muted-foreground hover:text-foreground'
                        }`}
                      >
                        <Icon className="w-4 h-4 mt-0.5 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm">{tab.label}</div>
                          <div className="text-xs text-muted-foreground mt-0.5 line-clamp-2">
                            {tab.description}
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </nav>

                {/* Quick Info */}
                <div className="mt-6 p-3 rounded-lg bg-card border">
                  <div className="flex items-center gap-2 mb-2">
                    <Settings className="w-3 h-3 text-muted-foreground" />
                    <span className="text-xs font-medium text-muted-foreground">
                      Settings Info
                    </span>
                  </div>
                  <div className="space-y-1 text-xs text-muted-foreground">
                    <div>Settings are automatically saved</div>
                    <div>Changes apply immediately</div>
                    <div>Visit Customization for themes & typography</div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="p-6">
                {activeTab === 'profile' && <ProfileSettingsSection />}
                {activeTab === 'editor' && <EditorSettingsSection />}
                {activeTab === 'accessibility' && <AccessibilitySettingsSection />}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Footer */}
        <div className="px-4 sm:px-6 lg:px-8 py-3 border-t border-border bg-muted/30">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
              <span>BookScribe AI Settings v1.0</span>
              <Badge variant="outline" className="text-xs">
                Auto-saved
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>Last updated: Just now</span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Settings trigger button component
interface SettingsButtonProps {
  variant?: 'ghost' | 'outline' | 'default';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

export function SettingsButton({ 
  variant = 'ghost', 
  size = 'sm', 
  className = '' 
}: SettingsButtonProps) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setOpen(true)}
        className={className}
        aria-label="Open settings"
      >
        <Settings className="w-4 h-4" />
        <span className="sr-only">Settings</span>
      </Button>
      <SettingsModal open={open} onOpenChange={setOpen} />
    </>
  );
}
