/**
 * API Utilities
 * Common utilities for API routes and handlers
 */

import { NextRequest, NextResponse } from 'next/server'
import { z, ZodError } from 'zod'
import { createClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { RATE_LIMITS } from '@/lib/config/network-config'
import { TIME_MS } from '@/lib/constants'

// Standard API response types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  errors?: Record<string, string[]>
  meta?: {
    page?: number
    pageSize?: number
    total?: number
    [key: string]: unknown
  }
}

// Create standard success response
export function successResponse<T>(
  data: T,
  meta?: ApiResponse['meta'],
  status = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      data,
      meta
    },
    { status }
  )
}

// Create standard error response
export function errorResponse(
  error: string | Error | ZodError,
  status = 400
): NextResponse<ApiResponse> {
  if (error instanceof ZodError) {
    const errors: Record<string, string[]> = {}
    error.errors.forEach((err) => {
      const path = err.path.join('.')
      if (!errors[path]) errors[path] = []
      errors[path].push(err.message)
    })
    
    return NextResponse.json(
      {
        success: false,
        error: 'Validation failed',
        errors
      },
      { status: 422 }
    )
  }
  
  const message = error instanceof Error ? error.message : error
  
  return NextResponse.json(
    {
      success: false,
      error: message
    },
    { status }
  )
}

// Parse and validate request body
export async function parseRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json()
    return schema.parse(body)
  } catch (error) {
    if (error instanceof ZodError) {
      throw error
    }
    throw new Error('Invalid request body')
  }
}

// Parse and validate query parameters
export function parseQueryParams<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): T {
  const searchParams = request.nextUrl.searchParams
  const params: Record<string, unknown> = {}
  
  searchParams.forEach((value, key) => {
    // Handle array parameters (e.g., ?tags=a&tags=b)
    if (params[key]) {
      if (Array.isArray(params[key])) {
        (params[key] as unknown[]).push(value)
      } else {
        params[key] = [params[key], value]
      }
    } else {
      params[key] = value
    }
  })
  
  return schema.parse(params)
}

// Common pagination schema
export const paginationSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  pageSize: z.coerce.number().int().positive().max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

export type PaginationParams = z.infer<typeof paginationSchema>

// Calculate pagination offset
export function getPaginationOffset(params: PaginationParams): number {
  return (params.page - 1) * params.pageSize
}

// Create pagination meta
export function createPaginationMeta(
  params: PaginationParams,
  total: number
): ApiResponse['meta'] {
  return {
    page: params.page,
    pageSize: params.pageSize,
    total,
    totalPages: Math.ceil(total / params.pageSize),
    hasNext: params.page * params.pageSize < total,
    hasPrev: params.page > 1
  }
}

// Rate limiting headers
export function getRateLimitHeaders(
  limit: number,
  remaining: number,
  reset: Date
): Record<string, string> {
  return {
    'X-RateLimit-Limit': limit.toString(),
    'X-RateLimit-Remaining': remaining.toString(),
    'X-RateLimit-Reset': reset.toISOString(),
    'X-RateLimit-Reset-Seconds': Math.floor(reset.getTime() / TIME_MS.SECOND).toString()
  }
}

// Cache control headers
export function getCacheHeaders(maxAge: number, sMaxAge?: number): Record<string, string> {
  const directives = [`max-age=${maxAge}`]
  if (sMaxAge !== undefined) {
    directives.push(`s-maxage=${sMaxAge}`)
  }
  
  return {
    'Cache-Control': directives.join(', ')
  }
}

// CORS headers
export function getCorsHeaders(origin?: string): Record<string, string> {
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:TIME_MS.TYPING_TIMEOUT']
  const headers: Record<string, string> = {
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400'
  }
  
  if (origin && allowedOrigins.includes(origin)) {
    headers['Access-Control-Allow-Origin'] = origin
  }
  
  return headers
}

// Standard API handler wrapper
interface ApiHandlerOptions {
  requireAuth?: boolean
  requireAdmin?: boolean
  rateLimit?: keyof typeof RATE_LIMITS.API
  cache?: number
}

export function apiHandler<T = unknown>(
  handler: (
    request: NextRequest,
    context: {
      params?: Record<string, string>
      user?: { id: string; email: string; role?: string }
    }
  ) => Promise<NextResponse<ApiResponse<T>>>
) {
  return async (
    request: NextRequest,
    context?: { params?: Record<string, string> }
  ): Promise<NextResponse<ApiResponse<T>>> => {
    try {
      // Log request
      logger.info(`API Request: ${request.method} ${request.url}`)
      
      // Handle CORS preflight
      if (request.method === 'OPTIONS') {
        return new NextResponse(null, {
          status: 200,
          headers: getCorsHeaders(request.headers.get('origin') || undefined)
        })
      }
      
      // Execute handler
      const response = await handler(request, {
        params: context?.params,
      })
      
      // Add CORS headers
      const corsHeaders = getCorsHeaders(request.headers.get('origin') || undefined)
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value)
      })
      
      return response
    } catch (error) {
      logger.error('API Handler Error:', error)
      
      if (error instanceof ZodError) {
        return errorResponse(error, 422)
      }
      
      return errorResponse(
        error instanceof Error ? error : new Error('Internal server error'),
        500
      )
    }
  }
}

// Extract user from request
export async function getUserFromRequest(request: NextRequest) {
  try {
    const supabase = createClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }
    
    return {
      id: user.id,
      email: user.email!,
      role: user.user_metadata?.role
    }
  } catch {
    return null
  }
}

// Common request validation schemas
export const idParamSchema = z.object({
  id: z.string().uuid()
})

export const slugParamSchema = z.object({
  slug: z.string().min(1).max(255)
})

export const searchSchema = z.object({
  q: z.string().min(1).max(255),
  page: z.coerce.number().int().positive().default(1),
  pageSize: z.coerce.number().int().positive().max(100).default(20)
})

// Sort query builder
export function buildSortQuery(
  sortBy?: string,
  sortOrder: 'asc' | 'desc' = 'desc',
  allowedFields: string[] = []
): string {
  if (!sortBy || !allowedFields.includes(sortBy)) {
    return `created_at ${sortOrder}`
  }
  
  return `${sortBy} ${sortOrder}`
}