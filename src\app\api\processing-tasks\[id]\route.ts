import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'

export const GET = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const user = request.user!
    const taskId = params.id
    const supabase = await createTypedServerClient()

    // Get specific task
    const { data: task, error } = await supabase
      .from('processing_tasks')
      .select('*')
      .eq('id', taskId)
      .eq('user_id', user.id)
      .single()

    if (error || !task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ task })
  } catch (error) {
    console.error('Error fetching task:', error)
    return NextResponse.json(
      { error: 'Failed to fetch task' },
      { status: 500 }
    )
  }
})