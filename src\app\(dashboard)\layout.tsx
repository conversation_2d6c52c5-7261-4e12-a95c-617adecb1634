import { createServerClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import { DashboardLayoutClient } from './layout-client'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Auth check is handled by middleware, no need to duplicate here
  return (
    <DashboardLayoutClient>
      {children}
    </DashboardLayoutClient>
  )
}