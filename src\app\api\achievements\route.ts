import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { requireAuth } from '@/lib/api/auth-middleware'
import { logger } from '@/lib/services/logger'

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (authResult instanceof NextResponse) {
      return authResult
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || authResult.id
    const category = searchParams.get('category')
    const unlocked = searchParams.get('unlocked')

    const supabase = await createServerClient()

    // Fetch user achievements with progress
    let query = supabase
      .from('user_achievements')
      .select('*')
      .eq('user_id', userId)
      .order('unlocked_at', { ascending: false, nullsFirst: false })

    if (category) {
      query = query.eq('category', category)
    }

    if (unlocked === 'true') {
      query = query.not('unlocked_at', 'is', null)
    } else if (unlocked === 'false') {
      query = query.is('unlocked_at', null)
    }

    const { data: achievements, error } = await query

    if (error) {
      // If achievements tables don't exist, return empty array
      if (error.code === '42P01') {
        logger.warn('Achievements tables do not exist, returning empty array')
        return NextResponse.json([])
      }
      throw error
    }

    // Also get achievement definitions to ensure we have all possible achievements
    const { data: definitions } = await supabase
      .from('achievements')
      .select('*')
      .order('points', { ascending: false })

    // Merge achievements with definitions to show progress on locked ones
    const allAchievements = definitions?.map(def => {
      const userAchievement = achievements?.find(a => a.achievement_id === def.id)
      return userAchievement || {
        id: def.id,
        achievement_id: def.id,
        user_id: userId,
        title: def.title,
        description: def.description,
        category: def.category,
        tier: def.tier,
        icon: def.icon,
        current_value: 0,
        target_value: def.target_value,
        unlocked_at: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }) || []

    return NextResponse.json({ 
      achievements: allAchievements,
      total: allAchievements.length,
      unlocked: allAchievements.filter(a => a.unlocked_at).length
    })

  } catch (error) {
    logger.error('Error fetching achievements:', error)
    return NextResponse.json(
      { error: 'Failed to fetch achievements' },
      { status: 500 }
    )
  }
}