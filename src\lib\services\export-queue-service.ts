import { createServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { ExportService } from '@/lib/export/export-service'
import { formatDistanceToNow } from 'date-fns'

interface ExportJobData {
  id: string
  projectId: string
  projectTitle: string
  userId: string
  format: string
  options?: any
}

export class ExportQueueService {
  private static isProcessing = false
  private static processingInterval: NodeJS.Timeout | null = null

  /**
   * Start the queue processor
   */
  static startProcessor() {
    if (this.processingInterval) {
      return
    }

    // Process queue every 5 seconds
    this.processingInterval = setInterval(() => {
      this.processQueue()
    }, 5000)

    // Process immediately
    this.processQueue()
  }

  /**
   * Stop the queue processor
   */
  static stopProcessor() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }
  }

  /**
   * Add a job to the queue
   */
  static async addJob(data: {
    projectId: string
    projectTitle: string
    userId: string
    format: string
    options?: any
  }): Promise<string> {
    try {
      const supabase = await createServerClient()
      
      // Estimate processing time based on format and project size
      const estimatedTime = this.estimateProcessingTime(data.format)

      const { data: job, error } = await supabase
        .from('export_jobs')
        .insert({
          project_id: data.projectId,
          project_title: data.projectTitle,
          user_id: data.userId,
          format: data.format,
          status: 'pending',
          progress: 0,
          estimated_time: estimatedTime,
          metadata: data.options || {}
        })
        .select()
        .single()

      if (error) {
        throw error
      }

      logger.info(`Export job created: ${job.id} for project ${data.projectId}`)
      
      // Start processor if not running
      this.startProcessor()

      return job.id
    } catch (error) {
      logger.error('Failed to create export job:', error)
      throw error
    }
  }

  /**
   * Process the queue
   */
  private static async processQueue() {
    if (this.isProcessing) {
      return
    }

    this.isProcessing = true

    try {
      const supabase = await createServerClient()
      
      // Get next pending job
      const { data: job, error } = await supabase
        .from('export_jobs')
        .select('*')
        .eq('status', 'pending')
        .order('created_at', { ascending: true })
        .limit(1)
        .single()

      if (error || !job) {
        // No jobs to process
        return
      }

      logger.info(`Processing export job: ${job.id}`)

      // Update job status to processing
      await this.updateJobStatus(job.id, 'processing', 0, 'Initializing export...')

      try {
        await this.processJob(job)
      } catch (error) {
        logger.error(`Export job ${job.id} failed:`, error)
        await this.updateJobStatus(
          job.id, 
          'failed', 
          0, 
          'Export failed',
          error instanceof Error ? error.message : 'Unknown error'
        )
      }
    } catch (error) {
      logger.error('Queue processing error:', error)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Process a single job
   */
  private static async processJob(job: any) {
    const startTime = Date.now()
    const supabase = await createServerClient()

    try {
      // Get project data
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', job.project_id)
        .single()

      if (projectError || !project) {
        throw new Error('Project not found')
      }

      // Get chapters
      await this.updateJobStatus(job.id, 'processing', 10, 'Loading chapters...')
      
      const { data: chapters, error: chaptersError } = await supabase
        .from('chapters')
        .select('*')
        .eq('project_id', job.project_id)
        .order('chapter_number')

      if (chaptersError) {
        throw chaptersError
      }

      // Get characters
      await this.updateJobStatus(job.id, 'processing', 20, 'Loading characters...')
      
      const { data: characters } = await supabase
        .from('characters')
        .select('*')
        .eq('project_id', job.project_id)

      // Get story arc
      const { data: storyArc } = await supabase
        .from('story_arcs')
        .select('*')
        .eq('project_id', job.project_id)
        .single()

      // Prepare export data
      await this.updateJobStatus(job.id, 'processing', 30, 'Preparing content...')
      
      const exportData = {
        project,
        chapters: chapters || [],
        characters: characters || [],
        storyArc,
        metadata: {
          exportDate: new Date().toISOString(),
          format: job.format,
          ...job.metadata
        }
      }

      // Export based on format
      await this.updateJobStatus(job.id, 'processing', 50, `Generating ${job.format.toUpperCase()} file...`)
      
      let result: Blob
      switch (job.format) {
        case 'txt':
          result = await ExportService.exportToTxt(exportData)
          break
        case 'markdown':
          result = await ExportService.exportToMarkdown(exportData)
          break
        case 'docx':
          result = await ExportService.exportToDocx(exportData)
          break
        case 'pdf':
          result = await ExportService.exportToPdf(exportData)
          break
        case 'epub':
          result = await ExportService.exportToEpub(exportData)
          break
        default:
          throw new Error(`Unsupported format: ${job.format}`)
      }

      // Upload to storage
      await this.updateJobStatus(job.id, 'processing', 80, 'Uploading file...')
      
      const fileName = `${project.title}_${new Date().getTime()}.${job.format}`
      const filePath = `exports/${job.user_id}/${job.project_id}/${fileName}`
      
      const { error: uploadError } = await supabase.storage
        .from('project-files')
        .upload(filePath, result, {
          contentType: this.getMimeType(job.format),
          upsert: false
        })

      if (uploadError) {
        throw uploadError
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('project-files')
        .getPublicUrl(filePath)

      // Calculate processing time
      const processingTime = Math.round((Date.now() - startTime) / 1000)

      // Update job as completed
      await supabase
        .from('export_jobs')
        .update({
          status: 'completed',
          progress: 100,
          current_step: 'Export completed',
          file_url: publicUrl,
          file_size: result.size,
          processing_time: processingTime,
          updated_at: new Date().toISOString()
        })
        .eq('id', job.id)

      logger.info(`Export job ${job.id} completed in ${processingTime}s`)
    } catch (error) {
      throw error
    }
  }

  /**
   * Update job status
   */
  private static async updateJobStatus(
    jobId: string,
    status: string,
    progress: number,
    currentStep: string,
    error?: string
  ) {
    try {
      const supabase = await createServerClient()
      
      const updateData: any = {
        status,
        progress,
        current_step: currentStep,
        updated_at: new Date().toISOString()
      }

      if (error) {
        updateData.error = error
      }

      await supabase
        .from('export_jobs')
        .update(updateData)
        .eq('id', jobId)
    } catch (error) {
      logger.error('Failed to update job status:', error)
    }
  }

  /**
   * Cancel a job
   */
  static async cancelJob(jobId: string): Promise<boolean> {
    try {
      const supabase = await createServerClient()
      
      const { error } = await supabase
        .from('export_jobs')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', jobId)
        .eq('status', 'processing')

      if (error) {
        throw error
      }

      return true
    } catch (error) {
      logger.error('Failed to cancel job:', error)
      return false
    }
  }

  /**
   * Estimate processing time based on format
   */
  private static estimateProcessingTime(format: string): number {
    const baseTime = 10 // seconds
    
    switch (format) {
      case 'txt':
      case 'markdown':
        return baseTime
      case 'docx':
        return baseTime * 2
      case 'pdf':
        return baseTime * 3
      case 'epub':
        return baseTime * 4
      default:
        return baseTime
    }
  }

  /**
   * Get MIME type for format
   */
  private static getMimeType(format: string): string {
    switch (format) {
      case 'txt':
        return 'text/plain'
      case 'markdown':
        return 'text/markdown'
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      case 'pdf':
        return 'application/pdf'
      case 'epub':
        return 'application/epub+zip'
      default:
        return 'application/octet-stream'
    }
  }

  /**
   * Clean up old jobs
   */
  static async cleanupOldJobs(daysToKeep: number = 30) {
    try {
      const supabase = await createServerClient()
      
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

      // Get old jobs to delete their files
      const { data: oldJobs } = await supabase
        .from('export_jobs')
        .select('id, file_url')
        .in('status', ['completed', 'failed', 'cancelled'])
        .lt('created_at', cutoffDate.toISOString())

      if (oldJobs && oldJobs.length > 0) {
        // Delete files from storage
        for (const job of oldJobs) {
          if (job.file_url) {
            try {
              const url = new URL(job.file_url)
              const filePath = url.pathname.split('/storage/v1/object/public/project-files/')[1]
              if (filePath) {
                await supabase.storage
                  .from('project-files')
                  .remove([filePath])
              }
            } catch (error) {
              logger.warn(`Failed to delete file for job ${job.id}:`, error)
            }
          }
        }

        // Delete job records
        const { error } = await supabase
          .from('export_jobs')
          .delete()
          .in('id', oldJobs.map(j => j.id))

        if (error) {
          logger.error('Failed to delete old jobs:', error)
        } else {
          logger.info(`Cleaned up ${oldJobs.length} old export jobs`)
        }
      }
    } catch (error) {
      logger.error('Cleanup error:', error)
    }
  }
}