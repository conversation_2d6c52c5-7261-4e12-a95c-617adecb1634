#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to replace hardcoded Tailwind color classes with semantic color tokens
 * This ensures consistent theming across all components
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Mapping of hardcoded colors to semantic tokens
const colorReplacements = {
  // Text colors
  'text-green-500': 'text-success',
  'text-green-600': 'text-success',
  'text-green-700': 'text-success',
  'text-emerald-500': 'text-success',
  'text-emerald-600': 'text-success',
  
  'text-red-500': 'text-error',
  'text-red-600': 'text-error',
  'text-red-700': 'text-error',
  
  'text-yellow-500': 'text-warning',
  'text-yellow-600': 'text-warning',
  'text-amber-500': 'text-warning',
  'text-amber-600': 'text-warning',
  'text-orange-500': 'text-warning',
  'text-orange-600': 'text-warning',
  
  'text-blue-500': 'text-info',
  'text-blue-600': 'text-info',
  'text-indigo-500': 'text-info',
  'text-indigo-600': 'text-info',
  
  // Background colors
  'bg-green-500': 'bg-success',
  'bg-green-600': 'bg-success',
  'bg-emerald-500': 'bg-success',
  'bg-emerald-600': 'bg-success',
  'bg-green-50': 'bg-success-light',
  'bg-green-100': 'bg-success-light',
  
  'bg-red-500': 'bg-error',
  'bg-red-600': 'bg-error',
  'bg-red-50': 'bg-error-light',
  'bg-red-100': 'bg-error-light',
  
  'bg-yellow-500': 'bg-warning',
  'bg-yellow-600': 'bg-warning',
  'bg-amber-500': 'bg-warning',
  'bg-amber-600': 'bg-warning',
  'bg-yellow-50': 'bg-warning-light',
  'bg-yellow-100': 'bg-warning-light',
  
  'bg-blue-500': 'bg-info',
  'bg-blue-600': 'bg-info',
  'bg-indigo-500': 'bg-info',
  'bg-indigo-600': 'bg-info',
  'bg-blue-50': 'bg-info-light',
  'bg-blue-100': 'bg-info-light',
  
  // Border colors
  'border-green-500': 'border-success',
  'border-green-600': 'border-success',
  'border-emerald-500': 'border-success',
  
  'border-red-500': 'border-error',
  'border-red-600': 'border-error',
  
  'border-yellow-500': 'border-warning',
  'border-yellow-600': 'border-warning',
  'border-amber-500': 'border-warning',
  
  'border-blue-500': 'border-info',
  'border-blue-600': 'border-info',
  'border-indigo-500': 'border-info',
  
  // Special cases for dynamic classes
  '[&>div]:bg-green-500': '[&>div]:bg-success',
  '[&>div]:bg-amber-500': '[&>div]:bg-warning',
  '[&>div]:bg-blue-500': '[&>div]:bg-info',
  
  // Hover states
  'hover:bg-green-600': 'hover:bg-success/90',
  'hover:bg-emerald-600': 'hover:bg-success/90',
  'hover:bg-red-600': 'hover:bg-error/90',
  'hover:bg-yellow-600': 'hover:bg-warning/90',
  'hover:bg-amber-600': 'hover:bg-warning/90',
  'hover:bg-blue-600': 'hover:bg-info/90',
  
  // Focus states
  'focus:ring-green-500': 'focus:ring-success',
  'focus:ring-red-500': 'focus:ring-error',
  'focus:ring-yellow-500': 'focus:ring-warning',
  'focus:ring-blue-500': 'focus:ring-info',
};

// Files to exclude from replacement
const excludePatterns = [
  '**/node_modules/**',
  '**/.next/**',
  '**/dist/**',
  '**/build/**',
  '**/*.min.js',
  '**/*.min.css',
  '**/semantic-colors.css', // Don't replace in our semantic colors file
  '**/globals.css', // Theme definitions should stay as-is
];

function replaceInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // Track replacements for reporting
  const replacements = [];
  
  // Replace each hardcoded color with semantic token
  Object.entries(colorReplacements).forEach(([oldColor, newColor]) => {
    // Create regex that matches the color class in various contexts
    const regex = new RegExp(`\\b${oldColor}\\b`, 'g');
    const matches = content.match(regex);
    
    if (matches && matches.length > 0) {
      content = content.replace(regex, newColor);
      hasChanges = true;
      replacements.push({ oldColor, newColor, count: matches.length });
    }
  });
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated ${filePath}`);
    replacements.forEach(({ oldColor, newColor, count }) => {
      console.log(`   - Replaced ${count} instances of "${oldColor}" with "${newColor}"`);
    });
    return true;
  }
  
  return false;
}

function processFiles() {
  console.log('🎨 Replacing hardcoded colors with semantic tokens...\n');
  
  // Find all TypeScript/TSX files
  const files = glob.sync('src/**/*.{ts,tsx}', {
    ignore: excludePatterns,
    absolute: true,
  });
  
  let updatedCount = 0;
  let totalFiles = files.length;
  
  files.forEach((file, index) => {
    process.stdout.write(`\rProcessing files: ${index + 1}/${totalFiles}`);
    
    if (replaceInFile(file)) {
      updatedCount++;
    }
  });
  
  console.log(`\n\n✨ Color replacement complete!`);
  console.log(`📊 Updated ${updatedCount} out of ${totalFiles} files`);
  console.log(`\n💡 Remember to:`);
  console.log(`   - Test the application thoroughly`);
  console.log(`   - Check dark mode compatibility`);
  console.log(`   - Verify all themes work correctly`);
}

// Run the script
if (require.main === module) {
  processFiles();
}

module.exports = { replaceInFile, colorReplacements };