import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface TestProject {
  id: string;
  title: string;
  description: string;
  ownerId: string;
  createdAt: string;
}

export async function createTestProject(projectData: {
  title: string;
  description: string;
  ownerId: string;
}): Promise<TestProject> {
  const projectId = uuidv4();

  const { data, error } = await supabase
    .from('projects')
    .insert({
      id: projectId,
      title: projectData.title,
      description: projectData.description,
      owner_id: projectData.ownerId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      settings: {
        primaryGenre: 'fantasy',
        targetAudience: 'adult',
        writingStyle: 'descriptive',
        narrativeVoice: 'third-person',
        tense: 'past',
        pacing: 'medium'
      }
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test project: ${error.message}`);
  }

  // Add owner as project member
  await supabase
    .from('project_members')
    .insert({
      project_id: projectId,
      user_id: projectData.ownerId,
      role: 'owner',
      created_at: new Date().toISOString()
    });

  return {
    id: data.id,
    title: data.title,
    description: data.description,
    ownerId: data.owner_id,
    createdAt: data.created_at
  };
}

export async function deleteTestProject(projectId: string): Promise<void> {
  try {
    // Delete related data first
    await supabase.from('chapters').delete().eq('project_id', projectId);
    await supabase.from('characters').delete().eq('project_id', projectId);
    await supabase.from('project_members').delete().eq('project_id', projectId);
    await supabase.from('project_invitations').delete().eq('project_id', projectId);
    await supabase.from('activity_logs').delete().eq('project_id', projectId);
    
    // Delete the project
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);

    if (error) {
      console.error(`Failed to delete test project ${projectId}:`, error);
    }
  } catch (error) {
    console.error(`Error cleaning up test project ${projectId}:`, error);
  }
}

export async function addProjectMember(
  projectId: string,
  userId: string,
  role: 'owner' | 'editor' | 'viewer' = 'editor'
): Promise<void> {
  const { error } = await supabase
    .from('project_members')
    .insert({
      project_id: projectId,
      user_id: userId,
      role,
      created_at: new Date().toISOString()
    });

  if (error) {
    throw new Error(`Failed to add project member: ${error.message}`);
  }
}

export async function createProjectInvitation(
  projectId: string,
  invitedEmail: string,
  invitedById: string,
  role: 'editor' | 'viewer' = 'editor'
): Promise<string> {
  const invitationId = uuidv4();

  const { error } = await supabase
    .from('project_invitations')
    .insert({
      id: invitationId,
      project_id: projectId,
      invited_email: invitedEmail,
      invited_by: invitedById,
      role,
      status: 'pending',
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    });

  if (error) {
    throw new Error(`Failed to create invitation: ${error.message}`);
  }

  return invitationId;
}

export async function acceptProjectInvitation(invitationId: string, userId: string): Promise<void> {
  // Get invitation details
  const { data: invitation, error: fetchError } = await supabase
    .from('project_invitations')
    .select('*')
    .eq('id', invitationId)
    .single();

  if (fetchError || !invitation) {
    throw new Error(`Failed to fetch invitation: ${fetchError?.message}`);
  }

  // Update invitation status
  const { error: updateError } = await supabase
    .from('project_invitations')
    .update({
      status: 'accepted',
      accepted_at: new Date().toISOString(),
      accepted_by: userId
    })
    .eq('id', invitationId);

  if (updateError) {
    throw new Error(`Failed to update invitation: ${updateError.message}`);
  }

  // Add user as project member
  await addProjectMember(invitation.project_id, userId, invitation.role);
}

export async function createTestChapter(projectId: string, chapterData: {
  title: string;
  content: string;
  orderIndex: number;
}): Promise<string> {
  const chapterId = uuidv4();

  const { error } = await supabase
    .from('chapters')
    .insert({
      id: chapterId,
      project_id: projectId,
      title: chapterData.title,
      content: chapterData.content,
      order_index: chapterData.orderIndex,
      word_count: chapterData.content.split(' ').length,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

  if (error) {
    throw new Error(`Failed to create test chapter: ${error.message}`);
  }

  return chapterId;
}