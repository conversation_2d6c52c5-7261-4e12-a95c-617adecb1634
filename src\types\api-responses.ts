/**
 * Type definitions for API responses
 */

import type { Project, WritingSession, StoryBible } from '@/lib/db/types'

// Analytics Sessions Response Types
export interface AnalyticsSessionResponse {
  sessions: WritingSession[]
  count: number
  streak: number
  overview?: {
    totalWords: number
    totalSessions: number
    totalDuration: number
    avgSessionDuration: number
    avgWordsPerSession: number
    avgWordsPerMinute: number
  }
  hourlyPattern?: Array<{ date: string; value: number }>
  weeklyPattern?: Array<{ date: string; value: number }>
  dailyData?: Array<{ date: string; value: number; sessions: number }>
}

// Projects Response Types
export interface ProjectProgress {
  wordsWritten: number
  targetWords: number
  chaptersComplete: number
  targetChapters: number
  percentComplete: number
}

export interface ProjectAIProcessing {
  isActive: boolean
  currentTask?: string
  tasksInQueue: number
  lastProcessed?: string | Date
}

export interface ProjectMetadata {
  createdAt: Date
  updatedAt: Date
  targetAudience: string
  contentRating: string
  estimatedReadTime: number
}

export interface ProjectStats {
  dailyWords: number
  weeklyWords: number
  streak: number
  avgWordsPerDay: number
}

export interface ProjectWithProcessing extends Project {
  progress: ProjectProgress
  aiProcessing: ProjectAIProcessing
  metadata: ProjectMetadata
  stats: ProjectStats
  settings: Record<string, unknown> | null
}

// Orchestration Response Types
export interface OrchestrationTask {
  id: string
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress?: number
  error?: string
}

export interface OrchestrationProgressResponse {
  success: boolean
  data?: {
    totalTasks: number
    completedTasks: number
    runningTasks: number
    failedTasks: number
    estimatedTimeRemaining: number
    currentPhase: string
    taskDetails: OrchestrationTask[]
  }
  error?: string
}

// Agent Initialization Response Types
export interface AgentServices {
  orchestrator: boolean
  contentGenerator: boolean
  contextManager: boolean
  analytics: boolean
  semanticSearch: boolean
}

export interface AgentStatus {
  storyArchitect: boolean
  characterDeveloper: boolean
  chapterPlanner: boolean
  adaptivePlanner: boolean
  writingAgent: boolean
}

export interface AgentContext {
  projectId: string
  initialized: boolean
  agentCount: number
}

export interface AgentOrchestration {
  skipped: boolean
  status?: string
}

export interface AgentInitializationResult {
  success: boolean
  projectId: string
  initializationType: 'basic' | 'full' | 'services_only'
  timestamp: string
  services?: AgentServices
  agents?: AgentStatus
  context?: AgentContext
  orchestration?: AgentOrchestration
  error?: string
}

// Profile Response Types
export interface ProfileData {
  id: string
  user_id: string
  name: string
  description: string
  category: 'custom' | 'template' | 'shared'
  is_public: boolean
  is_featured: boolean
  settings: Record<string, unknown>
  tags: string[]
  usage_count: number
  created_at: string
  updated_at: string
}

export interface ProfileCreationResponse {
  profile: ProfileData
}

// Writing Pattern Types
export interface HourlyPattern {
  hour: string
  words: number
  sessions: number
}

export interface WeeklyPattern {
  day: string
  words: number
  sessions: number
}

export interface DailyPattern {
  date: string
  words: number
  sessions: number
}

export interface WritingPatternData {
  hourly: HourlyPattern[]
  weekly: WeeklyPattern[]
  daily: Map<string, DailyPattern>
}

// Generic API Response Types
export interface ApiSuccessResponse<T> {
  success: true
  data: T
}

export interface ApiErrorResponse {
  success: false
  error: string
  details?: unknown
}

export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse