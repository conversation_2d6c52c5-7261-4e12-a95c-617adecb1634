-- Fixed version: Create AI usage logs table for tracking AI agent usage
-- This is needed for AI-related achievements

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS update_writing_session(UUI<PERSON>, UUID, INTEGER);
DROP FUNCTION IF EXISTS log_ai_usage();

-- Create AI usage logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS ai_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  agent_type VARCHAR(100) NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10, 4) DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_user ON ai_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_project ON ai_usage_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_created ON ai_usage_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_agent_type ON ai_usage_logs(agent_type);

-- Enable RLS
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own AI usage" ON ai_usage_logs;
DROP POLICY IF EXISTS "Users can insert their own AI usage" ON ai_usage_logs;

-- Create RLS policies
CREATE POLICY "Users can view their own AI usage" ON ai_usage_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own AI usage" ON ai_usage_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Grant permissions
GRANT SELECT, INSERT ON ai_usage_logs TO authenticated;

-- Create writing sessions table for tracking daily streaks
CREATE TABLE IF NOT EXISTS writing_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  session_date DATE NOT NULL,
  words_written INTEGER DEFAULT 0,
  duration_minutes INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, session_date)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user ON writing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_date ON writing_sessions(session_date DESC);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_project ON writing_sessions(project_id);

-- Enable RLS
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own writing sessions" ON writing_sessions;
DROP POLICY IF EXISTS "Users can manage their own writing sessions" ON writing_sessions;

-- Create RLS policies
CREATE POLICY "Users can view their own writing sessions" ON writing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own writing sessions" ON writing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON writing_sessions TO authenticated;

-- Create trigger to log AI usage when AI endpoints are used
CREATE OR REPLACE FUNCTION log_ai_usage()
RETURNS TRIGGER AS $$
BEGIN
  -- This function would be called by triggers on AI-related operations
  -- For now, it's a placeholder
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update writing sessions
CREATE OR REPLACE FUNCTION update_writing_session(
  p_user_id UUID,
  p_project_id UUID,
  p_words_written INTEGER
)
RETURNS VOID AS $$
DECLARE
  v_session_id UUID;
BEGIN
  -- First try to update existing session
  UPDATE writing_sessions 
  SET 
    words_written = words_written + p_words_written,
    duration_minutes = GREATEST(
      duration_minutes,
      EXTRACT(EPOCH FROM (NOW() - created_at)) / 60
    )::INTEGER
  WHERE user_id = p_user_id 
  AND session_date = CURRENT_DATE
  RETURNING id INTO v_session_id;
  
  -- If no existing session, insert new one
  IF v_session_id IS NULL THEN
    INSERT INTO writing_sessions (user_id, project_id, session_date, words_written)
    VALUES (p_user_id, p_project_id, CURRENT_DATE, p_words_written);
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION update_writing_session(UUID, UUID, INTEGER) TO authenticated;

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE 'AI usage logs and writing sessions tables created successfully!';
    RAISE NOTICE 'Tables created: ai_usage_logs, writing_sessions';
    RAISE NOTICE 'Functions created: log_ai_usage(), update_writing_session()';
END $$;