/**
 * Responsive Design Utilities
 * Helper functions and hooks for responsive design
 */

import { useEffect, useState } from 'react'

// Breakpoint values (matching Tailwind defaults)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof BREAKPOINTS

// Media query strings
export const MEDIA_QUERIES = {
  sm: `(min-width: ${BREAKPOINTS.sm}px)`,
  md: `(min-width: ${BREAKPOINTS.md}px)`,
  lg: `(min-width: ${BREAKPOINTS.lg}px)`,
  xl: `(min-width: ${BREAKPOINTS.xl}px)`,
  '2xl': `(min-width: ${BREAKPOINTS['2xl']}px)`,
  mobile: `(max-width: ${BREAKPOINTS.sm - 1}px)`,
  tablet: `(min-width: ${BREAKPOINTS.sm}px) and (max-width: ${BREAKPOINTS.lg - 1}px)`,
  desktop: `(min-width: ${BREAKPOINTS.lg}px)`,
  touch: '(hover: none) and (pointer: coarse)',
  mouse: '(hover: hover) and (pointer: fine)',
} as const

// Hook to detect current breakpoint
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<Breakpoint | 'base'>('base')

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      
      if (width >= BREAKPOINTS['2xl']) {
        setBreakpoint('2xl')
      } else if (width >= BREAKPOINTS.xl) {
        setBreakpoint('xl')
      } else if (width >= BREAKPOINTS.lg) {
        setBreakpoint('lg')
      } else if (width >= BREAKPOINTS.md) {
        setBreakpoint('md')
      } else if (width >= BREAKPOINTS.sm) {
        setBreakpoint('sm')
      } else {
        setBreakpoint('base')
      }
    }

    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  return breakpoint
}

// Hook to check if screen matches a media query
export function useMediaQuery(query: string) {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia(query)
    setMatches(mediaQuery.matches)

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    mediaQuery.addEventListener('change', handler)
    return () => mediaQuery.removeEventListener('change', handler)
  }, [query])

  return matches
}

// Convenience hooks for common queries
export function useIsMobile() {
  return useMediaQuery(MEDIA_QUERIES.mobile)
}

export function useIsTablet() {
  return useMediaQuery(MEDIA_QUERIES.tablet)
}

export function useIsDesktop() {
  return useMediaQuery(MEDIA_QUERIES.desktop)
}

export function useIsTouchDevice() {
  return useMediaQuery(MEDIA_QUERIES.touch)
}

// Hook to check if screen is at least a certain size
export function useIsAtLeast(breakpoint: Breakpoint) {
  return useMediaQuery(MEDIA_QUERIES[breakpoint])
}

// Get responsive value based on current breakpoint
export function useResponsiveValue<T>(values: {
  base: T
  sm?: T
  md?: T
  lg?: T
  xl?: T
  '2xl'?: T
}) {
  const breakpoint = useBreakpoint()
  
  // Find the appropriate value for current breakpoint
  const breakpoints: (Breakpoint | 'base')[] = ['base', 'sm', 'md', 'lg', 'xl', '2xl']
  const currentIndex = breakpoints.indexOf(breakpoint)
  
  // Work backwards from current breakpoint to find a defined value
  for (let i = currentIndex; i >= 0; i--) {
    const bp = breakpoints[i]
    if (bp in values && values[bp as keyof typeof values] !== undefined) {
      return values[bp as keyof typeof values]!
    }
  }
  
  return values.base
}

// Responsive padding/margin classes
export const responsiveSpacing = (
  base: string,
  sm?: string,
  md?: string,
  lg?: string,
  xl?: string
) => {
  const classes = [base]
  if (sm) classes.push(`sm:${sm}`)
  if (md) classes.push(`md:${md}`)
  if (lg) classes.push(`lg:${lg}`)
  if (xl) classes.push(`xl:${xl}`)
  return classes.join(' ')
}

// Responsive grid classes
export const responsiveGrid = (
  base: number,
  sm?: number,
  md?: number,
  lg?: number,
  xl?: number
) => {
  const classes = [`grid-cols-${base}`]
  if (sm) classes.push(`sm:grid-cols-${sm}`)
  if (md) classes.push(`md:grid-cols-${md}`)
  if (lg) classes.push(`lg:grid-cols-${lg}`)
  if (xl) classes.push(`xl:grid-cols-${xl}`)
  return classes.join(' ')
}

// Touch-friendly tap target sizes
export const TAP_TARGET_SIZE = {
  SMALL: 'min-h-[36px] min-w-[36px]', // 36px minimum
  MEDIUM: 'min-h-[44px] min-w-[44px]', // 44px recommended
  LARGE: 'min-h-[48px] min-w-[48px]', // 48px comfortable
} as const

// Mobile-first visibility utilities
export const VISIBILITY = {
  MOBILE_ONLY: 'block sm:hidden',
  TABLET_ONLY: 'hidden sm:block lg:hidden',
  DESKTOP_ONLY: 'hidden lg:block',
  MOBILE_AND_TABLET: 'block lg:hidden',
  TABLET_AND_DESKTOP: 'hidden sm:block',
} as const

// Responsive text size utilities
export const responsiveText = (
  base: string,
  sm?: string,
  md?: string,
  lg?: string
) => {
  const classes = [base]
  if (sm) classes.push(`sm:${sm}`)
  if (md) classes.push(`md:${md}`)
  if (lg) classes.push(`lg:${lg}`)
  return classes.join(' ')
}