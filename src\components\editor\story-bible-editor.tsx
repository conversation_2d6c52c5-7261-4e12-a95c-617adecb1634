'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
 
  Users, 
  Map, 
  Clock, 
  Lightbulb 
} from 'lucide-react'

interface Character {
  id: string
  name: string
  role: string
  description: string
  backstory: string
  personality: Record<string, unknown>
  relationships: Array<Record<string, unknown>>
}

interface TimelineEvent {
  id?: string
  event: string
  chapter: number
}

interface PlotThread {
  id: string
  description: string
  status: 'active' | 'resolved'
}

interface WorldRule {
  key: string
  value: string
}

type EditingItem = Character | TimelineEvent | PlotThread | WorldRule | null

interface StoryBibleEditorProps {
  projectId: string
  storyBible: {
    characters: Array<Character>
    worldRules: Record<string, string>
    timeline: Array<TimelineEvent>
    plotThreads: Array<PlotThread>
  }
  onUpdate?: () => void
}

export function StoryBibleEditor({ projectId, storyBible, onUpdate }: StoryBibleEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editingItem, setEditingItem] = useState<EditingItem>(null)
  const [editingType, setEditingType] = useState<'character' | 'world' | 'timeline' | 'plot' | null>(null)

  // Character editing
  const [characterForm, setCharacterForm] = useState({
    name: '',
    role: '',
    description: '',
    backstory: '',
    traits: ''
  })

  // World rule editing
  const [worldForm, setWorldForm] = useState({
    key: '',
    value: ''
  })

  // Timeline editing
  const [timelineForm, setTimelineForm] = useState({
    event: '',
    chapter: 1
  })

  // Plot thread editing
  const [plotForm, setPlotForm] = useState({
    description: '',
    status: 'active' as 'active' | 'resolved'
  })

  const resetForms = () => {
    setCharacterForm({ name: '', role: '', description: '', backstory: '', traits: '' })
    setWorldForm({ key: '', value: '' })
    setTimelineForm({ event: '', chapter: 1 })
    setPlotForm({ description: '', status: 'active' })
    setEditingItem(null)
    setEditingType(null)
    setIsEditing(false)
  }

  const handleEdit = (type: 'character' | 'world' | 'timeline' | 'plot', item: Character | TimelineEvent | PlotThread | WorldRule | Record<string, unknown>) => {
    setEditingType(type)
    setEditingItem(item as EditingItem)
    setIsEditing(true)

    switch (type) {
      case 'character':
        if ('name' in item && typeof item.name === 'string') {
          const characterItem = item as Character
          setCharacterForm({
            name: characterItem.name || '',
            role: characterItem.role || '',
            description: characterItem.description || '',
            backstory: characterItem.backstory || '',
            traits: characterItem.personality && Array.isArray(characterItem.personality.traits) 
              ? characterItem.personality.traits.join(', ') 
              : ''
          })
        }
        break
      case 'world':
        if ('key' in item && 'value' in item) {
          const worldItem = item as WorldRule
          setWorldForm({
            key: worldItem.key || '',
            value: worldItem.value || ''
          })
        }
        break
      case 'timeline':
        if ('event' in item) {
          const timelineItem = item as unknown as { event: string; chapter: number }
          setTimelineForm({
            event: timelineItem.event || '',
            chapter: timelineItem.chapter || 1
          })
        }
        break
      case 'plot':
        if ('description' in item) {
          const plotItem = item as PlotThread
          setPlotForm({
            description: plotItem.description || '',
            status: plotItem.status || 'active'
          })
        }
        break
    }
  }

  const handleSave = async () => {
    try {
      let updateData: Record<string, unknown> = {}

      switch (editingType) {
        case 'character':
          updateData = {
            type: 'character',
            id: editingItem && 'id' in editingItem ? editingItem.id : undefined,
            data: {
              ...characterForm,
              traits: characterForm.traits.split(',').map(t => t.trim()).filter(Boolean)
            }
          }
          break
        case 'world':
          updateData = {
            type: 'world_rule',
            id: editingItem && 'key' in editingItem ? editingItem.key : undefined,
            data: worldForm
          }
          break
        case 'timeline':
          updateData = {
            type: 'timeline_event',
            id: editingItem && 'id' in editingItem ? editingItem.id : undefined,
            data: timelineForm
          }
          break
        case 'plot':
          updateData = {
            type: 'plot_thread',
            id: editingItem && 'id' in editingItem ? editingItem.id : undefined,
            data: plotForm
          }
          break
      }

      const response = await fetch('/api/story-bible/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          ...updateData,
          action: editingItem ? 'update' : 'create'
        })
      })

      if (response.ok) {
        resetForms()
        onUpdate?.()
      } else {
        throw new Error('Failed to save')
      }
    } catch {
      alert('Failed to save changes')
    }
  }

  const handleDelete = async (type: string, id: string) => {
    try {
      const response = await fetch('/api/story-bible/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          type,
          id,
          action: 'delete'
        })
      })

      if (response.ok) {
        onUpdate?.()
      } else {
        throw new Error('Failed to delete')
      }
    } catch {
      alert('Failed to delete item')
    }
  }

  return (
    <div className="space-y-6">
      {/* Characters Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Characters
            </CardTitle>
            <Button 
              size="sm" 
              onClick={() => handleEdit('character', {})}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Character
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 sm:gap-5 lg:gap-6">
            {storyBible.characters.map((character) => (
              <Card key={character.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium">{character.name}</h4>
                      <Badge variant="outline">{character.role}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {character.description}
                    </p>
                    {(() => {
                      const traits = character.personality?.traits;
                      if (traits && Array.isArray(traits)) {
                        return (
                          <div className="flex flex-wrap gap-1">
                            {traits.slice(0, 3).map((trait: string, index: number) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {trait}
                              </Badge>
                            ))}
                          </div>
                        );
                      }
                      return null;
                    })()}
                  </div>
                  <div className="flex gap-1">
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleEdit('character', character)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleDelete('character', character.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* World Rules Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Map className="h-5 w-5" />
              World Rules
            </CardTitle>
            <Button 
              size="sm" 
              onClick={() => handleEdit('world', {})}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Rule
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {Object.entries(storyBible.worldRules).map(([key, value]) => (
              <Card key={key} className="p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm capitalize mb-1">
                      {key.replace(/_/g, ' ')}
                    </h4>
                    <p className="text-sm text-muted-foreground">{value}</p>
                  </div>
                  <div className="flex gap-1">
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleEdit('world', { key, value })}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleDelete('world_rule', key)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Timeline Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Timeline
            </CardTitle>
            <Button 
              size="sm" 
              onClick={() => handleEdit('timeline', {})}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Event
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {storyBible.timeline.map((event, index) => (
              <Card key={index} className="p-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <Badge variant="outline" className="text-xs">
                      Ch. {event.chapter}
                    </Badge>
                    <p className="text-sm flex-1">{event.event}</p>
                  </div>
                  <div className="flex gap-1">
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleEdit('timeline', { ...event, id: index })}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleDelete('timeline_event', index.toString())}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Plot Threads Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Plot Threads
            </CardTitle>
            <Button 
              size="sm" 
              onClick={() => handleEdit('plot', {})}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Thread
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {storyBible.plotThreads.map((thread) => (
              <Card key={thread.id} className="p-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <Badge 
                      variant={thread.status === 'active' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {thread.status}
                    </Badge>
                    <p className="text-sm flex-1">{thread.description}</p>
                  </div>
                  <div className="flex gap-1">
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleEdit('plot', thread)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => handleDelete('plot_thread', thread.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="max-w-2xl lg:max-w-3xl xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {editingItem ? 'Edit' : 'Add'} {editingType?.replace('_', ' ')}
            </DialogTitle>
            <DialogDescription>
              {editingType === 'character' && 'Modify character details and personality traits'}
              {editingType === 'world' && 'Define or update world-building rules'}
              {editingType === 'timeline' && 'Add or edit story timeline events'}
              {editingType === 'plot' && 'Manage plot threads and their status'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {editingType === 'character' && (
              <>
                <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                  <div>
                    <label className="text-sm font-medium">Name</label>
                    <Input 
                      value={characterForm.name}
                      onChange={(e) => setCharacterForm({...characterForm, name: e.target.value})}
                      placeholder="Character name"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Role</label>
                    <Select 
                      value={characterForm.role} 
                      onValueChange={(value) => setCharacterForm({...characterForm, role: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="protagonist">Protagonist</SelectItem>
                        <SelectItem value="antagonist">Antagonist</SelectItem>
                        <SelectItem value="supporting">Supporting</SelectItem>
                        <SelectItem value="minor">Minor</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Textarea 
                    value={characterForm.description}
                    onChange={(e) => setCharacterForm({...characterForm, description: e.target.value})}
                    placeholder="Physical description and key characteristics"
                    rows={3}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Backstory</label>
                  <Textarea 
                    value={characterForm.backstory}
                    onChange={(e) => setCharacterForm({...characterForm, backstory: e.target.value})}
                    placeholder="Character's history and background"
                    rows={3}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Personality Traits</label>
                  <Input 
                    value={characterForm.traits}
                    onChange={(e) => setCharacterForm({...characterForm, traits: e.target.value})}
                    placeholder="Separate traits with commas"
                  />
                </div>
              </>
            )}

            {editingType === 'world' && (
              <>
                <div>
                  <label className="text-sm font-medium">Rule Name</label>
                  <Input 
                    value={worldForm.key}
                    onChange={(e) => setWorldForm({...worldForm, key: e.target.value})}
                    placeholder="e.g., magic_system, technology_level"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Rule Description</label>
                  <Textarea 
                    value={worldForm.value}
                    onChange={(e) => setWorldForm({...worldForm, value: e.target.value})}
                    placeholder="Describe how this rule works in your world"
                    rows={3}
                  />
                </div>
              </>
            )}

            {editingType === 'timeline' && (
              <>
                <div>
                  <label className="text-sm font-medium">Chapter</label>
                  <Input 
                    type="number"
                    value={timelineForm.chapter}
                    onChange={(e) => setTimelineForm({...timelineForm, chapter: parseInt(e.target.value) || 1})}
                    placeholder="Chapter number"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Event Description</label>
                  <Textarea 
                    value={timelineForm.event}
                    onChange={(e) => setTimelineForm({...timelineForm, event: e.target.value})}
                    placeholder="Describe what happens in this event"
                    rows={3}
                  />
                </div>
              </>
            )}

            {editingType === 'plot' && (
              <>
                <div>
                  <label className="text-sm font-medium">Status</label>
                  <Select 
                    value={plotForm.status} 
                    onValueChange={(value: 'active' | 'resolved') => setPlotForm({...plotForm, status: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">Plot Thread Description</label>
                  <Textarea 
                    value={plotForm.description}
                    onChange={(e) => setPlotForm({...plotForm, description: e.target.value})}
                    placeholder="Describe this plot thread"
                    rows={3}
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={resetForms}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}