"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { Progress } from "@/components/ui/progress"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  format,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isSameDay,
  isSameMonth,
  isToday,
  parseISO,
  addMonths,
  subMonths,
} from "date-fns"
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Target,
  TrendingUp,
  Award,
  Flame,
  Edit3,
  <PERSON>,
  BookO<PERSON>,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { logger } from '@/lib/services/logger'

interface WritingCalendarProps {
  userId: string
  projectId?: string
  onDateSelect?: (date: Date) => void
}

interface DayData {
  date: string
  wordCount: number
  sessionCount: number
  timeSpent: number
  projects: string[]
  streak?: number
  goalProgress?: number
}

interface CalendarData {
  [date: string]: DayData
}

interface MonthStats {
  totalWords: number
  totalSessions: number
  totalTime: number
  activeDays: number
  bestDay: { date: string; words: number }
  currentStreak: number
  monthlyGoal?: number
  goalProgress?: number
}

const WORD_COUNT_COLORS = [
  { min: 0, max: 0, color: "bg-muted", label: "No writing" },
  { min: 1, max: 250, color: "bg-green-200 dark:bg-green-900", label: "1-250 words" },
  { min: 251, max: 500, color: "bg-green-300 dark:bg-green-800", label: "251-500 words" },
  { min: 501, max: 1000, color: "bg-green-400 dark:bg-green-700", label: "501-1k words" },
  { min: 1001, max: 2000, color: "bg-green-500 dark:bg-green-600", label: "1k-2k words" },
  { min: 2001, max: Infinity, color: "bg-green-600 dark:bg-green-500", label: "2k+ words" },
]

function getWordCountColor(count: number): string {
  const range = WORD_COUNT_COLORS.find(r => count >= r.min && count <= r.max)
  return range?.color || "bg-muted"
}

export function WritingCalendar({ userId, projectId, onDateSelect }: WritingCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [calendarData, setCalendarData] = useState<CalendarData>({})
  const [monthStats, setMonthStats] = useState<MonthStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [viewMode, setViewMode] = useState<"month" | "week">("month")

  useEffect(() => {
    fetchCalendarData()
  }, [userId, projectId, currentMonth])

  const fetchCalendarData = async () => {
    try {
      setLoading(true)
      
      const start = startOfMonth(currentMonth)
      const end = endOfMonth(currentMonth)
      
      const params = new URLSearchParams({
        startDate: start.toISOString(),
        endDate: end.toISOString(),
        ...(projectId && { projectId })
      })

      const response = await fetch(`/api/analytics/sessions?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch calendar data')
      }

      const data = await response.json()
      
      // Process sessions into calendar format
      const calendarMap: CalendarData = {}
      let totalWords = 0
      let totalSessions = 0
      let totalTime = 0
      let bestDay = { date: '', words: 0 }
      
      data.sessions?.forEach((session: any) => {
        const dateStr = format(parseISO(session.created_at), 'yyyy-MM-dd')
        
        if (!calendarMap[dateStr]) {
          calendarMap[dateStr] = {
            date: dateStr,
            wordCount: 0,
            sessionCount: 0,
            timeSpent: 0,
            projects: []
          }
        }
        
        calendarMap[dateStr].wordCount += session.word_count || 0
        calendarMap[dateStr].sessionCount += 1
        calendarMap[dateStr].timeSpent += session.duration || 0
        
        if (session.project_id && !calendarMap[dateStr].projects.includes(session.project_id)) {
          calendarMap[dateStr].projects.push(session.project_id)
        }
        
        totalWords += session.word_count || 0
        totalSessions += 1
        totalTime += session.duration || 0
        
        if (calendarMap[dateStr].wordCount > bestDay.words) {
          bestDay = { date: dateStr, words: calendarMap[dateStr].wordCount }
        }
      })
      
      setCalendarData(calendarMap)
      setMonthStats({
        totalWords,
        totalSessions,
        totalTime: Math.round(totalTime / 60), // Convert to minutes
        activeDays: Object.keys(calendarMap).length,
        bestDay,
        currentStreak: data.currentStreak || 0,
        monthlyGoal: data.monthlyGoal,
        goalProgress: data.goalProgress
      })
      
    } catch (err) {
      logger.error('Error fetching calendar data:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    onDateSelect?.(date)
  }

  const handleMonthChange = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => 
      direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1)
    )
  }

  const renderCalendarGrid = () => {
    const monthStart = startOfMonth(currentMonth)
    const monthEnd = endOfMonth(currentMonth)
    const calendarStart = startOfWeek(monthStart)
    const calendarEnd = endOfWeek(monthEnd)
    
    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd })
    
    return (
      <div className="grid grid-cols-7 gap-1">
        {/* Weekday headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-xs font-medium text-muted-foreground p-2">
            {day}
          </div>
        ))}
        
        {/* Calendar days */}
        {days.map(day => {
          const dateStr = format(day, 'yyyy-MM-dd')
          const dayData = calendarData[dateStr]
          const isCurrentMonth = isSameMonth(day, currentMonth)
          const isSelected = selectedDate && isSameDay(day, selectedDate)
          const isTodayDate = isToday(day)
          
          return (
            <TooltipProvider key={dateStr}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => handleDateSelect(day)}
                    className={cn(
                      "relative p-2 h-20 border rounded-md transition-all hover:border-primary",
                      !isCurrentMonth && "opacity-40",
                      isSelected && "ring-2 ring-primary",
                      isTodayDate && "border-primary",
                      dayData && getWordCountColor(dayData.wordCount)
                    )}
                  >
                    <div className="absolute top-1 left-1 text-xs font-medium">
                      {format(day, 'd')}
                    </div>
                    
                    {dayData && (
                      <div className="flex flex-col items-center justify-center h-full pt-3">
                        <div className="text-sm font-semibold">
                          {dayData.wordCount.toLocaleString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          words
                        </div>
                        {dayData.sessionCount > 1 && (
                          <div className="text-xs text-muted-foreground">
                            {dayData.sessionCount} sessions
                          </div>
                        )}
                      </div>
                    )}
                    
                    {dayData?.streak && dayData.streak > 0 && (
                      <div className="absolute bottom-1 right-1">
                        <Flame className="h-3 w-3 text-orange-500" />
                      </div>
                    )}
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p className="font-medium">{format(day, 'MMMM d, yyyy')}</p>
                    {dayData ? (
                      <>
                        <p>{dayData.wordCount.toLocaleString()} words written</p>
                        <p>{dayData.sessionCount} writing session{dayData.sessionCount !== 1 ? 's' : ''}</p>
                        <p>{Math.round(dayData.timeSpent / 60)} minutes</p>
                        {dayData.projects.length > 0 && (
                          <p>{dayData.projects.length} project{dayData.projects.length !== 1 ? 's' : ''}</p>
                        )}
                      </>
                    ) : (
                      <p className="text-muted-foreground">No writing activity</p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )
        })}
      </div>
    )
  }

  const renderSelectedDateDetails = () => {
    if (!selectedDate) return null
    
    const dateStr = format(selectedDate, 'yyyy-MM-dd')
    const dayData = calendarData[dateStr]
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            {format(selectedDate, 'EEEE, MMMM d, yyyy')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {dayData ? (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <div className="text-2xl font-bold">{dayData.wordCount.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">words written</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">{dayData.sessionCount}</div>
                  <div className="text-sm text-muted-foreground">session{dayData.sessionCount !== 1 ? 's' : ''}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">{Math.round(dayData.timeSpent / 60)}</div>
                  <div className="text-sm text-muted-foreground">minutes</div>
                </div>
              </div>
              
              {dayData.projects.length > 0 && (
                <div>
                  <div className="text-sm font-medium mb-2">Projects worked on:</div>
                  <div className="flex flex-wrap gap-2">
                    {dayData.projects.map(projectId => (
                      <Badge key={projectId} variant="secondary">
                        Project {projectId.slice(0, 8)}...
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground">No writing activity on this date</p>
          )}
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="h-4 bg-muted animate-pulse rounded" />
            <div className="h-64 bg-muted animate-pulse rounded" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Month Stats */}
      {monthStats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Monthly Total</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{monthStats.totalWords.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">words written</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Active Days</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{monthStats.activeDays}</div>
              <p className="text-xs text-muted-foreground">
                of {format(endOfMonth(currentMonth), 'd')} days
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Best Day</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{monthStats.bestDay.words.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {monthStats.bestDay.date ? format(parseISO(monthStats.bestDay.date), 'MMM d') : 'No activity'}
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Current Streak</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Flame className={cn(
                  "h-5 w-5",
                  monthStats.currentStreak > 0 ? "text-orange-500" : "text-muted-foreground"
                )} />
                <div className="text-2xl font-bold">{monthStats.currentStreak}</div>
              </div>
              <p className="text-xs text-muted-foreground">consecutive days</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Calendar */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Writing Calendar</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleMonthChange('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="min-w-[140px] text-center font-medium">
                {format(currentMonth, 'MMMM yyyy')}
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleMonthChange('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as any)}>
            <TabsList className="mb-4">
              <TabsTrigger value="month">Month View</TabsTrigger>
              <TabsTrigger value="week">Week View</TabsTrigger>
            </TabsList>
            
            <TabsContent value="month">
              {renderCalendarGrid()}
              
              {/* Legend */}
              <div className="mt-4 flex flex-wrap items-center gap-4 text-xs">
                <span className="font-medium">Word count:</span>
                {WORD_COUNT_COLORS.map((range, index) => (
                  <div key={index} className="flex items-center gap-1">
                    <div className={cn("w-4 h-4 rounded", range.color)} />
                    <span>{range.label}</span>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="week">
              <div className="text-center text-muted-foreground py-8">
                Week view coming soon...
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Selected Date Details */}
      {renderSelectedDateDetails()}

      {/* Writing Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Calendar Insights</CardTitle>
          <CardDescription>
            Based on your writing patterns this month
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {monthStats && monthStats.activeDays > 10 && (
              <div className="flex items-start gap-2">
                <Award className="h-4 w-4 text-yellow-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Consistent Writer</p>
                  <p className="text-sm text-muted-foreground">
                    You've written on {monthStats.activeDays} days this month. Great consistency!
                  </p>
                </div>
              </div>
            )}
            
            {monthStats && monthStats.currentStreak >= 7 && (
              <div className="flex items-start gap-2">
                <Flame className="h-4 w-4 text-orange-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">On Fire!</p>
                  <p className="text-sm text-muted-foreground">
                    {monthStats.currentStreak} day streak! Keep the momentum going.
                  </p>
                </div>
              </div>
            )}
            
            {monthStats && monthStats.bestDay.words > 2000 && (
              <div className="flex items-start gap-2">
                <TrendingUp className="h-4 w-4 text-green-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">High Output Day</p>
                  <p className="text-sm text-muted-foreground">
                    Your best day was {format(parseISO(monthStats.bestDay.date), 'MMMM d')} with {monthStats.bestDay.words.toLocaleString()} words!
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}