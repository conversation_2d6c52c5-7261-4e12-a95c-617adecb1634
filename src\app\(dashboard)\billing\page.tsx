'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { formatDistanceToNow } from 'date-fns'
import { 
  CreditCard, 
  Package, 
  Calendar, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  Receipt,
  TrendingUp,
  ExternalLink,
  Sparkles
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { PricingCard } from '@/components/pricing/pricing-card'
import { PricingToggle } from '@/components/pricing/pricing-toggle'
import { 
  SUBSCRIPTION_TIERS, 
  format<PERSON><PERSON>, 
  getUserTier,
  type SubscriptionTier,
  type UserSubscription 
} from '@/lib/subscription'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { API_ENDPOINTS } from '@/lib/config'

interface BillingData {
  subscription: UserSubscription | null
  currentTier: SubscriptionTier
  usage: {
    wordsUsed: number
    projects: number
  }
  availableTiers: SubscriptionTier[]
  billingHistory: Array<{
    id: string
    date: string
    amount: number
    currency: string
    status: 'paid' | 'pending' | 'failed'
    description: string
  }>
}

export default function BillingPage() {
  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [billingData, setBillingData] = useState<BillingData | null>(null)
  const [isAnnual, setIsAnnual] = useState(false)

  useEffect(() => {
    if (user) {
      fetchBillingData()
    }
  }, [user])

  const fetchBillingData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(API_ENDPOINTS.BILLING.SUBSCRIPTIONS)
      if (!response.ok) throw new Error('Failed to fetch billing data')
      
      const data = await response.json()
      
      // Fetch billing history
      const historyResponse = await fetch(API_ENDPOINTS.BILLING.HISTORY)
      const historyData = await historyResponse.json()
      
      setBillingData({
        ...data,
        billingHistory: historyData.history || []
      })
    } catch (error) {
      logger.error('Error fetching billing data', { userId: user?.id }, error as Error)
      toast({
        title: 'Error',
        description: 'Failed to load billing information',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubscribe = async (tier: SubscriptionTier) => {
    try {
      setIsProcessing(true)
      
      const response = await fetch(API_ENDPOINTS.BILLING.SUBSCRIPTIONS, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tierId: tier.id,
          action: 'create_subscription'
        })
      })

      if (!response.ok) throw new Error('Failed to create subscription')
      
      const { sessionUrl } = await response.json()
      
      // Redirect to Stripe Checkout
      window.location.href = sessionUrl
    } catch (error) {
      logger.error('Subscription error', { userId: user?.id, priceId }, error as Error)
      toast({
        title: 'Error',
        description: 'Failed to start subscription process',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will retain access until the end of your billing period.')) {
      return
    }

    try {
      setIsProcessing(true)
      
      const response = await fetch(API_ENDPOINTS.BILLING.SUBSCRIPTIONS, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tierId: billingData?.currentTier.id,
          action: 'cancel_subscription'
        })
      })

      if (!response.ok) throw new Error('Failed to cancel subscription')
      
      toast({
        title: 'Success',
        description: 'Subscription cancelled successfully'
      })
      await fetchBillingData()
    } catch (error) {
      logger.error('Cancellation error', { userId: user?.id }, error as Error)
      toast({
        title: 'Error',
        description: 'Failed to cancel subscription',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReactivateSubscription = async () => {
    try {
      setIsProcessing(true)
      
      const response = await fetch(API_ENDPOINTS.BILLING.SUBSCRIPTIONS, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tierId: billingData?.currentTier.id,
          action: 'reactivate_subscription'
        })
      })

      if (!response.ok) throw new Error('Failed to reactivate subscription')
      
      toast({
        title: 'Success',
        description: 'Subscription reactivated successfully'
      })
      await fetchBillingData()
    } catch (error) {
      logger.error('Reactivation error', { userId: user?.id }, error as Error)
      toast({
        title: 'Error',
        description: 'Failed to reactivate subscription',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleManageBilling = async () => {
    try {
      setIsProcessing(true)
      
      const response = await fetch(API_ENDPOINTS.BILLING.PORTAL, {
        method: 'POST'
      })

      if (!response.ok) throw new Error('Failed to open billing portal')
      
      const { url } = await response.json()
      window.open(url, '_blank')
    } catch (error) {
      logger.error('Billing portal error', { userId: user?.id }, error as Error)
      toast({
        title: 'Error',
        description: 'Failed to open billing portal',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (!billingData) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load billing information. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  const { subscription, currentTier, usage, availableTiers, billingHistory } = billingData
  const usagePercentage = currentTier.limits.monthlyWords > 0 
    ? (usage.wordsUsed / currentTier.limits.monthlyWords) * 100 
    : 0

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-literary-display">Billing & Subscription</h1>
        <p className="text-muted-foreground mt-2">
          Manage your subscription, billing details, and usage
        </p>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="plans">Change Plan</TabsTrigger>
          <TabsTrigger value="history">Billing History</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Current Plan Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Current Plan
                  </CardTitle>
                  <CardDescription>
                    Your subscription details and status
                  </CardDescription>
                </div>
                {currentTier.id !== 'starter' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleManageBilling}
                    disabled={isProcessing}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Manage Billing
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-3">
                    <h3 className="text-2xl font-literary-display">
                      {currentTier.name}
                    </h3>
                    {currentTier.popular && (
                      <Badge variant="default" className="font-mono">
                        <Sparkles className="w-3 h-3 mr-1" />
                        POPULAR
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {currentTier.description}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">
                    {formatPrice(currentTier.price, currentTier.currency)}
                  </p>
                  <p className="text-sm text-muted-foreground font-mono">
                    per {currentTier.interval}
                  </p>
                </div>
              </div>

              {subscription && subscription.status === 'active' && (
                <>
                  <Separator />
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Billing Period</span>
                      </div>
                      <p className="text-sm">
                        {new Date(subscription.currentPeriodStart).toLocaleDateString()} - {' '}
                        {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-success" />
                        <span className="text-muted-foreground">Status</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={subscription.cancelAtPeriodEnd ? 'secondary' : 'default'}>
                          {subscription.cancelAtPeriodEnd ? 'Cancelling' : 'Active'}
                        </Badge>
                        {subscription.cancelAtPeriodEnd && (
                          <span className="text-xs text-muted-foreground">
                            Ends {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {subscription.cancelAtPeriodEnd && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Subscription Ending</AlertTitle>
                      <AlertDescription>
                        Your subscription will end on {new Date(subscription.currentPeriodEnd).toLocaleDateString()}.
                        You can reactivate anytime before then.
                      </AlertDescription>
                      <Button
                        size="sm"
                        className="mt-2"
                        onClick={handleReactivateSubscription}
                        disabled={isProcessing}
                      >
                        Reactivate Subscription
                      </Button>
                    </Alert>
                  )}
                </>
              )}
            </CardContent>
          </Card>

          {/* Usage Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Current Usage
              </CardTitle>
              <CardDescription>
                Your usage for the current billing period
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* AI Words Usage */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">AI Generated Words</span>
                  <span className="text-muted-foreground font-mono">
                    {usage.wordsUsed.toLocaleString()} / {' '}
                    {currentTier.limits.monthlyWords === -1 
                      ? 'Unlimited' 
                      : currentTier.limits.monthlyWords.toLocaleString()}
                  </span>
                </div>
                {currentTier.limits.monthlyWords > 0 && (
                  <Progress value={usagePercentage} className="h-2" />
                )}
              </div>

              {/* Projects Usage */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Active Projects</span>
                  <span className="text-muted-foreground font-mono">
                    {usage.projects} / {' '}
                    {currentTier.limits.projects === -1 
                      ? 'Unlimited' 
                      : currentTier.limits.projects}
                  </span>
                </div>
                {currentTier.limits.projects > 0 && (
                  <Progress 
                    value={(usage.projects / currentTier.limits.projects) * 100} 
                    className="h-2" 
                  />
                )}
              </div>

              {/* Available Features */}
              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-3">Available Features</h4>
                <div className="grid gap-2 sm:grid-cols-2">
                  {currentTier.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-muted-foreground">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          {currentTier.id !== 'starter' && subscription?.status === 'active' && !subscription.cancelAtPeriodEnd && (
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={handleCancelSubscription}
                disabled={isProcessing}
              >
                Cancel Subscription
              </Button>
            </div>
          )}
        </TabsContent>

        {/* Plans Tab */}
        <TabsContent value="plans" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-literary-display">Available Plans</h2>
              <PricingToggle isAnnual={isAnnual} onToggle={setIsAnnual} />
            </div>
            
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {availableTiers.map((tier) => (
                <PricingCard
                  key={tier.id}
                  tier={tier}
                  isAnnual={isAnnual}
                  onSelect={(selectedTier) => {
                    if (selectedTier.id === currentTier.id) {
                      toast({
                        title: 'Info',
                        description: 'You are already on this plan'
                      })
                    } else if (selectedTier.id === 'starter') {
                      toast({
                        title: 'Info',
                        description: 'Please cancel your current subscription to switch to the free plan'
                      })
                    } else {
                      handleSubscribe(selectedTier)
                    }
                  }}
                />
              ))}
            </div>
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                Billing History
              </CardTitle>
              <CardDescription>
                Your payment history and invoices
              </CardDescription>
            </CardHeader>
            <CardContent>
              {billingHistory.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-8">
                  No billing history available
                </p>
              ) : (
                <div className="space-y-4">
                  {billingHistory.map((payment) => (
                    <div
                      key={payment.id}
                      className="flex items-center justify-between p-4 rounded-lg border"
                    >
                      <div className="space-y-1">
                        <p className="text-sm font-medium">{payment.description}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(payment.date), { addSuffix: true })}
                        </p>
                      </div>
                      <div className="flex items-center gap-4">
                        <Badge
                          variant={
                            payment.status === 'paid' 
                              ? 'default' 
                              : payment.status === 'failed' 
                              ? 'destructive' 
                              : 'secondary'
                          }
                        >
                          {payment.status}
                        </Badge>
                        <span className="font-mono font-medium">
                          {formatPrice(payment.amount, payment.currency)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}