# API Validation Guide

## Overview
All API endpoints should validate incoming data using Zod schemas from `@/lib/validation/unified-schemas`.

## Example Implementation

```typescript
import { NextRequest } from 'next/server'
import { z } from 'zod'
import { schemas } from '@/lib/validation/unified-schemas'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

// Define request schema
const createProjectSchema = z.object({
  name: schemas.project.name,
  description: schemas.strings.description,
  genre: schemas.project.genre,
  settings: schemas.composite.metadata
})

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    // Parse and validate request body
    const body = await request.json()
    const validatedData = createProjectSchema.parse(body)
    
    // Use validated data
    const project = await createProject(validatedData)
    
    return UnifiedResponse.created(project)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return UnifiedResponse.validationError(error)
    }
    return UnifiedResponse.error({
      message: error.message,
      code: 'CREATE_PROJECT_ERROR'
    })
  }
})
```

## Common Patterns

### Query Parameters
```typescript
const querySchema = z.object({
  page: schemas.numbers.page,
  limit: schemas.numbers.limit,
  sortBy: z.string().optional(),
  sortOrder: schemas.enums.sortOrder
})

const { searchParams } = new URL(request.url)
const queryData = Object.fromEntries(searchParams.entries())
const validatedQuery = querySchema.parse(queryData)
```

### Optional Fields
```typescript
const updateSchema = z.object({
  name: schemas.strings.name.optional(),
  description: schemas.strings.description,
  settings: schemas.composite.metadata
}).partial() // Makes all fields optional
```

### File Uploads
```typescript
const uploadSchema = z.object({
  file: schemas.file.image,
  projectId: schemas.ids.project
})
```

## Best Practices
1. Always validate input data before processing
2. Use predefined schemas from unified-schemas.ts
3. Return proper validation errors using UnifiedResponse.validationError()
4. Type your validated data for better TypeScript support
5. Use .partial() for update endpoints where all fields are optional
