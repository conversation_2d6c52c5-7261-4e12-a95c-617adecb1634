/**
 * Application-wide constants
 * Centralized location for all hardcoded values and configuration
 */

// Re-export configurations from dedicated files
export * from '@/lib/config/api-endpoints'
export * from '@/lib/config/network-config'
export * from '@/lib/config/environment-config'

// Legacy API Endpoints (kept for backward compatibility)
export const API_ENDPOINTS = {
  ERRORS: {
    REPORT: '/api/errors/report',
    BATCH_REPORT: '/api/errors/batch-report',
  },
  ANALYTICS: {
    AI_USAGE: '/api/analytics/ai-usage',
    BEHAVIORAL: '/api/analytics/behavioral',
  },
  AI: {
    TYPED_STREAM: '/api/ai/typed-stream',
  },
  SERIES: {
    ANALYTICS: (id: string) => `/api/series/${id}/analytics`,
    CONTINUITY_ISSUES: (id: string) => `/api/series/${id}/continuity-issues`,
    UNIVERSE_RULES: (id: string) => `/api/series/${id}/universe-rules`,
    CHARACTER_ARCS: (id: string) => `/api/series/${id}/character-arcs`,
  },
  WEBHOOKS: {
    STRIPE: '/api/webhooks/stripe',
  },
} as const;

import { EMAIL_CONFIG } from '@/lib/config/environment-config'

// Email Constants
export const EMAIL_DEFAULTS = {
  FROM_ADDRESS: EMAIL_CONFIG.ADDRESSES.FROM,
  FROM_NAME: 'BookScribe AI',
  SUPPORT_EMAIL: EMAIL_CONFIG.ADDRESSES.SUPPORT,
  TEST_EMAIL: EMAIL_CONFIG.ADDRESSES.TEST,
  DEMO_EMAIL: '<EMAIL>', // Use this <NAME_EMAIL>
} as const;

import { API_URLS } from '@/lib/config/environment-config'

// URL Constants
export const APP_URLS = {
  BASE_URL: API_URLS.INTERNAL.APP,
  MARKETING_URL: 'https://bookscribe.ai',
  APP_URL: 'https://app.bookscribe.ai',
  API_URL: API_URLS.INTERNAL.API,
  WEBSOCKET_URL: API_URLS.INTERNAL.WEBSOCKET,
} as const;

// Demo Mode Constants
export const DEMO_CONSTANTS = {
  IS_DEMO_MODE: process.env.NEXT_PUBLIC_DEMO_MODE === 'true',
  DEMO_USER_ID: 'demo-user-001',
  DEMO_PROJECT_ID: 'demo-project-1',
  DEMO_CHAPTER_ID: 'demo-chapter-1',
} as const;

// Limits and Thresholds
export const LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_CHAPTER_LENGTH: 50000, // characters
  MAX_PROJECT_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MIN_PASSWORD_LENGTH: 8,
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000,
} as const;

// Time Constants
export const TIME_CONSTANTS = {
  SESSION_TIMEOUT_MS: 30 * 60 * 1000, // 30 minutes
  IDLE_TIMEOUT_MS: 60 * 1000, // 1 minute
  PRESENCE_UPDATE_INTERVAL_MS: 5000, // 5 seconds
  AUTOSAVE_INTERVAL_MS: 30 * 1000, // 30 seconds
  TOAST_DURATION_MS: 5000, // 5 seconds
} as const;

import { FEATURE_FLAGS as ENV_FEATURE_FLAGS } from '@/lib/config/environment-config'

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_COLLABORATION: ENV_FEATURE_FLAGS.REALTIME_COLLABORATION,
  ENABLE_AI_AGENTS: ENV_FEATURE_FLAGS.AI_AGENTS,
  ENABLE_EXPORT: ENV_FEATURE_FLAGS.EXPORT_FORMATS,
  ENABLE_VOICE_ANALYSIS: true,
  ENABLE_SERIES_MANAGEMENT: true,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  GENERIC: 'An unexpected error occurred. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  RATE_LIMIT: 'Too many requests. Please try again later.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVED: 'Changes saved successfully.',
  CREATED: 'Created successfully.',
  UPDATED: 'Updated successfully.',
  DELETED: 'Deleted successfully.',
  EXPORTED: 'Export completed successfully.',
  IMPORTED: 'Import completed successfully.',
} as const;

// Regex Patterns
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  ISBN: /^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/,
} as const;

// Export Format Constants
export const EXPORT_FORMATS = {
  PDF: 'pdf',
  EPUB: 'epub',
  DOCX: 'docx',
  TXT: 'txt',
  MARKDOWN: 'markdown',
} as const;

// Theme Constants
export const THEME_NAMES = {
  WRITERS_SANCTUARY: 'writers-sanctuary',
  FOREST_MANUSCRIPT: 'forest-manuscript',
  EVENING_STUDY: 'evening-study',
  MIDNIGHT_INK: 'midnight-ink',
} as const;