# Script Utilities

## `standardize-zustand-stores.js`

This helper script generates a fully typed Zustand store scaffold with the
recommended middleware stack (`devtools` → `persist` → `immer`). It replaces the
previous TODO placeholders with concrete state, action, and async examples.

### Usage

```bash
node scripts/standardize-zustand-stores.js \
  --name todo \
  --state "items:string[]" \
  --state "loading:boolean" \
  --action addItem \
  --action removeItem \
  --async fetchItems \
  --out src/stores/todo-store.ts
```

**Options**

- `--name, -n` – base name for the store (e.g. `todo`)
- `--state, -s` – state definition in `name:type` format. Repeat for multiple
  state values.
- `--action, -a` – additional action names to scaffold. Setters for each state
  are generated automatically.
- `--async, -A` – optional async action name. Adds loading guards and error
  logging.
- `--out, -o` – path for the generated file. Omit to run the analysis mode
  which regenerates the improvement guide.

The generated file includes example selectors and is ready to be adapted for
your specific store logic.

