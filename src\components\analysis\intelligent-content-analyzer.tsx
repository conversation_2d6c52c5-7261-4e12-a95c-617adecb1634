'use client';

import { useState, useEffect, useCallback } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain, 
  TrendingUp, 
  Users, 
  BookOpen, 
  Heart, 
  AlertTriangle,
  Zap
} from 'lucide-react';
import { PlotHoleDetector } from './plot-hole-detector';
import { PacingAnalyzer } from './pacing-analyzer';
import { CharacterArcVisualizer } from './character-arc-visualizer';
import { ReadabilityScorer } from './readability-scorer';
import { EmotionalJourneyMapper } from './emotional-journey-mapper';
import type { AnalysisData } from '@/lib/types/analysis';

interface IntelligentContentAnalyzerProps {
  projectId: string;
  analysisType?: 'full' | 'chapter' | 'realtime';
  chapterNumber?: number;
  content?: string;
}

export function IntelligentContentAnalyzer({ 
  projectId, 
  analysisType = 'full',
  chapterNumber,
  content 
}: IntelligentContentAnalyzerProps) {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const runFullAnalysis = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/intelligent-analysis/${projectId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'full' }),
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysisData(data);
      }
    } catch (error) {
      logger.error('Error running analysis:', error);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  const runChapterAnalysis = useCallback(async (chapter: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/intelligent-analysis/${projectId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'chapter', chapterNumber: chapter, content }),
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysisData(data);
      }
    } catch (error) {
      logger.error('Error running chapter analysis:', error);
    } finally {
      setLoading(false);
    }
  }, [projectId, content]);

  useEffect(() => {
    if (analysisType === 'full') {
      runFullAnalysis();
    } else if (analysisType === 'chapter' && chapterNumber) {
      runChapterAnalysis(chapterNumber);
    }
  }, [projectId, analysisType, chapterNumber, runFullAnalysis, runChapterAnalysis]);

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-2 border-info border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-sm text-slate-600">Running intelligent analysis...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analysisData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="w-5 h-5 mr-2" />
            Intelligent Content Analysis
          </CardTitle>
          <CardDescription>
            AI-powered analysis of your story&apos;s structure, pacing, characters, and more
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={runFullAnalysis} className="w-full">
            <Zap className="w-4 h-4 mr-2" />
            Run Full Analysis
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Brain className="w-5 h-5 mr-2" />
              Story Health Dashboard
            </span>
            <div className="flex items-center space-x-2">
              <Badge variant={analysisData.overallHealth >= 80 ? 'default' : 
                            analysisData.overallHealth >= 60 ? 'secondary' : 'destructive'}>
                {analysisData.overallHealth}% Health
              </Badge>
              <Button size="sm" variant="outline" onClick={runFullAnalysis}>
                <Zap className="w-3 h-3 mr-1" />
                Refresh
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 sm:gap-5 lg:gap-6 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-info">{analysisData.plotHoles.length}</div>
              <div className="text-sm text-slate-600">Plot Issues</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">{analysisData.pacingData.overallScore}%</div>
              <div className="text-sm text-slate-600">Pacing Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{analysisData.characterArcs.length}</div>
              <div className="text-sm text-slate-600">Character Arcs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-warning">{analysisData.readabilityScore.score}%</div>
              <div className="text-sm text-slate-600">Readability</div>
            </div>
          </div>

          {/* Critical Issues Alert */}
          {analysisData.plotHoles.some(hole => hole.severity === 'critical') && (
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Critical plot issues detected!</strong> Review the Plot Analysis tab for immediate attention.
              </AlertDescription>
            </Alert>
          )}

          {/* Quick Recommendations */}
          <div className="space-y-2">
            <h4 className="font-medium">Top Recommendations</h4>
            {analysisData.recommendations.slice(0, 3).map((rec) => (
              <div key={rec.id} className="flex items-start p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                <Badge 
                  variant={rec.priority === 'high' ? 'destructive' : 
                          rec.priority === 'medium' ? 'secondary' : 'outline'}
                  className="mr-3 mt-0.5"
                >
                  {rec.priority}
                </Badge>
                <div className="flex-1">
                  <div className="font-medium text-sm">{rec.title}</div>
                  <div className="text-xs text-slate-600 dark:text-slate-400">{rec.description}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Analysis Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="plot" className="flex items-center">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Plot
          </TabsTrigger>
          <TabsTrigger value="pacing" className="flex items-center">
            <TrendingUp className="w-3 h-3 mr-1" />
            Pacing
          </TabsTrigger>
          <TabsTrigger value="characters" className="flex items-center">
            <Users className="w-3 h-3 mr-1" />
            Characters
          </TabsTrigger>
          <TabsTrigger value="readability" className="flex items-center">
            <BookOpen className="w-3 h-3 mr-1" />
            Readability
          </TabsTrigger>
          <TabsTrigger value="emotion" className="flex items-center">
            <Heart className="w-3 h-3 mr-1" />
            Emotion
          </TabsTrigger>
        </TabsList>

        <TabsContent value="plot" className="space-y-4">
          <PlotHoleDetector plotHoles={analysisData.plotHoles} projectId={projectId} />
        </TabsContent>

        <TabsContent value="pacing" className="space-y-4">
          <PacingAnalyzer pacingData={analysisData.pacingData} projectId={projectId} />
        </TabsContent>

        <TabsContent value="characters" className="space-y-4">
          <CharacterArcVisualizer characterArcs={analysisData.characterArcs} projectId={projectId} />
        </TabsContent>

        <TabsContent value="readability" className="space-y-4">
          <ReadabilityScorer readabilityData={analysisData.readabilityScore} projectId={projectId} />
        </TabsContent>

        <TabsContent value="emotion" className="space-y-4">
          <EmotionalJourneyMapper emotionalData={analysisData.emotionalJourney} projectId={projectId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}