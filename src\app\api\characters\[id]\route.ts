import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { updateCharacterSchema } from '@/lib/validation/schemas'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { baseSchemas } from '@/lib/validation/common-schemas'

// Parameter validation schema
const characterIdSchema = z.object({
  id: baseSchemas.uuid
});

export const GET = UnifiedAuthService.withAuth(async (
  request: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params;
  
  // Validate character ID
  const paramValidation = characterIdSchema.safeParse({ id });
  if (!paramValidation.success) {
    return UnifiedResponse.error('Invalid character ID', 400, paramValidation.error.errors);
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'character-read',
    rateLimitCost: 1,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const supabase = await createTypedServerClient();

    // Get character with project ownership check
    const { data: character, error } = await supabase
      .from('characters')
      .select(`
        *,
        projects!inner (
          id,
          title,
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        logger.info('Character not found', {
          characterId: id,
          userId: user.id,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error('Character not found', 404);
      }
      logger.error('Error fetching character:', error, {
        characterId: id,
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to fetch character');
    }

    // Check if user has access (owner or collaborator)
    const isOwner = character.projects.user_id === user.id;
    if (!isOwner) {
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('role')
        .eq('project_id', character.project_id)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();
      
      if (!collaborator) {
        logger.warn('Unauthorized character access attempt', {
          characterId: id,
          userId: user.id,
          projectOwnerId: character.projects.user_id,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error('Access denied to this character', 403);
      }
    }

    // Clean up the response to remove nested project data
    const { projects: _, ...cleanCharacter } = character;
    void _; // Suppress unused variable warning

    logger.info('Character fetched successfully', {
      characterId: id,
      userId: user.id,
      projectId: character.project_id,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ character: cleanCharacter });

  } catch (error) {
    logger.error('Error fetching character:', error, {
      characterId: id,
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to fetch character');
  }
});

export const PUT = UnifiedAuthService.withAuth(async (
  request: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params;
  
  // Validate character ID
  const paramValidation = characterIdSchema.safeParse({ id });
  if (!paramValidation.success) {
    return UnifiedResponse.error('Invalid character ID', 400, paramValidation.error.errors);
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: updateCharacterSchema,
    rateLimitKey: 'character-update',
    rateLimitCost: 3,
    maxBodySize: 50 * 1024, // 50KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      const body = await req.json();
      const { name, description } = body;
      
      // Check for malicious content in character data
      const textToCheck = `${name || ''} ${description || ''}`;
      if (textToCheck.match(/<script|javascript:|onerror|onclick|<iframe|<object|<embed/i)) {
        return { valid: false, error: 'Character data contains potentially malicious content' };
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const validatedData = context.body;

  try {
    const supabase = await createTypedServerClient();

    // First verify ownership through project
    const { data: ownershipCheck } = await supabase
      .from('characters')
      .select(`
        project_id,
        character_id,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (!ownershipCheck) {
      logger.info('Character not found for update', {
        characterId: id,
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Character not found', 404);
    }

    // Check if user has edit permissions
    const isOwner = ownershipCheck.projects.user_id === user.id;
    if (!isOwner) {
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('role')
        .eq('project_id', ownershipCheck.project_id)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();
      
      if (!collaborator || collaborator.role === 'viewer') {
        logger.warn('Unauthorized character update attempt', {
          characterId: id,
          userId: user.id,
          projectOwnerId: ownershipCheck.projects.user_id,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error('Insufficient permissions to update character', 403);
      }
    }

    // If character_id is being updated, check for conflicts
    if (validatedData.character_id && validatedData.character_id !== ownershipCheck.character_id) {
      const { data: existingCharacter } = await supabase
        .from('characters')
        .select('id')
        .eq('project_id', ownershipCheck.project_id)
        .eq('character_id', validatedData.character_id)
        .neq('id', id)
        .single()

      if (existingCharacter) {
        logger.warn('Duplicate character ID on update attempt', {
          characterId: id,
          newCharacterId: validatedData.character_id,
          projectId: ownershipCheck.project_id,
          userId: user.id,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error(
          'Character with this ID already exists in the project',
          409
        );
      }
    }

    // Update character
    const updateFields = {
      ...validatedData,
      updated_at: new Date().toISOString()
    }

    const { data: character, error } = await supabase
      .from('characters')
      .update(updateFields)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      logger.error('Error updating character:', error, {
        characterId: id,
        userId: user.id,
        clientIP: context.clientIP
      });
      
      if (error.code === '23505') { // Unique constraint violation
        return UnifiedResponse.error(
          'A character with this name already exists',
          409
        );
      }
      
      return UnifiedResponse.error('Failed to update character');
    }

    logger.info('Character updated successfully', {
      characterId: id,
      userId: user.id,
      projectId: ownershipCheck.project_id,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ character });

  } catch (error) {
    logger.error('Error updating character:', error, {
      characterId: id,
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to update character');
  }
});

export const DELETE = UnifiedAuthService.withAuth(async (
  request: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params;
  
  // Validate character ID
  const paramValidation = characterIdSchema.safeParse({ id });
  if (!paramValidation.success) {
    return UnifiedResponse.error('Invalid character ID', 400, paramValidation.error.errors);
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'character-delete',
    rateLimitCost: 5,
    maxRequestSize: 1024,
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const supabase = await createTypedServerClient();

    // First verify ownership through project
    const { data: character } = await supabase
      .from('characters')
      .select(`
        id,
        name,
        project_id,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (!character) {
      logger.info('Character not found for deletion', {
        characterId: id,
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Character not found', 404);
    }

    // Check if user has delete permissions (owner or editor)
    const isOwner = character.projects.user_id === user.id;
    if (!isOwner) {
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('role')
        .eq('project_id', character.project_id)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();
      
      if (!collaborator || collaborator.role === 'viewer') {
        logger.warn('Unauthorized character deletion attempt', {
          characterId: id,
          userId: user.id,
          projectOwnerId: character.projects.user_id,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error('Insufficient permissions to delete character', 403);
      }
    }

    // Delete character
    const { error } = await supabase
      .from('characters')
      .delete()
      .eq('id', id)

    if (error) {
      logger.error('Error deleting character:', error, {
        characterId: id,
        userId: user.id,
        clientIP: context.clientIP
      });
      
      if (error.code === '23503') { // Foreign key constraint violation
        return UnifiedResponse.error(
          'Cannot delete character: it is referenced by other data',
          409
        );
      }
      
      return UnifiedResponse.error('Failed to delete character');
    }

    logger.info('Character deleted successfully', {
      characterId: id,
      characterName: character.name,
      userId: user.id,
      projectId: character.project_id,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      message: `Character "${character.name}" deleted successfully`
    });

  } catch (error) {
    logger.error('Error deleting character:', error, {
      characterId: id,
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to delete character');
  }
});