import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { StepComponentProps } from './wizard-types'
import { SPACING } from '@/lib/config/ui-config'

export function StepCharactersWorld({ formData, updateFormData, mode }: StepComponentProps) {
  return (
    <div className={SPACING.SPACE_Y.MD}>
      <div>
        <Label htmlFor="protagonist" className="text-base font-semibold">
          Protagonist Description
        </Label>
        <Textarea
          id="protagonist"
          value={formData.protagonist}
          onChange={(e) => updateFormData('protagonist', e.target.value)}
          placeholder="Describe your main character..."
          className="mt-2 min-h-[100px]"
          disabled={mode === 'demo'}
        />
      </div>
      
      <div>
        <Label htmlFor="antagonist" className="text-base font-semibold">
          Antagonist Description
        </Label>
        <Textarea
          id="antagonist"
          value={formData.antagonist}
          onChange={(e) => updateFormData('antagonist', e.target.value)}
          placeholder="Describe the main opposing force..."
          className="mt-2 min-h-[100px]"
          disabled={mode === 'demo'}
        />
      </div>
      
      <div>
        <Label htmlFor="setting" className="text-base font-semibold">
          Setting Description
        </Label>
        <Textarea
          id="setting"
          value={formData.setting}
          onChange={(e) => updateFormData('setting', e.target.value)}
          placeholder="Describe where your story takes place..."
          className="mt-2 min-h-[100px]"
          disabled={mode === 'demo'}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="worldBuildingDepth" className="text-base font-semibold">
            World-Building Depth
          </Label>
          <Select
            value={formData.worldBuildingDepth}
            onValueChange={(value) => updateFormData('worldBuildingDepth', value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger id="worldBuildingDepth" className="mt-2">
              <SelectValue placeholder="Select depth" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="minimal">Minimal - Focus on story</SelectItem>
              <SelectItem value="moderate">Moderate - Balanced approach</SelectItem>
              <SelectItem value="extensive">Extensive - Rich detail</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="timePeriod" className="text-base font-semibold">
            Time Period
          </Label>
          <Select
            value={formData.timePeriod}
            onValueChange={(value) => updateFormData('timePeriod', value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger id="timePeriod" className="mt-2">
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="contemporary">Contemporary</SelectItem>
              <SelectItem value="historical">Historical</SelectItem>
              <SelectItem value="near-future">Near Future</SelectItem>
              <SelectItem value="far-future">Far Future</SelectItem>
              <SelectItem value="medieval-fantasy">Medieval Fantasy</SelectItem>
              <SelectItem value="alternate-history">Alternate History</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div>
        <Label htmlFor="protagonistName" className="text-base font-semibold">
          Protagonist Name
        </Label>
        <Input
          id="protagonistName"
          value={formData.protagonistName}
          onChange={(e) => updateFormData('protagonistName', e.target.value)}
          placeholder="What's your main character's name?"
          className="mt-2"
          disabled={mode === 'demo'}
        />
      </div>
    </div>
  )
}