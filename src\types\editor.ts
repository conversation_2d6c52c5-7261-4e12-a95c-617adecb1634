/**
 * Type definitions for editor and chat functionality
 */

// Chat message types
export type ChatMessageAction = 'edit' | 'expand' | 'rewrite' | 'suggest' | 'analyze' | 'generate'

export interface ChatMessageContext {
  selectedText?: string
  action?: ChatMessageAction
  chapterId?: string
  characterIds?: string[]
  wordCount?: number
  sceneId?: string
  targetWordCount?: number
  style?: string
  tone?: string
}

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  context?: ChatMessageContext
  timestamp: Date
  status?: 'sending' | 'sent' | 'error'
  error?: string
}

// Editor state types
export interface EditorSelection {
  start: number
  end: number
  text: string
}

export interface EditorCursor {
  line: number
  column: number
  offset: number
}

export interface EditorState {
  content: string
  selection?: EditorSelection
  cursor: EditorCursor
  isDirty: boolean
  wordCount: number
  characterCount: number
  lastSaved?: Date
}

// Story bible entry types
export type StoryBibleEntryType = 
  | 'character'
  | 'location'
  | 'world-building'
  | 'timeline'
  | 'plot-device'
  | 'theme'
  | 'conflict'
  | 'setting'
  | 'research'
  | 'note'

export interface BaseStoryBibleEntry {
  id: string
  projectId: string
  type: StoryBibleEntryType
  createdAt: Date
  updatedAt: Date
  tags: string[]
  importance?: 'low' | 'medium' | 'high'
  connections?: string[]
  chapter_introduced?: number
}

export interface CharacterEntry extends BaseStoryBibleEntry {
  type: 'character'
  data: {
    name: string
    role: string
    traits: string[]
    backstory?: string
    description: string
    relationships?: Array<{
      characterId: string
      relationship: string
      description: string
    }>
    arc?: {
      starting: string
      transformation: string
      ending: string
    }
  }
}

export interface LocationEntry extends BaseStoryBibleEntry {
  type: 'location'
  data: {
    name: string
    description: string
    significance?: string
    geography?: string
    culture?: string
    inhabitants?: string[]
  }
}

export interface WorldBuildingEntry extends BaseStoryBibleEntry {
  type: 'world-building'
  data: {
    category: string
    rule: string
    explanation: string
    implications?: string[]
    exceptions?: string[]
  }
}

export interface TimelineEntry extends BaseStoryBibleEntry {
  type: 'timeline'
  data: {
    event: string
    date: string
    description?: string
    participants?: string[]
    consequences?: string[]
  }
}

export interface PlotDeviceEntry extends BaseStoryBibleEntry {
  type: 'plot-device'
  data: {
    name: string
    purpose: string
    usage: string[]
    firstAppearance?: number
    resolution?: string
  }
}

export type StoryBibleEntry = 
  | CharacterEntry
  | LocationEntry
  | WorldBuildingEntry
  | TimelineEntry
  | PlotDeviceEntry
  | (BaseStoryBibleEntry & { data: Record<string, unknown> })

// Editor configuration types
export interface EditorConfig {
  theme: 'light' | 'dark' | 'writer-sanctuary-light' | 'writer-sanctuary-dark'
  fontSize: number
  fontFamily: string
  lineHeight: number
  wordWrap: boolean
  showLineNumbers: boolean
  showMinimap: boolean
  autoSave: boolean
  autoSaveInterval: number
  spellCheck: boolean
  grammarCheck: boolean
  aiAssistance: boolean
  collaborationEnabled: boolean
}