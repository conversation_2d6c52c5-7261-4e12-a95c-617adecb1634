import { SettingsPage } from '@/components/settings/settings-page';
import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';

export default async function Settings() {
  const supabase = createClient();
  
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    redirect('/login');
  }
  
  return <SettingsPage user={user} />;
}