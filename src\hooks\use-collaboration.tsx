'use client'

import { useState, useEffect, useCallback } from 'react'
import { collaborationService } from '@/lib/services/unified-collaboration-service'
import type { CollaborationUser, CollaborationChange } from '@/types/collaboration'
import { useUser } from '@/hooks/use-user'
import { useSubscription } from '@/hooks/use-subscription'
import { toast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { getBrowserClient } from '@/lib/supabase'

interface SelectionRange {
  userId: string
  startLine: number
  startColumn: number
  endLine: number
  endColumn: number
  timestamp: number
}

interface UseCollaborationProps {
  projectId: string
  documentId: string
  onContentChange?: (change: CollaborationChange) => void
  onUserJoined?: (user: CollaborationUser) => void
  onUserLeft?: (userId: string) => void
}

export function useCollaboration({
  projectId,
  documentId,
  onContentChange,
  onUserJoined,
  onUserLeft
}: UseCollaborationProps) {
  const { user } = useUser()
  const { subscription, canUseFeature } = useSubscription()
  const [isConnected, setIsConnected] = useState(false)
  const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>([])
  const [error, setError] = useState<string | null>(null)
  const [sessionId, setSessionId] = useState<string | null>(null)

  // Check if collaboration is available for the user's subscription
  const isCollaborationAvailable = canUseFeature('collaboration') || canUseFeature('real_time_collaboration')

  // Connect to collaboration session
  useEffect(() => {
    if (!user || !isCollaborationAvailable) {
      if (!isCollaborationAvailable && user) {
        setError('Collaboration is not available in your current subscription plan')
      }
      return
    }

    const connectToSession = async () => {
      try {
        // First, try to join an existing session or create a new one
        const result = await collaborationService.joinSession(
          sessionId || `${projectId}-${documentId}`,
          user.id,
          { name: user.user_metadata?.name || user.email || 'Anonymous', email: user.email || '' }
        )

        if (result.success && result.data) {
          setSessionId(result.data.id)
          setIsConnected(true)
          setActiveUsers(result.data.participants)
          setError(null)
        } else {
          throw new Error(result.error || 'Failed to join collaboration session')
        }
      } catch (err) {
        logger.error('Failed to connect to collaboration session', err)
        setError(err instanceof Error ? err.message : 'Failed to connect')
        setIsConnected(false)
      }
    }

    connectToSession()

    return () => {
      // Clean up: leave the session when component unmounts
      if (sessionId && user) {
        // Note: There's no explicit disconnect method, but leaving the session should handle cleanup
        setIsConnected(false)
        setSessionId(null)
      }
    }
  }, [projectId, documentId, user, isCollaborationAvailable])

  // Subscribe to collaboration events via Supabase realtime
  useEffect(() => {
    if (!isConnected || !sessionId) return

    const supabase = getBrowserClient()
    const channel = supabase
      .channel(`collaboration:${sessionId}`)
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState()
        const users: CollaborationUser[] = []
        
        Object.values(state).forEach((presences) => {
          presences.forEach((presence: any) => {
            if (presence.user) {
              users.push(presence.user)
            }
          })
        })
        
        setActiveUsers(users)
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        newPresences.forEach((presence: any) => {
          if (presence.user && onUserJoined) {
            onUserJoined(presence.user)
            if (presence.user.id !== user?.id) {
              toast({
                title: "User Joined",
                description: `${presence.user.name} joined the editing session`
              })
            }
          }
        })
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        leftPresences.forEach((presence: any) => {
          if (presence.user) {
            const leftUserId = presence.user.id
            setActiveUsers(prev => prev.filter(u => u.id !== leftUserId))
            if (onUserLeft) {
              onUserLeft(leftUserId)
            }
            toast({
              title: "User Left",
              description: `${presence.user.name} left the editing session`
            })
          }
        })
      })
      .on('broadcast', { event: 'collaboration' }, ({ payload }) => {
        if (payload.type === 'content.changed' && onContentChange) {
          onContentChange(payload.data as CollaborationChange)
        }
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [isConnected, sessionId, user, onUserJoined, onUserLeft, onContentChange])

  // Send content change
  const sendChange = useCallback(async (change: Omit<CollaborationChange, 'id' | 'sessionId' | 'userId' | 'timestamp' | 'version'>) => {
    if (!isConnected || !sessionId || !user) return
    
    try {
      const result = await collaborationService.submitChange(
        sessionId,
        user.id,
        change
      )
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to submit change')
      }
    } catch (err) {
      logger.error('Failed to send content change', err)
      toast({
        title: "Sync Error",
        description: "Failed to sync your changes. They will be retried.",
        variant: "destructive"
      })
    }
  }, [isConnected, sessionId, user])

  // Send cursor position
  const sendCursorPosition = useCallback(async (position: { line: number; column: number }) => {
    if (!isConnected || !user || !sessionId) return
    
    try {
      await collaborationService.updateCursor(sessionId, user.id, position)
    } catch (err) {
      logger.error('Failed to update cursor position', err)
    }
  }, [isConnected, sessionId, user])

  // Send selection range
  const sendSelection = useCallback(async (selection: Omit<SelectionRange, 'userId' | 'timestamp'>) => {
    if (!isConnected || !user || !sessionId) return
    
    try {
      await collaborationService.updateSelection(sessionId, user.id, {
        startLine: selection.startLine,
        startColumn: selection.startColumn,
        endLine: selection.endLine,
        endColumn: selection.endColumn
      })
    } catch (err) {
      logger.error('Failed to update selection', err)
    }
  }, [isConnected, sessionId, user])

  return {
    isConnected,
    activeUsers,
    error,
    sendChange,
    sendCursorPosition,
    sendSelection
  }
}