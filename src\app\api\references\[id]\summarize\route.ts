import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server';
import { authenticateUser } from '@/lib/auth';
import { vercelAIClient } from '@/lib/ai/vercel-ai-client';
import { AI_MODELS } from '@/lib/config/ai-settings';
import { logger } from '@/lib/services/logger'
import { AI_MODEL_PARAMS } from '@/lib/constants'

export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }
    const { user, supabase } = authResult;
    
    if (!user || !supabase) {
      return handleAPIError(new AuthenticationError())
    }

    const { id: materialId } = await params;

    // Get the material
    const { data: material, error: fetchError } = await supabase
      .from('reference_materials')
      .select('*')
      .eq('id', materialId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'));
      }
      throw fetchError;
    }

    // Check if already has AI summary (stored in description for now)
    if (material.description && material.description.startsWith('[AI Summary]')) {
      return NextResponse.json({ 
        summary: material.description.replace('[AI Summary] ', ''),
        message: 'Summary already exists' 
      });
    }

    let contentToSummarize = '';

    // Determine content source based on material type
    if (material.file_type === 'url' && material.file_url) {
      // For URLs, we might need to fetch content (basic implementation)
      try {
        const response = await fetch(material.file_url);
        const html = await response.text();
        // Extract text content (simplified - in production, use proper HTML parsing)
        contentToSummarize = html.replace(/<[^>]*>/g, ' ').substring(0, 10000);
      } catch {
        contentToSummarize = `URL: ${material.file_url}\nTitle: ${material.name}\nDescription: ${material.description || 'No description available'}`;
      }
    } else if (material.file_type === 'document' && material.file_url) {
      // For documents, we'd need to extract text (placeholder)
      contentToSummarize = `Document: ${material.name}\nDescription: ${material.description || 'No description available'}\nFile Type: ${material.mime_type || 'Unknown'}`;
    } else {
      contentToSummarize = `${material.name}\n${material.description || 'No additional content available'}`;
    }

    if (!contentToSummarize.trim()) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Generate AI summary
    const systemPrompt = `You are an AI assistant that creates concise, helpful summaries of reference materials for fiction writers. 

Your task is to analyze the provided content and create a summary that will be useful for writers referencing this material in their creative work.

Focus on:
- Key themes, concepts, or information
- How this material might be useful for story development
- Important details that a writer should remember
- Any inspiration or ideas this material might spark

Keep the summary concise (2-4 sentences) but informative. Write in a way that helps writers quickly understand the value and content of this reference material.`;
    
    const userPrompt = `Please summarize this reference material:\n\nTitle: ${material.name}\nType: ${material.file_type}\nDescription: ${material.description || 'None provided'}\n\nContent:\n${contentToSummarize.substring(0, SIZE_LIMITS.EMBEDDING_TEXT_LIMIT)}`;
    
    const summary = await vercelAIClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.FAST,
        temperature: AI_MODEL_PARAMS.DEFAULT_TEMPERATURE,
        maxTokens: 300,
        systemPrompt
      }
    );

    if (!summary) {
      return NextResponse.json({ error: 'Failed to generate summary' }, { status: 500 });
    }

    // Update the material with the AI summary (store in description with prefix)
    const updatedDescription = `[AI Summary] ${summary}`;
    const { data: updatedMaterial, error: updateError } = await supabase
      .from('reference_materials')
      .update({ 
        description: updatedDescription,
        updated_at: new Date().toISOString()
      })
      .eq('id', materialId)
      .select()
      .single();

    if (updateError) throw updateError;

    return NextResponse.json({ 
      summary,
      material: {
        id: updatedMaterial.id,
        projectId: updatedMaterial.project_id,
        name: updatedMaterial.name,
        description: updatedMaterial.description,
        fileType: updatedMaterial.file_type,
        fileUrl: updatedMaterial.file_url,
        fileSize: updatedMaterial.file_size,
        mimeType: updatedMaterial.mime_type,
        tags: updatedMaterial.tags || [],
        createdAt: new Date(updatedMaterial.created_at),
        updatedAt: new Date(updatedMaterial.updated_at),
      }
    });
  } catch (error) {
    logger.error('Summarization API error:', error);
    
    // Handle OpenAI API errors specifically
    if (error instanceof Error && error.message.includes('API key')) {
      return NextResponse.json({ error: 'AI service configuration error' }, { status: 503 });
    }
    
    return NextResponse.json({ error: 'Failed to generate summary' }, { status: 500 });
  }
}