import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { openai } from '@/lib/ai/openai-client'

export const POST = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: materialId } = await params
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Get the material and verify ownership
    const { data: material, error: fetchError } = await supabase
      .from('reference_materials')
      .select('*, projects!inner(user_id)')
      .eq('id', materialId)
      .eq('projects.user_id', user.id)
      .single()

    if (fetchError || !material) {
      return UnifiedResponse.error('Reference material not found', 404)
    }

    // Check if already has AI summary
    if (material.ai_summary) {
      return UnifiedResponse.success({ 
        summary: material.ai_summary,
        message: 'Summary already exists' 
      })
    }

    let contentToSummarize = ''

    // Determine content source based on material type
    if (material.type === 'url' && material.content) {
      // For URLs, use the stored content
      contentToSummarize = material.content
    } else if (material.type === 'note' && material.content) {
      // For notes, use the content directly
      contentToSummarize = material.content
    } else if (material.type === 'document' && material.file_url) {
      // For documents, we'd need to extract text (placeholder for now)
      contentToSummarize = `Document: ${material.title}\nDescription: ${material.description || 'No description available'}\nFile Type: ${material.mime_type || 'Unknown'}`
    } else {
      contentToSummarize = `${material.title}\n${material.description || 'No additional content available'}`
    }

    if (!contentToSummarize.trim()) {
      return UnifiedResponse.error('No content available to summarize', 400)
    }

    // Generate AI summary
    const systemPrompt = `You are an AI assistant that creates concise, helpful summaries of reference materials for fiction writers. 

Your task is to analyze the provided content and create a summary that will be useful for writers referencing this material in their creative work.

Focus on:
- Key themes, concepts, or information
- How this material might be useful for story development
- Important details that a writer should remember
- Any inspiration or ideas this material might spark

Keep the summary concise (2-4 sentences) but informative. Write in a way that helps writers quickly understand the value and content of this reference material.`
    
    const userPrompt = `Please summarize this reference material:\n\nTitle: ${material.title}\nType: ${material.type}\nDescription: ${material.description || 'None provided'}\n\nContent:\n${contentToSummarize.substring(0, 3000)}`
    
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 300
    })

    const summary = response.choices[0].message.content?.trim()

    if (!summary) {
      return UnifiedResponse.error('Failed to generate summary')
    }

    // Update the material with the AI summary
    const { data: updatedMaterial, error: updateError } = await supabase
      .from('reference_materials')
      .update({ 
        ai_summary: summary,
        updated_at: new Date().toISOString()
      })
      .eq('id', materialId)
      .select()
      .single()

    if (updateError) {
      logger.error('Failed to update material with summary:', updateError)
      return UnifiedResponse.error('Failed to save summary')
    }

    // Return formatted response
    const formattedMaterial = {
      id: updatedMaterial.id,
      projectId: updatedMaterial.project_id,
      type: updatedMaterial.type,
      title: updatedMaterial.title,
      description: updatedMaterial.description,
      fileUrl: updatedMaterial.file_url,
      fileSize: updatedMaterial.file_size,
      mimeType: updatedMaterial.mime_type,
      content: updatedMaterial.content,
      tags: updatedMaterial.tags || [],
      aiSummary: updatedMaterial.ai_summary,
      createdAt: new Date(updatedMaterial.created_at),
      updatedAt: new Date(updatedMaterial.updated_at),
    }

    return UnifiedResponse.success({ 
      summary,
      material: formattedMaterial
    })
  } catch (error) {
    logger.error('Summarization error:', error)
    
    // Handle OpenAI API errors specifically
    if (error instanceof Error && error.message.includes('API key')) {
      return UnifiedResponse.error('AI service configuration error', 503)
    }
    
    return UnifiedResponse.error('Failed to generate summary')
  }
})