-- ============================================================================
-- GDPR COMPLIANCE TABLES AND POLICIES
-- ============================================================================
-- Implements GDPR requirements including consent management, data requests,
-- and privacy controls for BookScribe AI

-- ============================================================================
-- 1. USER CONSENT TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.user_consent (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    marketing BOOLEAN NOT NULL DEFAULT false,
    analytics BOOLEAN NOT NULL DEFAULT false,
    third_party_sharing BOOLEAN NOT NULL DEFAULT false,
    ai_data_processing BOOLEAN NOT NULL DEFAULT true,
    performance_tracking BOOLEAN NOT NULL DEFAULT false,
    consented_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    consent_version VARCHAR(20) DEFAULT '1.0',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_user_consent_user_id ON public.user_consent(user_id, consented_at DESC);
CREATE INDEX idx_user_consent_date ON public.user_consent(consented_at DESC);

-- Enable RLS
ALTER TABLE public.user_consent ENABLE ROW LEVEL SECURITY;

-- Users can view their own consent history
CREATE POLICY "Users can view own consent history" ON public.user_consent
    FOR SELECT USING (auth.uid() = user_id);

-- System can insert consent records (service role)
CREATE POLICY "System can insert consent records" ON public.user_consent
    FOR INSERT WITH CHECK (true);

-- ============================================================================
-- 2. GDPR REQUESTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.gdpr_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN (
        'access', 'erasure', 'rectification', 
        'portability', 'consent_withdrawal', 'processing_restriction'
    )),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'in_progress', 'completed', 'rejected', 'expired'
    )),
    reason TEXT,
    data_categories TEXT[],
    download_url TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_gdpr_requests_user ON public.gdpr_requests(user_id, created_at DESC);
CREATE INDEX idx_gdpr_requests_status ON public.gdpr_requests(status, created_at DESC);
CREATE INDEX idx_gdpr_requests_type ON public.gdpr_requests(type, status);
CREATE INDEX idx_gdpr_requests_expires ON public.gdpr_requests(expires_at) WHERE status = 'completed';

-- Enable RLS
ALTER TABLE public.gdpr_requests ENABLE ROW LEVEL SECURITY;

-- Users can view their own GDPR requests
CREATE POLICY "Users can view own GDPR requests" ON public.gdpr_requests
    FOR SELECT USING (auth.uid() = user_id);

-- System can manage GDPR requests (service role)
CREATE POLICY "System can manage GDPR requests" ON public.gdpr_requests
    FOR ALL WITH CHECK (true);

-- ============================================================================
-- 3. GDPR EXPORTS TABLE (Secure Download Links)
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.gdpr_exports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    request_id UUID REFERENCES public.gdpr_requests(id) ON DELETE CASCADE,
    data TEXT NOT NULL, -- Encrypted data
    format VARCHAR(20) DEFAULT 'json' CHECK (format IN ('json', 'csv', 'xml')),
    size_bytes BIGINT,
    checksum VARCHAR(64), -- SHA-256 checksum
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    downloaded_at TIMESTAMP WITH TIME ZONE,
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_gdpr_exports_user ON public.gdpr_exports(user_id, created_at DESC);
CREATE INDEX idx_gdpr_exports_expires ON public.gdpr_exports(expires_at);
CREATE INDEX idx_gdpr_exports_request ON public.gdpr_exports(request_id);

-- Enable RLS
ALTER TABLE public.gdpr_exports ENABLE ROW LEVEL SECURITY;

-- Users can access their own exports
CREATE POLICY "Users can access own exports" ON public.gdpr_exports
    FOR SELECT USING (auth.uid() = user_id AND expires_at > NOW());

-- ============================================================================
-- 4. USER PREFERENCES TABLE (Privacy Settings)
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    analytics_enabled BOOLEAN NOT NULL DEFAULT false,
    marketing_emails BOOLEAN NOT NULL DEFAULT false,
    performance_tracking BOOLEAN NOT NULL DEFAULT false,
    data_retention_days INTEGER DEFAULT 365,
    auto_delete_enabled BOOLEAN DEFAULT false,
    privacy_mode VARCHAR(20) DEFAULT 'standard' CHECK (privacy_mode IN ('minimal', 'standard', 'full')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Users can manage their own preferences
CREATE POLICY "Users can view own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ============================================================================
-- 5. EMAIL PREFERENCES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.email_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    marketing_emails BOOLEAN NOT NULL DEFAULT false,
    newsletter BOOLEAN NOT NULL DEFAULT false,
    product_updates BOOLEAN NOT NULL DEFAULT true,
    promotional_offers BOOLEAN NOT NULL DEFAULT false,
    account_notifications BOOLEAN NOT NULL DEFAULT true,
    writing_reminders BOOLEAN NOT NULL DEFAULT true,
    achievement_notifications BOOLEAN NOT NULL DEFAULT true,
    collaboration_notifications BOOLEAN NOT NULL DEFAULT true,
    unsubscribe_token VARCHAR(255) UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS
ALTER TABLE public.email_preferences ENABLE ROW LEVEL SECURITY;

-- Users can manage their own email preferences
CREATE POLICY "Users can manage own email preferences" ON public.email_preferences
    FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- 6. DELETED USER ARCHIVES (Legal Retention)
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.deleted_user_archives (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    anonymous_id VARCHAR(255) NOT NULL UNIQUE,
    encrypted_data TEXT NOT NULL,
    deletion_reason VARCHAR(50),
    deleted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    retention_until TIMESTAMP WITH TIME ZONE NOT NULL,
    metadata JSONB
);

-- Add index for cleanup
CREATE INDEX idx_deleted_user_archives_retention ON public.deleted_user_archives(retention_until);

-- No RLS - admin access only

-- ============================================================================
-- 7. DATA PROCESSING LOG
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.data_processing_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    processing_type VARCHAR(50) NOT NULL,
    purpose VARCHAR(100) NOT NULL,
    legal_basis VARCHAR(50) NOT NULL CHECK (legal_basis IN (
        'consent', 'contract', 'legal_obligation', 
        'vital_interests', 'public_task', 'legitimate_interests'
    )),
    data_categories TEXT[],
    third_party_sharing BOOLEAN DEFAULT false,
    retention_days INTEGER,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_data_processing_log_user ON public.data_processing_log(user_id, processed_at DESC);
CREATE INDEX idx_data_processing_log_type ON public.data_processing_log(processing_type, processed_at DESC);

-- Enable RLS
ALTER TABLE public.data_processing_log ENABLE ROW LEVEL SECURITY;

-- Users can view their own processing log
CREATE POLICY "Users can view own processing log" ON public.data_processing_log
    FOR SELECT USING (auth.uid() = user_id);

-- ============================================================================
-- 8. CONSENT AUDIT LOG
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.consent_audit_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL CHECK (action IN ('granted', 'withdrawn', 'modified')),
    consent_type VARCHAR(50) NOT NULL,
    old_value BOOLEAN,
    new_value BOOLEAN,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_consent_audit_user ON public.consent_audit_log(user_id, created_at DESC);
CREATE INDEX idx_consent_audit_action ON public.consent_audit_log(action, created_at DESC);

-- Enable RLS
ALTER TABLE public.consent_audit_log ENABLE ROW LEVEL SECURITY;

-- Users can view their own audit log
CREATE POLICY "Users can view own consent audit" ON public.consent_audit_log
    FOR SELECT USING (auth.uid() = user_id);

-- ============================================================================
-- 9. FUNCTIONS
-- ============================================================================

-- Function to check if user has given consent for specific purpose
CREATE OR REPLACE FUNCTION check_user_consent(
    p_user_id UUID,
    p_consent_type TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_consent BOOLEAN;
BEGIN
    SELECT 
        CASE p_consent_type
            WHEN 'marketing' THEN marketing
            WHEN 'analytics' THEN analytics
            WHEN 'third_party_sharing' THEN third_party_sharing
            WHEN 'ai_data_processing' THEN ai_data_processing
            WHEN 'performance_tracking' THEN performance_tracking
            ELSE false
        END INTO v_consent
    FROM public.user_consent
    WHERE user_id = p_user_id
    ORDER BY consented_at DESC
    LIMIT 1;
    
    RETURN COALESCE(v_consent, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log consent changes
CREATE OR REPLACE FUNCTION log_consent_change() RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Log all initial consents
        INSERT INTO public.consent_audit_log (user_id, action, consent_type, old_value, new_value, ip_address, user_agent)
        VALUES 
            (NEW.user_id, 'granted', 'marketing', NULL, NEW.marketing, NEW.ip_address, NEW.user_agent),
            (NEW.user_id, 'granted', 'analytics', NULL, NEW.analytics, NEW.ip_address, NEW.user_agent),
            (NEW.user_id, 'granted', 'third_party_sharing', NULL, NEW.third_party_sharing, NEW.ip_address, NEW.user_agent),
            (NEW.user_id, 'granted', 'ai_data_processing', NULL, NEW.ai_data_processing, NEW.ip_address, NEW.user_agent),
            (NEW.user_id, 'granted', 'performance_tracking', NULL, NEW.performance_tracking, NEW.ip_address, NEW.user_agent);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for consent audit logging
CREATE TRIGGER trigger_log_consent_changes
    AFTER INSERT ON public.user_consent
    FOR EACH ROW
    EXECUTE FUNCTION log_consent_change();

-- Function to anonymize old data
CREATE OR REPLACE FUNCTION anonymize_old_user_data() RETURNS void AS $$
DECLARE
    v_cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get cutoff date (users inactive for more than 2 years)
    v_cutoff_date := NOW() - INTERVAL '2 years';
    
    -- Anonymize old session data
    UPDATE public.user_sessions
    SET ip_address = '0.0.0.0'::inet,
        user_agent = 'Anonymized'
    WHERE created_at < v_cutoff_date;
    
    -- Anonymize old analytics data
    UPDATE public.user_analytics
    SET metadata = jsonb_set(metadata, '{anonymized}', 'true')
    WHERE created_at < v_cutoff_date;
    
    -- Log the anonymization
    INSERT INTO public.data_processing_log (
        processing_type, 
        purpose, 
        legal_basis,
        data_categories
    ) VALUES (
        'anonymization',
        'Privacy compliance - automatic data minimization',
        'legal_obligation',
        ARRAY['technical_data', 'usage_data']
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle GDPR data export
CREATE OR REPLACE FUNCTION export_user_data_for_gdpr(p_user_id UUID) 
RETURNS TABLE(category TEXT, data JSONB) AS $$
BEGIN
    -- Personal Information
    RETURN QUERY
    SELECT 'personal_info'::TEXT, 
           jsonb_build_object(
               'profile', row_to_json(p.*),
               'consent', row_to_json(c.*)
           )
    FROM public.profiles p
    LEFT JOIN public.user_consent c ON c.user_id = p.id
    WHERE p.id = p_user_id;
    
    -- Projects and Content
    RETURN QUERY
    SELECT 'content_data'::TEXT,
           jsonb_agg(
               jsonb_build_object(
                   'project', row_to_json(pr.*),
                   'chapters', ch.chapters,
                   'characters', char.characters
               )
           )
    FROM public.projects pr
    LEFT JOIN LATERAL (
        SELECT jsonb_agg(row_to_json(c.*)) as chapters
        FROM public.chapters c
        WHERE c.project_id = pr.id
    ) ch ON true
    LEFT JOIN LATERAL (
        SELECT jsonb_agg(row_to_json(chr.*)) as characters
        FROM public.characters chr
        WHERE chr.project_id = pr.id
    ) char ON true
    WHERE pr.user_id = p_user_id
    GROUP BY pr.user_id;
    
    -- Add more categories as needed
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 10. SCHEDULED MAINTENANCE
-- ============================================================================

-- Function to clean up expired GDPR exports
CREATE OR REPLACE FUNCTION cleanup_expired_gdpr_exports() RETURNS void AS $$
BEGIN
    DELETE FROM public.gdpr_exports
    WHERE expires_at < NOW();
    
    -- Update expired requests
    UPDATE public.gdpr_requests
    SET status = 'expired'
    WHERE status = 'completed' 
      AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 11. GRANT PERMISSIONS
-- ============================================================================

GRANT EXECUTE ON FUNCTION check_user_consent(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION export_user_data_for_gdpr(UUID) TO authenticated;

-- ============================================================================
-- 12. ADD UPDATED_AT TRIGGERS
-- ============================================================================

CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_email_preferences_updated_at
    BEFORE UPDATE ON public.email_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_gdpr_requests_updated_at
    BEFORE UPDATE ON public.gdpr_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Add comments for documentation
COMMENT ON TABLE public.user_consent IS 'Stores user consent records for GDPR compliance';
COMMENT ON TABLE public.gdpr_requests IS 'Tracks GDPR data requests (access, erasure, etc.)';
COMMENT ON TABLE public.gdpr_exports IS 'Secure storage for GDPR data exports with expiry';
COMMENT ON TABLE public.user_preferences IS 'User privacy and data handling preferences';
COMMENT ON TABLE public.deleted_user_archives IS 'Encrypted archives of deleted user data for legal retention';
COMMENT ON FUNCTION check_user_consent(UUID, TEXT) IS 'Checks if user has given consent for specific data processing';
COMMENT ON FUNCTION export_user_data_for_gdpr(UUID) IS 'Exports all user data for GDPR data access requests';