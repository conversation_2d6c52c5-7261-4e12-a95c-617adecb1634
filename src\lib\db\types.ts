export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

// JSONB Type Definitions for structured data
export interface StoryStructure {
  acts?: Array<{
    number: number
    description: string
    chapters: number[]
    key_events: string[]
  }>
  plot_points?: Array<{
    type: 'inciting_incident' | 'plot_point_1' | 'midpoint' | 'plot_point_2' | 'climax' | 'resolution'
    chapter: number
    description: string
  }>
  world_building?: {
    setting: string
    rules: string[]
    locations: Array<{ name: string; description: string }>
  }
  timeline?: Array<{
    date: string
    event: string
    chapter?: number
  }>
}

export interface CharacterData {
  [character_id: string]: {
    name: string
    role: string
    description: string
    backstory: string
    personality_traits: string[]
    relationships: Array<{ character_id: string; relationship: string; description: string }>
    voice_data?: {
      speaking_style: string
      vocabulary: string[]
      mannerisms: string[]
    }
  }
}

export interface ChapterScenes {
  scenes: Array<{
    number: number
    setting: string
    characters: string[]
    objectives: string[]
    conflicts: string[]
    resolutions: string[]
  }>
}

export interface CharacterStates {
  [character_id: string]: {
    emotional_state: string
    goals: string[]
    knowledge: string[]
    relationships: { [other_character_id: string]: string }
  }
}

export interface PlotAdvancement {
  main_plot: {
    threads: string[]
    advancement: string
    conflicts_introduced: string[]
    conflicts_resolved: string[]
  }
  subplots: Array<{
    name: string
    advancement: string
    status: 'active' | 'resolved' | 'paused'
  }>
}

export interface AiAnalysis {
  objectives?: string[]
  conflicts?: string[]
  resolutions?: string[]
  notes?: string[]
  quality_score?: number
  suggestions?: string[]
}

export interface VoiceData {
  speaking_style: string
  vocabulary: string[]
  mannerisms: string[]
}

export interface PersonalityTraits {
  traits: string[]
  strengths: string[]
  flaws: string[]
  motivations: string[]
}

export interface CharacterArc {
  starting_point: string
  transformation: string
  ending_point: string
  key_moments: Array<{
    chapter: number
    description: string
  }>
}

export interface Relationships {
  [character_id: string]: {
    relationship_type: string
    description: string
    history: string
    current_status: string
  }
}

export interface QualityScore {
  overall: number
  structure: number
  character_development: number
  pacing: number
  dialogue: number
  description: number
  notes: string[]
}

// Database interface following actual Supabase schema
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string | null
          full_name: string | null
          avatar_url: string | null
          stripe_customer_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      user_subscriptions: {
        Row: {
          id: string
          user_id: string
          tier_id: string
          status: 'active' | 'canceled' | 'past_due' | 'incomplete'
          stripe_subscription_id: string | null
          stripe_customer_id: string | null
          current_period_start: string | null
          current_period_end: string | null
          cancel_at_period_end: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          tier_id: string
          status: 'active' | 'canceled' | 'past_due' | 'incomplete'
          stripe_subscription_id?: string | null
          stripe_customer_id?: string | null
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          tier_id?: string
          status?: 'active' | 'canceled' | 'past_due' | 'incomplete'
          stripe_subscription_id?: string | null
          stripe_customer_id?: string | null
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      usage_tracking: {
        Row: {
          id: string
          user_id: string
          period_start: string
          ai_generations: number
          projects: number
          exports: number
          storage_used: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          period_start: string
          ai_generations?: number
          projects?: number
          exports?: number
          storage_used?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          period_start?: string
          ai_generations?: number
          projects?: number
          exports?: number
          storage_used?: number
          created_at?: string
          updated_at?: string
        }
      }
      usage_events: {
        Row: {
          id: string
          user_id: string
          event_type: string
          amount: number
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          event_type: string
          amount?: number
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          event_type?: string
          amount?: number
          metadata?: Json | null
          created_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          // Genre & Style Selections
          primary_genre: string | null
          subgenre: string | null
          custom_genre: string | null
          narrative_voice: string | null
          tense: string | null
          tone_options: string[] | null
          writing_style: string | null
          custom_style_description: string | null
          // Story Structure & Pacing
          structure_type: string | null
          pacing_preference: string | null
          chapter_structure: string | null
          timeline_complexity: string | null
          custom_structure_notes: string | null
          // Character & World Building
          protagonist_types: string[] | null
          antagonist_types: string[] | null
          character_complexity: string | null
          character_arc_types: string[] | null
          custom_character_concepts: string | null
          time_period: string | null
          geographic_setting: string | null
          world_type: string | null
          magic_tech_level: string | null
          custom_setting_description: string | null
          // Themes & Content
          major_themes: string[] | null
          philosophical_themes: string[] | null
          social_themes: string[] | null
          custom_themes: string | null
          target_audience: string | null
          content_rating: string | null
          content_warnings: string[] | null
          cultural_sensitivity_notes: string | null
          // Series & Scope
          project_scope: string | null
          series_type: string | null
          interconnection_level: string | null
          custom_scope_description: string | null
          // Technical Specifications
          target_word_count: number | null
          current_word_count: number
          target_chapters: number | null
          chapter_count_type: string | null
          pov_character_count: number | null
          pov_character_type: string | null
          // Research & References
          research_needs: string[] | null
          fact_checking_level: string | null
          custom_research_notes: string | null
          // Initial story concept (enhanced schema only)
          initial_concept: string | null
          // System Fields
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          // Genre & Style Selections
          primary_genre?: string | null
          subgenre?: string | null
          custom_genre?: string | null
          narrative_voice?: string | null
          tense?: string | null
          tone_options?: string[] | null
          writing_style?: string | null
          custom_style_description?: string | null
          // Story Structure & Pacing
          structure_type?: string | null
          pacing_preference?: string | null
          chapter_structure?: string | null
          timeline_complexity?: string | null
          custom_structure_notes?: string | null
          // Character & World Building
          protagonist_types?: string[] | null
          antagonist_types?: string[] | null
          character_complexity?: string | null
          character_arc_types?: string[] | null
          custom_character_concepts?: string | null
          time_period?: string | null
          geographic_setting?: string | null
          world_type?: string | null
          magic_tech_level?: string | null
          custom_setting_description?: string | null
          // Themes & Content
          major_themes?: string[] | null
          philosophical_themes?: string[] | null
          social_themes?: string[] | null
          custom_themes?: string | null
          target_audience?: string | null
          content_rating?: string | null
          content_warnings?: string[] | null
          cultural_sensitivity_notes?: string | null
          // Series & Scope
          project_scope?: string | null
          series_type?: string | null
          interconnection_level?: string | null
          custom_scope_description?: string | null
          // Technical Specifications
          target_word_count?: number | null
          current_word_count?: number
          target_chapters?: number | null
          chapter_count_type?: string | null
          pov_character_count?: number | null
          pov_character_type?: string | null
          // Research & References
          research_needs?: string[] | null
          fact_checking_level?: string | null
          custom_research_notes?: string | null
          // Initial story concept
          initial_concept?: string | null
          // System Fields
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          // Genre & Style Selections
          primary_genre?: string | null
          subgenre?: string | null
          custom_genre?: string | null
          narrative_voice?: string | null
          tense?: string | null
          tone_options?: string[] | null
          writing_style?: string | null
          custom_style_description?: string | null
          // Story Structure & Pacing
          structure_type?: string | null
          pacing_preference?: string | null
          chapter_structure?: string | null
          timeline_complexity?: string | null
          custom_structure_notes?: string | null
          // Character & World Building
          protagonist_types?: string[] | null
          antagonist_types?: string[] | null
          character_complexity?: string | null
          character_arc_types?: string[] | null
          custom_character_concepts?: string | null
          time_period?: string | null
          geographic_setting?: string | null
          world_type?: string | null
          magic_tech_level?: string | null
          custom_setting_description?: string | null
          // Themes & Content
          major_themes?: string[] | null
          philosophical_themes?: string[] | null
          social_themes?: string[] | null
          custom_themes?: string | null
          target_audience?: string | null
          content_rating?: string | null
          content_warnings?: string[] | null
          cultural_sensitivity_notes?: string | null
          // Series & Scope
          project_scope?: string | null
          series_type?: string | null
          interconnection_level?: string | null
          custom_scope_description?: string | null
          // Technical Specifications
          target_word_count?: number | null
          current_word_count?: number
          target_chapters?: number | null
          chapter_count_type?: string | null
          pov_character_count?: number | null
          pov_character_type?: string | null
          // Research & References
          research_needs?: string[] | null
          fact_checking_level?: string | null
          custom_research_notes?: string | null
          // Initial story concept
          initial_concept?: string | null
          // System Fields
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      story_arcs: {
        Row: {
          id: string
          project_id: string
          structure_data: Json | null
          act_number: number | null
          description: string | null
          key_events: Json | null
          plot_points: Json | null
          world_building: Json | null
          timeline: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          structure_data?: Json | null
          act_number?: number | null
          description?: string | null
          key_events?: Json | null
          plot_points?: Json | null
          world_building?: Json | null
          timeline?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          structure_data?: Json | null
          act_number?: number | null
          description?: string | null
          key_events?: Json | null
          plot_points?: Json | null
          world_building?: Json | null
          timeline?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      characters: {
        Row: {
          id: string
          project_id: string
          character_id: string
          name: string
          role: string
          description: string | null
          backstory: string | null
          personality_traits: Json | null
          character_arc: Json | null
          relationships: Json | null
          voice_data: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          character_id: string
          name: string
          role: string
          description?: string | null
          backstory?: string | null
          personality_traits?: Json | null
          character_arc?: Json | null
          relationships?: Json | null
          voice_data?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          character_id?: string
          name?: string
          role?: string
          description?: string | null
          backstory?: string | null
          personality_traits?: Json | null
          character_arc?: Json | null
          relationships?: Json | null
          voice_data?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      chapters: {
        Row: {
          id: string
          project_id: string
          chapter_number: number
          title: string | null
          target_word_count: number | null
          actual_word_count: number
          outline: string | null
          content: string | null
          scenes_data: Json | null
          character_states: Json | null
          status: string
          ai_notes: Json | null
          pov_character: string | null
          plot_advancement: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          chapter_number: number
          title?: string | null
          target_word_count?: number | null
          actual_word_count?: number
          outline?: string | null
          content?: string | null
          scenes_data?: Json | null
          character_states?: Json | null
          status?: string
          ai_notes?: Json | null
          pov_character?: string | null
          plot_advancement?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          chapter_number?: number
          title?: string | null
          target_word_count?: number | null
          actual_word_count?: number
          outline?: string | null
          content?: string | null
          scenes_data?: Json | null
          character_states?: Json | null
          status?: string
          ai_notes?: Json | null
          pov_character?: string | null
          plot_advancement?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      agent_logs: {
        Row: {
          id: string
          project_id: string
          agent_type: string
          input_data: Json | null
          output_data: Json | null
          execution_time: number | null
          status: string
          error_message: string | null
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          agent_type: string
          input_data?: Json | null
          output_data?: Json | null
          execution_time?: number | null
          status: string
          error_message?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          agent_type?: string
          input_data?: Json | null
          output_data?: Json | null
          execution_time?: number | null
          status?: string
          error_message?: string | null
          created_at?: string
        }
      }
      selection_profiles: {
        Row: {
          id: string
          user_id: string | null
          name: string
          description: string | null
          is_public: boolean
          // All the same selection fields as projects table
          primary_genre: string | null
          subgenre: string | null
          custom_genre: string | null
          narrative_voice: string | null
          tense: string | null
          tone_options: string[] | null
          writing_style: string | null
          custom_style_description: string | null
          structure_type: string | null
          pacing_preference: string | null
          chapter_structure: string | null
          timeline_complexity: string | null
          custom_structure_notes: string | null
          protagonist_types: string[] | null
          antagonist_types: string[] | null
          character_complexity: string | null
          character_arc_types: string[] | null
          custom_character_concepts: string | null
          time_period: string | null
          geographic_setting: string | null
          world_type: string | null
          magic_tech_level: string | null
          custom_setting_description: string | null
          major_themes: string[] | null
          philosophical_themes: string[] | null
          social_themes: string[] | null
          custom_themes: string | null
          target_audience: string | null
          content_rating: string | null
          content_warnings: string[] | null
          cultural_sensitivity_notes: string | null
          project_scope: string | null
          series_type: string | null
          interconnection_level: string | null
          custom_scope_description: string | null
          target_word_count: number | null
          chapter_count_type: string | null
          pov_character_count: number | null
          pov_character_type: string | null
          research_needs: string[] | null
          fact_checking_level: string | null
          custom_research_notes: string | null
          usage_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          name: string
          description?: string | null
          is_public?: boolean
          // All the same selection fields as projects table
          primary_genre?: string | null
          subgenre?: string | null
          custom_genre?: string | null
          narrative_voice?: string | null
          tense?: string | null
          tone_options?: string[] | null
          writing_style?: string | null
          custom_style_description?: string | null
          structure_type?: string | null
          pacing_preference?: string | null
          chapter_structure?: string | null
          timeline_complexity?: string | null
          custom_structure_notes?: string | null
          protagonist_types?: string[] | null
          antagonist_types?: string[] | null
          character_complexity?: string | null
          character_arc_types?: string[] | null
          custom_character_concepts?: string | null
          time_period?: string | null
          geographic_setting?: string | null
          world_type?: string | null
          magic_tech_level?: string | null
          custom_setting_description?: string | null
          major_themes?: string[] | null
          philosophical_themes?: string[] | null
          social_themes?: string[] | null
          custom_themes?: string | null
          target_audience?: string | null
          content_rating?: string | null
          content_warnings?: string[] | null
          cultural_sensitivity_notes?: string | null
          project_scope?: string | null
          series_type?: string | null
          interconnection_level?: string | null
          custom_scope_description?: string | null
          target_word_count?: number | null
          chapter_count_type?: string | null
          pov_character_count?: number | null
          pov_character_type?: string | null
          research_needs?: string[] | null
          fact_checking_level?: string | null
          custom_research_notes?: string | null
          usage_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          name?: string
          description?: string | null
          is_public?: boolean
          // All the same selection fields as projects table
          primary_genre?: string | null
          subgenre?: string | null
          custom_genre?: string | null
          narrative_voice?: string | null
          tense?: string | null
          tone_options?: string[] | null
          writing_style?: string | null
          custom_style_description?: string | null
          structure_type?: string | null
          pacing_preference?: string | null
          chapter_structure?: string | null
          timeline_complexity?: string | null
          custom_structure_notes?: string | null
          protagonist_types?: string[] | null
          antagonist_types?: string[] | null
          character_complexity?: string | null
          character_arc_types?: string[] | null
          custom_character_concepts?: string | null
          time_period?: string | null
          geographic_setting?: string | null
          world_type?: string | null
          magic_tech_level?: string | null
          custom_setting_description?: string | null
          major_themes?: string[] | null
          philosophical_themes?: string[] | null
          social_themes?: string[] | null
          custom_themes?: string | null
          target_audience?: string | null
          content_rating?: string | null
          content_warnings?: string[] | null
          cultural_sensitivity_notes?: string | null
          project_scope?: string | null
          series_type?: string | null
          interconnection_level?: string | null
          custom_scope_description?: string | null
          target_word_count?: number | null
          chapter_count_type?: string | null
          pov_character_count?: number | null
          pov_character_type?: string | null
          research_needs?: string[] | null
          fact_checking_level?: string | null
          custom_research_notes?: string | null
          usage_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      reference_materials: {
        Row: {
          id: string
          project_id: string
          user_id: string
          name: string
          description: string | null
          file_type: string | null
          file_url: string | null
          file_size: number | null
          mime_type: string | null
          tags: string[] | null
          is_processed: boolean
          processing_status: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          name: string
          description?: string | null
          file_type?: string | null
          file_url?: string | null
          file_size?: number | null
          mime_type?: string | null
          tags?: string[] | null
          is_processed?: boolean
          processing_status?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          name?: string
          description?: string | null
          file_type?: string | null
          file_url?: string | null
          file_size?: number | null
          mime_type?: string | null
          tags?: string[] | null
          is_processed?: boolean
          processing_status?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      selection_analytics: {
        Row: {
          id: string
          user_id: string
          project_id: string
          selection_profile_id: string | null
          event_type: string
          selection_data: Json | null
          outcome_data: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          project_id: string
          selection_profile_id?: string | null
          event_type: string
          selection_data?: Json | null
          outcome_data?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          project_id?: string
          selection_profile_id?: string | null
          event_type?: string
          selection_data?: Json | null
          outcome_data?: Json | null
          created_at?: string
        }
      }
      story_bible: {
        Row: {
          id: string
          project_id: string
          entry_type: string
          entry_key: string
          entry_data: Json
          chapter_introduced: number | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          entry_type: string
          entry_key: string
          entry_data: Json
          chapter_introduced?: number | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          entry_type?: string
          entry_key?: string
          entry_data?: Json
          chapter_introduced?: number | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      story_bibles: {
        Row: {
          id: string
          project_id: string
          structure_data: Json | null
          character_data: Json | null
          world_data: Json | null
          timeline_data: Json | null
          theme_tracking: Json | null
          continuity_data: Json | null
          style_guide: Json | null
          last_updated: string
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          structure_data?: Json | null
          character_data?: Json | null
          world_data?: Json | null
          timeline_data?: Json | null
          theme_tracking?: Json | null
          continuity_data?: Json | null
          style_guide?: Json | null
          last_updated?: string
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          structure_data?: Json | null
          character_data?: Json | null
          world_data?: Json | null
          timeline_data?: Json | null
          theme_tracking?: Json | null
          continuity_data?: Json | null
          style_guide?: Json | null
          last_updated?: string
          created_at?: string
        }
      }
      chapter_versions: {
        Row: {
          id: string
          chapter_id: string
          version_number: number
          content: string
          word_count: number | null
          changes_summary: string | null
          quality_score: Json | null
          created_by: string | null
          created_at: string
        }
        Insert: {
          id?: string
          chapter_id: string
          version_number: number
          content: string
          word_count?: number | null
          changes_summary?: string | null
          quality_score?: Json | null
          created_by?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          chapter_id?: string
          version_number?: number
          content?: string
          word_count?: number | null
          changes_summary?: string | null
          quality_score?: Json | null
          created_by?: string | null
          created_at?: string
        }
      }
      writing_sessions: {
        Row: {
          id: string
          project_id: string
          user_id: string
          chapter_id: string | null
          session_type: string | null
          words_written: number
          words_edited: number | null
          duration_minutes: number | null
          ai_assistance_used: boolean | null
          ai_suggestions_accepted: number | null
          ai_suggestions_rejected: number | null
          session_notes: string | null
          started_at: string
          ended_at: string | null
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          chapter_id?: string | null
          session_type?: string | null
          words_written?: number
          words_edited?: number | null
          duration_minutes?: number | null
          ai_assistance_used?: boolean | null
          ai_suggestions_accepted?: number | null
          ai_suggestions_rejected?: number | null
          session_notes?: string | null
          started_at: string
          ended_at?: string | null
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          chapter_id?: string | null
          session_type?: string | null
          words_written?: number
          words_edited?: number | null
          duration_minutes?: number | null
          ai_assistance_used?: boolean | null
          ai_suggestions_accepted?: number | null
          ai_suggestions_rejected?: number | null
          session_notes?: string | null
          started_at?: string
          ended_at?: string | null
        }
      }
      editing_sessions: {
        Row: {
          id: string
          user_id: string
          chapter_id: string
          selection_start: number | null
          selection_end: number | null
          selected_text: string | null
          ai_prompt: string | null
          ai_response: string | null
          action_type: string | null
          was_applied: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          chapter_id: string
          selection_start?: number | null
          selection_end?: number | null
          selected_text?: string | null
          ai_prompt?: string | null
          ai_response?: string | null
          action_type?: string | null
          was_applied?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          chapter_id?: string
          selection_start?: number | null
          selection_end?: number | null
          selected_text?: string | null
          ai_prompt?: string | null
          ai_response?: string | null
          action_type?: string | null
          was_applied?: boolean
          created_at?: string
        }
      }
      processing_tasks: {
        Row: {
          id: string
          project_id: string
          user_id: string
          type: string
          status: string
          priority: string
          payload: Json
          result: Json | null
          error: string | null
          estimated_duration: number
          created_at: string
          started_at: string | null
          completed_at: string | null
          actual_duration: number | null
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          type: string
          status?: string
          priority?: string
          payload: Json
          result?: Json | null
          error?: string | null
          estimated_duration?: number
          created_at?: string
          started_at?: string | null
          completed_at?: string | null
          actual_duration?: number | null
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          type?: string
          status?: string
          priority?: string
          payload?: Json
          result?: Json | null
          error?: string | null
          estimated_duration?: number
          created_at?: string
          started_at?: string | null
          completed_at?: string | null
          actual_duration?: number | null
        }
      }
      content_embeddings: {
        Row: {
          id: string
          project_id: string
          content_type: string
          content_id: string
          text_content: string
          embedding: number[]
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          content_type: string
          content_id: string
          text_content: string
          embedding: number[]
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          content_type?: string
          content_id?: string
          text_content?: string
          embedding?: number[]
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      writing_goals: {
        Row: {
          id: string
          user_id: string
          project_id: string | null
          goal_type: 'daily' | 'weekly' | 'monthly' | 'project'
          target_words: number
          start_date: string
          end_date: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          project_id?: string | null
          goal_type: 'daily' | 'weekly' | 'monthly' | 'project'
          target_words: number
          start_date: string
          end_date?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          project_id?: string | null
          goal_type?: 'daily' | 'weekly' | 'monthly' | 'project'
          target_words?: number
          start_date?: string
          end_date?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      writing_goal_progress: {
        Row: {
          id: string
          goal_id: string
          date: string
          words_written: number
          sessions_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          goal_id: string
          date: string
          words_written?: number
          sessions_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          goal_id?: string
          date?: string
          words_written?: number
          sessions_count?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      increment_profile_usage: {
        Args: {
          profile_id: string
        }
        Returns: void
      }
      update_updated_at_column: {
        Args: Record<PropertyKey, never>
        Returns: unknown
      }
      search_similar_content: {
        Args: {
          query_embedding: number[]
          project_uuid: string
          content_type_filter?: string | null
          similarity_threshold?: number
          match_count?: number
        }
        Returns: {
          id: string
          content_type: string
          content_id: string
          text_content: string
          metadata: Json | null
          similarity: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Helper Types for easier access
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Specific table types for convenience
export type Profile = Tables<'profiles'>
export type UserSubscription = Tables<'user_subscriptions'>
export type UsageTracking = Tables<'usage_tracking'>
export type UsageEvent = Tables<'usage_events'>
export type Project = Tables<'projects'>
export type StoryArc = Tables<'story_arcs'>
export type Chapter = Tables<'chapters'>
export type Character = Tables<'characters'>
export type StoryBible = Tables<'story_bible'>
export type StoryBibles = Tables<'story_bibles'>
export type ReferenceMaterial = Tables<'reference_materials'>
export type AgentLog = Tables<'agent_logs'>
export type SelectionProfile = Tables<'selection_profiles'>
export type SelectionAnalytics = Tables<'selection_analytics'>
export type WritingSession = Tables<'writing_sessions'>
export type ChapterVersion = Tables<'chapter_versions'>
export type EditingSession = Tables<'editing_sessions'>
export type ProcessingTask = Tables<'processing_tasks'>
export type ContentEmbedding = Tables<'content_embeddings'>
export type WritingGoal = Tables<'writing_goals'>
export type WritingGoalProgress = Tables<'writing_goal_progress'>

// Insert types for convenience
export type ProfileInsert = TablesInsert<'profiles'>
export type ProjectInsert = TablesInsert<'projects'>
export type ChapterInsert = TablesInsert<'chapters'>
export type CharacterInsert = TablesInsert<'characters'>
export type SelectionProfileInsert = TablesInsert<'selection_profiles'>
export type WritingSessionInsert = TablesInsert<'writing_sessions'>
export type AgentLogInsert = TablesInsert<'agent_logs'>
export type ReferenceMaterialInsert = TablesInsert<'reference_materials'>
export type StoryBibleInsert = TablesInsert<'story_bible'>
export type ProcessingTaskInsert = TablesInsert<'processing_tasks'>
export type ContentEmbeddingInsert = TablesInsert<'content_embeddings'>
export type WritingGoalInsert = TablesInsert<'writing_goals'>
export type WritingGoalProgressInsert = TablesInsert<'writing_goal_progress'>

// Update types for convenience
export type ProjectUpdate = TablesUpdate<'projects'>
export type ChapterUpdate = TablesUpdate<'chapters'>
export type CharacterUpdate = TablesUpdate<'characters'>
export type SelectionProfileUpdate = TablesUpdate<'selection_profiles'>
export type ProcessingTaskUpdate = TablesUpdate<'processing_tasks'>
export type ContentEmbeddingUpdate = TablesUpdate<'content_embeddings'>

// Enum types based on schema constraints and common values
export type SubscriptionStatus = 'active' | 'canceled' | 'past_due' | 'incomplete'
export type ProjectStatus = 'planning' | 'writing' | 'editing' | 'completed' | 'paused' | 'archived'
export type ChapterStatus = 'planned' | 'writing' | 'review' | 'complete'
export type CharacterRole = 'protagonist' | 'antagonist' | 'supporting' | 'minor'
export type ContentRating = 'G' | 'PG' | 'PG-13' | 'R' | 'NC-17'
export type ProjectScope = 'standalone' | 'duology' | 'trilogy' | 'series' | 'epic_series' | 'anthology'
export type WritingStyle = 'literary' | 'commercial' | 'pulp' | 'experimental' | 'minimalist' | 'descriptive' | 'dialogue_heavy'
export type NarrativeVoice = 'first_person' | 'third_person_limited' | 'third_person_omniscient' | 'second_person' | 'multiple_pov'
export type Tense = 'present' | 'past' | 'mixed'
export type AgentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
export type SessionType = 'writing' | 'editing' | 'planning'
export type ActionType = 'edit' | 'expand' | 'rewrite' | 'suggest'
export type EntryType = 'character_state' | 'plot_point' | 'world_rule' | 'timeline_event'
export type FileType = 'document' | 'image' | 'map' | 'character_sheet' | 'research'
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed'
export type ChapterCountType = 'fixed' | 'flexible' | 'scene_based'
export type CreatedBy = 'user' | 'ai_writer' | 'ai_editor'
export type EventType = 'profile_used' | 'project_completed' | 'project_abandoned' | 'ai_generation' | 'export' | 'storage_use'
export type ContentType = 'chapter' | 'character' | 'story_bible' | 'reference'

// Structured JSONB type mappings for better type safety
export type StoryStructureData = StoryStructure
export type CharacterDataMap = CharacterData
export type ChapterScenesData = ChapterScenes
export type CharacterStatesData = CharacterStates
export type PlotAdvancementData = PlotAdvancement
export type AiAnalysisData = AiAnalysis
export type VoiceDataStructure = VoiceData
export type PersonalityTraitsData = PersonalityTraits
export type CharacterArcData = CharacterArc
export type RelationshipsData = Relationships
export type QualityScoreData = QualityScore

// Series Management Types
export interface Series {
  id: string
  user_id: string
  title: string
  description?: string
  genre?: string
  target_audience?: string
  overall_arc_description?: string
  planned_book_count?: number
  current_book_count?: number
  shared_universe_rules?: Record<string, unknown>
  character_continuity?: Record<string, unknown>
  timeline_span?: string
  publication_status?: 'planning' | 'active' | 'completed' | 'hiatus'
  first_published_date?: string
  last_published_date?: string
  created_at: string
  updated_at: string
}

export interface SeriesBook {
  id: string
  series_id: string
  project_id: string
  book_number: number
  book_role?: 'main' | 'prequel' | 'sequel' | 'spin-off' | 'companion'
  chronological_order?: number
  publication_order?: number
  introduces_characters?: string[]
  concludes_arcs?: string[]
  sets_up_future?: Record<string, unknown>
  created_at: string
  updated_at: string
}

export interface SeriesCharacterArc {
  id: string
  series_id: string
  character_name: string
  arc_title?: string
  arc_description?: string
  arc_type?: 'main' | 'romance' | 'redemption' | 'growth' | string
  starts_in_book?: number
  concludes_in_book?: number
  peak_moment_book?: number
  character_growth_stages?: Record<string, unknown>
  relationship_changes?: Record<string, unknown>
  skills_or_powers_gained?: Record<string, unknown>
  created_at: string
  updated_at: string
}

export interface SeriesUniverseRule {
  id: string
  series_id: string
  rule_category: 'magic_system' | 'technology' | 'geography' | 'culture' | 'history'
  rule_title: string
  rule_description: string
  established_in_book?: number
  modified_in_books?: number[]
  exceptions?: Record<string, unknown>
  example_usage?: Record<string, unknown>
  related_rules?: string[]
  created_at: string
  updated_at: string
}

export interface SeriesContinuityIssue {
  id: string
  series_id: string
  issue_type: 'character_inconsistency' | 'timeline_conflict' | 'world_rule_violation'
  issue_title: string
  issue_description: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
  occurs_in_books?: number[]
  specific_references?: Record<string, unknown>
  status?: 'open' | 'investigating' | 'resolved' | 'wont_fix'
  resolution_notes?: string
  resolved_in_book?: number
  resolved_at?: string
  created_at: string
  updated_at: string
}

// Series Management Enum Types
export type PublicationStatus = 'planning' | 'active' | 'completed' | 'hiatus'
export type BookRole = 'main' | 'prequel' | 'sequel' | 'spin-off' | 'companion'
export type RuleCategory = 'magic_system' | 'technology' | 'geography' | 'culture' | 'history'
export type IssueType = 'character_inconsistency' | 'timeline_conflict' | 'world_rule_violation'
export type IssueSeverity = 'low' | 'medium' | 'high' | 'critical'
export type IssueStatus = 'open' | 'investigating' | 'resolved' | 'wont_fix'