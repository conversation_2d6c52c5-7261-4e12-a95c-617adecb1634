import { NextRequest } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

// Mock dependencies
jest.mock('@/lib/supabase')
jest.mock('@/lib/services/logger')
jest.mock('@/lib/auth/unified-auth-service')

const mockSupabase = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn()
}

const mockCreateServerClient = createServerClient as jest.MockedFunction<typeof createServerClient>
mockCreateServerClient.mockResolvedValue(mockSupabase as any)

describe('Location CRUD Operations Integration Tests', () => {
  const mockUserId = 'test-user-123'
  const mockProjectId = 'test-project-456'
  const mockLocationId = 'test-location-789'
  const mockUser = { user: { id: mockUserId } }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('PUT /api/projects/[id]/locations/[locationId]', () => {
    it('should update location successfully', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock location ownership check
      const locationQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: mockLocationId,
            project_id: mockProjectId,
            name: 'Old Name'
          },
          error: null
        })
      }

      // Mock project ownership check
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: mockProjectId, user_id: mockUserId },
          error: null
        })
      }

      // Mock update operation
      const updateQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: mockLocationId,
            name: 'Updated Name',
            description: 'Updated description',
            features: ['New Feature'],
            updated_at: new Date().toISOString()
          },
          error: null
        })
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'locations' && !updateQuery.update.mock.calls.length) return locationQuery
        if (table === 'projects') return projectQuery
        if (table === 'locations') return updateQuery
        return null
      })

      // Create request
      const updateData = {
        name: 'Updated Name',
        description: 'Updated description',
        features: ['New Feature']
      }

      const request = new NextRequest(
        `http://localhost/api/projects/${mockProjectId}/locations/${mockLocationId}`,
        {
          method: 'PUT',
          body: JSON.stringify(updateData)
        }
      )

      // Mock the route handler - since we don't have individual location routes yet
      const response = await mockLocationUpdateHandler(request, mockLocationId)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.location.name).toBe('Updated Name')
      expect(data.data.location.features).toContain('New Feature')
    })

    it('should prevent updating location from different project', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock location belonging to different project
      const locationQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: mockLocationId,
            project_id: 'different-project-123'
          },
          error: null
        })
      }

      // Mock project check - user doesn't own the project
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: new Error('Not found')
        })
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'locations') return locationQuery
        if (table === 'projects') return projectQuery
        return null
      })

      const request = new NextRequest(
        `http://localhost/api/projects/${mockProjectId}/locations/${mockLocationId}`,
        {
          method: 'PUT',
          body: JSON.stringify({ name: 'Hacker Attempt' })
        }
      )

      const response = await mockLocationUpdateHandler(request, mockLocationId)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('FORBIDDEN')
    })
  })

  describe('DELETE /api/projects/[id]/locations/[locationId]', () => {
    it('should delete location and its children', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock location with children check
      const locationQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: mockLocationId,
            project_id: mockProjectId
          },
          error: null
        })
      }

      // Mock project ownership
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: mockProjectId, user_id: mockUserId },
          error: null
        })
      }

      // Mock children locations query
      const childrenQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: [
            { id: 'child-1', name: 'Child Location 1' },
            { id: 'child-2', name: 'Child Location 2' }
          ],
          error: null
        })
      }

      // Mock delete operation
      const deleteQuery = {
        delete: jest.fn().mockReturnThis(),
        in: jest.fn().mockResolvedValue({
          data: null,
          error: null
        })
      }

      let queryCount = 0
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'locations') {
          queryCount++
          if (queryCount === 1) return locationQuery
          if (queryCount === 2) return childrenQuery
          if (queryCount === 3) return deleteQuery
        }
        if (table === 'projects') return projectQuery
        return null
      })

      const request = new NextRequest(
        `http://localhost/api/projects/${mockProjectId}/locations/${mockLocationId}`,
        {
          method: 'DELETE'
        }
      )

      const response = await mockLocationDeleteHandler(request, mockLocationId)

      expect(response.status).toBe(204)
      expect(deleteQuery.in).toHaveBeenCalledWith('id', [mockLocationId, 'child-1', 'child-2'])
    })

    it('should handle deletion of location with no children', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock queries
      const locationQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: mockLocationId,
            project_id: mockProjectId
          },
          error: null
        })
      }

      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: mockProjectId, user_id: mockUserId },
          error: null
        })
      }

      const childrenQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: [], // No children
          error: null
        })
      }

      const deleteQuery = {
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: null,
          error: null
        })
      }

      let queryCount = 0
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'locations') {
          queryCount++
          if (queryCount === 1) return locationQuery
          if (queryCount === 2) return childrenQuery
          if (queryCount === 3) return deleteQuery
        }
        if (table === 'projects') return projectQuery
        return null
      })

      const request = new NextRequest(
        `http://localhost/api/projects/${mockProjectId}/locations/${mockLocationId}`,
        {
          method: 'DELETE'
        }
      )

      const response = await mockLocationDeleteHandler(request, mockLocationId)

      expect(response.status).toBe(204)
      expect(deleteQuery.eq).toHaveBeenCalledWith('id', mockLocationId)
    })
  })

  describe('Batch Operations', () => {
    it('should create multiple locations in hierarchy', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const locations = [
        { name: 'Middle Earth', locationType: 'world' },
        { name: 'The Shire', locationType: 'region', parentName: 'Middle Earth' },
        { name: 'Hobbiton', locationType: 'city', parentName: 'The Shire' }
      ]

      // Test batch creation logic
      const createdLocations = []
      const locationMap = new Map()

      for (const location of locations) {
        const projectQuery = {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { id: mockProjectId },
            error: null
          })
        }

        const parentId = location.parentName ? locationMap.get(location.parentName) : null
        const newLocation = {
          id: `loc-${location.name.toLowerCase().replace(' ', '-')}`,
          name: location.name,
          location_type: location.locationType,
          parent_location_id: parentId,
          project_id: mockProjectId
        }

        const locationQuery = {
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: newLocation,
            error: null
          })
        }

        mockSupabase.from.mockImplementation((table) => {
          if (table === 'projects') return projectQuery
          if (table === 'locations') return locationQuery
          return null
        })

        locationMap.set(location.name, newLocation.id)
        createdLocations.push(newLocation)
      }

      // Verify hierarchy
      expect(createdLocations[0].parent_location_id).toBeNull()
      expect(createdLocations[1].parent_location_id).toBe('loc-middle-earth')
      expect(createdLocations[2].parent_location_id).toBe('loc-the-shire')
    })
  })

  describe('Search and Filter Operations', () => {
    it('should filter locations by type', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const mockLocations = [
        { id: '1', name: 'World 1', location_type: 'world' },
        { id: '2', name: 'City 1', location_type: 'city' },
        { id: '3', name: 'City 2', location_type: 'city' },
        { id: '4', name: 'Building 1', location_type: 'building' }
      ]

      const query = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockLocations.filter(l => l.location_type === 'city'),
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(query)

      const request = new NextRequest(
        `http://localhost/api/projects/${mockProjectId}/locations?type=city`
      )
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // This would be implemented in the actual GET handler
      // For now, we're testing the concept
      const filteredData = mockLocations.filter(l => l.location_type === 'city')
      
      expect(filteredData).toHaveLength(2)
      expect(filteredData.every(l => l.location_type === 'city')).toBe(true)
    })

    it('should search locations by name', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const mockLocations = [
        { id: '1', name: 'The Shire', location_type: 'region' },
        { id: '2', name: 'Shire Gardens', location_type: 'building' },
        { id: '3', name: 'Rivendell', location_type: 'city' }
      ]

      // Simulate search functionality
      const searchTerm = 'shire'
      const searchResults = mockLocations.filter(l => 
        l.name.toLowerCase().includes(searchTerm.toLowerCase())
      )

      expect(searchResults).toHaveLength(2)
      expect(searchResults[0].name).toBe('The Shire')
      expect(searchResults[1].name).toBe('Shire Gardens')
    })
  })
})

// Mock handlers for update and delete operations
// In real implementation, these would be separate route files
async function mockLocationUpdateHandler(request: NextRequest, locationId: string) {
  const body = await request.json()
  
  // Simulate UnifiedResponse pattern
  return new Response(JSON.stringify({
    success: true,
    data: {
      location: {
        id: locationId,
        ...body,
        updated_at: new Date().toISOString()
      }
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: '1.0'
    }
  }), { status: 200 })
}

async function mockLocationDeleteHandler(request: NextRequest, locationId: string) {
  // Simulate successful deletion with no content
  return new Response(null, { status: 204 })
}