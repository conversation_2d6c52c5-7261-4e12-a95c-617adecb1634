'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'

interface CreateGoalDialogProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: any) => void
  initialData?: any
}

export function CreateGoalDialog({ open, onClose, onSubmit, initialData }: CreateGoalDialogProps) {
  const [goalType, setGoalType] = useState(initialData?.goal_type || 'daily')
  const [targetWords, setTargetWords] = useState(initialData?.target_words || '')
  const [startDate, setStartDate] = useState<Date | undefined>(
    initialData?.start_date ? new Date(initialData.start_date) : new Date()
  )
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialData?.end_date ? new Date(initialData.end_date) : undefined
  )
  const [projectId, setProjectId] = useState(initialData?.project_id || '')
  const [projects, setProjects] = useState<any[]>([])

  useEffect(() => {
    if (open) {
      fetchProjects()
    }
  }, [open])

  useEffect(() => {
    if (initialData) {
      setGoalType(initialData.goal_type || 'daily')
      setTargetWords(initialData.target_words || '')
      setStartDate(initialData.start_date ? new Date(initialData.start_date) : new Date())
      setEndDate(initialData.end_date ? new Date(initialData.end_date) : undefined)
      setProjectId(initialData.project_id || '')
    }
  }, [initialData])

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (response.ok) {
        const data = await response.json()
        setProjects(data.projects || [])
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    }
  }

  const handleSubmit = () => {
    const data: any = {
      goal_type: goalType,
      target_words: parseInt(targetWords),
      start_date: startDate?.toISOString().split('T')[0]
    }

    if (endDate) {
      data.end_date = endDate.toISOString().split('T')[0]
    }

    if (projectId && goalType === 'project') {
      data.project_id = projectId
    }

    onSubmit(data)
  }

  const getSuggestedTargets = () => {
    switch (goalType) {
      case 'daily':
        return [250, 500, 1000, 1500, 2000]
      case 'weekly':
        return [2500, 5000, 7500, 10000, 15000]
      case 'monthly':
        return [10000, 25000, 50000, 75000, 100000]
      case 'project':
        return [50000, 75000, 100000, 120000, 150000]
      default:
        return [500, 1000, 2000]
    }
  }

  const getGoalDescription = () => {
    switch (goalType) {
      case 'daily':
        return 'Set a daily word count target to maintain consistent writing habits'
      case 'weekly':
        return 'Track your progress over a week with flexible daily scheduling'
      case 'monthly':
        return 'Set ambitious monthly targets for significant progress'
      case 'project':
        return 'Define a total word count goal for your entire project'
      default:
        return ''
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {initialData ? 'Edit Writing Goal' : 'Create Writing Goal'}
          </DialogTitle>
          <DialogDescription>
            {getGoalDescription()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Goal Type */}
          <div className="space-y-2">
            <Label htmlFor="goal-type">Goal Type</Label>
            <Select value={goalType} onValueChange={setGoalType}>
              <SelectTrigger id="goal-type">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily Goal</SelectItem>
                <SelectItem value="weekly">Weekly Goal</SelectItem>
                <SelectItem value="monthly">Monthly Goal</SelectItem>
                <SelectItem value="project">Project Goal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Project Selection (for project goals) */}
          {goalType === 'project' && (
            <div className="space-y-2">
              <Label htmlFor="project">Project</Label>
              <Select value={projectId} onValueChange={setProjectId}>
                <SelectTrigger id="project">
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Target Words */}
          <div className="space-y-2">
            <Label htmlFor="target">Target Words</Label>
            <Input
              id="target"
              type="number"
              value={targetWords}
              onChange={(e) => setTargetWords(e.target.value)}
              placeholder="Enter word count target"
            />
            <div className="flex flex-wrap gap-2 mt-2">
              {getSuggestedTargets().map(target => (
                <Button
                  key={target}
                  variant="outline"
                  size="sm"
                  onClick={() => setTargetWords(target.toString())}
                >
                  {target.toLocaleString()}
                </Button>
              ))}
            </div>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !startDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* End Date (optional) */}
          {goalType !== 'daily' && (
            <div className="space-y-2">
              <Label>End Date (Optional)</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : <span>No deadline</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                    disabled={(date) => date < (startDate || new Date())}
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!targetWords || parseInt(targetWords) <= 0}
          >
            {initialData ? 'Update Goal' : 'Create Goal'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}