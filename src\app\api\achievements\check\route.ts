import { NextRequest, NextResponse } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { requireAuth } from '@/lib/api/auth-middleware'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (authResult instanceof NextResponse) return authResult
    
    const { user } = authResult
    const body = await request.json()
    const { lastCheck } = body
    
    const supabase = await createTypedServerClient()

    // Call the check_and_unlock_achievements function
    const { data, error } = await supabase
      .rpc('check_and_unlock_achievements', { p_user_id: user.id })

    if (error) {
      logger.error('Error checking achievements:', error)
      throw error
    }

    // The RPC function returns an array of rows, each with a newly_unlocked array
    const newlyUnlockedIds = data && data.length > 0 ? data[0].newly_unlocked : []

    // If there are newly unlocked achievements, fetch their details
    let newAchievements = []
    if (newlyUnlockedIds.length > 0) {
      const { data: achievements } = await supabase
        .from('achievements')
        .select('*')
        .in('id', newlyUnlockedIds)

      newAchievements = achievements || []
      
      // Broadcast achievement unlocks
      for (const achievement of newAchievements) {
        await supabase
          .channel('achievement-updates')
          .send({
            type: 'broadcast',
            event: 'new-achievement',
            payload: {
              userId: user.id,
              achievement
            }
          })
      }
    }

    // Also check for achievements unlocked since last check
    if (lastCheck) {
      const { data: recentUnlocks } = await supabase
        .from('user_achievements')
        .select(`
          unlocked_at,
          achievements(*)
        `)
        .eq('user_id', user.id)
        .gte('unlocked_at', lastCheck)
        .order('unlocked_at', { ascending: false })

      const recentAchievements = recentUnlocks?.map(ua => ua.achievements).filter(Boolean) || []
      
      // Combine new and recent achievements
      newAchievements = [...newAchievements, ...recentAchievements.filter(
        ra => !newAchievements.find(na => na.id === ra.id)
      )]
    }

    return NextResponse.json({ 
      newAchievements,
      checked: true 
    })

  } catch (error) {
    logger.error('Achievement check error:', error)
    return NextResponse.json(
      { error: 'Failed to check achievements' },
      { status: 500 }
    )
  }
}