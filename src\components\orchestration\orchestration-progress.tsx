'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  StopCircle, 
  Clock, 
  CheckCircle2, 
  XCircle, 
  Loader2,
  Zap,
  Users,
  BookOpen,
  Search
} from 'lucide-react';

interface OrchestrationProgress {
  totalTasks: number;
  completedTasks: number;
  runningTasks: number;
  failedTasks: number;
  estimatedTimeRemaining: number;
  currentPhase: string;
}

interface OrchestrationProgressProps {
  projectId: string;
  isActive: boolean;
  onComplete?: () => void;
  onCancel?: () => void;
}

export function OrchestrationProgress({
  projectId,
  isActive,
  onComplete,
  onCancel
}: OrchestrationProgressProps) {
  const [progress, setProgress] = useState<OrchestrationProgress | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProgress = useCallback(async () => {
    if (!isActive) return;
    
    try {
      const response = await fetch(`/api/orchestration/progress?projectId=${projectId}`);
      const data = await response.json();
      
      if (data.success) {
        setProgress(data.data);
        setError(null);
        
        // Check if orchestration is complete
        if (data.data.completedTasks + data.data.failedTasks === data.data.totalTasks) {
          onComplete?.();
        }
      } else {
        setError(data.error);
      }
    } catch {
      setError('Failed to fetch progress');
    }
  }, [projectId, isActive, onComplete]);

  useEffect(() => {
    if (!isActive) return;

    // Initial fetch
    fetchProgress();

    // Set up polling
    const interval = setInterval(fetchProgress, 2000); // Poll every 2 seconds

    return () => clearInterval(interval);
  }, [fetchProgress, isActive]);

  const handleCancel = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/orchestration/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId, action: 'cancel' })
      });

      const data = await response.json();
      if (data.success) {
        onCancel?.();
      } else {
        setError(data.error);
      }
    } catch {
      setError('Failed to cancel orchestration');
    } finally {
      setLoading(false);
    }
  };

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'Story Analysis':
        return <BookOpen className="w-4 h-4" />;
      case 'Character Development':
        return <Users className="w-4 h-4" />;
      case 'Chapter Planning':
        return <BookOpen className="w-4 h-4" />;
      case 'Content Generation':
        return <Zap className="w-4 h-4" />;
      case 'Quality Assurance':
        return <Search className="w-4 h-4" />;
      default:
        return <Loader2 className="w-4 h-4 animate-spin" />;
    }
  };


  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!isActive || !progress) {
    return null;
  }

  const progressPercentage = progress.totalTasks > 0 
    ? Math.round(((progress.completedTasks + progress.failedTasks) / progress.totalTasks) * 100)
    : 0;

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getPhaseIcon(progress.currentPhase)}
            <div>
              <CardTitle className="text-lg">AI Orchestration in Progress</CardTitle>
              <CardDescription>{progress.currentPhase}</CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{formatTime(Math.floor(progress.estimatedTimeRemaining / 1000))}</span>
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              disabled={loading}
              className="text-error hover:text-error"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <StopCircle className="w-4 h-4" />
              )}
              Cancel
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <div className="p-4 bg-error-light border border-red-200 rounded-lg">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{progressPercentage}%</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <p className="text-xs text-gray-600">
            {progress.completedTasks} of {progress.totalTasks} tasks completed
          </p>
        </div>

        {/* Task Status Grid */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-success-light rounded-lg">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <CheckCircle2 className="w-4 h-4 text-success" />
              <span className="text-sm font-medium text-green-800">Completed</span>
            </div>
            <div className="text-2xl font-bold text-success">
              {progress.completedTasks}
            </div>
          </div>

          <div className="text-center p-3 bg-info-light rounded-lg">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Loader2 className="w-4 h-4 text-info animate-spin" />
              <span className="text-sm font-medium text-blue-800">Running</span>
            </div>
            <div className="text-2xl font-bold text-info">
              {progress.runningTasks}
            </div>
          </div>

          {progress.failedTasks > 0 && (
            <div className="text-center p-3 bg-error-light rounded-lg">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <XCircle className="w-4 h-4 text-error" />
                <span className="text-sm font-medium text-red-800">Failed</span>
              </div>
              <div className="text-2xl font-bold text-error">
                {progress.failedTasks}
              </div>
            </div>
          )}
        </div>

        {/* Phase Description */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-2">{progress.currentPhase}</h4>
          <p className="text-sm text-gray-600">
            {progress.currentPhase === 'Story Analysis' && 
              'Analyzing story structure, themes, and building the foundational framework for your novel.'}
            {progress.currentPhase === 'Character Development' && 
              'Creating detailed character profiles, relationships, and personality dynamics.'}
            {progress.currentPhase === 'Chapter Planning' && 
              'Structuring your story into chapters with detailed scene planning and pacing.'}
            {progress.currentPhase === 'Content Generation' && 
              'Generating initial content and establishing narrative voice and style.'}
            {progress.currentPhase === 'Quality Assurance' && 
              'Performing consistency checks and ensuring narrative coherence throughout the story.'}
            {progress.currentPhase === 'Processing' && 
              'Finalizing all components and preparing your story framework.'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}