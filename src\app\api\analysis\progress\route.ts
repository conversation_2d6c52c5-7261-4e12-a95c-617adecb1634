import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthorizationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { createTypedServerClient } from '@/lib/supabase'
import { checkProjectAccess } from '@/lib/auth/collaboration-auth'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'

// Validation schema for readability metrics
const readabilityMetricsSchema = z.object({
  projectId: z.string().uuid(),
  metrics: z.object({
    overallScore: z.number().min(0).max(100),
    fleschKincaid: z.number(),
    gunningFog: z.number(),
    colemanLiau: z.number(),
    ari: z.number(),
    sentences: z.number().int().min(0),
    words: z.number().int().min(0),
    syllables: z.number().int().min(0),
    complexWords: z.number().int().min(0),
  }),
  timestamp: z.string().datetime(),
})

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = readabilityMetricsSchema.parse(body)

    // Check if user has access to the project
    const permissions = await checkProjectAccess(authResult.user.id, validatedData.projectId)
    if (!permissions.canEdit) {
      return handleAPIError(new AuthorizationError())
    }

    // Store the readability metrics
    const supabase = await createTypedServerClient()
    const { error } = await supabase
      .from('readability_progress')
      .insert({
        project_id: validatedData.projectId,
        user_id: authResult.user.id,
        overall_score: validatedData.metrics.overallScore,
        flesch_kincaid: validatedData.metrics.fleschKincaid,
        gunning_fog: validatedData.metrics.gunningFog,
        coleman_liau: validatedData.metrics.colemanLiau,
        ari: validatedData.metrics.ari,
        sentences: validatedData.metrics.sentences,
        words: validatedData.metrics.words,
        syllables: validatedData.metrics.syllables,
        complex_words: validatedData.metrics.complexWords,
        tracked_at: validatedData.timestamp,
      })

    if (error) {
      logger.error('Failed to store readability progress', error)
      return NextResponse.json(
        { error: 'Failed to save progress data' },
        { status: 500 }
      )
    }

    logger.info('Readability progress tracked', {
      projectId: validatedData.projectId,
      userId: authResult.user.id,
      score: validatedData.metrics.overallScore,
    })

    return NextResponse.json({
      success: true,
      message: 'Progress tracked successfully',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    logger.error('Error tracking readability progress', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    // Get project ID from query params
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    if (!projectId) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Check if user has access to the project
    const permissions = await checkProjectAccess(authResult.user.id, projectId)
    if (!permissions.canView) {
      return handleAPIError(new AuthorizationError())
    }

    // Fetch readability progress history
    const supabase = await createTypedServerClient()
    const { data, error } = await supabase
      .from('readability_progress')
      .select('*')
      .eq('project_id', projectId)
      .order('tracked_at', { ascending: false })
      .limit(30) // Last 30 data points

    if (error) {
      logger.error('Failed to fetch readability progress', error)
      return NextResponse.json(
        { error: 'Failed to fetch progress data' },
        { status: 500 }
      )
    }

    // Transform data for frontend consumption
    const progressData = data?.map(item => ({
      id: item.id,
      timestamp: item.tracked_at,
      overallScore: item.overall_score,
      metrics: {
        fleschKincaid: item.flesch_kincaid,
        gunningFog: item.gunning_fog,
        colemanLiau: item.coleman_liau,
        ari: item.ari,
        sentences: item.sentences,
        words: item.words,
        syllables: item.syllables,
        complexWords: item.complex_words,
      },
    })) || []

    return NextResponse.json({
      success: true,
      data: progressData,
    })
  } catch (error) {
    logger.error('Error fetching readability progress', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}