import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, AuthorizationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { z } from 'zod'
<<<<<<< HEAD
import { createServerClient } from '@/lib/supabase'
import { mailerooEmailService, EmailTemplates, type EmailTemplate } from '@/lib/services/email/maileroo-email-service'
=======
import { createTypedServerClient } from '@/lib/supabase'
import { emailService } from '@/lib/email/service'
>>>>>>> dd974a56edaaeb3bb72610137e1e155511ba61d9
import { logger } from '@/lib/services/logger'
import { globalLimiter } from '@/lib/rate-limiter-unified'
import { RATE_LIMITS } from '@/lib/constants'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

// Validation schema
const sendEmailSchema = z.object({
  template: z.enum([
    EmailTemplates.WELCOME,
    EmailTemplates.ACHIEVEMENT_UNLOCKED,
    EmailTemplates.GOAL_REMINDER,
    EmailTemplates.COLLABORATION_INVITE,
    EmailTemplates.EXPORT_READY,
    EmailTemplates.SUBSCRIPTION_RENEWED,
    EmailTemplates.SUBSCRIPTION_CANCELLED,
    EmailTemplates.PASSWORD_RESET,
    EmailTemplates.EMAIL_VERIFICATION,
    EmailTemplates.WEEKLY_PROGRESS,
    EmailTemplates.PROJECT_SHARED,
    EmailTemplates.COMMENT_NOTIFICATION,
    EmailTemplates.ACCOUNT_DELETED
  ] as const),
  to: z.union([z.string().email(), z.array(z.string().email())]),
  data: z.record(z.unknown()),
  scheduledFor: z.string().datetime().optional(),
  userId: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.headers.get('x-forwarded-for') ?? request.headers.get('x-real-ip') ?? 'unknown'
    try {
      await globalLimiter.check(RATE_LIMITS.SERVICE_ORCHESTRATOR_WRITE, ip) // 10 emails per minute
    } catch {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    // Authentication
    const supabase = await createTypedServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Check if user is admin for certain email types
    const adminOnlyTemplates = [EmailTemplates.WEEKLY_PROGRESS] // These are sent by system
    const body = await request.json()
    
    // Validate request
    const validationResult = sendEmailSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }
    
    const { template, to, data, scheduledFor, userId } = validationResult.data

    // Check permissions for admin-only templates
    if (adminOnlyTemplates.includes(template)) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single()
      
      if (!profile?.is_admin) {
        return handleAPIError(new AuthorizationError())
      }
    }

    // Check email preferences
    const recipients = Array.isArray(to) ? to : [to]
    const filteredRecipients: string[] = []
    
    for (const recipient of recipients) {
      // Get user ID from email
      const { data: recipientProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', recipient)
        .single()
      
      if (recipientProfile) {
        // Email preferences are now handled internally by mailerooEmailService
        // The service will check preferences when sending
        filteredRecipients.push(recipient)
      } else {
        // Non-user emails (like invites) always go through
        filteredRecipients.push(recipient)
      }
    }

    if (filteredRecipients.length === 0) {
      return NextResponse.json({ 
        message: 'No recipients after preference filtering',
        sent: 0 
      })
    }

    // Send or queue email
    try {
      if (filteredRecipients.length === 1) {
        // Single recipient
        await mailerooEmailService.sendEmail(
          filteredRecipients[0],
          template as EmailTemplate,
          data,
          {
            scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined,
            userId: userId || user.id
          }
        )
      } else {
        // Multiple recipients - use bulk send
        const recipientsWithData = filteredRecipients.map(email => ({
          email,
          data: data as any
        }))
        
        await mailerooEmailService.sendBulkEmail(
          recipientsWithData,
          template as EmailTemplate,
          {
            scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined,
            batchSize: 50
          }
        )
      }
      
      return NextResponse.json({ 
        message: scheduledFor ? 'Email queued successfully' : 'Email sent successfully',
        recipients: filteredRecipients.length
      })
    } catch (error) {
      logger.error('Email send error:', error)
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      )
    }

  } catch (error) {
    logger.error('Email send error:', error)
    return NextResponse.json(
      { error: 'Failed to process email request' },
      { status: 500 }
    )
  }
}