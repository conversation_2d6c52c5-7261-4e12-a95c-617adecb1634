'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Calendar, Clock, FileText, Users, MapPin } from 'lucide-react'
import Link from 'next/link'
import type { Series } from '@/lib/db/types'
import { TIME_MS } from '@/lib/constants'

interface SeriesTimelineProps {
  series: Array<Series & {
    projects?: Array<{
      project: {
        id: string
        title: string
        status: string
        timeline_start?: string
        timeline_end?: string
        setting?: string
        created_at: string
        current_word_count: number
      }
    }>
  }>
}

export function SeriesTimeline({ series }: SeriesTimelineProps) {
  // Collect all projects with timeline data
  const timelineEvents = series.flatMap(s => 
    (s.projects || []).map(p => ({
      ...p.project,
      seriesTitle: s.title,
      seriesId: s.id
    }))
  ).filter(p => p.timeline_start || p.created_at)
  .sort((a, b) => {
    const dateA = new Date(a.timeline_start || a.created_at)
    const dateB = new Date(b.timeline_start || b.created_at)
    return dateA.getTime() - dateB.getTime()
  })

  if (timelineEvents.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Timeline Data</h3>
        <p className="text-muted-foreground">
          Add timeline information to your books to see them here
        </p>
      </div>
    )
  }

  // Group events by year
  const eventsByYear = timelineEvents.reduce((acc, event) => {
    const year = new Date(event.timeline_start || event.created_at).getFullYear()
    if (!acc[year]) acc[year] = []
    acc[year].push(event)
    return acc
  }, {} as Record<number, typeof timelineEvents>)

  return (
    <ScrollArea className="w-full">
      <div className="p-6 min-w-[800px]">
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-border" />

          {/* Timeline events */}
          <div className="space-y-8">
            {Object.entries(eventsByYear).map(([year, events]) => (
              <div key={year} className="relative">
                {/* Year marker */}
                <div className="absolute -left-1 top-0 w-16 h-16 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold">
                  {year}
                </div>
                
                {/* Events for this year */}
                <div className="ml-24 space-y-4">
                  {events.map((event) => (
                    <Card key={event.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-lg">
                              <Link 
                                href={`/projects/${event.id}`}
                                className="hover:text-primary transition-colors"
                              >
                                {event.title}
                              </Link>
                            </CardTitle>
                            <CardDescription className="flex items-center gap-2 mt-1">
                              <Link 
                                href={`/series/${event.seriesId}`}
                                className="hover:text-primary transition-colors"
                              >
                                {event.seriesTitle}
                              </Link>
                              <span>•</span>
                              <Badge variant="outline" className="text-xs">
                                {event.status}
                              </Badge>
                            </CardDescription>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-muted-foreground">
                              {new Date(event.timeline_start || event.created_at).toLocaleDateString()}
                              {event.timeline_end && (
                                <> - {new Date(event.timeline_end).toLocaleDateString()}</>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {Math.round(event.current_word_count / TIME_MS.SECOND)}k words
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      {(event.setting || event.timeline_start) && (
                        <CardContent className="pt-0">
                          <div className="flex flex-wrap gap-4 sm:gap-5 lg:gap-6 text-sm">
                            {event.setting && (
                              <div className="flex items-center gap-1 text-muted-foreground">
                                <MapPin className="h-3 w-3" />
                                {event.setting}
                              </div>
                            )}
                            {event.timeline_start && (
                              <div className="flex items-center gap-1 text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                Story timeline
                              </div>
                            )}
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  )
}