/**
 * Type definitions for React hooks
 */

import type { StoryBible } from '@/lib/db/types'
import type { CharacterEntry, LocationEntry, WorldBuildingEntry, TimelineEntry, PlotDeviceEntry } from './editor'

// Story Bible Hook Types
export interface CharacterInput {
  name: string
  role: string
  description: string
  backstory?: string
  traits: string[]
  relationships?: Array<{
    characterId: string
    relationship: string
    description: string
  }>
}

export interface LocationInput {
  name: string
  description: string
  significance?: string
  geography?: string
  culture?: string
}

export interface WorldRuleInput {
  category: string
  rule: string
  explanation: string
  implications?: string[]
}

export interface TimelineEventInput {
  event: string
  date: string
  description?: string
  chapter?: number
}

export interface PlotThreadInput {
  description: string
  status?: 'active' | 'resolved'
}

export interface StoryBibleData {
  characters: Map<string, CharacterEntry>
  locations: Map<string, LocationEntry>
  worldRules: Map<string, WorldBuildingEntry>
  timeline: TimelineEntry[]
  plotThreads: Array<{ id: string; description: string; status: 'active' | 'resolved' }>
}

export interface UseStoryBibleReturn {
  storyBible: StoryBibleData
  isLoading: boolean
  error?: string
  loadStoryBible: () => Promise<void>
  addCharacter: (character: CharacterInput) => Promise<boolean>
  updateCharacter: (id: string, character: CharacterInput) => Promise<boolean>
  deleteCharacter: (id: string) => Promise<boolean>
  addLocation: (location: LocationInput) => Promise<boolean>
  updateLocation: (id: string, location: LocationInput) => Promise<boolean>
  deleteLocation: (id: string) => Promise<boolean>
  addWorldRule: (key: string, value: WorldRuleInput) => Promise<boolean>
  updateWorldRule: (key: string, value: WorldRuleInput) => Promise<boolean>
  deleteWorldRule: (key: string) => Promise<boolean>
  addTimelineEvent: (event: TimelineEventInput) => Promise<boolean>
  updateTimelineEvent: (id: string, event: TimelineEventInput) => Promise<boolean>
  deleteTimelineEvent: (id: string) => Promise<boolean>
  addPlotThread: (thread: PlotThreadInput) => Promise<boolean>
  updatePlotThread: (id: string, thread: PlotThreadInput) => Promise<boolean>
  deletePlotThread: (id: string) => Promise<boolean>
}

// Analytics Hook Types
export interface UseAnalyticsReturn {
  data: {
    sessions: WritingSession[]
    overview: AnalyticsOverview
    patterns: WritingPatterns
  }
  isLoading: boolean
  error?: string
  refresh: () => Promise<void>
  dateRange: DateRange
  setDateRange: (range: DateRange) => void
}

export interface AnalyticsOverview {
  totalWords: number
  totalSessions: number
  totalDuration: number
  avgSessionDuration: number
  avgWordsPerSession: number
  avgWordsPerMinute: number
  streak: number
  bestDay: { date: string; words: number }
}

export interface WritingPatterns {
  hourly: Array<{ hour: number; avgWords: number; sessions: number }>
  daily: Array<{ day: string; words: number; sessions: number }>
  weekly: Array<{ week: string; words: number; sessions: number }>
}

export interface DateRange {
  from: Date
  to: Date
  preset?: 'today' | 'week' | 'month' | 'year' | 'all'
}

// Collaboration Hook Types
export interface UseCollaborationReturn {
  isConnected: boolean
  activeUsers: CollaborationUser[]
  sendChange: (change: TextChange) => void
  sendCursorPosition: (position: TextPosition) => void
  sendSelection: (selection: TextSelection) => void
  error?: string
  reconnect: () => void
  disconnect: () => void
}

export interface CollaborationUser {
  id: string
  name: string
  email: string
  role: 'owner' | 'editor' | 'viewer'
  color: string
  cursor?: TextPosition
  selection?: TextSelection
  isActive: boolean
  lastActivity: Date
}

export interface TextPosition {
  line: number
  column: number
}

export interface TextChange {
  from: TextPosition
  to: TextPosition
  text: string
  userId: string
  timestamp: number
}

export interface TextSelection {
  start: TextPosition
  end: TextPosition
  userId: string
  timestamp: number
}

// Form Validation Hook Types
export interface UseFormValidationReturn<T> {
  values: T
  errors: Partial<Record<keyof T, string>>
  touched: Partial<Record<keyof T, boolean>>
  isValid: boolean
  isSubmitting: boolean
  handleChange: <K extends keyof T>(field: K, value: T[K]) => void
  handleBlur: (field: keyof T) => void
  handleSubmit: (onSubmit: (values: T) => Promise<void>) => Promise<void>
  reset: () => void
  setFieldError: (field: keyof T, error: string) => void
  setFieldValue: <K extends keyof T>(field: K, value: T[K]) => void
}

// Achievement Tracker Hook Types
export interface UseAchievementTrackerReturn {
  trackWordCount: (wordCount: number) => void
  trackChapterCompletion: (chapterId: string) => void
  trackWritingSession: (duration: number, wordCount: number) => void
  achievements: Achievement[]
  recentActivity: Activity[]
  stats: WritingStats
}

export interface Achievement {
  id: string
  type: 'word_count' | 'chapter_count' | 'streak' | 'quality' | 'speed'
  title: string
  description: string
  progress: number
  target: number
  completed: boolean
  unlockedAt?: Date
  icon: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export interface Activity {
  id: string
  type: string
  description: string
  timestamp: Date
  metadata?: Record<string, unknown>
}

export interface WritingStats {
  totalWords: number
  totalChapters: number
  currentStreak: number
  bestStreak: number
  avgWordsPerDay: number
  avgWordsPerSession: number
}

// Writing Session Types
export interface WritingSession {
  id: string
  projectId: string
  userId: string
  startTime: Date
  endTime?: Date
  wordCount: number
  wordsWritten: number
  wordsDeleted: number
  duration: number
  chapterId?: string
  characterCount: number
  paragraphCount: number
  sentenceCount: number
  avgWordsPerMinute: number
}