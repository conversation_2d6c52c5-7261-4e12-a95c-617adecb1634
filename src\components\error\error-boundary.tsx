'use client'

import { Component } from 'react'
import { logger } from '@/lib/services/logger';

import type { ReactNode, ErrorInfo, ComponentType } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertTriangle, RefreshCw, Home, Bug, Mail } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { config } from '@/lib/config'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showErrorDetails?: boolean
  resetKeys?: Array<string | number>
  resetOnPropsChange?: boolean
  isolate?: boolean
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  eventId: string | null
}

export class ErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      eventId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // Log error to console in development
    if (config.isDevelopment) {
      logger.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // Call custom error handler
    this.props.onError?.(error, errorInfo)

    // Report error to external service (in production)
    this.reportError(error, errorInfo)

    // Show toast notification for non-isolated errors
    if (!this.props.isolate) {
      toast({
        variant: "destructive",
        title: "Something went wrong",
        description: "An unexpected error occurred. We've been notified and are working on a fix."
      })
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetKeys, resetOnPropsChange } = this.props
    const { hasError } = this.state

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetKeys?.some((key, idx) => prevProps.resetKeys?.[idx] !== key)) {
        this.resetErrorBoundary()
      }
    }

    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetErrorBoundary()
    }
  }

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      window.clearTimeout(this.resetTimeoutId)
    }

    this.resetTimeoutId = window.setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        eventId: null
      })
    }, 100)
  }

  private reportError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // In production, send to error tracking service
      if (config.isProduction) {
        await fetch('/api/errors/report', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            eventId: this.state.eventId
          })
        })
      }
    } catch (reportError) {
      logger.error('Failed to report error:', reportError);
    }
  }

  private copyErrorDetails = () => {
    const { error, errorInfo, eventId } = this.state
    const errorDetails = {
      eventId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href
    }

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
    toast({
      title: "Error details copied",
      description: "Error information has been copied to your clipboard."
    })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      const { error, errorInfo, eventId } = this.state
      const { showErrorDetails = config.isDevelopment, isolate } = this.props

      return (
        <div className={`${isolate ? '' : 'min-h-screen'} flex items-center justify-center bg-background p-4`}>
          <Card className="w-full max-w-2xl lg:max-w-3xl xl:max-w-4xl">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-error-light rounded-full w-fit">
                <AlertTriangle className="h-6 w-6 text-error" />
              </div>
              <CardTitle>Something went wrong</CardTitle>
              <CardDescription>
                We encountered an unexpected error. Our team has been notified.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {eventId && (
                <Alert>
                  <Bug className="h-4 w-4" />
                  <AlertTitle>Error Reference</AlertTitle>
                  <AlertDescription>
                    Error ID: <code className="text-xs bg-muted px-1 rounded">{eventId}</code>
                  </AlertDescription>
                </Alert>
              )}

              {showErrorDetails && error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Technical Details</AlertTitle>
                  <AlertDescription>
                    <div className="mt-2 space-y-2">
                      <div>
                        <strong>Error:</strong> {error.message}
                      </div>
                      {error.stack && (
                        <details className="text-xs">
                          <summary className="cursor-pointer font-medium">Stack Trace</summary>
                          <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                            {error.stack}
                          </pre>
                        </details>
                      )}
                      {errorInfo?.componentStack && (
                        <details className="text-xs">
                          <summary className="cursor-pointer font-medium">Component Stack</summary>
                          <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                            {errorInfo.componentStack}
                          </pre>
                        </details>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex flex-wrap gap-2">
                <Button 
                  onClick={this.resetErrorBoundary}
                  className="flex-1"
                  variant="outline"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                
                {!isolate && (
                  <Button 
                    onClick={() => window.location.href = '/dashboard'}
                    className="flex-1"
                  >
                    <Home className="h-4 w-4 mr-2" />
                    Go to Dashboard
                  </Button>
                )}

                {showErrorDetails && (
                  <Button 
                    onClick={this.copyErrorDetails}
                    variant="outline"
                    size="sm"
                  >
                    Copy Details
                  </Button>
                )}
              </div>

              <div className="text-center space-y-2">
                <p className="text-xs text-muted-foreground">
                  If this problem persists, please contact support with the error ID above.
                </p>
                <Button 
                  variant="link" 
                  size="sm"
                  onClick={() => window.open('mailto:<EMAIL>?subject=Error%20Report&body=Error%20ID:%20' + eventId)}
                >
                  <Mail className="h-3 w-3 mr-1" />
                  Contact Support
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// HOC for easier usage
export function withErrorBoundary<T extends object>(
  Component: ComponentType<T>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: T) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  return WrappedComponent
}