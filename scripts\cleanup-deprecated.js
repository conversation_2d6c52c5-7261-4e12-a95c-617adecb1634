#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files identified as safe to remove
const filesToRemove = [
  'src/lib/api/middleware.ts',
  'src/lib/api/auth-helpers.ts',
  'src/lib/api/auth-middleware.ts',
  'src/lib/api/response-utils.ts',
  'src/lib/api/add-auth-to-routes.ts',
  'src/lib/auth/index.ts',
  'src/lib/auth/server.ts',
  'src/lib/auth/admin.ts',
  'src/lib/auth/validation.ts',
  'src/lib/themes/theme-registry.ts',
];

console.log('Removing deprecated files...\n');

let removedCount = 0;
let errorCount = 0;

for (const file of filesToRemove) {
  const fullPath = path.join(process.cwd(), file);
  
  try {
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      console.log(`✅ Removed: ${file}`);
      removedCount++;
    } else {
      console.log(`⚪ Already gone: ${file}`);
    }
  } catch (error) {
    console.error(`❌ Error removing ${file}:`, error.message);
    errorCount++;
  }
}

console.log(`\n=== Summary ===`);
console.log(`Successfully removed: ${removedCount} files`);
console.log(`Errors: ${errorCount}`);

if (removedCount > 0) {
  console.log('\n✅ Deprecated files have been cleaned up successfully!');
}