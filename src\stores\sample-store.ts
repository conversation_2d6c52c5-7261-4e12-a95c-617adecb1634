import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { logger } from '@/lib/services/logger'

interface SampleStore {
  items: string[]
  loading: boolean

  // Actions
  setItems: (items: string[]) => void
  setLoading: (loading: boolean) => void
  addItem: () => void
  removeItem: () => void
  fetchItems: () => Promise<void>
}

export const useSampleStore = create<SampleStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        items: [],
        loading: false,

        setItems: (items) => set({ items }),
        setLoading: (loading) => set({ loading }),
        addItem: () => {
          // Implement addItem
        },
        removeItem: () => {
          // Implement removeItem
        },
        fetchItems: async () => {
          set({ loading: true });
          try {
            // Async logic here
          } catch (error) {
            logger.error(error);
          } finally {
            set({ loading: false });
          }
        },
      })),
      {
        name: 'sample-storage',
      }
    ),
    {
      name: 'SampleStore'
    }
  )
)

// Selectors for better performance
export const useSampleSelectors = {
  getItems: (state: SampleStore) => state.items
};
