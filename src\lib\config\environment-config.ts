import { z } from 'zod'

// Rate limiting configuration
export const RATE_LIMITS = {
  // API rate limits per hour
  API: {
    DEFAULT: parseInt(process.env.API_RATE_LIMIT_DEFAULT || '100'),
    AUTHENTICATED: parseInt(process.env.API_RATE_LIMIT_AUTH || '1000'),
    AI_GENERATION: parseInt(process.env.API_RATE_LIMIT_AI || '30'),
    AI_ANALYSIS: parseInt(process.env.API_RATE_LIMIT_ANALYSIS || '10'),
    WEBHOOKS: parseInt(process.env.API_RATE_LIMIT_WEBHOOKS || '500'),
  },
  
  // Window duration in milliseconds
  WINDOW: {
    DEFAULT: parseInt(process.env.RATE_LIMIT_WINDOW || '3600000'), // 1 hour
    AI: parseInt(process.env.RATE_LIMIT_WINDOW_AI || '3600000'), // 1 hour
  },
  
  // Burst limits (short-term)
  BURST: {
    DEFAULT: parseInt(process.env.BURST_LIMIT_DEFAULT || '20'),
    AI: parseInt(process.env.BURST_LIMIT_AI || '5'),
    WINDOW: parseInt(process.env.BURST_WINDOW || '60000'), // 1 minute
  }
}

// AI Model configuration
export const AI_CONFIG = {
  // Token limits
  TOKEN_LIMITS: {
    CHAPTER: parseInt(process.env.AI_TOKEN_LIMIT_CHAPTER || '10000'),
    SCENE: parseInt(process.env.AI_TOKEN_LIMIT_SCENE || '8000'),
    CHARACTER: parseInt(process.env.AI_TOKEN_LIMIT_CHARACTER || '6000'),
    DIALOGUE: parseInt(process.env.AI_TOKEN_LIMIT_DIALOGUE || '4000'),
    SUMMARY: parseInt(process.env.AI_TOKEN_LIMIT_SUMMARY || '2000'),
    DEFAULT: parseInt(process.env.AI_TOKEN_LIMIT_DEFAULT || '4000'),
  },
  
  // Quality thresholds
  QUALITY: {
    EXCELLENT: parseInt(process.env.AI_QUALITY_EXCELLENT || '95'),
    GOOD: parseInt(process.env.AI_QUALITY_GOOD || '90'),
    ACCEPTABLE: parseInt(process.env.AI_QUALITY_ACCEPTABLE || '85'),
    NEEDS_IMPROVEMENT: parseInt(process.env.AI_QUALITY_POOR || '70'),
    UNACCEPTABLE: parseInt(process.env.AI_QUALITY_UNACCEPTABLE || '50'),
    
    // Minimum scores by content type
    MINIMUM: {
      CHAPTER: parseInt(process.env.AI_MIN_QUALITY_CHAPTER || '85'),
      CHARACTER: parseInt(process.env.AI_MIN_QUALITY_CHARACTER || '90'),
      DIALOGUE: parseInt(process.env.AI_MIN_QUALITY_DIALOGUE || '88'),
      SCENE: parseInt(process.env.AI_MIN_QUALITY_SCENE || '87'),
      PLOT: parseInt(process.env.AI_MIN_QUALITY_PLOT || '92'),
    }
  },
  
  // Model selection
  MODELS: {
    PRIMARY: process.env.AI_MODEL_PRIMARY || 'gpt-4-turbo-preview',
    SECONDARY: process.env.AI_MODEL_SECONDARY || 'gpt-4',
    FAST: process.env.AI_MODEL_FAST || 'gpt-3.5-turbo',
    EMBEDDING: process.env.AI_MODEL_EMBEDDING || 'text-embedding-3-small',
  },
  
  // Temperature settings
  TEMPERATURE: {
    CREATIVE: parseFloat(process.env.AI_TEMP_CREATIVE || '0.9'),
    BALANCED: parseFloat(process.env.AI_TEMP_BALANCED || '0.7'),
    FOCUSED: parseFloat(process.env.AI_TEMP_FOCUSED || '0.5'),
    ANALYTICAL: parseFloat(process.env.AI_TEMP_ANALYTICAL || '0.3'),
    DETERMINISTIC: parseFloat(process.env.AI_TEMP_DETERMINISTIC || '0.1'),
  },
  
  // Retry configuration
  RETRY: {
    MAX_ATTEMPTS: parseInt(process.env.AI_RETRY_MAX || '3'),
    INITIAL_DELAY: parseInt(process.env.AI_RETRY_DELAY || '1000'),
    MAX_DELAY: parseInt(process.env.AI_RETRY_MAX_DELAY || '30000'),
    BACKOFF_MULTIPLIER: parseFloat(process.env.AI_RETRY_BACKOFF || '2'),
  }
}

// API URLs configuration
export const API_URLS = {
  // External APIs
  EXTERNAL: {
    OPENAI: process.env.OPENAI_API_URL || 'https://api.openai.com/v1',
    GEMINI: process.env.GEMINI_API_URL || 'https://generativelanguage.googleapis.com/v1',
    XAI: process.env.XAI_API_URL || 'https://api.x.ai/v1',
    STRIPE: process.env.STRIPE_API_URL || 'https://api.stripe.com/v1',
    ZERUH: process.env.ZERUH_API_URL || 'https://api.zeruh.com/v1',
    SENDGRID: process.env.SENDGRID_API_URL || 'https://api.sendgrid.com/v3',
    RESEND: process.env.RESEND_API_URL || 'https://api.resend.com',
    MAILEROO: process.env.MAILEROO_API_URL || 'https://smtp.maileroo.com/v1',
    DICEBEAR: process.env.DICEBEAR_API_URL || 'https://api.dicebear.com/7.x',
  },
  
  // Internal APIs
  INTERNAL: {
    APP: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    API: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
    WEBSOCKET: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8080',
  },
  
  // Service URLs
  SERVICES: {
    COLLABORATION: process.env.COLLAB_SERVICE_URL || 'http://localhost:8081',
    ANALYTICS: process.env.ANALYTICS_SERVICE_URL || 'http://localhost:8082',
    EXPORT: process.env.EXPORT_SERVICE_URL || 'http://localhost:8083',
  },
  
  // Brand & Marketing URLs
  BRAND: {
    MARKETING: process.env.NEXT_PUBLIC_MARKETING_URL || 'https://bookscribe.ai',
    DOCS: process.env.NEXT_PUBLIC_DOCS_URL || 'https://docs.bookscribe.ai',
    FEEDBACK: process.env.NEXT_PUBLIC_FEEDBACK_URL || 'https://feedback.bookscribe.ai',
    BLOG: process.env.NEXT_PUBLIC_BLOG_URL || 'https://blog.bookscribe.ai',
    DISCORD: process.env.NEXT_PUBLIC_DISCORD_URL || 'https://discord.gg/bookscribe',
    TWITTER: process.env.NEXT_PUBLIC_TWITTER_URL || 'https://twitter.com/bookscribeai',
    YOUTUBE: process.env.NEXT_PUBLIC_YOUTUBE_URL || 'https://youtube.com/@bookscribeai',
  },
  
  // Setup & Dashboard URLs
  SETUP: {
    OPENAI: process.env.OPENAI_SETUP_URL || 'https://platform.openai.com/api-keys',
    STRIPE: process.env.STRIPE_SETUP_URL || 'https://dashboard.stripe.com',
    SUPABASE: process.env.SUPABASE_SETUP_URL || 'https://supabase.com/dashboard',
  }
}

// Cache configuration
export const CACHE_CONFIG = {
  // TTL in seconds
  TTL: {
    DEFAULT: parseInt(process.env.CACHE_TTL_DEFAULT || '3600'), // 1 hour
    USER_DATA: parseInt(process.env.CACHE_TTL_USER || '300'), // 5 minutes
    PROJECT_DATA: parseInt(process.env.CACHE_TTL_PROJECT || '600'), // 10 minutes
    AI_RESPONSE: parseInt(process.env.CACHE_TTL_AI || '86400'), // 24 hours
    STATIC_CONTENT: parseInt(process.env.CACHE_TTL_STATIC || '604800'), // 7 days
  },
  
  // Cache size limits
  MAX_SIZE: {
    MEMORY: parseInt(process.env.CACHE_MAX_MEMORY || '104857600'), // 100MB
    ITEMS: parseInt(process.env.CACHE_MAX_ITEMS || '1000'),
  },
  
  // Cache keys
  KEYS: {
    PREFIX: process.env.CACHE_KEY_PREFIX || 'bookscribe',
    VERSION: process.env.CACHE_KEY_VERSION || 'v1',
  }
}

// Performance configuration
export const PERFORMANCE_CONFIG = {
  // Timeouts in milliseconds
  TIMEOUTS: {
    API_DEFAULT: parseInt(process.env.TIMEOUT_API_DEFAULT || '30000'), // 30s
    AI_GENERATION: parseInt(process.env.TIMEOUT_AI_GENERATION || '120000'), // 2min
    FILE_UPLOAD: parseInt(process.env.TIMEOUT_FILE_UPLOAD || '300000'), // 5min
    WEBSOCKET: parseInt(process.env.TIMEOUT_WEBSOCKET || '60000'), // 1min
  },
  
  // Batch processing
  BATCH: {
    SIZE: parseInt(process.env.BATCH_SIZE_DEFAULT || '10'),
    AI_REQUESTS: parseInt(process.env.BATCH_SIZE_AI || '3'),
    DB_OPERATIONS: parseInt(process.env.BATCH_SIZE_DB || '100'),
  },
  
  // Concurrency limits
  CONCURRENCY: {
    AI_AGENTS: parseInt(process.env.CONCURRENCY_AI_AGENTS || '3'),
    FILE_UPLOADS: parseInt(process.env.CONCURRENCY_UPLOADS || '5'),
    EXPORTS: parseInt(process.env.CONCURRENCY_EXPORTS || '2'),
  }
}

// Security configuration
export const SECURITY_CONFIG = {
  // Session configuration
  SESSION: {
    DURATION: parseInt(process.env.SESSION_DURATION || '86400000'), // 24 hours
    REFRESH_THRESHOLD: parseInt(process.env.SESSION_REFRESH || '3600000'), // 1 hour
    MAX_AGE: parseInt(process.env.SESSION_MAX_AGE || '604800000'), // 7 days
  },
  
  // API Security
  API: {
    REQUIRE_AUTH: process.env.API_REQUIRE_AUTH !== 'false',
    VALIDATE_ORIGIN: process.env.API_VALIDATE_ORIGIN !== 'false',
    ALLOWED_ORIGINS: (process.env.API_ALLOWED_ORIGINS || 'http://localhost:3000').split(','),
  },
  
  // Content Security
  CONTENT: {
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || '52428800'), // 50MB
    ALLOWED_FILE_TYPES: (process.env.ALLOWED_FILE_TYPES || '.txt,.doc,.docx,.md,.json').split(','),
    SANITIZE_HTML: process.env.SANITIZE_HTML !== 'false',
  }
}

// Feature flags
export const FEATURE_FLAGS = {
  // Core features
  REALTIME_COLLABORATION: process.env.FEATURE_REALTIME_COLLAB === 'true',
  AI_AGENTS: process.env.FEATURE_AI_AGENTS !== 'false',
  EXPORT_FORMATS: process.env.FEATURE_EXPORT_FORMATS !== 'false',
  ANALYTICS: process.env.FEATURE_ANALYTICS === 'true',
  
  // Experimental features
  EXPERIMENTAL: {
    VOICE_NARRATION: process.env.FEATURE_VOICE_NARRATION === 'true',
    AI_ILLUSTRATIONS: process.env.FEATURE_AI_ILLUSTRATIONS === 'true',
    MULTIPLAYER_EDITING: process.env.FEATURE_MULTIPLAYER === 'true',
    PLUGIN_SYSTEM: process.env.FEATURE_PLUGINS === 'true',
  },
  
  // Development features
  DEV: {
    DEBUG_MODE: process.env.DEBUG_MODE === 'true',
    SHOW_ERRORS: process.env.SHOW_ERRORS === 'true',
    MOCK_DATA: process.env.USE_MOCK_DATA === 'true',
    HOT_RELOAD: process.env.HOT_RELOAD !== 'false',
  }
}

// Monitoring configuration
export const MONITORING_CONFIG = {
  // Error tracking
  SENTRY: {
    ENABLED: process.env.SENTRY_ENABLED === 'true',
    DSN: process.env.SENTRY_DSN || '',
    ENVIRONMENT: process.env.SENTRY_ENVIRONMENT || 'development',
    SAMPLE_RATE: parseFloat(process.env.SENTRY_SAMPLE_RATE || '1.0'),
  },
  
  // Analytics
  ANALYTICS: {
    ENABLED: process.env.ANALYTICS_ENABLED === 'true',
    PROVIDER: process.env.ANALYTICS_PROVIDER || 'posthog',
    API_KEY: process.env.ANALYTICS_API_KEY || '',
  },
  
  // Logging
  LOGGING: {
    LEVEL: process.env.LOG_LEVEL || 'info',
    FORMAT: process.env.LOG_FORMAT || 'json',
    DESTINATION: process.env.LOG_DESTINATION || 'console',
  }
}

// Email configuration
export const EMAIL_CONFIG = {
  // Email addresses
  ADDRESSES: {
    FROM: process.env.EMAIL_FROM || '<EMAIL>',
    SUPPORT: process.env.EMAIL_SUPPORT || '<EMAIL>',
    ADMIN: process.env.EMAIL_ADMIN || '<EMAIL>',
    TEST: process.env.EMAIL_TEST || '<EMAIL>',
  },
  
  // Email service
  SERVICE: {
    PROVIDER: process.env.EMAIL_PROVIDER || 'resend',
    API_KEY: process.env.EMAIL_API_KEY || '',
    DOMAIN: process.env.EMAIL_DOMAIN || 'bookscribe.ai',
  },
  
  // Email settings
  SETTINGS: {
    BATCH_SIZE: parseInt(process.env.EMAIL_BATCH_SIZE || '100'),
    RATE_LIMIT: parseInt(process.env.EMAIL_RATE_LIMIT || '100'),
    RETRY_ATTEMPTS: parseInt(process.env.EMAIL_RETRY || '3'),
  }
}

// Collaboration configuration
export const COLLABORATION_CONFIG = {
  // Session settings
  SESSION: {
    MAX_PARTICIPANTS: parseInt(process.env.COLLAB_MAX_PARTICIPANTS || '10'),
    IDLE_TIMEOUT: parseInt(process.env.COLLAB_IDLE_TIMEOUT || '1800000'), // 30 min
    LOCK_TIMEOUT: parseInt(process.env.COLLAB_LOCK_TIMEOUT || '60000'), // 1 min
  },
  
  // Conflict resolution
  CONFLICT: {
    STRATEGY: process.env.COLLAB_CONFLICT_STRATEGY || 'operational-transform',
    AUTO_RESOLVE: process.env.COLLAB_AUTO_RESOLVE === 'true',
    BUFFER_WINDOW: parseInt(process.env.COLLAB_BUFFER_WINDOW || '100'), // ms
  },
  
  // Real-time settings
  REALTIME: {
    ENABLED: process.env.COLLAB_REALTIME_ENABLED !== 'false',
    HEARTBEAT_INTERVAL: parseInt(process.env.COLLAB_HEARTBEAT || '30000'), // 30s
    RECONNECT_ATTEMPTS: parseInt(process.env.COLLAB_RECONNECT || '5'),
  }
}

// Export consolidated configuration
export const ENV_CONFIG = {
  RATE_LIMITS,
  AI_CONFIG,
  API_URLS,
  CACHE_CONFIG,
  PERFORMANCE_CONFIG,
  SECURITY_CONFIG,
  FEATURE_FLAGS,
  MONITORING_CONFIG,
  EMAIL_CONFIG,
  COLLABORATION_CONFIG,
} as const

// Type exports
export type RateLimitsConfig = typeof RATE_LIMITS
export type AIConfig = typeof AI_CONFIG
export type APIUrlsConfig = typeof API_URLS
export type CacheConfig = typeof CACHE_CONFIG
export type PerformanceConfig = typeof PERFORMANCE_CONFIG
export type SecurityConfig = typeof SECURITY_CONFIG
export type FeatureFlagsConfig = typeof FEATURE_FLAGS
export type MonitoringConfig = typeof MONITORING_CONFIG
export type EmailConfig = typeof EMAIL_CONFIG
export type CollaborationConfig = typeof COLLABORATION_CONFIG
export type EnvironmentConfig = typeof ENV_CONFIG