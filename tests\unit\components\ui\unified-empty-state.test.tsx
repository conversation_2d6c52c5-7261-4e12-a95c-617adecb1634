import { describe, it, expect, jest } from '@jest/globals';
import { render, screen, fireEvent } from '@testing-library/react';
import { UnifiedEmptyState } from '@/components/ui/unified-empty-state';

describe('UnifiedEmptyState Component', () => {
  const defaultProps = {
    icon: '📝',
    title: 'No content yet',
    description: 'Start by creating your first item',
  };

  it('should render with basic props', () => {
    render(<UnifiedEmptyState {...defaultProps} />);
    
    expect(screen.getByText('📝')).toBeInTheDocument();
    expect(screen.getByText('No content yet')).toBeInTheDocument();
    expect(screen.getByText('Start by creating your first item')).toBeInTheDocument();
  });

  describe('action button', () => {
    it('should render action button when provided', () => {
      const handleAction = jest.fn();
      render(
        <UnifiedEmptyState
          {...defaultProps}
          actionLabel="Create New"
          onAction={handleAction}
        />
      );
      
      const button = screen.getByRole('button', { name: 'Create New' });
      expect(button).toBeInTheDocument();
    });

    it('should call onAction when button is clicked', () => {
      const handleAction = jest.fn();
      render(
        <UnifiedEmptyState
          {...defaultProps}
          actionLabel="Create New"
          onAction={handleAction}
        />
      );
      
      const button = screen.getByRole('button', { name: 'Create New' });
      fireEvent.click(button);
      expect(handleAction).toHaveBeenCalledTimes(1);
    });

    it('should not render action button without both label and handler', () => {
      // Only label, no handler
      const { rerender } = render(
        <UnifiedEmptyState {...defaultProps} actionLabel="Create" />
      );
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
      
      // Only handler, no label
      rerender(
        <UnifiedEmptyState {...defaultProps} onAction={() => {}} />
      );
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });
  });

  describe('icon variations', () => {
    it('should render emoji icon', () => {
      render(<UnifiedEmptyState {...defaultProps} icon="🚀" />);
      expect(screen.getByText('🚀')).toBeInTheDocument();
    });

    it('should render React component as icon', () => {
      const CustomIcon = () => <svg data-testid="custom-icon">Icon</svg>;
      render(<UnifiedEmptyState {...defaultProps} icon={<CustomIcon />} />);
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    it('should render icon from icon library', () => {
      const LibraryIcon = () => (
        <svg data-testid="library-icon" className="w-6 h-6">
          <path d="..." />
        </svg>
      );
      render(<UnifiedEmptyState {...defaultProps} icon={<LibraryIcon />} />);
      expect(screen.getByTestId('library-icon')).toBeInTheDocument();
    });
  });

  describe('content variations', () => {
    it('should render without description', () => {
      render(
        <UnifiedEmptyState
          icon="📭"
          title="Empty inbox"
        />
      );
      
      expect(screen.getByText('Empty inbox')).toBeInTheDocument();
      expect(screen.queryByText('description')).not.toBeInTheDocument();
    });

    it('should render with HTML in description', () => {
      render(
        <UnifiedEmptyState
          {...defaultProps}
          description={
            <>
              Start by <strong>creating</strong> your first item
            </>
          }
        />
      );
      
      expect(screen.getByText('creating')).toHaveClass('strong');
    });

    it('should render with custom content', () => {
      render(
        <UnifiedEmptyState {...defaultProps}>
          <div data-testid="custom-content">
            <p>Custom empty state content</p>
            <button>Custom action</button>
          </div>
        </UnifiedEmptyState>
      );
      
      expect(screen.getByTestId('custom-content')).toBeInTheDocument();
      expect(screen.getByText('Custom empty state content')).toBeInTheDocument();
    });
  });

  describe('styling and theming', () => {
    it('should apply default styles', () => {
      const { container } = render(<UnifiedEmptyState {...defaultProps} />);
      const wrapper = container.firstChild as HTMLElement;
      
      expect(wrapper).toHaveClass('flex');
      expect(wrapper).toHaveClass('flex-col');
      expect(wrapper).toHaveClass('items-center');
      expect(wrapper).toHaveClass('justify-center');
    });

    it('should accept custom className', () => {
      const { container } = render(
        <UnifiedEmptyState {...defaultProps} className="custom-empty-state" />
      );
      const wrapper = container.firstChild as HTMLElement;
      
      expect(wrapper).toHaveClass('custom-empty-state');
    });

    it('should have proper spacing', () => {
      render(<UnifiedEmptyState {...defaultProps} />);
      
      const iconWrapper = screen.getByText('📝').parentElement;
      expect(iconWrapper).toHaveClass('mb-4');
    });

    it('should style text appropriately', () => {
      render(<UnifiedEmptyState {...defaultProps} />);
      
      const title = screen.getByText('No content yet');
      const description = screen.getByText('Start by creating your first item');
      
      expect(title).toHaveClass('text-lg');
      expect(title).toHaveClass('font-semibold');
      expect(description).toHaveClass('text-sm');
      expect(description).toHaveClass('text-muted-foreground');
    });
  });

  describe('common use cases', () => {
    it('should render search empty state', () => {
      render(
        <UnifiedEmptyState
          icon="🔍"
          title="No results found"
          description="Try adjusting your search terms"
          actionLabel="Clear search"
          onAction={() => {}}
        />
      );
      
      expect(screen.getByText('No results found')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Clear search' })).toBeInTheDocument();
    });

    it('should render error empty state', () => {
      render(
        <UnifiedEmptyState
          icon="⚠️"
          title="Something went wrong"
          description="We couldn't load your content"
          actionLabel="Try again"
          onAction={() => {}}
        />
      );
      
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Try again' })).toBeInTheDocument();
    });

    it('should render loading empty state', () => {
      render(
        <UnifiedEmptyState
          icon="⏳"
          title="Loading..."
          description="Please wait while we fetch your content"
        />
      );
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper heading structure', () => {
      render(<UnifiedEmptyState {...defaultProps} />);
      
      const title = screen.getByText('No content yet');
      expect(title.tagName).toBe('H3');
    });

    it('should have proper semantic structure', () => {
      const { container } = render(
        <UnifiedEmptyState
          {...defaultProps}
          actionLabel="Create"
          onAction={() => {}}
        />
      );
      
      const wrapper = container.firstChild as HTMLElement;
      expect(wrapper.tagName).toBe('DIV');
      expect(wrapper).toHaveAttribute('role', 'status');
    });

    it('should support aria-label', () => {
      const { container } = render(
        <UnifiedEmptyState
          {...defaultProps}
          aria-label="Empty state message"
        />
      );
      
      const wrapper = container.firstChild as HTMLElement;
      expect(wrapper).toHaveAttribute('aria-label', 'Empty state message');
    });
  });

  describe('responsive behavior', () => {
    it('should be centered on all screen sizes', () => {
      const { container } = render(<UnifiedEmptyState {...defaultProps} />);
      const wrapper = container.firstChild as HTMLElement;
      
      expect(wrapper).toHaveClass('text-center');
      expect(wrapper).toHaveClass('p-8');
    });

    it('should have maximum width constraint', () => {
      const { container } = render(<UnifiedEmptyState {...defaultProps} />);
      const wrapper = container.firstChild as HTMLElement;
      
      expect(wrapper).toHaveClass('max-w-md');
      expect(wrapper).toHaveClass('mx-auto');
    });
  });

  describe('integration with other components', () => {
    it('should work inside a card', () => {
      render(
        <div className="card">
          <UnifiedEmptyState {...defaultProps} />
        </div>
      );
      
      expect(screen.getByText('No content yet')).toBeInTheDocument();
    });

    it('should work with conditional rendering', () => {
      const items: any[] = [];
      
      render(
        <>
          {items.length === 0 ? (
            <UnifiedEmptyState {...defaultProps} />
          ) : (
            <div>Items list</div>
          )}
        </>
      );
      
      expect(screen.getByText('No content yet')).toBeInTheDocument();
      expect(screen.queryByText('Items list')).not.toBeInTheDocument();
    });
  });
});