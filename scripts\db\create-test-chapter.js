const { createClient } = require('@supabase/supabase-js')

// Use environment variables for database connection
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://xvqeiwrpbzpiqvwuvtpj.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTestChapter() {
  console.log('Creating test chapter...')
  
  try {
    const projectId = '446f89f9-3e0b-4508-9bff-4c1e1500e87c'
    
    // Create a test chapter
    const { data: chapter, error } = await supabase
      .from('chapters')
      .insert({
        project_id: projectId,
        chapter_number: 1,
        title: 'Chapter 1: The Beginning',
        content: 'This is the beginning of our story...',
        actual_word_count: 8,
        status: 'draft'
      })
      .select()
      .single()
    
    if (error) {
      console.error('❌ Error creating chapter:', error)
    } else {
      console.log('✅ Created test chapter:', chapter)
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

createTestChapter()
