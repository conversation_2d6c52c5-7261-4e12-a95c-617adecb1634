// Service Layer Exports
export { BaseService, ServiceRegistry } from './base-service';
export { ServiceManager } from './service-manager';

// Core Services
export { AIOrchestrator } from './ai-orchestrator';
export { AnalyticsEngine } from './analytics-engine';
export { ContentGenerator } from './content-generator';
export { ContextManager } from './context-manager';
export { SemanticSearchService } from './semantic-search';
export { UnifiedCollaborationService, collaborationService } from './unified-collaboration-service';
export { ChapterGenerator } from './chapter-generator';
export { PlanAdjustmentService } from './plan-adjustment';
export { ContentAnalysisService, contentAnalysisService } from './content-analysis-service';
export { StreamDataService, streamDataService } from './stream-data-service';

// Service Types
export type { ServiceConfig, ServiceResponse, AnalyticsEvent } from './types';