import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { ExportQueueService } from '@/lib/services/export-queue-service'

// POST - Cancel an export job
export const POST = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: jobId } = await params
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify ownership
    const { data: job, error } = await supabase
      .from('export_jobs')
      .select('status')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single()

    if (error || !job) {
      return UnifiedResponse.error('Export job not found', 404)
    }

    if (!['pending', 'processing'].includes(job.status)) {
      return UnifiedResponse.error('Job cannot be cancelled', 400)
    }

    const cancelled = await ExportQueueService.cancelJob(jobId)

    if (!cancelled) {
      return UnifiedResponse.error('Failed to cancel job', 500)
    }

    return UnifiedResponse.success({ 
      success: true, 
      message: 'Export job cancelled successfully' 
    })
  } catch (error) {
    logger.error('Cancel export job error:', error)
    return UnifiedResponse.error('Failed to cancel export job')
  }
})