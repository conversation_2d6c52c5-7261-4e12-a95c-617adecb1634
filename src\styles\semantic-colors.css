/* Semantic Color Tokens for BookScribe */
/* These tokens provide semantic meaning to colors across all themes */

@layer base {
  :root {
    /* Semantic Status Colors */
    --color-success: 142 71% 45%;
    --color-success-foreground: 0 0% 100%;
    --color-success-light: 142 71% 85%;
    --color-success-dark: 142 71% 25%;
    
    --color-error: 0 84% 60%;
    --color-error-foreground: 0 0% 100%;
    --color-error-light: 0 84% 85%;
    --color-error-dark: 0 84% 35%;
    
    --color-warning: 38 92% 50%;
    --color-warning-foreground: 0 0% 0%;
    --color-warning-light: 38 92% 85%;
    --color-warning-dark: 38 92% 30%;
    
    --color-info: 217 91% 60%;
    --color-info-foreground: 0 0% 100%;
    --color-info-light: 217 91% 85%;
    --color-info-dark: 217 91% 35%;
    
    /* Semantic UI Colors */
    --color-primary-50: 25 75% 95%;
    --color-primary-100: 25 75% 90%;
    --color-primary-200: 25 75% 80%;
    --color-primary-300: 25 75% 70%;
    --color-primary-400: 25 75% 60%;
    --color-primary-500: 25 75% 45%; /* Default primary */
    --color-primary-600: 25 75% 35%;
    --color-primary-700: 25 75% 25%;
    --color-primary-800: 25 75% 15%;
    --color-primary-900: 25 75% 5%;
    
    /* Neutral Colors */
    --color-gray-50: 0 0% 98%;
    --color-gray-100: 0 0% 96%;
    --color-gray-200: 0 0% 90%;
    --color-gray-300: 0 0% 83%;
    --color-gray-400: 0 0% 64%;
    --color-gray-500: 0 0% 45%;
    --color-gray-600: 0 0% 32%;
    --color-gray-700: 0 0% 25%;
    --color-gray-800: 0 0% 12%;
    --color-gray-900: 0 0% 6%;
    
    /* Chart & Visualization Colors */
    --color-chart-1: var(--color-primary-500);
    --color-chart-2: var(--color-info);
    --color-chart-3: var(--color-success);
    --color-chart-4: var(--color-warning);
    --color-chart-5: 280 65% 60%;
    --color-chart-6: 340 75% 55%;
    
    /* Achievement & Metric Colors */
    --color-achievement: 270 60% 55%;
    --color-achievement-foreground: 0 0% 100%;
    --color-achievement-light: 270 60% 85%;
    --color-achievement-dark: 270 60% 35%;
    
    --color-metric: 280 65% 60%;
    --color-metric-foreground: 0 0% 100%;
    --color-metric-light: 280 65% 85%;
    --color-metric-dark: 280 65% 35%;
  }
  
  /* Override for dark themes */
  .dark,
  .writers-sanctuary-dark,
  .evening-study-dark,
  .midnight-ink-dark,
  .forest-manuscript-dark {
    --color-success: 142 71% 65%;
    --color-success-light: 142 71% 35%;
    --color-success-dark: 142 71% 85%;
    
    --color-error: 0 84% 70%;
    --color-error-light: 0 84% 35%;
    --color-error-dark: 0 84% 85%;
    
    --color-warning: 38 92% 70%;
    --color-warning-light: 38 92% 35%;
    --color-warning-dark: 38 92% 85%;
    
    --color-info: 217 91% 70%;
    --color-info-light: 217 91% 35%;
    --color-info-dark: 217 91% 85%;
  }
}

/* Utility classes for semantic colors */
@layer utilities {
  /* Text colors */
  .text-success { color: hsl(var(--color-success)); }
  .text-error { color: hsl(var(--color-error)); }
  .text-warning { color: hsl(var(--color-warning)); }
  .text-info { color: hsl(var(--color-info)); }
  
  /* Background colors */
  .bg-success { background-color: hsl(var(--color-success)); }
  .bg-error { background-color: hsl(var(--color-error)); }
  .bg-warning { background-color: hsl(var(--color-warning)); }
  .bg-info { background-color: hsl(var(--color-info)); }
  
  .bg-success-light { background-color: hsl(var(--color-success-light)); }
  .bg-error-light { background-color: hsl(var(--color-error-light)); }
  .bg-warning-light { background-color: hsl(var(--color-warning-light)); }
  .bg-info-light { background-color: hsl(var(--color-info-light)); }
  
  /* Border colors */
  .border-success { border-color: hsl(var(--color-success)); }
  .border-error { border-color: hsl(var(--color-error)); }
  .border-warning { border-color: hsl(var(--color-warning)); }
  .border-info { border-color: hsl(var(--color-info)); }
  
  /* Primary color scale */
  .text-primary-50 { color: hsl(var(--color-primary-50)); }
  .text-primary-100 { color: hsl(var(--color-primary-100)); }
  .text-primary-200 { color: hsl(var(--color-primary-200)); }
  .text-primary-300 { color: hsl(var(--color-primary-300)); }
  .text-primary-400 { color: hsl(var(--color-primary-400)); }
  .text-primary-500 { color: hsl(var(--color-primary-500)); }
  .text-primary-600 { color: hsl(var(--color-primary-600)); }
  .text-primary-700 { color: hsl(var(--color-primary-700)); }
  .text-primary-800 { color: hsl(var(--color-primary-800)); }
  .text-primary-900 { color: hsl(var(--color-primary-900)); }
  
  .bg-primary-50 { background-color: hsl(var(--color-primary-50)); }
  .bg-primary-100 { background-color: hsl(var(--color-primary-100)); }
  .bg-primary-200 { background-color: hsl(var(--color-primary-200)); }
  .bg-primary-300 { background-color: hsl(var(--color-primary-300)); }
  .bg-primary-400 { background-color: hsl(var(--color-primary-400)); }
  .bg-primary-500 { background-color: hsl(var(--color-primary-500)); }
  .bg-primary-600 { background-color: hsl(var(--color-primary-600)); }
  .bg-primary-700 { background-color: hsl(var(--color-primary-700)); }
  .bg-primary-800 { background-color: hsl(var(--color-primary-800)); }
  .bg-primary-900 { background-color: hsl(var(--color-primary-900)); }
  
  /* Neutral color scale */
  .text-gray-50 { color: hsl(var(--color-gray-50)); }
  .text-gray-100 { color: hsl(var(--color-gray-100)); }
  .text-gray-200 { color: hsl(var(--color-gray-200)); }
  .text-gray-300 { color: hsl(var(--color-gray-300)); }
  .text-gray-400 { color: hsl(var(--color-gray-400)); }
  .text-gray-500 { color: hsl(var(--color-gray-500)); }
  .text-gray-600 { color: hsl(var(--color-gray-600)); }
  .text-gray-700 { color: hsl(var(--color-gray-700)); }
  .text-gray-800 { color: hsl(var(--color-gray-800)); }
  .text-gray-900 { color: hsl(var(--color-gray-900)); }
  
  .bg-gray-50 { background-color: hsl(var(--color-gray-50)); }
  .bg-gray-100 { background-color: hsl(var(--color-gray-100)); }
  .bg-gray-200 { background-color: hsl(var(--color-gray-200)); }
  .bg-gray-300 { background-color: hsl(var(--color-gray-300)); }
  .bg-gray-400 { background-color: hsl(var(--color-gray-400)); }
  .bg-gray-500 { background-color: hsl(var(--color-gray-500)); }
  .bg-gray-600 { background-color: hsl(var(--color-gray-600)); }
  .bg-gray-700 { background-color: hsl(var(--color-gray-700)); }
  .bg-gray-800 { background-color: hsl(var(--color-gray-800)); }
  .bg-gray-900 { background-color: hsl(var(--color-gray-900)); }
  
  /* Achievement & Metric colors */
  .text-achievement { color: hsl(var(--color-achievement)); }
  .text-metric { color: hsl(var(--color-metric)); }
  
  .bg-achievement { background-color: hsl(var(--color-achievement)); }
  .bg-metric { background-color: hsl(var(--color-metric)); }
  
  .bg-achievement-light { background-color: hsl(var(--color-achievement-light)); }
  .bg-metric-light { background-color: hsl(var(--color-metric-light)); }
  
  .border-achievement { border-color: hsl(var(--color-achievement)); }
  .border-metric { border-color: hsl(var(--color-metric)); }
}