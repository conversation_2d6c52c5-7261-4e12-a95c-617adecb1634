"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SimpleTypewriter } from "@/components/ui/simple-typewriter";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { SettingsButton } from "@/components/settings/settings-modal";
import { <PERSON><PERSON><PERSON>, Book<PERSON>pen, Brain, Globe, Star, Feather, Users, Award } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen overflow-hidden">
      {/* Paper texture background */}
      <div className="fixed inset-0 paper-texture opacity-30" />
      
      {/* Header */}
      <header className="relative z-50 border-b border-border bg-background/95 backdrop-blur-xl shadow-sm">
        <div className="container flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 blur-sm opacity-20" />
              <div className="relative w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-6 h-6 text-primary-foreground" />
              </div>
            </div>
            <h1 className="text-3xl font-bold tracking-tight font-literary-display leading-none mt-3">
              BookScribe AI
            </h1>
          </div>

          <nav className="flex items-center gap-2 sm:gap-4">
            <a href="/pricing">
              <Button
                variant="ghost"
                className="text-foreground/80 hover:text-foreground hover:bg-accent font-medium"
              >
                Pricing
              </Button>
            </a>
            <ThemeToggle />
            <SettingsButton />
            <a href="/login">
              <Button
                variant="ghost"
                className="text-foreground/80 hover:text-foreground hover:bg-accent font-medium"
              >
                Sign In
              </Button>
            </a>
            <a href="/signup">
              <Button variant="literary" className="font-medium px-6">
                Begin Writing
                <Feather className="w-4 h-4 ml-1" />
              </Button>
            </a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main id="main-content" className="relative z-10">
        <section className="relative min-h-[90vh] flex items-center justify-center px-4 py-20">
          <div className="container max-w-6xl mx-auto text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-primary/30 bg-primary/10 mb-8 shadow-sm">
              <BookOpen className="w-4 h-4 text-primary" />
              <span className="text-sm text-primary font-medium">Join 10,000+ authors crafting their masterpieces</span>
            </div>

            {/* Main heading with literary styling */}
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-foreground font-literary-display">
              <span className="block mb-2">
                Your Next Great Novel
              </span>
              <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent">
                <SimpleTypewriter
                  words={["Awaits Your Pen", "Begins Here", "Starts Today", "Lives Within You"]}
                  typingSpeed={120}
                  deletingSpeed={60}
                  delay={2500}
                />
              </span>
            </h2>

            <p className="text-lg sm:text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-8 px-4 font-mono">
              Step into your digital writing sanctuary, where AI meets artistry.
              <span className="block mt-2 text-primary font-semibold">
                From blank page to bestseller, craft stories that captivate.
              </span>
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <a href="/signup">
                <Button
                  size="lg"
                  variant="literary"
                  className="group relative overflow-hidden font-medium px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg transition-all duration-300 w-full sm:w-auto"
                >
                  <span className="relative z-10 flex items-center">
                    <Feather className="w-5 h-5 mr-2" />
                    Begin Your Story
                  </span>
                  <div className="absolute inset-0 bg-white/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
                </Button>
              </a>
              <a href="/demo">
                <Button
                  size="lg"
                  variant="outline"
                  className="font-medium px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg w-full sm:w-auto"
                >
                  <BookOpen className="w-5 h-5 mr-2" />
                  Explore Features
                </Button>
              </a>
            </div>

            {/* Stats - Literary Style */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="text-center p-8 bg-card rounded-xl border border-border shadow-sm backdrop-blur-sm">
                <div className="text-3xl font-bold text-primary mb-2 font-literary">2M+</div>
                <div className="text-sm text-muted-foreground font-medium">Words Written Daily</div>
                <div className="w-8 h-1 bg-primary/30 mx-auto mt-2 rounded-full"></div>
              </div>
              <div className="text-center p-8 bg-card rounded-xl border border-border shadow-sm backdrop-blur-sm">
                <div className="text-3xl font-bold text-primary mb-2 font-literary">98%</div>
                <div className="text-sm text-muted-foreground font-medium">Author Satisfaction</div>
                <div className="w-8 h-1 bg-primary/30 mx-auto mt-2 rounded-full"></div>
              </div>
              <div className="text-center p-8 bg-card rounded-xl border border-border shadow-sm backdrop-blur-sm">
                <div className="text-3xl font-bold text-primary mb-2 font-literary">50+</div>
                <div className="text-sm text-muted-foreground font-medium">Published Novels</div>
                <div className="w-8 h-1 bg-primary/30 mx-auto mt-2 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Scroll indicator - Literary Style */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-primary/30 rounded-full flex justify-center bg-background/20 backdrop-blur-sm">
              <div className="w-1 h-3 bg-primary/60 rounded-full mt-2" />
            </div>
          </div>
        </section>

        {/* Features Section - Your Writing Toolkit */}
        <section className="relative py-20 px-4 border-t border-border">
          <div className="container max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h3 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 text-foreground font-literary-display">
                Your Writing Toolkit
              </h3>
              <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto px-4 font-literary">
                Every tool a novelist needs, beautifully crafted and intelligently designed
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/80 mx-auto mt-6 rounded-full"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Manuscript Editor */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <PenTool className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Manuscript Editor</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Write with the elegance of a typewriter and the power of AI. Your words flow naturally while intelligent assistance guides your craft.
                  </p>
                </div>
              </div>

              {/* Story Bible */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <BookOpen className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Story Bible</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Keep track of every character, location, and plot thread. Your story&apos;s universe organized like a master storyteller&apos;s notebook.
                  </p>
                </div>
              </div>

              {/* AI Writing Partner */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Brain className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">AI Writing Partner</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Collaborate with AI that understands your voice and vision. Get suggestions that enhance, never replace, your creative genius.
                  </p>
                </div>
              </div>

              {/* Character Development */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Users className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Character Development</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Breathe life into your characters with detailed profiles, relationship maps, and personality insights that evolve with your story.
                  </p>
                </div>
              </div>

              {/* Progress Tracking */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Award className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Progress Tracking</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Watch your novel grow with beautiful visualizations, milestone celebrations, and insights that keep you motivated.
                  </p>
                </div>
              </div>

              {/* Research Hub */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Globe className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Research Hub</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Organize your research, inspiration, and reference materials in one accessible place, always at your fingertips while you write.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Testimonials Section */}
        <section className="relative py-20 px-4 border-t border-border">
          <div className="container max-w-4xl mx-auto text-center">
            <h3 className="text-3xl sm:text-4xl font-bold mb-4 text-foreground font-literary-display">
              Loved by Authors Worldwide
            </h3>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/80 mx-auto mb-12 rounded-full"></div>

            <div className="bg-card rounded-2xl p-6 border border-border shadow-lg backdrop-blur-sm">
              <div className="flex justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-primary fill-current" />
                ))}
              </div>
              <blockquote className="text-lg text-foreground mb-6 italic font-literary">
                &ldquo;BookScribe AI transformed my writing process. What used to take months now flows naturally in weeks.
                The AI understands my voice and helps me craft stories I never thought possible.&rdquo;
              </blockquote>
              <div className="flex items-center justify-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center">
                  <span className="text-primary-foreground font-semibold">SJ</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-foreground">Sarah Johnson</div>
                  <div className="text-sm text-muted-foreground">Author of &ldquo;The Midnight Chronicles&rdquo;</div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="relative z-10 border-t border-border py-12 bg-background/95 backdrop-blur-xl">
        <div className="container text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
              <Feather className="w-4 h-4 text-primary-foreground" />
            </div>
            <span className="text-lg font-semibold text-foreground font-literary">
              BookScribe AI
            </span>
          </div>
          <p className="text-muted-foreground text-sm">
            © 2025 BookScribe AI. Where every great story begins.
          </p>
        </div>
      </footer>
    </div>
  );
}