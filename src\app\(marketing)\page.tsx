import { <PERSON><PERSON> } from "@/components/ui/button";
import { SimpleTypewriter } from "@/components/ui/simple-typewriter";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { SettingsButton } from "@/components/settings/settings-modal";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Brain, Globe, Star, Feather, Users, Award } from "lucide-react";
import { createServerClient } from '@/lib/supabase/server';
import { AuthButtons } from "@/components/layout/auth-buttons";

export default async function Home() {
  const supabase = await createServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  return (
    <div className="min-h-screen overflow-hidden">
      {/* Paper texture background */}
      <div className="fixed inset-0 paper-texture opacity-30" />
      
      {/* Header */}
      <header className="relative z-50 border-b border-border bg-background/95 backdrop-blur-xl shadow-sm">
        <div className="container-wide flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 blur-sm opacity-20" />
              <div className="relative w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-6 h-6 text-primary-foreground" />
              </div>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight font-literary-display leading-none mt-4">
              BookScribe AI
            </h1>
          </div>

          <nav className="flex items-center gap-2 sm:gap-4 sm:gap-5 lg:gap-6">
            <a href="/pricing">
              <Button
                variant="ghost"
                className="text-foreground/80 hover:text-foreground hover:bg-accent font-medium"
              >
                Pricing
              </Button>
            </a>
            <ThemeToggle />
            <SettingsButton />
            <AuthButtons user={user} />
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main id="main-content" className="relative z-10">
        <section className="relative min-h-[90vh] flex items-center justify-center px-4 py-20">
          <div className="container-wide max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px] xl:max-w-[1400px] 2xl:max-w-[1600px] mx-auto text-center">
            {/* Badge - scales with screen size */}
            <div className="inline-flex items-center gap-2 px-4 py-2 lg:px-6 lg:py-3 xl:px-8 xl:py-4 rounded-full border border-primary/30 bg-primary/10 mb-8 shadow-sm">
              <BookOpen className="w-4 h-4 lg:w-5 lg:h-5 xl:w-6 xl:h-6 text-primary" />
              <span className="text-sm lg:text-base xl:text-lg text-primary font-medium">Join 10,000+ authors crafting their masterpieces</span>
            </div>

            {/* Main heading with literary styling - scales dramatically with screen size */}
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-bold mb-6 text-foreground font-literary-display">
              <span className="block mb-2">
                Your Next Great Novel
              </span>
              <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent">
                <SimpleTypewriter
                  words={["Awaits Your Pen", "Begins Here", "Starts Today", "Lives Within You"]}
                  typingSpeed={120}
                  deletingSpeed={60}
                  delay={2500}
                />
              </span>
            </h2>

            <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl text-muted-foreground max-w-3xl xl:max-w-4xl 2xl:max-w-5xl mx-auto leading-relaxed mb-8 px-4 font-mono">
              Step into your digital writing sanctuary, where AI meets artistry.
              <span className="block mt-2 text-primary font-semibold">
                From blank page to bestseller, craft stories that captivate.
              </span>
            </p>

            {/* CTA Buttons - scale with screen size */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 xl:gap-8 justify-center">
              {user ? (
                <a href="/dashboard">
                  <Button
                    size="lg"
                    variant="literary"
                    className="group relative overflow-hidden font-medium px-6 sm:px-8 lg:px-10 xl:px-12 2xl:px-16 py-4 sm:py-5 lg:py-6 xl:py-7 2xl:py-8 text-base sm:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl transition-all duration-300 w-full sm:w-auto"
                  >
                    <span className="relative z-10 flex items-center">
                      <Feather className="w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 xl:w-8 xl:h-8 2xl:w-9 2xl:h-9 mr-2 lg:mr-3" />
                      Continue Writing
                    </span>
                    <div className="absolute inset-0 bg-white/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
                  </Button>
                </a>
              ) : (
                <a href="/signup">
                  <Button
                    size="lg"
                    variant="literary"
                    className="group relative overflow-hidden font-medium px-6 sm:px-8 lg:px-10 xl:px-12 2xl:px-16 py-4 sm:py-5 lg:py-6 xl:py-7 2xl:py-8 text-base sm:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl transition-all duration-300 w-full sm:w-auto"
                  >
                    <span className="relative z-10 flex items-center">
                      <Feather className="w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 xl:w-8 xl:h-8 2xl:w-9 2xl:h-9 mr-2 lg:mr-3" />
                      Begin Your Story
                    </span>
                    <div className="absolute inset-0 bg-white/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
                  </Button>
                </a>
              )}
              <a href="/demo">
                <Button
                  size="lg"
                  variant="outline"
                  className="font-medium px-6 sm:px-8 lg:px-10 xl:px-12 2xl:px-16 py-4 sm:py-5 lg:py-6 xl:py-7 2xl:py-8 text-base sm:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl w-full sm:w-auto"
                >
                  <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 xl:w-8 xl:h-8 2xl:w-9 2xl:h-9 mr-2 lg:mr-3" />
                  Explore Features
                </Button>
              </a>
            </div>
          </div>

          {/* Scroll indicator - Literary Style */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-primary/30 rounded-full flex justify-center bg-background/20 backdrop-blur-sm">
              <div className="w-1 h-3 bg-primary/60 rounded-full mt-2" />
            </div>
          </div>
        </section>

        {/* Features Section - Your Writing Toolkit */}
        <section className="relative py-20 px-4 border-t border-border">
          <div className="container-wide max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px] xl:max-w-[1400px] 2xl:max-w-[1600px] mx-auto">
            <div className="text-center mb-16">
              <h3 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 text-foreground font-literary-display">
                Your Writing Toolkit
              </h3>
              <p className="text-lg sm:text-xl lg:text-2xl xl:text-3xl text-muted-foreground max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto px-4 font-literary">
                Every tool a novelist needs, beautifully crafted and intelligently designed
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/80 mx-auto mt-6 rounded-full"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
              {/* Manuscript Editor */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <PenTool className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Manuscript Editor</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Write with the elegance of a typewriter and the power of AI. Your words flow naturally while intelligent assistance guides your craft.
                  </p>
                </div>
              </div>

              {/* Story Bible */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <BookOpen className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Story Bible</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Keep track of every character, location, and plot thread. Your story&apos;s universe organized like a master storyteller&apos;s notebook.
                  </p>
                </div>
              </div>

              {/* AI Writing Partner */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Brain className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">AI Writing Partner</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Collaborate with AI that understands your voice and vision. Get suggestions that enhance, never replace, your creative genius.
                  </p>
                </div>
              </div>

              {/* Character Development */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Users className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Character Development</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Breathe life into your characters with detailed profiles, relationship maps, and personality insights that evolve with your story.
                  </p>
                </div>
              </div>

              {/* Progress Tracking */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Award className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Progress Tracking</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Watch your novel grow with beautiful visualizations, milestone celebrations, and insights that keep you motivated.
                  </p>
                </div>
              </div>

              {/* Research Hub */}
              <div className="group relative overflow-hidden rounded-2xl border border-border backdrop-blur-sm bg-card p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:border-primary/30">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative z-10">
                  <div className="mb-4 w-16 h-16 rounded-xl bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center border border-primary/30">
                    <Globe className="w-8 h-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 font-literary">Research Hub</h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Organize your research, inspiration, and reference materials in one accessible place, always at your fingertips while you write.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Testimonials Section */}
        <section className="relative py-20 px-4 border-t border-border">
          <div className="container-wide max-w-4xl mx-auto text-center">
            <h3 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 text-foreground font-literary-display">
              Loved by Authors Worldwide
            </h3>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/80 mx-auto mb-12 rounded-full"></div>

            <div className="bg-card rounded-2xl p-6 border border-border shadow-lg backdrop-blur-sm">
              <div className="flex justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-primary fill-current" />
                ))}
              </div>
              <blockquote className="text-lg text-foreground mb-6 italic font-literary">
                &ldquo;BookScribe AI transformed my writing process. What used to take months now flows naturally in weeks.
                The AI understands my voice and helps me craft stories I never thought possible.&rdquo;
              </blockquote>
              <div className="flex items-center justify-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center">
                  <span className="text-primary-foreground font-semibold">SJ</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-foreground">Sarah Johnson</div>
                  <div className="text-sm text-muted-foreground">Author of &ldquo;The Midnight Chronicles&rdquo;</div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="relative z-10 border-t border-border py-12 bg-background/95 backdrop-blur-xl">
        <div className="container-wide text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
              <Feather className="w-4 h-4 text-primary-foreground" />
            </div>
            <span className="text-lg font-semibold text-foreground font-literary">
              BookScribe AI
            </span>
          </div>
          <p className="text-muted-foreground text-sm">
            © 2025 BookScribe AI. Where every great story begins.
          </p>
        </div>
      </footer>
    </div>
  );
}