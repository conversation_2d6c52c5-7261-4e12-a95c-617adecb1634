import type { PricingTier } from '@/components/billing/pricing-card'
import { SIZE_LIMITS } from '@/lib/constants'

export const SUBSCRIPTION_TIERS: PricingTier[] = [
  {
    id: 'free',
    name: 'Starter',
    description: 'Perfect for trying out BookScribe',
    monthlyPrice: 0,
    yearlyPrice: 0,
    stripePriceId: {
      monthly: '',
      yearly: '',
    },
    features: [
      { name: '1 Active Project', included: true },
      { name: 'Basic AI Writing Assistant', included: true },
      { name: '10,000 AI Words/month', included: true, value: '10k' },
      { name: 'Character & World Building', included: true },
      { name: 'Chapter Planning', included: true },
      { name: 'Export to TXT/Markdown', included: true },
      { name: 'Community Support', included: true },
      { name: 'Advanced AI Agents', included: false },
      { name: 'Voice Profile System', included: false },
      { name: 'Real-time Collaboration', included: false },
      { name: 'Series Management', included: false },
      { name: 'Priority Support', included: false },
    ],
  },
  {
    id: 'novelist',
    name: 'Novelist',
    description: 'For serious writers working on their masterpiece',
    monthlyPrice: 2900, // $29
    yearlyPrice: 27900, // $279 (20% off)
    stripePriceId: {
      monthly: process.env.NEXT_PUBLIC_STRIPE_NOVELIST_MONTHLY_PRICE_ID || '',
      yearly: process.env.NEXT_PUBLIC_STRIPE_NOVELIST_YEARLY_PRICE_ID || '',
    },
    highlighted: true,
    badge: 'Most Popular',
    features: [
      { name: '5 Active Projects', included: true },
      { name: 'All AI Writing Agents', included: true },
      { name: '100,000 AI Words/month', included: true, value: '100k' },
      { name: 'Advanced Character Development', included: true },
      { name: 'Voice Profile System', included: true },
      { name: 'Writing Analytics', included: true },
      { name: 'Export to DOCX/PDF', included: true },
      { name: 'Email Support', included: true },
      { name: 'Real-time Collaboration', included: false },
      { name: 'Series Management', included: false },
      { name: 'Custom AI Training', included: false },
      { name: 'White-label Options', included: false },
    ],
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'For prolific authors and writing teams',
    monthlyPrice: 5900, // $59
    yearlyPrice: 56900, // $569 (20% off)
    stripePriceId: {
      monthly: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID || '',
      yearly: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_YEARLY_PRICE_ID || '',
    },
    features: [
      { name: 'Unlimited Projects', included: true },
      { name: 'All AI Writing Agents', included: true },
      { name: '500,000 AI Words/month', included: true, value: '500k' },
      { name: 'Series & Universe Management', included: true },
      { name: 'Real-time Collaboration', included: true, value: '5 users' },
      { name: 'Advanced Analytics & Insights', included: true },
      { name: 'Export to All Formats', included: true },
      { name: 'Voice Cloning (Beta)', included: true },
      { name: 'Priority Email Support', included: true },
      { name: 'Custom AI Training', included: false },
      { name: 'Dedicated Account Manager', included: false },
      { name: 'SLA Guarantee', included: false },
    ],
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Custom solutions for publishers and agencies',
    monthlyPrice: -1, // Custom pricing
    yearlyPrice: -1,
    stripePriceId: {
      monthly: '',
      yearly: '',
    },
    features: [
      { name: 'Everything in Professional', included: true },
      { name: 'Unlimited AI Words', included: true },
      { name: 'Unlimited Team Members', included: true },
      { name: 'Custom AI Model Training', included: true },
      { name: 'White-label Options', included: true },
      { name: 'API Access', included: true },
      { name: 'Dedicated Infrastructure', included: true },
      { name: 'SLA & Uptime Guarantee', included: true },
      { name: '24/7 Phone Support', included: true },
      { name: 'Dedicated Success Manager', included: true },
      { name: 'Custom Integrations', included: true },
      { name: 'On-premise Deployment', included: true },
    ],
  },
]

export function getTierById(tierId: string): PricingTier | undefined {
  return SUBSCRIPTION_TIERS.find(tier => tier.id === tierId)
}

export function getTierByPriceId(priceId: string): PricingTier | undefined {
  return SUBSCRIPTION_TIERS.find(tier => 
    tier.stripePriceId.monthly === priceId || 
    tier.stripePriceId.yearly === priceId
  )
}

export function calculateUsagePercentage(used: number, limit: number): number {
  if (limit === 0) return 0
  return Math.min((used / limit) * 100, 100)
}

export interface UsageLimits {
  projects: number
  aiWords: number
  collaborators: number
  storage: number // in GB
}

export function getTierLimits(tierId: string): UsageLimits {
  switch (tierId) {
    case 'free':
      return {
        projects: 1,
        aiWords: 10000,
        collaborators: 0,
        storage: 1,
      }
    case 'novelist':
      return {
        projects: 5,
        aiWords: SIZE_LIMITS.LARGE_DOCUMENT_THRESHOLD,
        collaborators: 0,
        storage: 10,
      }
    case 'professional':
      return {
        projects: -1, // unlimited
        aiWords: 500000,
        collaborators: 5,
        storage: 100,
      }
    case 'enterprise':
      return {
        projects: -1,
        aiWords: -1,
        collaborators: -1,
        storage: -1,
      }
    default:
      return {
        projects: 1,
        aiWords: 10000,
        collaborators: 0,
        storage: 1,
      }
  }
}