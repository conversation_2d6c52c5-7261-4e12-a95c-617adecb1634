'use client'

import { useState, useEffect } from 'react'
import { Bell } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { NotificationPanel } from './notification-panel'
import { useAuth } from '@/contexts/auth-context'
import { cn } from '@/lib/utils'

interface NotificationBellProps {
  className?: string
  showBadge?: boolean
  pulseOnNew?: boolean
}

export function NotificationBell({ 
  className, 
  showBadge = true,
  pulseOnNew = true 
}: NotificationBellProps) {
  const { user } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)
  const [hasNewNotification, setHasNewNotification] = useState(false)

  useEffect(() => {
    if (!user) return

    fetchUnreadCount()
    
    // Poll for new notifications every 30 seconds
    const interval = setInterval(fetchUnreadCount, 30000)
    
    return () => clearInterval(interval)
  }, [user])

  const fetchUnreadCount = async () => {
    try {
      const response = await fetch('/api/notifications?unread=true&limit=1')
      if (response.ok) {
        const data = await response.json()
        const newCount = data.unread_count || 0
        
        // Check if count increased (new notification)
        if (newCount > unreadCount) {
          setHasNewNotification(true)
          // Reset pulse animation after 5 seconds
          setTimeout(() => setHasNewNotification(false), 5000)
        }
        
        setUnreadCount(newCount)
      }
    } catch (error) {
      console.error('Error fetching notification count:', error)
    }
  }

  const handleNotificationRead = () => {
    // Refresh count when a notification is read
    fetchUnreadCount()
  }

  const handleClose = () => {
    setIsOpen(false)
    setHasNewNotification(false)
  }

  if (!user) return null

  return (
    <>
      <div className="relative">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "relative",
            hasNewNotification && pulseOnNew && "animate-pulse",
            className
          )}
        >
          <Bell className="h-5 w-5" />
          {showBadge && unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </div>

      <NotificationPanel
        isOpen={isOpen}
        onClose={handleClose}
        onNotificationRead={handleNotificationRead}
      />
    </>
  )
}