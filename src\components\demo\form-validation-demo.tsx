'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ValidatedFormField, RequiredFieldIndicator, FormSection, FormErrorSummary } from '@/components/ui/validated-form-field';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useToast } from '@/hooks/use-toast';
import { 
  loginFormSchema, 
  registrationFormSchema, 
  projectFormSchema,
  type LoginFormData,
  type RegistrationFormData,
  type ProjectFormData
} from '@/lib/validation/form-utils';
import { Loader2 } from 'lucide-react';

export function FormValidationDemo() {
  const { toast } = useToast();

  // Login Form State
  const [loginData, setLoginData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false
  });

  // Registration Form State
  const [registrationData, setRegistrationData] = useState<RegistrationFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    acceptTerms: false
  });

  // Project Form State
  const [projectData, setProjectData] = useState<ProjectFormData>({
    title: '',
    description: '',
    genre: 'fantasy',
    status: 'planning',
    targetWordCount: 50000,
    targetChapters: 20,
    tags: []
  });

  // Form validation hooks
  const loginValidation = useFormValidation({
    schema: loginFormSchema,
    validateOnBlur: true,
    validateOnChange: false
  });

  const registrationValidation = useFormValidation({
    schema: registrationFormSchema,
    validateOnBlur: true,
    validateOnChange: true,
    debounceMs: 500
  });

  const projectValidation = useFormValidation({
    schema: projectFormSchema,
    validateOnBlur: true
  });

  // Submit handlers
  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = await loginValidation.validateForm(loginData);
    if (!validation.isValid) return;

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    toast({
      title: 'Login successful',
      description: 'Welcome back to BookScribe!'
    });
  };

  const handleRegistrationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = await registrationValidation.validateForm(registrationData);
    if (!validation.isValid) return;

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    toast({
      title: 'Account created',
      description: 'Welcome to BookScribe! Check your email to verify your account.'
    });
  };

  const handleProjectSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = await projectValidation.validateForm(projectData);
    if (!validation.isValid) return;

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    toast({
      title: 'Project created',
      description: `"${projectData.title}" has been created successfully.`
    });
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Form Validation Patterns</h1>
        <p className="text-muted-foreground">
          Standardized form validation examples for BookScribe
        </p>
      </div>

      <Tabs defaultValue="login" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="login">Login Form</TabsTrigger>
          <TabsTrigger value="registration">Registration Form</TabsTrigger>
          <TabsTrigger value="project">Project Form</TabsTrigger>
        </TabsList>

        {/* Login Form Example */}
        <TabsContent value="login">
          <Card>
            <CardHeader>
              <CardTitle>Login Form</CardTitle>
              <CardDescription>
                Simple form with email and password validation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLoginSubmit} className="space-y-6 max-w-md">
                <FormErrorSummary errors={loginValidation.errors} />
                
                <ValidatedFormField
                  id="login-email"
                  type="email"
                  label="Email"
                  value={loginData.email}
                  onChange={(value) => setLoginData({ ...loginData, email: value })}
                  onBlur={() => loginValidation.handleFieldBlur('email', loginData.email, loginData)}
                  error={loginValidation.getFieldError('email')}
                  touched={loginValidation.isFieldTouched('email')}
                  required
                  placeholder="<EMAIL>"
                  autoComplete="email"
                />

                <ValidatedFormField
                  id="login-password"
                  type="password"
                  label="Password"
                  value={loginData.password}
                  onChange={(value) => setLoginData({ ...loginData, password: value })}
                  onBlur={() => loginValidation.handleFieldBlur('password', loginData.password, loginData)}
                  error={loginValidation.getFieldError('password')}
                  touched={loginValidation.isFieldTouched('password')}
                  required
                  placeholder="Enter your password"
                  autoComplete="current-password"
                />

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="remember-me"
                    checked={loginData.rememberMe}
                    onChange={(e) => setLoginData({ ...loginData, rememberMe: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="remember-me" className="text-sm">
                    Remember me
                  </label>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={loginValidation.isValidating}
                >
                  {loginValidation.isValidating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Registration Form Example */}
        <TabsContent value="registration">
          <Card>
            <CardHeader>
              <CardTitle>Registration Form</CardTitle>
              <CardDescription>
                Complex form with password confirmation and terms acceptance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleRegistrationSubmit} className="space-y-6 max-w-md">
                <RequiredFieldIndicator />
                <FormErrorSummary errors={registrationValidation.errors} />
                
                <FormSection title="Account Information">
                  <ValidatedFormField
                    id="reg-email"
                    type="email"
                    label="Email"
                    value={registrationData.email}
                    onChange={(value) => {
                      setRegistrationData({ ...registrationData, email: value });
                      registrationValidation.handleFieldChange('email', value, { ...registrationData, email: value });
                    }}
                    onBlur={() => registrationValidation.handleFieldBlur('email', registrationData.email, registrationData)}
                    error={registrationValidation.getFieldError('email')}
                    touched={registrationValidation.isFieldTouched('email')}
                    required
                    placeholder="<EMAIL>"
                    description="We'll use this for login and notifications"
                  />

                  <ValidatedFormField
                    id="reg-username"
                    type="text"
                    label="Username (Optional)"
                    value={registrationData.username || ''}
                    onChange={(value) => setRegistrationData({ ...registrationData, username: value })}
                    onBlur={() => registrationValidation.handleFieldBlur('username', registrationData.username, registrationData)}
                    error={registrationValidation.getFieldError('username')}
                    touched={registrationValidation.isFieldTouched('username')}
                    placeholder="johndoe123"
                    description="3-20 characters, letters, numbers, hyphens, and underscores only"
                  />
                </FormSection>

                <FormSection title="Security">
                  <ValidatedFormField
                    id="reg-password"
                    type="password"
                    label="Password"
                    value={registrationData.password}
                    onChange={(value) => {
                      setRegistrationData({ ...registrationData, password: value });
                      registrationValidation.handleFieldChange('password', value, { ...registrationData, password: value });
                    }}
                    onBlur={() => registrationValidation.handleFieldBlur('password', registrationData.password, registrationData)}
                    error={registrationValidation.getFieldError('password')}
                    touched={registrationValidation.isFieldTouched('password')}
                    required
                    placeholder="Create a strong password"
                    description="At least 8 characters with uppercase, lowercase, and numbers"
                  />

                  <ValidatedFormField
                    id="reg-confirm-password"
                    type="password"
                    label="Confirm Password"
                    value={registrationData.confirmPassword}
                    onChange={(value) => {
                      setRegistrationData({ ...registrationData, confirmPassword: value });
                      registrationValidation.handleFieldChange('confirmPassword', value, { ...registrationData, confirmPassword: value });
                    }}
                    onBlur={() => registrationValidation.handleFieldBlur('confirmPassword', registrationData.confirmPassword, registrationData)}
                    error={registrationValidation.getFieldError('confirmPassword')}
                    touched={registrationValidation.isFieldTouched('confirmPassword')}
                    required
                    placeholder="Re-enter your password"
                  />
                </FormSection>

                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <input
                      type="checkbox"
                      id="accept-terms"
                      checked={registrationData.acceptTerms}
                      onChange={(e) => setRegistrationData({ ...registrationData, acceptTerms: e.target.checked })}
                      className="rounded border-gray-300 mt-1"
                      aria-describedby="terms-error"
                    />
                    <label htmlFor="accept-terms" className="text-sm">
                      I accept the <a href="#" className="text-primary hover:underline">Terms and Conditions</a> and <a href="#" className="text-primary hover:underline">Privacy Policy</a>
                    </label>
                  </div>
                  {registrationValidation.getFieldError('acceptTerms') && (
                    <p id="terms-error" className="text-sm text-destructive">
                      {registrationValidation.getFieldError('acceptTerms')}
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={registrationValidation.isValidating}
                >
                  {registrationValidation.isValidating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Project Form Example */}
        <TabsContent value="project">
          <Card>
            <CardHeader>
              <CardTitle>Project Form</CardTitle>
              <CardDescription>
                Multi-section form with various field types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProjectSubmit} className="space-y-6 max-w-2xl">
                <RequiredFieldIndicator />
                <FormErrorSummary errors={projectValidation.errors} />
                
                <FormSection 
                  title="Basic Information" 
                  description="Tell us about your writing project"
                >
                  <ValidatedFormField
                    id="project-title"
                    type="text"
                    label="Project Title"
                    value={projectData.title}
                    onChange={(value) => setProjectData({ ...projectData, title: value })}
                    onBlur={() => projectValidation.handleFieldBlur('title', projectData.title, projectData)}
                    error={projectValidation.getFieldError('title')}
                    touched={projectValidation.isFieldTouched('title')}
                    required
                    placeholder="Enter your project title"
                  />

                  <ValidatedFormField
                    id="project-description"
                    type="textarea"
                    label="Description"
                    value={projectData.description || ''}
                    onChange={(value) => setProjectData({ ...projectData, description: value })}
                    onBlur={() => projectValidation.handleFieldBlur('description', projectData.description, projectData)}
                    error={projectValidation.getFieldError('description')}
                    touched={projectValidation.isFieldTouched('description')}
                    placeholder="Brief description of your project..."
                    rows={4}
                    maxLength={1000}
                    showCharacterCount
                  />
                </FormSection>

                <FormSection title="Project Details">
                  <div className="grid grid-cols-2 gap-4">
                    <ValidatedFormField
                      id="project-genre"
                      type="select"
                      label="Genre"
                      value={projectData.genre}
                      onChange={(value) => setProjectData({ ...projectData, genre: value as any })}
                      onBlur={() => projectValidation.handleFieldBlur('genre', projectData.genre, projectData)}
                      error={projectValidation.getFieldError('genre')}
                      touched={projectValidation.isFieldTouched('genre')}
                      required
                      options={[
                        { value: 'fantasy', label: 'Fantasy' },
                        { value: 'science-fiction', label: 'Science Fiction' },
                        { value: 'mystery', label: 'Mystery' },
                        { value: 'romance', label: 'Romance' },
                        { value: 'thriller', label: 'Thriller' },
                        { value: 'horror', label: 'Horror' },
                        { value: 'historical', label: 'Historical' },
                        { value: 'literary', label: 'Literary' },
                        { value: 'young-adult', label: 'Young Adult' },
                        { value: 'non-fiction', label: 'Non-Fiction' },
                        { value: 'other', label: 'Other' }
                      ]}
                    />

                    <ValidatedFormField
                      id="project-status"
                      type="select"
                      label="Status"
                      value={projectData.status}
                      onChange={(value) => setProjectData({ ...projectData, status: value as any })}
                      onBlur={() => projectValidation.handleFieldBlur('status', projectData.status, projectData)}
                      error={projectValidation.getFieldError('status')}
                      touched={projectValidation.isFieldTouched('status')}
                      required
                      options={[
                        { value: 'planning', label: 'Planning' },
                        { value: 'writing', label: 'Writing' },
                        { value: 'editing', label: 'Editing' },
                        { value: 'completed', label: 'Completed' },
                        { value: 'on-hold', label: 'On Hold' }
                      ]}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <ValidatedFormField
                      id="project-word-count"
                      type="number"
                      label="Target Word Count"
                      value={projectData.targetWordCount || ''}
                      onChange={(value) => setProjectData({ ...projectData, targetWordCount: parseInt(value) || undefined })}
                      onBlur={() => projectValidation.handleFieldBlur('targetWordCount', projectData.targetWordCount, projectData)}
                      error={projectValidation.getFieldError('targetWordCount')}
                      touched={projectValidation.isFieldTouched('targetWordCount')}
                      placeholder="50000"
                      min={1000}
                      max={500000}
                      step={1000}
                    />

                    <ValidatedFormField
                      id="project-chapters"
                      type="number"
                      label="Target Chapters"
                      value={projectData.targetChapters || ''}
                      onChange={(value) => setProjectData({ ...projectData, targetChapters: parseInt(value) || undefined })}
                      onBlur={() => projectValidation.handleFieldBlur('targetChapters', projectData.targetChapters, projectData)}
                      error={projectValidation.getFieldError('targetChapters')}
                      touched={projectValidation.isFieldTouched('targetChapters')}
                      placeholder="20"
                      min={1}
                      max={999}
                    />
                  </div>
                </FormSection>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={projectValidation.isValidating}
                >
                  {projectValidation.isValidating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Project...
                    </>
                  ) : (
                    'Create Project'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Best Practices */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Form Validation Best Practices</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>• Always indicate required fields with an asterisk (*)</p>
          <p>• Validate on blur for immediate feedback</p>
          <p>• Show errors only after the field has been touched</p>
          <p>• Provide helpful error messages that explain how to fix the issue</p>
          <p>• Use proper input types (email, tel, url) for better mobile experience</p>
          <p>• Include field descriptions for complex inputs</p>
          <p>• Group related fields into sections</p>
          <p>• Show a summary of all errors at the top of the form</p>
          <p>• Disable submit button while validating</p>
          <p>• Use appropriate ARIA attributes for accessibility</p>
        </CardContent>
      </Card>
    </div>
  );
}