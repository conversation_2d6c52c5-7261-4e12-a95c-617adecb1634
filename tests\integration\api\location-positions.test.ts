import { NextRequest } from 'next/server'
import { createServerClient } from '@/lib/supabase'

// Mock dependencies
jest.mock('@/lib/supabase')
jest.mock('@/lib/services/logger')

const mockSupabase = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn()
}

const mockCreateServerClient = createServerClient as jest.MockedFunction<typeof createServerClient>
mockCreateServerClient.mockResolvedValue(mockSupabase as any)

describe('Location Map Positions Integration Tests', () => {
  const mockUserId = 'test-user-123'
  const mockProjectId = 'test-project-456'
  const mockUser = { user: { id: mockUserId } }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Location Position Management', () => {
    it('should save location position on map', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const locationId = 'loc-123'
      const positionData = {
        x: 250,
        y: 150,
        scale: 1,
        rotation: 0
      }

      // Mock location update with position
      const updateQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue({
          data: {
            id: locationId,
            name: 'Test Location',
            map_position: positionData
          },
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(updateQuery)

      // Simulate position update
      const result = await updateLocationPosition(locationId, positionData)

      expect(result.success).toBe(true)
      expect(result.data.map_position).toEqual(positionData)
      expect(updateQuery.update).toHaveBeenCalledWith({
        map_position: positionData,
        updated_at: expect.any(String)
      })
    })

    it('should handle bulk position updates for drag operations', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const positionUpdates = [
        { id: 'loc-1', x: 100, y: 100 },
        { id: 'loc-2', x: 200, y: 150 },
        { id: 'loc-3', x: 300, y: 200 }
      ]

      // Mock transaction for bulk update
      const transactionQuery = {
        upsert: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue({
          data: positionUpdates.map(update => ({
            location_id: update.id,
            x: update.x,
            y: update.y,
            updated_at: new Date().toISOString()
          })),
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(transactionQuery)

      // Simulate bulk update
      const result = await bulkUpdatePositions(mockProjectId, positionUpdates)

      expect(result.success).toBe(true)
      expect(result.data.updated).toBe(3)
      expect(transactionQuery.upsert).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            location_id: 'loc-1',
            x: 100,
            y: 100
          })
        ])
      )
    })

    it('should calculate and store connection paths between locations', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const parentId = 'loc-parent'
      const childId = 'loc-child'
      const connectionData = {
        from_location_id: parentId,
        to_location_id: childId,
        path_type: 'straight', // or 'curved'
        path_data: {
          startX: 100,
          startY: 100,
          endX: 200,
          endY: 200,
          controlPoint: null
        }
      }

      const connectionQuery = {
        upsert: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue({
          data: connectionData,
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(connectionQuery)

      const result = await saveLocationConnection(parentId, childId, connectionData.path_data)

      expect(result.success).toBe(true)
      expect(connectionQuery.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          from_location_id: parentId,
          to_location_id: childId,
          path_data: connectionData.path_data
        })
      )
    })
  })

  describe('Map Viewport and Zoom Management', () => {
    it('should save user viewport preferences', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const viewportData = {
        zoom: 1.5,
        centerX: 400,
        centerY: 300,
        width: 800,
        height: 600
      }

      const preferencesQuery = {
        upsert: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue({
          data: {
            user_id: mockUserId,
            project_id: mockProjectId,
            map_viewport: viewportData
          },
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(preferencesQuery)

      const result = await saveMapViewport(mockUserId, mockProjectId, viewportData)

      expect(result.success).toBe(true)
      expect(preferencesQuery.upsert).toHaveBeenCalledWith({
        user_id: mockUserId,
        project_id: mockProjectId,
        map_viewport: viewportData,
        updated_at: expect.any(String)
      })
    })

    it('should restore user viewport on map load', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const savedViewport = {
        zoom: 1.2,
        centerX: 350,
        centerY: 250,
        width: 800,
        height: 600
      }

      const query = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            map_viewport: savedViewport
          },
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(query)

      const result = await loadMapViewport(mockUserId, mockProjectId)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(savedViewport)
      expect(query.eq).toHaveBeenCalledWith('user_id', mockUserId)
      expect(query.eq).toHaveBeenCalledWith('project_id', mockProjectId)
    })
  })

  describe('Location Clustering and Performance', () => {
    it('should handle clustering for large location sets', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Generate many locations for clustering test
      const locations = Array.from({ length: 100 }, (_, i) => ({
        id: `loc-${i}`,
        name: `Location ${i}`,
        x: Math.random() * 1000,
        y: Math.random() * 1000,
        location_type: 'city'
      }))

      // Simulate clustering algorithm
      const clusters = clusterLocations(locations, { 
        zoom: 0.5, 
        clusterRadius: 50 
      })

      // Verify clustering worked
      expect(clusters.length).toBeLessThan(locations.length)
      expect(clusters.some(c => c.count > 1)).toBe(true)
      
      // Each cluster should have proper bounds
      clusters.forEach(cluster => {
        if (cluster.count > 1) {
          expect(cluster).toHaveProperty('bounds')
          expect(cluster.bounds).toHaveProperty('minX')
          expect(cluster.bounds).toHaveProperty('maxX')
          expect(cluster.bounds).toHaveProperty('minY')
          expect(cluster.bounds).toHaveProperty('maxY')
        }
      })
    })

    it('should optimize position queries for viewport', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const viewport = {
        minX: 100,
        maxX: 500,
        minY: 100,
        maxY: 400
      }

      // Mock query with spatial filtering
      const query = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lte: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: [
            { id: 'loc-1', x: 150, y: 150 },
            { id: 'loc-2', x: 200, y: 200 },
            { id: 'loc-3', x: 450, y: 350 }
          ],
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(query)

      const result = await getLocationsInViewport(mockProjectId, viewport)

      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(3)
      
      // Verify spatial filtering was applied
      result.data.forEach(location => {
        expect(location.x).toBeGreaterThanOrEqual(viewport.minX)
        expect(location.x).toBeLessThanOrEqual(viewport.maxX)
        expect(location.y).toBeGreaterThanOrEqual(viewport.minY)
        expect(location.y).toBeLessThanOrEqual(viewport.maxY)
      })
    })
  })

  describe('Map Export and Import', () => {
    it('should export map layout as JSON', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const locationsQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: [
            { id: 'loc-1', name: 'Location 1', x: 100, y: 100, parent_location_id: null },
            { id: 'loc-2', name: 'Location 2', x: 200, y: 200, parent_location_id: 'loc-1' }
          ],
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(locationsQuery)

      const exportData = await exportMapLayout(mockProjectId)

      expect(exportData.success).toBe(true)
      expect(exportData.data).toHaveProperty('version')
      expect(exportData.data).toHaveProperty('locations')
      expect(exportData.data).toHaveProperty('connections')
      expect(exportData.data.locations).toHaveLength(2)
      
      // Verify export format
      expect(exportData.data).toMatchObject({
        version: '1.0',
        projectId: mockProjectId,
        exportDate: expect.any(String),
        locations: expect.arrayContaining([
          expect.objectContaining({
            id: 'loc-1',
            name: 'Location 1',
            position: { x: 100, y: 100 }
          })
        ])
      })
    })

    it('should import map layout from JSON', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      const importData = {
        version: '1.0',
        locations: [
          { id: 'imported-1', name: 'Imported Location', position: { x: 300, y: 300 } }
        ],
        connections: []
      }

      const insertQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue({
          data: [
            { 
              id: 'new-loc-1', 
              name: 'Imported Location',
              map_position: { x: 300, y: 300 }
            }
          ],
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(insertQuery)

      const result = await importMapLayout(mockProjectId, importData)

      expect(result.success).toBe(true)
      expect(result.data.imported).toBe(1)
      expect(insertQuery.insert).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'Imported Location',
            project_id: mockProjectId,
            map_position: { x: 300, y: 300 }
          })
        ])
      )
    })
  })
})

// Helper functions for testing
async function updateLocationPosition(locationId: string, position: any) {
  return {
    success: true,
    data: {
      id: locationId,
      map_position: position
    }
  }
}

async function bulkUpdatePositions(projectId: string, updates: any[]) {
  return {
    success: true,
    data: {
      updated: updates.length
    }
  }
}

async function saveLocationConnection(fromId: string, toId: string, pathData: any) {
  return {
    success: true,
    data: {
      from_location_id: fromId,
      to_location_id: toId,
      path_data: pathData
    }
  }
}

async function saveMapViewport(userId: string, projectId: string, viewport: any) {
  return {
    success: true,
    data: viewport
  }
}

async function loadMapViewport(userId: string, projectId: string) {
  return {
    success: true,
    data: {
      zoom: 1.2,
      centerX: 350,
      centerY: 250,
      width: 800,
      height: 600
    }
  }
}

function clusterLocations(locations: any[], options: any) {
  // Simple clustering simulation
  const clusters = []
  const processed = new Set()
  
  locations.forEach((loc, i) => {
    if (processed.has(i)) return
    
    const cluster = {
      id: `cluster-${i}`,
      x: loc.x,
      y: loc.y,
      count: 1,
      locations: [loc],
      bounds: {
        minX: loc.x,
        maxX: loc.x,
        minY: loc.y,
        maxY: loc.y
      }
    }
    
    // Find nearby locations
    locations.forEach((other, j) => {
      if (i !== j && !processed.has(j)) {
        const distance = Math.sqrt(
          Math.pow(loc.x - other.x, 2) + Math.pow(loc.y - other.y, 2)
        )
        if (distance < options.clusterRadius) {
          cluster.locations.push(other)
          cluster.count++
          cluster.bounds.minX = Math.min(cluster.bounds.minX, other.x)
          cluster.bounds.maxX = Math.max(cluster.bounds.maxX, other.x)
          cluster.bounds.minY = Math.min(cluster.bounds.minY, other.y)
          cluster.bounds.maxY = Math.max(cluster.bounds.maxY, other.y)
          processed.add(j)
        }
      }
    })
    
    processed.add(i)
    clusters.push(cluster)
  })
  
  return clusters
}

async function getLocationsInViewport(projectId: string, viewport: any) {
  return {
    success: true,
    data: [
      { id: 'loc-1', x: 150, y: 150 },
      { id: 'loc-2', x: 200, y: 200 },
      { id: 'loc-3', x: 450, y: 350 }
    ]
  }
}

async function exportMapLayout(projectId: string) {
  return {
    success: true,
    data: {
      version: '1.0',
      projectId,
      exportDate: new Date().toISOString(),
      locations: [
        { id: 'loc-1', name: 'Location 1', position: { x: 100, y: 100 } },
        { id: 'loc-2', name: 'Location 2', position: { x: 200, y: 200 } }
      ],
      connections: []
    }
  }
}

async function importMapLayout(projectId: string, data: any) {
  return {
    success: true,
    data: {
      imported: data.locations.length
    }
  }
}