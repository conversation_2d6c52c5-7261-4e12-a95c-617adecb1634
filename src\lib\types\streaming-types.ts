/**
 * Typed Interfaces for Streaming Responses
 * Provides comprehensive type definitions for all streaming operations in BookScribe
 */

import { z } from 'zod';

// Base streaming types
export interface StreamToken {
  token: string;
  timestamp: number;
  index: number;
}

export interface StreamMetadata {
  model: string;
  temperature: number;
  maxTokens: number;
  streamId: string;
  startTime: number;
}

export interface StreamProgress {
  tokens: number;
  estimatedTokens: number;
  percentComplete: number;
  tokensPerSecond: number;
  estimatedTimeRemaining: number;
}

export interface StreamError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  recoverable: boolean;
}

// Stream event types
export enum StreamEventType {
  START = 'start',
  TOKEN = 'token',
  PROGRESS = 'progress',
  METADATA = 'metadata',
  QUALITY = 'quality',
  STRUCTURE = 'structure',
  COMPLETE = 'complete',
  ERROR = 'error',
  ABORT = 'abort'
}

// Base stream event
export interface BaseStreamEvent {
  type: StreamEventType;
  timestamp: number;
  streamId: string;
}

// Specific stream events
export interface StreamStartEvent extends BaseStreamEvent {
  type: StreamEventType.START;
  metadata: StreamMetadata;
}

export interface StreamTokenEvent extends BaseStreamEvent {
  type: StreamEventType.TOKEN;
  data: StreamToken;
}

export interface StreamProgressEvent extends BaseStreamEvent {
  type: StreamEventType.PROGRESS;
  data: StreamProgress;
}

export interface StreamMetadataEvent extends BaseStreamEvent {
  type: StreamEventType.METADATA;
  data: Record<string, unknown>;
}

export interface StreamQualityEvent extends BaseStreamEvent {
  type: StreamEventType.QUALITY;
  data: {
    score: number;
    metrics: {
      coherence: number;
      consistency: number;
      engagement: number;
      voiceAdherence: number;
    };
  };
}

export interface StreamStructureEvent<T = unknown> extends BaseStreamEvent {
  type: StreamEventType.STRUCTURE;
  data: {
    partial: boolean;
    structure: T;
  };
}

export interface StreamCompleteEvent extends BaseStreamEvent {
  type: StreamEventType.COMPLETE;
  data: {
    content: string;
    tokens: number;
    duration: number;
    metadata?: Record<string, unknown>;
  };
}

export interface StreamErrorEvent extends BaseStreamEvent {
  type: StreamEventType.ERROR;
  data: StreamError;
}

export interface StreamAbortEvent extends BaseStreamEvent {
  type: StreamEventType.ABORT;
  data: {
    reason: string;
    partial: string;
  };
}

// Union type for all stream events
export type StreamEvent = 
  | StreamStartEvent
  | StreamTokenEvent
  | StreamProgressEvent
  | StreamMetadataEvent
  | StreamQualityEvent
  | StreamStructureEvent
  | StreamCompleteEvent
  | StreamErrorEvent
  | StreamAbortEvent;

// Content generation specific types
export interface ContentGenerationRequest {
  prompt: string;
  systemPrompt?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  estimatedTokens?: number;
  contentType?: ContentType;
  context?: ContentContext;
  streaming?: boolean;
}

export enum ContentType {
  CHAPTER = 'chapter',
  SCENE = 'scene',
  DIALOGUE = 'dialogue',
  DESCRIPTION = 'description',
  CHARACTER = 'character',
  OUTLINE = 'outline',
  SYNOPSIS = 'synopsis',
  GENERAL = 'general'
}

export interface ContentContext {
  projectId: string;
  chapterId?: string;
  characterIds?: string[];
  previousContent?: string;
  storyBible?: Record<string, unknown>;
  voiceProfile?: VoiceProfile;
}

export interface VoiceProfile {
  tone: string;
  style: string;
  vocabulary: string[];
  sentencePatterns: string[];
  narrativePerspective: 'first' | 'second' | 'third-limited' | 'third-omniscient';
}

// Structured content streaming
export interface StructuredStreamResponse<T> {
  content: string;
  structured: T;
  metadata: {
    model: string;
    tokens: number;
    quality?: number;
    processingTime: number;
  };
}

// Chapter generation streaming
export interface ChapterStreamData {
  title: string;
  content: string;
  scenes: SceneData[];
  wordCount: number;
  readingTime: number;
  characters: string[];
  locations: string[];
  plotPoints: PlotPoint[];
}

export interface SceneData {
  id: string;
  content: string;
  characters: string[];
  location: string;
  mood: string;
  conflict?: string;
}

export interface PlotPoint {
  type: 'setup' | 'confrontation' | 'resolution' | 'cliffhanger';
  description: string;
  impact: 'low' | 'medium' | 'high';
}

// Character development streaming
export interface CharacterStreamData {
  profile: CharacterProfile;
  dialogue: DialogueSample[];
  relationships: CharacterRelationship[];
  arc: CharacterArc;
}

export interface CharacterProfile {
  name: string;
  age?: number;
  description: string;
  personality: string[];
  motivations: string[];
  fears: string[];
  backstory: string;
  voice: VoiceProfile;
}

export interface DialogueSample {
  context: string;
  line: string;
  emotion: string;
}

export interface CharacterRelationship {
  characterId: string;
  characterName: string;
  relationship: string;
  dynamic: string;
}

export interface CharacterArc {
  start: string;
  middle: string;
  end: string;
  growth: string;
}

// Analysis streaming
export interface AnalysisStreamData {
  type: AnalysisType;
  results: AnalysisResult[];
  summary: string;
  suggestions: Suggestion[];
  overallScore: number;
}

export enum AnalysisType {
  VOICE_CONSISTENCY = 'voice_consistency',
  PLOT_COHERENCE = 'plot_coherence',
  CHARACTER_DEVELOPMENT = 'character_development',
  PACING = 'pacing',
  DIALOGUE_QUALITY = 'dialogue_quality',
  WORLD_BUILDING = 'world_building'
}

export interface AnalysisResult {
  category: string;
  score: number;
  details: string;
  examples?: string[];
}

export interface Suggestion {
  type: 'improvement' | 'warning' | 'error';
  category: string;
  message: string;
  location?: {
    chapter?: number;
    paragraph?: number;
    line?: number;
  };
  fix?: string;
}

// Agent streaming
export interface AgentStreamData {
  agentId: string;
  agentType: AgentType;
  status: AgentStatus;
  output: unknown;
  handoff?: AgentHandoff;
}

export enum AgentType {
  STORY_ARCHITECT = 'story_architect',
  CHARACTER_DEVELOPER = 'character_developer',
  CHAPTER_PLANNER = 'chapter_planner',
  WRITING_AGENT = 'writing_agent',
  EDITOR_AGENT = 'editor_agent',
  ANALYST_AGENT = 'analyst_agent'
}

export enum AgentStatus {
  INITIALIZING = 'initializing',
  PROCESSING = 'processing',
  GENERATING = 'generating',
  REVIEWING = 'reviewing',
  COMPLETE = 'complete',
  ERROR = 'error'
}

export interface AgentHandoff {
  fromAgent: string;
  toAgent: string;
  data: Record<string, unknown>;
  instructions: string;
}

// Stream handler interface
export interface StreamHandler<T = unknown> {
  onStart?: (metadata: StreamMetadata) => void;
  onToken?: (token: StreamToken) => void;
  onProgress?: (progress: StreamProgress) => void;
  onQuality?: (quality: number, metrics: Record<string, number>) => void;
  onStructure?: (structure: T, partial: boolean) => void;
  onComplete?: (content: string, metadata?: Record<string, unknown>) => void;
  onError?: (error: StreamError) => void;
  onAbort?: (reason: string, partial: string) => void;
}

// SSE (Server-Sent Events) types
export interface SSEMessage {
  id?: string;
  event?: string;
  data: string;
  retry?: number;
}

export interface SSEStreamOptions {
  url: string;
  method?: 'GET' | 'POST';
  headers?: Record<string, string>;
  body?: unknown;
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Error) => void;
  onClose?: () => void;
  signal?: AbortSignal;
}

// Validation schemas
export const streamTokenSchema = z.object({
  token: z.string(),
  timestamp: z.number(),
  index: z.number()
});

export const streamProgressSchema = z.object({
  tokens: z.number(),
  estimatedTokens: z.number(),
  percentComplete: z.number().min(0).max(100),
  tokensPerSecond: z.number(),
  estimatedTimeRemaining: z.number()
});

export const streamEventSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal(StreamEventType.START),
    timestamp: z.number(),
    streamId: z.string(),
    metadata: z.object({
      model: z.string(),
      temperature: z.number(),
      maxTokens: z.number(),
      streamId: z.string(),
      startTime: z.number()
    })
  }),
  z.object({
    type: z.literal(StreamEventType.TOKEN),
    timestamp: z.number(),
    streamId: z.string(),
    data: streamTokenSchema
  }),
  z.object({
    type: z.literal(StreamEventType.PROGRESS),
    timestamp: z.number(),
    streamId: z.string(),
    data: streamProgressSchema
  }),
  z.object({
    type: z.literal(StreamEventType.COMPLETE),
    timestamp: z.number(),
    streamId: z.string(),
    data: z.object({
      content: z.string(),
      tokens: z.number(),
      duration: z.number(),
      metadata: z.record(z.unknown()).optional()
    })
  }),
  z.object({
    type: z.literal(StreamEventType.ERROR),
    timestamp: z.number(),
    streamId: z.string(),
    data: z.object({
      code: z.string(),
      message: z.string(),
      details: z.record(z.unknown()).optional(),
      recoverable: z.boolean()
    })
  })
]);

// Type guards
export function isStreamStartEvent(event: StreamEvent): event is StreamStartEvent {
  return event.type === StreamEventType.START;
}

export function isStreamTokenEvent(event: StreamEvent): event is StreamTokenEvent {
  return event.type === StreamEventType.TOKEN;
}

export function isStreamProgressEvent(event: StreamEvent): event is StreamProgressEvent {
  return event.type === StreamEventType.PROGRESS;
}

export function isStreamCompleteEvent(event: StreamEvent): event is StreamCompleteEvent {
  return event.type === StreamEventType.COMPLETE;
}

export function isStreamErrorEvent(event: StreamEvent): event is StreamErrorEvent {
  return event.type === StreamEventType.ERROR;
}

// Utility functions
export function createStreamId(): string {
  return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function calculateProgress(tokens: number, estimatedTokens: number): number {
  return Math.min(Math.round((tokens / estimatedTokens) * 100), 99);
}

export function estimateTimeRemaining(
  currentTokens: number,
  estimatedTokens: number,
  tokensPerSecond: number
): number {
  if (tokensPerSecond === 0) return Infinity;
  const remainingTokens = Math.max(0, estimatedTokens - currentTokens);
  return Math.round(remainingTokens / tokensPerSecond);
}