'use client'

import { useState, useEffect } from 'react'
import { GoalTracker } from '../components/goal-tracker'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from 'recharts'
import { Target, Plus, Trophy, TrendingUp } from 'lucide-react'
import { RecommendedAchievements } from '@/components/goals/recommended-achievements'
import { CustomGoalModal } from '@/components/goals/custom-goal-modal'
import { useAuth } from '@/contexts/auth-context'

interface Goal {
  id: string
  type: string
  title: string
  current: number
  target: number
  progress: number
  unit: string
  deadline: string
}

interface GoalsData {
  active: Goal[]
  completed: number
  completionRate: number
}

interface AnalyticsData {
  goals?: GoalsData
}

interface GoalsSectionProps {
  data: AnalyticsData | null
  isLoading: boolean
}

export function GoalsSection({ data, isLoading }: GoalsSectionProps) {
  const [showRecommendations, setShowRecommendations] = useState(false)
  const [showCustomGoalModal, setShowCustomGoalModal] = useState(false)
  const [refreshGoals, setRefreshGoals] = useState(0)
  const [showAllGoals, setShowAllGoals] = useState(false)
  const { user } = useAuth()
  
  const goals = data?.goals || {
    active: [],
    completed: 0,
    completionRate: 0
  }

  // Calculate goal statistics
  const totalGoals = goals.active.length + goals.completed
  const activeGoals = goals.active.length
  
  // Calculate completed goals this month
  const currentMonth = new Date().getMonth()
  const currentYear = new Date().getFullYear()
  const completedThisMonth = goals.completedThisMonth || 0
  
  const goalStats = [
    { name: 'Completed', value: goals.completed, color: 'hsl(var(--color-success))' },
    { name: 'In Progress', value: activeGoals, color: 'hsl(var(--primary))' },
    { name: 'Overdue', value: goals.active.filter((g) => new Date(g.deadline) < new Date()).length, color: 'hsl(var(--color-error))' }
  ]

  return (
    <>
      {/* Goal Statistics */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Goals</p>
                <p className="text-2xl font-bold">{totalGoals}</p>
              </div>
              <Target className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold text-success">{goals.completed}</p>
              </div>
              <Trophy className="h-8 w-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{goals.completionRate}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-info" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">{completedThisMonth}</p>
              </div>
              <Badge variant="secondary" className="text-xs">
                +{Math.round(completedThisMonth / Math.max(1, goals.completed) * 100)}%
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Goals and Stats */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Active Goals List */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Active Goals</h3>
            <Button size="sm" onClick={() => setShowCustomGoalModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Goal
            </Button>
          </div>
          <GoalTracker 
            goals={showAllGoals ? goals.active : goals.active.slice(0, 5)} 
            loading={isLoading}
            onRefresh={() => setRefreshGoals(prev => prev + 1)}
          />
          {goals.active.length > 5 && (
            <Button 
              variant="ghost" 
              className="w-full mt-4"
              onClick={() => setShowAllGoals(!showAllGoals)}
            >
              {showAllGoals ? `Show Less` : `View All Goals (${goals.active.length})`}
            </Button>
          )}
        </div>

        {/* Goal Distribution Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Goal Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={goalStats}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {goalStats.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Goal Achievement Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Achievement Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { date: '2024-01-15', goal: 'Complete Chapter 10', type: 'Chapter', achieved: true },
              { date: '2024-01-10', goal: 'Write 10,000 words', type: 'Word Count', achieved: true },
              { date: '2024-01-05', goal: '7-day writing streak', type: 'Streak', achieved: true },
              { date: '2024-01-01', goal: 'Start new project', type: 'Project', achieved: true }
            ].map((achievement, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-2 h-2 bg-success rounded-full" />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{achievement.goal}</p>
                      <p className="text-sm text-muted-foreground">{achievement.date}</p>
                    </div>
                    <Badge variant="secondary">{achievement.type}</Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Goal Recommendations */}
      {showRecommendations ? (
        <RecommendedAchievements
          userId={user?.id || ''}
          currentStats={{
            avgDailyWords: data?.avgDailyWords || 500,
            currentStreak: data?.currentStreak || 0,
            avgQualityScore: data?.qualityDimensions?.[0]?.score || 75,
            projectProgress: data?.projectProgress?.[0]?.value || 0
          }}
          onGoalsSelected={(selectedGoals) => {
            setShowRecommendations(false)
            setRefreshGoals(prev => prev + 1) // Trigger refresh
          }}
        />
      ) : (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Goal Recommendations</CardTitle>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => setShowRecommendations(true)}
              >
                View Personalized Recommendations
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Get AI-powered goal recommendations based on your writing patterns and progress.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Custom Goal Modal */}
      <CustomGoalModal
        open={showCustomGoalModal}
        onOpenChange={setShowCustomGoalModal}
        onGoalCreated={() => {
          setRefreshGoals(prev => prev + 1) // Trigger refresh
        }}
      />
    </>
  )
}