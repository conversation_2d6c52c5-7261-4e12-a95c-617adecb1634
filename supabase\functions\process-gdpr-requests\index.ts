import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.1';
import { corsHeaders } from '../_shared/cors.ts';

interface GDPRRequest {
  id: string;
  user_id: string;
  type: string;
  status: string;
  data_categories?: string[];
  metadata?: any;
}

serve(async (req) => {
  // Only allow scheduled or POST requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Initialize Supabase client
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Get all pending GDPR requests
    const { data: pendingRequests, error: fetchError } = await supabase
      .from('gdpr_requests')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: true })
      .limit(10); // Process in batches

    if (fetchError) throw fetchError;

    const results = [];

    for (const request of pendingRequests || []) {
      try {
        // Update status to in_progress
        await supabase
          .from('gdpr_requests')
          .update({ status: 'in_progress' })
          .eq('id', request.id);

        // Process based on type
        let result;
        switch (request.type) {
          case 'access':
            result = await processAccessRequest(supabase, request);
            break;
          case 'erasure':
            result = await processErasureRequest(supabase, request);
            break;
          case 'portability':
            result = await processPortabilityRequest(supabase, request);
            break;
          default:
            throw new Error(`Unknown request type: ${request.type}`);
        }

        // Update request as completed
        await supabase
          .from('gdpr_requests')
          .update({
            status: 'completed',
            processed_at: new Date().toISOString(),
            download_url: result?.downloadUrl
          })
          .eq('id', request.id);

        results.push({
          requestId: request.id,
          status: 'completed',
          result
        });

      } catch (error) {
        console.error(`Failed to process request ${request.id}:`, error);
        
        // Mark as failed
        await supabase
          .from('gdpr_requests')
          .update({
            status: 'rejected',
            metadata: { 
              ...request.metadata,
              error: error.message 
            }
          })
          .eq('id', request.id);

        results.push({
          requestId: request.id,
          status: 'failed',
          error: error.message
        });
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        processed: results.length,
        results
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    );

  } catch (error) {
    console.error('Failed to process GDPR requests:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});

async function processAccessRequest(supabase: any, request: GDPRRequest) {
  const userId = request.user_id;
  const categories = request.data_categories || ['all'];
  
  // Collect user data
  const userData: any = {
    requestId: request.id,
    generatedAt: new Date().toISOString(),
    categories: {}
  };

  // Personal information
  if (categories.includes('all') || categories.includes('personal_info')) {
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    userData.categories.personal_info = { profile };
  }

  // Content data
  if (categories.includes('all') || categories.includes('content_data')) {
    const { data: projects } = await supabase
      .from('projects')
      .select('*, chapters(*), characters(*)')
      .eq('user_id', userId);
    
    userData.categories.content_data = { projects };
  }

  // Store encrypted export
  const encryptedData = btoa(JSON.stringify(userData));
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

  const { data: exportRecord, error } = await supabase
    .from('gdpr_exports')
    .insert({
      user_id: userId,
      request_id: request.id,
      data: encryptedData,
      format: 'json',
      size_bytes: encryptedData.length,
      expires_at: expiresAt.toISOString()
    })
    .select()
    .single();

  if (error) throw error;

  return {
    downloadUrl: `/api/gdpr/download/${exportRecord.id}`
  };
}

async function processErasureRequest(supabase: any, request: GDPRRequest) {
  const userId = request.user_id;
  
  // Verify confirmation
  if (!request.metadata?.confirmed) {
    throw new Error('Erasure request requires confirmation');
  }

  // Create final backup before deletion
  await processAccessRequest(supabase, request);

  // Delete data in order of dependencies
  const tables = [
    'ai_suggestions',
    'chapter_versions',
    'chapters',
    'characters',
    'reference_materials',
    'projects',
    'writing_sessions',
    'user_analytics',
    'ai_usage_logs',
    'user_sessions',
    'email_logs',
    'notifications',
    'profiles'
  ];

  for (const table of tables) {
    await supabase
      .from(table)
      .delete()
      .eq('user_id', userId);
  }

  // Delete auth user
  await supabase.auth.admin.deleteUser(userId);

  return { message: 'Account deleted successfully' };
}

async function processPortabilityRequest(supabase: any, request: GDPRRequest) {
  // Similar to access request but in machine-readable format
  const result = await processAccessRequest(supabase, request);
  
  // Update format metadata
  await supabase
    .from('gdpr_exports')
    .update({ 
      format: 'json-ld',
      metadata: { portable: true }
    })
    .eq('request_id', request.id);

  return result;
}