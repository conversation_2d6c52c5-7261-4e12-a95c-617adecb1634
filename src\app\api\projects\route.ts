import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { aiProcessingQueue } from '@/lib/services/ai-processing-queue';
import type { ProjectSettings } from '@/lib/types/project-settings';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service';
import { RequestValidationMiddleware, withRequestValidation } from '@/lib/api/request-validation-middleware';
import { apiSchemas } from '@/lib/validation/api-schemas';
import { cacheKeys, getServerCache, setServerCache } from '@/lib/cache/server';
import { TIME_MS } from '@/lib/constants'
import { logger } from '@/lib/services/logger';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { orchestratorInstances } from '@/lib/agents/orchestrator-instances';
import { createTypedServerClient } from '@/lib/supabase';
import { UnifiedResponse } from '@/lib/api/unified-response';

export const GET = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'authenticated',
    rateLimitCost: 1,
    querySchema: apiSchemas.projects.query,
    maxRequestSize: 1024, // 1KB for GET requests
    allowedContentTypes: ['application/json'],
    validateOrigin: process.env.NODE_ENV === 'production',
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || []
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const user = request.user!;
    const userId = user.id;

    // Check cache first
    const cacheKey = cacheKeys.projectList(userId);
    const cached = getServerCache(cacheKey);
    if (cached) {
      return NextResponse.json({ projects: cached });
    }

    // Get all projects for the user
    const supabase = await createTypedServerClient();
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select(`*, chapters(count)`)
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (projectsError) throw projectsError;
    const projects = projectsData || [];

    // Get AI processing status for all projects at once (batch operation)
    const projectIds = projects.map(p => p.id);
    const allProcessingProgress = await aiProcessingQueue.getBatchProjectProgress(projectIds);

    // Map projects with their processing status
    const projectsWithProcessing = projects.map((project) => {
        const processingProgress = allProcessingProgress[project.id] || {
          projectId: project.id,
          currentTask: null,
          queuedTasks: [],
          completedTasks: [],
          totalTasks: 0,
          completedCount: 0,
          isActive: false,
          estimatedTimeRemaining: 0,
        };
        
        // Calculate progress metrics
        const chapters = project.chapters || [];
        const chapterCount = Array.isArray(chapters) ? chapters.length : chapters[0]?.count || 0;
        
        const wordsWritten = project.word_count || 0;
        const targetWords = project.target_word_count || 80000;
        const targetChapters = project.target_chapters || 20;
        
        return {
          id: project.id,
          name: project.name,
          description: project.description || '',
          genre: project.genre || 'Fiction',
          status: project.status || 'planning',
          progress: {
            wordsWritten,
            targetWords,
            chaptersComplete: chapterCount,
            targetChapters,
            percentComplete: Math.round((wordsWritten / targetWords) * 100),
          },
          aiProcessing: {
            isActive: processingProgress.isActive,
            currentTask: processingProgress.currentTask?.type,
            tasksInQueue: processingProgress.queuedTasks.length,
            lastProcessed: processingProgress.currentTask?.startedAt || 
              ((processingProgress.completedTasks && 
                Array.isArray(processingProgress.completedTasks) && 
                processingProgress.completedTasks.length > 0)
                  ? processingProgress.completedTasks[0]?.completedAt 
                  : undefined),
          },
          createdBy: 'Current User',
          metadata: {
            createdAt: new Date(project.created_at),
            updatedAt: new Date(project.updated_at),
            targetAudience: project.target_audience || 'Adult',
            contentRating: project.content_rating || 'PG-13',
            estimatedReadTime: Math.round(wordsWritten / 250), // 250 words per minute reading speed
          },
          stats: {
            dailyWords: project.daily_word_count || 0,
            weeklyWords: project.weekly_word_count || 0,
            streak: project.writing_streak || 0,
            avgWordsPerDay: project.avg_words_per_day || 0,
          },
          settings: project.project_settings?.[0]?.settings || null,
        };
      });

    // Cache the results for 5 minutes
    setServerCache(cacheKey, projectsWithProcessing, 5 * 60 * TIME_MS.SECOND);

    return UnifiedResponse.success({ projects: projectsWithProcessing });
  } catch (error) {
    logger.error('Projects GET error:', {
      error,
      userId: request.user?.id,
      clientIP: context.clientIP,
      query: context.query
    });
    return UnifiedResponse.error('Failed to fetch projects');
  }
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation with comprehensive security checks
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: apiSchemas.projects.create,
    rateLimitKey: 'authenticated',
    rateLimitCost: 5, // Higher cost for project creation
    maxBodySize: 50 * 1024, // 50KB max for project creation
    allowedContentTypes: ['application/json'],
    validateCSRF: process.env.NODE_ENV === 'production',
    validateOrigin: process.env.NODE_ENV === 'production',
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [],
    customValidator: async (req) => {
      // Custom validation for project creation limits
      const user = req.user;
      if (user) {
        const supabase = await createTypedServerClient();
        const { count: projectCount } = await supabase
          .from('projects')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);
        const maxProjects = user.subscription_tier === 'premium' ? 50 : 10;
        
        if (projectCount >= maxProjects) {
          return {
            valid: false,
            error: `Project limit reached. Maximum ${maxProjects} projects allowed for your subscription tier.`
          };
        }
      }
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const user = request.user!;
    const settings = context.body;
    const userId = user.id;

    const supabase = await createTypedServerClient();

    // Create project with validated data
    const { data: project, error: createError } = await supabase
      .from('projects')
      .insert({
      user_id: userId,
      title: settings.title,
      description: settings.description || '',
      genre: settings.genre,
      status: 'draft',
      target_word_count: settings.target_word_count || 80000,
      target_chapters: settings.target_chapters || 20,
      current_word_count: 0,
      is_playground: settings.is_playground || false,
    })
      .select()
      .single();

    if (createError) throw createError;

    // Log successful project creation
    logger.info('Project created successfully', {
      projectId: project.id,
      userId,
      title: settings.title,
      genre: settings.genre,
      clientIP: context.clientIP,
      userAgent: context.userAgent
    });

    // Save additional project settings if provided
    if (settings.settings) {
      try {
        await supabase
          .from('project_settings')
          .upsert({ project_id: project.id, settings: settings.settings });
      } catch (settingsError) {
        logger.warn('Failed to save project settings', {
          projectId: project.id,
          error: settingsError
        });
      }
    }

    // Invalidate the user's project list cache
    const cacheKey = cacheKeys.projectList(userId);
    setServerCache(cacheKey, null, 0); // Clear cache

    // Check if we should start orchestration immediately
    let orchestrationStarted = false;
    if (settings.startOrchestration && settings.storyPrompt && settings.projectSelections) {
      try {
        // Validate that all required fields are present for orchestration
        const { projectSelections, storyPrompt } = settings;
        if (
          projectSelections.genre &&
          projectSelections.tone &&
          projectSelections.style &&
          projectSelections.pov &&
          projectSelections.tense &&
          storyPrompt.length >= 10
        ) {
          // Create new orchestrator instance
          const orchestrator = new AdvancedAgentOrchestrator(3); // 3 concurrent tasks
          orchestratorInstances.set(project.id, orchestrator);

          // Set up event listeners for progress tracking
          orchestrator.on('orchestration:started', (data) => {
            logger.info(`Orchestration started for project ${project.id}:`, data);
          });

          orchestrator.on('task:completed', (data) => {
            logger.info(`Task completed: ${data.taskId} for project ${project.id}`);
          });

          orchestrator.on('orchestration:completed', async (data) => {
            logger.info(`Orchestration completed for project ${project.id}:`, data);
            
            // Clean up the instance after completion
            setTimeout(() => {
              orchestratorInstances.delete(project.id);
            }, 30000); // Keep for 30 seconds for final status checks
          });

          orchestrator.on('orchestration:cancelled', () => {
            logger.info(`Orchestration cancelled for project ${project.id}`);
            orchestratorInstances.delete(project.id);
          });

          // Start orchestration asynchronously
          const orchestrationPromise = orchestrator.orchestrateProject(
            project.id,
            projectSelections as ProjectSettings,
            storyPrompt,
            settings.target_word_count || 80000,
            settings.target_chapters || 20
          );

          // Don't await the orchestration - let it run in background
          orchestrationPromise.then(async (result) => {
            if (result.success && result.data) {
              // Store the completed book context in the database
              try {
                const supabase = await createTypedServerClient();
                const { error: updateError } = await supabase
                  .from('projects')
                  .update({
                    book_context: result.data,
                    status: 'outlined',
                    updated_at: new Date().toISOString()
                  })
                  .eq('id', project.id);

                if (updateError) {
                  logger.error('Failed to update project with book context:', updateError);
                } else {
                  logger.info(`Book context saved for project ${project.id}`);
                }
              } catch (error) {
                logger.error('Error saving book context:', error);
              }
            }
          }).catch((error) => {
            logger.error(`Orchestration failed for project ${project.id}:`, error);
          });

          orchestrationStarted = true;
          
          logger.info('Orchestration started with project creation', {
            projectId: project.id,
            userId,
            wordCount: settings.target_word_count || 80000,
            chapters: settings.target_chapters || 20,
            clientIP: context.clientIP
          });
        }
      } catch (orchestrationError) {
        logger.error('Failed to start orchestration with project creation', {
          projectId: project.id,
          error: orchestrationError,
          userId,
          clientIP: context.clientIP
        });
        // Don't fail the project creation if orchestration fails to start
      }
    }

    return NextResponse.json({ 
      success: true,
      project: {
        id: project.id,
        title: project.title,
        description: project.description,
        genre: project.genre,
        status: project.status,
        target_word_count: project.target_word_count,
        target_chapters: project.target_chapters,
        is_playground: project.is_playground,
        created_at: project.created_at,
      },
      orchestration: orchestrationStarted ? {
        started: true,
        message: 'AI generation started',
        estimatedDuration: '10-15 minutes',
        progressEndpoint: `/api/orchestration/progress?projectId=${project.id}`
      } : null
    }, { status: 201 });
  } catch (error) {
    logger.error('Projects POST error:', {
      error,
      userId: request.user?.id,
      clientIP: context.clientIP,
      requestBody: context.body
    });
    return UnifiedResponse.error(
      'Failed to create project',
      500,
      { details: error instanceof Error ? error.message : 'Unknown error' }
    );
  }
})