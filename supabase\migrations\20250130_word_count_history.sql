-- Create word_count_history table for tracking daily word counts
CREATE TABLE IF NOT EXISTS public.word_count_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    word_count INTEGER NOT NULL DEFAULT 0,
    session_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, date)
);

-- Add RLS policies
ALTER TABLE public.word_count_history ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own word count history
CREATE POLICY "Users can view own word count history" ON public.word_count_history
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own word count history
CREATE POLICY "Users can insert own word count history" ON public.word_count_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own word count history
CREATE POLICY "Users can update own word count history" ON public.word_count_history
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own word count history
CREATE POLICY "Users can delete own word count history" ON public.word_count_history
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_word_count_history_project_date ON public.word_count_history(project_id, date);
CREATE INDEX idx_word_count_history_user_date ON public.word_count_history(user_id, date);
CREATE INDEX idx_word_count_history_date ON public.word_count_history(date);

-- Create function to automatically update word count history when project word count changes
CREATE OR REPLACE FUNCTION update_word_count_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if current_word_count has changed
    IF OLD.current_word_count IS DISTINCT FROM NEW.current_word_count THEN
        -- Calculate the difference
        DECLARE
            word_diff INTEGER := COALESCE(NEW.current_word_count, 0) - COALESCE(OLD.current_word_count, 0);
        BEGIN
            -- Only record positive changes (words added)
            IF word_diff > 0 THEN
                INSERT INTO public.word_count_history (
                    project_id,
                    user_id,
                    date,
                    word_count
                )
                VALUES (
                    NEW.id,
                    NEW.user_id,
                    CURRENT_DATE,
                    word_diff
                )
                ON CONFLICT (project_id, date)
                DO UPDATE SET
                    word_count = public.word_count_history.word_count + word_diff,
                    session_count = public.word_count_history.session_count + 1,
                    updated_at = CURRENT_TIMESTAMP;
            END IF;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on projects table
CREATE TRIGGER trigger_update_word_count_history
    AFTER UPDATE OF current_word_count ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION update_word_count_history();

-- Add updated_at trigger for word_count_history
CREATE TRIGGER update_word_count_history_updated_at
    BEFORE UPDATE ON public.word_count_history
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();