'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts'
import {
  TrendingUp,
  Calendar,
  BookOpen,
  Users,
  Target,
  Award,
  Clock,
  PenTool,
  FileText,
  Activity,
  Zap,
  AlertCircle,
  CheckCircle2,
  Timer,
  BarChart3
} from 'lucide-react'
import { TIME_MS } from '@/lib/constants'

interface WritingStats {
  totalWords: number
  totalChapters: number
  averageChapterLength: number
  writingStreak: number
  dailyAverage: number
  weeklyAverage: number
  monthlyAverage: number
  completionRate: number
  estimatedCompletion: string
}

interface ChapterStats {
  chapterNumber: number
  title: string
  wordCount: number
  createdAt: string
  updatedAt: string
  writingTime: number // in minutes
  revisions: number
}

interface CharacterAppearance {
  characterName: string
  appearances: number
  chapters: number[]
  percentage: number
}

interface WritingSession {
  date: string
  duration: number // minutes
  wordsWritten: number
  productivity: number // words per minute
}

interface PlotThreadProgress {
  threadName: string
  status: string
  chaptersActive: number
  progressPercentage: number
}

interface WritingAnalyticsDashboardProps {
  projectId: string
  projectTitle: string
  targetWordCount?: number
  targetChapters?: number
}

export function WritingAnalyticsDashboard({
  projectId,
  projectTitle,
  targetWordCount = 80000,
  targetChapters = 20
}: WritingAnalyticsDashboardProps) {
  const [stats, setStats] = useState<WritingStats>({
    totalWords: 0,
    totalChapters: 0,
    averageChapterLength: 0,
    writingStreak: 0,
    dailyAverage: 0,
    weeklyAverage: 0,
    monthlyAverage: 0,
    completionRate: 0,
    estimatedCompletion: 'N/A'
  })
  const [chapterStats, setChapterStats] = useState<ChapterStats[]>([])
  const [characterAppearances, setCharacterAppearances] = useState<CharacterAppearance[]>([])
  const [writingSessions, setWritingSessions] = useState<WritingSession[]>([])
  const [plotThreads, setPlotThreads] = useState<PlotThreadProgress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('week')
  const { toast } = useToast()

  useEffect(() => {
    loadAnalytics()
  }, [projectId, timeRange])

  const loadAnalytics = async () => {
    setIsLoading(true)
    try {
      // Simulate loading analytics data
      // In a real implementation, this would fetch from the API
      await new Promise(resolve => setTimeout(resolve, TIME_MS.SECOND))
      
      // Mock data for demonstration
      setStats({
        totalWords: 42385,
        totalChapters: 12,
        averageChapterLength: 3532,
        writingStreak: 7,
        dailyAverage: 1250,
        weeklyAverage: 8750,
        monthlyAverage: 35000,
        completionRate: 52.9,
        estimatedCompletion: '6 weeks'
      })

      setChapterStats([
        { chapterNumber: 1, title: 'The Beginning', wordCount: 3245, createdAt: '2024-01-01', updatedAt: '2024-01-02', writingTime: 120, revisions: 3 },
        { chapterNumber: 2, title: 'First Encounter', wordCount: 3687, createdAt: '2024-01-03', updatedAt: '2024-01-04', writingTime: 150, revisions: 2 },
        { chapterNumber: 3, title: 'Rising Conflict', wordCount: 4012, createdAt: '2024-01-05', updatedAt: '2024-01-07', writingTime: 180, revisions: 4 },
        { chapterNumber: 4, title: 'Unexpected Turn', wordCount: 3456, createdAt: '2024-01-08', updatedAt: '2024-01-09', writingTime: 140, revisions: 2 },
        { chapterNumber: 5, title: 'Deep Revelations', wordCount: 3892, createdAt: '2024-01-10', updatedAt: '2024-01-12', writingTime: 160, revisions: 5 },
      ])

      setCharacterAppearances([
        { characterName: 'Sarah', appearances: 145, chapters: [1,2,3,4,5,6,7,8,9,10,11,12], percentage: 100 },
        { characterName: 'Marcus', appearances: 89, chapters: [1,2,4,5,7,8,10,11,12], percentage: 75 },
        { characterName: 'Elena', appearances: 67, chapters: [2,3,5,6,8,9,11], percentage: 58 },
        { characterName: 'Dr. Chen', appearances: 34, chapters: [3,4,6,9,10], percentage: 42 },
      ])

      setWritingSessions([
        { date: '2024-01-15', duration: 120, wordsWritten: 2340, productivity: 19.5 },
        { date: '2024-01-16', duration: 90, wordsWritten: 1567, productivity: 17.4 },
        { date: '2024-01-17', duration: 150, wordsWritten: 2890, productivity: 19.3 },
        { date: '2024-01-18', duration: 180, wordsWritten: 3245, productivity: 18.0 },
        { date: '2024-01-19', duration: 60, wordsWritten: 987, productivity: 16.5 },
        { date: '2024-01-20', duration: 240, wordsWritten: 4567, productivity: 19.0 },
        { date: '2024-01-21', duration: 120, wordsWritten: 2145, productivity: 17.9 },
      ])

      setPlotThreads([
        { threadName: 'Main Mystery', status: 'active', chaptersActive: 10, progressPercentage: 65 },
        { threadName: 'Romance Subplot', status: 'active', chaptersActive: 7, progressPercentage: 45 },
        { threadName: 'Character Backstory', status: 'resolved', chaptersActive: 5, progressPercentage: 100 },
        { threadName: 'Political Intrigue', status: 'setup', chaptersActive: 2, progressPercentage: 15 },
      ])
    } catch (error) {
      logger.error('Error loading analytics:', error)
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const wordCountData = chapterStats.map(ch => ({
    chapter: `Ch ${ch.chapterNumber}`,
    words: ch.wordCount,
    target: targetWordCount / targetChapters
  }))

  const productivityData = writingSessions.map(session => ({
    date: new Date(session.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    words: session.wordsWritten,
    productivity: session.productivity
  }))

  const characterPieData = characterAppearances.map(char => ({
    name: char.characterName,
    value: char.appearances
  }))

  const COLORS = ['#8b5cf6', '#ec4899', '#06b6d4', '#10b981', '#f59e0b', '#ef4444']

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <BarChart3 className="h-8 w-8 animate-pulse text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Total Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalWords.toLocaleString()} words
            </div>
            <Progress value={stats.completionRate} className="mt-2" />
            <p className="text-sm text-muted-foreground mt-1">
              {stats.completionRate}% of target
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Chapters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalChapters} / {targetChapters}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Avg: {stats.averageChapterLength.toLocaleString()} words
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Writing Streak
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold flex items-center gap-2">
              {stats.writingStreak} days
              <Award className="h-5 w-5 text-warning" />
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Daily avg: {stats.dailyAverage.toLocaleString()} words
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4" />
              Estimated Completion
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.estimatedCompletion}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              At current pace
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Detailed Analytics</CardTitle>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="progress" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="progress">Progress</TabsTrigger>
              <TabsTrigger value="productivity">Productivity</TabsTrigger>
              <TabsTrigger value="characters">Characters</TabsTrigger>
              <TabsTrigger value="plot">Plot Threads</TabsTrigger>
            </TabsList>

            <TabsContent value="progress" className="space-y-4">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={wordCountData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="chapter" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="words" fill="#8b5cf6" name="Word Count" />
                    <Bar dataKey="target" fill="#e5e7eb" name="Target" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              
              <div className="grid grid-cols-3 gap-4 sm:gap-5 lg:gap-6 mt-4">
                <div className="text-center p-4 bg-muted rounded">
                  <p className="text-sm text-muted-foreground">Weekly Average</p>
                  <p className="text-xl font-bold">{stats.weeklyAverage.toLocaleString()}</p>
                </div>
                <div className="text-center p-4 bg-muted rounded">
                  <p className="text-sm text-muted-foreground">Monthly Average</p>
                  <p className="text-xl font-bold">{stats.monthlyAverage.toLocaleString()}</p>
                </div>
                <div className="text-center p-4 bg-muted rounded">
                  <p className="text-sm text-muted-foreground">Words to Go</p>
                  <p className="text-xl font-bold">
                    {(targetWordCount - stats.totalWords).toLocaleString()}
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="productivity" className="space-y-4">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={productivityData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="words"
                      stroke="#8b5cf6"
                      fill="#8b5cf6"
                      fillOpacity={0.3}
                      name="Words Written"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="productivity"
                      stroke="#ec4899"
                      name="Words/Minute"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Recent Sessions</h4>
                <ScrollArea className="h-48">
                  {writingSessions.map((session, idx) => (
                    <div key={idx} className="flex items-center justify-between p-3 border-b">
                      <div>
                        <p className="font-medium">
                          {new Date(session.date).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {session.duration} minutes
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{session.wordsWritten.toLocaleString()} words</p>
                        <p className="text-sm text-muted-foreground">
                          {session.productivity.toFixed(1)} words/min
                        </p>
                      </div>
                    </div>
                  ))}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="characters" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={characterPieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {characterPieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>

                <div className="space-y-2">
                  <h4 className="font-semibold">Character Appearances</h4>
                  <ScrollArea className="h-56">
                    {characterAppearances.map((char, idx) => (
                      <div key={idx} className="p-3 border-b">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium">{char.characterName}</p>
                          <Badge variant="outline">{char.appearances} mentions</Badge>
                        </div>
                        <Progress value={char.percentage} className="h-2" />
                        <p className="text-xs text-muted-foreground mt-1">
                          Appears in {char.chapters.length} chapters
                        </p>
                      </div>
                    ))}
                  </ScrollArea>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="plot" className="space-y-4">
              <div className="space-y-4">
                {plotThreads.map((thread, idx) => (
                  <Card key={idx}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base flex items-center gap-2">
                          {thread.threadName}
                          {thread.status === 'active' && <Activity className="h-4 w-4 text-success" />}
                          {thread.status === 'resolved' && <CheckCircle2 className="h-4 w-4 text-gray-500" />}
                          {thread.status === 'setup' && <AlertCircle className="h-4 w-4 text-info" />}
                        </CardTitle>
                        <Badge variant={
                          thread.status === 'active' ? 'default' :
                          thread.status === 'resolved' ? 'secondary' : 'outline'
                        }>
                          {thread.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Progress value={thread.progressPercentage} className="mb-2" />
                      <p className="text-sm text-muted-foreground">
                        Active across {thread.chaptersActive} chapters • {thread.progressPercentage}% complete
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}