import type { Email<PERSON>rovider, SendEmailOptions, EmailResult } from '../types'
import { logger } from '@/lib/services/logger'

export class MailerooProvider implements EmailProvider {
  name = 'maileroo'
  private apiKey: string
  private baseUrl = 'https://api.maileroo.com/v1'

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  async verifyConfiguration(): Promise<boolean> {
    try {
      // Test the API key with a simple request
      const response = await fetch(`${this.baseUrl}/status`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      })
      
      return response.ok
    } catch (error) {
      logger.error('Maileroo configuration verification failed:', error)
      return false
    }
  }

  async sendEmail(options: SendEmailOptions): Promise<EmailResult> {
    try {
      const recipients = Array.isArray(options.to) ? options.to : [options.to]
      
      const payload = {
        from: options.from || 'BookScribe AI <<EMAIL>>',
        to: recipients,
        subject: options.subject || 'BookScribe AI Notification',
        text: options.text,
        html: options.html,
        reply_to: options.replyTo,
        tags: ['bookscribe', options.template || 'transactional'],
        tracking: true
      }

      const response = await fetch(`${this.baseUrl}/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `Maileroo API error: ${response.status}`)
      }

      return {
        id: data.message_id,
        success: true,
        provider: this.name
      }
    } catch (error) {
      logger.error('Maileroo send failed:', error)
      return {
        id: '',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send email via Maileroo',
        provider: this.name
      }
    }
  }
}