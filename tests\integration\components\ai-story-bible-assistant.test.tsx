import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AIStoryBibleAssistant } from '@/components/story-bible/ai-story-bible-assistant'
import { useToast } from '@/hooks/use-toast'

// Mock hooks and fetch
jest.mock('@/hooks/use-toast')
global.fetch = jest.fn()

const mockUseToast = useToast as jest.MockedFunction<typeof useToast>

describe('AIStoryBibleAssistant', () => {
  const mockToast = jest.fn()
  const mockOnUpdate = jest.fn()
  const mockOnNewEntry = jest.fn()
  
  const defaultProps = {
    projectId: 'test-project-123',
    context: {
      characters: [
        { id: 'char1', name: 'Hero', role: 'protagonist' },
        { id: 'char2', name: 'Villain', role: 'antagonist' }
      ],
      plotSummary: 'An epic tale of good versus evil',
      themes: ['Redemption', 'Power'],
      worldRules: [
        { category: 'Magic', content: 'Magic exists but is rare' }
      ]
    },
    onUpdate: mockOnUpdate,
    onNewEntry: mockOnNewEntry
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseToast.mockReturnValue({ toast: mockToast } as any)
    ;(global.fetch as jest.Mock).mockReset()
  })

  describe('Rendering', () => {
    it('should render AI assistant interface', () => {
      render(<AIStoryBibleAssistant {...defaultProps} />)

      expect(screen.getByText('AI Story Bible Assistant')).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/ask about your story/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument()
    })

    it('should display context summary', () => {
      render(<AIStoryBibleAssistant {...defaultProps} />)

      expect(screen.getByText(/2 characters/i)).toBeInTheDocument()
      expect(screen.getByText(/2 themes/i)).toBeInTheDocument()
      expect(screen.getByText(/1 world rule/i)).toBeInTheDocument()
    })

    it('should display suggested prompts', () => {
      render(<AIStoryBibleAssistant {...defaultProps} />)

      expect(screen.getByText(/What are the key plot points/i)).toBeInTheDocument()
      expect(screen.getByText(/Explain the magic system/i)).toBeInTheDocument()
      expect(screen.getByText(/Describe the world geography/i)).toBeInTheDocument()
    })
  })

  describe('Chat Functionality', () => {
    it('should send user message and receive AI response', async () => {
      const mockResponse = {
        response: 'The magic system is based on elemental control.',
        suggestions: [
          { type: 'world_rule', content: 'Elemental magic requires a focus stone' }
        ]
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockResponse })
      })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'Tell me about the magic system')

      const sendButton = screen.getByRole('button', { name: /send/i })
      await userEvent.click(sendButton)

      await waitFor(() => {
        expect(screen.getByText('Tell me about the magic system')).toBeInTheDocument()
        expect(screen.getByText('The magic system is based on elemental control.')).toBeInTheDocument()
      })

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/ai/story-bible-assistant',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            projectId: 'test-project-123',
            query: 'Tell me about the magic system',
            context: defaultProps.context
          })
        })
      )
    })

    it('should handle API errors gracefully', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Server error' })
      })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'Test query')

      const sendButton = screen.getByRole('button', { name: /send/i })
      await userEvent.click(sendButton)

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Failed to get AI response. Please try again.',
          variant: 'destructive'
        })
      })
    })

    it('should show loading state while processing', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })

      ;(global.fetch as jest.Mock).mockReturnValueOnce(promise)

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'Test query')

      const sendButton = screen.getByRole('button', { name: /send/i })
      await userEvent.click(sendButton)

      expect(screen.getByText(/thinking/i)).toBeInTheDocument()
      expect(sendButton).toBeDisabled()

      // Resolve the promise
      resolvePromise!({
        ok: true,
        json: async () => ({ data: { response: 'Test response' } })
      })

      await waitFor(() => {
        expect(screen.queryByText(/thinking/i)).not.toBeInTheDocument()
        expect(sendButton).not.toBeDisabled()
      })
    })
  })

  describe('Suggestions', () => {
    it('should display AI suggestions', async () => {
      const mockResponse = {
        response: 'Here are some world building ideas.',
        suggestions: [
          { 
            type: 'world_rule', 
            content: 'Magic users are marked by glowing tattoos',
            category: 'Magic System'
          },
          { 
            type: 'timeline_event', 
            content: 'The Great Sundering',
            date: '1000 years ago',
            description: 'When magic was split into elements'
          }
        ]
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockResponse })
      })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'Give me world building ideas')

      const sendButton = screen.getByRole('button', { name: /send/i })
      await userEvent.click(sendButton)

      await waitFor(() => {
        expect(screen.getByText('Suggestions:')).toBeInTheDocument()
        expect(screen.getByText('Magic users are marked by glowing tattoos')).toBeInTheDocument()
        expect(screen.getByText('The Great Sundering')).toBeInTheDocument()
      })
    })

    it('should allow adding suggestions to story bible', async () => {
      const mockResponse = {
        response: 'Here is a world rule suggestion.',
        suggestions: [
          { 
            type: 'world_rule', 
            content: 'Time flows differently in magical realms',
            category: 'Magic System'
          }
        ]
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockResponse })
      })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'Suggest a world rule')

      const sendButton = screen.getByRole('button', { name: /send/i })
      await userEvent.click(sendButton)

      await waitFor(() => {
        const addButton = screen.getByRole('button', { name: /add to story bible/i })
        expect(addButton).toBeInTheDocument()
      })

      const addButton = screen.getByRole('button', { name: /add to story bible/i })
      await userEvent.click(addButton)

      expect(mockOnNewEntry).toHaveBeenCalledWith(
        'world_rule',
        expect.objectContaining({
          content: 'Time flows differently in magical realms',
          category: 'Magic System'
        })
      )

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Added to Story Bible',
        description: 'World rule has been added'
      })
    })
  })

  describe('Suggested Prompts', () => {
    it('should fill input when clicking suggested prompt', async () => {
      render(<AIStoryBibleAssistant {...defaultProps} />)

      const suggestedPrompt = screen.getByText(/What are the key plot points/i)
      await userEvent.click(suggestedPrompt)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      expect(input).toHaveValue('What are the key plot points?')
    })

    it('should send message when clicking suggested prompt', async () => {
      const mockResponse = {
        response: 'The key plot points are...',
        suggestions: []
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockResponse })
      })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const suggestedPrompt = screen.getByText(/What are the key plot points/i)
      await userEvent.click(suggestedPrompt)

      await waitFor(() => {
        expect(screen.getByText('The key plot points are...')).toBeInTheDocument()
      })
    })
  })

  describe('Chat History', () => {
    it('should maintain chat history', async () => {
      const mockResponse1 = {
        response: 'First response',
        suggestions: []
      }
      const mockResponse2 = {
        response: 'Second response',
        suggestions: []
      }

      ;(global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ data: mockResponse1 })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ data: mockResponse2 })
        })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      // First message
      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'First question')
      await userEvent.click(screen.getByRole('button', { name: /send/i }))

      await waitFor(() => {
        expect(screen.getByText('First question')).toBeInTheDocument()
        expect(screen.getByText('First response')).toBeInTheDocument()
      })

      // Second message
      await userEvent.clear(input)
      await userEvent.type(input, 'Second question')
      await userEvent.click(screen.getByRole('button', { name: /send/i }))

      await waitFor(() => {
        expect(screen.getByText('Second question')).toBeInTheDocument()
        expect(screen.getByText('Second response')).toBeInTheDocument()
      })

      // Both messages should still be visible
      expect(screen.getByText('First question')).toBeInTheDocument()
      expect(screen.getByText('First response')).toBeInTheDocument()
    })

    it('should scroll to bottom on new messages', async () => {
      const mockScrollIntoView = jest.fn()
      Element.prototype.scrollIntoView = mockScrollIntoView

      const mockResponse = {
        response: 'Test response',
        suggestions: []
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockResponse })
      })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'Test question')
      await userEvent.click(screen.getByRole('button', { name: /send/i }))

      await waitFor(() => {
        expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' })
      })
    })
  })

  describe('Keyboard Shortcuts', () => {
    it('should send message on Enter key', async () => {
      const mockResponse = {
        response: 'Test response',
        suggestions: []
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockResponse })
      })

      render(<AIStoryBibleAssistant {...defaultProps} />)

      const input = screen.getByPlaceholderText(/ask about your story/i)
      await userEvent.type(input, 'Test question')
      await userEvent.keyboard('{Enter}')

      await waitFor(() => {
        expect(screen.getByText('Test response')).toBeInTheDocument()
      })
    })

    it('should not send empty message', async () => {
      render(<AIStoryBibleAssistant {...defaultProps} />)

      const sendButton = screen.getByRole('button', { name: /send/i })
      await userEvent.click(sendButton)

      expect(global.fetch).not.toHaveBeenCalled()
    })
  })
})