import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()

    const { error, count } = await supabase
      .from('notifications')
      .update({ 
        read: true, 
        read_at: new Date().toISOString() 
      })
      .eq('user_id', user.id)
      .eq('read', false)
      .select('id', { count: 'exact' })

    if (error) {
      console.error('Error marking all notifications as read:', error)
      return NextResponse.json(
        { error: 'Failed to update notifications' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      updated_count: count || 0
    })
  } catch (error) {
    console.error('Error in mark-all-read:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})