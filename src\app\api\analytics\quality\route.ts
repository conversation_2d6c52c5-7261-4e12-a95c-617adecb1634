import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { AI_MODELS } from '@/lib/config/ai-settings'
import { z } from 'zod'
import { ConsistencyValidator } from '@/lib/services/consistency-validator'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { logger } from '@/lib/services/logger'
import { baseSchemas } from '@/lib/validation/common-schemas'

// Query validation schema
const qualityQuerySchema = z.object({
  projectId: baseSchemas.uuid.optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}/, 'Invalid date format').optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}/, 'Invalid date format').optional()
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before or equal to end date'
});

// Body validation schema for POST
const qualityAnalysisSchema = z.object({
  projectId: baseSchemas.uuid.optional(),
  content: z.string().min(50, 'Content must be at least 50 characters').max(100000, 'Content exceeds maximum length'),
  analysisType: z.enum(['quick', 'detailed']).optional().default('quick')
})

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId'),
    startDate: searchParams.get('startDate'),
    endDate: searchParams.get('endDate')
  };

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    querySchema: qualityQuerySchema,
    rateLimitKey: 'analytics-quality',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // If projectId provided, verify access
      if (queryParams.projectId) {
        const supabase = await createTypedServerClient();
        const { data: project } = await supabase
          .from('projects')
          .select('id')
          .eq('id', queryParams.projectId)
          .eq('user_id', user.id)
          .single();
        
        if (!project) {
          const { data: collaborator } = await supabase
            .from('project_collaborators')
            .select('role')
            .eq('project_id', queryParams.projectId)
            .eq('user_id', user.id)
            .eq('status', 'active')
            .single();
          
          if (!collaborator) {
            return { valid: false, error: 'Access denied to this project' };
          }
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, startDate, endDate } = context.query;

  try {

    const supabase = await createTypedServerClient()

    // Get quality metrics from database
    let query = supabase
      .from('quality_metrics')
      .select('*')
      .eq('user_id', user.id)

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data: qualityMetrics, error } = await query
      .order('created_at', { ascending: false })
      .limit(500); // Limit to prevent excessive data

    if (error) {
      logger.error('Failed to fetch quality metrics:', error, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to fetch quality analytics');
    }

    // Calculate aggregate quality scores
    const aggregateQuality = qualityMetrics.length > 0 ? {
      overallScore: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.overall_score || 0), 0) / qualityMetrics.length),
      readability: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_readability || 0), 0) / qualityMetrics.length),
      consistency: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_consistency || 0), 0) / qualityMetrics.length),
      pacing: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_pacing || 0), 0) / qualityMetrics.length),
      engagement: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_engagement || 0), 0) / qualityMetrics.length),
      dialogue: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_dialogue_authenticity || 0), 0) / qualityMetrics.length),
      description: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_creativity || 0), 0) / qualityMetrics.length),
    } : {
      overallScore: 0,
      readability: 0,
      consistency: 0,
      pacing: 0,
      engagement: 0,
      dialogue: 0,
      description: 0,
    }

    logger.info('Quality analytics fetched', {
      userId: user.id,
      projectId,
      metricsCount: qualityMetrics.length,
      overallScore: aggregateQuality.overallScore,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      metrics: qualityMetrics,
      aggregate: aggregateQuality,
      count: qualityMetrics.length,
      period: {
        start: startDate || qualityMetrics[qualityMetrics.length - 1]?.created_at || null,
        end: endDate || qualityMetrics[0]?.created_at || null
      }
    });

  } catch (error) {
    logger.error('Quality analytics error:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to generate quality analytics');
  }
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: qualityAnalysisSchema,
    rateLimitKey: 'quality-analysis',
    rateLimitCost: 10, // High cost for AI analysis
    maxBodySize: 100 * 1024, // 100KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      const body = await req.json();
      const { projectId, content } = body;
      
      // Check for malicious content
      if (content && content.match(/<script|javascript:|onerror|onclick|<iframe|<object|<embed/i)) {
        return { valid: false, error: 'Content contains potentially malicious code' };
      }
      
      // If projectId provided, verify access
      if (projectId) {
        const supabase = await createTypedServerClient();
        const { data: project } = await supabase
          .from('projects')
          .select('id')
          .eq('id', projectId)
          .eq('user_id', user.id)
          .single();
        
        if (!project) {
          const { data: collaborator } = await supabase
            .from('project_collaborators')
            .select('role')
            .eq('project_id', projectId)
            .eq('user_id', user.id)
            .eq('status', 'active')
            .single();
          
          if (!collaborator) {
            return { valid: false, error: 'Access denied to this project' };
          }
        }
      }
      
      // Check daily analysis limit
      const supabase = await createTypedServerClient();
      const today = new Date().toISOString().split('T')[0];
      const { data: todayAnalyses } = await supabase
        .from('quality_metrics')
        .select('id')
        .eq('user_id', user.id)
        .gte('created_at', today)
        .limit(50);
      
      if (todayAnalyses && todayAnalyses.length >= 50) {
        return { 
          valid: false, 
          error: 'Daily quality analysis limit reached (50 analyses)' 
        };
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, content, analysisType } = context.body;

  try {

    const supabase = await createTypedServerClient()

    // Generate quality analysis using AI
    const qualityPrompt = `Analyze the following writing sample and provide quality metrics on a scale of 0-100:

Content:
${content}

Please evaluate:
1. Readability - How clear and easy to read is the text?
2. Consistency - How consistent are character voices, tone, and style?
3. Pacing - How well does the text maintain reader interest and flow?
4. Engagement - How compelling and engaging is the content?
5. Dialogue - How natural and authentic is the dialogue (if present)?
6. Description - How vivid and effective are the descriptions?

Provide an overall quality score and specific scores for each dimension.`

    const qualityAnalysis = await vercelAIClient.generateObjectWithFallback(
      qualityPrompt,
      z.object({
        overall_score: z.number().min(0).max(100),
        readability: z.number().min(0).max(100),
        consistency: z.number().min(0).max(100),
        pacing: z.number().min(0).max(100),
        engagement: z.number().min(0).max(100),
        dialogue: z.number().min(0).max(100),
        description: z.number().min(0).max(100),
        feedback: z.string(),
        improvement_suggestions: z.array(z.string()),
      }),
      {
        model: AI_MODELS.FAST,
        temperature: 0.3,
        systemPrompt: 'You are an expert writing quality analyst. Provide detailed, constructive feedback on writing quality.'
      }
    )

    // Save quality metrics to database
    const { data: qualityRecord, error: insertError } = await supabase
      .from('quality_metrics')
      .insert({
        user_id: user.id,
        project_id: projectId,
        overall_score: qualityAnalysis.overall_score,
        avg_readability: qualityAnalysis.readability,
        avg_consistency: qualityAnalysis.consistency,
        avg_pacing: qualityAnalysis.pacing,
        avg_engagement: qualityAnalysis.engagement,
        avg_dialogue_authenticity: qualityAnalysis.dialogue,
        avg_creativity: qualityAnalysis.description,
        feedback: qualityAnalysis.feedback,
        improvement_suggestions: qualityAnalysis.improvement_suggestions,
        content_sample: content.substring(0, 500), // Store first 500 chars as sample
        word_count: content.trim().split(/\s+/).length,
        analysis_type: analysisType,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      logger.error('Failed to save quality metrics:', insertError, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to save quality analysis');
    }

    logger.info('Quality analysis completed', {
      userId: user.id,
      projectId,
      analysisId: qualityRecord.id,
      overallScore: qualityAnalysis.overall_score,
      contentLength: content.length,
      analysisType,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      qualityMetrics: qualityRecord,
      analysis: qualityAnalysis,
      message: 'Quality analysis completed successfully'
    });

  } catch (error) {
    logger.error('Quality analysis error:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to perform quality analysis');
  }
});