# BookScribe Behavioral Analytics System

## Overview

The Behavioral Analytics System tracks and analyzes user behavior patterns to provide insights that improve the writing experience, optimize feature development, and personalize AI assistance. It captures user interactions, writing patterns, feature usage, and productivity metrics while maintaining privacy and consent.

## Architecture

### Database Schema

#### User Behavior Events Table
Core event tracking:

```sql
user_behavior_events:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - session_id: UUID - References user_sessions
  - event_type: VARCHAR(100) - click, type, navigate, feature_use, etc.
  - event_category: VARCHAR(50) - editor, ai, navigation, settings
  - event_action: VARCHAR(100) - Specific action taken
  - event_label: VARCHAR(255) - Additional context
  - event_value: FLOAT - Numeric value if applicable
  - metadata: JSONB - Additional event data
  - timestamp: TIMESTAMPTZ - When event occurred
  - page_url: TEXT - Current page/route
  - user_agent: TEXT - Browser/device info
  - ip_address: INET - User IP (hashed)
  - created_at: TIMESTAMPTZ
```

#### User Sessions Table
Session tracking:

```sql
user_sessions:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - session_start: TIMESTAMPTZ
  - session_end: TIMESTAMPTZ
  - duration_seconds: INTEGER
  - page_views: INTEGER
  - events_count: INTEGER
  - features_used: TEXT[] - Feature IDs used
  - writing_time: INTEGER - Active writing seconds
  - idle_time: INTEGER - Inactive seconds
  - device_type: VARCHAR(50) - desktop, tablet, mobile
  - browser: VARCHAR(50)
  - os: VARCHAR(50)
  - screen_resolution: VARCHAR(20)
  - timezone: VARCHAR(50)
  - created_at: TIMESTAMPTZ
```

#### Feature Usage Analytics Table
Detailed feature tracking:

```sql
feature_usage_analytics:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - feature_id: VARCHAR(100) - Feature identifier
  - feature_category: VARCHAR(50) - ai, editor, export, etc.
  - usage_count: INTEGER - Times used
  - total_duration: INTEGER - Total seconds used
  - avg_duration: FLOAT - Average usage duration
  - success_rate: FLOAT - Successful uses percentage
  - error_count: INTEGER - Errors encountered
  - last_used: TIMESTAMPTZ
  - first_used: TIMESTAMPTZ
  - user_rating: INTEGER - 1-5 rating if provided
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Writing Behavior Patterns Table
Writing-specific analytics:

```sql
writing_behavior_patterns:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects
  - pattern_type: VARCHAR(50) - time_of_day, burst_writing, steady_pace
  - pattern_data: JSONB - Pattern-specific data
  - confidence_score: FLOAT - Pattern confidence (0-1)
  - insights: JSONB - Generated insights
  - recommendations: JSONB - Personalized recommendations
  - period_start: DATE
  - period_end: DATE
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

## Behavioral Metrics

### 1. User Engagement Metrics
Core engagement tracking:

```typescript
interface EngagementMetrics {
  daily_active_users: number;
  weekly_active_users: number;
  monthly_active_users: number;
  
  session_metrics: {
    avg_session_duration: number;
    avg_sessions_per_user: number;
    bounce_rate: number;
    pages_per_session: number;
  };
  
  retention_metrics: {
    day_1_retention: number;
    day_7_retention: number;
    day_30_retention: number;
    churn_rate: number;
  };
  
  feature_adoption: {
    [feature_id: string]: {
      adoption_rate: number;
      daily_active_users: number;
      avg_uses_per_user: number;
      satisfaction_score: number;
    };
  };
}
```

### 2. Writing Behavior Patterns
User writing analysis:

```typescript
interface WritingPatterns {
  writing_schedule: {
    preferred_days: string[];
    preferred_hours: number[];
    consistency_score: number;
    streak_data: {
      current_streak: number;
      longest_streak: number;
      avg_streak_length: number;
    };
  };
  
  productivity_patterns: {
    avg_words_per_session: number;
    avg_session_duration: number;
    peak_productivity_time: string;
    productivity_variance: number;
    burst_vs_steady: 'burst' | 'steady' | 'mixed';
  };
  
  content_patterns: {
    avg_chapter_length: number;
    revision_frequency: number;
    ai_assistance_usage: number;
    preferred_ai_features: string[];
  };
  
  workflow_patterns: {
    planning_vs_discovery: number; // 0-1 scale
    linear_vs_nonlinear: number;
    revision_style: 'immediate' | 'batch' | 'final';
    outline_usage: boolean;
  };
}
```

### 3. Feature Usage Analytics
Detailed feature tracking:

```typescript
interface FeatureAnalytics {
  feature_id: string;
  usage_funnel: {
    discovered: number;
    tried: number;
    repeated_use: number;
    regular_use: number;
    power_user: number;
  };
  
  performance_metrics: {
    avg_load_time: number;
    error_rate: number;
    completion_rate: number;
    abandonment_rate: number;
  };
  
  user_journey: {
    entry_points: { [source: string]: number };
    exit_points: { [destination: string]: number };
    typical_flow: string[];
  };
  
  satisfaction_metrics: {
    nps_score: number;
    user_ratings: number[];
    feedback_themes: string[];
  };
}
```

### 4. AI Usage Patterns
AI feature analytics:

```typescript
interface AIUsagePatterns {
  ai_feature_usage: {
    [feature: string]: {
      usage_count: number;
      acceptance_rate: number;
      modification_rate: number;
      satisfaction_score: number;
    };
  };
  
  generation_patterns: {
    avg_generation_length: number;
    preferred_ai_models: string[];
    prompt_complexity: number;
    iteration_count: number;
  };
  
  assistance_preferences: {
    proactive_suggestions_enabled: boolean;
    suggestion_acceptance_rate: number;
    preferred_assistance_level: 'minimal' | 'moderate' | 'maximal';
    trust_score: number; // How much they trust AI
  };
}
```

## API Endpoints

### Analytics Data Retrieval

#### GET /api/analytics/behavioral
Get user behavioral analytics:

```typescript
// Request
GET /api/analytics/behavioral?user_id=uuid&date_range=last_30_days

// Response
{
  overview: {
    total_sessions: 45,
    total_writing_time: 3600, // minutes
    words_written: 25000,
    features_used: 15,
    productivity_score: 0.82
  },
  patterns: {
    writing_schedule: {
      most_productive_day: "Saturday",
      most_productive_hour: 10,
      consistency_score: 0.75
    },
    feature_preferences: [
      { feature: "ai_dialogue", usage_rate: 0.65 },
      { feature: "character_arc", usage_rate: 0.45 }
    ]
  },
  insights: [
    {
      type: "productivity_tip",
      message: "You write 40% more on weekend mornings",
      confidence: 0.85
    }
  ],
  recommendations: [
    {
      type: "feature_suggestion",
      feature: "writing_goals",
      reason: "Based on your consistent writing pattern"
    }
  ]
}
```

#### POST /api/analytics/behavioral/events
Track behavioral event:

```typescript
// Request
{
  event_type: "feature_use",
  event_category: "ai_generation",
  event_action: "generate_dialogue",
  event_label: "character_conversation",
  event_value: 350, // words generated
  metadata: {
    chapter_id: "uuid",
    character_count: 2,
    generation_time: 4.5
  }
}

// Response
{
  event_id: "uuid",
  tracked: true
}
```

### Pattern Analysis

#### GET /api/analytics/behavioral/patterns
Get behavior patterns:

```typescript
// Request
GET /api/analytics/behavioral/patterns?user_id=uuid&pattern_type=writing

// Response
{
  patterns: [
    {
      type: "burst_writer",
      confidence: 0.87,
      description: "Tends to write in intensive bursts",
      data: {
        avg_burst_duration: 120, // minutes
        avg_burst_words: 2500,
        burst_frequency: "2-3 times per week"
      },
      recommendations: [
        "Schedule longer uninterrupted writing sessions",
        "Use sprint timers to maximize burst productivity"
      ]
    }
  ]
}
```

#### POST /api/analytics/behavioral/insights
Generate behavioral insights:

```typescript
// Request
{
  user_id: "uuid",
  analysis_type: "productivity" | "feature_usage" | "comprehensive",
  time_period: "last_90_days"
}

// Response
{
  insights: [
    {
      category: "productivity",
      insight: "Writing speed increases 25% after using outline feature",
      supporting_data: {
        with_outline_wpm: 45,
        without_outline_wpm: 36,
        sample_size: 25
      },
      actionable_recommendation: "Consider using outlines for all chapters"
    }
  ]
}
```

## Behavioral Tracking

### Event Collection
```typescript
class BehaviorTracker {
  trackEvent(event: BehaviorEvent): void {
    // Validate event
    if (!this.validateEvent(event)) return;
    
    // Enrich with context
    const enrichedEvent = this.enrichEvent(event);
    
    // Check user consent
    if (!this.hasConsent(event.user_id)) {
      enrichedEvent.data = this.anonymize(enrichedEvent.data);
    }
    
    // Queue for processing
    this.eventQueue.push(enrichedEvent);
    
    // Real-time processing for critical events
    if (this.isCriticalEvent(event)) {
      this.processImmediately(enrichedEvent);
    }
  }
  
  private enrichEvent(event: BehaviorEvent): EnrichedEvent {
    return {
      ...event,
      session_id: this.getCurrentSession(),
      timestamp: new Date(),
      context: {
        page_url: window.location.href,
        viewport: this.getViewport(),
        device: this.getDeviceInfo()
      }
    };
  }
}
```

### Pattern Recognition
```typescript
class PatternRecognizer {
  async recognizePatterns(
    userId: string,
    eventHistory: BehaviorEvent[]
  ): Promise<BehaviorPattern[]> {
    const patterns = [];
    
    // Time-based patterns
    const timePatterns = await this.analyzeTimePatterns(eventHistory);
    patterns.push(...timePatterns);
    
    // Sequence patterns
    const sequencePatterns = await this.analyzeSequencePatterns(eventHistory);
    patterns.push(...sequencePatterns);
    
    // Productivity patterns
    const productivityPatterns = await this.analyzeProductivityPatterns(
      eventHistory
    );
    patterns.push(...productivityPatterns);
    
    // AI usage patterns
    const aiPatterns = await this.analyzeAIPatterns(eventHistory);
    patterns.push(...aiPatterns);
    
    return this.rankPatternsByConfidence(patterns);
  }
}
```

### Insight Generation
```typescript
class InsightGenerator {
  generateInsights(
    patterns: BehaviorPattern[],
    metrics: UserMetrics
  ): Insight[] {
    const insights = [];
    
    // Productivity insights
    if (this.hasProductivityPattern(patterns)) {
      insights.push(this.generateProductivityInsight(patterns, metrics));
    }
    
    // Feature discovery
    const unusedFeatures = this.findUnusedBeneficialFeatures(
      patterns,
      metrics
    );
    if (unusedFeatures.length > 0) {
      insights.push(this.generateFeatureDiscoveryInsight(unusedFeatures));
    }
    
    // Workflow optimization
    const inefficiencies = this.detectWorkflowInefficiencies(patterns);
    if (inefficiencies.length > 0) {
      insights.push(this.generateWorkflowInsight(inefficiencies));
    }
    
    return this.prioritizeInsights(insights);
  }
}
```

## Privacy & Consent

### Data Collection Policies
```typescript
interface PrivacySettings {
  tracking_enabled: boolean;
  granularity: 'minimal' | 'standard' | 'detailed';
  data_retention_days: number;
  exclude_features: string[];
  anonymize_content: boolean;
  
  consent_scopes: {
    basic_analytics: boolean;
    feature_usage: boolean;
    writing_patterns: boolean;
    ai_interactions: boolean;
    performance_monitoring: boolean;
  };
}
```

### Data Anonymization
```typescript
class DataAnonymizer {
  anonymize(event: BehaviorEvent): AnonymizedEvent {
    return {
      ...event,
      user_id: this.hashUserId(event.user_id),
      ip_address: this.anonymizeIP(event.ip_address),
      metadata: this.scrubPII(event.metadata),
      content_hash: this.hashContent(event.content)
    };
  }
  
  private scrubPII(data: any): any {
    // Remove personally identifiable information
    const piiPatterns = [
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email
      /\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b/, // Phone
      /\b\d{3}-\d{2}-\d{4}\b/ // SSN
    ];
    
    return this.recursiveClean(data, piiPatterns);
  }
}
```

## Visualization & Reporting

### Analytics Dashboard Components

#### User Activity Heatmap
```tsx
<ActivityHeatmap
  userId={userId}
  dateRange="last_90_days"
  metric="writing_time" | "words_written" | "feature_usage"
  interactive={true}
  onCellClick={handleCellClick}
/>
```

#### Feature Usage Funnel
```tsx
<FeatureUsageFunnel
  feature={selectedFeature}
  timeframe="last_30_days"
  showDropoffReasons={true}
  compareWith={previousPeriod}
/>
```

#### Productivity Trends
```tsx
<ProductivityTrends
  userId={userId}
  metrics={['words_per_hour', 'session_length', 'consistency']}
  showInsights={true}
  showRecommendations={true}
/>
```

#### Behavior Flow
```tsx
<BehaviorFlow
  startPoint="login"
  endPoint="chapter_complete"
  showMostCommonPaths={5}
  showDropoffPoints={true}
  interactive={true}
/>
```

## Real-time Analytics

### Live Tracking
```typescript
class RealTimeAnalytics {
  constructor() {
    this.activeUsers = new Map();
    this.currentEvents = new EventStream();
    this.liveMetrics = new MetricsCalculator();
  }
  
  trackLiveUser(userId: string, action: UserAction) {
    // Update active user state
    this.activeUsers.set(userId, {
      lastAction: action,
      timestamp: Date.now(),
      sessionDuration: this.calculateSessionDuration(userId)
    });
    
    // Stream to dashboard
    this.broadcastUpdate({
      type: 'user_activity',
      userId,
      action,
      metrics: this.liveMetrics.calculate()
    });
  }
  
  getLiveStats(): LiveStats {
    return {
      activeUsers: this.activeUsers.size,
      currentWriters: this.countActiveWriters(),
      eventsPerMinute: this.currentEvents.getRate(),
      popularFeatures: this.getTopFeatures(5)
    };
  }
}
```

## Performance Optimization

### Data Aggregation
```typescript
interface AggregationStrategy {
  raw_events: {
    retention: '7_days',
    sample_rate: 1.0
  };
  
  hourly_aggregates: {
    retention: '30_days',
    metrics: ['event_count', 'unique_users', 'session_count']
  };
  
  daily_aggregates: {
    retention: '1_year',
    metrics: ['all_metrics']
  };
  
  monthly_aggregates: {
    retention: 'indefinite',
    metrics: ['summary_metrics']
  };
}
```

### Query Optimization
```sql
-- Materialized view for common queries
CREATE MATERIALIZED VIEW user_activity_summary AS
SELECT 
  user_id,
  DATE(timestamp) as activity_date,
  COUNT(*) as event_count,
  COUNT(DISTINCT session_id) as session_count,
  SUM(CASE WHEN event_category = 'writing' THEN 1 ELSE 0 END) as writing_events,
  SUM(CASE WHEN event_category = 'ai' THEN 1 ELSE 0 END) as ai_events
FROM user_behavior_events
GROUP BY user_id, DATE(timestamp);

CREATE INDEX idx_activity_summary_user_date 
ON user_activity_summary(user_id, activity_date DESC);
```

## Machine Learning Integration

### Predictive Analytics
```typescript
interface PredictiveModels {
  churn_prediction: {
    features: ['login_frequency', 'writing_frequency', 'feature_usage'],
    model_type: 'gradient_boosting',
    update_frequency: 'weekly'
  };
  
  feature_recommendation: {
    features: ['user_patterns', 'similar_users', 'success_metrics'],
    model_type: 'collaborative_filtering',
    update_frequency: 'daily'
  };
  
  productivity_forecast: {
    features: ['historical_patterns', 'time_factors', 'project_state'],
    model_type: 'time_series',
    update_frequency: 'real_time'
  };
}
```

## Future Enhancements

1. **Advanced Personalization**
   - AI-driven UI customization
   - Personalized feature recommendations
   - Adaptive assistance levels

2. **Cohort Analysis**
   - User segment identification
   - A/B testing framework
   - Feature rollout optimization

3. **Predictive Insights**
   - Churn prediction
   - Success likelihood scoring
   - Optimal timing recommendations

4. **Cross-Platform Analytics**
   - Mobile app integration
   - Multi-device tracking
   - Unified user journey

## Related Systems
- Analytics System (general analytics)
- User Management System
- AI Recommendation Engine
- Feature Flag System