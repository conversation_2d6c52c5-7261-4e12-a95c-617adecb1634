#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Analyzing state management patterns...\n');

// Find all Context and Store files
const contextFiles = [];
const storeFiles = [];
const hookFiles = [];

function findStateFiles(dir) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules')) {
      findStateFiles(fullPath);
    } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      if (content.includes('createContext')) {
        contextFiles.push(fullPath);
      }
      if (content.includes('zustand') || content.includes('create(')) {
        storeFiles.push(fullPath);
      }
      if (item.startsWith('use') && content.includes('useState')) {
        hookFiles.push(fullPath);
      }
    }
  }
}

const srcDir = path.join(process.cwd(), 'src');
findStateFiles(srcDir);

console.log(`Found ${contextFiles.length} Context files`);
console.log(`Found ${storeFiles.length} Zustand stores`);
console.log(`Found ${hookFiles.length} custom hooks with state\n`);

// Analyze each context
const migrationCandidates = [];
const keepAsContext = [];

console.log('📊 Context Analysis:');
console.log('━'.repeat(60));

contextFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const relativePath = path.relative(process.cwd(), file);
  const fileName = path.basename(file);
  
  // Analyze context usage
  const analysis = {
    file: relativePath,
    name: fileName,
    hasGlobalState: false,
    hasComplexLogic: false,
    isAuthRelated: false,
    isThemeProvider: false,
    isFormContext: false,
    recommendation: ''
  };
  
  // Check patterns
  if (fileName.includes('auth') || content.includes('useAuth')) {
    analysis.isAuthRelated = true;
    analysis.recommendation = 'Keep as Context (Auth wrapper)';
  } else if (fileName.includes('theme') || content.includes('ThemeProvider')) {
    analysis.isThemeProvider = true;
    analysis.recommendation = 'Keep as Context (Theme provider)';
  } else if (content.includes('FormProvider') || fileName.includes('form')) {
    analysis.isFormContext = true;
    analysis.recommendation = 'Keep as Context (Form-specific)';
  } else if (content.includes('localStorage') || content.includes('sessionStorage')) {
    analysis.hasGlobalState = true;
    analysis.recommendation = 'Migrate to Zustand (Persistent state)';
  } else if (content.match(/use(State|Reducer|Callback|Effect)/g)?.length > 5) {
    analysis.hasComplexLogic = true;
    analysis.recommendation = 'Migrate to Zustand (Complex logic)';
  } else {
    analysis.recommendation = 'Evaluate case-by-case';
  }
  
  if (analysis.recommendation.includes('Migrate')) {
    migrationCandidates.push(analysis);
  } else {
    keepAsContext.push(analysis);
  }
  
  console.log(`\n${fileName}:`);
  console.log(`  Path: ${relativePath}`);
  console.log(`  Recommendation: ${analysis.recommendation}`);
});

// Analyze Zustand stores
console.log('\n\n📊 Zustand Store Analysis:');
console.log('━'.repeat(60));

const storePatterns = {
  hasDevtools: 0,
  hasPersist: 0,
  hasImmer: 0,
  hasSelectors: 0,
  hasAsyncActions: 0
};

storeFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const relativePath = path.relative(process.cwd(), file);
  
  console.log(`\n${path.basename(file)}:`);
  console.log(`  Path: ${relativePath}`);
  
  // Check patterns
  const patterns = [];
  if (content.includes('devtools')) {
    patterns.push('devtools');
    storePatterns.hasDevtools++;
  }
  if (content.includes('persist')) {
    patterns.push('persist');
    storePatterns.hasPersist++;
  }
  if (content.includes('immer')) {
    patterns.push('immer');
    storePatterns.hasImmer++;
  }
  if (content.includes('get()')) {
    patterns.push('selectors');
    storePatterns.hasSelectors++;
  }
  if (content.includes('async')) {
    patterns.push('async actions');
    storePatterns.hasAsyncActions++;
  }
  
  console.log(`  Features: ${patterns.join(', ') || 'basic'}`);
});

// Create migration plan
const migrationPlan = {
  timestamp: new Date().toISOString(),
  summary: {
    totalContexts: contextFiles.length,
    totalStores: storeFiles.length,
    migrationCandidates: migrationCandidates.length,
    keepAsContext: keepAsContext.length
  },
  contexts: {
    migrate: migrationCandidates,
    keep: keepAsContext
  },
  stores: {
    files: storeFiles.map(f => path.relative(process.cwd(), f)),
    patterns: storePatterns
  },
  recommendations: [
    {
      priority: 'high',
      task: 'Migrate global state contexts to Zustand',
      files: migrationCandidates.filter(c => c.hasGlobalState).map(c => c.file)
    },
    {
      priority: 'medium',
      task: 'Standardize Zustand store patterns',
      details: 'Add devtools, persist, and TypeScript types to all stores'
    },
    {
      priority: 'low',
      task: 'Create store documentation',
      details: 'Document store interfaces and usage patterns'
    }
  ]
};

// Save migration plan
fs.writeFileSync(
  path.join(process.cwd(), 'scripts/state-management-analysis.json'),
  JSON.stringify(migrationPlan, null, 2)
);

// Create example migration
if (migrationCandidates.length > 0) {
  const example = migrationCandidates[0];
  const migrationExample = `// Example migration for ${example.name}

// Before (React Context):
// ${example.file}

// After (Zustand Store):
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

interface StoreState {
  // Add state properties from context
  // Add actions from context
}

export const useStore = create<StoreState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        
        // Actions
      }),
      {
        name: '${example.name.replace('.tsx', '')}-storage'
      }
    ),
    {
      name: '${example.name.replace('.tsx', '')}Store'
    }
  )
)

// Update component usage:
// const { state, action } = useStore()
`;

  fs.writeFileSync(
    path.join(process.cwd(), 'scripts/example-context-migration.ts'),
    migrationExample
  );
}

// Summary
console.log('\n\n📊 Summary:');
console.log('━'.repeat(60));
console.log(`Total Contexts: ${contextFiles.length}`);
console.log(`  - Keep as Context: ${keepAsContext.length}`);
console.log(`  - Migrate to Zustand: ${migrationCandidates.length}`);
console.log(`\nTotal Zustand Stores: ${storeFiles.length}`);
console.log(`  - With devtools: ${storePatterns.hasDevtools}`);
console.log(`  - With persistence: ${storePatterns.hasPersist}`);
console.log(`  - With async actions: ${storePatterns.hasAsyncActions}`);

console.log('\n📝 Recommendations:');
migrationPlan.recommendations.forEach(rec => {
  console.log(`\n[${rec.priority.toUpperCase()}] ${rec.task}`);
  if (rec.details) console.log(`  ${rec.details}`);
  if (rec.files && rec.files.length > 0) {
    console.log(`  Files: ${rec.files.join(', ')}`);
  }
});

console.log('\n✅ State management analysis complete!');
console.log('📄 See scripts/state-management-analysis.json for details');