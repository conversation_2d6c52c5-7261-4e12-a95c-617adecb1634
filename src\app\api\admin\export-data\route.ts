import { NextResponse } from 'next/server'
import { handleAPIError, AuthorizationError } from '@/lib/api/error-handler';
import { createClient } from '@/lib/supabase'
import { requireAuth } from '@/lib/api/auth-middleware'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'

export async function GET(request: Request) {
  try {
    // Check authentication
    const authResult = await requireAuth(request)
    if (authResult instanceof NextResponse) return authResult
    
    const { user } = authResult
    const supabase = await createClient()

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profile?.role !== 'admin') {
      return handleAPIError(new AuthorizationError())
    }

    // Collect data for export
    const exportData = {
      exportDate: new Date().toISOString(),
      exportedBy: user.email,
      data: {}
    }

    // Get all data (simplified - in production would be more selective)
    const [
      users,
      projects,
      subscriptions,
      aiUsage
    ] = await Promise.all([
      supabase.from('users').select('id, email, created_at'),
      supabase.from('projects').select('id, title, user_id, created_at'),
      supabase.from('user_subscriptions').select('*'),
      supabase.from('ai_usage_logs').select('*').order('timestamp', { ascending: false }).limit(TIME_MS.SECOND)
    ])

    exportData.data = {
      users: users.data || [],
      projects: projects.data || [],
      subscriptions: subscriptions.data || [],
      recentAIUsage: aiUsage.data || []
    }

    // Log the export
    logger.info('Admin data export', { adminId: user.id, timestamp: exportData.exportDate })

    // Return as downloadable JSON
    return new NextResponse(JSON.stringify(exportData, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="bookscribe-export-${Date.now()}.json"`
      }
    })
  } catch (error) {
    logger.error('Admin export error:', error)
    return NextResponse.json(
      { error: 'Export failed' },
      { status: 500 }
    )
  }
}