'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Trophy, 
  Target,
  Zap,
  TrendingUp,
  Medal,
  ArrowRight
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export function EmptyAchievements() {
  const router = useRouter()

  return (
    <div className="max-w-4xl mx-auto py-12">
      <Card className="border-2 border-dashed border-muted-foreground/20">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/10 flex items-center justify-center">
            <Trophy className="w-8 h-8 text-primary" />
          </div>
          
          <h2 className="text-2xl font-literary-display text-foreground mb-4">
            No Achievements Yet
          </h2>
          
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto">
            Start writing to unlock achievements and track your progress. Earn rewards for consistency, quality, and milestones.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Zap className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Writing Streaks</h3>
              <p className="text-sm text-muted-foreground">
                Build daily habits and maintain consistency
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Target className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Word Count Goals</h3>
              <p className="text-sm text-muted-foreground">
                Hit milestones and track your productivity
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Medal className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Quality Badges</h3>
              <p className="text-sm text-muted-foreground">
                Earn recognition for exceptional writing
              </p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 justify-center">
            <Button 
              size="lg" 
              onClick={() => router.push('/projects')}
              className="group"
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              Start Writing
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => router.push('/goals')}
            >
              <Target className="w-4 h-4 mr-2" />
              Set Goals
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}