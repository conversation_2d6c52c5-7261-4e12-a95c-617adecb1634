-- Create character insights cache table
CREATE TABLE IF NOT EXISTS character_insights_cache (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  character_id uuid NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  insights jsonb NOT NULL DEFAULT '[]'::jsonb,
  overall_score integer DEFAULT 0,
  strengths_count integer DEFAULT 0,
  improvements_count integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Add unique constraint to prevent duplicate entries
  UNIQUE(character_id, project_id, created_at)
);

-- Add indexes for efficient querying
CREATE INDEX idx_character_insights_cache_character_id ON character_insights_cache(character_id);
CREATE INDEX idx_character_insights_cache_project_id ON character_insights_cache(project_id);
CREATE INDEX idx_character_insights_cache_created_at ON character_insights_cache(created_at DESC);

-- Add RLS policies
ALTER TABLE character_insights_cache ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own project insights
CREATE POLICY "Users can read insights for their projects" ON character_insights_cache
  FOR SELECT
  USING (
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    )
  );

-- Policy for users to create insights for their projects
CREATE POLICY "Users can create insights for their projects" ON character_insights_cache
  FOR INSERT
  WITH CHECK (
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    )
  );

-- Add cleanup function to remove old cache entries
CREATE OR REPLACE FUNCTION cleanup_old_character_insights()
RETURNS void AS $$
BEGIN
  DELETE FROM character_insights_cache
  WHERE created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Optional: Create a scheduled job to run cleanup (if using pg_cron)
-- SELECT cron.schedule('cleanup-character-insights', '0 2 * * *', 'SELECT cleanup_old_character_insights();');