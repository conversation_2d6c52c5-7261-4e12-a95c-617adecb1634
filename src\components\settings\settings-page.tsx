'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ProfileSettingsSection } from './profile-settings-section';
import { ThemeSettingsSection } from './theme-settings-section';
import { EditorSettingsSection } from './editor-settings-section';
import { AccessibilitySettingsSection } from './accessibility-settings-section';
import { User, Palette, FileEdit, Accessibility, CreditCard, Bell, Shield, Link as LinkIcon } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface SettingsPageProps {
  user: SupabaseUser;
}

export function SettingsPage({ user }: SettingsPageProps) {
  const [activeTab, setActiveTab] = useState('profile');

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground mt-2">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            <Button
              variant={activeTab === 'profile' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('profile')}
            >
              <User className="mr-2 h-4 w-4" />
              Profile
            </Button>
            <Button
              variant={activeTab === 'theme' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('theme')}
            >
              <Palette className="mr-2 h-4 w-4" />
              Appearance
            </Button>
            <Button
              variant={activeTab === 'editor' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('editor')}
            >
              <FileEdit className="mr-2 h-4 w-4" />
              Editor
            </Button>
            <Button
              variant={activeTab === 'accessibility' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('accessibility')}
            >
              <Accessibility className="mr-2 h-4 w-4" />
              Accessibility
            </Button>
            
            <Separator className="my-4" />
            
            <Button
              variant={activeTab === 'billing' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('billing')}
            >
              <CreditCard className="mr-2 h-4 w-4" />
              Billing
            </Button>
            <Button
              variant={activeTab === 'notifications' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('notifications')}
            >
              <Bell className="mr-2 h-4 w-4" />
              Notifications
            </Button>
            <Button
              variant={activeTab === 'security' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('security')}
            >
              <Shield className="mr-2 h-4 w-4" />
              Security
            </Button>
            <Button
              variant={activeTab === 'integrations' ? 'secondary' : 'ghost'}
              className="w-full justify-start"
              onClick={() => setActiveTab('integrations')}
            >
              <LinkIcon className="mr-2 h-4 w-4" />
              Integrations
            </Button>
          </nav>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-6">
              {activeTab === 'profile' && (
                <ProfileSettingsSection user={user} />
              )}
              
              {activeTab === 'theme' && (
                <ThemeSettingsSection />
              )}
              
              {activeTab === 'editor' && (
                <EditorSettingsSection />
              )}
              
              {activeTab === 'accessibility' && (
                <AccessibilitySettingsSection />
              )}
              
              {activeTab === 'billing' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Billing & Subscription</h2>
                    <p className="text-muted-foreground">
                      Manage your subscription and payment methods
                    </p>
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Current Plan</h3>
                      <p className="text-sm text-muted-foreground">
                        You are currently on the Free plan
                      </p>
                    </div>
                    <Link href="/billing">
                      <Button>Manage Billing</Button>
                    </Link>
                  </div>
                </div>
              )}
              
              {activeTab === 'notifications' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Notification Preferences</h2>
                    <p className="text-muted-foreground">
                      Choose how you want to be notified
                    </p>
                  </div>
                  <Separator />
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Notification settings coming soon...
                    </p>
                  </div>
                </div>
              )}
              
              {activeTab === 'security' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Security Settings</h2>
                    <p className="text-muted-foreground">
                      Manage your account security
                    </p>
                  </div>
                  <Separator />
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium mb-2">Two-Factor Authentication</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Add an extra layer of security to your account
                      </p>
                      <Button variant="outline" disabled>
                        Enable 2FA (Coming Soon)
                      </Button>
                    </div>
                    <Separator />
                    <div>
                      <h3 className="font-medium mb-2">Active Sessions</h3>
                      <p className="text-sm text-muted-foreground">
                        Manage devices where you're signed in
                      </p>
                    </div>
                  </div>
                </div>
              )}
              
              {activeTab === 'integrations' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Integrations</h2>
                    <p className="text-muted-foreground">
                      Connect BookScribe with other services
                    </p>
                  </div>
                  <Separator />
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Third-party integrations coming soon...
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}