'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { InputField } from '@/components/ui/form-field'
import { useFormValidation } from '@/hooks/use-form-validation'
import { emailUpdateSchema, passwordUpdateSchema, type EmailUpdateFormData, type PasswordUpdateFormData } from '@/lib/validation/profile-schemas'
import type { User } from '@supabase/supabase-js'
import { useToast } from '@/hooks/use-toast'

interface ProfileFormProps {
  user: User
}

export function ProfileForm({ user }: ProfileFormProps) {
  const [emailData, setEmailData] = useState<EmailUpdateFormData>({
    email: user.email || '',
  })
  const [passwordData, setPasswordData] = useState<PasswordUpdateFormData>({
    newPassword: '',
    confirmPassword: '',
  })
  const [loading, setLoading] = useState(false)
  const supabase = createClient()
  const { toast } = useToast()

  const emailValidation = useFormValidation({
    schema: emailUpdateSchema,
    validateOnBlur: true,
  })

  const passwordValidation = useFormValidation({
    schema: passwordUpdateSchema,
    validateOnBlur: true,
  })

  const handleEmailUpdate = async (e: React.FormEvent) => {
    e.preventDefault()

    const validation = await emailValidation.validateForm(emailData)
    if (!validation.isValid) {
      return
    }

    setLoading(true)

    try {
      const { error } = await supabase.auth.updateUser({
        email: emailData.email
      })

      if (error) throw error
      
      toast({
        title: "Email update sent",
        description: "Please check your new email for confirmation.",
      })
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault()

    const validation = await passwordValidation.validateForm(passwordData)
    if (!validation.isValid) {
      return
    }

    setLoading(true)

    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      })

      if (error) throw error
      
      toast({
        title: "Password updated",
        description: "Your password has been updated successfully.",
      })
      setPasswordData({ newPassword: '', confirmPassword: '' })
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold">Account Settings</h2>
        <p className="text-muted-foreground">
          Manage your account information and security settings
        </p>
      </div>

      {/* Email Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Email Address</CardTitle>
          <CardDescription>
            Update your email address. You&apos;ll need to verify the new email.
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleEmailUpdate}>
          <CardContent className="space-y-4">
            <InputField
              id="email"
              label="Email"
              type="email"
              value={emailData.email}
              onChange={(value) => setEmailData({ email: value as string })}
              onBlur={() => emailValidation.handleFieldBlur('email', emailData.email, emailData)}
              error={emailValidation.shouldShowFieldError('email') ? emailValidation.getFieldError('email') : undefined}
              disabled={loading}
              required
            />
          </CardContent>
          <CardFooter>
            <Button 
              type="submit" 
              disabled={loading || emailValidation.isValidating || emailData.email === user.email}
            >
              {loading ? 'Updating...' : 'Update Email'}
            </Button>
          </CardFooter>
        </form>
      </Card>

      <Separator />

      {/* Password Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Password</CardTitle>
          <CardDescription>
            Change your password to keep your account secure.
          </CardDescription>
        </CardHeader>
        <form onSubmit={handlePasswordUpdate}>
          <CardContent className="space-y-4">
            <InputField
              id="newPassword"
              label="New Password"
              type="password"
              placeholder="Must contain uppercase, lowercase, and number"
              value={passwordData.newPassword}
              onChange={(value) => setPasswordData(prev => ({ ...prev, newPassword: value as string }))}
              onBlur={() => passwordValidation.handleFieldBlur('newPassword', passwordData.newPassword, passwordData)}
              error={passwordValidation.shouldShowFieldError('newPassword') ? passwordValidation.getFieldError('newPassword') : undefined}
              disabled={loading}
              required
            />
            
            <InputField
              id="confirmPassword"
              label="Confirm New Password"
              type="password"
              placeholder="Re-enter your new password"
              value={passwordData.confirmPassword}
              onChange={(value) => setPasswordData(prev => ({ ...prev, confirmPassword: value as string }))}
              onBlur={() => passwordValidation.handleFieldBlur('confirmPassword', passwordData.confirmPassword, passwordData)}
              error={passwordValidation.shouldShowFieldError('confirmPassword') ? passwordValidation.getFieldError('confirmPassword') : undefined}
              disabled={loading}
              required
            />
          </CardContent>
          <CardFooter>
            <Button 
              type="submit" 
              disabled={loading || passwordValidation.isValidating || !passwordData.newPassword || !passwordData.confirmPassword}
            >
              {loading ? 'Updating...' : 'Update Password'}
            </Button>
          </CardFooter>
        </form>
      </Card>

      <Separator />

      {/* Account Information */}
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
          <CardDescription>
            Your account details and metadata.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Label className="text-muted-foreground">User ID</Label>
              <p className="font-mono text-xs">{user.id}</p>
            </div>
            <div>
              <Label className="text-muted-foreground">Account Created</Label>
              <p>{new Date(user.created_at).toLocaleDateString()}</p>
            </div>
            <div>
              <Label className="text-muted-foreground">Last Sign In</Label>
              <p>{user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Never'}</p>
            </div>
            <div>
              <Label className="text-muted-foreground">Email Confirmed</Label>
              <p>{user.email_confirmed_at ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}