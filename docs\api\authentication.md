# BookScribe Authentication & Security Documentation

## Table of Contents
- [Overview](#overview)
- [Authentication Architecture](#authentication-architecture)
- [Authentication Flow](#authentication-flow)
- [Security Measures](#security-measures)
- [API Authentication](#api-authentication)
- [Row-Level Security](#row-level-security)
- [Session Management](#session-management)
- [Security Best Practices](#security-best-practices)

## Overview

BookScribe implements a comprehensive security architecture using Supabase Auth for authentication, JWT tokens for API access, and PostgreSQL Row-Level Security (RLS) for data protection.

### Key Security Features
- **JWT-based Authentication**: Secure token-based auth via Supabase
- **Row-Level Security**: Database-level access control
- **Role-Based Access Control**: Admin, user, and guest roles
- **Secure Session Management**: Automatic token refresh
- **Content Security Policy**: XSS and injection protection
- **Rate Limiting**: DDoS and abuse prevention
- **Input Validation**: Zod schema validation throughout

## Authentication Architecture

### System Overview

```mermaid
graph TB
    subgraph "Client Layer"
        Browser[Web Browser]
        AuthUI[Auth Components]
        Storage[Secure Storage]
    end
    
    subgraph "Authentication Layer"
        SupaAuth[Supabase Auth]
        JWT[JWT Tokens]
        Session[Session Manager]
    end
    
    subgraph "API Layer"
        Middleware[Auth Middleware]
        RateLimit[Rate Limiter]
        Validation[Input Validation]
    end
    
    subgraph "Data Layer"
        RLS[Row Level Security]
        Database[(PostgreSQL)]
    end
    
    Browser --> AuthUI
    AuthUI --> SupaAuth
    SupaAuth --> JWT
    JWT --> Storage
    
    Browser --> Middleware
    Middleware --> Session
    Session --> JWT
    
    Middleware --> RateLimit
    RateLimit --> Validation
    Validation --> RLS
    RLS --> Database
```

### Authentication Components

#### Supabase Auth Configuration
```typescript
// src/lib/supabase/client.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

export const supabase = createClientComponentClient({
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
  supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  options: {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true
    }
  }
})
```

#### Unified Auth Service
```typescript
// src/lib/auth/unified-auth-service.ts
export class UnifiedAuthService {
  async signUp(email: string, password: string, metadata?: any) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    })
    return { data, error }
  }

  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  }

  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  async getSession() {
    const { data: { session } } = await supabase.auth.getSession()
    return session
  }

  async getUser() {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  }
}
```

## Authentication Flow

### Sign Up Flow

```mermaid
sequenceDiagram
    participant User
    participant Client
    participant SupaAuth
    participant Email
    participant Database
    
    User->>Client: Enter credentials
    Client->>SupaAuth: signUp(email, password)
    SupaAuth->>Database: Create auth.users entry
    SupaAuth->>Email: Send confirmation
    Email-->>User: Confirmation link
    User->>Client: Click confirmation
    Client->>SupaAuth: Verify email token
    SupaAuth->>Database: Update email_verified
    SupaAuth-->>Client: Session & tokens
    Client->>Client: Store session
    Client-->>User: Redirect to dashboard
```

### Sign In Flow

```mermaid
sequenceDiagram
    participant User
    participant Client
    participant SupaAuth
    participant Database
    
    User->>Client: Enter credentials
    Client->>SupaAuth: signInWithPassword()
    SupaAuth->>Database: Verify credentials
    Database-->>SupaAuth: User data
    SupaAuth-->>Client: Session & JWT
    Client->>Client: Store tokens
    Client-->>User: Redirect to app
```

### OAuth Flow (Social Login)

```typescript
// OAuth provider setup
async function signInWithProvider(provider: 'google' | 'github') {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: `${window.location.origin}/auth/callback`,
      scopes: provider === 'github' ? 'read:user user:email' : undefined
    }
  })
}
```

## Security Measures

### API Middleware

#### Authentication Middleware
```typescript
// src/lib/api/unified-middleware.ts
export async function withAuth(
  req: NextRequest,
  handler: (req: NextRequest, user: User) => Promise<Response>
) {
  const token = req.headers.get('authorization')?.replace('Bearer ', '')
  
  if (!token) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  }

  try {
    const { data: { user }, error } = await supabase.auth.getUser(token)
    
    if (error || !user) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    return handler(req, user)
  } catch (error) {
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 401 }
    )
  }
}
```

#### Rate Limiting
```typescript
// src/lib/rate-limiter-unified.ts
export class UnifiedRateLimiter {
  private limits = {
    free: { requests: 100, window: 3600 }, // 100/hour
    starter: { requests: 1000, window: 3600 }, // 1000/hour
    professional: { requests: 10000, window: 3600 }, // 10000/hour
    enterprise: { requests: Infinity, window: 3600 } // Unlimited
  }

  async checkLimit(userId: string, tier: SubscriptionTier) {
    const key = `rate_limit:${userId}`
    const limit = this.limits[tier]
    
    const current = await redis.incr(key)
    
    if (current === 1) {
      await redis.expire(key, limit.window)
    }
    
    if (current > limit.requests) {
      throw new RateLimitError('Rate limit exceeded')
    }
    
    return {
      remaining: limit.requests - current,
      reset: await redis.ttl(key)
    }
  }
}
```

### Input Validation

#### Zod Schema Validation
```typescript
// src/lib/validation/unified-schemas.ts
import { z } from 'zod'

export const projectSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  settings: z.object({
    primaryGenre: z.enum(['fantasy', 'scifi', 'mystery', 'romance']),
    targetWordCount: z.number().min(1000).max(500000),
    narrativeVoice: z.enum(['first_person', 'third_person', 'omniscient'])
  })
})

// API endpoint validation
export async function validateRequest<T>(
  data: unknown,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    return schema.parse(data)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('Invalid request data', error.errors)
    }
    throw error
  }
}
```

### Content Security Policy

```typescript
// src/app/layout.tsx
export const metadata: Metadata = {
  // ... other metadata
  other: {
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https: blob:",
      "font-src 'self' data:",
      "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.openai.com",
      "frame-src 'self' https://js.stripe.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests"
    ].join('; ')
  }
}
```

## API Authentication

### Protected API Routes

```typescript
// src/app/api/projects/route.ts
export async function GET(req: NextRequest) {
  return withAuth(req, async (req, user) => {
    // User is authenticated
    const projects = await getProjectsForUser(user.id)
    return NextResponse.json({ success: true, data: projects })
  })
}

export async function POST(req: NextRequest) {
  return withAuth(req, async (req, user) => {
    const body = await req.json()
    const validated = await validateRequest(body, projectSchema)
    
    const project = await createProject({
      ...validated,
      user_id: user.id
    })
    
    return NextResponse.json({ success: true, data: project })
  })
}
```

### Admin Authentication

```typescript
// Admin role check
export async function withAdmin(
  req: NextRequest,
  handler: (req: NextRequest, user: User) => Promise<Response>
) {
  return withAuth(req, async (req, user) => {
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    return handler(req, user)
  })
}
```

## Row-Level Security

### Database Policies

#### Projects Table RLS
```sql
-- Enable RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Users can view their own projects
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can view shared projects
CREATE POLICY "Users can view shared projects" ON projects
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM project_collaborators
      WHERE project_id = projects.id
      AND user_id = auth.uid()
      AND invitation_accepted = true
    )
  );

-- Users can create projects
CREATE POLICY "Users can create projects" ON projects
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own projects
CREATE POLICY "Users can update own projects" ON projects
  FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Users can delete their own projects
CREATE POLICY "Users can delete own projects" ON projects
  FOR DELETE
  USING (auth.uid() = user_id);
```

#### Characters Table RLS
```sql
-- Characters inherit project permissions
CREATE POLICY "Users can manage project characters" ON characters
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = characters.project_id
      AND (
        projects.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM project_collaborators
          WHERE project_id = projects.id
          AND user_id = auth.uid()
          AND role IN ('owner', 'editor')
        )
      )
    )
  );
```

### RLS Helper Functions

```sql
-- Check if user has project access
CREATE OR REPLACE FUNCTION has_project_access(project_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM projects p
    WHERE p.id = $1
    AND (
      p.user_id = auth.uid()
      OR EXISTS (
        SELECT 1 FROM project_collaborators pc
        WHERE pc.project_id = p.id
        AND pc.user_id = auth.uid()
        AND pc.invitation_accepted = true
      )
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Session Management

### Session Provider
```typescript
// src/components/auth/session-refresh-provider.tsx
export function SessionRefreshProvider({ children }: { children: ReactNode }) {
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'TOKEN_REFRESHED') {
        console.log('Token refreshed successfully')
      }
      
      if (event === 'SIGNED_OUT') {
        // Clear all application state
        clearStores()
        router.push('/login')
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  return <>{children}</>
}
```

### Token Refresh Strategy

```typescript
// Automatic token refresh setup
const REFRESH_THRESHOLD = 60 * 5 // 5 minutes before expiry

async function ensureValidSession() {
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) return null
  
  const expiresAt = session.expires_at
  const now = Math.floor(Date.now() / 1000)
  
  if (expiresAt - now < REFRESH_THRESHOLD) {
    const { data: { session: newSession } } = await supabase.auth.refreshSession()
    return newSession
  }
  
  return session
}
```

## Security Best Practices

### 1. Environment Variables
```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=xxx # Public key (safe for client)
SUPABASE_SERVICE_ROLE_KEY=xxx # Never expose to client!
```

### 2. Secure Headers
```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ]
  }
}
```

### 3. API Security Checklist

#### Authentication
- ✅ All API routes require authentication (except public endpoints)
- ✅ JWT tokens validated on every request
- ✅ Tokens expire and refresh automatically
- ✅ Secure token storage (httpOnly cookies for SSR)

#### Authorization
- ✅ Role-based access control implemented
- ✅ Resource-level permissions checked
- ✅ Database RLS policies enforced
- ✅ Admin routes separately protected

#### Input Validation
- ✅ All inputs validated with Zod schemas
- ✅ SQL injection prevented via parameterized queries
- ✅ XSS prevented via React's default escaping
- ✅ File upload restrictions enforced

#### Rate Limiting
- ✅ Per-user rate limits based on subscription
- ✅ Global rate limits for public endpoints
- ✅ DDoS protection at CDN level
- ✅ Exponential backoff for failed attempts

### 4. Data Protection

#### Encryption
- **In Transit**: All data encrypted with TLS 1.3
- **At Rest**: Database encryption via Supabase
- **Secrets**: Environment variables for sensitive data
- **Passwords**: Bcrypt hashing with salt rounds

#### Privacy
- **PII Protection**: Sensitive data marked in schema
- **Data Minimization**: Only collect necessary data
- **Right to Delete**: GDPR-compliant data deletion
- **Audit Logs**: Track all data access

### 5. Security Monitoring

```typescript
// Error reporting with sanitization
export function reportSecurityEvent(event: SecurityEvent) {
  // Sanitize sensitive data
  const sanitized = {
    ...event,
    user_id: hashUserId(event.user_id),
    ip_address: anonymizeIP(event.ip_address)
  }
  
  // Send to monitoring service
  Sentry.captureEvent(sanitized)
  
  // Log for audit
  logger.security(sanitized)
}
```

## Development Security

### Local Development
```bash
# Development auth bypass (NEVER in production!)
NEXT_PUBLIC_DEV_BYPASS_AUTH=true # Only for local development
```

### Testing Authentication
```typescript
// Test helpers
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  role: 'user'
}

export function mockSupabaseAuth() {
  return {
    auth: {
      getUser: jest.fn().mockResolvedValue({ data: { user: mockUser } }),
      getSession: jest.fn().mockResolvedValue({ data: { session: mockSession } })
    }
  }
}
```

## Security Incident Response

### 1. Detection
- Monitor failed auth attempts
- Track unusual API usage patterns
- Alert on RLS policy violations
- Watch for token abuse

### 2. Response
- Immediate user notification
- Force password reset if compromised
- Revoke all sessions
- Update security policies

### 3. Recovery
- Restore from secure backups
- Audit all access logs
- Update security measures
- Document lessons learned