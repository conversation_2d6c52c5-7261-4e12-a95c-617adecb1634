'use client'

import React, { useState, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Users, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react'

interface Character {
  id: string
  name: string
  role: string
  description: string
  relationships: Array<{
    characterId: string
    type: string
    description: string
  }>
}

interface CharacterRelationshipVizProps {
  characters: Character[]
}

interface Node {
  id: string
  name: string
  role: string
  x: number
  y: number
  fx?: number
  fy?: number
}

interface Link {
  source: string
  target: string
  type: string
  description: string
}

function CharacterRelationshipVizComponent({ characters }: CharacterRelationshipVizProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [zoom, setZoom] = useState(1)
  const [panX, setPanX] = useState(0)
  const [panY, setPanY] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  // Convert characters to nodes and links
  const nodes: Node[] = characters.map((char, index) => {
    const angle = (index / characters.length) * 2 * Math.PI
    const radius = Math.min(200, characters.length * 30)
    return {
      id: char.id,
      name: char.name,
      role: char.role,
      x: 300 + Math.cos(angle) * radius,
      y: 300 + Math.sin(angle) * radius
    }
  })

  const links: Link[] = []
  characters.forEach(char => {
    char.relationships?.forEach(rel => {
      // Only add link if target character exists
      if (characters.find(c => c.id === rel.characterId)) {
        links.push({
          source: char.id,
          target: rel.characterId,
          type: rel.type,
          description: rel.description
        })
      }
    })
  })

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX - panX, y: e.clientY - panY })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPanX(e.clientX - dragStart.x)
      setPanY(e.clientY - dragStart.y)
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 3))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.3))
  }

  const handleReset = () => {
    setZoom(1)
    setPanX(0)
    setPanY(0)
    setSelectedNode(null)
  }

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'protagonist': return 'hsl(var(--color-info))'
      case 'antagonist': return 'hsl(var(--color-error))'
      case 'supporting': return 'hsl(var(--color-success))'
      case 'minor': return 'hsl(var(--color-gray-600))'
      default: return 'hsl(var(--color-chart-5))'
    }
  }

  const getRelationshipColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'family': return 'hsl(var(--color-warning))'
      case 'friend': return 'hsl(var(--color-success))'
      case 'enemy': return 'hsl(var(--color-error))'
      case 'romantic': return 'hsl(var(--color-chart-6))'
      case 'ally': return 'hsl(var(--color-info))'
      case 'rival': return 'hsl(38 92% 60%)'
      default: return 'hsl(var(--color-gray-600))'
    }
  }

  if (characters.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Character Relationships
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 sm:py-8 lg:py-10">
            <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">No characters available for visualization</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Character Relationships
          </CardTitle>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleReset}>
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative overflow-hidden border rounded-lg" style={{ height: '600px' }}>
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
          >
            <g transform={`translate(${panX}, ${panY}) scale(${zoom})`}>
              {/* Render links */}
              {links.map((link, index) => {
                const sourceNode = nodes.find(n => n.id === link.source)
                const targetNode = nodes.find(n => n.id === link.target)
                if (!sourceNode || !targetNode) return null

                return (
                  <g key={index}>
                    <line
                      x1={sourceNode.x}
                      y1={sourceNode.y}
                      x2={targetNode.x}
                      y2={targetNode.y}
                      stroke={getRelationshipColor(link.type)}
                      strokeWidth="2"
                      opacity="0.6"
                    />
                    <text
                      x={(sourceNode.x + targetNode.x) / 2}
                      y={(sourceNode.y + targetNode.y) / 2}
                      textAnchor="middle"
                      fontSize="12"
                      fill="hsl(var(--color-gray-600))"
                      className="pointer-events-none"
                    >
                      {link.type}
                    </text>
                  </g>
                )
              })}

              {/* Render nodes */}
              {nodes.map((node) => (
                <g key={node.id}>
                  <circle
                    cx={node.x}
                    cy={node.y}
                    r="30"
                    fill={getRoleColor(node.role)}
                    stroke={selectedNode?.id === node.id ? "hsl(var(--foreground))" : "hsl(var(--background))"}
                    strokeWidth={selectedNode?.id === node.id ? "3" : "2"}
                    style={{ cursor: 'pointer' }}
                    onClick={() => setSelectedNode(selectedNode?.id === node.id ? null : node)}
                  />
                  <text
                    x={node.x}
                    y={node.y}
                    textAnchor="middle"
                    dy="0.35em"
                    fontSize="12"
                    fontWeight="bold"
                    fill="white"
                    className="pointer-events-none"
                  >
                    {node.name.length > 8 ? node.name.slice(0, 8) + '...' : node.name}
                  </text>
                  <text
                    x={node.x}
                    y={node.y + 45}
                    textAnchor="middle"
                    fontSize="10"
                    fill="hsl(var(--color-gray-600))"
                    className="pointer-events-none"
                  >
                    {node.role}
                  </text>
                </g>
              ))}
            </g>
          </svg>

          {/* Selected character details */}
          {selectedNode && (
            <div className="absolute top-4 right-4 w-64 bg-white border rounded-lg shadow-lg p-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-4 h-4 rounded-full" 
                    style={{ backgroundColor: getRoleColor(selectedNode.role) }}
                  />
                  <h3 className="font-semibold">{selectedNode.name}</h3>
                </div>
                <Badge variant="outline">{selectedNode.role}</Badge>
                
                {/* Show relationships */}
                <div className="space-y-1">
                  <h4 className="text-sm font-medium">Relationships:</h4>
                  {links
                    .filter(link => link.source === selectedNode.id || link.target === selectedNode.id)
                    .map((link, index) => {
                      const otherCharId = link.source === selectedNode.id ? link.target : link.source
                      const otherChar = characters.find(c => c.id === otherCharId)
                      return (
                        <div key={index} className="text-xs bg-muted p-2 rounded">
                          <span className="font-medium">{link.type}</span> with {otherChar?.name}
                          {link.description && (
                            <p className="text-muted-foreground mt-1">{link.description}</p>
                          )}
                        </div>
                      )
                    })
                  }
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="mt-4 flex flex-wrap gap-4 sm:gap-5 lg:gap-6 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full bg-info" />
            <span>Protagonist</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full bg-error" />
            <span>Antagonist</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full bg-success" />
            <span>Supporting</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full bg-gray-500" />
            <span>Minor</span>
          </div>
        </div>

        <div className="mt-2 text-xs text-muted-foreground">
          Click and drag to pan • Use zoom controls • Click characters for details
        </div>
      </CardContent>
    </Card>
  )
}

export const CharacterRelationshipViz = React.memo(CharacterRelationshipVizComponent)