import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { UnifiedResponse } from '@/lib/utils/response'
import { logger } from '@/lib/services/logger'

const querySchema = z.object({
  projectId: z.string().uuid(),
  timeRange: z.enum(['24h', '7d', '30d', '90d']).optional().default('7d'),
  granularity: z.enum(['hour', 'day', 'week']).optional()
})

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    const timeRange = searchParams.get('timeRange') || '7d'
    const granularity = searchParams.get('granularity')
    
    if (!projectId) {
      return UnifiedResponse.error('Project ID is required', 400)
    }

    const validation = querySchema.safeParse({ 
      projectId, 
      timeRange,
      granularity: granularity || (timeRange === '24h' ? 'hour' : timeRange === '7d' ? 'day' : 'week')
    })
    
    if (!validation.success) {
      return UnifiedResponse.error('Invalid query parameters', 400, validation.error.errors)
    }

    const { timeRange: range, granularity: gran } = validation.data
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Calculate date range
    const now = new Date()
    const startDate = new Date()
    switch (range) {
      case '24h':
        startDate.setHours(now.getHours() - 24)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
    }

    // Get memory usage history from AI generations
    const { data: generations, error: genError } = await supabase
      .from('ai_generations')
      .select('tokens_used, model, created_at')
      .eq('project_id', projectId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true })

    if (genError) {
      logger.error('Error fetching usage history:', genError)
      return UnifiedResponse.error('Failed to fetch usage history')
    }

    // Get optimization events
    const { data: optimizations } = await supabase
      .from('memory_optimization_logs')
      .select('*')
      .eq('project_id', projectId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true })

    // Process data into time series
    const dataPoints = processTimeSeriesData(generations || [], optimizations || [], gran)
    
    // Calculate statistics
    const stats = {
      totalTokensUsed: generations?.reduce((sum, gen) => sum + (gen.tokens_used || 0), 0) || 0,
      averageTokensPerGeneration: generations?.length 
        ? Math.round((generations.reduce((sum, gen) => sum + (gen.tokens_used || 0), 0) || 0) / generations.length)
        : 0,
      peakUsage: Math.max(...dataPoints.map(dp => dp.tokens)),
      optimizationCount: optimizations?.length || 0,
      tokensSaved: optimizations?.reduce((sum, opt) => sum + (opt.tokens_saved || 0), 0) || 0
    }

    return UnifiedResponse.success({
      projectId,
      timeRange: range,
      granularity: gran,
      dataPoints,
      stats,
      optimizations: optimizations?.map(opt => ({
        timestamp: opt.created_at,
        type: opt.optimization_type,
        tokensSaved: opt.tokens_saved,
        strategy: opt.strategy
      })) || []
    })
  } catch (error) {
    logger.error('Memory usage history error:', error)
    return UnifiedResponse.error('Failed to fetch usage history')
  }
})

function processTimeSeriesData(
  generations: any[], 
  optimizations: any[], 
  granularity: 'hour' | 'day' | 'week'
): any[] {
  const dataMap = new Map<string, { tokens: number, generations: number, optimizations: number }>()
  
  // Process generations
  generations.forEach(gen => {
    const key = getTimeKey(new Date(gen.created_at), granularity)
    const existing = dataMap.get(key) || { tokens: 0, generations: 0, optimizations: 0 }
    existing.tokens += gen.tokens_used || 0
    existing.generations += 1
    dataMap.set(key, existing)
  })
  
  // Process optimizations
  optimizations.forEach(opt => {
    const key = getTimeKey(new Date(opt.created_at), granularity)
    const existing = dataMap.get(key) || { tokens: 0, generations: 0, optimizations: 0 }
    existing.optimizations += 1
    dataMap.set(key, existing)
  })
  
  // Convert to array and sort
  return Array.from(dataMap.entries())
    .map(([timestamp, data]) => ({
      timestamp,
      ...data
    }))
    .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
}

function getTimeKey(date: Date, granularity: 'hour' | 'day' | 'week'): string {
  switch (granularity) {
    case 'hour':
      return new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        date.getHours()
      ).toISOString()
    case 'day':
      return new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate()
      ).toISOString()
    case 'week':
      const weekStart = new Date(date)
      weekStart.setDate(date.getDate() - date.getDay())
      return new Date(
        weekStart.getFullYear(),
        weekStart.getMonth(),
        weekStart.getDate()
      ).toISOString()
  }
}