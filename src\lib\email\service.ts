import { createClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { ResendProvider } from './providers/resend'
import type { 
  EmailProvider, 
  SendEmailOptions, 
  EmailResult, 
  EmailTemplateType,
  EmailQueueItem,
  EmailTemplateData,
  WelcomeEmailData,
  PasswordResetEmailData,
  WeeklyProgressEmailData,
  SubscriptionConfirmationEmailData,
  WritingMilestoneEmailData,
  CollaborationInviteEmailData,
  AchievementUnlockedEmailData
} from './types'
import * as templates from './templates'
import { TIME_MS } from '@/lib/constants'

class EmailService {
  private provider: EmailProvider | null = null
  private static instance: EmailService

  private constructor() {
    // Initialize with Resend if API key is available
    const resendApiKey = process.env.RESEND_API_KEY
    if (resendApiKey) {
      this.provider = new ResendProvider(resendApiKey)
    }
  }

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService()
    }
    return EmailService.instance
  }

  async verifyConfiguration(): Promise<boolean> {
    if (!this.provider) {
      logger.warn('No email provider configured')
      return false
    }
    
    return this.provider.verifyConfiguration()
  }

  private getTemplate(type: EmailTemplateType, data: EmailTemplateData) {
    switch (type) {
      case 'welcome':
        return templates.welcomeTemplate(data as WelcomeEmailData)
      case 'password-reset':
        return templates.passwordResetTemplate(data as PasswordResetEmailData)
      case 'weekly-progress':
        return templates.weeklyProgressTemplate(data as WeeklyProgressEmailData)
      case 'subscription-confirmation':
        return templates.subscriptionConfirmationTemplate(data as SubscriptionConfirmationEmailData)
      case 'writing-milestone':
        return templates.writingMilestoneTemplate(data as WritingMilestoneEmailData)
      case 'collaboration-invite':
        return templates.collaborationInviteTemplate(data as CollaborationInviteEmailData)
      case 'achievement-unlocked':
        return templates.achievementUnlockedTemplate(data as AchievementUnlockedEmailData)
      default:
        // TypeScript exhaustiveness check
        const _exhaustive: never = type
        throw new Error(`Unknown email template type: ${type}`)
    }
  }

  async sendEmail(options: SendEmailOptions): Promise<EmailResult> {
    if (!this.provider) {
      logger.error('Email provider not configured')
      return {
        id: '',
        success: false,
        error: 'Email service not configured',
        provider: 'none'
      }
    }

    try {
      // Get template
      const template = this.getTemplate(options.template, options.data)
      
      // Add unsubscribe URLs
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:TIME_MS.TYPING_TIMEOUT'
      const userId = options.data.user?.id || ''
      const unsubscribeToken = await this.generateUnsubscribeToken(userId)
      
      const processedHtml = template.html
        .replace('{{unsubscribeUrl}}', `${baseUrl}/unsubscribe?token=${unsubscribeToken}`)
        .replace('{{preferencesUrl}}', `${baseUrl}/email-preferences`)
      
      // Send email
      const result = await this.provider.sendEmail({
        ...options,
        subject: template.subject,
        html: processedHtml,
        text: template.text,
        from: options.from || 'BookScribe AI <<EMAIL>>',
        replyTo: options.replyTo || '<EMAIL>'
      })

      // Log email send
      if (result.success) {
        await this.logEmailSent(options, result)
      }

      return result
    } catch (error) {
      logger.error('Failed to send email:', error)
      return {
        id: '',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send email',
        provider: this.provider.name
      }
    }
  }

  async queueEmail(options: SendEmailOptions & { scheduledFor?: Date }): Promise<string> {
    const supabase = await createClient()
    
    const queueItem: Partial<EmailQueueItem> = {
      to: Array.isArray(options.to) ? options.to.join(',') : options.to,
      template: options.template,
      data: options.data,
      status: 'pending',
      attempts: 0,
      scheduledFor: options.scheduledFor || new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const { data, error } = await supabase
      .from('email_queue')
      .insert(queueItem)
      .select('id')
      .single()

    if (error) {
      logger.error('Failed to queue email:', error)
      throw error
    }

    return data.id
  }

  async processEmailQueue(): Promise<void> {
    const supabase = await createClient()
    
    // Get pending emails that are due
    const { data: emails, error } = await supabase
      .from('email_queue')
      .select('*')
      .eq('status', 'pending')
      .lte('scheduledFor', new Date().toISOString())
      .lt('attempts', 3)
      .limit(10)

    if (error) {
      logger.error('Failed to fetch email queue:', error)
      return
    }

    for (const email of emails || []) {
      await this.processQueuedEmail(email)
    }
  }

  private async processQueuedEmail(queueItem: EmailQueueItem): Promise<void> {
    const supabase = await createClient()
    
    // Update status to processing
    await supabase
      .from('email_queue')
      .update({ 
        status: 'processing',
        attempts: queueItem.attempts + 1,
        updatedAt: new Date()
      })
      .eq('id', queueItem.id)

    try {
      // Send email
      const result = await this.sendEmail({
        to: queueItem.to,
        template: queueItem.template,
        data: queueItem.data
      })

      // Update status based on result
      await supabase
        .from('email_queue')
        .update({
          status: result.success ? 'sent' : 'failed',
          error: result.error,
          sentAt: result.success ? new Date() : null,
          updatedAt: new Date()
        })
        .eq('id', queueItem.id)

    } catch (error) {
      // Update status to failed
      await supabase
        .from('email_queue')
        .update({
          status: queueItem.attempts >= 3 ? 'failed' : 'pending',
          error: error instanceof Error ? error.message : 'Unknown error',
          updatedAt: new Date()
        })
        .eq('id', queueItem.id)
    }
  }

  private async generateUnsubscribeToken(userId: string): Promise<string> {
    // In production, this should generate a secure token
    // For now, we'll use a simple base64 encoding
    return Buffer.from(`${userId}:${Date.now()}`).toString('base64')
  }

  private async logEmailSent(options: SendEmailOptions, result: EmailResult): Promise<void> {
    try {
      const supabase = await createClient()
      await supabase
        .from('email_logs')
        .insert({
          to: Array.isArray(options.to) ? options.to.join(',') : options.to,
          template: options.template,
          provider: result.provider,
          provider_id: result.id,
          sent_at: new Date()
        })
    } catch (error) {
      logger.error('Failed to log email:', error)
    }
  }

  async updateEmailPreferences(userId: string, preferences: Partial<{
    marketing: boolean
    progress: boolean
    achievements: boolean
    collaboration: boolean
    newsletter: boolean
  }>): Promise<void> {
    const supabase = await createClient()
    
    const { error } = await supabase
      .from('email_preferences')
      .upsert({
        user_id: userId,
        ...preferences,
        updated_at: new Date()
      })

    if (error) {
      logger.error('Failed to update email preferences:', error)
      throw error
    }
  }

  async getEmailPreferences(userId: string): Promise<any> {
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .from('email_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error && error.code !== 'PGRST116') { // Not found error
      logger.error('Failed to get email preferences:', error)
      throw error
    }

    // Return default preferences if not found
    return data || {
      marketing: true,
      progress: true,
      achievements: true,
      collaboration: true,
      newsletter: true
    }
  }
}

export const emailService = EmailService.getInstance()