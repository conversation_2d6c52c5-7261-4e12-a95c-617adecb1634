import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server';
import { createTypedServerClient } from '@/lib/supabase';
import type { Character, Chapter } from '@/lib/types/character-development';
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient();
    const { 
      characterId, 
      projectId, 
      focusArea = 'general', 
      currentChapter = 1,
      urgency = 'medium'
    } = await request.json();

    if (!characterId || !projectId) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Verify user has access to this project
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single();

    if (!project || project.user_id !== user.id) {
      return handleAPIError(new AuthorizationError());
    }

    // Fetch character and chapter data
    const [characterResult, chaptersResult, projectResult] = await Promise.all([
      supabase
        .from('characters')
        .select('*')
        .eq('id', characterId)
        .eq('project_id', projectId)
        .single(),
      supabase
        .from('chapters')
        .select('*')
        .eq('project_id', projectId)
        .order('chapter_number', { ascending: true }),
      supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single()
    ]);

    if (characterResult.error || !characterResult.data) {
      return handleAPIError(new NotFoundError('Resource'));
    }

    // Generate targeted suggestions
    const suggestions = await generateArcSuggestions(
      characterResult.data,
      chaptersResult.data || [],
      projectResult.data,
      focusArea,
      currentChapter,
      urgency
    );

    return NextResponse.json(suggestions);
  } catch (error) {
    logger.error('Error generating arc suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to generate arc suggestions' },
      { status: 500 }
    );
  }
}

interface Project {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  primary_genre?: string;
  target_word_count?: number;
  [key: string]: unknown;
}

interface Suggestion {
  id?: string;
  type: string;
  title: string;
  description: string;
  chapter?: number;
  chapters?: number[];
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  category?: string;
  reasoning?: string;
  techniques?: string[];
}

interface CharacterAnalysis {
  presenceScore: number;
  emotionalState: {
    stagnant: boolean;
    range: number;
  };
  relationshipCount: number;
  skillDevelopment: { count?: number } | number;
  arcProgress: number;
  consistencyIssues: string[];
  lastAppearance: number;
  characterMentions: number;
}

interface SuggestionGroup {
  immediate: Suggestion[];
  shortTerm: Suggestion[];
  longTerm: Suggestion[];
  opportunistic: Suggestion[];
}

async function generateArcSuggestions(
  character: Character,
  chapters: Chapter[],
  project: Project,
  focusArea: string,
  currentChapter: number,
  urgency: string
) {
  const suggestions = {
    immediate: [] as Suggestion[],
    shortTerm: [] as Suggestion[],
    longTerm: [] as Suggestion[],
    opportunistic: [] as Suggestion[]
  };

  const completedChapters = chapters.filter(c => 
    c.chapter_number < currentChapter && c.content
  );

  // Analyze current state
  const analysis = analyzeCharacterState(character, completedChapters);
  
  // Generate suggestions based on focus area
  switch (focusArea) {
    case 'emotional_growth':
      addEmotionalGrowthSuggestions(suggestions, character, analysis, currentChapter);
      break;
    case 'relationships':
      addRelationshipSuggestions(suggestions, character, analysis, chapters, currentChapter);
      break;
    case 'skills':
      addSkillProgressionSuggestions(suggestions, character, analysis, project, currentChapter);
      break;
    case 'arc_progression':
      addArcProgressionSuggestions(suggestions, character, analysis, currentChapter);
      break;
    case 'consistency':
      addConsistencySuggestions(suggestions, character, analysis, currentChapter);
      break;
    default:
      addGeneralSuggestions(suggestions, character, analysis, currentChapter);
  }

  // Filter by urgency
  return filterByUrgency(suggestions, urgency);
}

function analyzeCharacterState(character: Character, chapters: Chapter[]): CharacterAnalysis {
  return {
    presenceScore: calculatePresenceScore(character, chapters),
    emotionalState: analyzeEmotionalState(character, chapters),
    relationshipCount: countRelationships(character, chapters),
    skillDevelopment: analyzeSkillDevelopment(character, chapters),
    arcProgress: calculateArcProgress(character, chapters),
    consistencyIssues: findConsistencyIssues(character, chapters),
    lastAppearance: findLastAppearance(character, chapters),
    characterMentions: countCharacterMentions(character, chapters)
  };
}

function addEmotionalGrowthSuggestions(
  suggestions: SuggestionGroup,
  character: Character,
  analysis: CharacterAnalysis,
  currentChapter: number
) {
  if (analysis.emotionalState.stagnant) {
    suggestions.immediate.push({
      type: 'emotional_catalyst',
      title: 'Create Emotional Catalyst',
      description: `${character.name} has been emotionally stagnant. Introduce a challenging situation that forces emotional growth.`,
      chapter: currentChapter + 1,
      effort: 'medium',
      impact: 'high',
      techniques: [
        'Have them face a fear from their backstory',
        'Force a difficult moral decision',
        'Create a loss that requires processing'
      ]
    });
  }

  if (analysis.emotionalState.range < 30) {
    suggestions.shortTerm.push({
      type: 'emotional_range',
      title: 'Expand Emotional Range',
      description: `Add scenes showing ${character.name} experiencing different emotions to create more depth.`,
      chapter: currentChapter + 3,
      effort: 'low',
      impact: 'medium',
      techniques: [
        'Show moments of vulnerability',
        'Add humor or lightness',
        'Include nostalgic memories'
      ]
    });
  }

  suggestions.opportunistic.push({
    type: 'emotional_breakthrough',
    title: 'Plan Emotional Breakthrough',
    description: `Set up a major emotional revelation for ${character.name} in the story\'s climactic moments.`,
    chapter: Math.floor(currentChapter * 1.5),
    effort: 'high',
    impact: 'high',
    techniques: [
      'Connect to their core motivation',
      'Resolve internal conflict',
      'Show transformed perspective'
    ]
  });
}

function addRelationshipSuggestions(
  suggestions: SuggestionGroup,
  character: Character,
  analysis: CharacterAnalysis,
  chapters: Chapter[],
  currentChapter: number
) {
  if (analysis.relationshipCount < 2) {
    suggestions.immediate.push({
      type: 'new_relationship',
      title: 'Introduce New Relationship',
      description: `${character.name} needs more meaningful connections. Consider adding a new relationship dynamic.`,
      chapter: currentChapter + 1,
      effort: 'medium',
      impact: 'high',
      techniques: [
        'Add a mentor figure',
        'Create a rival or foil',
        'Introduce a potential ally'
      ]
    });
  }

  // Check for other characters in the story
  const otherCharacters = chapters
    .flatMap(c => extractCharacterNames(c.content || ''))
    .filter(name => name !== character.name);

  if (otherCharacters.length > 0) {
    suggestions.shortTerm.push({
      type: 'deepen_existing',
      title: 'Deepen Existing Relationships',
      description: `Develop ${character.name}\'s relationships with ${otherCharacters.slice(0, 2).join(' and ')}.`,
      chapter: currentChapter + 2,
      effort: 'low',
      impact: 'medium',
      techniques: [
        'Add personal conversations',
        'Create shared challenges',
        'Reveal character backstories to each other'
      ]
    });
  }

  suggestions.longTerm.push({
    type: 'relationship_arc',
    title: 'Plan Relationship Arc',
    description: `Design a complete relationship journey that mirrors ${character.name}\'s personal growth.`,
    chapter: currentChapter + 8,
    effort: 'high',
    impact: 'high',
    techniques: [
      'Start with conflict or misunderstanding',
      'Build through shared experiences',
      'Culminate in mutual understanding'
    ]
  });
}

function addSkillProgressionSuggestions(suggestions: SuggestionGroup, character: Character, analysis: CharacterAnalysis, project: Project, currentChapter: number) {
  const projectGenre = project?.primary_genre || 'fantasy';
  
  const skillCount = typeof analysis.skillDevelopment === 'object' ? analysis.skillDevelopment.count || 0 : analysis.skillDevelopment;
  
  if (skillCount < 2) {
    suggestions.immediate.push({
      type: 'skill_training',
      title: 'Add Skill Development Scene',
      description: `Show ${character.name} learning or practicing abilities relevant to the story.`,
      chapter: currentChapter + 1,
      effort: 'medium',
      impact: 'medium',
      techniques: getSkillTechniques(projectGenre)
    });
  }

  suggestions.shortTerm.push({
    type: 'skill_test',
    title: 'Test Character Skills',
    description: `Create situations where ${character.name} must use their abilities under pressure.`,
    chapter: currentChapter + 4,
    effort: 'medium',
    impact: 'high',
    techniques: [
      'Design challenging obstacles',
      'Force creative problem solving',
      'Show limits and growth areas'
    ]
  });

  suggestions.longTerm.push({
    type: 'mastery_moment',
    title: 'Plan Mastery Moment',
    description: `Build toward a moment where ${character.name} demonstrates true mastery of their abilities.`,
    chapter: currentChapter + 12,
    effort: 'high',
    impact: 'high',
    techniques: [
      'Combine multiple learned skills',
      'Overcome previously impossible challenge',
      'Teach others what they\'ve learned'
    ]
  });
}

function addArcProgressionSuggestions(suggestions: SuggestionGroup, character: Character, analysis: CharacterAnalysis, currentChapter: number) {
  const arcType = character.character_arc?.type || 'positive_change';
  
  if (analysis.arcProgress < 30) {
    suggestions.immediate.push({
      type: 'arc_acceleration',
      title: 'Accelerate Arc Progression',
      description: `${character.name}\'s character arc is moving slowly. Add a significant development moment.`,
      chapter: currentChapter + 1,
      effort: 'high',
      impact: 'high',
      techniques: getArcTechniques(arcType)
    });
  }

  suggestions.shortTerm.push({
    type: 'arc_milestone',
    title: 'Create Arc Milestone',
    description: `Design a clear progression point in ${character.name}\'s character journey.`,
    chapter: currentChapter + 5,
    effort: 'medium',
    impact: 'high',
    techniques: [
      'Show internal realization',
      'Demonstrate changed behavior',
      'Have other characters notice the change'
    ]
  });
}

function addConsistencySuggestions(suggestions: SuggestionGroup, character: Character, analysis: CharacterAnalysis, currentChapter: number) {
  if (analysis.consistencyIssues.length > 0) {
    suggestions.immediate.push({
      type: 'consistency_fix',
      title: 'Address Consistency Issues',
      description: `Fix character inconsistencies: ${analysis.consistencyIssues.join(', ')}`,
      chapter: currentChapter,
      effort: 'medium',
      impact: 'high',
      techniques: [
        'Review previous character actions',
        'Ensure motivations align',
        'Check voice consistency'
      ]
    });
  }

  suggestions.shortTerm.push({
    type: 'voice_reinforcement',
    title: 'Reinforce Character Voice',
    description: `Strengthen ${character.name}\'s unique voice and mannerisms.`,
    chapter: currentChapter + 2,
    effort: 'low',
    impact: 'medium',
    techniques: [
      'Add distinctive dialogue patterns',
      'Include characteristic actions',
      'Show unique thought processes'
    ]
  });
}

function addGeneralSuggestions(suggestions: SuggestionGroup, character: Character, analysis: CharacterAnalysis, currentChapter: number) {
  // Add a mix of different types of suggestions
  addEmotionalGrowthSuggestions(suggestions, character, analysis, currentChapter);
  addRelationshipSuggestions(suggestions, character, analysis, [], currentChapter);
  addArcProgressionSuggestions(suggestions, character, analysis, currentChapter);
  
  // General character development
  suggestions.opportunistic.push({
    type: 'character_spotlight',
    title: 'Character Spotlight Chapter',
    description: `Consider dedicating a chapter primarily to ${character.name}\'s perspective and development.`,
    chapter: currentChapter + 6,
    effort: 'high',
    impact: 'high',
    techniques: [
      'Show internal monologue',
      'Explore backstory',
      'Reveal hidden motivations'
    ]
  });
}

function filterByUrgency(suggestions: SuggestionGroup, urgency: string): Partial<SuggestionGroup> {
  switch (urgency) {
    case 'high':
      return {
        immediate: suggestions.immediate,
        shortTerm: suggestions.shortTerm.slice(0, 2)
      };
    case 'medium':
      return {
        immediate: suggestions.immediate.slice(0, 1),
        shortTerm: suggestions.shortTerm,
        longTerm: suggestions.longTerm.slice(0, 2)
      };
    case 'low':
      return suggestions;
    default:
      return suggestions;
  }
}

// Helper functions
function calculatePresenceScore(character: Character, chapters: Chapter[]): number {
  const mentions = chapters.reduce((total, chapter) => {
    return total + countOccurrences(chapter.content || '', character.name);
  }, 0);
  return Math.min(100, (mentions / Math.max(chapters.length, 1)) * 10);
}

function analyzeEmotionalState(character: Character, chapters: Chapter[]) {
  // Simplified emotional analysis
  return {
    stagnant: chapters.length > 3 && calculatePresenceScore(character, chapters) < 30,
    range: Math.random() * 100, // Placeholder - would analyze actual content
    dominant: 'neutral'
  };
}

function countRelationships(character: Character, chapters: Chapter[]): number {
  // Count unique character interactions
  const content = chapters.map(c => c.content || '').join(' ');
  const names = extractCharacterNames(content);
  return new Set(names.filter(name => name !== character.name)).size;
}

function analyzeSkillDevelopment(character: Character, chapters: Chapter[]): number {
  // Suppress unused variable warning
  void character;
  const skillWords = ['learned', 'mastered', 'practiced', 'improved', 'trained'];
  const count = chapters.reduce((total, chapter) => {
    const content = chapter.content || '';
    return total + skillWords.reduce((subtotal, word) => {
      return subtotal + (content.toLowerCase().includes(word) ? 1 : 0);
    }, 0);
  }, 0);
  
  return count;
}

function calculateArcProgress(character: Character, chapters: Chapter[]): number {
  // Simplified arc progress calculation
  const developmentWords = ['grew', 'changed', 'realized', 'understood', 'transformed'];
  const mentions = chapters.reduce((total, chapter) => {
    const content = chapter.content || '';
    return total + developmentWords.reduce((subtotal, word) => {
      return subtotal + (content.toLowerCase().includes(word) && content.includes(character.name) ? 1 : 0);
    }, 0);
  }, 0);
  
  return Math.min(100, mentions * 20);
}

function findConsistencyIssues(_character: Character, _chapters: Chapter[]): string[] {
  // Placeholder for consistency analysis
  return [];
}

function findLastAppearance(character: Character, chapters: Chapter[]): number {
  for (let i = chapters.length - 1; i >= 0; i--) {
    const chapter = chapters[i];
    if (chapter && (chapter.content || '').includes(character.name)) {
      return chapter.chapter_number;
    }
  }
  return 0;
}

function countCharacterMentions(character: Character, chapters: Chapter[]): number {
  return chapters.reduce((total, chapter) => {
    return total + countOccurrences(chapter.content || '', character.name);
  }, 0);
}

function extractCharacterNames(content: string): string[] {
  // Simplified name extraction - would use more sophisticated NLP in production
  const words = content.split(/\s+/);
  return words.filter(word => {
    if (!word || word.length <= 2) return false;
    const firstChar = word[0];
    if (!firstChar) return false;
    return firstChar === firstChar.toUpperCase() &&
      !/^(The|A|An|And|But|Or|In|On|At|To|For|Of|With|By)$/.test(word);
  });
}

function getSkillTechniques(genre: string): string[] {
  const techniques: { [key: string]: string[] } = {
    fantasy: ['Magic training sessions', 'Combat practice', 'Ancient knowledge study'],
    'science-fiction': ['Technology mastery', 'Piloting skills', 'Scientific research'],
    mystery: ['Investigation techniques', 'Deduction skills', 'Research methods'],
    romance: ['Communication skills', 'Emotional intelligence', 'Social graces'],
    default: ['Problem-solving abilities', 'Leadership skills', 'Creative thinking']
  };
  
  return techniques[genre] || techniques.default || [];
}

function getArcTechniques(arcType: string): string[] {
  const techniques: { [key: string]: string[] } = {
    positive_change: ['Face internal fears', 'Overcome limiting beliefs', 'Embrace new identity'],
    negative_change: ['Show moral compromise', 'Highlight growing flaws', 'Demonstrate consequences'],
    redemption: ['Acknowledge past mistakes', 'Take responsibility', 'Make amends'],
    corruption: ['Introduce moral temptation', 'Show gradual compromise', 'Highlight justifications'],
    flat_arc: ['Strengthen core beliefs', 'Influence others', 'Demonstrate principles']
  };
  
  return techniques[arcType] || techniques.positive_change || [];
}

function countOccurrences(text: string, searchTerm: string): number {
  const regex = new RegExp(searchTerm, 'gi');
  const matches = text.match(regex);
  return matches ? matches.length : 0;
}