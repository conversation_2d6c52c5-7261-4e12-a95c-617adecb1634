import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { applyRateLimit, type EnhancedRateLimitOptions } from '@/lib/rate-limiter-unified'
import { APIError, handleAPIError } from '@/lib/api/error-handler'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'

/**
 * Unified middleware configuration
 * Combines auth, rate limiting, validation, and error handling
 */
export interface UnifiedMiddlewareConfig {
  // Authentication options
  auth?: boolean | 'admin' | 'project' | 'series'
  
  // Rate limiting options
  rateLimit?: boolean | EnhancedRateLimitOptions
  
  // Request validation
  validation?: {
    body?: z.ZodSchema
    query?: z.ZodSchema
    params?: z.ZodSchema
  }
  
  // Response options
  cors?: {
    origins?: string[]
    methods?: string[]
    headers?: string[]
    credentials?: boolean
  }
  
  // Performance options
  timeout?: number
  cache?: {
    ttl?: number
    key?: (req: NextRequest) => string
  }
  
  // Monitoring options
  monitor?: {
    logRequest?: boolean
    logResponse?: boolean
    trackMetrics?: boolean
  }
}

/**
 * Request with all middleware enhancements
 */
export interface EnhancedRequest extends AuthenticatedRequest {
  id: string
  validatedBody?: any
  validatedQuery?: any
  validatedParams?: any
  startTime: number
}

/**
 * Default configuration
 */
const DEFAULT_CONFIG: UnifiedMiddlewareConfig = {
  auth: true,
  rateLimit: true,
  monitor: {
    logRequest: true,
    logResponse: true,
    trackMetrics: true
  }
}

/**
 * Apply CORS headers to response
 */
function applyCorsHeaders(
  request: NextRequest,
  response: NextResponse,
  config: UnifiedMiddlewareConfig['cors']
): void {
  if (!config) return
  
  const origin = request.headers.get('origin')
  
  // Handle allowed origins
  if (config.origins) {
    if (config.origins.includes('*')) {
      response.headers.set('Access-Control-Allow-Origin', '*')
    } else if (origin && config.origins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin)
    }
  }
  
  // Handle allowed methods
  if (config.methods) {
    response.headers.set('Access-Control-Allow-Methods', config.methods.join(', '))
  }
  
  // Handle allowed headers
  if (config.headers) {
    response.headers.set('Access-Control-Allow-Headers', config.headers.join(', '))
  }
  
  // Handle credentials
  if (config.credentials) {
    response.headers.set('Access-Control-Allow-Credentials', 'true')
  }
  
  // Set max age
  response.headers.set('Access-Control-Max-Age', '86400')
}

/**
 * Validate request data
 */
async function validateRequest(
  request: NextRequest,
  validation: UnifiedMiddlewareConfig['validation']
): Promise<{
  body?: any
  query?: any
  params?: any
} | NextResponse> {
  const results: any = {}
  
  try {
    // Validate body
    if (validation?.body) {
      const body = await request.json()
      const result = validation.body.safeParse(body)
      if (!result.success) {
        return handleAPIError(
          new APIError(
            `Invalid request body: ${result.error.errors.map(e => e.message).join(', ')}`,
            400
          )
        )
      }
      results.body = result.data
    }
    
    // Validate query params
    if (validation?.query) {
      const url = new URL(request.url)
      const query = Object.fromEntries(url.searchParams)
      const result = validation.query.safeParse(query)
      if (!result.success) {
        return handleAPIError(
          new APIError(
            `Invalid query params: ${result.error.errors.map(e => e.message).join(', ')}`,
            400
          )
        )
      }
      results.query = result.data
    }
    
    return results
  } catch (error) {
    logger.error('Validation error', error)
    return handleAPIError(new APIError('Invalid request format', 400))
  }
}

/**
 * Main unified middleware wrapper
 */
export function withUnifiedMiddleware<T extends {} = {}>(
  handler: (request: EnhancedRequest & T, context?: any) => Promise<NextResponse>,
  config: UnifiedMiddlewareConfig = {}
) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    const requestId = request.headers.get('x-request-id') || uuidv4()
    const startTime = Date.now()
    
    // Create enhanced request
    const enhancedRequest = request as EnhancedRequest
    enhancedRequest.id = requestId
    enhancedRequest.startTime = startTime
    
    try {
      // Log request if enabled
      if (finalConfig.monitor?.logRequest) {
        logger.info('API request', {
          requestId,
          method: request.method,
          url: request.url,
          headers: Object.fromEntries(request.headers.entries())
        })
      }
      
      // Handle CORS preflight
      if (finalConfig.cors && request.method === 'OPTIONS') {
        const response = new NextResponse(null, { status: 200 })
        applyCorsHeaders(request, response, finalConfig.cors)
        return response
      }
      
      // Apply rate limiting
      if (finalConfig.rateLimit) {
        const rateLimitOptions = typeof finalConfig.rateLimit === 'object' 
          ? finalConfig.rateLimit 
          : {}
        const rateLimitResponse = await applyRateLimit(request, rateLimitOptions)
        if (rateLimitResponse) {
          return rateLimitResponse
        }
      }
      
      // Apply authentication
      if (finalConfig.auth) {
        let authResponse: NextResponse | null = null
        
        switch (finalConfig.auth) {
          case 'admin':
            const adminUser = await UnifiedAuthService.authenticateAdmin(request)
            if (!adminUser) {
              authResponse = handleAPIError(new APIError('Admin access required', 403))
            } else {
              enhancedRequest.user = adminUser
            }
            break
            
          case 'project':
            if (context?.params?.id) {
              const projectUser = await UnifiedAuthService.authenticateProjectAccess(
                request,
                context.params.id
              )
              if (!projectUser) {
                authResponse = handleAPIError(new APIError('Project access denied', 403))
              } else {
                enhancedRequest.user = projectUser
              }
            }
            break
            
          case 'series':
            if (context?.params?.id) {
              const seriesUser = await UnifiedAuthService.authenticateSeriesAccess(
                request,
                context.params.id
              )
              if (!seriesUser) {
                authResponse = handleAPIError(new APIError('Series access denied', 403))
              } else {
                enhancedRequest.user = seriesUser
              }
            }
            break
            
          default:
            const user = await UnifiedAuthService.authenticateUser(request)
            if (!user) {
              authResponse = handleAPIError(new APIError('Authentication required', 401))
            } else {
              enhancedRequest.user = user
            }
        }
        
        if (authResponse) {
          return authResponse
        }
      }
      
      // Apply validation
      if (finalConfig.validation) {
        const validationResult = await validateRequest(request, finalConfig.validation)
        if (validationResult instanceof NextResponse) {
          return validationResult
        }
        
        enhancedRequest.validatedBody = validationResult.body
        enhancedRequest.validatedQuery = validationResult.query
        enhancedRequest.validatedParams = validationResult.params
      }
      
      // Execute handler with timeout if specified
      let handlerPromise = handler(enhancedRequest as EnhancedRequest & T, context)
      
      if (finalConfig.timeout) {
        handlerPromise = Promise.race([
          handlerPromise,
          new Promise<NextResponse>((_, reject) => 
            setTimeout(() => reject(new APIError('Request timeout', 504)), finalConfig.timeout)
          )
        ])
      }
      
      const response = await handlerPromise
      
      // Add standard headers
      response.headers.set('x-request-id', requestId)
      response.headers.set('x-response-time', `${Date.now() - startTime}ms`)
      
      // Apply CORS headers
      if (finalConfig.cors) {
        applyCorsHeaders(request, response, finalConfig.cors)
      }
      
      // Log response if enabled
      if (finalConfig.monitor?.logResponse) {
        logger.info('API response', {
          requestId,
          status: response.status,
          duration: Date.now() - startTime
        })
      }
      
      return response
      
    } catch (error) {
      // Log error
      logger.error('API error', {
        requestId,
        error,
        duration: Date.now() - startTime
      })
      
      // Return error response
      const errorResponse = handleAPIError(error)
      errorResponse.headers.set('x-request-id', requestId)
      errorResponse.headers.set('x-response-time', `${Date.now() - startTime}ms`)
      
      return errorResponse
    }
  }
}

/**
 * Convenience middleware presets
 */
export const Middleware = {
  // Public endpoint (no auth)
  public: (handler: (request: EnhancedRequest) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: false,
      rateLimit: { type: 'default' }
    }),
  
  // Authenticated endpoint
  authenticated: (handler: (request: EnhancedRequest) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: true,
      rateLimit: { type: 'authenticated' }
    }),
  
  // Admin endpoint
  admin: (handler: (request: EnhancedRequest) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: 'admin',
      rateLimit: { type: 'authenticated' }
    }),
  
  // AI generation endpoint
  aiGeneration: (handler: (request: EnhancedRequest) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: true,
      rateLimit: { type: 'ai-generation' }
    }),
  
  // AI analysis endpoint
  aiAnalysis: (handler: (request: EnhancedRequest) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: true,
      rateLimit: { type: 'ai-analysis' }
    }),
  
  // Project-specific endpoint
  project: (handler: (request: EnhancedRequest, context: any) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: 'project',
      rateLimit: { type: 'authenticated' }
    }),
  
  // Series-specific endpoint
  series: (handler: (request: EnhancedRequest, context: any) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: 'series',
      rateLimit: { type: 'authenticated' }
    }),
  
  // Webhook endpoint
  webhook: (handler: (request: EnhancedRequest) => Promise<NextResponse>) =>
    withUnifiedMiddleware(handler, {
      auth: false,
      rateLimit: { type: 'webhook' },
      cors: {
        origins: ['*'],
        methods: ['POST'],
        headers: ['Content-Type', 'X-Webhook-Signature']
      }
    })
}

/**
 * Helper functions for common patterns
 */
export function getValidatedBody<T>(request: EnhancedRequest): T {
  if (!request.validatedBody) {
    throw new APIError('No validated body found', 500)
  }
  return request.validatedBody as T
}

export function getValidatedQuery<T>(request: EnhancedRequest): T {
  if (!request.validatedQuery) {
    throw new APIError('No validated query found', 500)
  }
  return request.validatedQuery as T
}

export function getValidatedParams<T>(request: EnhancedRequest): T {
  if (!request.validatedParams) {
    throw new APIError('No validated params found', 500)
  }
  return request.validatedParams as T
}

// Pagination helpers
export interface PaginationParams {
  page: number
  limit: number
  offset: number
}

export function getPaginationParams(request: EnhancedRequest): PaginationParams {
  const url = new URL(request.url)
  const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'))
  const limit = Math.min(100, Math.max(1, parseInt(url.searchParams.get('limit') || '20')))
  const offset = (page - 1) * limit
  
  return { page, limit, offset }
}

export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  params: PaginationParams
): NextResponse {
  return NextResponse.json({
    data,
    pagination: {
      page: params.page,
      limit: params.limit,
      total,
      totalPages: Math.ceil(total / params.limit),
      hasMore: params.offset + data.length < total
    }
  })
}