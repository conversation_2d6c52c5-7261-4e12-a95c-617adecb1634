import { logger } from '@/lib/services/logger';

// Mock the logger
jest.mock('@/lib/services/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

describe('MailerooEmailService', () => {
  let emailService: any;
  const mockApiKey = 'test-api-key';
  const mockEndpoint = 'https://api.maileroo.com/v1/send';

  beforeEach(async () => {
    jest.resetModules();
    process.env.EMAIL_API_KEY = mockApiKey;
    process.env.EMAIL_ENDPOINT = mockEndpoint;
    process.env.EMAIL_FROM = '<EMAIL>';
    process.env.EMAIL_FROM_NAME = 'BookScribe AI';
    const module = await import('@/lib/services/maileroo-email-service');
    const { MailerooEmailService } = module;
    emailService = new MailerooEmailService();
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should throw error if API key is not provided', async () => {
      jest.resetModules();
      delete process.env.EMAIL_API_KEY;
      process.env.EMAIL_ENDPOINT = mockEndpoint;
      process.env.EMAIL_FROM = '<EMAIL>';
      process.env.EMAIL_FROM_NAME = 'BookScribe AI';
      const module = await import('@/lib/services/maileroo-email-service');
      const { MailerooEmailService } = module;
      const service = new MailerooEmailService();
      await expect(
        service.sendEmail('<EMAIL>', 'welcome', { userName: 'Test', loginUrl: 'https://bookscribe.ai/login' })
      ).rejects.toThrow();
    });

    it('should use configured endpoint', () => {
      expect(emailService).toBeDefined();
    });
  });

  describe('sendEmail', () => {
    it('should send welcome email successfully', async () => {
      const mockResponse = { success: true, messageId: 'msg-123' };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'welcome', {
        userName: 'Test User',
        loginUrl: 'https://bookscribe.ai/login',
      });

      expect(global.fetch).toHaveBeenCalledWith(
        mockEndpoint,
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': mockApiKey,
          },
          body: JSON.stringify({
            from: 'BookScribe AI <<EMAIL>>',
            to: '<EMAIL>',
            subject: 'Welcome to BookScribe AI!',
            html: expect.stringContaining('Welcome to BookScribe AI'),
            text: expect.stringContaining('Welcome to BookScribe AI'),
            tags: ['welcome', 'transactional'],
          }),
        })
      );
    });

    it('should send password reset email successfully', async () => {
      const mockResponse = { success: true, messageId: 'msg-456' };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'password-reset', {
        userName: 'Test User',
        resetUrl: 'https://bookscribe.ai/reset?token=xyz',
      });

      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining('resetUrl'),
        })
      );
    });

    it('should send collaboration invite email successfully', async () => {
      const mockResponse = { success: true, messageId: 'msg-789' };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'collaboration-invite', {
        inviterName: 'John Doe',
        projectName: 'My Novel',
        role: 'editor',
        acceptUrl: 'https://bookscribe.ai/accept?token=abc',
      });

      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining('collaboration'),
        })
      );
    });

    it('should handle scheduled emails', async () => {
      const mockResponse = { success: true, messageId: 'msg-scheduled' };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const scheduledFor = new Date(Date.now() + 3600000); // 1 hour from now
      await emailService.sendEmail(
        '<EMAIL>',
        'welcome',
        { userName: 'Test User', loginUrl: 'https://bookscribe.ai/login' },
        { scheduledFor }
      );

      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining(`"send_at":"${scheduledFor.toISOString()}"`),
        })
      );
    });

    it('should handle multiple recipients', async () => {
      const mockResponse = { success: true, messageId: 'msg-multiple' };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail(
        ['<EMAIL>', '<EMAIL>'],
        'welcome',
        { userName: 'Users', loginUrl: 'https://bookscribe.ai/login' }
      );

      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining('"to":["<EMAIL>","<EMAIL>"]'),
        })
      );
    });

    it('should handle API errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Invalid email' }),
      });

      await expect(
        emailService.sendEmail('invalid-email', 'welcome', {
          userName: 'Test',
          loginUrl: 'https://bookscribe.ai/login',
        })
      ).rejects.toThrow('Failed to send email: 400');

      expect(logger.error).toHaveBeenCalledWith(
        'Error sending email:',
        expect.any(Error)
      );
    });

    it('should handle network errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(
        emailService.sendEmail('<EMAIL>', 'welcome', {
          userName: 'Test',
          loginUrl: 'https://bookscribe.ai/login',
        })
      ).rejects.toThrow('Network error');

      expect(logger.error).toHaveBeenCalledWith(
        'Error sending email:',
        expect.any(Error)
      );
    });
  });

  describe('email templates', () => {
    it('should generate correct welcome email content', async () => {
      const mockResponse = { success: true };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'welcome', {
        userName: 'John Doe',
        loginUrl: 'https://bookscribe.ai/login',
      });

      const callArgs = (global.fetch as jest.Mock).mock.calls[0][1];
      const body = JSON.parse(callArgs.body);

      expect(body.html).toContain('John Doe');
      expect(body.html).toContain('https://bookscribe.ai/login');
      expect(body.html).toContain('Get Started Writing');
      expect(body.text).toContain('John Doe');
    });

    it('should generate correct password reset email content', async () => {
      const mockResponse = { success: true };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'password-reset', {
        userName: 'Jane Smith',
        resetUrl: 'https://bookscribe.ai/reset?token=xyz123',
      });

      const callArgs = (global.fetch as jest.Mock).mock.calls[0][1];
      const body = JSON.parse(callArgs.body);

      expect(body.subject).toBe('Reset Your BookScribe AI Password');
      expect(body.html).toContain('Jane Smith');
      expect(body.html).toContain('https://bookscribe.ai/reset?token=xyz123');
      expect(body.html).toContain('Reset Password');
    });

    it('should generate correct collaboration invite email content', async () => {
      const mockResponse = { success: true };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'collaboration-invite', {
        inviterName: 'Alice Cooper',
        projectName: 'Epic Fantasy Novel',
        role: 'viewer',
        acceptUrl: 'https://bookscribe.ai/accept?token=abc456',
      });

      const callArgs = (global.fetch as jest.Mock).mock.calls[0][1];
      const body = JSON.parse(callArgs.body);

      expect(body.subject).toBe('You\'ve been invited to collaborate on Epic Fantasy Novel');
      expect(body.html).toContain('Alice Cooper');
      expect(body.html).toContain('Epic Fantasy Novel');
      expect(body.html).toContain('viewer');
      expect(body.html).toContain('https://bookscribe.ai/accept?token=abc456');
    });

    it('should generate correct project update email content', async () => {
      const mockResponse = { success: true };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'project-update', {
        userName: 'Bob Builder',
        projectName: 'Mystery Thriller',
        updateSummary: 'Chapter 5 has been updated',
        projectUrl: 'https://bookscribe.ai/projects/123',
      });

      const callArgs = (global.fetch as jest.Mock).mock.calls[0][1];
      const body = JSON.parse(callArgs.body);

      expect(body.subject).toBe('Update on Mystery Thriller');
      expect(body.html).toContain('Bob Builder');
      expect(body.html).toContain('Mystery Thriller');
      expect(body.html).toContain('Chapter 5 has been updated');
      expect(body.html).toContain('https://bookscribe.ai/projects/123');
    });

    it('should generate correct ai-credits-low email content', async () => {
      const mockResponse = { success: true };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await emailService.sendEmail('<EMAIL>', 'ai-credits-low', {
        userName: 'Credit User',
        creditsRemaining: 500,
        upgradeUrl: 'https://bookscribe.ai/upgrade',
      });

      const callArgs = (global.fetch as jest.Mock).mock.calls[0][1];
      const body = JSON.parse(callArgs.body);

      expect(body.subject).toBe('Your AI Credits are Running Low');
      expect(body.html).toContain('Credit User');
      expect(body.html).toContain('500');
      expect(body.html).toContain('https://bookscribe.ai/upgrade');
    });
  });

  describe('queue processing', () => {
    it('should queue email when userId is provided', async () => {
      await emailService.sendEmail(
        '<EMAIL>',
        'welcome',
        { userName: 'Test', loginUrl: 'https://bookscribe.ai/login' },
        { userId: 'user-123' }
      );

      expect(emailService['emailQueue']).toHaveLength(1);
      expect(emailService['emailQueue'][0]).toMatchObject({
        to: '<EMAIL>',
        template: 'welcome',
        userId: 'user-123',
      });
    });

    it('should process queued emails', async () => {
      const mockResponse = { success: true };
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => mockResponse,
      });

      // Queue multiple emails
      await emailService.sendEmail(
        '<EMAIL>',
        'welcome',
        { userName: 'User 1', loginUrl: 'https://bookscribe.ai/login' },
        { userId: 'user-1' }
      );
      await emailService.sendEmail(
        '<EMAIL>',
        'password-reset',
        { userName: 'User 2', resetUrl: 'https://bookscribe.ai/reset' },
        { userId: 'user-2' }
      );

      expect(emailService['emailQueue']).toHaveLength(2);

      // Process queue
      await emailService.processQueue();

      expect(global.fetch).toHaveBeenCalledTimes(2);
      expect(emailService['emailQueue']).toHaveLength(0);
      expect(logger.info).toHaveBeenCalledWith('Processing email queue: 2 emails');
    });

    it('should handle errors during queue processing', async () => {
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        })
        .mockRejectedValueOnce(new Error('Send failed'));

      // Queue emails
      await emailService.sendEmail(
        '<EMAIL>',
        'welcome',
        { userName: 'User 1', loginUrl: 'https://bookscribe.ai/login' },
        { userId: 'user-1' }
      );
      await emailService.sendEmail(
        '<EMAIL>',
        'welcome',
        { userName: 'User 2', loginUrl: 'https://bookscribe.ai/login' },
        { userId: 'user-2' }
      );

      // Process queue
      await emailService.processQueue();

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to send queued email:',
        expect.any(Error)
      );
      expect(emailService['emailQueue']).toHaveLength(0);
    });

    it('should clear queue for specific user', async () => {
      // Queue emails for different users
      await emailService.sendEmail(
        '<EMAIL>',
        'welcome',
        { userName: 'User 1', loginUrl: 'https://bookscribe.ai/login' },
        { userId: 'user-1' }
      );
      await emailService.sendEmail(
        '<EMAIL>',
        'welcome',
        { userName: 'User 2', loginUrl: 'https://bookscribe.ai/login' },
        { userId: 'user-2' }
      );
      await emailService.sendEmail(
        '<EMAIL>',
        'password-reset',
        { userName: 'User 1', resetUrl: 'https://bookscribe.ai/reset' },
        { userId: 'user-1' }
      );

      expect(emailService['emailQueue']).toHaveLength(3);

      // Clear queue for user-1
      emailService.clearUserQueue('user-1');

      expect(emailService['emailQueue']).toHaveLength(1);
      expect(emailService['emailQueue'][0].userId).toBe('user-2');
    });
  });

  describe('rate limiting', () => {
    it('should respect rate limits', async () => {
      const mockResponse = { success: true };
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => mockResponse,
      });

      // Send multiple emails rapidly
      const promises = [];
      for (let i = 0; i < 15; i++) {
        promises.push(
          emailService.sendEmail(`user${i}@example.com`, 'welcome', {
            userName: `User ${i}`,
            loginUrl: 'https://bookscribe.ai/login',
          })
        );
      }

      await Promise.all(promises);

      // Check that emails were sent with appropriate delays
      expect(global.fetch).toHaveBeenCalledTimes(15);
    });
  });
});