import { describe, it, expect } from '@jest/globals';
import { z } from 'zod';
import {
  emailSchema,
  passwordSchema,
  nameSchema,
  idSchema,
  dateSchema,
  urlSchema,
  paginationSchema,
  sortSchema,
  searchSchema,
  createProjectSchema,
  updateProjectSchema,
  projectSettingsSchema,
  createChapterSchema,
  updateChapterSchema,
  createCharacterSchema,
  updateCharacterSchema,
} from '@/lib/validation/schemas';

describe('Validation Schemas', () => {
  describe('Basic Schemas', () => {
    describe('emailSchema', () => {
      it('should validate correct email addresses', () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        validEmails.forEach(email => {
          expect(() => emailSchema.parse(email)).not.toThrow();
        });
      });

      it('should reject invalid email addresses', () => {
        const invalidEmails = [
          'notanemail',
          '@example.com',
          'user@',
          'user @example.com',
          '',
        ];

        invalidEmails.forEach(email => {
          expect(() => emailSchema.parse(email)).toThrow();
        });
      });
    });

    describe('passwordSchema', () => {
      it('should validate strong passwords', () => {
        const validPasswords = [
          'Password123!',
          'MyStr0ng@Pass',
          'Secure#Pass1',
        ];

        validPasswords.forEach(password => {
          expect(() => passwordSchema.parse(password)).not.toThrow();
        });
      });

      it('should reject weak passwords', () => {
        const invalidPasswords = [
          'short',
          'nocapitals123!',
          'NoNumbers!',
          'NoSpecialChar1',
          'NOLOWERCASE123!',
          '',
        ];

        invalidPasswords.forEach(password => {
          expect(() => passwordSchema.parse(password)).toThrow();
        });
      });
    });

    describe('nameSchema', () => {
      it('should validate names', () => {
        expect(() => nameSchema.parse('John Doe')).not.toThrow();
        expect(() => nameSchema.parse('A')).not.toThrow();
        expect(() => nameSchema.parse('Very Long Name That Is Still Valid')).not.toThrow();
      });

      it('should reject invalid names', () => {
        expect(() => nameSchema.parse('')).toThrow();
        expect(() => nameSchema.parse('a'.repeat(101))).toThrow();
      });
    });

    describe('idSchema', () => {
      it('should validate UUIDs', () => {
        const validIds = [
          '123e4567-e89b-12d3-a456-************',
          '550e8400-e29b-41d4-a716-************',
        ];

        validIds.forEach(id => {
          expect(() => idSchema.parse(id)).not.toThrow();
        });
      });

      it('should reject invalid UUIDs', () => {
        const invalidIds = [
          '123',
          'not-a-uuid',
          '123e4567-e89b-12d3-a456',
          '',
        ];

        invalidIds.forEach(id => {
          expect(() => idSchema.parse(id)).toThrow();
        });
      });
    });
  });

  describe('Project Schemas', () => {
    describe('createProjectSchema', () => {
      it('should validate valid project data', () => {
        const validProject = {
          title: 'My Novel',
          description: 'A story about adventure',
          settings: {
            primaryGenre: 'fantasy',
            targetAudience: 'adult',
            writingStyle: 'descriptive',
            narrativeVoice: 'third-person',
            tense: 'past',
            pacing: 'medium',
          },
        };

        expect(() => createProjectSchema.parse(validProject)).not.toThrow();
      });

      it('should provide defaults for optional fields', () => {
        const minimalProject = {
          title: 'My Novel',
        };

        const parsed = createProjectSchema.parse(minimalProject);
        expect(parsed.description).toBe('');
        expect(parsed.settings).toBeDefined();
      });

      it('should reject invalid project data', () => {
        const invalidProjects = [
          { title: '' }, // Empty title
          { title: 'a'.repeat(201) }, // Title too long
          { title: 'Valid', settings: { primaryGenre: 'invalid' } }, // Invalid genre
        ];

        invalidProjects.forEach(project => {
          expect(() => createProjectSchema.parse(project)).toThrow();
        });
      });
    });

    describe('projectSettingsSchema', () => {
      it('should validate all settings options', () => {
        const settings = {
          primaryGenre: 'fantasy',
          secondaryGenres: ['romance', 'mystery'],
          targetAudience: 'young-adult',
          writingStyle: 'poetic',
          narrativeVoice: 'first-person',
          tense: 'present',
          pacing: 'fast',
          violenceLevel: 'graphic',
          romanceLevel: 'explicit',
          profanityLevel: 'heavy',
          themeDepth: 'deep',
          worldBuildingDepth: 'extensive',
          characterComplexity: 'complex',
          plotComplexity: 'complex',
          tone: 'dark',
          dialogueStyle: 'realistic',
          descriptionLevel: 'detailed',
          useDeepPOV: true,
          showDontTell: true,
          varyProse: false,
          useSymbolism: true,
          useCliffhangers: true,
          useForeshadowing: true,
          useFlashbacks: false,
          useUnreliableNarrator: false,
          protagonistTypes: ['anti-hero'],
          antagonistTypes: ['rival'],
          supportingRoles: ['mentor', 'comic-relief'],
          majorThemes: ['redemption', 'love'],
          minorThemes: ['family'],
          culturalElements: ['diverse'],
          magicSystemType: 'hard',
          technologyLevel: 'futuristic',
          politicalSystem: 'democracy',
          economicSystem: 'capitalist',
          geographyType: 'archipelago',
          pacingPreference: 'variable',
        };

        expect(() => projectSettingsSchema.parse(settings)).not.toThrow();
      });

      it('should reject invalid enum values', () => {
        const invalidSettings = {
          primaryGenre: 'invalid-genre',
          targetAudience: 'babies',
          writingStyle: 'wrong',
        };

        expect(() => projectSettingsSchema.parse(invalidSettings)).toThrow();
      });
    });
  });

  describe('Chapter Schemas', () => {
    describe('createChapterSchema', () => {
      it('should validate chapter creation data', () => {
        const validChapter = {
          projectId: '123e4567-e89b-12d3-a456-************',
          title: 'Chapter 1: The Beginning',
          content: 'It was a dark and stormy night...',
          orderIndex: 1,
          targetWordCount: 3000,
        };

        expect(() => createChapterSchema.parse(validChapter)).not.toThrow();
      });

      it('should calculate word count if not provided', () => {
        const chapter = {
          projectId: '123e4567-e89b-12d3-a456-************',
          title: 'Chapter 1',
          content: 'This is a test.',
        };

        const parsed = createChapterSchema.parse(chapter);
        expect(parsed.wordCount).toBe(4);
      });
    });
  });

  describe('Character Schemas', () => {
    describe('createCharacterSchema', () => {
      it('should validate character creation data', () => {
        const validCharacter = {
          projectId: '123e4567-e89b-12d3-a456-************',
          name: 'John Doe',
          role: 'protagonist',
          age: 30,
          appearance: 'Tall and dark-haired',
          personality: 'Brave and kind',
          backstory: 'Grew up in a small village',
          motivation: 'To save the world',
          arc: 'From naive to wise',
          relationships: [
            { characterId: '550e8400-e29b-41d4-a716-************', type: 'friend' },
          ],
          traits: ['brave', 'loyal'],
          skills: ['swordsmanship', 'leadership'],
        };

        expect(() => createCharacterSchema.parse(validCharacter)).not.toThrow();
      });

      it('should validate role enum values', () => {
        const character = {
          projectId: '123e4567-e89b-12d3-a456-************',
          name: 'Villain',
          role: 'antagonist',
        };

        expect(() => createCharacterSchema.parse(character)).not.toThrow();

        character.role = 'invalid-role' as any;
        expect(() => createCharacterSchema.parse(character)).toThrow();
      });
    });
  });

  describe('Utility Schemas', () => {
    describe('paginationSchema', () => {
      it('should validate pagination parameters', () => {
        const valid = { page: 1, limit: 20 };
        const parsed = paginationSchema.parse(valid);
        expect(parsed.page).toBe(1);
        expect(parsed.limit).toBe(20);
      });

      it('should provide defaults', () => {
        const parsed = paginationSchema.parse({});
        expect(parsed.page).toBe(1);
        expect(parsed.limit).toBe(10);
      });

      it('should enforce limits', () => {
        const parsed = paginationSchema.parse({ limit: 200 });
        expect(parsed.limit).toBe(100); // Max limit
      });
    });

    describe('sortSchema', () => {
      it('should validate sort parameters', () => {
        const valid = { field: 'createdAt', order: 'desc' };
        expect(() => sortSchema.parse(valid)).not.toThrow();
      });

      it('should provide defaults', () => {
        const parsed = sortSchema.parse({ field: 'name' });
        expect(parsed.order).toBe('asc');
      });
    });

    describe('searchSchema', () => {
      it('should validate search parameters', () => {
        const valid = {
          query: 'test search',
          filters: { category: 'fiction' },
          includeArchived: true,
        };

        const parsed = searchSchema.parse(valid);
        expect(parsed.query).toBe('test search');
        expect(parsed.includeArchived).toBe(true);
      });

      it('should reject empty queries', () => {
        expect(() => searchSchema.parse({ query: '' })).toThrow();
      });
    });
  });
});