/**
 * Accessible Modal Component
 * Provides a fully accessible modal dialog with focus management
 */

import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAccessibility } from '@/hooks/use-accessibility';
import { AccessibleIconButton } from './accessible-wrapper';

interface AccessibleModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnEscape?: boolean;
  closeOnBackdropClick?: boolean;
  showCloseButton?: boolean;
  className?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
}

export function AccessibleModal({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = 'md',
  closeOnEscape = true,
  closeOnBackdropClick = true,
  showCloseButton = true,
  className,
  'aria-labelledby': ariaLabelledBy,
  'aria-describedby': ariaDescribedBy,
}: AccessibleModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);
  
  const { containerRef, focusFirst } = useAccessibility({
    autoFocus: true,
    restoreFocus: true,
    focusTrap: true,
    enableEscapeKey: closeOnEscape,
    onEscape: onClose,
  });

  const titleId = ariaLabelledBy || `modal-title-${Math.random().toString(36).substr(2, 9)}`;
  const descId = ariaDescribedBy || `modal-desc-${Math.random().toString(36).substr(2, 9)}`;

  // Handle open/close
  useEffect(() => {
    if (isOpen) {
      previousActiveElement.current = document.activeElement as HTMLElement;
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
      
      // Announce to screen readers
      const announcement = document.createElement('div');
      announcement.setAttribute('role', 'status');
      announcement.setAttribute('aria-live', 'polite');
      announcement.className = 'sr-only';
      announcement.textContent = `Dialog opened: ${title}`;
      document.body.appendChild(announcement);
      
      setTimeout(() => {
        document.body.removeChild(announcement);
      }, 1000);
    } else {
      // Restore body scroll
      document.body.style.overflow = '';
      
      // Restore focus
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen, title]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-in fade-in duration-200"
        onClick={closeOnBackdropClick ? onClose : undefined}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div
        ref={modalRef}
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        role="presentation"
      >
        <div
          ref={containerRef as React.RefObject<HTMLDivElement>}
          role="dialog"
          aria-modal="true"
          aria-labelledby={titleId}
          aria-describedby={description ? descId : undefined}
          className={cn(
            'relative w-full bg-background rounded-lg shadow-lg',
            'animate-in fade-in zoom-in-95 duration-200',
            sizeClasses[size],
            className
          )}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 pb-4">
            <h2 id={titleId} className="text-lg font-semibold">
              {title}
            </h2>
            {showCloseButton && (
              <AccessibleIconButton
                icon={<X className="h-4 w-4" />}
                screenReaderText="Close dialog"
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="rounded-md"
              />
            )}
          </div>
          
          {/* Description */}
          {description && (
            <p id={descId} className="px-6 pb-4 text-sm text-muted-foreground">
              {description}
            </p>
          )}
          
          {/* Content */}
          <div className="px-6 pb-6">
            {children}
          </div>
        </div>
      </div>
    </>
  );
}

// Accessible Dialog Actions
interface AccessibleDialogActionsProps {
  children: React.ReactNode;
  className?: string;
}

export function AccessibleDialogActions({ children, className }: AccessibleDialogActionsProps) {
  return (
    <div className={cn('flex items-center justify-end gap-2 mt-6', className)}>
      {children}
    </div>
  );
}

// Accessible Confirm Dialog
interface AccessibleConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmVariant?: 'default' | 'destructive';
  isLoading?: boolean;
}

export function AccessibleConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  confirmVariant = 'default',
  isLoading = false,
}: AccessibleConfirmDialogProps) {
  const handleConfirm = () => {
    onConfirm();
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <AccessibleModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      description={description}
      size="sm"
    >
      <AccessibleDialogActions>
        <button
          onClick={onClose}
          disabled={isLoading}
          className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-accent"
          aria-label={`${cancelLabel} and close dialog`}
        >
          {cancelLabel}
        </button>
        <button
          onClick={handleConfirm}
          disabled={isLoading}
          className={cn(
            'px-4 py-2 text-sm font-medium rounded-md transition-colors',
            confirmVariant === 'destructive'
              ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
              : 'bg-primary text-primary-foreground hover:bg-primary/90',
            isLoading && 'opacity-50 cursor-not-allowed'
          )}
          aria-label={`${confirmLabel} action`}
          aria-busy={isLoading}
        >
          {isLoading ? 'Processing...' : confirmLabel}
        </button>
      </AccessibleDialogActions>
    </AccessibleModal>
  );
}

// Accessible Alert Dialog
interface AccessibleAlertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  type?: 'info' | 'warning' | 'error' | 'success';
}

export function AccessibleAlertDialog({
  isOpen,
  onClose,
  title,
  description,
  type = 'info',
}: AccessibleAlertDialogProps) {
  const iconMap = {
    info: '💡',
    warning: '⚠️',
    error: '❌',
    success: '✅',
  };

  const colorMap = {
    info: 'text-info',
    warning: 'text-warning',
    error: 'text-error',
    success: 'text-success',
  };

  return (
    <AccessibleModal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <span className="flex items-center gap-2">
          <span className={cn('text-2xl', colorMap[type])} aria-hidden="true">
            {iconMap[type]}
          </span>
          {title}
        </span>
      }
      size="sm"
    >
      <p className="text-sm text-muted-foreground">{description}</p>
      <AccessibleDialogActions>
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          autoFocus
        >
          OK
        </button>
      </AccessibleDialogActions>
    </AccessibleModal>
  );
}