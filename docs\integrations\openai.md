# OpenAI Integration Documentation

## Overview

BookScribe's OpenAI integration powers the entire AI agent system, leveraging GPT-4.1 and GPT-o4 mini models to generate high-quality novel content. This documentation covers the integration architecture, configuration, usage patterns, and best practices.

## Integration Architecture

### System Overview

```mermaid
graph TB
    subgraph "BookScribe Application"
        Agents[AI Agents]
        ModelSelector[Model Selector]
        RateLimiter[Rate Limiter]
        ErrorHandler[<PERSON><PERSON><PERSON>]
    end
    
    subgraph "AI Services"
        VercelAI[Vercel AI SDK]
        DirectAPI[Direct OpenAI API]
        Fallback[Fallback Handler]
    end
    
    subgraph "OpenAI Services"
        GPT4[GPT-4.1]
        GPT4Mini[GPT-o4 mini]
        Embeddings[text-embedding-3-small]
    end
    
    Agents --> ModelSelector
    ModelSelector --> VercelAI
    ModelSelector --> DirectAPI
    VercelAI --> GPT4
    VercelAI --> GPT4Mini
    DirectAPI --> Embeddings
    RateLimiter --> VercelAI
    ErrorHandler --> Fallback
```

## Configuration

### Environment Variables
```bash
# Required
OPENAI_API_KEY=sk-...

# Optional
OPENAI_ORG_ID=org-...
OPENAI_API_VERSION=v1
OPENAI_BASE_URL=https://api.openai.com
```

### Model Configuration
```typescript
// src/lib/config/ai-settings.ts
export const AI_MODELS = {
  'gpt-4.1': {
    id: 'gpt-4-1106-preview',
    contextWindow: 128000,
    outputTokens: 4096,
    tier: 'premium',
    costPer1kTokens: { input: 0.01, output: 0.03 }
  },
  'gpt-o4-mini': {
    id: 'gpt-4o-mini',
    contextWindow: 128000,
    outputTokens: 4096,
    tier: 'starter',
    costPer1kTokens: { input: 0.00015, output: 0.0006 }
  },
  'text-embedding-3-small': {
    id: 'text-embedding-3-small',
    dimensions: 1536,
    costPer1kTokens: 0.00002
  }
};
```

## Model Selection Strategy

### Subscription-Based Selection
```typescript
class AIModelSelector {
  selectModel(subscription: UserSubscription, task: TaskType): ModelConfig {
    if (subscription.tier === 'premium' || subscription.tier === 'professional') {
      return AI_MODELS['gpt-4.1'];
    }
    
    // Starter tier uses mini model
    if (subscription.tier === 'starter') {
      return AI_MODELS['gpt-o4-mini'];
    }
    
    // Free tier has limited access
    if (task === 'critical') {
      return AI_MODELS['gpt-o4-mini'];
    }
    
    throw new Error('Upgrade required for this feature');
  }
}
```

### Task-Specific Optimization
```typescript
const taskModelMapping = {
  // High-quality tasks use GPT-4.1
  storyArchitecture: 'gpt-4.1',
  characterDevelopment: 'gpt-4.1',
  chapterWriting: 'gpt-4.1',
  
  // Speed-optimized tasks use mini
  adaptivePlanning: 'gpt-o4-mini',
  quickSuggestions: 'gpt-o4-mini',
  metadataGeneration: 'gpt-o4-mini'
};
```

## API Integration Patterns

### Vercel AI SDK Integration
```typescript
import { openai } from '@ai-sdk/openai';
import { generateObject, generateText, streamText } from 'ai';

// Text generation
async function generateChapterContent(prompt: string, model: string) {
  const result = await generateText({
    model: openai(model),
    prompt,
    temperature: 0.7,
    maxTokens: 4096,
  });
  
  return result.text;
}

// Structured output
async function generateStoryStructure(prompt: string) {
  const result = await generateObject({
    model: openai('gpt-4-1106-preview'),
    schema: storyStructureSchema,
    prompt,
  });
  
  return result.object;
}

// Streaming for real-time feedback
async function streamChapterGeneration(prompt: string) {
  const result = await streamText({
    model: openai('gpt-4-1106-preview'),
    prompt,
    onFinish: ({ text, usage }) => {
      trackUsage(usage);
    },
  });
  
  return result.toTextStreamResponse();
}
```

### Direct API Usage
```typescript
// For embeddings and specialized tasks
async function generateEmbedding(text: string) {
  const response = await openai.embeddings.create({
    model: 'text-embedding-3-small',
    input: text,
  });
  
  return response.data[0].embedding;
}
```

## Rate Limiting & Quota Management

### Rate Limiter Implementation
```typescript
class OpenAIRateLimiter {
  private limits = {
    'gpt-4.1': { rpm: 500, tpm: 150000 },
    'gpt-o4-mini': { rpm: 5000, tpm: 2000000 }
  };
  
  private buckets = new Map<string, TokenBucket>();
  
  async acquireTokens(model: string, tokens: number): Promise<void> {
    const bucket = this.getBucket(model);
    await bucket.acquire(tokens);
  }
  
  trackUsage(model: string, usage: Usage): void {
    // Track for analytics and billing
    this.updateQuota(usage);
    this.emitMetrics(model, usage);
  }
}
```

### Subscription Quotas
```typescript
const subscriptionQuotas = {
  free: {
    monthlyTokens: 50000,
    dailyRequests: 20,
    models: ['gpt-o4-mini']
  },
  starter: {
    monthlyTokens: 2000000,
    dailyRequests: 1000,
    models: ['gpt-o4-mini']
  },
  professional: {
    monthlyTokens: 10000000,
    dailyRequests: 5000,
    models: ['gpt-4.1', 'gpt-o4-mini']
  },
  premium: {
    monthlyTokens: 50000000,
    dailyRequests: 'unlimited',
    models: ['gpt-4.1', 'gpt-o4-mini']
  }
};
```

## Error Handling & Resilience

### Error Types & Responses
```typescript
class OpenAIErrorHandler {
  async handleError(error: OpenAIError): Promise<ErrorResponse> {
    switch (error.code) {
      case 'rate_limit_exceeded':
        return this.handleRateLimit(error);
        
      case 'insufficient_quota':
        return this.handleQuotaExceeded(error);
        
      case 'model_not_found':
        return this.fallbackToAvailableModel(error);
        
      case 'context_length_exceeded':
        return this.handleContextOverflow(error);
        
      case 'timeout':
        return this.retryWithBackoff(error);
        
      default:
        return this.handleGenericError(error);
    }
  }
}
```

### Retry Strategy
```typescript
const retryConfig = {
  maxRetries: 3,
  initialDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
  
  retryableErrors: [
    'rate_limit_exceeded',
    'timeout',
    'internal_server_error'
  ],
  
  nonRetryableErrors: [
    'invalid_api_key',
    'insufficient_quota',
    'invalid_request'
  ]
};
```

## Context Management

### Context Window Optimization
```typescript
class ContextManager {
  private readonly MAX_CONTEXT = 128000;
  private readonly RESERVE_OUTPUT = 4096;
  
  async prepareContext(
    fullContext: BookContext,
    requirements: ContextRequirements
  ): Promise<OptimizedContext> {
    const tokenCount = await this.estimateTokens(fullContext);
    
    if (tokenCount <= this.MAX_CONTEXT - this.RESERVE_OUTPUT) {
      return fullContext;
    }
    
    // Apply compression strategies
    return this.compressContext(fullContext, requirements);
  }
  
  private compressContext(
    context: BookContext,
    requirements: ContextRequirements
  ): OptimizedContext {
    // Prioritize based on requirements
    const prioritized = this.prioritizeContent(context, requirements);
    
    // Summarize less critical content
    const summarized = this.summarizeContent(prioritized);
    
    // Ensure within limits
    return this.trimToLimit(summarized);
  }
}
```

## Prompt Engineering

### Agent-Specific Prompts
```typescript
const agentPrompts = {
  storyArchitect: {
    system: `You are a master story architect who creates award-winning 
             and bestselling novel structures...`,
    temperature: 0.8,
    topP: 0.95,
    frequencyPenalty: 0.3,
    presencePenalty: 0.3
  },
  
  writingAgent: {
    system: `You are a bestselling author who writes compelling prose 
             that could win literary awards...`,
    temperature: 0.7,
    topP: 0.9,
    frequencyPenalty: 0.5,
    presencePenalty: 0.5
  }
};
```

### Structured Output Schemas
```typescript
// Using Zod for schema validation
const storyStructureSchema = z.object({
  title: z.string(),
  premise: z.string(),
  acts: z.array(actSchema),
  themes: z.array(z.string()),
  conflicts: z.array(conflictSchema),
  timeline: z.array(timelineEventSchema)
});

// Request structured output
const response = await generateObject({
  model: openai('gpt-4-1106-preview'),
  schema: storyStructureSchema,
  prompt: generatePrompt(context),
  mode: 'json'
});
```

## Cost Optimization

### Token Usage Tracking
```typescript
interface TokenUsageTracker {
  track(usage: {
    model: string;
    promptTokens: number;
    completionTokens: number;
    totalCost: number;
  }): void;
  
  getMonthlyUsage(userId: string): MonthlyUsage;
  
  predictMonthlyBill(userId: string): BillPrediction;
  
  suggestOptimizations(usage: UsagePattern): Optimization[];
}
```

### Cost Reduction Strategies
1. **Smart Caching**: Cache common responses
2. **Batch Processing**: Group similar requests
3. **Model Selection**: Use mini for appropriate tasks
4. **Context Pruning**: Remove unnecessary context
5. **Response Limiting**: Set appropriate max tokens

## Monitoring & Analytics

### Usage Metrics
```typescript
const openAIMetrics = {
  // Request metrics
  totalRequests: Counter,
  requestLatency: Histogram,
  errorRate: Gauge,
  
  // Token metrics
  tokensUsed: Counter,
  costIncurred: Counter,
  
  // Model metrics
  modelUsage: {
    'gpt-4.1': Counter,
    'gpt-o4-mini': Counter
  },
  
  // Quality metrics
  responseQuality: Histogram,
  retryRate: Gauge
};
```

### Logging & Debugging
```typescript
const loggingConfig = {
  requests: {
    level: 'info',
    includePrompt: false, // Privacy
    includeResponse: false,
    includeMetadata: true
  },
  
  errors: {
    level: 'error',
    includeContext: true,
    alertThreshold: 5
  },
  
  performance: {
    slowRequestThreshold: 10000,
    trackP95Latency: true
  }
};
```

## Best Practices

### For Developers
1. **Always use model selector** for subscription awareness
2. **Implement proper error handling** with retries
3. **Monitor token usage** to prevent overages
4. **Cache responses** when appropriate
5. **Use streaming** for better UX on long generations

### For Optimal Performance
1. Batch similar requests together
2. Implement request queuing
3. Use appropriate temperature settings
4. Optimize prompt length
5. Monitor and adjust rate limits

### Security Considerations
1. Never expose API keys in client code
2. Validate all inputs before sending
3. Implement request signing
4. Use environment variables
5. Rotate keys regularly

## Troubleshooting

### Common Issues

1. **Rate Limit Errors**
   - Solution: Implement exponential backoff
   - Prevention: Use request queuing

2. **Context Length Exceeded**
   - Solution: Compress context aggressively
   - Prevention: Monitor context size

3. **High Costs**
   - Solution: Optimize model selection
   - Prevention: Implement usage alerts

4. **Timeout Errors**
   - Solution: Increase timeout, retry
   - Prevention: Stream long responses

5. **Quality Issues**
   - Solution: Adjust temperature/prompts
   - Prevention: A/B test configurations

## Future Enhancements

### Planned Features
1. **Fine-tuned Models**: Custom models for BookScribe
2. **Fallback Providers**: Alternative AI providers
3. **Edge Deployment**: Reduced latency
4. **Batch API**: Cost-effective processing
5. **WebGPU Integration**: Client-side inference

### Research Areas
- Prompt optimization techniques
- Context compression algorithms
- Cost prediction models
- Quality assurance automation
- Multi-model orchestration