'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { BookOpen, Globe, Users, Calendar } from 'lucide-react'
import type { Series } from '@/lib/db/types'

interface CreateSeriesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreate: (series: Partial<Series>) => void
  universeId?: string
}

export function CreateSeriesDialog({
  open,
  onOpenChange,
  onCreate,
  universeId
}: CreateSeriesDialogProps) {
  const [seriesData, setSeriesData] = useState<Partial<Series>>({
    title: '',
    description: '',
    planned_books: 3,
    genre: 'fantasy',
    target_audience: 'adult',
    universe_id: universeId,
    settings: {
      maintain_voice_consistency: true,
      share_world_building: true,
      track_character_arcs: true,
      timeline_type: 'chronological'
    }
  })

  const handleSubmit = () => {
    if (!seriesData.title?.trim()) return
    onCreate(seriesData)
    // Reset form
    setSeriesData({
      title: '',
      description: '',
      planned_books: 3,
      genre: 'fantasy',
      target_audience: 'adult',
      universe_id: universeId,
      settings: {
        maintain_voice_consistency: true,
        share_world_building: true,
        track_character_arcs: true,
        timeline_type: 'chronological'
      }
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl lg:max-w-3xl xl:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Create New Series</DialogTitle>
          <DialogDescription>
            Set up a multi-book series to maintain continuity across your novels
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Series Title</Label>
              <Input
                id="title"
                value={seriesData.title || ''}
                onChange={(e) => setSeriesData({ ...seriesData, title: e.target.value })}
                placeholder="The Chronicles of..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={seriesData.description || ''}
                onChange={(e) => setSeriesData({ ...seriesData, description: e.target.value })}
                placeholder="A sweeping epic that follows..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
              <div className="space-y-2">
                <Label htmlFor="genre">Genre</Label>
                <Select
                  value={seriesData.genre || 'fantasy'}
                  onValueChange={(value) => setSeriesData({ ...seriesData, genre: value })}
                >
                  <SelectTrigger id="genre">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fantasy">Fantasy</SelectItem>
                    <SelectItem value="scifi">Science Fiction</SelectItem>
                    <SelectItem value="mystery">Mystery</SelectItem>
                    <SelectItem value="romance">Romance</SelectItem>
                    <SelectItem value="thriller">Thriller</SelectItem>
                    <SelectItem value="literary">Literary Fiction</SelectItem>
                    <SelectItem value="historical">Historical Fiction</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="planned-books">Planned Books</Label>
                <Input
                  id="planned-books"
                  type="number"
                  min="2"
                  max="20"
                  value={seriesData.planned_books || 3}
                  onChange={(e) => setSeriesData({ ...seriesData, planned_books: parseInt(e.target.value) || 3 })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="audience">Target Audience</Label>
              <Select
                value={seriesData.target_audience || 'adult'}
                onValueChange={(value) => setSeriesData({ ...seriesData, target_audience: value })}
              >
                <SelectTrigger id="audience">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="middle-grade">Middle Grade (8-12)</SelectItem>
                  <SelectItem value="young-adult">Young Adult (13-17)</SelectItem>
                  <SelectItem value="new-adult">New Adult (18-25)</SelectItem>
                  <SelectItem value="adult">Adult (18+)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Series Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Series Settings</CardTitle>
              <CardDescription>Configure how books in this series will be connected</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Maintain Voice Consistency</Label>
                  <p className="text-sm text-muted-foreground">
                    Use the same writing style across all books
                  </p>
                </div>
                <Switch
                  checked={seriesData.settings?.maintain_voice_consistency ?? true}
                  onCheckedChange={(checked) => 
                    setSeriesData({
                      ...seriesData,
                      settings: { ...seriesData.settings, maintain_voice_consistency: checked }
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Share World Building</Label>
                  <p className="text-sm text-muted-foreground">
                    Books share the same world and settings
                  </p>
                </div>
                <Switch
                  checked={seriesData.settings?.share_world_building ?? true}
                  onCheckedChange={(checked) => 
                    setSeriesData({
                      ...seriesData,
                      settings: { ...seriesData.settings, share_world_building: checked }
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Track Character Arcs</Label>
                  <p className="text-sm text-muted-foreground">
                    Monitor character development across books
                  </p>
                </div>
                <Switch
                  checked={seriesData.settings?.track_character_arcs ?? true}
                  onCheckedChange={(checked) => 
                    setSeriesData({
                      ...seriesData,
                      settings: { ...seriesData.settings, track_character_arcs: checked }
                    })
                  }
                />
              </div>

              <div className="space-y-2">
                <Label>Timeline Type</Label>
                <Select
                  value={seriesData.settings?.timeline_type || 'chronological'}
                  onValueChange={(value) => 
                    setSeriesData({
                      ...seriesData,
                      settings: { ...seriesData.settings, timeline_type: value }
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chronological">Chronological</SelectItem>
                    <SelectItem value="parallel">Parallel</SelectItem>
                    <SelectItem value="standalone">Standalone</SelectItem>
                    <SelectItem value="anthology">Anthology</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle className="text-base">Series Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                  <span>{seriesData.planned_books} planned books</span>
                </div>
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="capitalize">{seriesData.genre} genre</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="capitalize">{seriesData.target_audience} audience</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="capitalize">{seriesData.settings?.timeline_type} timeline</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!seriesData.title?.trim()}>
            Create Series
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}