-- Create AI usage logs table for tracking AI agent usage
-- This is needed for AI-related achievements

CREATE TABLE IF NOT EXISTS ai_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  agent_type VARCHAR(100) NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10, 4) DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_user ON ai_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_project ON ai_usage_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_created ON ai_usage_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_agent_type ON ai_usage_logs(agent_type);

-- Enable RLS
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own AI usage" ON ai_usage_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own AI usage" ON ai_usage_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Grant permissions
GRANT SELECT, INSERT ON ai_usage_logs TO authenticated;

-- Create writing sessions table for tracking daily streaks
CREATE TABLE IF NOT EXISTS writing_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  session_date DATE NOT NULL,
  words_written INTEGER DEFAULT 0,
  duration_minutes INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, session_date)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user ON writing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_date ON writing_sessions(session_date DESC);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_project ON writing_sessions(project_id);

-- Enable RLS
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own writing sessions" ON writing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own writing sessions" ON writing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON writing_sessions TO authenticated;

-- Update the achievement checking function to handle missing tables
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE (newly_unlocked UUID[]) AS $$
DECLARE
  v_newly_unlocked UUID[];
  v_achievement RECORD;
  v_current_value INTEGER;
  v_progress INTEGER;
  v_user_achievement_id UUID;
  v_table_exists BOOLEAN;
BEGIN
  v_newly_unlocked := ARRAY[]::UUID[];
  
  -- Check each achievement
  FOR v_achievement IN 
    SELECT a.* 
    FROM achievements a
    LEFT JOIN user_achievements ua ON ua.achievement_id = a.id AND ua.user_id = p_user_id
    WHERE ua.unlocked_at IS NULL OR ua.id IS NULL
  LOOP
    v_current_value := 0;
    
    -- Calculate current value based on achievement type
    CASE v_achievement.criteria->>'type'
      WHEN 'word_count' THEN
        SELECT COALESCE(SUM(word_count), 0)
        FROM chapters c
        JOIN projects p ON p.id = c.project_id
        WHERE p.user_id = p_user_id
        INTO v_current_value;
        
      WHEN 'chapters_completed' THEN
        SELECT COUNT(*)
        FROM chapters c
        JOIN projects p ON p.id = c.project_id
        WHERE p.user_id = p_user_id 
        AND c.status IN ('published', 'completed')
        INTO v_current_value;
        
      WHEN 'projects_created' THEN
        SELECT COUNT(*)
        FROM projects
        WHERE user_id = p_user_id
        INTO v_current_value;
        
      WHEN 'series_created' THEN
        -- Check if series table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'series'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM series
          WHERE user_id = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'characters_created' THEN
        -- Check if characters table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'characters'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM characters c
          JOIN books b ON b.id = c.book_id
          JOIN projects p ON p.id = b.project_id
          WHERE p.user_id = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'universes_created' THEN
        -- Check if universes table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'universes'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM universes
          WHERE created_by = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'daily_streak' THEN
        -- Check if writing_sessions table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'writing_sessions'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          -- Calculate consecutive days streak
          WITH consecutive_days AS (
            SELECT 
              session_date,
              session_date - INTERVAL '1 day' * ROW_NUMBER() OVER (ORDER BY session_date) AS grp
            FROM writing_sessions
            WHERE user_id = p_user_id
            AND words_written > 0
            ORDER BY session_date DESC
          ),
          streaks AS (
            SELECT 
              COUNT(*) as streak_length,
              MAX(session_date) as last_date
            FROM consecutive_days
            GROUP BY grp
            ORDER BY MAX(session_date) DESC
            LIMIT 1
          )
          SELECT COALESCE(MAX(streak_length), 0)::INTEGER
          FROM streaks
          WHERE last_date >= CURRENT_DATE - INTERVAL '1 day'
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'daily_words' THEN
        -- Check if writing_sessions table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'writing_sessions'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COALESCE(MAX(words_written), 0)
          FROM writing_sessions
          WHERE user_id = p_user_id
          AND session_date = CURRENT_DATE
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'ai_uses' THEN
        -- Check if ai_usage_logs table exists
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = 'ai_usage_logs'
        ) INTO v_table_exists;
        
        IF v_table_exists THEN
          SELECT COUNT(*)
          FROM ai_usage_logs
          WHERE user_id = p_user_id
          INTO v_current_value;
        ELSE
          v_current_value := 0;
        END IF;
        
      WHEN 'exports' THEN
        -- For now, return 0 (would need export_logs table)
        v_current_value := 0;
        
      ELSE
        v_current_value := 0;
    END CASE;
    
    -- Calculate progress percentage
    IF v_achievement.target_value > 0 THEN
      v_progress := LEAST(100, (v_current_value * 100 / v_achievement.target_value));
    ELSE
      v_progress := 0;
    END IF;
    
    -- Insert or update user achievement record
    INSERT INTO user_achievements (user_id, achievement_id, current_value, progress)
    VALUES (p_user_id, v_achievement.id, v_current_value, v_progress)
    ON CONFLICT (user_id, achievement_id) 
    DO UPDATE SET 
      current_value = EXCLUDED.current_value,
      progress = EXCLUDED.progress,
      updated_at = NOW()
    RETURNING id INTO v_user_achievement_id;
    
    -- Check if achievement should be unlocked
    IF v_current_value >= v_achievement.target_value THEN
      UPDATE user_achievements 
      SET unlocked_at = NOW()
      WHERE id = v_user_achievement_id 
      AND unlocked_at IS NULL;
      
      IF FOUND THEN
        v_newly_unlocked := array_append(v_newly_unlocked, v_achievement.id);
      END IF;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT v_newly_unlocked;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to log AI usage when AI endpoints are used
CREATE OR REPLACE FUNCTION log_ai_usage()
RETURNS TRIGGER AS $$
BEGIN
  -- This function would be called by triggers on AI-related operations
  -- For now, it's a placeholder
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update writing sessions
CREATE OR REPLACE FUNCTION update_writing_session(
  p_user_id UUID,
  p_project_id UUID,
  p_words_written INTEGER
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO writing_sessions (user_id, project_id, session_date, words_written)
  VALUES (p_user_id, p_project_id, CURRENT_DATE, p_words_written)
  ON CONFLICT (user_id, session_date)
  DO UPDATE SET 
    words_written = writing_sessions.words_written + p_words_written,
    duration_minutes = EXTRACT(EPOCH FROM (NOW() - writing_sessions.created_at)) / 60;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION update_writing_session(UUID, UUID, INTEGER) TO authenticated;

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE 'AI usage logs and writing sessions tables created successfully!';
    RAISE NOTICE 'Achievement system updated to handle missing tables gracefully.';
END $$;