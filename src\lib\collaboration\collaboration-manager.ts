import { WebSocketClient, CollaborationEvent } from './websocket-client';
import { OperationalTransform, Operation } from './operational-transform';
import { logger } from '@/lib/services/logger';
import { trackFeatureUsage } from '@/lib/monitoring/sentry';
import { TIME_MS } from '@/lib/constants'

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
}

export interface Collaborator extends User {
  cursor?: { line: number; column: number };
  selection?: { start: any; end: any };
  status: 'active' | 'idle' | 'away';
  lastSeen: number;
}

export interface CollaborationSession {
  projectId: string;
  chapterId?: string;
  documentId: string;
  version: number;
  collaborators: Map<string, Collaborator>;
  localOperations: Operation[];
  serverOperations: Operation[];
}

export interface CollaborationOptions {
  wsUrl: string;
  token: string;
  user: User;
  projectId: string;
  chapterId?: string;
  documentId: string;
  onContentChange?: (content: string) => void;
  onCollaboratorJoin?: (collaborator: Collaborator) => void;
  onCollaboratorLeave?: (userId: string) => void;
  onCollaboratorUpdate?: (collaborator: Collaborator) => void;
  onSyncComplete?: () => void;
  debug?: boolean;
}

export class CollaborationManager {
  private ws: WebSocketClient;
  private ot: OperationalTransform;
  private session: CollaborationSession;
  private options: CollaborationOptions;
  private syncInProgress = false;
  private presenceUpdateTimer?: NodeJS.Timeout;
  private idleTimer?: NodeJS.Timeout;
  private content: string = '';

  constructor(options: CollaborationOptions) {
    this.options = options;
    
    this.ws = new WebSocketClient({
      url: options.wsUrl,
      token: options.token,
      debug: options.debug,
    });
    
    this.ot = new OperationalTransform();
    
    this.session = {
      projectId: options.projectId,
      chapterId: options.chapterId,
      documentId: options.documentId,
      version: 0,
      collaborators: new Map(),
      localOperations: [],
      serverOperations: [],
    };
    
    this.setupEventListeners();
    trackFeatureUsage('collaboration_started', { projectId: options.projectId });
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);

      this.ws.once('connected', () => {
        clearTimeout(timeout);
        this.joinSession();
        this.startPresenceUpdates();
        resolve();
      });

      this.ws.once('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });

      this.ws.connect();
    });
  }

  disconnect(): void {
    this.stopPresenceUpdates();
    this.ws.send('leave', { documentId: this.session.documentId });
    this.ws.disconnect();
    trackFeatureUsage('collaboration_ended', { 
      projectId: this.options.projectId,
      duration: Date.now() - this.session.collaborators.get(this.options.user.id)?.lastSeen || 0
    });
  }

  // Content editing methods
  insertText(position: number, text: string): void {
    const operation: Operation = {
      type: 'insert',
      position,
      content: text,
      userId: this.options.user.id,
      timestamp: Date.now(),
    };
    
    this.applyLocalOperation(operation);
  }

  deleteText(position: number, length: number): void {
    const operation: Operation = {
      type: 'delete',
      position,
      length,
      userId: this.options.user.id,
      timestamp: Date.now(),
    };
    
    this.applyLocalOperation(operation);
  }

  replaceText(position: number, length: number, text: string): void {
    const operation: Operation = {
      type: 'replace',
      position,
      length,
      content: text,
      userId: this.options.user.id,
      timestamp: Date.now(),
    };
    
    this.applyLocalOperation(operation);
  }

  // Cursor and selection methods
  updateCursor(position: { line: number; column: number }): void {
    this.ws.sendCursorPosition(position);
    this.resetIdleTimer();
  }

  updateSelection(selection: { start: any; end: any }): void {
    this.ws.sendSelection(selection);
    this.resetIdleTimer();
  }

  // Get current collaborators
  getCollaborators(): Collaborator[] {
    return Array.from(this.session.collaborators.values())
      .filter(c => c.id !== this.options.user.id);
  }

  getCollaborator(userId: string): Collaborator | undefined {
    return this.session.collaborators.get(userId);
  }

  // Private methods
  private setupEventListeners(): void {
    this.ws.on('connected', () => {
      logger.info('Collaboration connected');
    });

    this.ws.on('disconnected', () => {
      logger.info('Collaboration disconnected');
      this.stopPresenceUpdates();
    });

    this.ws.on('error', (error) => {
      logger.error('Collaboration error:', error);
    });

    this.ws.on('collaboration', (event: CollaborationEvent) => {
      this.handleCollaborationEvent(event);
    });

    this.ws.on('presence', (data) => {
      this.handlePresenceUpdate(data);
    });

    this.ws.on('sync', (data) => {
      this.handleSync(data);
    });

    this.ws.on('message', (message) => {
      this.handleMessage(message);
    });
  }

  private joinSession(): void {
    this.ws.send('join', {
      documentId: this.session.documentId,
      user: this.options.user,
      cursor: null,
      selection: null,
    });
  }

  private handleCollaborationEvent(event: CollaborationEvent): void {
    switch (event.type) {
      case 'edit':
        this.handleRemoteOperation(event.data);
        break;
      case 'cursor':
        this.handleRemoteCursor(event.userId, event.data);
        break;
      case 'selection':
        this.handleRemoteSelection(event.userId, event.data);
        break;
    }
  }

  private handleRemoteOperation(operation: Operation): void {
    // Transform the operation against local operations
    const transformed = this.ot.transformOperation(
      operation,
      this.session.localOperations
    );
    
    // Apply the transformed operation
    this.content = this.ot.applyOperation(this.content, transformed);
    this.session.serverOperations.push(transformed);
    this.session.version++;
    
    // Notify about content change
    if (this.options.onContentChange) {
      this.options.onContentChange(this.content);
    }
  }

  private applyLocalOperation(operation: Operation): void {
    // Apply operation locally
    this.content = this.ot.applyOperation(this.content, operation);
    this.session.localOperations.push(operation);
    
    // Send to server
    this.ws.sendEdit({
      operation: operation.type,
      position: operation.position,
      content: operation.content,
      length: operation.length,
    });
    
    // Notify about content change
    if (this.options.onContentChange) {
      this.options.onContentChange(this.content);
    }
  }

  private handleRemoteCursor(userId: string, cursor: any): void {
    const collaborator = this.session.collaborators.get(userId);
    if (collaborator) {
      collaborator.cursor = cursor;
      collaborator.lastSeen = Date.now();
      if (this.options.onCollaboratorUpdate) {
        this.options.onCollaboratorUpdate(collaborator);
      }
    }
  }

  private handleRemoteSelection(userId: string, selection: any): void {
    const collaborator = this.session.collaborators.get(userId);
    if (collaborator) {
      collaborator.selection = selection;
      collaborator.lastSeen = Date.now();
      if (this.options.onCollaboratorUpdate) {
        this.options.onCollaboratorUpdate(collaborator);
      }
    }
  }

  private handlePresenceUpdate(data: any): void {
    if (data.type === 'join') {
      const collaborator: Collaborator = {
        ...data.user,
        status: 'active',
        lastSeen: Date.now(),
      };
      
      this.session.collaborators.set(data.user.id, collaborator);
      
      if (this.options.onCollaboratorJoin && data.user.id !== this.options.user.id) {
        this.options.onCollaboratorJoin(collaborator);
      }
    } else if (data.type === 'leave') {
      this.session.collaborators.delete(data.userId);
      
      if (this.options.onCollaboratorLeave && data.userId !== this.options.user.id) {
        this.options.onCollaboratorLeave(data.userId);
      }
    } else if (data.type === 'update') {
      const collaborator = this.session.collaborators.get(data.userId);
      if (collaborator) {
        Object.assign(collaborator, data.updates);
        collaborator.lastSeen = Date.now();
        
        if (this.options.onCollaboratorUpdate && data.userId !== this.options.user.id) {
          this.options.onCollaboratorUpdate(collaborator);
        }
      }
    }
  }

  private handleSync(data: any): void {
    this.syncInProgress = true;
    
    // Update document version
    this.session.version = data.version;
    
    // Update content
    this.content = data.content;
    
    // Clear local operations that have been acknowledged
    this.session.localOperations = this.session.localOperations.filter(
      op => op.timestamp > data.lastProcessedTimestamp
    );
    
    // Update collaborators
    if (data.collaborators) {
      this.session.collaborators.clear();
      data.collaborators.forEach((c: any) => {
        this.session.collaborators.set(c.id, {
          ...c,
          lastSeen: Date.now(),
        });
      });
    }
    
    this.syncInProgress = false;
    
    if (this.options.onSyncComplete) {
      this.options.onSyncComplete();
    }
    
    // Notify about content change
    if (this.options.onContentChange) {
      this.options.onContentChange(this.content);
    }
  }

  private handleMessage(message: any): void {
    logger.debug('Received message:', message);
  }

  private startPresenceUpdates(): void {
    // Add self to collaborators
    const self: Collaborator = {
      ...this.options.user,
      status: 'active',
      lastSeen: Date.now(),
    };
    this.session.collaborators.set(this.options.user.id, self);

    // Send presence updates periodically
    this.presenceUpdateTimer = setInterval(() => {
      this.ws.sendPresence({
        status: self.status,
        cursor: self.cursor,
        selection: self.selection,
      });
    }, TIME_MS.TOAST_DURATION);
    
    this.resetIdleTimer();
  }

  private stopPresenceUpdates(): void {
    if (this.presenceUpdateTimer) {
      clearInterval(this.presenceUpdateTimer);
      this.presenceUpdateTimer = undefined;
    }
    
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = undefined;
    }
  }

  private resetIdleTimer(): void {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }
    
    const self = this.session.collaborators.get(this.options.user.id);
    if (self) {
      self.status = 'active';
    }
    
    this.idleTimer = setTimeout(() => {
      const self = this.session.collaborators.get(this.options.user.id);
      if (self) {
        self.status = 'idle';
        this.ws.sendPresence({ status: 'idle' });
      }
    }, 60000); // 1 minute
  }

  // Utility methods
  setContent(content: string): void {
    this.content = content;
  }

  getContent(): string {
    return this.content;
  }

  getVersion(): number {
    return this.session.version;
  }

  isConnected(): boolean {
    return this.ws.isConnected();
  }

  isSyncing(): boolean {
    return this.syncInProgress;
  }
}