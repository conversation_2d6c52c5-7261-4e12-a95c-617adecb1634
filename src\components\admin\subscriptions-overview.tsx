'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { SUBSCRIPTION_TIERS } from '@/lib/subscription'
import { logger } from '@/lib/services/logger'
import { TrendingUp, Users, DollarSign, Package } from 'lucide-react'

interface TierStats {
  tier: string
  count: number
  revenue: number
  percentage: number
}

interface SubscriptionMetrics {
  totalSubscriptions: number
  monthlyRecurringRevenue: number
  averageRevenuePerUser: number
  churnRate: number
  tierDistribution: TierStats[]
}

export function SubscriptionsOverview() {
  const [metrics, setMetrics] = useState<SubscriptionMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  const supabase = createClient()

  useEffect(() => {
    loadSubscriptionMetrics()
  }, [])

  const loadSubscriptionMetrics = async () => {
    try {
      // Load active subscriptions
      const { data: subscriptions, error } = await supabase
        .from('user_subscriptions')
        .select('tier, status, created_at')
        .eq('status', 'active')

      if (error) throw error

      // Calculate tier distribution
      const tierCounts: Record<string, number> = {}
      subscriptions?.forEach(sub => {
        tierCounts[sub.tier] = (tierCounts[sub.tier] || 0) + 1
      })

      const totalSubs = subscriptions?.length || 0
      const tierDistribution: TierStats[] = SUBSCRIPTION_TIERS.map(tier => {
        const count = tierCounts[tier.id] || 0
        return {
          tier: tier.name,
          count,
          revenue: count * tier.price,
          percentage: totalSubs > 0 ? (count / totalSubs) * 100 : 0
        }
      })

      // Calculate MRR
      const mrr = tierDistribution.reduce((total, tier) => total + tier.revenue, 0)
      const arpu = totalSubs > 0 ? mrr / totalSubs : 0

      // Calculate churn (simplified - subscriptions canceled in last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { count: churnedCount } = await supabase
        .from('user_subscriptions')
        .select('id', { count: 'exact' })
        .eq('status', 'canceled')
        .gte('updated_at', thirtyDaysAgo.toISOString())

      const churnRate = totalSubs > 0 ? ((churnedCount || 0) / totalSubs) * 100 : 0

      setMetrics({
        totalSubscriptions: totalSubs,
        monthlyRecurringRevenue: mrr,
        averageRevenuePerUser: arpu,
        churnRate,
        tierDistribution
      })
    } catch (error) {
      logger.error('Failed to load subscription metrics:', error)
      toast({
        title: 'Error',
        description: 'Failed to load subscription metrics',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading || !metrics) {
    return <div>Loading subscription data...</div>
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subscriptions</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalSubscriptions}</div>
            <p className="text-xs text-muted-foreground">Active paying customers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Recurring Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.monthlyRecurringRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">MRR</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Revenue Per User</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.averageRevenuePerUser.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">ARPU</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.churnRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Tier Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Subscription Tier Distribution</CardTitle>
          <CardDescription>Breakdown of active subscriptions by tier</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {metrics.tierDistribution.map((tier) => (
            <div key={tier.tier} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{tier.tier}</span>
                  <Badge variant="outline">{tier.count} users</Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  ${tier.revenue}/mo ({tier.percentage.toFixed(1)}%)
                </div>
              </div>
              <Progress value={tier.percentage} className="h-2" />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Revenue Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Revenue Analysis</CardTitle>
          <CardDescription>Monthly revenue by subscription tier</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.tierDistribution
              .filter(tier => tier.revenue > 0)
              .sort((a, b) => b.revenue - a.revenue)
              .map((tier) => {
                const revenuePercentage = (tier.revenue / metrics.monthlyRecurringRevenue) * 100
                return (
                  <div key={tier.tier} className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{tier.tier}</div>
                      <div className="text-sm text-muted-foreground">
                        {tier.count} customers × ${SUBSCRIPTION_TIERS.find(t => t.name === tier.tier)?.price}/mo
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">${tier.revenue}</div>
                      <div className="text-sm text-muted-foreground">
                        {revenuePercentage.toFixed(1)}% of MRR
                      </div>
                    </div>
                  </div>
                )
              })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}