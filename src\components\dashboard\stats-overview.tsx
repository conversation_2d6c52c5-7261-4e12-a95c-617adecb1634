"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { 
  BookOpen, 
  Target, 
  TrendingUp, 
  FileText, 
  Users,
  Clock,
  Award
} from 'lucide-react'

interface Project {
  id: string
  title: string
  status: string
  target_word_count: number | null
  current_word_count: number
  created_at: string
  updated_at: string
  chapters?: Array<{
    status: string
    actual_word_count: number
  }>
  characters?: Array<{
    id: string
  }>
}

interface StatsOverviewProps {
  projects: Project[]
}

function StatsOverviewComponent({ projects }: StatsOverviewProps) {
  // Calculate aggregate statistics
  const totalProjects = projects.length
  const activeProjects = projects.filter(p => ['writing', 'editing'].includes(p.status)).length
  const completedProjects = projects.filter(p => p.status === 'complete').length
  
  const totalWordsWritten = projects.reduce((sum, project) => sum + project.current_word_count, 0)
  const totalTargetWords = projects.reduce((sum, project) => sum + (project.target_word_count || 0), 0)
  const overallProgress = totalTargetWords > 0 ? (totalWordsWritten / totalTargetWords) * 100 : 0
  
  const totalChapters = projects.reduce((sum, project) => {
    return sum + (project.chapters?.length || 0)
  }, 0)
  
  const completedChapters = projects.reduce((sum, project) => {
    return sum + (project.chapters?.filter(ch => ch.status === 'complete').length || 0)
  }, 0)
  
  const totalCharacters = projects.reduce((sum, project) => {
    return sum + (project.characters?.length || 0)
  }, 0)
  
  // Calculate average daily words (last 30 days)
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  const recentProjects = projects.filter(p => 
    new Date(p.updated_at) > thirtyDaysAgo
  )
  
  const recentWords = recentProjects.reduce((sum, project) => sum + project.current_word_count, 0)
  const avgWordsPerDay = Math.round(recentWords / 30)
  
  // Get most productive project
  const mostProductiveProject = projects.reduce((max, project) => {
    return project.current_word_count > (max?.current_word_count || 0) ? project : max
  }, projects[0])
  
  const stats = [
    {
      title: 'Total Projects',
      value: totalProjects,
      icon: BookOpen,
      description: `${activeProjects} active, ${completedProjects} completed`,
      color: 'text-info'
    },
    {
      title: 'Words Written',
      value: totalWordsWritten.toLocaleString(),
      icon: Target,
      description: totalTargetWords > 0 
        ? `${overallProgress.toFixed(1)}% of target`
        : 'Across all projects',
      color: 'text-success'
    },
    {
      title: 'Chapters',
      value: `${completedChapters}/${totalChapters}`,
      icon: FileText,
      description: totalChapters > 0 
        ? `${((completedChapters / totalChapters) * 100).toFixed(1)}% complete`
        : 'No chapters yet',
      color: 'text-purple-600'
    },
    {
      title: 'Characters',
      value: totalCharacters,
      icon: Users,
      description: `Across ${totalProjects} projects`,
      color: 'text-warning'
    }
  ]
  
  const additionalStats = [
    {
      title: 'Daily Average',
      value: avgWordsPerDay > 0 ? `${avgWordsPerDay}` : '0',
      subtitle: 'words/day (30d)',
      icon: TrendingUp,
      color: 'text-success'
    },
    {
      title: 'Most Productive',
      value: mostProductiveProject?.title || 'No projects',
      subtitle: mostProductiveProject 
        ? `${mostProductiveProject.current_word_count.toLocaleString()} words`
        : '',
      icon: Award,
      color: 'text-warning'
    },
    {
      title: 'Latest Activity',
      value: projects.length > 0 
        ? new Date(Math.max(...projects.map(p => new Date(p.updated_at).getTime())))
            .toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        : 'No activity',
      subtitle: 'Last update',
      icon: Clock,
      color: 'text-info'
    }
  ]
  
  if (projects.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-6 sm:py-8 lg:py-10">
          <div className="text-center space-y-2">
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto" />
            <h3 className="text-lg font-semibold">No projects yet</h3>
            <p className="text-muted-foreground">Create your first project to see your writing statistics</p>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid gap-4 sm:gap-5 lg:gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between space-y-0 pb-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </div>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    {stat.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
      
      {/* Overall Progress */}
      {totalTargetWords > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Overall Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Total Progress Across All Projects</span>
                <span className="font-medium">
                  {totalWordsWritten.toLocaleString()} / {totalTargetWords.toLocaleString()} words
                </span>
              </div>
              <Progress value={overallProgress} className="h-3" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{overallProgress.toFixed(1)}% complete</span>
                <span>{(totalTargetWords - totalWordsWritten).toLocaleString()} words remaining</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Additional Stats */}
      <div className="grid gap-4 sm:gap-5 lg:gap-6 md:grid-cols-3">
        {additionalStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Icon className={`h-5 w-5 ${stat.color}`} />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-lg font-semibold truncate">
                      {stat.value}
                    </p>
                    {stat.subtitle && (
                      <p className="text-xs text-muted-foreground">
                        {stat.subtitle}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}

export const StatsOverview = React.memo(StatsOverviewComponent)