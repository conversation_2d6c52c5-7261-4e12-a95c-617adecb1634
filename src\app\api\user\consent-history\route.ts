import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Get user's consent history
    const { data: consents, error } = await supabase
      .from('user_consent_history')
      .select('*')
      .eq('user_id', user.id)
      .order('granted_at', { ascending: false })
      .limit(50)

    if (error) {
      throw error
    }

    return NextResponse.json({
      consents: consents || []
    })
  } catch (error) {
    console.error('Error fetching consent history:', error)
    return NextResponse.json(
      { error: 'Failed to fetch consent history' },
      { status: 500 }
    )
  }
})