'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Users, Search, BookOpen, TrendingUp, Link as LinkIcon } from 'lucide-react'
import { LoadingSkeleton } from '@/components/ui/loading-skeleton'
import { EmptyState } from '@/components/ui/empty-state'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import { toast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import type { Series, Character } from '@/lib/db/types'

interface SeriesCharacterMapProps {
  series: Array<Series & {
    projects?: Array<{
      project: {
        id: string
        title: string
      }
    }>
  }>
}

interface CharacterWithBooks extends Character {
  appearances: Array<{
    projectId: string
    projectTitle: string
    seriesId: string
    seriesTitle: string
  }>
}

export function SeriesCharacterMap({ series }: SeriesCharacterMapProps) {
  const [characters, setCharacters] = useState<CharacterWithBooks[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedView, setSelectedView] = useState<'all' | 'crossover' | 'recurring'>('all')

  useEffect(() => {
    loadCharacters()
  }, [series])

  const loadCharacters = async () => {
    try {
      setLoading(true)
      const supabase = createClient()
      
      // Get all project IDs from all series
      const projectIds = series.flatMap(s => 
        (s.projects || []).map(p => p.project.id)
      )

      if (projectIds.length === 0) {
        setCharacters([])
        return
      }

      // Load all characters from these projects
      const { data: charactersData, error } = await supabase
        .from('characters')
        .select('*')
        .in('project_id', projectIds)

      if (error) throw error

      // Map characters with their appearances
      const characterMap = new Map<string, CharacterWithBooks>()

      charactersData?.forEach(char => {
        const existingChar = characterMap.get(char.name.toLowerCase())
        
        if (existingChar) {
          // Character appears in multiple books
          const project = series.flatMap(s => s.projects || [])
            .find(p => p.project.id === char.project_id)
          
          if (project) {
            const seriesInfo = series.find(s => 
              s.projects?.some(p => p.project.id === char.project_id)
            )
            
            existingChar.appearances.push({
              projectId: char.project_id,
              projectTitle: project.project.title,
              seriesId: seriesInfo?.id || '',
              seriesTitle: seriesInfo?.title || ''
            })
          }
        } else {
          // New character
          const project = series.flatMap(s => s.projects || [])
            .find(p => p.project.id === char.project_id)
          
          const seriesInfo = series.find(s => 
            s.projects?.some(p => p.project.id === char.project_id)
          )
          
          characterMap.set(char.name.toLowerCase(), {
            ...char,
            appearances: project && seriesInfo ? [{
              projectId: char.project_id,
              projectTitle: project.project.title,
              seriesId: seriesInfo.id,
              seriesTitle: seriesInfo.title
            }] : []
          })
        }
      })

      setCharacters(Array.from(characterMap.values()))
    } catch (error) {
      logger.error('Failed to load characters', error)
      toast({
        title: 'Error',
        description: 'Failed to load character data',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredCharacters = characters.filter(char => {
    // Filter by search query
    if (searchQuery && !char.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }

    // Filter by view type
    switch (selectedView) {
      case 'crossover':
        return char.appearances.length > 1 && 
          new Set(char.appearances.map(a => a.seriesId)).size > 1
      case 'recurring':
        return char.appearances.length > 1
      default:
        return true
    }
  })

  const stats = {
    totalCharacters: characters.length,
    recurringCharacters: characters.filter(c => c.appearances.length > 1).length,
    crossoverCharacters: characters.filter(c => 
      c.appearances.length > 1 && 
      new Set(c.appearances.map(a => a.seriesId)).size > 1
    ).length
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <LoadingSkeleton type="stats-grid" count={3} />
        <LoadingSkeleton type="list" count={5} />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Characters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCharacters}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Recurring Characters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recurringCharacters}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Cross-Series Characters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.crossoverCharacters}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search characters..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Tabs value={selectedView} onValueChange={(v) => setSelectedView(v as 'all' | 'crossover' | 'recurring')}>
          <TabsList>
            <TabsTrigger value="all">All Characters</TabsTrigger>
            <TabsTrigger value="recurring">Recurring</TabsTrigger>
            <TabsTrigger value="crossover">Cross-Series</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Character Grid */}
      {filteredCharacters.length === 0 ? (
        <EmptyState
          icon={Users}
          title="No Characters Found"
          description={searchQuery ? 'Try adjusting your search' : 'Create characters in your books to see them here'}
        />
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredCharacters.map(char => (
            <Card key={char.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback>
                      {char.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{char.name}</CardTitle>
                    <CardDescription className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {char.role}
                      </Badge>
                      {char.appearances.length > 1 && (
                        <Badge variant="secondary" className="text-xs gap-1">
                          <TrendingUp className="h-3 w-3" />
                          {char.appearances.length} books
                        </Badge>
                      )}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {char.description && (
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {char.description}
                  </p>
                )}
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium flex items-center gap-1">
                    <BookOpen className="h-3 w-3" />
                    Appearances
                  </h4>
                  <div className="space-y-1">
                    {char.appearances.map((appearance, idx) => (
                      <div key={idx} className="text-sm">
                        <Link
                          href={`/projects/${appearance.projectId}`}
                          className="hover:text-primary transition-colors"
                        >
                          {appearance.projectTitle}
                        </Link>
                        <span className="text-muted-foreground"> in </span>
                        <Link
                          href={`/series/${appearance.seriesId}`}
                          className="hover:text-primary transition-colors"
                        >
                          {appearance.seriesTitle}
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>

                {char.appearances.length > 1 && 
                 new Set(char.appearances.map(a => a.seriesId)).size > 1 && (
                  <div className="mt-3 pt-3 border-t">
                    <Badge className="gap-1 text-xs">
                      <LinkIcon className="h-3 w-3" />
                      Cross-Series Character
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}