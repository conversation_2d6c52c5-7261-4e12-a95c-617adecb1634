# BookScribe Project Invitations & Collaboration System

## Overview

The Project Invitations & Collaboration System enables authors to invite team members to collaborate on their writing projects. It supports role-based permissions, secure invitation tokens, real-time collaboration features, and comprehensive access control.

## Architecture

### Database Schema

#### Project Invitations Table
Manages invitation lifecycle:

```sql
project_invitations:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - email: VARCHAR(255) - Invitee email
  - role: VARC<PERSON>R(20) - editor, viewer
  - token: VA<PERSON>HA<PERSON>(255) - Unique invitation token
  - inviter_id: UUID - References users
  - user_id: UUID - Set when accepted
  - status: VARCHAR(20) - pending, accepted, expired
  - expires_at: TIMESTAMPTZ - Token expiration
  - accepted_at: TIMESTAMPTZ - When accepted
  - message: TEXT - Personal message from inviter
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Project Collaborators Table
Active collaboration management:

```sql
project_collaborators:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - user_id: UUID - References auth.users
  - user_email: TEXT - Collaborator email
  - role: TEXT - viewer, commenter, editor, admin
  - status: TEXT - pending, accepted, declined
  - invited_by: UUID - References auth.users
  - invitation_message: TEXT
  - permissions: JSONB - Granular permissions
  - last_accessed_at: TIMESTAMPTZ
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
  - UNIQUE(project_id, user_email)
```

#### Collaboration Activity Table
Track collaboration actions:

```sql
collaboration_activity:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - user_id: UUID - References auth.users
  - activity_type: VARCHAR(50) - view, edit, comment, export
  - target_type: VARCHAR(50) - project, chapter, character
  - target_id: UUID - ID of affected resource
  - details: JSONB - Activity-specific data
  - ip_address: INET
  - user_agent: TEXT
  - created_at: TIMESTAMPTZ
```

## Collaboration Roles

### 1. Viewer
Read-only access:
- View all project content
- Read chapters and characters
- Access project analytics
- Export for personal use
- Cannot make any changes

### 2. Commenter
Viewer plus commenting:
- All viewer permissions
- Add comments on chapters
- Suggest edits (tracked changes)
- Participate in discussions
- Cannot directly edit content

### 3. Editor
Full content editing:
- All commenter permissions
- Edit chapters and content
- Create/modify characters
- Manage plot elements
- Use AI features
- Cannot change project settings

### 4. Admin
Project management:
- All editor permissions
- Manage collaborators
- Change project settings
- Delete content
- Access billing (if enabled)
- Transfer ownership

## API Endpoints

### Invitation Management

#### POST /api/projects/{id}/invite
Send project invitation:

```typescript
// Request
{
  email: "<EMAIL>",
  role: "editor",
  message: "Would love your input on this project!"
}

// Response
{
  invitation: {
    id: "uuid",
    email: "<EMAIL>",
    role: "editor",
    token: "secure_token_here",
    expires_at: "2024-01-22T10:30:00Z",
    invitation_url: "https://bookscribe.ai/invite/secure_token_here"
  }
}
```

#### GET /api/invitations/{token}
Get invitation details:

```typescript
// Response
{
  invitation: {
    id: "uuid",
    project: {
      id: "uuid",
      title: "My Novel",
      description: "A thrilling adventure..."
    },
    inviter: {
      name: "John Doe",
      avatar_url: "https://..."
    },
    role: "editor",
    message: "Would love your input!",
    expires_at: "2024-01-22T10:30:00Z"
  }
}
```

#### POST /api/invitations/{token}/accept
Accept invitation:

```typescript
// Response
{
  success: true,
  project_id: "uuid",
  redirect_url: "/projects/uuid"
}
```

### Collaborator Management

#### GET /api/projects/{id}/collaborators
List project collaborators:

```typescript
// Response
{
  collaborators: [
    {
      id: "uuid",
      user: {
        id: "uuid",
        name: "Jane Smith",
        email: "<EMAIL>",
        avatar_url: "https://..."
      },
      role: "editor",
      status: "active",
      last_accessed_at: "2024-01-15T14:30:00Z",
      invited_by: {
        name: "John Doe"
      },
      permissions: {
        can_edit_chapters: true,
        can_manage_characters: true,
        can_use_ai: true,
        can_export: true
      }
    }
  ],
  pending_invitations: [
    {
      email: "<EMAIL>",
      role: "viewer",
      expires_at: "2024-01-20T10:00:00Z"
    }
  ]
}
```

#### PUT /api/project-collaborators/{id}
Update collaborator role:

```typescript
// Request
{
  role: "admin",
  permissions: {
    can_manage_billing: true
  }
}
```

#### DELETE /api/project-collaborators/{id}
Remove collaborator:

```typescript
// Response
{
  success: true,
  removed_user_id: "uuid"
}
```

### Collaboration Activity

#### GET /api/projects/{id}/activity
Get collaboration activity log:

```typescript
// Response
{
  activities: [
    {
      id: "uuid",
      user: {
        name: "Jane Smith",
        avatar_url: "https://..."
      },
      activity_type: "edit",
      target_type: "chapter",
      target: {
        id: "uuid",
        title: "Chapter 5"
      },
      details: {
        words_added: 500,
        edit_duration: 1800
      },
      created_at: "2024-01-15T10:30:00Z"
    }
  ]
}
```

## Invitation Flow

### 1. Sending Invitations
```typescript
async function sendInvitation(projectId: string, email: string, role: Role) {
  // Validate project ownership
  const canInvite = await userCanInviteToProject(userId, projectId);
  if (!canInvite) throw new ForbiddenError();

  // Check existing collaborator
  const existing = await getCollaborator(projectId, email);
  if (existing) throw new AlreadyCollaboratorError();

  // Generate secure token
  const token = generateSecureToken();
  
  // Create invitation
  const invitation = await createInvitation({
    project_id: projectId,
    email,
    role,
    token,
    inviter_id: userId,
    expires_at: addDays(new Date(), 7)
  });

  // Send email
  await sendInvitationEmail(invitation);
  
  return invitation;
}
```

### 2. Accepting Invitations
```typescript
async function acceptInvitation(token: string, userId: string) {
  // Validate token
  const invitation = await getInvitationByToken(token);
  if (!invitation || invitation.status !== 'pending') {
    throw new InvalidTokenError();
  }

  // Check expiration
  if (invitation.expires_at < new Date()) {
    await updateInvitation(invitation.id, { status: 'expired' });
    throw new ExpiredInvitationError();
  }

  // Create collaborator
  const collaborator = await createCollaborator({
    project_id: invitation.project_id,
    user_id: userId,
    user_email: invitation.email,
    role: invitation.role,
    status: 'accepted',
    invited_by: invitation.inviter_id
  });

  // Update invitation
  await updateInvitation(invitation.id, {
    status: 'accepted',
    user_id: userId,
    accepted_at: new Date()
  });

  // Notify project owner
  await notifyInvitationAccepted(invitation);

  return collaborator;
}
```

## Permission System

### Granular Permissions
```typescript
interface CollaboratorPermissions {
  // Content permissions
  can_view_content: boolean;
  can_edit_chapters: boolean;
  can_create_chapters: boolean;
  can_delete_chapters: boolean;
  
  // Character permissions
  can_view_characters: boolean;
  can_manage_characters: boolean;
  
  // AI permissions
  can_use_ai_generation: boolean;
  can_use_ai_agents: boolean;
  
  // Project permissions
  can_export_project: boolean;
  can_view_analytics: boolean;
  can_manage_settings: boolean;
  can_invite_collaborators: boolean;
  can_manage_billing: boolean;
}
```

### Permission Checking
```typescript
async function checkPermission(
  userId: string, 
  projectId: string, 
  permission: keyof CollaboratorPermissions
): boolean {
  // Check if owner
  const project = await getProject(projectId);
  if (project.user_id === userId) return true;

  // Check collaborator permissions
  const collaborator = await getCollaborator(projectId, userId);
  if (!collaborator || collaborator.status !== 'accepted') return false;

  // Check specific permission
  return collaborator.permissions?.[permission] ?? 
         DEFAULT_PERMISSIONS[collaborator.role][permission];
}
```

## Real-time Collaboration

### Presence System
Show who's currently active:

```typescript
interface CollaboratorPresence {
  user_id: string;
  project_id: string;
  current_chapter?: string;
  cursor_position?: number;
  last_seen: Date;
  status: 'active' | 'idle' | 'offline';
}
```

### Activity Broadcasting
```typescript
// Broadcast collaborator actions
socket.on('chapter:edit', async (data) => {
  // Verify permissions
  if (!await checkPermission(userId, data.project_id, 'can_edit_chapters')) {
    return socket.emit('error', 'Permission denied');
  }

  // Log activity
  await logActivity({
    project_id: data.project_id,
    user_id: userId,
    activity_type: 'edit',
    target_type: 'chapter',
    target_id: data.chapter_id,
    details: data.changes
  });

  // Broadcast to other collaborators
  socket.to(`project:${data.project_id}`).emit('collaborator:edit', {
    user_id: userId,
    chapter_id: data.chapter_id,
    changes: data.changes
  });
});
```

## Security Measures

### Token Security
- Cryptographically secure tokens
- Time-limited validity (7 days default)
- Single-use tokens
- Token rotation on security events

### Access Control
```sql
-- RLS Policy for collaborators
CREATE POLICY "Collaborators can access project" ON projects
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM project_collaborators
      WHERE project_id = projects.id
      AND user_id = auth.uid()
      AND status = 'accepted'
    )
  );
```

### Audit Trail
Track all collaboration actions:
- Who invited whom
- When access was granted/revoked
- What changes were made
- IP addresses and timestamps

## UI Components

### Invitation Modal
```tsx
<InviteCollaboratorModal
  projectId={projectId}
  onInvite={handleInvite}
  existingCollaborators={collaborators}
  remainingSlots={remainingSlots}
/>
```

### Collaborator List
```tsx
<CollaboratorList
  collaborators={collaborators}
  pendingInvitations={pendingInvitations}
  onRoleChange={handleRoleChange}
  onRemove={handleRemove}
  canManage={userCanManage}
/>
```

### Permission Manager
```tsx
<PermissionManager
  collaborator={collaborator}
  onChange={updatePermissions}
  rolePermissions={ROLE_PERMISSIONS}
  customizable={true}
/>
```

### Activity Feed
```tsx
<CollaborationActivityFeed
  projectId={projectId}
  activities={activities}
  realtime={true}
  groupByUser={false}
/>
```

## Notifications

### Invitation Notifications
- Email when invited
- In-app notification on acceptance
- Reminder before expiration
- Notification when removed

### Activity Notifications
- Real-time edit notifications
- Daily activity digests
- Mention notifications
- Comment notifications

## Limits & Quotas

### Collaboration Limits
```typescript
const COLLABORATION_LIMITS = {
  free: {
    max_collaborators: 2,
    max_pending_invitations: 3,
    invitation_expiry_days: 7
  },
  pro: {
    max_collaborators: 10,
    max_pending_invitations: 20,
    invitation_expiry_days: 30
  },
  team: {
    max_collaborators: 50,
    max_pending_invitations: 100,
    invitation_expiry_days: 90
  }
};
```

## Future Enhancements

1. **Advanced Permissions**
   - Time-based access
   - Chapter-specific permissions
   - Custom permission roles
   - Permission templates

2. **Collaboration Features**
   - Suggested edits workflow
   - Version comparison
   - Collaborative outlining
   - Team chat integration

3. **Enterprise Features**
   - SSO integration
   - Team management
   - Centralized billing
   - Usage analytics

4. **External Collaboration**
   - Guest reviewer access
   - Public comment links
   - Beta reader management
   - Editor integration

## Related Systems
- Real-time Collaboration System
- Notification System (collaboration alerts)
- Email System (invitations)
- Project Management (access control)