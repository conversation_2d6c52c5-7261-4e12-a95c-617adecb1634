import { createServerClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import ProductivityPageClient from './page-client'

export default async function ProductivityPage() {
  const supabase = await createServerClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  const { data: projects } = await supabase
    .from('projects')
    .select('id, title')
    .eq('user_id', user.id)
    .eq('status', 'active')
    .order('created_at', { ascending: false })
  
  return <ProductivityPageClient userId={user.id} projects={projects || []} />
}