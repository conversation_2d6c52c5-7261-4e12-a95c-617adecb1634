import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { promisify } from 'util'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'
import {
  validateFileComprehensive,
  sanitizeTextInput
} from '@/lib/file-upload-security'
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

interface ParsedChapter {
  title: string
  content: string
  wordCount: number
  chapterNumber: number
}

// Maximum file size: 15MB (EPUB files can be larger)
const MAX_FILE_SIZE = 15 * 1024 * 1024;

// Validation schema for form data
const epubImportSchema = z.object({
  projectId: baseSchemas.uuid,
  parseMetadata: z.enum(['true', 'false']).transform(val => val === 'true').optional().default('true')
});

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  // Enhanced request validation for file uploads
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'file-upload',
    rateLimitCost: 6, // Higher cost for EPUB processing
    maxBodySize: MAX_FILE_SIZE,
    allowedContentTypes: ['multipart/form-data'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      try {
        const formData = await req.formData();
        const projectId = formData.get('projectId') as string;
        
        if (!projectId) {
          return { valid: false, error: 'Project ID is required' };
        }

        // Validate project ID format
        if (!baseSchemas.uuid.safeParse(projectId).success) {
          return { valid: false, error: 'Invalid project ID format' };
        }

        // Verify user owns the project
        const project = await verifyProjectAccess(projectId, user.id)
        if (!project) {
          return { valid: false, error: PROJECT_ACCESS_ERROR };
        }

        // Check if project is in a valid state for import
        if (project.status === 'archived') {
          return { valid: false, error: 'Cannot import to archived project' };
        }

        return { valid: true };
      } catch (error) {
        return { valid: false, error: 'Invalid form data' };
      }
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    // Validate form data
    const formDataObj = {
      projectId: formData.get('projectId') as string,
      parseMetadata: formData.get('parseMetadata') || 'true'
    };

    const validatedFormData = epubImportSchema.parse(formDataObj);

    if (!file) {
      return UnifiedResponse.error('File is required', 400);
    }

    // Log import attempt
    logger.info('EPUB import initiated', {
      userId: user.id,
      projectId: validatedFormData.projectId,
      fileName: file.name,
      fileSize: file.size,
      parseMetadata: validatedFormData.parseMetadata,
      clientIP: context.clientIP
    });

    // Validate file type and security
    const fileValidation = await validateFileComprehensive(file);
    if (!fileValidation.isValid) {
      logger.warn('File validation failed', {
        userId: user.id,
        fileName: file.name,
        error: fileValidation.error,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error(fileValidation.error || 'Invalid file', 400);
    }

    // Enhanced security validation
    if (!file.name.toLowerCase().endsWith('.epub')) {
      return UnifiedResponse.error('File must be an EPUB document', 400);
    }

    // Ensure it's an EPUB file
    const validMimeTypes = ['application/epub+zip', 'application/octet-stream', 'application/zip'];
    
    if (file.type && !validMimeTypes.includes(file.type)) {
      logger.warn('Invalid file type for EPUB import', {
        userId: user.id,
        fileName: file.name,
        mimeType: file.type,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('File must be an EPUB document', 400);
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return UnifiedResponse.error(`File size exceeds maximum limit of ${MAX_FILE_SIZE / 1024 / 1024}MB`, 400);
    }

    // Convert file to buffer and save temporarily
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    
    // Create temporary file path
    const tempPath = `/tmp/${Date.now()}_${file.name}`
    const fs = await import('fs').then(m => m.promises)
    await fs.writeFile(tempPath, buffer)

    try {
      // Parse EPUB (server-side only)
      if (typeof window !== 'undefined') {
        throw new Error('EPUB parsing is only available on server-side')
      }

      logger.info(`Starting EPUB parsing for file: ${file.name}, size: ${file.size} bytes`)

      const epub = (await import('epub')).default
      const epubBook = new epub(tempPath)

      // Add timeout for parsing to prevent hanging
      const parsePromise = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('EPUB parsing timeout after 30 seconds'))
        }, 30000)

        epubBook.on('end', () => {
          clearTimeout(timeout)
          resolve(undefined)
        })
        epubBook.on('error', (err) => {
          clearTimeout(timeout)
          reject(err)
        })
        epubBook.parse()
      })

      await parsePromise
      logger.info(`EPUB parsing completed successfully for: ${file.name}`)

      // Extract chapters
      const chapters: ParsedChapter[] = [];
      let chapterNumber = 0;

      // Get chapter list from spine
      for (const spineItem of epubBook.flow) {
        chapterNumber++;
        
        // Get chapter content
        const getChapter = promisify(epubBook.getChapter.bind(epubBook));
        const chapterHtml = await getChapter(spineItem.id);
        
        // Extract text from HTML
        const text = extractTextFromHtml(chapterHtml || '');
        
        // Check for suspicious content
        if (RequestValidationMiddleware['detectMaliciousContent'](text)) {
          logger.warn('Malicious content detected in EPUB chapter', {
            userId: user.id,
            fileName: file.name,
            chapterNumber,
            clientIP: context.clientIP
          });
          // Skip this chapter
          continue;
        }
        
        // Get title from TOC or use default
        const tocItem = epubBook.toc.find((item: { href: string; title: string }) => item.href === spineItem.href);
        const title = sanitizeTextInput(tocItem?.title || `Chapter ${chapterNumber}`, 100);
        
        if (text.trim()) {
          chapters.push({
            title,
            content: sanitizeTextInput(text, 1000000), // 1MB max per chapter
            wordCount: text.split(/\s+/).filter((word: string) => word.length > 0).length,
            chapterNumber
          });
        }
      }

      // Clean up temp file
      await fs.unlink(tempPath);

      if (chapters.length === 0) {
        return UnifiedResponse.error('No valid chapters found in EPUB file', 400);
      }

      // Create chapters in database
      const supabase = await createTypedServerClient();
      const createdChapters = [];
      const errors = [];

      for (const chapter of chapters) {
        try {
          const { data, error } = await supabase
            .from('chapters')
            .insert({
              project_id: validatedFormData.projectId,
              chapter_number: chapter.chapterNumber,
              title: chapter.title,
              content: chapter.content,
              actual_word_count: chapter.wordCount,
              status: 'imported',
              created_by: user.id,
              metadata: {
                source: 'epub',
                originalFileName: file.name
              }
            })
            .select()
            .single();

          if (error) {
            logger.error('Error creating chapter:', error, {
              userId: user.id,
              projectId: validatedFormData.projectId,
              chapterNumber: chapter.chapterNumber
            });
            errors.push(`Chapter ${chapter.chapterNumber}: ${error.message}`);
            continue;
          }

          createdChapters.push(data);
        } catch (error) {
          logger.error('Unexpected error creating chapter:', error, {
            userId: user.id,
            projectId: validatedFormData.projectId,
            chapterNumber: chapter.chapterNumber
          });
          errors.push(`Chapter ${chapter.chapterNumber}: Failed to create`);
        }
      }

      // Update project metadata
      const totalWordCount = chapters.reduce((sum, ch) => sum + ch.wordCount, 0);
      
      // Get book metadata if requested
      let metadata = {};
      if (validatedFormData.parseMetadata) {
        metadata = {
          title: sanitizeTextInput(epubBook.metadata.title || '', 200),
          author: sanitizeTextInput(epubBook.metadata.creator || '', 200),
          publisher: sanitizeTextInput(epubBook.metadata.publisher || '', 200),
          language: sanitizeTextInput(epubBook.metadata.language || '', 50),
          description: sanitizeTextInput(epubBook.metadata.description || '', 1000),
          importedFrom: 'epub',
          originalFileName: file.name
        };
      }

      const { error: updateError } = await supabase
        .from('projects')
        .update({
          word_count: totalWordCount,
          chapters_count: chapters.length,
          metadata: metadata,
          updated_at: new Date().toISOString()
        })
        .eq('id', validatedFormData.projectId);

      if (updateError) {
        logger.error('Error updating project metadata:', updateError, {
          userId: user.id,
          projectId: validatedFormData.projectId
        });
      }

      logger.info('EPUB import completed', {
        userId: user.id,
        projectId: validatedFormData.projectId,
        chaptersImported: createdChapters.length,
        totalChapters: chapters.length,
        totalWordCount,
        hasMetadata: !!metadata,
        errors: errors.length,
        clientIP: context.clientIP
      });

      return UnifiedResponse.success({
        chaptersImported: createdChapters.length,
        totalWordCount,
        metadata,
        chapters: createdChapters.map(ch => ({
          id: ch.id,
          title: ch.title,
          chapterNumber: ch.chapter_number,
          wordCount: ch.actual_word_count
        })),
        errors: errors.length > 0 ? errors : undefined
      });

    } catch (parseError) {
      // Clean up temp file on error
      await fs.unlink(tempPath).catch(() => {})

      logger.error('EPUB parsing error:', parseError)

      // Provide user-friendly error messages
      if (parseError instanceof Error) {
        if (parseError.message.includes('timeout')) {
          return UnifiedResponse.error('EPUB parsing timed out', 408)
        }
        if (parseError.message.includes('Invalid')) {
          return UnifiedResponse.error('Invalid EPUB file format', 400)
        }
      }

      return UnifiedResponse.error('Failed to parse EPUB file', 400)
    }

  } catch (error) {
    logger.error('EPUB import error:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    
    return UnifiedResponse.error('Failed to import EPUB file');
  }
});

function extractTextFromHtml(html: string): string {
  // Remove HTML tags and decode entities
  const text = html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
    .replace(/<[^>]+>/g, ' ') // Remove HTML tags
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()

  return text
}