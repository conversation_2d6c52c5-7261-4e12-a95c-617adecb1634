import { jsPD<PERSON> } from 'jspdf';
import { logger } from '@/lib/services/logger';
import { watermarkService } from '@/lib/services/watermark-service';

import * as docx from 'docx';
import JSZip from 'jszip';

// Database client will be imported at runtime
import type { Chapter, Character, Project, StoryBible } from '@/lib/db/types';
import { canExportPremiumFormats } from '@/lib/subscription';
import type { UserSubscription } from '@/lib/subscription';

// Extended project interface for export purposes
interface ExportProject extends Project {
  author_name?: string
  isbn?: string
  publisher?: string
  language?: string
  cover_image_url?: string
  dedication?: string
  acknowledgments?: string
  author_bio?: string
}

// Database client interface
interface DatabaseClient {
  projects: {
    getById: (id: string) => Promise<Project | null>;
  };
  chapters: {
    getAll: (projectId: string) => Promise<Chapter[]>;
  };
  characters: {
    getAll: (projectId: string) => Promise<Character[]>;
  };
  storyBible: {
    getAll: (projectId: string) => Promise<StoryBible[]>;
  };
}

let db: DatabaseClient | null = null;

export interface ExportOptions {
  format: 'pdf' | 'epub' | 'docx' | 'txt' | 'markdown';
  includeMetadata: boolean;
  includeFrontMatter: boolean;
  includeChapterBreaks: boolean;
  includeTableOfContents: boolean;
  customStyling?: {
    fontFamily?: string;
    fontSize?: number;
    lineSpacing?: number;
    margins?: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
  };
  pageFormat?: 'letter' | 'a4' | 'custom' | 'trade';
  chapterSelection?: string[]; // Chapter IDs to include
  subscription?: UserSubscription | null; // User's subscription for permission checking
  userTierId?: string; // Alternative to full subscription object
  preset?: ExportPreset; // Export preset configuration
  includePageNumbers?: boolean;
  includeHeaders?: boolean;
  includeFooters?: boolean;
  headerText?: string;
  footerText?: string;
  sceneBreakSymbol?: string;
  chapterTitleFormat?: 'uppercase' | 'capitalize' | 'normal';
  progressCallback?: (progress: number, message: string) => void;
}

export interface ExportPreset {
  id: string;
  name: string;
  format: 'pdf' | 'epub' | 'docx' | 'txt' | 'markdown';
  settings: Partial<ExportOptions>;
}

export interface ExportSettings {
  theme?: string;
  fontSize?: number;
  fontFamily?: string;
  lineHeight?: number;
  paragraphSpacing?: number;
  chapterTitleStyle?: 'uppercase' | 'capitalize' | 'normal';
  includePageNumbers?: boolean;
  includeHeaders?: boolean;
  includeFooters?: boolean;
  headerText?: string;
  footerText?: string;
}

// Export presets for common use cases
export const EXPORT_PRESETS: Record<string, ExportPreset> = {
  manuscript: {
    id: 'manuscript',
    name: 'Manuscript (Standard Submission)',
    format: 'docx',
    settings: {
      includeMetadata: true,
      includeFrontMatter: true,
      includeChapterBreaks: true,
      includeTableOfContents: false,
      includePageNumbers: true,
      includeHeaders: true,
      chapterTitleFormat: 'uppercase',
      customStyling: {
        fontFamily: 'Times New Roman',
        fontSize: 12,
        lineSpacing: 2,
        margins: { top: 1, bottom: 1, left: 1, right: 1 }
      },
      pageFormat: 'letter'
    }
  },
  ebook: {
    id: 'ebook',
    name: 'E-book (Digital Publishing)',
    format: 'epub',
    settings: {
      includeMetadata: true,
      includeFrontMatter: true,
      includeChapterBreaks: true,
      includeTableOfContents: true,
      sceneBreakSymbol: '* * *',
      customStyling: {
        fontFamily: 'Georgia',
        fontSize: 14,
        lineSpacing: 1.5,
        margins: { top: 0.5, bottom: 0.5, left: 0.5, right: 0.5 }
      }
    }
  },
  printReady: {
    id: 'printReady',
    name: 'Print-Ready (POD)',
    format: 'pdf',
    settings: {
      includeMetadata: false,
      includeFrontMatter: true,
      includeChapterBreaks: true,
      includeTableOfContents: true,
      includePageNumbers: true,
      includeHeaders: false,
      includeFooters: true,
      chapterTitleFormat: 'capitalize',
      customStyling: {
        fontFamily: 'Garamond',
        fontSize: 11,
        lineSpacing: 1.4,
        margins: { top: 0.75, bottom: 0.75, left: 0.625, right: 0.625 }
      },
      pageFormat: 'trade'
    }
  }
}

export interface ProjectExportData {
  project: ExportProject;
  chapters: Chapter[];
  characters: Character[];
  storyBible: StoryBible[];
  settings: ExportSettings;
}

class ExportService {
  async exportProject(projectId: string, options: ExportOptions): Promise<Blob> {
    const tierId = options.userTierId || options.subscription?.tierId || 'starter';
    
    // Check export permissions based on tier
    if (tierId === 'starter') {
      // Starter tier can only export PDF with watermark
      if (options.format !== 'pdf') {
        throw new Error(`The Starter plan only supports PDF export. Please upgrade to export in ${options.format.toUpperCase()} format.`);
      }
    } else if (options.format === 'epub') {
      // EPUB requires Professional tier or higher
      const epubAllowedTiers = ['professional', 'studio'];
      if (!epubAllowedTiers.includes(tierId)) {
        throw new Error(`EPUB export requires Professional tier or higher. Current tier: ${tierId}`);
      }
    }
    
    // Fetch all project data
    const exportData = await this.gatherProjectData(projectId, options);
    
    switch (options.format) {
      case 'pdf':
        return await this.exportToPDF(exportData, options);
      case 'docx':
        return await this.exportToDocx(exportData, options);
      case 'epub':
        return await this.exportToEpub(exportData, options);
      case 'txt':
        return await this.exportToText(exportData, options);
      case 'markdown':
        return await this.exportToMarkdown(exportData, options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  private async gatherProjectData(projectId: string, options: ExportOptions): Promise<ProjectExportData> {
    if (!db) {
      try {
        const { db: dbClient } = await import('../db/client');
        db = dbClient;
      } catch {
        throw new Error('Database client not available in this context');
      }
    }

    try {
      const [project, allChapters, characters, storyBible] = await Promise.all([
        db.projects.getById(projectId),
        db.chapters.getAll(projectId),
        db.characters.getAll(projectId),
        db.storyBible.getAll(projectId)
      ]);

      if (!project) {
        throw new Error(`Project with ID ${projectId} not found`);
      }

      // Filter chapters if specific selection is provided
      const chapters = options.chapterSelection 
        ? allChapters.filter((ch: Chapter) => options.chapterSelection!.includes(ch.id))
        : allChapters || [];

      // Sort chapters by chapter number
      chapters.sort((a: Chapter, b: Chapter) => a.chapter_number - b.chapter_number);

      return {
        project,
        chapters,
        characters: characters || [],
        storyBible: storyBible || [],
        settings: {}
      };
    } catch (error) {
      logger.error('Error gathering project data:', error);
      throw new Error(`Failed to gather project data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async exportToPDF(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    // Handle different page formats
    let format: [number, number] | 'a4' | 'letter' = 'letter';
    if (options.pageFormat === 'a4') {
      format = 'a4';
    } else if (options.pageFormat === 'trade') {
      // Trade paperback: 6" x 9" (152.4mm x 228.6mm)
      format = [152.4, 228.6];
    }
    
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: format
    });

    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margins = options.customStyling?.margins || { top: 20, bottom: 20, left: 20, right: 20 };
    const contentWidth = pageWidth - margins.left - margins.right;

    let yPosition = margins.top;
    let currentPage = 1;
    let totalPages = 0; // Will be calculated in a first pass if needed
    
    // Helper function to add headers and footers
    const addHeaderFooter = (pageNum: number) => {
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      
      // Header
      if (options.includeHeaders) {
        const headerText = options.headerText || data.project.title;
        pdf.text(headerText, margins.left, 10, { maxWidth: contentWidth });
        // Add a line under header
        pdf.setDrawColor(200, 200, 200);
        pdf.line(margins.left, 12, pageWidth - margins.right, 12);
      }
      
      // Footer with page numbers
      if (options.includeFooters || options.includePageNumbers) {
        const footerY = pageHeight - 10;
        
        if (options.includePageNumbers) {
          const pageText = `${pageNum}`;
          const pageTextWidth = pdf.getTextWidth(pageText);
          pdf.text(pageText, (pageWidth - pageTextWidth) / 2, footerY);
        }
        
        if (options.footerText) {
          pdf.text(options.footerText, margins.left, footerY, { maxWidth: contentWidth / 2 });
        }
      }
    }

    // Title Page
    if (options.includeFrontMatter) {
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      const titleLines = pdf.splitTextToSize(data.project.title, contentWidth);
      pdf.text(titleLines, margins.left, yPosition);
      yPosition += titleLines.length * 10;

      if (data.project.description) {
        yPosition += 10;
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        const descLines = pdf.splitTextToSize(data.project.description, contentWidth);
        pdf.text(descLines, margins.left, yPosition);
        yPosition += descLines.length * 6;
      }

      // Metadata
      if (options.includeMetadata) {
        yPosition += 20;
        pdf.setFontSize(10);
        pdf.text(`Genre: ${data.project.primary_genre || 'Not specified'}`, margins.left, yPosition);
        yPosition += 6;
        pdf.text(`Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words`, margins.left, yPosition);
        yPosition += 6;
        pdf.text(`Chapters: ${data.chapters.length}`, margins.left, yPosition);
        yPosition += 6;
        pdf.text(`Created: ${new Date(data.project.created_at).toLocaleDateString()}`, margins.left, yPosition);
      }

      pdf.addPage();
      yPosition = margins.top;
    }

    // Table of Contents
    if (options.includeTableOfContents && data.chapters.length > 0) {
      pdf.setFontSize(18);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Table of Contents', margins.left, yPosition);
      yPosition += 15;

      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'normal');
      
      data.chapters.forEach((chapter) => {
        const title = chapter.title || `Chapter ${chapter.chapter_number}`;
        pdf.text(`${chapter.chapter_number}. ${title}`, margins.left + 5, yPosition);
        yPosition += 8;

        if (yPosition > pageHeight - margins.bottom) {
          pdf.addPage();
          yPosition = margins.top;
        }
      });

      pdf.addPage();
      yPosition = margins.top;
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      if (options.includeChapterBreaks && index > 0) {
        pdf.addPage();
        currentPage++;
        addHeaderFooter(currentPage);
        yPosition = margins.top + (options.includeHeaders ? 15 : 0);
      }

      // Chapter Title
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      let chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      
      // Apply chapter title format
      if (options.chapterTitleFormat === 'uppercase') {
        chapterTitle = chapterTitle.toUpperCase();
      } else if (options.chapterTitleFormat === 'capitalize') {
        chapterTitle = chapterTitle.split(' ').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
      }
      
      pdf.text(chapterTitle, margins.left, yPosition);
      yPosition += 20;

      // Chapter Content
      if (chapter.content) {
        pdf.setFontSize(options.customStyling?.fontSize || 11);
        pdf.setFont(options.customStyling?.fontFamily || 'helvetica', 'normal');
        
        // Process content for scene breaks
        const processedContent = this.processContentForPDF(chapter.content, options);
        const paragraphs = processedContent.split('\n\n').filter(p => p.trim());
        const lineHeight = (options.customStyling?.fontSize || 11) * (options.customStyling?.lineSpacing || 1.5) * 0.3527; // Convert to mm

        paragraphs.forEach((paragraph, pIndex) => {
          // Handle scene breaks
          if (paragraph.trim() === '***' || paragraph.trim() === '* * *' || paragraph.trim() === '---') {
            if (yPosition > pageHeight - margins.bottom - 20) {
              pdf.addPage();
              currentPage++;
              addHeaderFooter(currentPage);
              yPosition = margins.top + (options.includeHeaders ? 15 : 0);
            }
            
            // Draw scene break
            const sceneBreak = options.sceneBreakSymbol || '* * *';
            const breakWidth = pdf.getTextWidth(sceneBreak);
            pdf.text(sceneBreak, (pageWidth - breakWidth) / 2, yPosition + 5);
            yPosition += 15;
          } else {
            // Regular paragraph
            const lines = pdf.splitTextToSize(paragraph, contentWidth) as string[];
            
            lines.forEach((line: string, lineIndex: number) => {
              if (yPosition > pageHeight - margins.bottom - lineHeight - (options.includeFooters ? 15 : 0)) {
                pdf.addPage();
                currentPage++;
                addHeaderFooter(currentPage);
                yPosition = margins.top + (options.includeHeaders ? 15 : 0);
              }

              // First line indent (except for first paragraph after chapter title)
              const indent = (lineIndex === 0 && pIndex > 0) ? 5 : 0;
              pdf.text(line, margins.left + indent, yPosition);
              yPosition += lineHeight;
            });
            
            // Paragraph spacing
            yPosition += lineHeight * 0.5;
          }
        });
        
        // Report progress if callback provided
        if (options.progressCallback) {
          const progress = ((index + 1) / data.chapters.length) * 100;
          options.progressCallback(progress, `Processing chapter ${index + 1} of ${data.chapters.length}`);
        }
      }

      yPosition += 10; // Space after chapter
    });
    
    // Add header/footer to first page if needed
    if (options.includeHeaders || options.includeFooters || options.includePageNumbers) {
      const pageCount = pdf.internal.pages.length - 1; // Subtract 1 because pages array includes an empty first element
      for (let i = 1; i <= pageCount; i++) {
        pdf.setPage(i);
        addHeaderFooter(i);
      }
    }

    // Get PDF as ArrayBuffer for watermarking
    const pdfArrayBuffer = pdf.output('arraybuffer');
    
    // Apply watermark if user is on starter tier
    const tierId = options.userTierId || options.subscription?.tierId || 'starter';
    const finalPdfBuffer = await watermarkService.applyWatermarkIfRequired(pdfArrayBuffer, tierId);
    
    return new Blob([finalPdfBuffer], { type: 'application/pdf' });
  }

  private async exportToDocx(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    const sections: docx.ISectionOptions[] = [];
    
    // Apply preset if provided
    if (options.preset) {
      Object.assign(options, options.preset.settings);
    }
    
    // Convert margins to twips
    const margins = options.customStyling?.margins || { top: 1, bottom: 1, left: 1, right: 1 };
    const marginTwips = {
      top: docx.convertInchesToTwip(margins.top),
      bottom: docx.convertInchesToTwip(margins.bottom),
      left: docx.convertInchesToTwip(margins.left),
      right: docx.convertInchesToTwip(margins.right),
    };

    // Title page section
    if (options.includeFrontMatter) {
      sections.push({
        properties: {
          page: {
            margin: marginTwips,
          },
        },
        children: [
          new docx.Paragraph({
            children: [
              new docx.TextRun({
                text: data.project.title,
                size: 48,
                bold: true,
              }),
            ],
            alignment: docx.AlignmentType.CENTER,
            spacing: { after: 400 },
          }),
          ...(data.project.description ? [
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: data.project.description,
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.CENTER,
              spacing: { after: 400 },
            }),
          ] : []),
          ...(options.includeMetadata ? [
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: `Genre: ${data.project.primary_genre || 'Not specified'}`,
                  size: 20,
                }),
              ],
              spacing: { after: 200 },
            }),
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: `Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words`,
                  size: 20,
                }),
              ],
              spacing: { after: 200 },
            }),
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: `Created: ${new Date(data.project.created_at).toLocaleDateString()}`,
                  size: 20,
                }),
              ],
              spacing: { after: 200 },
            }),
          ] : []),
        ],
      });
    }

    // Table of Contents
    if (options.includeTableOfContents) {
      const tocChildren = [
        new docx.Paragraph({
          children: [
            new docx.TextRun({
              text: 'Table of Contents',
              size: 32,
              bold: true,
            }),
          ],
          spacing: { after: 400 },
        }),
        ...data.chapters.map(chapter => 
          new docx.Paragraph({
            children: [
              new docx.TextRun({
                text: `${chapter.chapter_number}. ${chapter.title || `Chapter ${chapter.chapter_number}`}`,
                size: 22,
              }),
            ],
            spacing: { after: 200 },
          })
        ),
      ];

      sections.push({
        properties: {
          page: {
            margin: {
              top: docx.convertInchesToTwip(1),
              bottom: docx.convertInchesToTwip(1),
              left: docx.convertInchesToTwip(1),
              right: docx.convertInchesToTwip(1),
            },
          },
        },
        children: tocChildren,
      });
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      const chapterChildren: docx.Paragraph[] = [];
      
      // Chapter title
      let chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      
      // Apply chapter title format
      if (options.chapterTitleFormat === 'uppercase') {
        chapterTitle = chapterTitle.toUpperCase();
      } else if (options.chapterTitleFormat === 'capitalize') {
        chapterTitle = chapterTitle.split(' ').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
      }
      
      chapterChildren.push(
        new docx.Paragraph({
          children: [
            new docx.TextRun({
              text: chapterTitle,
              size: 32,
              bold: true,
              font: options.customStyling?.fontFamily,
            }),
          ],
          spacing: { after: 400 },
          pageBreakBefore: options.includeChapterBreaks && index > 0,
          alignment: docx.AlignmentType.CENTER,
        })
      );

      if (chapter.content) {
        // Process content for scene breaks
        const processedContent = this.processContentForDocx(chapter.content, options);
        const paragraphs = processedContent.split('\n\n').filter(p => p.trim());
        
        paragraphs.forEach((paragraphText, pIndex) => {
          // Handle scene breaks
          if (paragraphText.trim() === '***' || paragraphText.trim() === '* * *' || paragraphText.trim() === '---') {
            chapterChildren.push(
              new docx.Paragraph({
                children: [
                  new docx.TextRun({
                    text: options.sceneBreakSymbol || '* * *',
                    size: (options.customStyling?.fontSize || 12) * 2,
                  }),
                ],
                alignment: docx.AlignmentType.CENTER,
                spacing: { before: 240, after: 240 },
              })
            );
          } else {
            // Regular paragraph with first-line indent
            chapterChildren.push(
              new docx.Paragraph({
                children: [
                  new docx.TextRun({
                    text: paragraphText.trim(),
                    size: (options.customStyling?.fontSize || 12) * 2, // Word uses half-points
                    font: options.customStyling?.fontFamily,
                  }),
                ],
                spacing: { 
                  after: 240,
                  line: options.customStyling?.lineSpacing ? options.customStyling.lineSpacing * 240 : 480,
                },
                indent: {
                  firstLine: pIndex > 0 ? docx.convertInchesToTwip(0.5) : 0, // First line indent except first paragraph
                },
              })
            );
          }
        });
        
        // Report progress if callback provided
        if (options.progressCallback) {
          const progress = ((index + 1) / data.chapters.length) * 100;
          options.progressCallback(progress, `Processing chapter ${index + 1} of ${data.chapters.length}`);
        }
      }

      // Create section with headers/footers if requested
      const sectionOptions: docx.ISectionOptions = {
        properties: {
          page: {
            margin: marginTwips,
          },
        },
        children: chapterChildren,
      };
      
      // Add headers if requested
      if (options.includeHeaders || options.headerText) {
        sectionOptions.headers = {
          default: new docx.Header({
            children: [
              new docx.Paragraph({
                children: [
                  new docx.TextRun({
                    text: options.headerText || data.project.title,
                    size: 20,
                  }),
                ],
                alignment: docx.AlignmentType.CENTER,
              }),
            ],
          }),
        };
      }
      
      // Add footers with page numbers if requested
      if (options.includeFooters || options.includePageNumbers) {
        sectionOptions.footers = {
          default: new docx.Footer({
            children: [
              new docx.Paragraph({
                children: [
                  ...(options.footerText ? [
                    new docx.TextRun({
                      text: options.footerText,
                      size: 20,
                    }),
                    new docx.TextRun({
                      text: '\t',
                    }),
                  ] : []),
                  ...(options.includePageNumbers ? [
                    new docx.TextRun({
                      children: [docx.PageNumber.CURRENT],
                      size: 20,
                    }),
                  ] : []),
                ],
                alignment: options.includePageNumbers ? docx.AlignmentType.CENTER : docx.AlignmentType.LEFT,
              }),
            ],
          }),
        };
      }
      
      sections.push(sectionOptions);
    });

    // Create document with all sections
    const doc = new docx.Document({
      creator: 'BookScribe AI',
      title: data.project.title,
      description: data.project.description || '',
      sections: sections
    });

    return await docx.Packer.toBlob(doc);
  }

  private async exportToEpub(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    const zip = new JSZip();
    
    // Apply preset if provided
    if (options.preset) {
      Object.assign(options, options.preset.settings);
    }
    
    // EPUB structure
    zip.file('mimetype', 'application/epub+zip');
    
    const metaInf = zip.folder('META-INF');
    metaInf!.file('container.xml', `<?xml version="1.0" encoding="UTF-8"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="OEBPS/content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>`);

    const oebps = zip.folder('OEBPS');
    const images = oebps!.folder('images');
    
    // Track progress
    let processedChapters = 0;
    const totalSteps = data.chapters.length + 10; // chapters + metadata files
    
    // Package file
    const packageContent = this.generateEpubPackage(data, options);
    oebps!.file('content.opf', packageContent);
    
    // NCX file for backwards compatibility
    const ncxContent = this.generateEpubNCX(data, options);
    oebps!.file('toc.ncx', ncxContent);
    
    // Navigation
    const navContent = this.generateEpubNavigation(data, options);
    oebps!.file('nav.xhtml', navContent);
    
    // Cover page
    if (data.project.cover_image_url) {
      const coverContent = this.generateEpubCoverPage(data);
      oebps!.file('cover.xhtml', coverContent);
      
      try {
        // Fetch cover image from URL
        const response = await fetch(data.project.cover_image_url);
        if (response.ok) {
          const blob = await response.blob();
          const extension = data.project.cover_image_url.split('.').pop() || 'jpg';
          images!.file(`cover.${extension}`, blob);
        }
      } catch (error) {
        logger.error('Failed to fetch cover image:', error);
      }
    }
    
    // Title page
    if (options.includeFrontMatter) {
      const titleContent = this.generateEpubTitlePage(data, options);
      oebps!.file('title.xhtml', titleContent);
      
      // Copyright page
      const copyrightContent = this.generateEpubCopyrightPage(data);
      oebps!.file('copyright.xhtml', copyrightContent);
      
      // Dedication page
      if (data.project.dedication) {
        const dedicationContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Dedication</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="dedication">
    <p>${this.escapeXml(data.project.dedication)}</p>
  </div>
</body>
</html>`;
        oebps!.file('dedication.xhtml', dedicationContent);
      }
    }
    
    // Table of Contents
    if (options.includeTableOfContents) {
      const tocContent = this.generateEpubTableOfContents(data);
      oebps!.file('toc.xhtml', tocContent);
    }
    
    // Chapters with enhanced content processing
    data.chapters.forEach((chapter, index) => {
      let chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      
      // Apply chapter title format
      if (options.chapterTitleFormat === 'uppercase') {
        chapterTitle = chapterTitle.toUpperCase();
      } else if (options.chapterTitleFormat === 'capitalize') {
        chapterTitle = chapterTitle.split(' ').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
      }
      
      const processedContent = chapter.content ? this.processChapterContent(chapter.content, options) : '<p>Content coming soon...</p>';
      const chapterContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${this.escapeXml(chapterTitle)}</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="chapter">
    <h1 class="chapter-title">${this.escapeXml(chapterTitle)}</h1>
    <div class="content">
      ${processedContent}
    </div>
  </div>
</body>
</html>`;
      oebps!.file(`chapter-${chapter.chapter_number}.xhtml`, chapterContent);
      
      processedChapters++;
      if (options.progressCallback) {
        const progress = (processedChapters / totalSteps) * 100;
        options.progressCallback(progress, `Processing chapter ${index + 1} of ${data.chapters.length}`);
      }
    });
    
    // Acknowledgments
    if (data.project.acknowledgments) {
      const acknowledgmentsContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Acknowledgments</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="acknowledgments">
    <h1>Acknowledgments</h1>
    <p>${this.escapeXml(data.project.acknowledgments)}</p>
  </div>
</body>
</html>`;
      oebps!.file('acknowledgments.xhtml', acknowledgmentsContent);
    }
    
    // Author bio
    if (data.project.author_bio) {
      const authorContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>About the Author</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="author-bio">
    <h1>About the Author</h1>
    <p>${this.escapeXml(data.project.author_bio)}</p>
  </div>
</body>
</html>`;
      oebps!.file('author.xhtml', authorContent);
    }
    
    // CSS
    const cssContent = this.generateEpubCSS(options);
    oebps!.file('styles.css', cssContent);

    return await zip.generateAsync({ type: 'blob' });
  }

  private async exportToText(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    let content = '';

    // Title and metadata
    if (options.includeFrontMatter) {
      content += `${data.project.title}\n`;
      content += '='.repeat(data.project.title.length) + '\n\n';
      
      if (data.project.description) {
        content += `${data.project.description}\n\n`;
      }

      if (options.includeMetadata) {
        content += `Genre: ${data.project.primary_genre || 'Not specified'}\n`;
        content += `Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words\n`;
        content += `Chapters: ${data.chapters.length}\n`;
        content += `Created: ${new Date(data.project.created_at).toLocaleDateString()}\n\n`;
      }
    }

    // Table of contents
    if (options.includeTableOfContents && data.chapters.length > 0) {
      content += 'Table of Contents\n';
      content += '-'.repeat(17) + '\n\n';
      
      data.chapters.forEach(chapter => {
        const title = chapter.title || `Chapter ${chapter.chapter_number}`;
        content += `${chapter.chapter_number}. ${title}\n`;
      });
      content += '\n\n';
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      if (options.includeChapterBreaks && index > 0) {
        content += '\n' + '='.repeat(50) + '\n\n';
      }

      const chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      content += `${chapterTitle}\n`;
      content += '-'.repeat(chapterTitle.length) + '\n\n';

      if (chapter.content) {
        content += chapter.content + '\n\n';
      }
    });

    return new Blob([content], { type: 'text/plain;charset=utf-8' });
  }

  private async exportToMarkdown(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    let content = '';

    // Title and metadata
    if (options.includeFrontMatter) {
      content += `# ${data.project.title}\n\n`;
      
      if (data.project.description) {
        content += `${data.project.description}\n\n`;
      }

      if (options.includeMetadata) {
        content += `**Genre:** ${data.project.primary_genre || 'Not specified'}  \n`;
        content += `**Word Count:** ${data.project.current_word_count?.toLocaleString() || '0'} words  \n`;
        content += `**Chapters:** ${data.chapters.length}  \n`;
        content += `**Created:** ${new Date(data.project.created_at).toLocaleDateString()}  \n\n`;
      }
    }

    // Table of contents
    if (options.includeTableOfContents && data.chapters.length > 0) {
      content += '## Table of Contents\n\n';
      
      data.chapters.forEach(chapter => {
        const title = chapter.title || `Chapter ${chapter.chapter_number}`;
        const anchor = title.toLowerCase().replace(/[^a-z0-9]+/g, '-');
        content += `${chapter.chapter_number}. [${title}](#${anchor})\n`;
      });
      content += '\n';
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      if (options.includeChapterBreaks && index > 0) {
        content += '\n---\n\n';
      }

      const chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      content += `## ${chapterTitle}\n\n`;

      if (chapter.content) {
        content += chapter.content + '\n\n';
      }
    });

    return new Blob([content], { type: 'text/markdown;charset=utf-8' });
  }

  // EPUB helper methods
  private generateEpubPackage(data: ProjectExportData, options: ExportOptions): string {
    const manifestItems = [
      '<item id="nav" href="nav.xhtml" media-type="application/xhtml+xml" properties="nav"/>',
      '<item id="ncx" href="toc.ncx" media-type="application/x-dtbncx+xml"/>',
      '<item id="css" href="styles.css" media-type="text/css"/>',
    ];

    const spineItems = [];

    // Add cover page if exists
    if (data.project.cover_image_url) {
      manifestItems.push('<item id="cover" href="cover.xhtml" media-type="application/xhtml+xml"/>');
      manifestItems.push('<item id="cover-image" href="images/cover.jpg" media-type="image/jpeg" properties="cover-image"/>');
      spineItems.push('<itemref idref="cover"/>');
    }

    if (options.includeFrontMatter) {
      manifestItems.push('<item id="title" href="title.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="title"/>');
      
      // Add dedication page if exists
      if (data.project.dedication) {
        manifestItems.push('<item id="dedication" href="dedication.xhtml" media-type="application/xhtml+xml"/>');
        spineItems.push('<itemref idref="dedication"/>');
      }
      
      // Add copyright page
      manifestItems.push('<item id="copyright" href="copyright.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="copyright"/>');
    }

    // Add table of contents
    if (options.includeTableOfContents) {
      manifestItems.push('<item id="toc" href="toc.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="toc"/>');
    }

    data.chapters.forEach(chapter => {
      manifestItems.push(`<item id="chapter-${chapter.chapter_number}" href="chapter-${chapter.chapter_number}.xhtml" media-type="application/xhtml+xml"/>`);
      spineItems.push(`<itemref idref="chapter-${chapter.chapter_number}"/>`);
    });

    // Add acknowledgments if exists
    if (data.project.acknowledgments) {
      manifestItems.push('<item id="acknowledgments" href="acknowledgments.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="acknowledgments"/>');
    }

    // Add author bio if exists
    if (data.project.author_bio) {
      manifestItems.push('<item id="author" href="author.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="author"/>');
    }

    return `<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" version="3.0" unique-identifier="uid">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:identifier id="uid">${data.project.id}</dc:identifier>
    <dc:title>${data.project.title}</dc:title>
    <dc:language>${data.project.language || 'en'}</dc:language>
    <dc:creator>${data.project.author_name || 'Unknown Author'}</dc:creator>
    <dc:publisher>${data.project.publisher || 'BookScribe AI'}</dc:publisher>
    <dc:date>${new Date().toISOString().split('T')[0]}</dc:date>
    ${data.project.description ? `<dc:description>${this.escapeXml(data.project.description)}</dc:description>` : ''}
    ${data.project.primary_genre ? `<dc:subject>${data.project.primary_genre}</dc:subject>` : ''}
    ${data.project.subgenre ? `<dc:subject>${data.project.subgenre}</dc:subject>` : ''}
    ${data.project.isbn ? `<dc:identifier opf:scheme="ISBN">${data.project.isbn}</dc:identifier>` : ''}
    ${data.project.cover_image_url ? '<meta name="cover" content="cover-image"/>' : ''}
  </metadata>
  <manifest>
    ${manifestItems.join('\n    ')}
  </manifest>
  <spine toc="ncx">
    ${spineItems.join('\n    ')}
  </spine>
  <guide>
    ${data.project.cover_image_url ? '<reference type="cover" title="Cover" href="cover.xhtml"/>' : ''}
    ${options.includeTableOfContents ? '<reference type="toc" title="Table of Contents" href="toc.xhtml"/>' : ''}
    <reference type="text" title="Start" href="chapter-1.xhtml"/>
  </guide>
</package>`;
  }

  private generateEpubNavigation(data: ProjectExportData, options: ExportOptions): string {
    let navItems = '';
    
    if (options.includeFrontMatter) {
      navItems += '<li><a href="title.xhtml">Title Page</a></li>';
    }

    data.chapters.forEach(chapter => {
      const title = chapter.title || `Chapter ${chapter.chapter_number}`;
      navItems += `<li><a href="chapter-${chapter.chapter_number}.xhtml">${title}</a></li>`;
    });

    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops">
<head>
  <title>Navigation</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <nav epub:type="toc">
    <h1>Table of Contents</h1>
    <ol>
      ${navItems}
    </ol>
  </nav>
</body>
</html>`;
  }

  private generateEpubTitlePage(data: ProjectExportData, options: ExportOptions): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${data.project.title}</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="title-page">
    <h1>${data.project.title}</h1>
    ${data.project.description ? `<p class="description">${data.project.description}</p>` : ''}
    ${options.includeMetadata ? `
    <div class="metadata">
      <p>Genre: ${data.project.primary_genre || 'Not specified'}</p>
      <p>Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words</p>
      <p>Created: ${new Date(data.project.created_at).toLocaleDateString()}</p>
    </div>` : ''}
  </div>
</body>
</html>`;
  }

  // Reserved for future use - EPUB chapter generation
  /*
  private _generateEpubChapter(chapter: { chapter_number: number; title?: string; content?: string }): string {
    const title = chapter.title || `Chapter ${chapter.chapter_number}`;
    const content = chapter.content || '';

    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${title}</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="chapter">
    <h1>${title}</h1>
    <div class="content">
      ${content ? content.split('\n\n').map((p: string) => `<p>${p.trim()}</p>`).join('\n      ') : '<p>Content coming soon...</p>'}
    </div>
  </div>
</body>
</html>`;
  }
  */

  private generateEpubCSS(options: ExportOptions): string {
    const fontSize = options.customStyling?.fontSize || 16;
    const lineHeight = options.customStyling?.lineSpacing || 1.5;
    const fontFamily = options.customStyling?.fontFamily || 'serif';

    return `
body {
  font-family: ${fontFamily};
  font-size: ${fontSize}px;
  line-height: ${lineHeight};
  margin: 2em;
}

.title-page {
  text-align: center;
  margin-top: 4em;
}

.title-page h1 {
  font-size: 2.5em;
  margin-bottom: 1em;
}

.title-page .description {
  font-size: 1.2em;
  font-style: italic;
  margin-bottom: 2em;
}

.title-page .metadata {
  margin-top: 3em;
  font-size: 0.9em;
  color: #666;
}

.chapter h1 {
  font-size: 2em;
  margin-bottom: 1em;
  page-break-before: always;
}

.chapter p {
  margin-bottom: 1em;
  text-indent: 1.5em;
}

.chapter p:first-of-type {
  text-indent: 0;
}

/* Drop caps for first letter */
.chapter p:first-of-type::first-letter {
  font-size: 3em;
  line-height: 1;
  float: left;
  margin: 0 0.1em 0 0;
}

/* Scene breaks */
.scene-break {
  text-align: center;
  margin: 2em 0;
  font-size: 1.5em;
}

/* Blockquotes */
blockquote {
  margin: 1em 2em;
  font-style: italic;
  border-left: 3px solid #ccc;
  padding-left: 1em;
}

/* Footnotes */
.footnote {
  font-size: 0.85em;
  vertical-align: super;
}

.footnotes {
  margin-top: 3em;
  padding-top: 1em;
  border-top: 1px solid #ccc;
  font-size: 0.9em;
}
`;
  }

  // Additional EPUB helper methods for advanced features
  private generateEpubNCX(data: ProjectExportData, options: ExportOptions): string {
    let navPoints = '';
    let playOrder = 1;

    if (data.project.cover_image_url) {
      navPoints += `
    <navPoint id="cover" playOrder="${playOrder++}">
      <navLabel><text>Cover</text></navLabel>
      <content src="cover.xhtml"/>
    </navPoint>`;
    }

    if (options.includeFrontMatter) {
      navPoints += `
    <navPoint id="title" playOrder="${playOrder++}">
      <navLabel><text>Title Page</text></navLabel>
      <content src="title.xhtml"/>
    </navPoint>`;
    }

    if (options.includeTableOfContents) {
      navPoints += `
    <navPoint id="toc" playOrder="${playOrder++}">
      <navLabel><text>Table of Contents</text></navLabel>
      <content src="toc.xhtml"/>
    </navPoint>`;
    }

    data.chapters.forEach(chapter => {
      const title = chapter.title || `Chapter ${chapter.chapter_number}`;
      navPoints += `
    <navPoint id="chapter-${chapter.chapter_number}" playOrder="${playOrder++}">
      <navLabel><text>${title}</text></navLabel>
      <content src="chapter-${chapter.chapter_number}.xhtml"/>
    </navPoint>`;
    });

    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE ncx PUBLIC "-//NISO//DTD ncx 2005-1//EN" "http://www.daisy.org/z3986/2005/ncx-2005-1.dtd">
<ncx xmlns="http://www.daisy.org/z3986/2005/ncx/" version="2005-1">
  <head>
    <meta name="dtb:uid" content="${data.project.id}"/>
    <meta name="dtb:depth" content="1"/>
    <meta name="dtb:totalPageCount" content="0"/>
    <meta name="dtb:maxPageNumber" content="0"/>
  </head>
  <docTitle>
    <text>${this.escapeXml(data.project.title)}</text>
  </docTitle>
  <navMap>
    ${navPoints}
  </navMap>
</ncx>`;
  }

  private generateEpubCoverPage(data: ProjectExportData): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Cover</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
  <style>
    body { margin: 0; padding: 0; }
    .cover { width: 100%; height: 100vh; text-align: center; }
    .cover img { max-width: 100%; max-height: 100%; }
  </style>
</head>
<body>
  <div class="cover">
    <img src="images/cover.jpg" alt="${this.escapeXml(data.project.title)} Cover"/>
  </div>
</body>
</html>`;
  }

  private generateEpubCopyrightPage(data: ProjectExportData): string {
    const year = new Date().getFullYear();
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Copyright</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="copyright">
    <p>Copyright © ${year} ${data.project.author_name || 'Author'}</p>
    <p>All rights reserved.</p>
    ${data.project.isbn ? `<p>ISBN: ${data.project.isbn}</p>` : ''}
    <p>Published by ${data.project.publisher || 'BookScribe AI'}</p>
    <p>This is a work of fiction. Names, characters, places, and incidents either are the product of the author's imagination or are used fictitiously. Any resemblance to actual persons, living or dead, events, or locales is entirely coincidental.</p>
  </div>
</body>
</html>`;
  }

  private generateEpubTableOfContents(data: ProjectExportData): string {
    let tocItems = '';
    
    data.chapters.forEach(chapter => {
      const title = chapter.title || `Chapter ${chapter.chapter_number}`;
      tocItems += `<li><a href="chapter-${chapter.chapter_number}.xhtml">${this.escapeXml(title)}</a></li>\n      `;
    });

    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Table of Contents</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="toc">
    <h1>Table of Contents</h1>
    <ol>
      ${tocItems}
    </ol>
  </div>
</body>
</html>`;
  }

  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }
  
  private processContentForPDF(content: string, options: ExportOptions): string {
    let processed = content;
    
    // Normalize scene breaks
    if (options.sceneBreakSymbol) {
      // Replace common scene break patterns with the specified symbol
      processed = processed.replace(/^(\*\*\*|\* \* \*|---)$/gm, options.sceneBreakSymbol);
    }
    
    // Clean up excessive whitespace
    processed = processed.replace(/\n{3,}/g, '\n\n');
    
    return processed;
  }
  
  private processContentForDocx(content: string, options: ExportOptions): string {
    // Similar to PDF processing but preserving more formatting for Word
    return this.processContentForPDF(content, options);
  }

  private processChapterContent(content: string, options?: ExportOptions): string {
    // Convert markdown-style formatting to HTML
    let processed = content;
    
    // Handle scene breaks with custom symbol
    const sceneBreakSymbol = options?.sceneBreakSymbol || '* * *';
    processed = processed.replace(/^(\*\*\*|\* \* \*|---)$/gm, `<div class="scene-break">${this.escapeXml(sceneBreakSymbol)}</div>`);
    
    // Bold text (but not scene breaks)
    processed = processed.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    
    // Italic text (but not bold or scene breaks)
    processed = processed.replace(/\*([^*]+)\*/g, '<em>$1</em>');
    
    // Smart quotes (optional enhancement)
    processed = processed
      .replace(/"([^"]*)"/g, '"$1"')
      .replace(/'([^']*)'/g, '\u2018$1\u2019');
    
    // Em dashes
    processed = processed.replace(/--/g, '—');
    
    // Paragraphs
    const paragraphs = processed
      .split('\n\n')
      .map(p => p.trim())
      .filter(p => p.length > 0)
      .map(p => {
        if (p.includes('class="scene-break"')) return p; // Already HTML
        // Check if it's dialogue (starts with quotation)
        const isDialogue = p.startsWith('"') || p.startsWith('"');
        return `<p${isDialogue ? ' class="dialogue"' : ''}>${p}</p>`;
      });
    
    return paragraphs.join('\n      ');
  }
}

export const exportService = new ExportService();