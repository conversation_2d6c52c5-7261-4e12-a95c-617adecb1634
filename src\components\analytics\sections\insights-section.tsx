import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { 
  Lightbulb, 
  TrendingUp, 
  Award, 
  AlertTriangle,
  Sparkles,
  Brain,
  Zap,
  Target,
  Trophy
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { AnalyticsData } from '@/types/analytics'

interface InsightsSectionProps {
  data: AnalyticsData | null
  isLoading: boolean
}

export function InsightsSection({ data, isLoading }: InsightsSectionProps) {
  const insights = data?.insights || {
    recommendations: [],
    achievements: []
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'productivity':
        return TrendingUp
      case 'quality':
        return Sparkles
      case 'consistency':
        return Brain
      case 'improvement':
        return Lightbulb
      default:
        return Zap
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive'
      case 'medium':
        return 'default'
      case 'low':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  return (
    <>
      {/* AI-Generated Insights */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Brain className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">AI Writing Insights</h3>
        </div>

        {insights.recommendations.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">
                Keep writing to unlock personalized insights!
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2">
            {insights.recommendations.map((recommendation, index) => {
              const Icon = getInsightIcon(recommendation.type)
              
              return (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div className="p-2 bg-primary/10 rounded">
                          <Icon className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-base">{recommendation.title}</CardTitle>
                          <Badge 
                            variant={getPriorityColor(recommendation.priority || 'medium') as 'destructive' | 'default' | 'secondary'}
                            className="mt-1 text-xs"
                          >
                            {recommendation.priority} priority
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      {recommendation.description}
                    </p>
                    <Button variant="link" className="px-0 mt-2" size="sm">
                      Learn more →
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {/* Writing Patterns Alert */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Writing Pattern Detected</AlertTitle>
        <AlertDescription>
          You write 40% more words during morning sessions (9-11 AM). Consider scheduling your most important writing during this time.
        </AlertDescription>
      </Alert>

      {/* Achievements */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Achievements</CardTitle>
            <Award className="h-5 w-5 text-warning" />
          </div>
        </CardHeader>
        <CardContent>
          {insights.achievements.length === 0 ? (
            <div className="text-center py-8">
              <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">
                Your achievements will appear here as you reach milestones
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {insights.achievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className="flex items-center gap-3 p-3 bg-warning/10 rounded-lg"
                >
                  <Award className="h-5 w-5 text-warning" />
                  <div className="flex-1">
                    <p className="font-medium">{achievement.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {achievement.description}
                    </p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {new Date(achievement.unlockedAt).toLocaleDateString()}
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Personalized Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Personalized Writing Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-3">
              <div className="p-2 bg-info/10 rounded-full h-fit">
                <Target className="h-4 w-4 text-info" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Set Smaller Daily Goals</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  Based on your writing patterns, breaking your daily goal into 2-3 smaller sessions could increase productivity by 25%.
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <div className="p-2 bg-purple-500/10 rounded-full h-fit">
                <Sparkles className="h-4 w-4 text-purple-500" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Focus on Dialogue</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  Your dialogue scores are consistently high. Consider leveraging this strength by planning more dialogue-heavy scenes.
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <div className="p-2 bg-success/10 rounded-full h-fit">
                <Zap className="h-4 w-4 text-success" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Maintain Your Streak</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  You're more productive when maintaining a streak. Even 100 words on busy days keeps momentum.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress Predictions */}
      <Card>
        <CardHeader>
          <CardTitle>Progress Predictions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">Current Project Completion</h4>
                <Badge variant="outline">Based on current pace</Badge>
              </div>
              <p className="text-2xl font-bold text-primary">March 15, 2024</p>
              <p className="text-sm text-muted-foreground mt-1">
                ~45 days at current average of 1,200 words/day
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">Optimized Completion</h4>
                <Badge variant="default">With recommendations</Badge>
              </div>
              <p className="text-2xl font-bold text-success">February 28, 2024</p>
              <p className="text-sm text-muted-foreground mt-1">
                ~30 days with suggested improvements
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}