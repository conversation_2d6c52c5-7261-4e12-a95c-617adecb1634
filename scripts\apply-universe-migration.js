const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

async function applyMigration() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing required environment variables');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('Applying universe settings migration...');
    
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250127_add_universe_settings.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    const { error } = await supabase.rpc('exec_sql', { query: migrationSql });

    if (error) {
      console.error('Migration error:', error);
      
      // If exec_sql doesn't exist, try running the migration statements individually
      const statements = migrationSql.split(';').filter(s => s.trim());
      
      for (const statement of statements) {
        if (statement.trim()) {
          console.log('Running statement:', statement.substring(0, 50) + '...');
          const { error: stmtError } = await supabase.rpc('query', { text: statement });
          if (stmtError) {
            console.error('Statement error:', stmtError);
            // Try direct execution as a fallback
            console.log('Trying alternative approach...');
          }
        }
      }
    } else {
      console.log('Migration applied successfully!');
    }

    // Verify the columns exist
    const { data, error: checkError } = await supabase
      .from('universes')
      .select('*')
      .limit(1);

    if (checkError) {
      console.error('Error checking table:', checkError);
    } else {
      console.log('Table check successful');
    }

  } catch (error) {
    console.error('Error applying migration:', error);
  }
}

applyMigration();