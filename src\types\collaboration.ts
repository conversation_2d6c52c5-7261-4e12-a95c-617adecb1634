/**
 * Type definitions for collaboration features
 */

// Text change and selection types
export interface TextPosition {
  line: number
  column: number
}

export interface TextChange {
  from: TextPosition
  to: TextPosition
  text: string
}

export interface TextSelection {
  start: TextPosition
  end: TextPosition
}

// Collaboration broadcast events
export type CollaborationBroadcastEvent = 
  | UserJoinedEvent
  | UserLeftEvent
  | CursorMovedEvent
  | ContentChangedEvent
  | SelectionChangedEvent

export interface UserJoinedEvent {
  type: 'user_joined'
  userId: string
  role: string
  timestamp: number
}

export interface UserLeftEvent {
  type: 'user_left'
  userId: string
  timestamp: number
}

export interface CursorMovedEvent {
  type: 'cursor_moved'
  userId: string
  cursor: TextPosition
  timestamp: number
}

export interface ContentChangedEvent {
  type: 'content_changed'
  userId: string
  changes: TextChange[]
  timestamp: number
}

export interface SelectionChangedEvent {
  type: 'selection_changed'
  userId: string
  selection: TextSelection
  timestamp: number
}

// Collaboration session types
export interface CollaborationUser {
  id: string
  name: string
  email: string
  color: string
  role: 'owner' | 'editor' | 'viewer' | 'commenter'
  status: 'online' | 'offline'
  isActive: boolean
  cursor?: TextPosition
  selection?: {
    start: TextPosition
    end: TextPosition
  }
  lastSeen?: number
}

export interface CollaborationSession {
  id: string
  projectId: string
  documentId: string
  ownerId: string
  participants: CollaborationUser[]
  users: CollaborationUser[] // Legacy compatibility
  owner: string // Legacy compatibility
  isActive: boolean
  version: number
  conflictStrategy: 'merge' | 'last-write-wins' | 'first-write-wins' | 'manual'
  createdAt: Date
  updatedAt: Date
  lastActivity: Date // Legacy compatibility
}

// Conflict resolution types
export interface TextConflict {
  id: string
  localChange: TextChange
  remoteChange: TextChange
  conflictType: 'overlap' | 'adjacent' | 'deletion'
  timestamp: Date
}

export interface ConflictResolution {
  conflictId: string
  resolution: 'local' | 'remote' | 'merge'
  mergedText?: string
  resolvedBy: string
  timestamp: Date
}

// Collaboration change types for operational transform
export interface CollaborationChange {
  id?: string
  sessionId: string
  userId: string
  type: 'insert' | 'delete' | 'replace' | 'format' | 'comment'
  range: {
    startLine: number
    startColumn: number
    endLine: number
    endColumn: number
  }
  text: string
  metadata?: {
    author?: string
    comment?: string
    formatting?: {
      bold?: boolean
      italic?: boolean
      underline?: boolean
    }
  }
  timestamp: number
  version?: number
}