import { NextRequest, NextResponse } from 'next/server';
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    // Get all universes the user has access to
    const { data: universes, error } = await supabase
      .from('universes')
      .select(`
        *,
        series:series!universe_id(
          id,
          title,
          description
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching universes:', error);
      return NextResponse.json({ error: 'Failed to fetch universes' }, { status: 500 });
    }

    return NextResponse.json({ universes });
  } catch (error) {
    logger.error('Error in GET /api/universes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const { name, description, rules = {}, is_public = false, settings = {} } = body;

    if (!name?.trim()) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Create universe data object
    const universeData: any = {
      name: name.trim(),
      description: description?.trim(),
      rules,
      created_by: user.user.id
    };

    // Only add is_public and settings if they're supported by the database
    // This is a temporary workaround until migration is applied
    try {
      // Test if columns exist by attempting a select
      const { error: testError } = await supabase
        .from('universes')
        .select('is_public, settings')
        .limit(1);
      
      if (!testError) {
        universeData.is_public = is_public;
        universeData.settings = settings;
      }
    } catch {
      // Columns don't exist yet, skip them
      logger.info('Skipping is_public and settings columns - migration pending');
    }

    const { data: universe, error } = await supabase
      .from('universes')
      .insert(universeData)
      .select()
      .single();

    if (error) {
      logger.error('Error creating universe:', error);
      return NextResponse.json({ error: 'Failed to create universe' }, { status: 500 });
    }

    return NextResponse.json({ universe });
  } catch (error) {
    logger.error('Error in POST /api/universes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}