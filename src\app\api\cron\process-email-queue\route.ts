import { NextRequest, NextResponse } from 'next/server'
import { mailerooEmailService } from '@/lib/services'

// This endpoint should be called by a cron job (e.g., Vercel Cron or external service)
// Run every 5 minutes: */5 * * * *
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret (optional but recommended)
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Process email queue
    await mailerooEmailService.processEmailQueue()

    return NextResponse.json({ 
      success: true, 
      message: 'Email queue processed',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error processing email queue:', error)
    return NextResponse.json(
      { error: 'Failed to process email queue' },
      { status: 500 }
    )
  }
}
