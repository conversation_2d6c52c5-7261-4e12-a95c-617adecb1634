import { NextRequest, NextResponse } from 'next/server'
import { LRUCache } from 'lru-cache'
import { createClient } from '@/lib/supabase/server'
import { RateLimitError } from '@/lib/api/error-handler'
import { logger } from '@/lib/services/logger'
import { RATE_LIMITS } from '@/lib/config/environment-config'

// Rate limit configuration
export interface RateLimitConfig {
  windowMs: number              // Time window in milliseconds
  maxRequests: number          // Maximum requests in the window
  keyGenerator?: (req: NextRequest) => Promise<string> | string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  message?: string
  headers?: boolean            // Whether to add rate limit headers
}

// Rate limit result
export interface RateLimitResult {
  success: boolean
  remaining: number
  reset: number
  retryAfter?: number
}

// Rate limiter class using LRU cache
export class UnifiedRateLimiter {
  private cache: LRUCache<string, number[]>
  
  constructor(
    private maxSize: number = 10000,     // Max number of unique keys
    private ttl: number = 60 * 60 * 1000 // Default 1 hour TTL
  ) {
    this.cache = new LRUCache({
      max: maxSize,
      ttl: ttl
    })
  }

  check(key: string, config: RateLimitConfig): RateLimitResult {
    const now = Date.now()
    const timestamps = this.cache.get(key) || []
    
    // Remove timestamps outside the current window
    const validTimestamps = timestamps.filter(
      timestamp => now - timestamp < config.windowMs
    )
    
    // Check if limit exceeded
    if (validTimestamps.length >= config.maxRequests) {
      const oldestTimestamp = Math.min(...validTimestamps)
      const resetTime = oldestTimestamp + config.windowMs
      
      return {
        success: false,
        remaining: 0,
        reset: resetTime,
        retryAfter: Math.ceil((resetTime - now) / 1000)
      }
    }
    
    // Add current timestamp
    validTimestamps.push(now)
    this.cache.set(key, validTimestamps)
    
    return {
      success: true,
      remaining: config.maxRequests - validTimestamps.length,
      reset: now + config.windowMs
    }
  }

  // Reset rate limit for a specific key
  reset(key: string): void {
    this.cache.delete(key)
  }

  // Get current usage for a key
  getUsage(key: string, windowMs: number): number {
    const now = Date.now()
    const timestamps = this.cache.get(key) || []
    return timestamps.filter(t => now - t < windowMs).length
  }
}

// Global rate limiter instance
const globalRateLimiter = new UnifiedRateLimiter()

// Default key generators
export const keyGenerators = {
  // IP-based (default)
  ip: (req: NextRequest): string => {
    return getClientIP(req)
  },
  
  // User-based (requires auth)
  user: async (req: NextRequest): Promise<string> => {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    return user?.id || getClientIP(req)
  },
  
  // API key based
  apiKey: (req: NextRequest): string => {
    const apiKey = req.headers.get('x-api-key')
    return apiKey || getClientIP(req)
  },
  
  // Combined user + endpoint
  userEndpoint: async (req: NextRequest): Promise<string> => {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    const endpoint = new URL(req.url).pathname
    const userId = user?.id || getClientIP(req)
    return `${userId}:${endpoint}`
  }
}

// Get client IP address
export function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0].trim() : 'unknown'
  return ip
}

// Main rate limit check function
export async function checkRateLimit(
  req: NextRequest,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  const keyGenerator = config.keyGenerator || keyGenerators.ip
  const key = await keyGenerator(req)
  
  logger.debug('Rate limit check', {
    key,
    endpoint: req.url,
    config
  })
  
  return globalRateLimiter.check(key, config)
}

// Middleware helper for rate limiting
export async function withRateLimit(
  req: NextRequest,
  config: RateLimitConfig
): Promise<NextResponse | null> {
  const result = await checkRateLimit(req, config)
  
  if (!result.success) {
    logger.warn('Rate limit exceeded', {
      ip: getClientIP(req),
      endpoint: req.url,
      retryAfter: result.retryAfter
    })
    
    return createRateLimitResponse(
      config.message || 'Too many requests',
      result.retryAfter
    )
  }
  
  return null // Continue with request
}

// Create rate limit response
export function createRateLimitResponse(
  message: string = 'Too many requests',
  retryAfter?: number
): NextResponse {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  }
  
  if (retryAfter) {
    headers['Retry-After'] = retryAfter.toString()
    headers['X-RateLimit-Retry-After'] = retryAfter.toString()
  }
  
  return NextResponse.json(
    {
      error: message,
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter
    },
    {
      status: 429,
      headers
    }
  )
}

// Pre-configured rate limiters based on environment config
export const rateLimiters = {
  // Default rate limiter
  default: {
    windowMs: RATE_LIMITS.WINDOW.DEFAULT,
    maxRequests: RATE_LIMITS.API.DEFAULT
  },
  
  // Authenticated requests
  authenticated: {
    windowMs: RATE_LIMITS.WINDOW.DEFAULT,
    maxRequests: RATE_LIMITS.API.AUTHENTICATED,
    keyGenerator: keyGenerators.user
  },
  
  // AI generation endpoints
  aiGeneration: {
    windowMs: RATE_LIMITS.WINDOW.AI,
    maxRequests: RATE_LIMITS.API.AI_GENERATION,
    keyGenerator: keyGenerators.user,
    message: 'AI generation rate limit exceeded. Please wait before trying again.'
  },
  
  // AI analysis endpoints
  aiAnalysis: {
    windowMs: RATE_LIMITS.WINDOW.AI,
    maxRequests: RATE_LIMITS.API.AI_ANALYSIS,
    keyGenerator: keyGenerators.user,
    message: 'AI analysis rate limit exceeded. Please wait before trying again.'
  },
  
  // Webhook endpoints
  webhook: {
    windowMs: RATE_LIMITS.WINDOW.DEFAULT,
    maxRequests: RATE_LIMITS.API.WEBHOOKS,
    keyGenerator: keyGenerators.ip
  },
  
  // Burst protection (short-term)
  burst: {
    windowMs: RATE_LIMITS.BURST.WINDOW,
    maxRequests: RATE_LIMITS.BURST.DEFAULT,
    keyGenerator: keyGenerators.ip,
    message: 'Too many requests in a short time. Please slow down.'
  },
  
  // AI burst protection
  aiBurst: {
    windowMs: RATE_LIMITS.BURST.WINDOW,
    maxRequests: RATE_LIMITS.BURST.AI,
    keyGenerator: keyGenerators.user,
    message: 'Too many AI requests in a short time. Please wait a moment.'
  }
}

// Helper to apply multiple rate limiters
export async function checkMultipleRateLimits(
  req: NextRequest,
  configs: RateLimitConfig[]
): Promise<RateLimitResult | null> {
  for (const config of configs) {
    const result = await checkRateLimit(req, config)
    if (!result.success) {
      return result
    }
  }
  return null
}

// Convenience functions for common patterns
export const RateLimit = {
  // Check default rate limit
  check: (req: NextRequest) => 
    checkRateLimit(req, rateLimiters.default),
  
  // Check authenticated rate limit
  checkAuth: (req: NextRequest) =>
    checkRateLimit(req, rateLimiters.authenticated),
  
  // Check AI rate limits (both regular and burst)
  checkAI: async (req: NextRequest, type: 'generation' | 'analysis' = 'generation') => {
    const configs = [
      type === 'generation' ? rateLimiters.aiGeneration : rateLimiters.aiAnalysis,
      rateLimiters.aiBurst
    ]
    
    for (const config of configs) {
      const result = await checkRateLimit(req, config)
      if (!result.success) {
        return result
      }
    }
    
    return { success: true, remaining: 0, reset: 0 }
  },
  
  // Create response helper
  response: createRateLimitResponse
}

// Export types
export type { RateLimitEntry } from '@/lib/api/rate-limiter'

// For backwards compatibility
export const aiLimiter = globalRateLimiter
export const authLimiter = globalRateLimiter
export const apiLimiter = globalRateLimiter