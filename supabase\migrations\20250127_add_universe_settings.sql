-- Add missing columns to universes table
ALTER TABLE universes 
ADD COLUMN is_public BOOLEAN DEFAULT false,
ADD COLUMN settings JSONB DEFAULT '{}';

-- Update the RLS policy for public universes
DROP POLICY IF EXISTS "Users can view universes they created or are part of" ON universes;

CREATE POLICY "Users can view universes they created or are part of" ON universes
    FOR SELECT USING (
        auth.uid() = created_by OR
        is_public = true OR
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.universe_id = universes.id
            AND s.user_id = auth.uid()
        )
    );