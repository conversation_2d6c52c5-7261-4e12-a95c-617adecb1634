import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { openai } from '@/lib/ai/openai-client'
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

const querySchema = z.object({
  projectId: z.string().uuid(),
  characterId: z.string().uuid()
})

interface CharacterArcEvent {
  id: string
  characterId: string
  chapterId: string
  chapterNumber: number
  type: 'growth' | 'setback' | 'relationship' | 'conflict' | 'revelation' | 'achievement'
  title: string
  description: string
  emotionalState?: string
  intensity: number
  timestamp: string
}

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    const characterId = searchParams.get('characterId')
    
    const validation = querySchema.safeParse({ projectId, characterId })
    if (!validation.success) {
      return UnifiedResponse.error('Invalid query parameters', 400, validation.error.errors)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const project = await verifyProjectAccess(projectId, user.id)
    if (!project) {
      return UnifiedResponse.error(PROJECT_ACCESS_ERROR, 404)
    }

    // Get character data
    const { data: character } = await supabase
      .from('characters')
      .select('*')
      .eq('id', characterId)
      .eq('project_id', projectId)
      .single()

    if (!character) {
      return UnifiedResponse.error('Character not found', 404)
    }

    // Get all characters in project for relationship analysis
    const { data: projectCharacters } = await supabase
      .from('characters')
      .select('id, name')
      .eq('project_id', projectId)

    // Get all chapters with character appearances
    const { data: chapters } = await supabase
      .from('chapters')
      .select('id, chapter_number, content, title')
      .eq('project_id', projectId)
      .order('chapter_number')

    if (!chapters || chapters.length === 0) {
      return UnifiedResponse.success({
        characterId,
        characterName: character.name,
        events: [],
        overallTrajectory: 'stable',
        keyMoments: [],
        relationships: []
      })
    }

    // Analyze character arc across chapters
    const events: CharacterArcEvent[] = []
    
    for (const chapter of chapters) {
      if (!chapter.content || !chapter.content.includes(character.name)) {
        continue
      }

      try {
        // Use AI to analyze character development in this chapter
        const analysis = await analyzeCharacterInChapter(
          character,
          chapter,
          events // Pass previous events for context
        )

        if (analysis && analysis.events.length > 0) {
          events.push(...analysis.events)
        }
      } catch (error) {
        logger.error('Failed to analyze chapter:', { chapterId: chapter.id, error })
      }
    }

    // Determine overall trajectory
    const trajectory = determineTrajectory(events)
    
    // Identify key moments (highest intensity events)
    const keyMoments = events
      .sort((a, b) => b.intensity - a.intensity)
      .slice(0, 5)

    // Analyze relationships between this character and others
    const relationships = extractRelationships(
      character,
      chapters,
      projectCharacters || []
    )

    return UnifiedResponse.success({
      characterId,
      characterName: character.name,
      events,
      overallTrajectory: trajectory,
      keyMoments,
      relationships
    })
  } catch (error) {
    logger.error('Character arc analysis error:', error)
    return UnifiedResponse.error('Failed to analyze character arc')
  }
})

async function analyzeCharacterInChapter(
  character: any,
  chapter: any,
  previousEvents: CharacterArcEvent[]
): Promise<{ events: CharacterArcEvent[] } | null> {
  try {
    const prompt = `Analyze the character "${character.name}" in this chapter and identify significant character development moments.

Character Profile:
${JSON.stringify({
  name: character.name,
  role: character.role,
  personality: character.personality,
  goals: character.goals
}, null, 2)}

Previous Development (for context):
${previousEvents.slice(-3).map(e => `- ${e.title}: ${e.description}`).join('\n')}

Chapter ${chapter.chapter_number}: ${chapter.title || ''}
${chapter.content.substring(0, 3000)}...

Identify character arc events in this chapter. For each significant moment, provide:
1. Type: growth, setback, relationship, conflict, revelation, or achievement
2. Title: Brief description (max 50 chars)
3. Description: What happened and why it matters (max 200 chars)
4. Emotional state: The character's emotional state during/after
5. Intensity: Rate 1-5 (5 being most significant)

Only include events that show actual character development or change.`

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a literary analyst specializing in character development. Analyze character arcs and identify key moments of growth, conflict, and change.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      response_format: { type: 'json_object' },
      max_tokens: 1000
    })

    const result = JSON.parse(response.choices[0].message.content || '{}')
    
    if (!result.events || !Array.isArray(result.events)) {
      return null
    }

    // Convert to our format
    const events: CharacterArcEvent[] = result.events.map((e: any) => ({
      id: `${chapter.id}_${Math.random().toString(36).substr(2, 9)}`,
      characterId: character.id,
      chapterId: chapter.id,
      chapterNumber: chapter.chapter_number,
      type: e.type || 'growth',
      title: (e.title || '').substring(0, 50),
      description: (e.description || '').substring(0, 200),
      emotionalState: e.emotional_state,
      intensity: Math.min(5, Math.max(1, e.intensity || 3)),
      timestamp: new Date().toISOString()
    }))

    return { events }
  } catch (error) {
    logger.error('AI analysis failed:', error)
    return null
  }
}

function determineTrajectory(events: CharacterArcEvent[]): 'rising' | 'falling' | 'stable' | 'volatile' {
  if (events.length === 0) return 'stable'

  // Calculate trajectory based on event types and intensities
  let score = 0
  let volatility = 0
  let lastScore = 0

  for (const event of events) {
    const eventScore = getEventScore(event)
    score += eventScore
    volatility += Math.abs(eventScore - lastScore)
    lastScore = eventScore
  }

  const avgVolatility = volatility / events.length

  if (avgVolatility > 3) return 'volatile'
  if (score > events.length * 0.5) return 'rising'
  if (score < events.length * -0.5) return 'falling'
  return 'stable'
}

function getEventScore(event: CharacterArcEvent): number {
  const typeScores = {
    growth: 1,
    achievement: 1,
    revelation: 0.5,
    relationship: 0,
    conflict: -0.5,
    setback: -1
  }

  return (typeScores[event.type] || 0) * (event.intensity / 3)
}

interface CharacterRelationship {
  characterId: string
  characterName: string
  type: 'ally' | 'rival' | 'neutral'
  interactions: number
}

export function extractRelationships(
  character: any,
  chapters: any[],
  characters: any[]
): CharacterRelationship[] {
  const relationships = new Map<string, CharacterRelationship>()
  const targetName = character.name.toLowerCase()


  const allyKeywords = [
    'ally',
    'friend',
    'support',
    'help',
    'save',
    'rescue',
    'team',
    'together',
    'assist',
    'protect'
  ]
  const rivalKeywords = [
    'enemy',
    'rival',
    'against',
    'fight',
    'battle',
    'kill',
    'attack',
    'confront',
    'versus',
    'oppose'
  ]

  for (const chapter of chapters) {
    if (!chapter.content || !chapter.content.toLowerCase().includes(targetName)) {
      continue
    }

    const content = chapter.content
    const contentLower = content.toLowerCase()

    const escapeRegExp = (str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    for (const other of characters) {
      if (other.id === character.id) continue

      const otherRegex = new RegExp(`\\b${escapeRegExp(other.name)}\\b`, 'gi')
      let match: RegExpExecArray | null
      while ((match = otherRegex.exec(content)) !== null) {
        const snippet = contentLower.slice(
          Math.max(0, match.index - 50),
          match.index + match[0].length + 50
        )

        let type: 'ally' | 'rival' | 'neutral' = 'neutral'
        if (allyKeywords.some(k => snippet.includes(k))) type = 'ally'
        if (rivalKeywords.some(k => snippet.includes(k))) type = 'rival'

        const existing = relationships.get(other.id)
        if (existing) {
          existing.interactions += 1
          if (existing.type === 'neutral' && type !== 'neutral') {
            existing.type = type
          }
          if (existing.type === 'ally' && type === 'rival') {
            existing.type = 'rival'
          }
        } else {
          relationships.set(other.id, {
            characterId: other.id,
            characterName: other.name,
            type,
            interactions: 1
          })
        }
      }
    }
  }

  return Array.from(relationships.values())
}
