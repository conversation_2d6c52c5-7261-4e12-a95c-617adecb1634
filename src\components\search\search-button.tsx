'use client'

import { Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface SearchButtonProps {
  onClick: () => void
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showShortcut?: boolean
}

export function SearchButton({ 
  onClick, 
  className,
  variant = 'outline',
  size = 'default',
  showShortcut = true
}: SearchButtonProps) {
  return (
    <Button
      variant={variant}
      size={size}
      onClick={onClick}
      className={cn(
        "relative group",
        size === 'icon' ? 'w-9 h-9' : 'gap-2',
        className
      )}
    >
      <Search className={cn(
        "transition-colors",
        size === 'icon' ? 'w-4 h-4' : 'w-4 h-4'
      )} />
      {size !== 'icon' && (
        <>
          <span>Search</span>
          {showShortcut && (
            <kbd className="pointer-events-none hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
              <span className="text-xs">⌘</span>K
            </kbd>
          )}
        </>
      )}
    </Button>
  )
}