# Zustand Store Improvement Guide

## Standard Store Structure

```typescript
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { logger } from '@/lib/services/logger'

interface ExampleStore {
  items: string[]
  loading: boolean

  // Actions
  setItems: (items: string[]) => void
  setLoading: (loading: boolean) => void
  addItem: () => void
  removeItem: () => void
  fetchItems: () => Promise<void>
}

export const useExampleStore = create<ExampleStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        items: [],
        loading: false,

        setItems: (items) => set({ items }),
        setLoading: (loading) => set({ loading }),
        addItem: () => {
          // Implement addItem
        },
        removeItem: () => {
          // Implement removeItem
        },
        fetchItems: async () => {
          set({ loading: true });
          try {
            // Async logic here
          } catch (error) {
            logger.error(error);
          } finally {
            set({ loading: false });
          }
        },
      })),
      {
        name: 'example-storage',
      }
    ),
    {
      name: 'ExampleStore'
    }
  )
)

// Selectors for better performance
export const useExampleSelectors = {
  getItems: (state: ExampleStore) => state.items
};
```

## Improvements Needed


### editor-store.ts
- Add devtools middleware for debugging

- Add immer middleware for easier updates
- Add devtools for debugging
- Add immer for easier state updates
- Create selectors for chapter lookups
- Type the chapter status properly


### project-store.ts
- Add devtools middleware for debugging
- Add persist middleware for localStorage
- Add immer middleware for easier updates
- Add devtools and persist middleware
- Create typed selectors for project queries
- Add error handling for async actions
- Implement optimistic updates


### settings-store.ts
- Add devtools middleware for debugging


- Already has persist - add devtools
- Create typed setting groups
- Add validation for settings
- Export individual setting hooks


### custom-themes-store.ts
- Add devtools middleware for debugging


- Already has persist - add devtools
- Add theme validation
- Create theme preset management
- Add import/export functionality


## Best Practices Applied

1. **TypeScript First**: Fully typed store interfaces
2. **Middleware Stack**: devtools → persist → immer
3. **Selectors**: Exported hooks for specific state slices
4. **Actions**: Clear naming, proper error handling
5. **Performance**: Use selectors to prevent unnecessary re-renders
