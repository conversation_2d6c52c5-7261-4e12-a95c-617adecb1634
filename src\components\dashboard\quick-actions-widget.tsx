'use client'

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Brain, Target, Zap, FileText, Clock, Sparkles } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface QuickAction {
  icon: React.ComponentType<{ className?: string }>
  label: string
  description: string
  href: string
  color: string
}

const quickActions: QuickAction[] = [
  {
    icon: PenTool,
    label: 'Continue Writing',
    description: 'Jump back into your latest chapter',
    href: '/projects',
    color: 'text-primary'
  },
  {
    icon: Sparkles,
    label: 'Playground',
    description: 'Explore features in sandbox mode',
    href: '/playground',
    color: 'text-blue-500'
  },
  {
    icon: Brain,
    label: 'AI Brainstorm',
    description: 'Get help with plot or characters',
    href: '/projects?tab=ai-assist',
    color: 'text-purple-500'
  },
  {
    icon: Target,
    label: 'Writing Goals',
    description: 'Check your progress today',
    href: '/analytics?view=goals',
    color: 'text-green-500'
  }
]

interface QuickActionsWidgetProps {
  className?: string
}

export function QuickActionsWidget({ className }: QuickActionsWidgetProps) {
  return (
    <Card className={cn('p-6', className)}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Quick Actions</h3>
        <Clock className="w-4 h-4 text-muted-foreground" />
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        {quickActions.map((action, index) => {
          const Icon = action.icon
          return (
            <Link key={index} href={action.href}>
              <Button
                variant="outline"
                className="w-full h-auto flex flex-col items-start p-4 hover:bg-muted/50 group"
              >
                <Icon className={cn('w-5 h-5 mb-2 transition-transform group-hover:scale-110', action.color)} />
                <span className="font-medium text-sm">{action.label}</span>
                <span className="text-xs text-muted-foreground">{action.description}</span>
              </Button>
            </Link>
          )
        })}
      </div>
    </Card>
  )
}

interface WritingStreakProps {
  currentStreak: number
  longestStreak: number
  todayWordCount: number
  dailyGoal: number
}

export function WritingStreakWidget({ 
  currentStreak = 7, 
  longestStreak = 21, 
  todayWordCount = 847, 
  dailyGoal = 1000 
}: WritingStreakProps) {
  const progress = (todayWordCount / dailyGoal) * 100

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Writing Streak</h3>
        <div className="flex items-center gap-1">
          {[...Array(7)].map((_, i) => (
            <div
              key={i}
              className={cn(
                'w-2 h-2 rounded-full',
                i < currentStreak ? 'bg-primary' : 'bg-muted'
              )}
            />
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-end">
          <div>
            <p className="text-3xl font-bold text-primary">{currentStreak}</p>
            <p className="text-sm text-muted-foreground">Day streak</p>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium">Best: {longestStreak} days</p>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Today's Progress</span>
            <span className="font-medium">{todayWordCount} / {dailyGoal} words</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-500"
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          {progress >= 100 && (
            <p className="text-xs text-primary font-medium">🎉 Daily goal achieved!</p>
          )}
        </div>
      </div>
    </Card>
  )
}

export function RecentProjectsWidget({ projects }: { projects: any[] }) {
  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Recent Projects</h3>
        <Link href="/projects">
          <Button variant="ghost" size="sm">
            View all
          </Button>
        </Link>
      </div>

      <div className="space-y-3">
        {projects.slice(0, 3).map((project) => (
          <Link
            key={project.id}
            href={`/projects/${project.id}/write`}
            className="block p-3 rounded-lg border hover:bg-muted/50 transition-colors"
          >
            <div className="flex items-start justify-between">
              <div>
                <h4 className="font-medium">{project.title}</h4>
                <p className="text-sm text-muted-foreground">
                  {project.primary_genre} • Chapter {project.current_chapter || 1}
                </p>
              </div>
              <FileText className="w-4 h-4 text-muted-foreground" />
            </div>
          </Link>
        ))}
      </div>
    </Card>
  )
}