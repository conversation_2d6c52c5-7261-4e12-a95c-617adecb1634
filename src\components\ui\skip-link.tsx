import * as React from "react"
import { cn } from "@/lib/utils"
import { FOCUS_CLASSES, SR_ONLY } from "@/lib/utils/accessibility-utils"

export interface SkipLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  targetId?: string
  children?: React.ReactNode
}

/**
 * Skip link component for keyboard navigation
 * Provides a way for keyboard users to skip to main content
 */
export function SkipLink({ 
  targetId = "main-content", 
  children = "Skip to main content",
  className,
  ...props 
}: SkipLinkProps) {
  return (
    <a
      href={`#${targetId}`}
      className={cn(
        "absolute left-0 top-0 z-[100] px-4 py-2",
        "bg-background text-foreground rounded-md",
        "transform -translate-y-full transition-transform duration-200",
        "focus:translate-y-0",
        FOCUS_CLASSES.VISIBLE,
        className
      )}
      {...props}
    >
      {children}
    </a>
  )
}

/**
 * Skip navigation component with multiple skip links
 */
export interface SkipNavigationProps {
  links?: Array<{
    targetId: string
    label: string
  }>
}

export function SkipNavigation({ 
  links = [
    { targetId: "main-content", label: "Skip to main content" },
    { targetId: "main-navigation", label: "Skip to navigation" },
  ] 
}: SkipNavigationProps) {
  return (
    <div className={cn(SR_ONLY, "focus-within:not-sr-only")}>
      {links.map((link) => (
        <SkipLink key={link.targetId} targetId={link.targetId}>
          {link.label}
        </SkipLink>
      ))}
    </div>
  )
}