-- ============================================================================
-- ENHANCED RLS POLICIES FOR BOOKSCRIBE AI - SECURITY HARDENING
-- ============================================================================
-- Addresses security audit findings from 2025-01-14
-- Implements strengthened admin controls, session validation, and audit logging
-- Version: 2.0 - Enhanced Security

-- ============================================================================
-- 1. CREATE ADMIN AUDIT LOG TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.admin_audit_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    target_resource VARCHAR(100),
    target_id UUID,
    request_details JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    execution_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for audit log queries
CREATE INDEX idx_admin_audit_logs_admin_user ON public.admin_audit_logs(admin_user_id, created_at DESC);
CREATE INDEX idx_admin_audit_logs_action ON public.admin_audit_logs(action, created_at DESC);
CREATE INDEX idx_admin_audit_logs_target ON public.admin_audit_logs(target_resource, target_id);
CREATE INDEX idx_admin_audit_logs_ip ON public.admin_audit_logs(ip_address, created_at DESC);

-- Enable RLS on audit logs
ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Admins can view audit logs" ON public.admin_audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- System can insert audit logs (service role only)
CREATE POLICY "System can insert audit logs" ON public.admin_audit_logs
    FOR INSERT WITH CHECK (true); -- Service role bypasses RLS anyway

-- ============================================================================
-- 2. CREATE ADMIN SESSION MANAGEMENT TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.admin_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    elevated BOOLEAN NOT NULL DEFAULT false,
    elevated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    revoked BOOLEAN NOT NULL DEFAULT false,
    revoked_at TIMESTAMP WITH TIME ZONE,
    revoked_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for session management
CREATE INDEX idx_admin_sessions_user_active ON public.admin_sessions(user_id, revoked, expires_at);
CREATE INDEX idx_admin_sessions_token ON public.admin_sessions(session_token) WHERE NOT revoked;
CREATE INDEX idx_admin_sessions_cleanup ON public.admin_sessions(expires_at) WHERE NOT revoked;

-- Enable RLS on admin sessions
ALTER TABLE public.admin_sessions ENABLE ROW LEVEL SECURITY;

-- Users can view their own admin sessions
CREATE POLICY "Users can view own admin sessions" ON public.admin_sessions
    FOR SELECT USING (auth.uid() = user_id);

-- System can manage admin sessions (service role)
CREATE POLICY "System can manage admin sessions" ON public.admin_sessions
    FOR ALL WITH CHECK (true);

-- ============================================================================
-- 3. STRENGTHEN EXISTING PROFILES TABLE FOR ADMIN VALIDATION
-- ============================================================================

-- Add enhanced admin validation columns if they don't exist
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS mfa_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS last_admin_action TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS admin_permissions JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS session_timeout_minutes INTEGER DEFAULT 60,
ADD COLUMN IF NOT EXISTS require_mfa_for_admin BOOLEAN DEFAULT false;

-- Add index for admin role queries
CREATE INDEX IF NOT EXISTS idx_profiles_admin_role ON public.profiles(role, mfa_enabled) WHERE role = 'admin';

-- Update existing admin RLS policy to be more restrictive
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile basic info" ON public.profiles
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id AND
        -- Prevent users from elevating their own role
        OLD.role = NEW.role AND
        -- Prevent users from changing admin-only fields
        OLD.admin_permissions = NEW.admin_permissions AND
        OLD.require_mfa_for_admin = NEW.require_mfa_for_admin
    );

-- Allow admins to manage other profiles with audit trail
CREATE POLICY "Admins can manage profiles with restrictions" ON public.profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        ) AND
        -- Prevent demoting the last admin
        (NEW.role = 'admin' OR 
         EXISTS (
             SELECT 1 FROM public.profiles 
             WHERE role = 'admin' AND id != NEW.id
         ))
    );

-- ============================================================================
-- 4. ENHANCED PROJECT ACCESS CONTROLS
-- ============================================================================

-- Add project access audit logging
ALTER TABLE public.projects 
ADD COLUMN IF NOT EXISTS last_admin_access TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS admin_access_count INTEGER DEFAULT 0;

-- Update project policies to include audit logging
DROP POLICY IF EXISTS "Users can view own projects" ON public.projects;
DROP POLICY IF EXISTS "Users can create projects" ON public.projects;
DROP POLICY IF EXISTS "Users can update own projects" ON public.projects;
DROP POLICY IF EXISTS "Users can delete own projects" ON public.projects;

-- Standard user access to projects
CREATE POLICY "Users can view own projects" ON public.projects
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create projects" ON public.projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON public.projects
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own projects" ON public.projects
    FOR DELETE USING (auth.uid() = user_id);

-- Admin access to all projects (bypasses normal RLS)
CREATE POLICY "Admins can view all projects" ON public.projects
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- ============================================================================
-- 5. ENHANCED CHAPTERS SECURITY
-- ============================================================================

-- Strengthen chapter access controls
DROP POLICY IF EXISTS "Users can access chapters in owned projects" ON public.chapters;

CREATE POLICY "Users can view chapters in own projects" ON public.chapters
    FOR SELECT USING (
        project_id IN (SELECT id FROM public.projects WHERE user_id = auth.uid())
    );

CREATE POLICY "Users can create chapters in own projects" ON public.chapters
    FOR INSERT WITH CHECK (
        project_id IN (SELECT id FROM public.projects WHERE user_id = auth.uid())
    );

CREATE POLICY "Users can update chapters in own projects" ON public.chapters
    FOR UPDATE USING (
        project_id IN (SELECT id FROM public.projects WHERE user_id = auth.uid())
    );

CREATE POLICY "Users can delete chapters in own projects" ON public.chapters
    FOR DELETE USING (
        project_id IN (SELECT id FROM public.projects WHERE user_id = auth.uid())
    );

-- Admin access to all chapters
CREATE POLICY "Admins can access all chapters" ON public.chapters
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- ============================================================================
-- 6. SECURE AI USAGE LOGS
-- ============================================================================

-- Ensure AI usage logs have proper RLS
ALTER TABLE IF EXISTS public.ai_usage_logs ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view own ai_usage_logs" ON public.ai_usage_logs;

CREATE POLICY "Users can view own ai_usage_logs" ON public.ai_usage_logs
    FOR SELECT USING (auth.uid() = user_id);

-- System can insert AI usage logs
CREATE POLICY "System can insert ai_usage_logs" ON public.ai_usage_logs
    FOR INSERT WITH CHECK (true);

-- Admins can view all AI usage (for monitoring)
CREATE POLICY "Admins can view all ai_usage_logs" ON public.ai_usage_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- ============================================================================
-- 7. SECURE COLLABORATION FEATURES
-- ============================================================================

-- Strengthen collaboration session access
DROP POLICY IF EXISTS "Users can view collaboration_sessions they own or participate in" ON public.collaboration_sessions;
DROP POLICY IF EXISTS "Project owners can create collaboration_sessions" ON public.collaboration_sessions;
DROP POLICY IF EXISTS "Session creators can update collaboration_sessions" ON public.collaboration_sessions;

CREATE POLICY "Users can view collaboration_sessions they participate in" ON public.collaboration_sessions
    FOR SELECT USING (
        -- Project owner
        project_id IN (SELECT id FROM public.projects WHERE user_id = auth.uid()) OR
        -- Active participant
        id IN (
            SELECT session_id FROM public.collaboration_participants 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Project owners can create collaboration_sessions" ON public.collaboration_sessions
    FOR INSERT WITH CHECK (
        project_id IN (SELECT id FROM public.projects WHERE user_id = auth.uid()) AND
        auth.uid() = created_by
    );

CREATE POLICY "Session owners can update collaboration_sessions" ON public.collaboration_sessions
    FOR UPDATE USING (
        auth.uid() = created_by OR
        project_id IN (SELECT id FROM public.projects WHERE user_id = auth.uid())
    );

-- ============================================================================
-- 8. SECURE SUBSCRIPTION AND BILLING DATA
-- ============================================================================

-- Ensure user subscriptions are properly secured
ALTER TABLE IF EXISTS public.user_subscriptions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view own subscriptions" ON public.user_subscriptions;

CREATE POLICY "Users can view own subscriptions" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- Admins can view all subscriptions (for support)
CREATE POLICY "Admins can view all subscriptions" ON public.user_subscriptions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- System can manage subscriptions (webhooks, etc)
CREATE POLICY "System can manage subscriptions" ON public.user_subscriptions
    FOR ALL WITH CHECK (true);

-- ============================================================================
-- 9. ADMIN PERMISSION VALIDATION FUNCTIONS
-- ============================================================================

-- Function to validate admin session and permissions
CREATE OR REPLACE FUNCTION validate_admin_access(
    required_permission TEXT DEFAULT NULL,
    max_session_age_minutes INTEGER DEFAULT 60
) RETURNS BOOLEAN AS $$
DECLARE
    user_profile RECORD;
    admin_session RECORD;
BEGIN
    -- Get user profile
    SELECT role, mfa_enabled, require_mfa_for_admin, session_timeout_minutes, admin_permissions
    INTO user_profile
    FROM public.profiles
    WHERE id = auth.uid();
    
    -- Check if user is admin
    IF user_profile.role != 'admin' THEN
        RETURN FALSE;
    END IF;
    
    -- Check for active admin session
    SELECT elevated, elevated_at, expires_at
    INTO admin_session
    FROM public.admin_sessions
    WHERE user_id = auth.uid() 
      AND NOT revoked 
      AND expires_at > NOW()
      AND elevated = true
    ORDER BY elevated_at DESC
    LIMIT 1;
    
    -- If no active elevated session, require re-authentication
    IF admin_session.elevated_at IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check session age
    IF admin_session.elevated_at < NOW() - INTERVAL '1 minute' * COALESCE(max_session_age_minutes, user_profile.session_timeout_minutes, 60) THEN
        RETURN FALSE;
    END IF;
    
    -- Check specific permission if required
    IF required_permission IS NOT NULL THEN
        IF NOT (user_profile.admin_permissions->required_permission)::BOOLEAN THEN
            RETURN FALSE;
        END IF;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
    action_name TEXT,
    target_resource TEXT DEFAULT NULL,
    target_id UUID DEFAULT NULL,
    request_details JSONB DEFAULT NULL,
    ip_address INET DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    execution_time_ms INTEGER DEFAULT NULL,
    success BOOLEAN DEFAULT true,
    error_message TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    log_id UUID;
    session_info RECORD;
BEGIN
    -- Get current session info
    SELECT session_token INTO session_info
    FROM public.admin_sessions
    WHERE user_id = auth.uid() 
      AND NOT revoked 
      AND expires_at > NOW()
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Insert audit log
    INSERT INTO public.admin_audit_logs (
        admin_user_id,
        action,
        target_resource,
        target_id,
        request_details,
        ip_address,
        user_agent,
        session_id,
        success,
        error_message,
        execution_time_ms
    ) VALUES (
        auth.uid(),
        action_name,
        target_resource,
        target_id,
        request_details,
        ip_address,
        user_agent,
        session_info.session_token,
        success,
        error_message,
        execution_time_ms
    ) RETURNING id INTO log_id;
    
    -- Update last admin action timestamp
    UPDATE public.profiles 
    SET last_admin_action = NOW()
    WHERE id = auth.uid();
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 10. CLEANUP FUNCTIONS FOR SECURITY MAINTENANCE
-- ============================================================================

-- Function to clean up expired admin sessions
CREATE OR REPLACE FUNCTION cleanup_expired_admin_sessions() RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    UPDATE public.admin_sessions 
    SET revoked = true, revoked_at = NOW()
    WHERE expires_at < NOW() AND NOT revoked;
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    -- Delete very old sessions (older than 30 days)
    DELETE FROM public.admin_sessions
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to detect suspicious admin activity
CREATE OR REPLACE FUNCTION detect_suspicious_admin_activity() RETURNS TABLE(
    admin_user_id UUID,
    suspicious_activity TEXT,
    activity_count BIGINT,
    last_occurrence TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Return suspicious patterns from last 24 hours
    RETURN QUERY
    WITH suspicious_patterns AS (
        -- Multiple failed admin actions
        SELECT 
            a.admin_user_id,
            'Multiple failed admin actions' as activity_type,
            COUNT(*) as count,
            MAX(a.created_at) as last_seen
        FROM public.admin_audit_logs a
        WHERE a.created_at > NOW() - INTERVAL '24 hours'
          AND a.success = false
        GROUP BY a.admin_user_id
        HAVING COUNT(*) > 5
        
        UNION ALL
        
        -- Admin actions from multiple IPs
        SELECT 
            a.admin_user_id,
            'Admin actions from multiple IP addresses' as activity_type,
            COUNT(DISTINCT a.ip_address) as count,
            MAX(a.created_at) as last_seen
        FROM public.admin_audit_logs a
        WHERE a.created_at > NOW() - INTERVAL '24 hours'
          AND a.ip_address IS NOT NULL
        GROUP BY a.admin_user_id
        HAVING COUNT(DISTINCT a.ip_address) > 3
        
        UNION ALL
        
        -- High volume of admin actions
        SELECT 
            a.admin_user_id,
            'High volume of admin actions' as activity_type,
            COUNT(*) as count,
            MAX(a.created_at) as last_seen
        FROM public.admin_audit_logs a
        WHERE a.created_at > NOW() - INTERVAL '1 hour'
        GROUP BY a.admin_user_id
        HAVING COUNT(*) > 50
    )
    SELECT 
        sp.admin_user_id,
        sp.activity_type,
        sp.count,
        sp.last_seen
    FROM suspicious_patterns sp
    ORDER BY sp.last_seen DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 11. GRANT APPROPRIATE PERMISSIONS
-- ============================================================================

-- Grant execute permissions to authenticated users for validation functions
GRANT EXECUTE ON FUNCTION validate_admin_access(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION log_admin_action(TEXT, TEXT, UUID, JSONB, INET, TEXT, INTEGER, BOOLEAN, TEXT) TO authenticated;

-- Grant execute permissions to service role for maintenance functions
GRANT EXECUTE ON FUNCTION cleanup_expired_admin_sessions() TO service_role;
GRANT EXECUTE ON FUNCTION detect_suspicious_admin_activity() TO service_role;

-- ============================================================================
-- 12. CREATE SCHEDULED CLEANUP JOB (REQUIRES SUPERUSER PRIVILEGES)
-- ============================================================================

-- Note: This would typically be set up as a cron job or scheduled function
-- For now, we'll create the function that can be called periodically

CREATE OR REPLACE FUNCTION scheduled_security_maintenance() RETURNS TEXT AS $$
DECLARE
    result TEXT := '';
    cleaned_sessions INTEGER;
    suspicious_count INTEGER;
BEGIN
    -- Clean up expired sessions
    SELECT cleanup_expired_admin_sessions() INTO cleaned_sessions;
    result := result || 'Cleaned ' || cleaned_sessions || ' expired admin sessions. ';
    
    -- Check for suspicious activity
    SELECT COUNT(*) INTO suspicious_count
    FROM detect_suspicious_admin_activity();
    
    IF suspicious_count > 0 THEN
        result := result || 'WARNING: ' || suspicious_count || ' suspicious admin activity patterns detected. ';
    ELSE
        result := result || 'No suspicious admin activity detected. ';
    END IF;
    
    -- Clean up old audit logs (keep 90 days)
    DELETE FROM public.admin_audit_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    result := result || 'Completed security maintenance.';
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 13. VERIFICATION QUERIES
-- ============================================================================

-- Verify all tables have RLS enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Count policies per table
SELECT 
    tablename,
    COUNT(*) as policy_count,
    STRING_AGG(DISTINCT cmd, ', ') as operations
FROM pg_policies 
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Log the migration completion
SELECT log_admin_action(
    'enhanced_rls_migration_complete',
    'database_schema',
    NULL,
    '{"version": "2.0", "security_level": "enhanced"}'::jsonb,
    NULL,
    'supabase_migration_system',
    NULL,
    true,
    NULL
);

COMMENT ON TABLE public.admin_audit_logs IS 'Comprehensive audit logging for all admin operations';
COMMENT ON TABLE public.admin_sessions IS 'Enhanced session management for admin users with time-based validation';
COMMENT ON FUNCTION validate_admin_access(TEXT, INTEGER) IS 'Validates admin permissions and session validity';
COMMENT ON FUNCTION log_admin_action(TEXT, TEXT, UUID, JSONB, INET, TEXT, INTEGER, BOOLEAN, TEXT) IS 'Logs admin actions for security audit trail';