import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { createClient } from '@/lib/supabase/server';
import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/api/unified-response');

describe('UnifiedAuthService', () => {
  let mockSupabase: any;
  let mockRequest: NextRequest;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
    };
    
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
    
    mockRequest = new NextRequest('http://localhost:3000/api/test');
  });

  describe('withAuth', () => {
    it('should allow authenticated users to access protected routes', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const handler = jest.fn().mockResolvedValue(new Response('Success'));
      const protectedHandler = UnifiedAuthService.withAuth(handler);

      const response = await protectedHandler(mockRequest);

      expect(mockSupabase.auth.getUser).toHaveBeenCalled();
      expect(handler).toHaveBeenCalledWith(mockRequest, { user: mockUser });
      expect(response).toBeDefined();
    });

    it('should reject unauthenticated requests', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const handler = jest.fn();
      const protectedHandler = UnifiedAuthService.withAuth(handler);

      await protectedHandler(mockRequest);

      expect(mockSupabase.auth.getUser).toHaveBeenCalled();
      expect(handler).not.toHaveBeenCalled();
    });

    it('should handle auth errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: null,
        error: new Error('Auth service unavailable'),
      });

      const handler = jest.fn();
      const protectedHandler = UnifiedAuthService.withAuth(handler);

      await protectedHandler(mockRequest);

      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('withProjectAccess', () => {
    it('should allow project owners to access their projects', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      const projectId = 'project-456';

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      mockSupabase.single.mockResolvedValue({
        data: { id: projectId, owner_id: mockUser.id },
        error: null,
      });

      const handler = jest.fn().mockResolvedValue(new Response('Success'));
      const protectedHandler = UnifiedAuthService.withProjectAccess(handler);

      mockRequest = new NextRequest(`http://localhost:3000/api/projects/${projectId}`);
      const response = await protectedHandler(mockRequest, { params: { id: projectId } });

      expect(mockSupabase.from).toHaveBeenCalledWith('projects');
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', projectId);
      expect(handler).toHaveBeenCalled();
    });

    it('should allow project members with appropriate roles', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      const projectId = 'project-456';

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      // First query returns project with different owner
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: projectId, owner_id: 'other-user' },
        error: null,
      });

      // Second query returns membership
      mockSupabase.single.mockResolvedValueOnce({
        data: { role: 'editor' },
        error: null,
      });

      const handler = jest.fn().mockResolvedValue(new Response('Success'));
      const protectedHandler = UnifiedAuthService.withProjectAccess(handler, ['editor', 'viewer']);

      mockRequest = new NextRequest(`http://localhost:3000/api/projects/${projectId}`);
      const response = await protectedHandler(mockRequest, { params: { id: projectId } });

      expect(handler).toHaveBeenCalled();
    });

    it('should reject users without project access', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      const projectId = 'project-456';

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      // Project with different owner
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: projectId, owner_id: 'other-user' },
        error: null,
      });

      // No membership found
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' },
      });

      const handler = jest.fn();
      const protectedHandler = UnifiedAuthService.withProjectAccess(handler);

      mockRequest = new NextRequest(`http://localhost:3000/api/projects/${projectId}`);
      await protectedHandler(mockRequest, { params: { id: projectId } });

      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('withRateLimit', () => {
    it('should allow requests within rate limit', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const handler = jest.fn().mockResolvedValue(new Response('Success'));
      const rateLimitedHandler = UnifiedAuthService.withRateLimit(handler, {
        requests: 10,
        windowMs: 60000,
      });

      // Make multiple requests within limit
      for (let i = 0; i < 5; i++) {
        await rateLimitedHandler(mockRequest);
      }

      expect(handler).toHaveBeenCalledTimes(5);
    });

    it('should reject requests exceeding rate limit', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const handler = jest.fn().mockResolvedValue(new Response('Success'));
      const rateLimitedHandler = UnifiedAuthService.withRateLimit(handler, {
        requests: 3,
        windowMs: 60000,
      });

      // Make requests up to limit
      for (let i = 0; i < 3; i++) {
        await rateLimitedHandler(mockRequest);
      }

      // This request should be rate limited
      await rateLimitedHandler(mockRequest);

      expect(handler).toHaveBeenCalledTimes(3);
    });
  });

  describe('withAdmin', () => {
    it('should allow admin users', async () => {
      const mockUser = { 
        id: 'admin-123', 
        email: '<EMAIL>',
        user_metadata: { role: 'admin' }
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const handler = jest.fn().mockResolvedValue(new Response('Success'));
      const adminHandler = UnifiedAuthService.withAdmin(handler);

      await adminHandler(mockRequest);

      expect(handler).toHaveBeenCalled();
    });

    it('should reject non-admin users', async () => {
      const mockUser = { 
        id: 'user-123', 
        email: '<EMAIL>',
        user_metadata: { role: 'user' }
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const handler = jest.fn();
      const adminHandler = UnifiedAuthService.withAdmin(handler);

      await adminHandler(mockRequest);

      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('validateRequest', () => {
    it('should validate request body against schema', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const schema = {
        parse: jest.fn().mockReturnValue({ name: 'Test', value: 123 }),
      };

      const handler = jest.fn().mockResolvedValue(new Response('Success'));
      const validatedHandler = UnifiedAuthService.validateRequest(handler, schema);

      mockRequest = new NextRequest('http://localhost:3000/api/test', {
        method: 'POST',
        body: JSON.stringify({ name: 'Test', value: 123 }),
      });

      await validatedHandler(mockRequest);

      expect(schema.parse).toHaveBeenCalled();
      expect(handler).toHaveBeenCalled();
    });

    it('should reject invalid request body', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const schema = {
        parse: jest.fn().mockImplementation(() => {
          throw new Error('Validation failed');
        }),
      };

      const handler = jest.fn();
      const validatedHandler = UnifiedAuthService.validateRequest(handler, schema);

      mockRequest = new NextRequest('http://localhost:3000/api/test', {
        method: 'POST',
        body: JSON.stringify({ invalid: 'data' }),
      });

      await validatedHandler(mockRequest);

      expect(schema.parse).toHaveBeenCalled();
      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('getUserFromRequest', () => {
    it('should extract user from request', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const user = await UnifiedAuthService.getUserFromRequest(mockRequest);

      expect(user).toEqual(mockUser);
    });

    it('should return null for unauthenticated requests', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const user = await UnifiedAuthService.getUserFromRequest(mockRequest);

      expect(user).toBeNull();
    });
  });
});