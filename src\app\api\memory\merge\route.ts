import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { UnifiedResponse } from '@/lib/utils/response'
import { getMemoryManager } from '@/lib/memory/memory-instances'
import { logger } from '@/lib/services/logger'

const mergeSchema = z.object({
  projectId: z.string().uuid(),
  contextIds: z.array(z.string()).optional(),
  threshold: z.number().min(0).max(1).optional().default(0.85),
  options: z.object({
    preserveOriginals: z.boolean().optional().default(false),
    mergeStrategy: z.enum(['combine', 'summarize', 'newest']).optional().default('combine'),
    maxMergeSize: z.number().optional().default(5)
  }).optional()
})

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const validation = mergeSchema.safeParse(body)
    
    if (!validation.success) {
      return UnifiedResponse.error('Invalid request data', 400, validation.error.errors)
    }

    const { projectId, contextIds, threshold, options } = validation.data
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Get memory manager
    const memoryManager = getMemoryManager(projectId)
    
    // Get stats before merging
    const statsBefore = memoryManager.getMemoryStats()
    
    // Perform merging
    const mergeResult = await memoryManager.mergeSimilarMemories({
      contextIds,
      similarityThreshold: threshold,
      ...options
    })
    
    // Get stats after merging
    const statsAfter = memoryManager.getMemoryStats()
    
    // Calculate impact
    const impact = {
      contextsBeforeMerge: statsBefore.contexts?.length || 0,
      contextsAfterMerge: statsAfter.contexts?.length || 0,
      tokensSaved: statsBefore.totalTokens - statsAfter.totalTokens,
      mergeGroups: mergeResult.mergeGroups || []
    }
    
    // Log merge event
    logger.info('Memory merge completed', {
      projectId,
      mergedCount: mergeResult.mergedCount,
      threshold,
      impact
    })
    
    return UnifiedResponse.success({
      message: `Successfully merged ${mergeResult.mergedCount} similar memory contexts`,
      mergeResult: {
        mergedCount: mergeResult.mergedCount,
        groupsProcessed: mergeResult.groupsProcessed || 0,
        errors: mergeResult.errors || []
      },
      stats: statsAfter,
      impact
    })
  } catch (error) {
    logger.error('Memory merge error:', error)
    return UnifiedResponse.error('Failed to merge memory contexts')
  }
})