'use client';

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react';
import { useDebounce } from '@/hooks/use-debounce';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { PaginationControls } from '@/components/ui/pagination';
import { 
  BookOpen, 
  Plus, 
  Search, 
  Users, 
  Globe, 
  TrendingUp,
  Calendar,
  FileText,
  Link as LinkIcon
} from 'lucide-react';
import { createClient } from '@/lib/supabase/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

import type { Series as SeriesType } from '@/lib/db/types';

interface SeriesWithRelations extends SeriesType {
  projects?: Array<{
    id: string;
    title: string;
    status: string;
    target_word_count: number;
  }>;
  series_books?: Array<{
    book_number: number;
    project_id: string;
    projects: {
      id: string;
      title: string;
      status: string;
      target_word_count: number;
    };
  }>;
}

interface PaginationData {
  total: number;
  limit: number;
  offset: number;
  page: number;
  totalPages: number;
}

export default function SeriesPage() {
  const [series, setSeries] = useState<SeriesWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    limit: 12,
    offset: 0,
    page: 1,
    totalPages: 1
  });
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newSeries, setNewSeries] = useState({ title: '', description: '' });
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const { user } = useAuth();
  const { toast } = useToast();
  const supabase = createClient();

  useEffect(() => {
    fetchSeries();
  }, [currentPage, debouncedSearchTerm]);

  const fetchSeries = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // First check if we're using the old or new schema
      const { data: testProject } = await supabase
        .from('projects')
        .select('series_id')
        .limit(1);
      
      const hasOldSchema = testProject && testProject[0] && 'series_id' in testProject[0];
      
      let query;
      if (hasOldSchema) {
        // Old schema - projects have series_id
        query = supabase
          .from('series')
          .select(`
            *,
            projects:projects!projects_series_id_fkey(id, title, status, target_word_count)
          `, { count: 'exact' })
          .eq('user_id', user.id);
      } else {
        // New schema - use series_books join table
        query = supabase
          .from('series')
          .select(`
            *,
            series_books (
              book_number,
              project_id,
              projects (id, title, status, target_word_count)
            )
          `, { count: 'exact' })
          .eq('user_id', user.id);
      }

      // Apply search filter
      if (debouncedSearchTerm) {
        query = query.or(`title.ilike.%${debouncedSearchTerm}%,description.ilike.%${debouncedSearchTerm}%`);
      }

      // Apply pagination
      const from = (currentPage - 1) * pagination.limit;
      const to = from + pagination.limit - 1;
      
      query = query
        .range(from, to)
        .order('updated_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) throw error;
      
      setSeries(data || []);
      
      // Update pagination
      const totalPages = Math.ceil((count || 0) / pagination.limit);
      setPagination({
        total: count || 0,
        limit: pagination.limit,
        offset: from,
        page: currentPage,
        totalPages
      });
    } catch (error) {
      logger.error('Error fetching series:', error);
      toast({
        title: 'Error',
        description: 'Failed to load series. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSeries = async () => {
    if (!user || !newSeries.title.trim()) return;

    try {
      const { data, error } = await supabase
        .from('series')
        .insert([{
          user_id: user.id,
          title: newSeries.title.trim(),
          description: newSeries.description.trim(),
        }])
        .select()
        .single();

      if (error) throw error;

      setSeries(prev => [data, ...prev]);
      setNewSeries({ title: '', description: '' });
      setIsCreateModalOpen(false);
      
      toast({
        title: 'Success',
        description: `Series "${data.title}" created successfully.`,
      });
    } catch (error) {
      logger.error('Error creating series:', error);
      toast({
        title: 'Error',
        description: 'Failed to create series. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Helper functions to handle both old and new schema
  const getProjects = (seriesItem: SeriesWithRelations) => {
    if (seriesItem.projects) {
      // Old schema
      return seriesItem.projects;
    } else if (seriesItem.series_books) {
      // New schema - extract projects from series_books
      return seriesItem.series_books
        .sort((a, b) => a.book_number - b.book_number)
        .map(sb => sb.projects)
        .filter(Boolean);
    }
    return [];
  };

  const getBookCount = (seriesItem: SeriesWithRelations) => {
    return getProjects(seriesItem).length;
  };

  // Since filtering is now handled server-side, we can use series directly
  const filteredSeries = series;

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading your series...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Series Management</h1>
          <p className="text-muted-foreground mt-2">
            Organize your projects into multi-book series and manage continuity across your literary universe.
            {pagination.total > 0 && ` (${pagination.total} total)`}
          </p>
        </div>
        
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Create Series
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Series</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="series-title">Series Title</Label>
                <Input
                  id="series-title"
                  value={newSeries.title}
                  onChange={(e) => setNewSeries(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter series title..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="series-description">Description</Label>
                <Textarea
                  id="series-description"
                  value={newSeries.description}
                  onChange={(e) => setNewSeries(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your series..."
                  rows={3}
                />
              </div>
              <div className="flex justify-end gap-2 pt-4">
                <Button 
                  variant="outline" 
                  onClick={() => setIsCreateModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateSeries}
                  disabled={!newSeries.title.trim()}
                >
                  Create Series
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search series..."
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Series Grid */}
      {filteredSeries.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {series.length === 0 ? 'No series yet' : 'No series found'}
          </h3>
          <p className="text-muted-foreground mb-6">
            {series.length === 0 
              ? 'Create your first series to organize multiple books in a shared universe.'
              : 'Try adjusting your search terms.'
            }
          </p>
          {series.length === 0 && (
            <Button onClick={() => setIsCreateModalOpen(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              Create Your First Series
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSeries.map((seriesItem) => (
            <Card key={seriesItem.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-1">{seriesItem.title}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {seriesItem.description || 'No description provided'}
                    </CardDescription>
                  </div>
                  <Badge variant="secondary" className="ml-2">
                    {getBookCount(seriesItem)} books
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                {/* Project List */}
                {getProjects(seriesItem).length > 0 ? (
                  <div className="space-y-2 mb-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Books in Series:</h4>
                    {getProjects(seriesItem).slice(0, 3).map((project) => (
                      <div key={project.id} className="flex items-center gap-2 text-sm">
                        <FileText className="h-3 w-3 text-muted-foreground" />
                        <Link 
                          href={`/projects/${project.id}`}
                          className="flex-1 hover:text-primary transition-colors"
                        >
                          {project.title}
                        </Link>
                        <Badge variant="outline" className="text-xs">
                          {project.status}
                        </Badge>
                      </div>
                    ))}
                    {getProjects(seriesItem).length > 3 && (
                      <p className="text-xs text-muted-foreground">
                        +{getProjects(seriesItem).length - 3} more books
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-4 text-sm text-muted-foreground">
                    No books in this series yet
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex-1 gap-2"
                    asChild
                  >
                    <Link href={`/series/${seriesItem.id}`}>
                      <Globe className="h-3 w-3" />
                      Manage
                    </Link>
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex-1 gap-2"
                    asChild
                  >
                    <Link href={`/series/${seriesItem.id}/continuity`}>
                      <TrendingUp className="h-3 w-3" />
                      Continuity
                    </Link>
                  </Button>
                </div>

                {/* Metadata */}
                <div className="flex items-center gap-4 mt-4 pt-4 border-t text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    Created {new Date(seriesItem.created_at).toLocaleDateString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Stats Footer */}
      {series.length > 0 && (
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <BookOpen className="h-8 w-8 text-primary" />
                <div>
                  <p className="text-2xl font-bold">{series.length}</p>
                  <p className="text-sm text-muted-foreground">Total Series</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-primary" />
                <div>
                  <p className="text-2xl font-bold">
                    {series.reduce((total, s) => total + getBookCount(s), 0)}
                  </p>
                  <p className="text-sm text-muted-foreground">Books in Series</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Globe className="h-8 w-8 text-primary" />
                <div>
                  <p className="text-2xl font-bold">
                    {series.filter(s => getBookCount(s) > 1).length}
                  </p>
                  <p className="text-sm text-muted-foreground">Multi-Book Series</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Pagination Controls */}
      {pagination.totalPages > 1 && (
        <div className="mt-8">
          <PaginationControls
            currentPage={currentPage}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
            showInfo={true}
            totalItems={pagination.total}
            itemsPerPage={pagination.limit}
          />
        </div>
      )}
    </div>
  );
}