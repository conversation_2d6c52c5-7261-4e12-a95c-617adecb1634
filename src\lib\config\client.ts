import { z } from 'zod';

import { logger } from '@/lib/services/logger';
import { TIME_MS } from '@/lib/constants'

const clientEnvSchema = z.object({
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:TIME_MS.TYPING_TIMEOUT'),
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  STRIPE_PUBLISHABLE_KEY: z.string().min(1),
});

type ClientEnvConfig = z.infer<typeof clientEnvSchema>;

class ClientConfig {
  private static instance: ClientConfig;
  private config: ClientEnvConfig;

  private constructor() {
    const env = {
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || process.env.STRIPE_PUBLISHABLE_KEY,
    };

    try {
      this.config = clientEnvSchema.parse(env);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const missingVars = error.errors.map(err => err.path.join('.')).join(', ');
        logger.error(`Missing or invalid client environment variables: ${missingVars}`);
        throw new Error('Configuration error');
      }
      throw error;
    }
  }

  static getInstance(): ClientConfig {
    if (!ClientConfig.instance) {
      ClientConfig.instance = new ClientConfig();
    }
    return ClientConfig.instance;
  }

  get env(): ClientEnvConfig {
    return this.config;
  }

  get supabase() {
    return {
      url: this.config.NEXT_PUBLIC_SUPABASE_URL,
      anonKey: this.config.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    };
  }

  get stripe() {
    return {
      publishableKey: this.config.STRIPE_PUBLISHABLE_KEY,
    };
  }

  get app() {
    return {
      url: this.config.NEXT_PUBLIC_APP_URL,
    };
  }
}

export const clientConfig = ClientConfig.getInstance();
export type { ClientEnvConfig };