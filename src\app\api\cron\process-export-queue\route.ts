import { NextRequest } from 'next/server'
import { UnifiedResponse } from '@/lib/utils/response'
import { logger } from '@/lib/services/logger'
import { ExportQueueService } from '@/lib/services/export-queue-service'

export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return UnifiedResponse.error('Unauthorized', 401)
    }

    // Start the queue processor
    ExportQueueService.startProcessor()

    // Clean up old jobs (older than 30 days)
    await ExportQueueService.cleanupOldJobs(30)

    return UnifiedResponse.success({ 
      message: 'Export queue processor started',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Export queue cron error:', error)
    return UnifiedResponse.error('Failed to process export queue')
  }
}

// Also export POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}