import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { z } from 'zod'

// Query params schema
const historyQuerySchema = z.object({
  limit: z.string().regex(/^\d+$/).transform(Number).pipe(z.number().min(1).max(100)).optional().default('20'),
  startingAfter: z.string().optional(),
  endingBefore: z.string().optional(),
  status: z.enum(['draft', 'open', 'paid', 'uncollectible', 'void']).optional()
});

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    limit: searchParams.get('limit'),
    startingAfter: searchParams.get('startingAfter'),
    endingBefore: searchParams.get('endingBefore'),
    status: searchParams.get('status')
  };

  // Validate query parameters
  const parseResult = historyQuerySchema.safeParse(queryParams);
  if (!parseResult.success) {
    return UnifiedResponse.error('Invalid query parameters', 400, parseResult.error.errors);
  }

  const { limit, startingAfter, endingBefore, status } = parseResult.data;

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'billing-history',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const supabase = await createTypedServerClient();

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (!profile?.stripe_customer_id) {
      logger.info('No Stripe customer ID found for user', {
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.success({ 
        invoices: [],
        hasMore: false,
        message: 'No billing history available'
      });
    }

    // Build Stripe query params
    const stripeParams: any = {
      customer: profile.stripe_customer_id,
      limit,
      expand: ['data.subscription', 'data.charge']
    };

    if (startingAfter) stripeParams.starting_after = startingAfter;
    if (endingBefore) stripeParams.ending_before = endingBefore;
    if (status) stripeParams.status = status;

    // Fetch invoices from Stripe
    const invoices = await stripe.invoices.list(stripeParams);

    // Transform invoice data for frontend
    const billingHistory = invoices.data.map(invoice => {
      const charge = typeof invoice.charge === 'object' ? invoice.charge : null;
      const subscription = typeof invoice.subscription === 'object' ? invoice.subscription : null;
      
      return {
        id: invoice.id,
        number: invoice.number,
        date: new Date(invoice.created * TIME_MS.SECOND).toISOString(),
        dueDate: invoice.due_date ? new Date(invoice.due_date * TIME_MS.SECOND).toISOString() : null,
        amount: invoice.amount_paid,
        amountDue: invoice.amount_due,
        currency: invoice.currency,
        status: invoice.status,
        paid: invoice.paid,
        invoicePdf: invoice.invoice_pdf,
        hostedInvoiceUrl: invoice.hosted_invoice_url,
        description: invoice.description || invoice.lines.data[0]?.description || 'Subscription',
        periodStart: invoice.period_start ? new Date(invoice.period_start * TIME_MS.SECOND).toISOString() : null,
        periodEnd: invoice.period_end ? new Date(invoice.period_end * TIME_MS.SECOND).toISOString() : null,
        paymentMethod: charge?.payment_method_details?.card ? {
          brand: charge.payment_method_details.card.brand,
          last4: charge.payment_method_details.card.last4
        } : null,
        subscriptionId: subscription?.id || null,
        lineItems: invoice.lines.data.map(item => ({
          id: item.id,
          description: item.description || 'Subscription item',
          amount: item.amount,
          quantity: item.quantity || 1,
          period: item.period ? {
            start: new Date(item.period.start * TIME_MS.SECOND).toISOString(),
            end: new Date(item.period.end * TIME_MS.SECOND).toISOString()
          } : null
        }))
      };
    });

    logger.info('Billing history retrieved', {
      userId: user.id,
      invoiceCount: billingHistory.length,
      hasMore: invoices.has_more,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      invoices: billingHistory,
      hasMore: invoices.has_more,
      nextCursor: invoices.has_more && billingHistory.length > 0 ? billingHistory[billingHistory.length - 1].id : null,
      previousCursor: billingHistory.length > 0 ? billingHistory[0].id : null
    });

  } catch (error) {
    logger.error('Billing history fetch error:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    
    if (error instanceof Error && error.message.includes('stripe')) {
      return UnifiedResponse.error(
        'Unable to retrieve billing history from payment provider',
        503
      );
    }
    
    return UnifiedResponse.error('Failed to fetch billing history');
  }
});