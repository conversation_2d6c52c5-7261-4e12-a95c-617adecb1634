import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (!profile?.stripe_customer_id) {
      return NextResponse.json({ invoices: [] })
    }

    // Fetch invoices from Stripe
    const invoices = await stripe.invoices.list({
      customer: profile.stripe_customer_id,
      limit: 20,
      expand: ['data.subscription'],
    })

    // Transform invoice data for frontend
    const billingHistory = invoices.data.map(invoice => ({
      id: invoice.id,
      date: new Date(invoice.created * TIME_MS.SECOND).toISOString(),
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: invoice.status,
      paid: invoice.paid,
      invoicePdf: invoice.invoice_pdf,
      hostedInvoiceUrl: invoice.hosted_invoice_url,
      description: invoice.description || invoice.lines.data[0]?.description || 'Subscription',
      periodStart: invoice.period_start ? new Date(invoice.period_start * TIME_MS.SECOND).toISOString() : null,
      periodEnd: invoice.period_end ? new Date(invoice.period_end * TIME_MS.SECOND).toISOString() : null,
    }))

    return NextResponse.json({ invoices: billingHistory })

  } catch (error) {
    logger.error('Billing history fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch billing history' },
      { status: 500 }
    )
  }
}