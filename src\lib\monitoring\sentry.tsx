import * as Sentry from '@sentry/nextjs';
import { CaptureContext, SeverityLevel } from '@sentry/types';
import { formatErrorForLogging, getUserFriendlyError } from '../config/error-messages';
import { MONITORING } from '@/lib/constants'

// Sentry configuration
export const initSentry = () => {
  const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;
  const environment = process.env.NODE_ENV;
  const isProduction = environment === 'production';

  if (!SENTRY_DSN) {
    console.warn('Sentry DSN not configured. Error tracking disabled.');
    return;
  }

  Sentry.init({
    dsn: SENTRY_DSN,
    environment,
    enabled: isProduction,
    
    // Performance Monitoring
    tracesSampleRate: isProduction ? MONITORING.TRACES_SAMPLE_RATE_PROD : MONITORING.TRACES_SAMPLE_RATE_DEV, // 10% in production, 100% in dev
    
    // Session Replay
    replaysSessionSampleRate: MONITORING.REPLAY_SESSION_SAMPLE_RATE, // 10% of sessions
    replaysOnErrorSampleRate: 1.0, // 100% of sessions with errors
    
    // Release tracking
    release: process.env.NEXT_PUBLIC_APP_VERSION,
    
    // Integrations
    integrations: [
      new Sentry.BrowserTracing({
        // Set sampling rate for performance monitoring
        tracingOrigins: ['localhost', process.env.NEXT_PUBLIC_APP_URL || '', /^\//],
        
        // Automatic route change tracking for Next.js
        routingInstrumentation: Sentry.nextRouterInstrumentation,
      }),
      new Sentry.Replay({
        // Mask sensitive content
        maskAllText: false,
        maskAllInputs: true,
        blockAllMedia: false,
        
        // Privacy settings
        privacy: {
          maskTextContent: false,
          maskInputOptions: {
            password: true,
            email: true,
            tel: true,
          },
        },
      }),
    ],
    
    // Filtering
    ignoreErrors: [
      // Browser errors
      'ResizeObserver loop limit exceeded',
      'ResizeObserver loop completed with undelivered notifications',
      'Non-Error promise rejection captured',
      
      // Network errors that are expected
      'NetworkError',
      'Failed to fetch',
      'Load failed',
      
      // User-caused errors
      'User cancelled',
      'The user aborted a request',
      
      // Extension errors
      'Extension context invalidated',
      'chrome-extension://',
      'moz-extension://',
    ],
    
    // Before send hook for filtering and enrichment
    beforeSend(event, hint) {
      // Don't send events in development unless explicitly enabled
      if (!isProduction && process.env.NEXT_PUBLIC_SENTRY_DEBUG !== 'true') {
        return null;
      }
      
      // Filter out non-actionable errors
      if (event.exception) {
        const error = hint.originalException;
        
        // Skip expected errors
        if (error && typeof error === 'object' && 'code' in error) {
          const errorCode = (error as any).code;
          if (['NEXT_NOT_FOUND', 'NEXT_REDIRECT'].includes(errorCode)) {
            return null;
          }
        }
      }
      
      // Add user-friendly error message
      if (hint.originalException) {
        event.extra = {
          ...event.extra,
          userFriendlyMessage: getUserFriendlyError(hint.originalException),
          errorDetails: formatErrorForLogging(hint.originalException),
        };
      }
      
      // Remove sensitive data
      if (event.request) {
        if (event.request.cookies) {
          event.request.cookies = '[Filtered]';
        }
        if (event.request.headers) {
          const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
          sensitiveHeaders.forEach(header => {
            if (event.request?.headers?.[header]) {
              event.request.headers[header] = '[Filtered]';
            }
          });
        }
      }
      
      return event;
    },
    
    // Breadcrumb filtering
    beforeBreadcrumb(breadcrumb) {
      // Filter out noisy breadcrumbs
      if (breadcrumb.category === 'console' && breadcrumb.level === 'log') {
        return null;
      }
      
      // Don't track navigation to auth pages (privacy)
      if (breadcrumb.category === 'navigation' && 
          breadcrumb.data?.to?.includes('/auth/')) {
        return null;
      }
      
      return breadcrumb;
    },
  });
};

// Error tracking utilities
export const trackError = (
  error: Error | unknown,
  context?: CaptureContext,
  level: SeverityLevel = 'error'
) => {
  if (!error) return;
  
  const errorObj = error instanceof Error ? error : new Error(String(error));
  
  Sentry.captureException(errorObj, {
    ...context,
    level,
    extra: {
      ...context?.extra,
      errorDetails: formatErrorForLogging(error),
      userFriendlyMessage: getUserFriendlyError(error),
    },
  });
};

// Track specific error types
export const trackAPIError = (
  endpoint: string,
  error: Error | unknown,
  statusCode?: number,
  requestData?: Record<string, unknown>
) => {
  trackError(error, {
    tags: {
      type: 'api_error',
      endpoint,
      statusCode: String(statusCode || 'unknown'),
    },
    extra: {
      requestData: requestData || {},
    },
  });
};

export const trackAIError = (
  operation: string,
  error: Error | unknown,
  model?: string,
  tokensUsed?: number
) => {
  trackError(error, {
    tags: {
      type: 'ai_error',
      operation,
      model: model || 'unknown',
    },
    extra: {
      tokensUsed,
    },
  });
};

export const trackDatabaseError = (
  operation: string,
  table: string,
  error: Error | unknown,
  query?: string
) => {
  trackError(error, {
    tags: {
      type: 'database_error',
      operation,
      table,
    },
    extra: {
      query: query || 'Not provided',
    },
  });
};

// Performance tracking
export const trackPerformance = (
  operation: string,
  duration: number,
  metadata?: Record<string, unknown>
) => {
  const transaction = Sentry.getCurrentHub().getScope()?.getTransaction();
  
  if (transaction) {
    const span = transaction.startChild({
      op: operation,
      description: `Performance: ${operation}`,
    });
    
    span.setData('duration', duration);
    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        span.setData(key, value);
      });
    }
    
    span.finish();
  }
};

// User context
export const setUserContext = (user: {
  id: string;
  email?: string;
  username?: string;
  subscription?: string;
}) => {
  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.username,
    subscription: user.subscription,
  });
};

export const clearUserContext = () => {
  Sentry.setUser(null);
};

// Custom breadcrumbs
export const addBreadcrumb = (
  message: string,
  category: string,
  level: Sentry.SeverityLevel = 'info',
  data?: Record<string, unknown>
) => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    data,
    timestamp: Date.now() / TIME_MS.SECOND,
  });
};

// Feature tracking
export const trackFeatureUsage = (
  feature: string,
  metadata?: Record<string, unknown>
) => {
  addBreadcrumb(`Feature used: ${feature}`, 'feature', 'info', metadata);
  
  // Also track as custom event for analytics
  Sentry.captureMessage(`Feature: ${feature}`, {
    level: 'info',
    tags: {
      type: 'feature_usage',
      feature,
    },
    extra: metadata,
  });
};

// Error boundary integration
export const ErrorBoundary = Sentry.ErrorBoundary;

export const withErrorBoundary = <P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  errorBoundaryOptions?: Sentry.ErrorBoundaryOptions
) => {
  return Sentry.withErrorBoundary(Component, {
    fallback: ({ error, resetError }) => (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <h2 className="text-2xl font-bold mb-4">Something went wrong</h2>
        <p className="text-muted-foreground mb-6">
          {getUserFriendlyError(error)}
        </p>
        <button
          onClick={resetError}
          className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
        >
          Try again
        </button>
      </div>
    ),
    showDialog: false,
    ...errorBoundaryOptions,
  });
};

// Profiler integration
export const withProfiler = <P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  name: string
) => {
  return Sentry.withProfiler(Component, { name });
};

// Export Sentry instance for direct access if needed
export { Sentry };