# BookScribe Environment Variables & Configuration

## Table of Contents
- [Overview](#overview)
- [Environment Variable Reference](#environment-variable-reference)
- [Configuration Files](#configuration-files)
- [Environment-Specific Settings](#environment-specific-settings)
- [Security Considerations](#security-considerations)
- [Validation & Type Safety](#validation--type-safety)

## Overview

BookScribe uses environment variables for configuration management, following the 12-factor app methodology. All sensitive configuration is stored in environment variables, while non-sensitive defaults are in configuration files.

## Environment Variable Reference

### Core Configuration

#### Database & Authentication
```bash
# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=https://xxxxxxxxxxxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Database URL (for migrations and scripts)
DATABASE_URL=postgresql://postgres:[password]@db.[project-id].supabase.co:5432/postgres
```

#### AI Configuration
```bash
# OpenAI (Required for AI features)
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Alternative AI Providers (Optional)
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxx
GOOGLE_GEMINI_API_KEY=xxxxxxxxxxxxxxxxxxxxx
XAI_API_KEY=xxxxxxxxxxxxxxxxxxxxx

# AI Configuration
AI_DEFAULT_MODEL=gpt-4-turbo
AI_FALLBACK_MODEL=gpt-4o-mini
AI_MAX_RETRIES=3
AI_TIMEOUT_MS=30000
```

#### Payment Processing
```bash
# Stripe (Required for payments)
STRIPE_SECRET_KEY=sk_live_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_xxxxxxxxxxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxx

# Stripe Price IDs
STRIPE_PRICE_STARTER_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_STARTER_YEARLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_PROFESSIONAL_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_PROFESSIONAL_YEARLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_ENTERPRISE_MONTHLY=price_xxxxxxxxxxxxx
```

### Application Configuration

#### URLs & Domains
```bash
# Application URLs
NEXT_PUBLIC_APP_URL=https://bookscribe.ai
NEXT_PUBLIC_API_URL=https://bookscribe.ai/api
NEXT_PUBLIC_MARKETING_URL=https://bookscribe.ai

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>
```

#### Feature Flags
```bash
# Feature Toggles
NEXT_PUBLIC_ENABLE_DEMO_MODE=true
NEXT_PUBLIC_ENABLE_COLLABORATION=true
NEXT_PUBLIC_ENABLE_VOICE_FEATURES=true
NEXT_PUBLIC_ENABLE_UNIVERSE_SHARING=true
NEXT_PUBLIC_ENABLE_ACHIEVEMENTS=true

# Development Features
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
NEXT_PUBLIC_SHOW_DEBUG_INFO=false
```

#### Analytics & Monitoring
```bash
# Sentry Error Tracking
SENTRY_DSN=https://<EMAIL>/xxxxx
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/xxxxx
SENTRY_ORG=bookscribe
SENTRY_PROJECT=bookscribe-app
SENTRY_AUTH_TOKEN=xxxxxxxxxxxxxxxxxxxxx

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_POSTHOG_KEY=phc_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

### Storage & CDN
```bash
# Supabase Storage
NEXT_PUBLIC_STORAGE_BUCKET=bookscribe-assets
NEXT_PUBLIC_STORAGE_URL=https://xxxxxxxxxxxx.supabase.co/storage/v1

# CDN Configuration
CDN_URL=https://cdn.bookscribe.ai
IMAGE_OPTIMIZATION_URL=https://images.bookscribe.ai
```

### Rate Limiting & Security
```bash
# Rate Limiting
RATE_LIMIT_WINDOW_MS=3600000
RATE_LIMIT_MAX_REQUESTS_FREE=100
RATE_LIMIT_MAX_REQUESTS_STARTER=1000
RATE_LIMIT_MAX_REQUESTS_PRO=10000

# Security
JWT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ENCRYPTION_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
SESSION_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# CORS
CORS_ALLOWED_ORIGINS=https://bookscribe.ai,https://app.bookscribe.ai
```

### Development & Testing
```bash
# Development
NODE_ENV=development
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_TELEMETRY_DISABLED=1

# Testing
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/bookscribe_test
TEST_STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxx
PLAYWRIGHT_TEST_URL=http://localhost:3000
```

## Configuration Files

### Environment File Structure
```
BookScribe/
├── .env                    # Default environment variables
├── .env.local             # Local overrides (git ignored)
├── .env.development       # Development environment
├── .env.test             # Test environment
├── .env.production       # Production environment
└── .env.example          # Example template for developers
```

### Example .env.example
```bash
# ===============================================
# BookScribe Environment Configuration
# ===============================================
# Copy this file to .env.local and fill in values

# --- Database & Auth (Required) ---
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# --- AI Services (Required) ---
OPENAI_API_KEY=

# --- Payments (Required for production) ---
STRIPE_SECRET_KEY=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_WEBHOOK_SECRET=

# --- Optional Services ---
# ANTHROPIC_API_KEY=
# SENTRY_DSN=

# --- Development ---
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
NEXT_PUBLIC_DEMO_MODE=false
```

### Unified Environment Configuration
```typescript
// src/lib/config/unified-env.ts
import { z } from 'zod'

const envSchema = z.object({
  // Database
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1),
  
  // AI
  OPENAI_API_KEY: z.string().startsWith('sk-'),
  
  // Payments
  STRIPE_SECRET_KEY: z.string().startsWith('sk_'),
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().startsWith('pk_'),
  
  // Optional
  SENTRY_DSN: z.string().url().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  
  // Feature Flags
  NEXT_PUBLIC_ENABLE_DEMO_MODE: z.enum(['true', 'false']).transform(v => v === 'true'),
  NEXT_PUBLIC_DEV_BYPASS_AUTH: z.enum(['true', 'false']).transform(v => v === 'true'),
})

export function validateEnv() {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    console.error('❌ Invalid environment variables:', error)
    throw new Error('Invalid environment configuration')
  }
}

export const env = validateEnv()
```

## Environment-Specific Settings

### Development Environment
```bash
# .env.development
NODE_ENV=development
NEXT_PUBLIC_ENVIRONMENT=development

# Use test Stripe keys
STRIPE_SECRET_KEY=sk_test_xxxxx
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_xxxxx

# Enable dev features
NEXT_PUBLIC_DEV_BYPASS_AUTH=true
NEXT_PUBLIC_SHOW_DEBUG_INFO=true
NEXT_PUBLIC_ENABLE_DEMO_MODE=true

# Lower rate limits for testing
RATE_LIMIT_MAX_REQUESTS_FREE=10
```

### Production Environment
```bash
# .env.production
NODE_ENV=production
NEXT_PUBLIC_ENVIRONMENT=production

# Production services only
STRIPE_SECRET_KEY=sk_live_xxxxx
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_xxxxx

# Disable dev features
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
NEXT_PUBLIC_SHOW_DEBUG_INFO=false

# Production URLs
NEXT_PUBLIC_APP_URL=https://bookscribe.ai
```

### Test Environment
```bash
# .env.test
NODE_ENV=test
NEXT_PUBLIC_ENVIRONMENT=test

# Test database
DATABASE_URL=postgresql://postgres:password@localhost:5432/bookscribe_test

# Mock external services
OPENAI_API_KEY=sk-test-mock-key
STRIPE_SECRET_KEY=sk_test_mock_key

# Disable external calls
DISABLE_EXTERNAL_APIS=true
```

## Security Considerations

### Secret Management

#### Never Commit Secrets
```gitignore
# .gitignore
.env.local
.env.production
.env*.local
```

#### Secret Rotation Policy
1. **API Keys**: Rotate every 90 days
2. **Database Passwords**: Rotate every 60 days
3. **JWT Secrets**: Rotate every 180 days
4. **Webhook Secrets**: Rotate when updating webhooks

### Access Control

#### Environment Variable Access
```typescript
// Server-only variables
const serverOnlyVars = [
  'SUPABASE_SERVICE_ROLE_KEY',
  'STRIPE_SECRET_KEY',
  'OPENAI_API_KEY',
  'DATABASE_URL'
]

// Validate server-only access
export function getServerEnv(key: string) {
  if (typeof window !== 'undefined') {
    throw new Error(`Cannot access ${key} on client side`)
  }
  return process.env[key]
}
```

#### Client-Safe Variables
Variables prefixed with `NEXT_PUBLIC_` are exposed to the client bundle. Only include non-sensitive configuration.

### Encryption

#### Encrypting Sensitive Values
```typescript
// src/lib/utils/crypto.ts
import crypto from 'crypto'

const algorithm = 'aes-256-gcm'
const key = Buffer.from(process.env.ENCRYPTION_KEY!, 'hex')

export function encrypt(text: string): string {
  const iv = crypto.randomBytes(16)
  const cipher = crypto.createCipheriv(algorithm, key, iv)
  
  let encrypted = cipher.update(text, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  const authTag = cipher.getAuthTag()
  
  return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted
}

export function decrypt(text: string): string {
  const parts = text.split(':')
  const iv = Buffer.from(parts[0], 'hex')
  const authTag = Buffer.from(parts[1], 'hex')
  const encrypted = parts[2]
  
  const decipher = crypto.createDecipheriv(algorithm, key, iv)
  decipher.setAuthTag(authTag)
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  
  return decrypted
}
```

## Validation & Type Safety

### Runtime Validation
```typescript
// src/lib/config/config-validator.ts
export class ConfigValidator {
  static validate(): void {
    const required = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'OPENAI_API_KEY'
    ]
    
    const missing = required.filter(key => !process.env[key])
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
    }
    
    // Validate formats
    this.validateUrls()
    this.validateApiKeys()
  }
  
  private static validateUrls(): void {
    const urlVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_APP_URL'
    ]
    
    urlVars.forEach(key => {
      const value = process.env[key]
      if (value && !isValidUrl(value)) {
        throw new Error(`Invalid URL format for ${key}`)
      }
    })
  }
  
  private static validateApiKeys(): void {
    // OpenAI key format
    if (process.env.OPENAI_API_KEY && 
        !process.env.OPENAI_API_KEY.startsWith('sk-')) {
      throw new Error('Invalid OpenAI API key format')
    }
    
    // Stripe key format
    if (process.env.STRIPE_SECRET_KEY) {
      const isTest = process.env.NODE_ENV !== 'production'
      const prefix = isTest ? 'sk_test_' : 'sk_live_'
      
      if (!process.env.STRIPE_SECRET_KEY.startsWith(prefix)) {
        throw new Error(`Stripe key should start with ${prefix}`)
      }
    }
  }
}
```

### Type-Safe Configuration
```typescript
// src/lib/config/app-config.ts
export const config = {
  app: {
    name: 'BookScribe',
    url: process.env.NEXT_PUBLIC_APP_URL!,
    environment: process.env.NODE_ENV as 'development' | 'production' | 'test'
  },
  
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceRole: getServerEnv('SUPABASE_SERVICE_ROLE_KEY')
  },
  
  ai: {
    openai: {
      apiKey: getServerEnv('OPENAI_API_KEY'),
      defaultModel: process.env.AI_DEFAULT_MODEL || 'gpt-4-turbo',
      maxRetries: parseInt(process.env.AI_MAX_RETRIES || '3')
    }
  },
  
  features: {
    demoMode: process.env.NEXT_PUBLIC_ENABLE_DEMO_MODE === 'true',
    collaboration: process.env.NEXT_PUBLIC_ENABLE_COLLABORATION === 'true',
    achievements: process.env.NEXT_PUBLIC_ENABLE_ACHIEVEMENTS === 'true'
  },
  
  limits: {
    free: {
      requests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS_FREE || '100'),
      aiGenerations: 10,
      projects: 1
    },
    starter: {
      requests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS_STARTER || '1000'),
      aiGenerations: 100,
      projects: 5
    }
  }
} as const

export type AppConfig = typeof config
```

## Loading Configuration

### Application Startup
```typescript
// src/app/layout.tsx
import { ConfigValidator } from '@/lib/config/config-validator'

// Validate environment on startup
if (typeof window === 'undefined') {
  ConfigValidator.validate()
}
```

### Next.js Configuration
```javascript
// next.config.js
module.exports = {
  env: {
    // Computed environment variables
    NEXT_PUBLIC_BUILD_TIME: new Date().toISOString(),
    NEXT_PUBLIC_BUILD_ID: process.env.VERCEL_GIT_COMMIT_SHA || 'local'
  },
  
  // Validate during build
  webpack: (config, { isServer }) => {
    if (isServer) {
      require('./src/lib/config/config-validator').ConfigValidator.validate()
    }
    return config
  }
}
```

## Best Practices

### 1. Environment Variable Naming
- Use `NEXT_PUBLIC_` prefix for client-side variables
- Use SCREAMING_SNAKE_CASE
- Group related variables with common prefixes

### 2. Default Values
```typescript
// Provide sensible defaults
const timeout = parseInt(process.env.API_TIMEOUT || '30000')
const retries = parseInt(process.env.MAX_RETRIES || '3')
```

### 3. Documentation
- Always update `.env.example` when adding new variables
- Document purpose and format of each variable
- Include example values (not real secrets)

### 4. Validation
- Validate all environment variables on startup
- Use TypeScript for type safety
- Fail fast with clear error messages

### 5. Security
- Never log sensitive environment variables
- Use different values for each environment
- Rotate secrets regularly
- Use secret management services in production