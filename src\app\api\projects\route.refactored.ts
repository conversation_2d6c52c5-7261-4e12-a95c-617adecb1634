/**
 * Refactored Projects API Route
 * This is an example of how to use the new shared utilities
 * to reduce duplication and improve maintainability
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createClient } from '@/lib/supabase'
import { 
  createAPIHandler, 
  createSuccessResponse, 
  createPaginatedResponse,
  parsePaginationParams,
  parseRequestBody,
  commonSchemas 
} from '@/lib/utils/api-utils'
import { 
  requireAuth, 
  requireSubscriptionTier 
} from '@/lib/middleware/auth-middleware'
import { API_ENDPOINTS } from '@/lib/config/api-endpoints'
import { API_TIMEOUTS } from '@/lib/config/network-config'
import { 
  projectNameSchema, 
  projectStatusSchema,
  genreSchema,
  writingStyleSchema,
  povSchema,
  tenseSchema
} from '@/lib/validation/common-schemas'
import { logger } from '@/lib/services/logger'
import { aiProcessingQueue } from '@/lib/services/ai-processing-queue'
import type { ProjectSettings } from '@/lib/types/project-settings'
import { TIME_SECONDS } from '@/lib/constants'

// Project creation schema using common schemas
const createProjectSchema = z.object({
  settings: z.object({
    projectName: projectNameSchema,
    description: commonSchemas.description,
    primaryGenre: genreSchema,
    subgenre: genreSchema.optional(),
    customGenre: z.string().optional(),
    narrativeVoice: povSchema.optional(),
    tense: tenseSchema.optional(),
    tone: z.array(z.string()).optional(),
    writingStyle: writingStyleSchema.optional(),
    customStyleDescription: z.string().optional(),
    structureType: z.string().optional(),
    pacingPreference: z.string().optional(),
    chapterStructure: z.string().optional(),
    timelineComplexity: z.string().optional(),
    customStructureNotes: z.string().optional(),
    protagonistTypes: z.array(z.string()).optional(),
    antagonistTypes: z.array(z.string()).optional(),
    characterComplexity: z.string().optional(),
    characterArcTypes: z.array(z.string()).optional(),
    customCharacterConcepts: z.string().optional(),
    timePeriod: z.string().optional(),
    geographicSetting: z.string().optional(),
    worldType: z.string().optional(),
    magicTechLevel: z.string().optional(),
    customSettingDescription: z.string().optional(),
    majorThemes: z.array(z.string()).optional(),
    philosophicalThemes: z.array(z.string()).optional(),
    socialThemes: z.array(z.string()).optional(),
    customThemes: z.string().optional(),
    targetAudience: z.string().min(1, 'Target audience is required'),
    contentRating: z.string().optional(),
    initialConcept: z.string().optional(),
    contentWarnings: z.array(z.string()).optional(),
    culturalSensitivityNotes: z.string().optional(),
    projectScope: z.string().optional(),
    seriesType: z.string().optional(),
    longDescription: z.string().optional(),
    storyPrompt: z.string().optional(),
    targetWordCount: z.number().int().positive().optional(),
    targetChapters: z.number().int().positive().max(100).optional()
  })
})

// GET /api/projects - List user's projects with pagination
export const GET = createAPIHandler(
  async (request: NextRequest) => {
    // Authenticate user
    const user = await requireAuth(request)
    const supabase = createClient()
    
    // Parse pagination parameters
    const { page, pageSize, offset } = parsePaginationParams(request)
    
    // Get total count
    const { count } = await supabase
      .from('projects')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
    
    // Get paginated projects
    const { data: projects, error } = await supabase
      .from('projects')
      .select(`
        *,
        chapters(count),
        characters(count)
      `)
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })
      .range(offset, offset + pageSize - 1)
    
    if (error) throw error
    
    return {
      projects,
      pagination: {
        page,
        pageSize,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize),
        hasMore: page * pageSize < (count || 0)
      }
    }
  },
  {
    timeout: 'DEFAULT',
    cache: {
      maxAge: 300, // 5 minutes
      sMaxAge: 600, // 10 minutes
      staleWhileRevalidate: TIME_SECONDS.HOUR // 1 hour
    }
  }
)

// POST /api/projects - Create a new project
export const POST = createAPIHandler(
  async (request: NextRequest) => {
    // Require authenticated user with at least free tier
    const user = await requireSubscriptionTier(request, ['free', 'pro', 'enterprise'])
    const supabase = createClient()
    
    // Parse and validate request body
    const body = await parseRequestBody(request, createProjectSchema)
    
    // Check project limit based on subscription
    const { count } = await supabase
      .from('projects')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
    
    const limits = {
      free: 3,
      pro: 50,
      enterprise: -1 // unlimited
    }
    
    const limit = limits[user.subscriptionTier || 'free']
    if (limit !== -1 && (count || 0) >= limit) {
      throw new Error(`Project limit reached. Upgrade to create more projects.`)
    }
    
    // Create project
    const { data: project, error } = await supabase
      .from('projects')
      .insert({
        user_id: user.id,
        title: body.settings.projectName,
        description: body.settings.description || '',
        settings: body.settings as ProjectSettings,
        status: 'draft'
      })
      .select()
      .single()
    
    if (error) throw error
    
    // Queue AI processing if configured
    if (body.settings.storyPrompt) {
      await aiProcessingQueue.add('generate-story', {
        projectId: project.id,
        prompt: body.settings.storyPrompt,
        settings: body.settings
      })
    }
    
    logger.info('Project created', { 
      projectId: project.id, 
      userId: user.id 
    })
    
    return project
  },
  {
    timeout: 'LONG',
    requireAuth: true
  }
)

// DELETE /api/projects - Delete multiple projects
export const DELETE = createAPIHandler(
  async (request: NextRequest) => {
    const user = await requireAuth(request)
    const supabase = createClient()
    
    // Parse project IDs from body
    const { projectIds } = await parseRequestBody(
      request,
      z.object({
        projectIds: z.array(commonSchemas.projectId)
      })
    )
    
    // Verify ownership of all projects
    const { data: projects } = await supabase
      .from('projects')
      .select('id')
      .eq('user_id', user.id)
      .in('id', projectIds)
    
    if (!projects || projects.length !== projectIds.length) {
      throw new Error('One or more projects not found or unauthorized')
    }
    
    // Delete projects (cascades to chapters, characters, etc.)
    const { error } = await supabase
      .from('projects')
      .delete()
      .in('id', projectIds)
    
    if (error) throw error
    
    logger.info('Projects deleted', { 
      projectIds, 
      userId: user.id 
    })
    
    return { deleted: projectIds }
  },
  {
    timeout: 'LONG',
    requireAuth: true
  }
)

// This refactored version demonstrates:
// 1. Use of createAPIHandler wrapper for consistent error handling
// 2. Use of authentication middleware functions
// 3. Use of common validation schemas
// 4. Use of configuration constants for timeouts
// 5. Consistent response format with pagination
// 6. Proper TypeScript typing throughout (no 'any' types)
// 7. Centralized logging approach