import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from './auth-helpers'
import { handleAPIError, RateLimitError, ApiError, ErrorType, ErrorSeverity } from './error-handler'
import { withRetry, withTimeout, RetryHandlers } from './retry-handler'
import { withCircuitBreaker, circuitBreakerManager, CIRCUIT_BREAKER_PRESETS } from '@/lib/services/circuit-breaker'
import { aiLimiter, getClientIP } from '@/lib/rate-limiter-unified'
import { logger } from '@/lib/services/logger'
import { PERFORMANCE_CONFIG } from '@/lib/config/environment-config'
import { v4 as uuidv4 } from 'uuid'
import { RATE_LIMITS } from '@/lib/constants'

// Middleware options
export interface MiddlewareOptions {
  auth?: boolean
  rateLimit?: {
    type: 'default' | 'ai-generation' | 'ai-analysis' | 'custom'
    requests?: number
    window?: number
  }
  retry?: {
    enabled: boolean
    type?: 'ai' | 'database' | 'external' | 'critical' | 'none'
  }
  circuitBreaker?: {
    enabled: boolean
    name?: string
    type?: 'openai' | 'database' | 'supabase' | 'custom'
  }
  timeout?: number
  cors?: {
    origins?: string[]
    methods?: string[]
    headers?: string[]
  }
}

// Default options
const DEFAULT_OPTIONS: MiddlewareOptions = {
  auth: true,
  retry: {
    enabled: true,
    type: 'none'
  },
  circuitBreaker: {
    enabled: false
  },
  timeout: PERFORMANCE_CONFIG.TIMEOUTS.API_DEFAULT
}

// Enhanced middleware wrapper
export function withEnhancedMiddleware(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
  options: MiddlewareOptions = {}
) {
  const finalOptions = { ...DEFAULT_OPTIONS, ...options }
  
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    const requestId = request.headers.get('x-request-id') || uuidv4()
    const startTime = Date.now()
    
    // Add request ID to all logs
    logger.info('API request started', {
      requestId,
      method: request.method,
      url: request.url,
      options: finalOptions
    })
    
    try {
      // CORS handling
      if (finalOptions.cors) {
        const origin = request.headers.get('origin')
        const corsHeaders = getCorsHeaders(origin, finalOptions.cors)
        
        // Handle preflight
        if (request.method === 'OPTIONS') {
          return new NextResponse(null, { status: 200, headers: corsHeaders })
        }
      }
      
      // Authentication check
      if (finalOptions.auth) {
        const authResponse = await withAuth(async () => null)(request)
        if (authResponse instanceof NextResponse && authResponse.status === 401) {
          return authResponse
        }
      }
      
      // Rate limiting
      if (finalOptions.rateLimit) {
        const rateLimitResult = await checkRateLimit(request, finalOptions.rateLimit)
        if (!rateLimitResult.success) {
          throw new RateLimitError(
            `Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds`,
            rateLimitResult.retryAfter
          )
        }
      }
      
      // Build the execution chain
      let executeHandler = () => handler(request, context)
      
      // Add timeout
      if (finalOptions.timeout) {
        const originalHandler = executeHandler
        executeHandler = () => withTimeout(originalHandler, finalOptions.timeout)
      }
      
      // Add circuit breaker
      if (finalOptions.circuitBreaker?.enabled) {
        const originalHandler = executeHandler
        executeHandler = () => applyCircuitBreaker(originalHandler, finalOptions.circuitBreaker!)
      }
      
      // Add retry logic
      if (finalOptions.retry?.enabled && finalOptions.retry.type !== 'none') {
        const originalHandler = executeHandler
        executeHandler = () => applyRetryLogic(originalHandler, finalOptions.retry!)
      }
      
      // Execute the handler
      const response = await executeHandler()
      
      // Add standard headers
      response.headers.set('x-request-id', requestId)
      response.headers.set('x-response-time', `${Date.now() - startTime}ms`)
      
      // Add CORS headers if configured
      if (finalOptions.cors) {
        const origin = request.headers.get('origin')
        const corsHeaders = getCorsHeaders(origin, finalOptions.cors)
        Object.entries(corsHeaders).forEach(([key, value]) => {
          response.headers.set(key, value)
        })
      }
      
      logger.info('API request completed', {
        requestId,
        duration: Date.now() - startTime,
        status: response.status
      })
      
      return response
      
    } catch (error) {
      logger.error('API request failed', {
        requestId,
        duration: Date.now() - startTime,
        error
      })
      
      // Handle the error and return response
      const errorResponse = handleAPIError(error, request.url)
      errorResponse.headers.set('x-request-id', requestId)
      errorResponse.headers.set('x-response-time', `${Date.now() - startTime}ms`)
      
      return errorResponse
    }
  }
}

// Rate limit checking
async function checkRateLimit(
  request: NextRequest,
  config: NonNullable<MiddlewareOptions['rateLimit']>
): Promise<{ success: boolean; retryAfter?: number }> {
  const ip = getClientIP(request)
  
  // Get the appropriate limiter based on type
  let limiter
  switch (config.type) {
    case 'ai-generation':
    case 'ai-analysis':
      limiter = aiLimiter
      break
    case 'custom':
      if (!config.requests || !config.window) {
        throw new Error('Custom rate limit requires requests and window parameters')
      }
      // Create custom limiter (simplified for this example)
      limiter = aiLimiter // Would create custom limiter in real implementation
      break
    default:
      // Use default limiter
      limiter = aiLimiter
  }
  
  const result = await limiter.check(RATE_LIMITS.SERVICE_ORCHESTRATOR_WRITE, ip) // Simplified check
  
  if (!result.success) {
    return {
      success: false,
      retryAfter: Math.ceil(result.reset / TIME_MS.SECOND)
    }
  }
  
  return { success: true }
}

// Apply circuit breaker based on configuration
async function applyCircuitBreaker<T>(
  fn: () => Promise<T>,
  config: NonNullable<MiddlewareOptions['circuitBreaker']>
): Promise<T> {
  switch (config.type) {
    case 'openai':
      return withCircuitBreaker('openai', fn, CIRCUIT_BREAKER_PRESETS.OPENAI) as Promise<T>
    case 'database':
    case 'supabase':
      return withCircuitBreaker('supabase', fn, CIRCUIT_BREAKER_PRESETS.SUPABASE) as Promise<T>
    case 'custom':
      if (!config.name) {
        throw new Error('Custom circuit breaker requires a name')
      }
      return withCircuitBreaker(config.name, fn) as Promise<T>
    default:
      return withCircuitBreaker('default', fn) as Promise<T>
  }
}

// Apply retry logic based on configuration
async function applyRetryLogic<T>(
  fn: () => Promise<T>,
  config: NonNullable<MiddlewareOptions['retry']>
): Promise<T> {
  switch (config.type) {
    case 'ai':
      return RetryHandlers.ai(fn)
    case 'database':
      return RetryHandlers.database(fn)
    case 'external':
      return RetryHandlers.external(fn)
    case 'critical':
      return RetryHandlers.critical(fn)
    default:
      return fn()
  }
}

// Get CORS headers
function getCorsHeaders(
  origin: string | null,
  config: NonNullable<MiddlewareOptions['cors']>
): Record<string, string> {
  const headers: Record<string, string> = {}
  
  // Check if origin is allowed
  if (origin && config.origins) {
    if (config.origins.includes('*') || config.origins.includes(origin)) {
      headers['Access-Control-Allow-Origin'] = origin
    }
  } else if (config.origins?.includes('*')) {
    headers['Access-Control-Allow-Origin'] = '*'
  }
  
  // Add other CORS headers
  if (config.methods) {
    headers['Access-Control-Allow-Methods'] = config.methods.join(', ')
  }
  
  if (config.headers) {
    headers['Access-Control-Allow-Headers'] = config.headers.join(', ')
  }
  
  headers['Access-Control-Max-Age'] = '86400' // 24 hours
  
  return headers
}

// Convenience middleware presets
export const Middleware = {
  // Public endpoint (no auth)
  public: (handler: (request: NextRequest) => Promise<NextResponse>) =>
    withEnhancedMiddleware(handler, {
      auth: false,
      retry: { enabled: false },
      circuitBreaker: { enabled: false }
    }),
  
  // AI endpoint with all protections
  ai: (handler: (request: NextRequest) => Promise<NextResponse>) =>
    withEnhancedMiddleware(handler, {
      auth: true,
      rateLimit: { type: 'ai-generation' },
      retry: { enabled: true, type: 'ai' },
      circuitBreaker: { enabled: true, type: 'openai' },
      timeout: PERFORMANCE_CONFIG.TIMEOUTS.AI_GENERATION
    }),
  
  // Database endpoint
  database: (handler: (request: NextRequest) => Promise<NextResponse>) =>
    withEnhancedMiddleware(handler, {
      auth: true,
      retry: { enabled: true, type: 'database' },
      circuitBreaker: { enabled: true, type: 'database' }
    }),
  
  // External API endpoint
  external: (handler: (request: NextRequest) => Promise<NextResponse>) =>
    withEnhancedMiddleware(handler, {
      auth: true,
      retry: { enabled: true, type: 'external' },
      circuitBreaker: { enabled: true, type: 'custom' }
    }),
  
  // Webhook endpoint
  webhook: (handler: (request: NextRequest) => Promise<NextResponse>) =>
    withEnhancedMiddleware(handler, {
      auth: false,
      retry: { enabled: false },
      circuitBreaker: { enabled: false },
      rateLimit: { type: 'custom', requests: 100, window: 60000 }
    })
}