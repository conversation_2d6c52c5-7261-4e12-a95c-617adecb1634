'use client'

import { But<PERSON> } from '@/components/ui/button'
import { SaveStatusIndicator } from '@/components/version-history/save-status-indicator'
import { WritingSessionTracker } from '@/components/editor/writing-session-tracker'
import { AgentStatusWidget } from '@/components/agents/agent-status-widget'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu'
import { 
  Save, 
  ArrowLeft,
  Wand2,
  Eye,
  Upload,
  Globe,
  BookOpenCheck,
  ChevronDown
} from 'lucide-react'
import Link from 'next/link'

interface WritePageHeaderProps {
  projectId: string
  projectTitle?: string
  isSaving: boolean
  hasUnsavedChanges: boolean
  lastSaved: Date | null
  focusMode: boolean
  onSave: () => void
  onToggleFocusMode: () => void
  onImportBook: () => void
  onGenerateChapter: () => void
  onToggleReview: () => void
  isReviewOpen?: boolean
}

export function WritePageHeader({
  projectId,
  projectTitle,
  isSaving,
  hasUnsavedChanges,
  lastSaved,
  focusMode,
  onSave,
  onToggleFocusMode,
  onImportBook,
  onGenerateChapter,
  onToggleReview,
  isReviewOpen
}: WritePageHeaderProps) {
  return (
    <div className={`
      border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60
      transition-all duration-300
      ${focusMode ? 'opacity-0 hover:opacity-100' : 'opacity-100'}
    `}>
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm" aria-label="Navigate back to project overview">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          
          {projectTitle && (
            <h1 className="text-lg font-semibold">{projectTitle}</h1>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Writing Session Tracker */}
          <WritingSessionTracker projectId={projectId} />
          
          {/* Agent Status */}
          <AgentStatusWidget projectId={projectId} />
          
          {/* Action Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2" aria-label="Document actions menu" aria-haspopup="true">
                Actions
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Document Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={onGenerateChapter}>
                <Wand2 className="h-4 w-4 mr-2" />
                Generate Chapter
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={onToggleReview}>
                <BookOpenCheck className="h-4 w-4 mr-2" />
                {isReviewOpen ? 'Close Review' : 'Review Chapter'}
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={onImportBook}>
                <Upload className="h-4 w-4 mr-2" />
                Import Book
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={onToggleFocusMode}>
                <Eye className="h-4 w-4 mr-2" />
                {focusMode ? 'Exit Focus Mode' : 'Focus Mode'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Save Button */}
          <Button
            onClick={onSave}
            disabled={isSaving || !hasUnsavedChanges}
            size="sm"
            variant={hasUnsavedChanges ? "default" : "outline"}
            aria-label={isSaving ? 'Saving document' : 'Save document'}
            aria-busy={isSaving}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
          
          {/* Save Status */}
          <SaveStatusIndicator
            isSaving={isSaving}
            lastSaved={lastSaved}
            hasUnsavedChanges={hasUnsavedChanges}
          />
        </div>
      </div>
    </div>
  )
}