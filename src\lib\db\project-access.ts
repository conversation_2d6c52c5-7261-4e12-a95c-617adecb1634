import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import type { Database } from '@/lib/db/types'

export type Project = Database['public']['Tables']['projects']['Row']

export const PROJECT_ACCESS_ERROR = 'Project not found or access denied'

export async function verifyProjectAccess(projectId: string, userId: string): Promise<Project | null> {
  const supabase = await createTypedServerClient<Database>()
  const { data: project, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', projectId)
    .eq('user_id', userId)
    .single()

  if (error || !project) {
    logger.warn(PROJECT_ACCESS_ERROR, { projectId, userId, error })
    return null
  }
  return project
}
