'use client'

import { useEffect } from 'react'
import { logger } from '@/lib/services/logger'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle, RefreshCw, Home, Bug } from 'lucide-react'
import { config } from '@/lib/config'

export default function DashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    logger.error('Dashboard error:', error)
  }, [error])

  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-error-light dark:bg-red-900/20 rounded-full w-fit">
            <AlertCircle className="h-6 w-6 text-error dark:text-red-400" />
          </div>
          <CardTitle>Something went wrong!</CardTitle>
          <CardDescription>
            We encountered an error loading your dashboard. This might be a temporary issue.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error.digest && (
            <Alert>
              <Bug className="h-4 w-4" />
              <AlertTitle>Error Reference</AlertTitle>
              <AlertDescription>
                Error ID: <code className="text-xs bg-muted px-1 rounded">{error.digest}</code>
              </AlertDescription>
            </Alert>
          )}

          {config.isDevelopment && error.message && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error Details (Development Only)</AlertTitle>
              <AlertDescription className="mt-2">
                <pre className="text-xs overflow-auto max-h-32">
                  {error.message}
                </pre>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button 
              onClick={reset}
              className="flex-1"
              variant="outline"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try again
            </Button>
            
            <Button 
              onClick={() => window.location.href = '/dashboard'}
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              Go to Dashboard
            </Button>
          </div>

          <p className="text-center text-xs text-muted-foreground">
            If this problem persists, please contact support with the error ID above.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}