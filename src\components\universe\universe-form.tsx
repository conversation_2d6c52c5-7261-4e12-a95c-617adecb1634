import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, X } from 'lucide-react'
import { UniverseFormData } from './types'
import { SPACING } from '@/lib/config/ui-config'

interface UniverseFormProps {
  initialData?: Partial<UniverseFormData>
  onSubmit: (data: UniverseFormData) => void
  onCancel: () => void
  isSubmitting?: boolean
}

export function UniverseForm({ initialData, onSubmit, onCancel, isSubmitting }: UniverseFormProps) {
  const [formData, setFormData] = useState<UniverseFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    genre: initialData?.genre || '',
    magicSystem: initialData?.magicSystem || '',
    technologyLevel: initialData?.technologyLevel || '',
    politicalSystems: initialData?.politicalSystems || '',
    geography: initialData?.geography || '',
    history: initialData?.history || '',
    cultures: initialData?.cultures || '',
    languages: initialData?.languages || '',
    religions: initialData?.religions || '',
    economy: initialData?.economy || '',
    conflicts: initialData?.conflicts || '',
    naturalLaws: initialData?.naturalLaws || ''
  })

  const [customTags, setCustomTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  const updateField = (field: keyof UniverseFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addTag = () => {
    if (newTag.trim()) {
      setCustomTags([...customTags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (index: number) => {
    setCustomTags(customTags.filter((_, i) => i !== index))
  }

  return (
    <form onSubmit={handleSubmit} className={SPACING.SPACE_Y.LG}>
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className={SPACING.SPACE_Y.MD}>
          <div>
            <Label htmlFor="name">Universe Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => updateField('name', e.target.value)}
              placeholder="Enter universe name"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => updateField('description', e.target.value)}
              placeholder="Describe your universe..."
              rows={4}
            />
          </div>
          
          <div>
            <Label htmlFor="genre">Genre</Label>
            <Select value={formData.genre} onValueChange={(value) => updateField('genre', value)}>
              <SelectTrigger id="genre">
                <SelectValue placeholder="Select genre" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fantasy">Fantasy</SelectItem>
                <SelectItem value="sci-fi">Science Fiction</SelectItem>
                <SelectItem value="horror">Horror</SelectItem>
                <SelectItem value="mystery">Mystery</SelectItem>
                <SelectItem value="contemporary">Contemporary</SelectItem>
                <SelectItem value="historical">Historical</SelectItem>
                <SelectItem value="dystopian">Dystopian</SelectItem>
                <SelectItem value="mixed">Mixed Genre</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* World Rules */}
      <Card>
        <CardHeader>
          <CardTitle>World Rules & Systems</CardTitle>
        </CardHeader>
        <CardContent className={SPACING.SPACE_Y.MD}>
          <div>
            <Label htmlFor="magicSystem">Magic System</Label>
            <Textarea
              id="magicSystem"
              value={formData.magicSystem}
              onChange={(e) => updateField('magicSystem', e.target.value)}
              placeholder="Describe the magic system if applicable..."
              rows={3}
            />
          </div>
          
          <div>
            <Label htmlFor="technologyLevel">Technology Level</Label>
            <Select 
              value={formData.technologyLevel} 
              onValueChange={(value) => updateField('technologyLevel', value)}
            >
              <SelectTrigger id="technologyLevel">
                <SelectValue placeholder="Select technology level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="primitive">Primitive</SelectItem>
                <SelectItem value="medieval">Medieval</SelectItem>
                <SelectItem value="renaissance">Renaissance</SelectItem>
                <SelectItem value="industrial">Industrial</SelectItem>
                <SelectItem value="modern">Modern</SelectItem>
                <SelectItem value="near-future">Near Future</SelectItem>
                <SelectItem value="far-future">Far Future</SelectItem>
                <SelectItem value="post-apocalyptic">Post-Apocalyptic</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="naturalLaws">Natural Laws</Label>
            <Textarea
              id="naturalLaws"
              value={formData.naturalLaws}
              onChange={(e) => updateField('naturalLaws', e.target.value)}
              placeholder="Any unique physical or natural laws..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Tags */}
      <Card>
        <CardHeader>
          <CardTitle>Tags & Keywords</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-3">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} size="icon">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {customTags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {tag}
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1"
                  onClick={() => removeTag(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end gap-3">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : (initialData ? 'Update Universe' : 'Create Universe')}
        </Button>
      </div>
    </form>
  )
}