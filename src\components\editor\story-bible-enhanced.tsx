'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useEditorStore } from '@/stores/editor-store'
import { useStoryBible } from '@/hooks/use-story-bible'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { Skeleton } from '@/components/ui/skeleton'
import { CharacterShareModal } from '@/components/universe/character-share-modal'
import { 
  Book, 
  Users, 
  Map, 
  Clock, 
  Lightbulb,
  X,
  ChevronDown,
  ChevronRight,
  Plus,
  Trash2,
  Loader2,
  Share2,
  MapPin,
  GitBranch,
  Target,
  AlertCircle,
  CheckCircle2
} from 'lucide-react'

interface Location {
  id: string
  name: string
  description?: string
  location_type: string
  parent_location_id?: string
  features: string[]
  significance?: string
  is_shareable: boolean
}

interface PlotThread {
  id: string
  name: string
  description?: string
  status: 'setup' | 'active' | 'resolved' | 'abandoned' | 'paused'
  thread_type: string
  importance: string
  started_chapter?: { id: string; title: string; chapter_number: number }
  resolved_chapter?: { id: string; title: string; chapter_number: number }
  characters?: Array<{ id: string; name: string; role: string }>
  locations?: Array<{ id: string; name: string }>
}

interface StoryBibleEnhancedProps {
  projectId: string
  seriesId?: string
  userId?: string
}

export function StoryBibleEnhanced({ projectId, seriesId, userId }: StoryBibleEnhancedProps) {
  const { showStoryBible, toggleStoryBible } = useEditorStore()
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['characters']))
  const [locations, setLocations] = useState<Location[]>([])
  const [plotThreads, setPlotThreads] = useState<PlotThread[]>([])
  const [isLoadingLocations, setIsLoadingLocations] = useState(false)
  const [isLoadingPlotThreads, setIsLoadingPlotThreads] = useState(false)
  const { toast } = useToast()

  // Character sharing
  const [shareModalOpen, setShareModalOpen] = useState(false)
  const [selectedCharacterForShare, setSelectedCharacterForShare] = useState<{
    id: string;
    name: string;
    role: string;
    description?: string;
  } | null>(null)
  const [currentSeriesTitle, setCurrentSeriesTitle] = useState('')

  // Location management
  const [isAddingLocation, setIsAddingLocation] = useState(false)
  const [newLocation, setNewLocation] = useState({
    name: '',
    description: '',
    locationType: 'other' as const,
    parentLocationId: '',
    features: [] as string[],
    significance: '',
    isShareable: false
  })

  // Plot thread management
  const [isAddingPlotThread, setIsAddingPlotThread] = useState(false)
  const [newPlotThread, setNewPlotThread] = useState({
    name: '',
    description: '',
    threadType: 'subplot' as const,
    importance: 'major' as const,
    status: 'setup' as const,
    relatedCharacters: [] as string[],
    relatedLocations: [] as string[]
  })

  const {
    storyBible,
    isLoading,
    loadStoryBible,
    addCharacter,
    deleteCharacter
  } = useStoryBible(projectId)

  useEffect(() => {
    if (projectId) {
      loadStoryBible()
      loadLocations()
      loadPlotThreads()
      if (seriesId) {
        loadSeriesInfo()
      }
    }
  }, [projectId, seriesId])

  const loadSeriesInfo = async () => {
    if (!seriesId) return
    try {
      const response = await fetch(`/api/series/${seriesId}`)
      if (response.ok) {
        const data = await response.json()
        setCurrentSeriesTitle(data.series.title)
      }
    } catch (error) {
      logger.error('Error loading series info:', error)
    }
  }

  const loadLocations = async () => {
    setIsLoadingLocations(true)
    try {
      const response = await fetch(`/api/projects/${projectId}/locations`)
      if (!response.ok) throw new Error('Failed to load locations')
      
      const data = await response.json()
      setLocations(data.locations || [])
    } catch (error) {
      logger.error('Error loading locations:', error)
      toast({
        title: "Error",
        description: "Failed to load locations",
        variant: "destructive"
      })
    } finally {
      setIsLoadingLocations(false)
    }
  }

  const loadPlotThreads = async () => {
    setIsLoadingPlotThreads(true)
    try {
      const response = await fetch(`/api/projects/${projectId}/plot-threads`)
      if (!response.ok) throw new Error('Failed to load plot threads')
      
      const data = await response.json()
      setPlotThreads(data.plotThreads || [])
    } catch (error) {
      logger.error('Error loading plot threads:', error)
      toast({
        title: "Error",
        description: "Failed to load plot threads",
        variant: "destructive"
      })
    } finally {
      setIsLoadingPlotThreads(false)
    }
  }

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const handleAddLocation = async () => {
    if (!newLocation.name.trim()) return

    try {
      const response = await fetch(`/api/projects/${projectId}/locations`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...newLocation,
          seriesId
        })
      })

      if (!response.ok) throw new Error('Failed to create location')

      const data = await response.json()
      setLocations([...locations, data.location])
      
      // Reset form
      setNewLocation({
        name: '',
        description: '',
        locationType: 'other',
        parentLocationId: '',
        features: [],
        significance: '',
        isShareable: false
      })
      setIsAddingLocation(false)
      
      toast({
        title: "Success",
        description: "Location added successfully"
      })
    } catch (error) {
      logger.error('Error adding location:', error)
      toast({
        title: "Error",
        description: "Failed to add location",
        variant: "destructive"
      })
    }
  }

  const handleAddPlotThread = async () => {
    if (!newPlotThread.name.trim()) return

    try {
      const response = await fetch(`/api/projects/${projectId}/plot-threads`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...newPlotThread,
          seriesId
        })
      })

      if (!response.ok) throw new Error('Failed to create plot thread')

      const data = await response.json()
      setPlotThreads([...plotThreads, data.plotThread])
      
      // Reset form
      setNewPlotThread({
        name: '',
        description: '',
        threadType: 'subplot',
        importance: 'major',
        status: 'setup',
        relatedCharacters: [],
        relatedLocations: []
      })
      setIsAddingPlotThread(false)
      
      toast({
        title: "Success",
        description: "Plot thread added successfully"
      })
    } catch (error) {
      logger.error('Error adding plot thread:', error)
      toast({
        title: "Error",
        description: "Failed to add plot thread",
        variant: "destructive"
      })
    }
  }

  const handleShareCharacter = (character: { id: string; name: string; role: string; description?: string }) => {
    if (!seriesId) {
      toast({
        title: "Error",
        description: "Series ID is required for character sharing",
        variant: "destructive"
      })
      return
    }
    setSelectedCharacterForShare(character)
    setShareModalOpen(true)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'setup': return <AlertCircle className="h-4 w-4 text-info" />
      case 'active': return <Target className="h-4 w-4 text-success" />
      case 'resolved': return <CheckCircle2 className="h-4 w-4 text-gray-500" />
      case 'abandoned': return <X className="h-4 w-4 text-error" />
      case 'paused': return <Clock className="h-4 w-4 text-warning" />
      default: return null
    }
  }

  if (!showStoryBible) return null

  return (
    <>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Book className="h-5 w-5" />
              Story Bible
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={toggleStoryBible}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 p-0">
          <Tabs defaultValue="reference" className="h-full">
            <TabsList className="grid w-full grid-cols-2 mx-4">
              <TabsTrigger value="reference">Reference</TabsTrigger>
              <TabsTrigger value="tracking">Tracking</TabsTrigger>
            </TabsList>

            <TabsContent value="reference" className="mt-0 h-full">
              <ScrollArea className="h-full">
                {isLoading ? (
                  <div className="p-4 space-y-4">
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                  </div>
                ) : (
                  <div className="p-4 space-y-4">
                    {/* Characters Section */}
                    <div className="space-y-2">
                      <Button
                        variant="ghost"
                        className="w-full justify-start p-2 h-auto"
                        onClick={() => toggleSection('characters')}
                      >
                        <div className="flex items-center gap-2">
                          {expandedSections.has('characters') ? 
                            <ChevronDown className="h-4 w-4" /> : 
                            <ChevronRight className="h-4 w-4" />
                          }
                          <Users className="h-4 w-4" />
                          <span className="font-medium">Characters</span>
                          <Badge variant="secondary" className="ml-auto">
                            {storyBible?.characters?.length || 0}
                          </Badge>
                        </div>
                      </Button>

                      {expandedSections.has('characters') && (
                        <div className="ml-6 space-y-3">
                          {storyBible?.characters?.map((character) => (
                            <Card key={character.id} className="p-3">
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <h4 className="font-medium">{character.name}</h4>
                                    <Badge variant="outline" className="text-xs">
                                      {character.role}
                                    </Badge>
                                  </div>
                                  <div className="flex gap-1">
                                    {seriesId && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleShareCharacter(character)}
                                        title="Share to another series"
                                      >
                                        <Share2 className="h-3 w-3" />
                                      </Button>
                                    )}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => deleteCharacter(character.id)}
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {character.description}
                                </p>
                              </div>
                            </Card>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Locations Section */}
                    <div className="space-y-2">
                      <Button
                        variant="ghost"
                        className="w-full justify-start p-2 h-auto"
                        onClick={() => toggleSection('locations')}
                      >
                        <div className="flex items-center gap-2">
                          {expandedSections.has('locations') ? 
                            <ChevronDown className="h-4 w-4" /> : 
                            <ChevronRight className="h-4 w-4" />
                          }
                          <MapPin className="h-4 w-4" />
                          <span className="font-medium">Locations</span>
                          <Badge variant="secondary" className="ml-auto">
                            {locations.length}
                          </Badge>
                        </div>
                      </Button>

                      {expandedSections.has('locations') && (
                        <div className="ml-6 space-y-3">
                          <Dialog open={isAddingLocation} onOpenChange={setIsAddingLocation}>
                            <DialogTrigger asChild>
                              <Button variant="outline" size="sm" className="w-full">
                                <Plus className="h-4 w-4 mr-2" />
                                Add Location
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Add New Location</DialogTitle>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div>
                                  <Label htmlFor="loc-name">Name</Label>
                                  <Input
                                    id="loc-name"
                                    value={newLocation.name}
                                    onChange={(e) => setNewLocation({...newLocation, name: e.target.value})}
                                    placeholder="Location name"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="loc-type">Type</Label>
                                  <Select
                                    value={newLocation.locationType}
                                    onValueChange={(value) => setNewLocation({...newLocation, locationType: value as typeof newLocation.locationType})}
                                  >
                                    <SelectTrigger id="loc-type">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="world">World</SelectItem>
                                      <SelectItem value="continent">Continent</SelectItem>
                                      <SelectItem value="country">Country</SelectItem>
                                      <SelectItem value="region">Region</SelectItem>
                                      <SelectItem value="city">City</SelectItem>
                                      <SelectItem value="building">Building</SelectItem>
                                      <SelectItem value="room">Room</SelectItem>
                                      <SelectItem value="other">Other</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <Label htmlFor="loc-desc">Description</Label>
                                  <Textarea
                                    id="loc-desc"
                                    value={newLocation.description}
                                    onChange={(e) => setNewLocation({...newLocation, description: e.target.value})}
                                    placeholder="Brief location description"
                                    rows={3}
                                  />
                                </div>
                                <div className="flex gap-2">
                                  <Button onClick={handleAddLocation}>Add Location</Button>
                                  <Button variant="outline" onClick={() => setIsAddingLocation(false)}>
                                    Cancel
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>

                          {isLoadingLocations ? (
                            <div className="flex justify-center p-4">
                              <Loader2 className="h-6 w-6 animate-spin" />
                            </div>
                          ) : locations.length > 0 ? (
                            locations.map((location) => (
                              <Card key={location.id} className="p-3">
                                <div className="space-y-2">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <h4 className="font-medium">{location.name}</h4>
                                      <Badge variant="outline" className="text-xs">
                                        {location.location_type}
                                      </Badge>
                                      {location.is_shareable && (
                                        <Badge variant="secondary" className="text-xs">
                                          Shareable
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  {location.description && (
                                    <p className="text-sm text-muted-foreground">
                                      {location.description}
                                    </p>
                                  )}
                                </div>
                              </Card>
                            ))
                          ) : (
                            <p className="text-sm text-muted-foreground">No locations defined yet</p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="tracking" className="mt-0 h-full">
              <ScrollArea className="h-full">
                <div className="p-4 space-y-4">
                  {/* Plot Threads */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <GitBranch className="h-4 w-4" />
                        <span className="font-medium">Plot Threads</span>
                      </div>
                      <Dialog open={isAddingPlotThread} onOpenChange={setIsAddingPlotThread}>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Plot Thread</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="thread-name">Name</Label>
                              <Input
                                id="thread-name"
                                value={newPlotThread.name}
                                onChange={(e) => setNewPlotThread({...newPlotThread, name: e.target.value})}
                                placeholder="Plot thread name"
                              />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="thread-type">Type</Label>
                                <Select
                                  value={newPlotThread.threadType}
                                  onValueChange={(value) => setNewPlotThread({...newPlotThread, threadType: value as typeof newPlotThread.threadType})}
                                >
                                  <SelectTrigger id="thread-type">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="main">Main Plot</SelectItem>
                                    <SelectItem value="subplot">Subplot</SelectItem>
                                    <SelectItem value="character_arc">Character Arc</SelectItem>
                                    <SelectItem value="mystery">Mystery</SelectItem>
                                    <SelectItem value="romance">Romance</SelectItem>
                                    <SelectItem value="conflict">Conflict</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <Label htmlFor="thread-importance">Importance</Label>
                                <Select
                                  value={newPlotThread.importance}
                                  onValueChange={(value) => setNewPlotThread({...newPlotThread, importance: value as typeof newPlotThread.importance})}
                                >
                                  <SelectTrigger id="thread-importance">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="critical">Critical</SelectItem>
                                    <SelectItem value="major">Major</SelectItem>
                                    <SelectItem value="minor">Minor</SelectItem>
                                    <SelectItem value="background">Background</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="thread-desc">Description</Label>
                              <Textarea
                                id="thread-desc"
                                value={newPlotThread.description}
                                onChange={(e) => setNewPlotThread({...newPlotThread, description: e.target.value})}
                                placeholder="What is this plot thread about?"
                                rows={3}
                              />
                            </div>
                            <div className="flex gap-2">
                              <Button onClick={handleAddPlotThread}>Add Thread</Button>
                              <Button variant="outline" onClick={() => setIsAddingPlotThread(false)}>
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>

                    <div className="space-y-2">
                      {isLoadingPlotThreads ? (
                        <div className="flex justify-center p-4">
                          <Loader2 className="h-6 w-6 animate-spin" />
                        </div>
                      ) : plotThreads.length > 0 ? (
                        plotThreads.map((thread) => (
                          <Card key={thread.id} className="p-3">
                            <div className="space-y-2">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(thread.status)}
                                    <h4 className="font-medium">{thread.name}</h4>
                                    <Badge variant="outline" className="text-xs">
                                      {thread.thread_type}
                                    </Badge>
                                    <Badge 
                                      variant={
                                        thread.importance === 'critical' ? 'destructive' :
                                        thread.importance === 'major' ? 'default' :
                                        'secondary'
                                      }
                                      className="text-xs"
                                    >
                                      {thread.importance}
                                    </Badge>
                                  </div>
                                  {thread.description && (
                                    <p className="text-sm text-muted-foreground mt-1">
                                      {thread.description}
                                    </p>
                                  )}
                                  <div className="flex flex-wrap gap-2 mt-2">
                                    {thread.started_chapter && (
                                      <Badge variant="outline" className="text-xs">
                                        Started: Ch. {thread.started_chapter.chapter_number}
                                      </Badge>
                                    )}
                                    {thread.resolved_chapter && (
                                      <Badge variant="outline" className="text-xs">
                                        Resolved: Ch. {thread.resolved_chapter.chapter_number}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Card>
                        ))
                      ) : (
                        <p className="text-sm text-muted-foreground">No plot threads tracked yet</p>
                      )}
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Character Share Modal */}
      {selectedCharacterForShare && seriesId && (
        <CharacterShareModal
          open={shareModalOpen}
          onOpenChange={setShareModalOpen}
          character={selectedCharacterForShare}
          currentSeriesId={seriesId}
          currentSeriesTitle={currentSeriesTitle}
          userId={userId || ''}
          onShareComplete={() => {
            setSelectedCharacterForShare(null)
            // Optionally reload data
          }}
        />
      )}
    </>
  )
}