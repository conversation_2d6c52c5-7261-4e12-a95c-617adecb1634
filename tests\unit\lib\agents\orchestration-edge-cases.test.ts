import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { StoryArchitectAgent } from '@/lib/agents/story-architect';
import { CharacterDeveloperAgent } from '@/lib/agents/character-developer';
import type { BookContext } from '@/lib/agents/types';
import type { ProjectSettings } from '@/lib/types/project-settings';

// Mock all dependencies
jest.mock('@/lib/agents/story-architect');
jest.mock('@/lib/agents/character-developer');
jest.mock('@/lib/agents/chapter-planner');
jest.mock('@/lib/agents/voice-aware-writing-agent');
jest.mock('@/lib/services/content-generator');
jest.mock('@/lib/agents/adaptive-planning-agent');
jest.mock('@/lib/services/context-manager');

describe('Agent Orchestration Edge Cases and Error Handling', () => {
  let orchestrator: AdvancedAgentOrchestrator;
  
  const mockProjectSettings: ProjectSettings = {
    primaryGenre: 'fantasy',
    secondaryGenres: ['adventure'],
    targetAudience: 'adult',
    writingStyle: 'descriptive',
    narrativeVoice: 'third-person',
    tense: 'past',
    pacing: 'medium',
    violenceLevel: 'moderate',
    romanceLevel: 'low',
    profanityLevel: 'mild',
    themeDepth: 'deep',
    worldBuildingDepth: 'extensive',
    characterComplexity: 'complex',
    plotComplexity: 'complex',
    tone: 'serious',
    dialogueStyle: 'natural',
    descriptionLevel: 'detailed',
    useDeepPOV: true,
    showDontTell: true,
    varyProse: true,
    useSymbolism: true,
    useCliffhangers: true,
    useForeshadowing: true,
    useFlashbacks: false,
    useUnreliableNarrator: false,
    protagonistTypes: ['hero'],
    antagonistTypes: ['villain'],
    supportingRoles: ['mentor', 'sidekick'],
    majorThemes: ['courage', 'friendship'],
    minorThemes: ['sacrifice'],
    culturalElements: [],
    magicSystemType: 'soft',
    technologyLevel: 'medieval',
    politicalSystem: 'monarchy',
    economicSystem: 'feudal',
    geographyType: 'earth-like',
    pacingPreference: 'medium'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    orchestrator = new AdvancedAgentOrchestrator(3);
  });

  describe('Network Failure Handling', () => {
    it('should handle API timeout gracefully', async () => {
      const timeoutError = new Error('Request timeout');
      (timeoutError as any).code = 'ETIMEDOUT';
      
      let attemptCount = 0;
      const mockStoryArchitect = StoryArchitectAgent.prototype as jest.Mocked<StoryArchitectAgent>;
      
      mockStoryArchitect.generateStoryStructure = jest.fn().mockImplementation(async () => {
        attemptCount++;
        if (attemptCount <= 2) {
          // Simulate timeout on first 2 attempts
          await new Promise(resolve => setTimeout(resolve, 100));
          throw timeoutError;
        }
        // Succeed on third attempt
        return {
          success: true,
          data: {
            title: 'Test Story',
            premise: 'Test premise',
            genre: 'fantasy',
            themes: [],
            acts: [],
            conflicts: [],
            timeline: [],
            worldBuilding: {
              setting: {
                timeForPeriod: 'medieval',
                locations: [],
                culture: 'fantasy',
                technology: 'pre-industrial'
              },
              rules: [],
              history: []
            },
            plotPoints: []
          }
        };
      });

      const result = await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'Test story',
        80000,
        20
      );

      // Should retry and eventually succeed
      expect(attemptCount).toBe(3);
      expect(result.success).toBeDefined();
    });

    it('should handle rate limiting with exponential backoff', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      (rateLimitError as any).headers = { 'retry-after': '60' };
      
      const attemptTimestamps: number[] = [];
      const mockStoryArchitect = StoryArchitectAgent.prototype as jest.Mocked<StoryArchitectAgent>;
      
      mockStoryArchitect.generateStoryStructure = jest.fn().mockImplementation(async () => {
        attemptTimestamps.push(Date.now());
        
        if (attemptTimestamps.length <= 2) {
          throw rateLimitError;
        }
        
        return {
          success: true,
          data: {
            title: 'Test Story',
            premise: 'Test premise',
            genre: 'fantasy',
            themes: [],
            acts: [],
            conflicts: [],
            timeline: [],
            worldBuilding: {
              setting: {
                timeForPeriod: 'medieval',
                locations: [],
                culture: 'fantasy',
                technology: 'pre-industrial'
              },
              rules: [],
              history: []
            },
            plotPoints: []
          }
        };
      });

      // Override retry logic to use shorter delays for testing
      orchestrator['handleTaskFailure'] = jest.fn().mockImplementation(async (failedTask) => {
        const task = { ...failedTask, retryCount: (failedTask.retryCount || 0) + 1 };
        
        if (task.retryCount <= 2) {
          // Exponential backoff: 100ms, 200ms, 400ms
          const delay = 100 * Math.pow(2, task.retryCount - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Re-execute task
          return orchestrator['executeTask'](task);
        }
        
        return failedTask;
      });

      const startTime = Date.now();
      await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'Test story',
        80000,
        20
      );

      // Verify exponential backoff timing
      expect(attemptTimestamps.length).toBe(3);
      if (attemptTimestamps.length >= 3) {
        const delay1 = attemptTimestamps[1] - attemptTimestamps[0];
        const delay2 = attemptTimestamps[2] - attemptTimestamps[1];
        
        // Second delay should be roughly double the first
        expect(delay2).toBeGreaterThan(delay1 * 1.5);
      }
    });

    it('should handle intermittent network failures', async () => {
      const networkErrors = [
        new Error('Network connection lost'),
        new Error('ECONNRESET'),
        new Error('Socket hang up')
      ];
      
      let callCount = 0;
      const failurePattern = [true, false, true, false, false]; // Intermittent failures
      
      const mockStoryArchitect = StoryArchitectAgent.prototype as jest.Mocked<StoryArchitectAgent>;
      
      mockStoryArchitect.generateStoryStructure = jest.fn().mockImplementation(async () => {
        const shouldFail = failurePattern[callCount % failurePattern.length];
        callCount++;
        
        if (shouldFail && callCount <= 5) {
          throw networkErrors[callCount % networkErrors.length];
        }
        
        return {
          success: true,
          data: {
            title: 'Test Story',
            premise: 'Test premise',
            genre: 'fantasy',
            themes: [],
            acts: [],
            conflicts: [],
            timeline: [],
            worldBuilding: {
              setting: {
                timeForPeriod: 'medieval',
                locations: [],
                culture: 'fantasy',
                technology: 'pre-industrial'
              },
              rules: [],
              history: []
            },
            plotPoints: []
          }
        };
      });

      const result = await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'Test story',
        80000,
        20
      );

      // Should handle intermittent failures and complete
      expect(callCount).toBeGreaterThan(1);
      expect(result.success).toBeDefined();
    });
  });

  describe('Invalid Input Handling', () => {
    it('should handle malformed project settings gracefully', async () => {
      const invalidSettings = {
        ...mockProjectSettings,
        primaryGenre: undefined as any, // Invalid
        targetAudience: '', // Empty
        pacing: 'invalid-pacing' as any // Invalid value
      };

      const result = await orchestrator.orchestrateProject(
        'test-project',
        invalidSettings,
        'Test story',
        80000,
        20
      );

      // Should handle gracefully, possibly with defaults
      expect(result).toBeDefined();
      expect(result.error).toBeDefined();
    });

    it('should handle extreme parameter values', async () => {
      const extremeCases = [
        { wordCount: 0, chapters: 0 },
        { wordCount: 1000000, chapters: 1000 },
        { wordCount: -100, chapters: -5 },
        { wordCount: Infinity, chapters: Infinity }
      ];

      for (const testCase of extremeCases) {
        const result = await orchestrator.orchestrateProject(
          'test-project',
          mockProjectSettings,
          'Test story',
          testCase.wordCount,
          testCase.chapters
        );

        // Should not crash, should return error
        expect(result).toBeDefined();
        if (testCase.wordCount <= 0 || testCase.chapters <= 0 || 
            !isFinite(testCase.wordCount) || !isFinite(testCase.chapters)) {
          expect(result.success).toBe(false);
        }
      }
    });

    it('should handle missing required context data', async () => {
      // Create incomplete context
      const incompleteContext: Partial<BookContext> = {
        projectId: 'test-project',
        // Missing required fields like settings, projectSelections
      };

      // Attempt to initialize agents with incomplete context
      try {
        orchestrator['initializeAgents']('test-project', undefined as any);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('Concurrent Modification Handling', () => {
    it('should handle task queue modifications during execution', async () => {
      const modificationEvents: string[] = [];
      
      // Create initial tasks
      const initialTasks = Array.from({ length: 10 }, (_, i) => ({
        id: `task-${i}`,
        type: 'content_generation' as const,
        dependencies: [],
        priority: 'medium' as const,
        estimatedDuration: 0.1,
        agent: 'test',
        payload: {}
      }));

      // Mock task execution that modifies queue
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        // Simulate task that discovers need for additional tasks
        if (task.id === 'task-5') {
          modificationEvents.push('Adding new tasks during execution');
          
          // Attempt to add new tasks (should be handled safely)
          const newTasks = [
            {
              id: 'dynamic-task-1',
              type: 'content_generation' as const,
              dependencies: ['task-5'],
              priority: 'high' as const,
              estimatedDuration: 0.05,
              agent: 'test',
              payload: {}
            }
          ];
          
          // This should be handled safely
          orchestrator['taskQueue'].push(...newTasks);
        }
        
        await new Promise(resolve => setTimeout(resolve, 50));
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration: 50,
          agentUsed: 'test'
        };
      });

      const results = await orchestrator['executeTaskPipeline'](initialTasks);
      
      // Should handle dynamic modifications
      expect(modificationEvents.length).toBeGreaterThan(0);
      expect(results.length).toBeGreaterThanOrEqual(initialTasks.length);
    });

    it('should handle cancellation during execution', async () => {
      let tasksStarted = 0;
      let tasksCompleted = 0;
      const cancellationTime = 250; // Cancel after 250ms
      
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        tasksStarted++;
        
        try {
          // Simulate long-running task
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(resolve, 500);
            
            // Listen for cancellation
            orchestrator.once('orchestration:cancelled', () => {
              clearTimeout(timeout);
              reject(new Error('Task cancelled'));
            });
          });
          
          tasksCompleted++;
          return {
            taskId: task.id,
            success: true,
            result: {},
            duration: 500,
            agentUsed: 'test'
          };
        } catch (error) {
          return {
            taskId: task.id,
            success: false,
            error: (error as Error).message,
            duration: Date.now(),
            agentUsed: 'test'
          };
        }
      });

      const tasks = Array.from({ length: 20 }, (_, i) => ({
        id: `task-${i}`,
        type: 'content_generation' as const,
        dependencies: [],
        priority: 'medium' as const,
        estimatedDuration: 0.5,
        agent: 'test',
        payload: {}
      }));

      // Start orchestration
      const orchestrationPromise = orchestrator['executeTaskPipeline'](tasks);
      
      // Cancel after delay
      setTimeout(() => {
        orchestrator.cancelOrchestration();
      }, cancellationTime);

      const results = await orchestrationPromise;
      
      // Some tasks should have started but not all completed
      expect(tasksStarted).toBeGreaterThan(0);
      expect(tasksStarted).toBeLessThan(tasks.length);
      expect(tasksCompleted).toBeLessThan(tasksStarted);
    });
  });

  describe('Memory and Resource Edge Cases', () => {
    it('should handle out-of-memory scenarios', async () => {
      const memoryPressureSimulated = jest.fn();
      
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        try {
          // Simulate memory pressure
          if (task.id === 'memory-intensive-task') {
            memoryPressureSimulated();
            
            // Try to allocate large amount of memory
            const largeArrays = [];
            for (let i = 0; i < 100; i++) {
              largeArrays.push(new Array(1024 * 1024).fill(0)); // 1MB arrays
            }
            
            // If we get here, memory was available
            return {
              taskId: task.id,
              success: true,
              result: { memoryAllocated: true },
              duration: 100,
              agentUsed: 'test'
            };
          }
        } catch (error) {
          // Handle out of memory
          if ((error as Error).message.includes('heap') || 
              (error as Error).message.includes('memory')) {
            return {
              taskId: task.id,
              success: false,
              error: 'Out of memory',
              duration: 0,
              agentUsed: 'test'
            };
          }
          throw error;
        }
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration: 50,
          agentUsed: 'test'
        };
      });

      const tasks = [
        {
          id: 'normal-task',
          type: 'content_generation' as const,
          dependencies: [],
          priority: 'medium' as const,
          estimatedDuration: 0.05,
          agent: 'test',
          payload: {}
        },
        {
          id: 'memory-intensive-task',
          type: 'content_generation' as const,
          dependencies: [],
          priority: 'medium' as const,
          estimatedDuration: 0.1,
          agent: 'test',
          payload: {}
        }
      ];

      const results = await orchestrator['executeTaskPipeline'](tasks);
      
      // Should handle memory pressure without crashing
      expect(results).toHaveLength(2);
      expect(memoryPressureSimulated).toHaveBeenCalled();
    });

    it('should handle stack overflow in deep dependency chains', async () => {
      // Create a very deep dependency chain
      const chainLength = 1000;
      const tasks = Array.from({ length: chainLength }, (_, i) => ({
        id: `task-${i}`,
        type: 'content_generation' as const,
        dependencies: i > 0 ? [`task-${i - 1}`] : [],
        priority: 'medium' as const,
        estimatedDuration: 0.001,
        agent: 'test',
        payload: { index: i }
      }));

      let maxCallStackDepth = 0;
      const callStack: string[] = [];

      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        callStack.push(task.id);
        maxCallStackDepth = Math.max(maxCallStackDepth, callStack.length);
        
        await new Promise(resolve => setTimeout(resolve, 1));
        
        callStack.pop();
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration: 1,
          agentUsed: 'test'
        };
      });

      // Should handle deep chains without stack overflow
      const results = await orchestrator['executeTaskPipeline'](tasks);
      
      expect(results).toHaveLength(chainLength);
      expect(maxCallStackDepth).toBeLessThan(100); // Should not have deep recursion
    });
  });

  describe('Data Corruption and Recovery', () => {
    it('should detect and handle corrupted task results', async () => {
      const corruptionLog: string[] = [];
      
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        // Simulate corrupted results for some tasks
        if (task.id.includes('corrupt')) {
          const corruptedResult = {
            taskId: task.id,
            success: true,
            result: undefined, // Missing required data
            duration: NaN, // Invalid number
            agentUsed: null as any // Invalid type
          };
          
          return corruptedResult;
        }
        
        return {
          taskId: task.id,
          success: true,
          result: { valid: true },
          duration: 50,
          agentUsed: 'test'
        };
      });

      // Override result validation
      const originalCompleteTask = orchestrator['completedTasks'];
      orchestrator['completedTasks'] = new Proxy(originalCompleteTask, {
        set(target, prop, value) {
          if (prop !== 'size' && value) {
            // Validate result
            if (!value.result || !Number.isFinite(value.duration) || !value.agentUsed) {
              corruptionLog.push(`Corrupted result detected for ${prop}`);
              
              // Attempt to repair
              value = {
                ...value,
                result: value.result || {},
                duration: Number.isFinite(value.duration) ? value.duration : 0,
                agentUsed: value.agentUsed || 'unknown'
              };
            }
          }
          return Reflect.set(target, prop, value);
        }
      });

      const tasks = [
        {
          id: 'normal-task',
          type: 'content_generation' as const,
          dependencies: [],
          priority: 'medium' as const,
          estimatedDuration: 0.05,
          agent: 'test',
          payload: {}
        },
        {
          id: 'corrupt-task',
          type: 'content_generation' as const,
          dependencies: [],
          priority: 'medium' as const,
          estimatedDuration: 0.05,
          agent: 'test',
          payload: {}
        }
      ];

      await orchestrator['executeTaskPipeline'](tasks);
      
      // Should detect corruption
      expect(corruptionLog.length).toBeGreaterThan(0);
    });

    it('should handle circular dependencies in task results', async () => {
      const circularReferenceDetected = jest.fn();
      
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        // Create circular reference in result
        const result: any = {
          id: task.id,
          data: {}
        };
        result.data.parent = result; // Circular reference
        
        // Attempt to serialize (would throw with circular reference)
        try {
          JSON.stringify(result);
        } catch (error) {
          if ((error as Error).message.includes('circular')) {
            circularReferenceDetected();
            
            // Return safe result
            return {
              taskId: task.id,
              success: true,
              result: { id: task.id, data: 'circular reference removed' },
              duration: 50,
              agentUsed: 'test'
            };
          }
        }
        
        return {
          taskId: task.id,
          success: true,
          result,
          duration: 50,
          agentUsed: 'test'
        };
      });

      const task = {
        id: 'circular-task',
        type: 'content_generation' as const,
        dependencies: [],
        priority: 'medium' as const,
        estimatedDuration: 0.05,
        agent: 'test',
        payload: {}
      };

      const results = await orchestrator['executeTaskPipeline']([task]);
      
      expect(circularReferenceDetected).toHaveBeenCalled();
      expect(results[0].success).toBe(true);
    });
  });

  describe('Timeout and Deadline Handling', () => {
    it('should enforce task timeouts', async () => {
      const timeoutEvents: string[] = [];
      
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        const timeout = task.payload.timeout as number || 5000;
        const duration = task.payload.duration as number || 100;
        
        return new Promise((resolve) => {
          const timer = setTimeout(() => {
            resolve({
              taskId: task.id,
              success: true,
              result: {},
              duration,
              agentUsed: 'test'
            });
          }, duration);
          
          // Simulate timeout enforcement
          setTimeout(() => {
            if (duration > timeout) {
              clearTimeout(timer);
              timeoutEvents.push(task.id);
              resolve({
                taskId: task.id,
                success: false,
                error: 'Task timeout',
                duration: timeout,
                agentUsed: 'test'
              });
            }
          }, timeout);
        });
      });

      const tasks = [
        {
          id: 'fast-task',
          type: 'content_generation' as const,
          dependencies: [],
          priority: 'medium' as const,
          estimatedDuration: 0.1,
          agent: 'test',
          payload: { duration: 50, timeout: 1000 }
        },
        {
          id: 'timeout-task',
          type: 'content_generation' as const,
          dependencies: [],
          priority: 'medium' as const,
          estimatedDuration: 2,
          agent: 'test',
          payload: { duration: 2000, timeout: 100 }
        }
      ];

      const results = await orchestrator['executeTaskPipeline'](tasks);
      
      expect(timeoutEvents).toContain('timeout-task');
      expect(results.find(r => r.taskId === 'timeout-task')?.success).toBe(false);
      expect(results.find(r => r.taskId === 'fast-task')?.success).toBe(true);
    });

    it('should handle orchestration-level deadlines', async () => {
      const deadline = Date.now() + 500; // 500ms deadline
      let tasksCompleted = 0;
      
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        // Check if deadline passed
        if (Date.now() > deadline) {
          return {
            taskId: task.id,
            success: false,
            error: 'Orchestration deadline exceeded',
            duration: 0,
            agentUsed: 'test'
          };
        }
        
        // Simulate varying task durations
        const duration = Math.random() * 200 + 50; // 50-250ms
        await new Promise(resolve => setTimeout(resolve, duration));
        
        tasksCompleted++;
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration,
          agentUsed: 'test'
        };
      });

      const tasks = Array.from({ length: 20 }, (_, i) => ({
        id: `task-${i}`,
        type: 'content_generation' as const,
        dependencies: [],
        priority: 'medium' as const,
        estimatedDuration: 0.15,
        agent: 'test',
        payload: {}
      }));

      const results = await orchestrator['executeTaskPipeline'](tasks);
      
      // Some tasks should complete, some should hit deadline
      expect(tasksCompleted).toBeGreaterThan(0);
      expect(tasksCompleted).toBeLessThan(tasks.length);
      
      const failedDueToDeadline = results.filter(r => 
        !r.success && r.error?.includes('deadline')
      ).length;
      expect(failedDueToDeadline).toBeGreaterThan(0);
    });
  });
});