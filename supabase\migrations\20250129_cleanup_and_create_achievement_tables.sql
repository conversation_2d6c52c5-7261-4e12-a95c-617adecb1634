-- Clean up and recreate achievement-related tables
-- This will drop and recreate tables to ensure clean state

-- First, drop everything that might exist
DROP TABLE IF EXISTS writing_sessions CASCADE;
DROP TABLE IF EXISTS ai_usage_logs CASCADE;

-- Now create them fresh

-- 1. Create AI usage logs table
CREATE TABLE ai_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  agent_type VARCHAR(100) NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10, 4) DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_ai_usage_logs_user ON ai_usage_logs(user_id);
CREATE INDEX idx_ai_usage_logs_created ON ai_usage_logs(created_at DESC);

-- Enable RLS
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate policies
CREATE POLICY "Users can view their own AI usage" ON ai_usage_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own AI usage" ON ai_usage_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Grant permissions
GRANT SELECT, INSERT ON ai_usage_logs TO authenticated;

-- 2. Create writing sessions table
CREATE TABLE writing_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  session_date DATE NOT NULL,
  words_written INTEGER DEFAULT 0,
  duration_minutes INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, session_date)
);

-- Create indexes
CREATE INDEX idx_writing_sessions_user ON writing_sessions(user_id);
CREATE INDEX idx_writing_sessions_date ON writing_sessions(session_date DESC);

-- Enable RLS
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own writing sessions" ON writing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own writing sessions" ON writing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON writing_sessions TO authenticated;

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE 'Successfully created:';
    RAISE NOTICE '✓ ai_usage_logs table';
    RAISE NOTICE '✓ writing_sessions table';
    RAISE NOTICE '';
    RAISE NOTICE 'You can now run the achievement system migration!';
END $$;