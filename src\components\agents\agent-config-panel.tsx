'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Settings, 
  Zap, 
  Brain, 
  Gauge,
  Save,
  RotateCcw,
  Info
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface AgentConfig {
  model: string
  temperature: number
  maxTokens: number
  qualityThreshold: number
  parallelProcessing: boolean
  maxConcurrentAgents: number
  retryAttempts: number
  enableAdaptivePlanning: boolean
}

interface AgentConfigPanelProps {
  projectId: string
}

export function AgentConfigPanel({ projectId }: AgentConfigPanelProps) {
  const { toast } = useToast()
  const [hasChanges, setHasChanges] = useState(false)
  const [config, setConfig] = useState<AgentConfig>({
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 4000,
    qualityThreshold: 85,
    parallelProcessing: true,
    maxConcurrentAgents: 3,
    retryAttempts: 3,
    enableAdaptivePlanning: true
  })

  const updateConfig = <K extends keyof AgentConfig>(key: K, value: AgentConfig[K]) => {
    setConfig(prev => ({ ...prev, [key]: value }))
    setHasChanges(true)
  }

  const saveConfiguration = async () => {
    try {
      // In a real implementation, this would save to the backend
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'Configuration Saved',
        description: 'Agent settings have been updated successfully',
      })
      setHasChanges(false)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save configuration',
        variant: 'destructive'
      })
    }
  }

  const resetToDefaults = () => {
    setConfig({
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 4000,
      qualityThreshold: 85,
      parallelProcessing: true,
      maxConcurrentAgents: 3,
      retryAttempts: 3,
      enableAdaptivePlanning: true
    })
    setHasChanges(true)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Agent Configuration</CardTitle>
              <CardDescription>
                Customize AI agent behavior and performance settings
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={resetToDefaults}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset to Defaults
              </Button>
              <Button
                size="sm"
                onClick={saveConfiguration}
                disabled={!hasChanges}
              >
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="general">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              {/* Model Selection */}
              <div className="space-y-2">
                <Label htmlFor="model">AI Model</Label>
                <Select
                  value={config.model}
                  onValueChange={(value) => updateConfig('model', value)}
                >
                  <SelectTrigger id="model">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4">GPT-4 (Highest Quality)</SelectItem>
                    <SelectItem value="gpt-4-turbo">GPT-4 Turbo (Faster)</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo (Budget)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Choose the AI model for all agents. Higher quality models produce better results but cost more.
                </p>
              </div>

              {/* Temperature */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="temperature">Creativity Level</Label>
                  <span className="text-sm text-muted-foreground">{config.temperature}</span>
                </div>
                <Slider
                  id="temperature"
                  min={0}
                  max={1}
                  step={0.1}
                  value={[config.temperature]}
                  onValueChange={([value]) => updateConfig('temperature', value)}
                />
                <p className="text-sm text-muted-foreground">
                  Higher values make output more creative but less predictable.
                </p>
              </div>

              {/* Max Tokens */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="maxTokens">Max Output Length</Label>
                  <span className="text-sm text-muted-foreground">{config.maxTokens} tokens</span>
                </div>
                <Slider
                  id="maxTokens"
                  min={1000}
                  max={8000}
                  step={500}
                  value={[config.maxTokens]}
                  onValueChange={([value]) => updateConfig('maxTokens', value)}
                />
                <p className="text-sm text-muted-foreground">
                  Maximum tokens per agent response. Higher values allow longer outputs.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-6">
              {/* Quality Threshold */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="quality">Quality Threshold</Label>
                  <Badge variant={config.qualityThreshold >= 90 ? 'default' : 'secondary'}>
                    {config.qualityThreshold}%
                  </Badge>
                </div>
                <Slider
                  id="quality"
                  min={50}
                  max={100}
                  step={5}
                  value={[config.qualityThreshold]}
                  onValueChange={([value]) => updateConfig('qualityThreshold', value)}
                />
                <p className="text-sm text-muted-foreground">
                  Minimum quality score required for agent outputs. Higher thresholds may require more processing time.
                </p>
              </div>

              {/* Parallel Processing */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="parallel">Parallel Processing</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow multiple agents to work simultaneously
                    </p>
                  </div>
                  <Switch
                    id="parallel"
                    checked={config.parallelProcessing}
                    onCheckedChange={(checked) => updateConfig('parallelProcessing', checked)}
                  />
                </div>
              </div>

              {/* Max Concurrent Agents */}
              {config.parallelProcessing && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="concurrent">Max Concurrent Agents</Label>
                    <Badge>{config.maxConcurrentAgents}</Badge>
                  </div>
                  <Slider
                    id="concurrent"
                    min={1}
                    max={5}
                    step={1}
                    value={[config.maxConcurrentAgents]}
                    onValueChange={([value]) => updateConfig('maxConcurrentAgents', value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Maximum number of agents that can run at the same time.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              {/* Retry Attempts */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="retries">Retry Attempts</Label>
                  <Badge variant="outline">{config.retryAttempts}</Badge>
                </div>
                <Slider
                  id="retries"
                  min={0}
                  max={5}
                  step={1}
                  value={[config.retryAttempts]}
                  onValueChange={([value]) => updateConfig('retryAttempts', value)}
                />
                <p className="text-sm text-muted-foreground">
                  Number of times to retry failed agent tasks.
                </p>
              </div>

              {/* Adaptive Planning */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="adaptive">Adaptive Planning</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow agents to adjust plans based on your changes
                    </p>
                  </div>
                  <Switch
                    id="adaptive"
                    checked={config.enableAdaptivePlanning}
                    onCheckedChange={(checked) => updateConfig('enableAdaptivePlanning', checked)}
                  />
                </div>
              </div>

              {/* Info Alert */}
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Advanced settings can significantly impact agent behavior and performance. 
                  Use with caution and monitor results closely.
                </AlertDescription>
              </Alert>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Configuration Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Configuration Tips
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div className="flex items-start gap-2">
            <Gauge className="h-4 w-4 text-muted-foreground mt-0.5" />
            <div>
              <p className="font-medium">Quality vs Speed</p>
              <p className="text-muted-foreground">
                Higher quality thresholds produce better content but take longer to process.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <Zap className="h-4 w-4 text-muted-foreground mt-0.5" />
            <div>
              <p className="font-medium">Parallel Processing</p>
              <p className="text-muted-foreground">
                Enable for faster results, but monitor for consistency across agent outputs.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <Settings className="h-4 w-4 text-muted-foreground mt-0.5" />
            <div>
              <p className="font-medium">Model Selection</p>
              <p className="text-muted-foreground">
                GPT-4 provides the best quality for complex creative tasks.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}