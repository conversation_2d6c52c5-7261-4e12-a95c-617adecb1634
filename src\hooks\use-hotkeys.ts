import { useEffect, useRef } from 'react'

type HotkeyCallback = (event: KeyboardEvent) => void

interface HotkeyOptions {
  enableOnFormTags?: boolean
  preventDefault?: boolean
  stopPropagation?: boolean
}

export function useHotkeys(
  keys: string,
  callback: HotkeyCallback,
  options: HotkeyOptions = {}
) {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    const {
      enableOnFormTags = false,
      preventDefault = true,
      stopPropagation = false
    } = options

    const handleKeyDown = (event: KeyboardEvent) => {
      // Skip if focusing on form elements (unless explicitly enabled)
      if (!enableOnFormTags) {
        const target = event.target as HTMLElement
        const tagName = target.tagName
        const isFormElement = ['INPUT', 'TEXTAREA', 'SELECT'].includes(tagName)
        const isContentEditable = target.contentEditable === 'true'
        
        if (isFormElement || isContentEditable) {
          return
        }
      }

      // Parse the key combination
      const keyCombo = keys.toLowerCase().split('+')
      const hasCtrl = keyCombo.includes('ctrl')
      const hasMeta = keyCombo.includes('meta') || keyCombo.includes('cmd')
      const hasAlt = keyCombo.includes('alt')
      const hasShift = keyCombo.includes('shift')
      
      // Get the actual key (last item after modifiers)
      const key = keyCombo[keyCombo.length - 1]

      // Check modifiers
      const ctrlPressed = hasCtrl ? (event.ctrlKey || event.metaKey) : false
      const metaPressed = hasMeta ? event.metaKey : false
      const altPressed = hasAlt ? event.altKey : false
      const shiftPressed = hasShift ? event.shiftKey : false

      // Check if all required modifiers are pressed
      const modifiersMatch = 
        (hasCtrl ? ctrlPressed : true) &&
        (hasMeta ? metaPressed : true) &&
        (hasAlt ? altPressed : true) &&
        (hasShift ? shiftPressed : true)

      // Check if the key matches
      const keyMatches = event.key.toLowerCase() === key

      // If everything matches, call the callback
      if (modifiersMatch && keyMatches) {
        if (preventDefault) {
          event.preventDefault()
        }
        if (stopPropagation) {
          event.stopPropagation()
        }
        callbackRef.current(event)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [keys, options])
}