import { useState, useEffect } from 'react';
import { writingSessionTracker } from '@/lib/services/writing-session-tracker';
import { useAuth } from '@/contexts/auth-context';
import { TIME_MS } from '@/lib/constants'
import { TIME_SECONDS } from '@/lib/constants'

interface SessionStats {
  duration: number;
  wordsWritten: number;
  wordsPerMinute: number;
}

export function useWritingSession(projectId?: string, chapterId?: string) {
  const { user } = useAuth();
  const [sessionStats, setSessionStats] = useState<SessionStats | null>(null);
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (!user || !projectId) {
      setSessionStats(null);
      setIsActive(false);
      return;
    }

    // Check for active session and update stats every second
    const interval = setInterval(() => {
      const stats = writingSessionTracker.getSessionStats(user.id, projectId, chapterId);
      
      if (stats) {
        setSessionStats(stats);
        setIsActive(true);
      } else {
        setSessionStats(null);
        setIsActive(false);
      }
    }, TIME_MS.SECOND);

    return () => clearInterval(interval);
  }, [user, projectId, chapterId]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / TIME_SECONDS.HOUR);
    const minutes = Math.floor((seconds % TIME_SECONDS.HOUR) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  return {
    isActive,
    duration: sessionStats?.duration || 0,
    wordsWritten: sessionStats?.wordsWritten || 0,
    wordsPerMinute: sessionStats?.wordsPerMinute || 0,
    formattedDuration: formatDuration(sessionStats?.duration || 0),
  };
}