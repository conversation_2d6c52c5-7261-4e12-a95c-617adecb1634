import { NextRequest, NextResponse } from 'next/server';
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';
import { createTypedServerClient } from '@/lib/supabase';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { logger } from '@/lib/services/logger'
const voiceProfileManager = new VoiceProfileManager();

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as 'author' | 'character' | 'narrator' | null;
    const projectId = searchParams.get('projectId');
    const seriesId = searchParams.get('seriesId');
    const isGlobal = searchParams.get('isGlobal') === 'true';

    const profiles = await voiceProfileManager.getUserVoiceProfiles(user.id, {
      type: type || undefined,
      projectId: projectId || undefined,
      seriesId: seriesId || undefined,
      isGlobal: isGlobal || undefined,
    });

    return UnifiedResponse.success({ profiles });
  } catch (error) {
    logger.error('Error fetching voice profiles:', error);
    return UnifiedResponse.error({
      message: 'Failed to fetch voice profiles',
      code: 'DATABASE_ERROR',
      details: error
    }, undefined, 500);
  }
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!;

    const body = await request.json();
    const { name, description, type, projectId, seriesId, characterId, isGlobal } = body;

    if (!name || !type) {
      return UnifiedResponse.validationError([{
        field: !name ? 'name' : 'type',
        message: 'This field is required'
      }]);
    }

    const profile = await voiceProfileManager.createVoiceProfile({
      name,
      description,
      type,
      projectId,
      seriesId,
      characterId,
      isGlobal,
    });

    if (!profile) {
      return UnifiedResponse.error({
        message: 'Failed to create voice profile',
        code: 'DATABASE_ERROR'
      }, undefined, 500);
    }

    return UnifiedResponse.created({ profile });
  } catch (error) {
    logger.error('Error creating voice profile:', error);
    return UnifiedResponse.error({
      message: 'Failed to create voice profile',
      code: 'DATABASE_ERROR',
      details: error
    }, undefined, 500);
  }
})