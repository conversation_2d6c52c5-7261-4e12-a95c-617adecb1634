-- Create user_map_preferences table for storing map viewport preferences
-- This table stores user-specific map view settings like zoom level, center position, and view options

CREATE TABLE IF NOT EXISTS user_map_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  
  -- Viewport settings
  viewport JSONB NOT NULL DEFAULT '{}',
  /* viewport structure:
    {
      "center": { "lat": 0, "lng": 0 },
      "zoom": 10,
      "bounds": {
        "north": 0,
        "south": 0,
        "east": 0,
        "west": 0
      },
      "rotation": 0,
      "pitch": 0
    }
  */
  
  -- Map display preferences
  map_style VARCHAR(50) DEFAULT 'default', -- default, satellite, terrain, dark, etc.
  show_labels BOOLEAN DEFAULT true,
  show_grid BOOLEAN DEFAULT false,
  show_connections BOOLEAN DEFAULT true, -- Show lines between related locations
  cluster_markers BOOLEAN DEFAULT true, -- Group nearby markers at low zoom
  
  -- Layer visibility
  visible_layers JSONB DEFAULT '[]', -- Array of layer IDs to show
  /* Example: ["cities", "regions", "landmarks", "characters_current"] */
  
  -- Filter preferences
  location_type_filters JSONB DEFAULT '[]', -- Which location types to show
  /* Example: ["city", "region", "building", "landmark"] */
  
  -- UI preferences
  sidebar_collapsed BOOLEAN DEFAULT false,
  minimap_visible BOOLEAN DEFAULT true,
  toolbar_position VARCHAR(20) DEFAULT 'top', -- top, bottom, left, right
  
  -- Last saved state
  last_viewed_location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  last_interaction TIMESTAMPTZ DEFAULT NOW(),
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Unique constraint: one preference set per user per project
  UNIQUE(user_id, project_id)
);

-- Create indexes for performance
CREATE INDEX idx_user_map_preferences_user_id ON user_map_preferences(user_id);
CREATE INDEX idx_user_map_preferences_project_id ON user_map_preferences(project_id);
CREATE INDEX idx_user_map_preferences_composite_user_project ON user_map_preferences(user_id, project_id);
CREATE INDEX idx_user_map_preferences_last_interaction ON user_map_preferences(last_interaction DESC);

-- Add RLS policies
ALTER TABLE user_map_preferences ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own preferences
CREATE POLICY "Users can view their map preferences" ON user_map_preferences
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy: Users can insert preferences for their accessible projects
CREATE POLICY "Users can create map preferences" ON user_map_preferences
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id
    AND project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
      UNION
      SELECT project_id FROM project_collaborators WHERE user_id = auth.uid()
    )
  );

-- Policy: Users can update their own preferences
CREATE POLICY "Users can update their map preferences" ON user_map_preferences
  FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own preferences
CREATE POLICY "Users can delete their map preferences" ON user_map_preferences
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_map_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  NEW.last_interaction = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER user_map_preferences_updated_at_trigger
  BEFORE UPDATE ON user_map_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_user_map_preferences_updated_at();

-- Function to get or create user map preferences
CREATE OR REPLACE FUNCTION get_or_create_user_map_preferences(
  p_user_id UUID,
  p_project_id UUID
) RETURNS user_map_preferences AS $$
DECLARE
  v_preferences user_map_preferences;
BEGIN
  -- Try to get existing preferences
  SELECT * INTO v_preferences
  FROM user_map_preferences
  WHERE user_id = p_user_id AND project_id = p_project_id;
  
  -- If not found, create new preferences with defaults
  IF v_preferences.id IS NULL THEN
    INSERT INTO user_map_preferences (user_id, project_id)
    VALUES (p_user_id, p_project_id)
    RETURNING * INTO v_preferences;
  END IF;
  
  RETURN v_preferences;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_or_create_user_map_preferences(UUID, UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE user_map_preferences IS 'Stores user-specific map viewport and display preferences';
COMMENT ON COLUMN user_map_preferences.viewport IS 'JSON object containing map viewport settings (center, zoom, bounds, etc.)';
COMMENT ON COLUMN user_map_preferences.map_style IS 'Map visual style (default, satellite, terrain, dark, etc.)';
COMMENT ON COLUMN user_map_preferences.visible_layers IS 'Array of layer IDs that should be visible';
COMMENT ON COLUMN user_map_preferences.location_type_filters IS 'Array of location types to display on the map';
COMMENT ON FUNCTION get_or_create_user_map_preferences IS 'Gets existing map preferences or creates default ones';