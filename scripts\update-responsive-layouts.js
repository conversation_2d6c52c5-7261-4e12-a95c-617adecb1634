const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Mapping of layout updates
const replacements = [
  // Update container classes to use wider variants
  { 
    pattern: /<div className="container">/g, 
    replacement: '<div className="container-wide">',
    filePattern: '**/*.tsx'
  },
  { 
    pattern: /className="container\s+/g, 
    replacement: 'className="container-wide ',
    filePattern: '**/*.tsx'
  },
  
  // Update grid layouts to be more responsive
  { 
    pattern: /grid-cols-2 lg:grid-cols-3/g, 
    replacement: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
    filePattern: '**/*.tsx'
  },
  { 
    pattern: /md:grid-cols-2 lg:grid-cols-3/g, 
    replacement: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
    filePattern: '**/*.tsx'
  },
  
  // Update spacing to be more responsive
  { 
    pattern: /className="(.*?)py-8(.*?)"/g, 
    replacement: 'className="$1py-6 sm:py-8 lg:py-10$2"',
    filePattern: '**/*.tsx'
  },
  { 
    pattern: /className="(.*?)px-6(.*?)"/g, 
    replacement: 'className="$1px-4 sm:px-6 lg:px-8$2"',
    filePattern: '**/*.tsx'
  },
  { 
    pattern: /className="(.*?)gap-4(.*?)"/g, 
    replacement: 'className="$1gap-4 sm:gap-5 lg:gap-6$2"',
    filePattern: '**/*.tsx'
  },
  
  // Update max-width constraints
  { 
    pattern: /max-w-6xl/g, 
    replacement: 'max-w-7xl xl:max-w-[1400px] 2xl:max-w-[1600px]',
    filePattern: '**/*.tsx'
  },
  { 
    pattern: /max-w-7xl/g, 
    replacement: 'max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px]',
    filePattern: '**/*.tsx'
  },
  
  // Update dialog/modal sizes
  { 
    pattern: /max-w-lg/g, 
    replacement: 'max-w-lg sm:max-w-xl lg:max-w-2xl',
    filePattern: '**/*.tsx'
  },
  { 
    pattern: /max-w-2xl/g, 
    replacement: 'max-w-2xl lg:max-w-3xl xl:max-w-4xl',
    filePattern: '**/*.tsx'
  },
];

// Files to exclude
const excludePatterns = [
  '**/node_modules/**',
  '**/build/**',
  '**/dist/**',
  '**/.next/**',
  '**/public/**',
  '**/*.test.tsx',
  '**/*.spec.tsx',
  '**/ui/*.tsx', // Don't update UI component library files
];

function updateFile(filePath, replacements) {
  let content = fs.readFileSync(filePath, 'utf-8');
  let updated = false;
  
  replacements.forEach(({ pattern, replacement }) => {
    const newContent = content.replace(pattern, replacement);
    if (newContent !== content) {
      content = newContent;
      updated = true;
    }
  });
  
  if (updated) {
    fs.writeFileSync(filePath, content, 'utf-8');
    return true;
  }
  
  return false;
}

// Group replacements by file pattern
const replacementsByPattern = {};
replacements.forEach(r => {
  const pattern = r.filePattern || '**/*.tsx';
  if (!replacementsByPattern[pattern]) {
    replacementsByPattern[pattern] = [];
  }
  replacementsByPattern[pattern].push(r);
});

let totalUpdated = 0;

// Process each file pattern
Object.entries(replacementsByPattern).forEach(([filePattern, patternReplacements]) => {
  const files = glob.sync(`src/${filePattern}`, {
    ignore: excludePatterns
  });
  
  files.forEach(file => {
    if (updateFile(file, patternReplacements)) {
      console.log(`Updated: ${file}`);
      totalUpdated++;
    }
  });
});

console.log(`\nTotal files updated: ${totalUpdated}`);
console.log('\nNote: This script makes basic responsive updates. Some components may need manual refinement.');
console.log('Remember to test the UI at different screen sizes after these updates.');