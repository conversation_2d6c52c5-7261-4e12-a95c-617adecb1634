/**
 * Custom ESLint plugin for BookScribe configuration enforcement
 * This plugin ensures developers use centralized configuration modules
 */

module.exports = {
  rules: {
    // Rule to enforce using centralized timing constants
    'use-timing-constants': {
      meta: {
        type: 'suggestion',
        docs: {
          description: 'Enforce using TIMING constants instead of hardcoded delays',
          category: 'Best Practices',
        },
        fixable: 'code',
        messages: {
          useTimingConstant: 'Use TIMING.{{category}}.{{constant}} from @/lib/config/animation-timing instead of hardcoded {{value}}ms',
        },
      },
      create(context) {
        const commonTimings = {
          150: { category: 'ANIMATION', constant: 'FAST' },
          200: { category: 'ANIMATION', constant: 'NORMAL' },
          300: { category: 'ANIMATION', constant: 'MEDIUM' },
          500: { category: 'ANIMATION', constant: 'SLOW' },
          1000: { category: 'RETRY', constant: 'INITIAL' },
          3000: { category: 'TOAST', constant: 'SUCCESS' },
          5000: { category: 'TOAST', constant: 'ERROR' },
        };

        return {
          CallExpression(node) {
            // Check setTimeout, setInterval, debounce calls
            if (
              ['setTimeout', 'setInterval'].includes(node.callee.name) ||
              (node.callee.property && ['debounce', 'throttle'].includes(node.callee.property.name))
            ) {
              const delayArg = node.arguments[1];
              if (delayArg && delayArg.type === 'Literal' && typeof delayArg.value === 'number') {
                const timing = commonTimings[delayArg.value];
                if (timing) {
                  context.report({
                    node: delayArg,
                    messageId: 'useTimingConstant',
                    data: {
                      ...timing,
                      value: delayArg.value,
                    },
                  });
                }
              }
            }
          },
        };
      },
    },

    // Rule to enforce using file size constants
    'use-file-size-constants': {
      meta: {
        type: 'suggestion',
        docs: {
          description: 'Enforce using FILE_LIMITS constants instead of hardcoded file sizes',
          category: 'Best Practices',
        },
        messages: {
          useFileSizeConstant: 'Use FILE_LIMITS.MAX_SIZES constants from @/lib/config/file-limits instead of hardcoded {{value}}',
        },
      },
      create(context) {
        const MB = 1024 * 1024;
        const commonSizes = [
          5 * MB,   // 5MB
          10 * MB,  // 10MB
          25 * MB,  // 25MB
          50 * MB,  // 50MB
          100 * MB, // 100MB
        ];

        return {
          Literal(node) {
            if (typeof node.value === 'number' && commonSizes.includes(node.value)) {
              // Check if it's likely a file size constant based on context
              const parent = node.parent;
              if (
                parent &&
                (parent.type === 'BinaryExpression' ||
                 (parent.type === 'VariableDeclarator' && /size|limit|max/i.test(parent.id.name)))
              ) {
                context.report({
                  node,
                  messageId: 'useFileSizeConstant',
                  data: {
                    value: `${node.value / MB}MB`,
                  },
                });
              }
            }
          },
        };
      },
    },

    // Rule to enforce using UI spacing constants
    'use-spacing-constants': {
      meta: {
        type: 'suggestion',
        docs: {
          description: 'Enforce using SPACING constants instead of hardcoded Tailwind spacing classes',
          category: 'Best Practices',
        },
        messages: {
          useSpacingConstant: 'Use SPACING constants from @/lib/config/ui-config instead of hardcoded className "{{value}}"',
        },
      },
      create(context) {
        const spacingPatterns = [
          /\bp-[0-9]+\b/,
          /\bm-[0-9]+\b/,
          /\bgap-[0-9]+\b/,
          /\bspace-[xy]-[0-9]+\b/,
        ];

        return {
          JSXAttribute(node) {
            if (node.name.name === 'className' && node.value) {
              const value = node.value.value || (node.value.expression && node.value.expression.value);
              if (typeof value === 'string') {
                for (const pattern of spacingPatterns) {
                  const match = value.match(pattern);
                  if (match) {
                    context.report({
                      node: node.value,
                      messageId: 'useSpacingConstant',
                      data: {
                        value: match[0],
                      },
                    });
                    break;
                  }
                }
              }
            }
          },
        };
      },
    },
  },
};