import { supabase } from '@/lib/supabase/client';
import { VoiceAnalyzer } from '@/lib/analysis/voice-analyzer';
import { logger } from '@/lib/services/logger';
import type { Database } from '@/lib/supabase/types';

type VoiceProfile = Database['public']['Tables']['voice_profiles']['Row'];
type VoiceProfileInsert = Database['public']['Tables']['voice_profiles']['Insert'];
type VoiceProfileUpdate = Database['public']['Tables']['voice_profiles']['Update'];
type VoiceTrainingSession = Database['public']['Tables']['voice_training_sessions']['Row'];
type SeriesCharacterContinuity = Database['public']['Tables']['series_character_continuity']['Row'];

export interface VoicePattern {
  sentenceStructure: {
    averageLength: number;
    complexityScore: number;
    variationScore: number;
    clausePatterns: string[];
  };
  vocabulary: {
    uniqueWords: number;
    averageWordLength: number;
    formalityScore: number;
    commonPhrases: string[];
    signatureWords: string[];
  };
  style: {
    descriptiveness: number;
    dialogueRatio: number;
    actionRatio: number;
    introspectionRatio: number;
    showVsTell: number;
  };
  tone: {
    emotionalRange: string[];
    intensity: number;
    consistency: number;
    moodProgression: string[];
  };
  rhythm: {
    punctuationPatterns: Record<string, number>;
    paragraphLengthVariation: number;
    transitionWords: string[];
    pacingScore: number;
  };
}

export interface VoiceConsistencySuggestion {
  type: 'style' | 'vocabulary' | 'tone' | 'rhythm' | 'structure';
  severity: 'low' | 'medium' | 'high';
  message: string;
  location?: { start: number; end: number };
  replacement?: string;
}

export interface VoiceDeviation {
  type: string;
  expected: string | number;
  actual: string | number;
  impact: 'minor' | 'moderate' | 'significant';
  description: string;
}

export class VoiceProfileManager {
  private voiceAnalyzer: VoiceAnalyzer;

  constructor() {
    this.voiceAnalyzer = new VoiceAnalyzer();
  }

  /**
   * Create a new voice profile
   */
  async createVoiceProfile(data: {
    name: string;
    description?: string;
    type: 'author' | 'character' | 'narrator';
    projectId?: string;
    seriesId?: string;
    characterId?: string;
    isGlobal?: boolean;
  }): Promise<VoiceProfile | null> {
    try {
      const { data: profile, error } = await supabase
        .from('voice_profiles')
        .insert({
          name: data.name,
          description: data.description,
          type: data.type,
          project_id: data.projectId,
          series_id: data.seriesId,
          character_id: data.characterId,
          is_global: data.isGlobal || false,
          patterns: {},
          sample_texts: [],
          confidence: 0.0,
          training_samples_count: 0,
          total_words_analyzed: 0,
        })
        .select()
        .single();

      if (error) throw error;
      return profile;
    } catch (error) {
      logger.error('Error creating voice profile:', error);
      return null;
    }
  }

  /**
   * Train a voice profile with new text samples
   */
  async trainVoiceProfile(
    profileId: string,
    texts: string[],
    source: 'manual_entry' | 'file_upload' | 'project_content' = 'manual_entry'
  ): Promise<boolean> {
    try {
      // Create training sessions for each text
      const sessions = await Promise.all(
        texts.map(async (text) => {
          const { data: session, error } = await supabase
            .from('voice_training_sessions')
            .insert({
              voice_profile_id: profileId,
              input_text: text,
              input_source: source,
              status: 'processing',
            })
            .select()
            .single();

          if (error) throw error;
          return session;
        })
      );

      // Get existing profile
      const { data: profile, error: profileError } = await supabase
        .from('voice_profiles')
        .select('*')
        .eq('id', profileId)
        .single();

      if (profileError || !profile) throw profileError || new Error('Profile not found');

      // Combine existing samples with new ones
      const allTexts = [...(profile.sample_texts || []), ...texts];
      
      // Analyze voice patterns
      const analysisResult = await this.voiceAnalyzer.analyzeUserVoice(
        allTexts,
        profile.project_id || profileId
      );

      // Update profile with new patterns
      const { error: updateError } = await supabase
        .from('voice_profiles')
        .update({
          patterns: analysisResult.patterns as unknown as Database['public']['Tables']['voice_profiles']['Update']['patterns'],
          sample_texts: allTexts.slice(-10), // Keep last 10 samples
          confidence: analysisResult.confidence / 100,
          training_samples_count: allTexts.length,
          total_words_analyzed: allTexts.join(' ').split(/\s+/).length,
          updated_at: new Date().toISOString(),
        })
        .eq('id', profileId);

      if (updateError) throw updateError;

      // Update training sessions
      await Promise.all(
        sessions.map(async (session) => {
          if (!session) return;
          
          await supabase
            .from('voice_training_sessions')
            .update({
              status: 'completed',
              analysis_results: analysisResult.patterns as unknown as Database['public']['Tables']['voice_training_sessions']['Update']['analysis_results'],
              patterns_extracted: analysisResult.patterns as unknown as Database['public']['Tables']['voice_training_sessions']['Update']['patterns_extracted'],
            })
            .eq('id', session.id);
        })
      );

      return true;
    } catch (error) {
      logger.error('Error training voice profile:', error);
      
      // Mark sessions as failed
      await supabase
        .from('voice_training_sessions')
        .update({
          status: 'failed',
          error_message: error instanceof Error ? error.message : 'Unknown error',
        })
        .eq('voice_profile_id', profileId)
        .eq('status', 'processing');
      
      return false;
    }
  }

  /**
   * Get voice profiles for a user
   */
  async getUserVoiceProfiles(userId: string, filters?: {
    type?: 'author' | 'character' | 'narrator';
    projectId?: string;
    seriesId?: string;
    isGlobal?: boolean;
  }): Promise<VoiceProfile[]> {
    try {
      let query = supabase
        .from('voice_profiles')
        .select('*')
        .or(`user_id.eq.${userId},is_global.eq.true`);

      if (filters?.type) {
        query = query.eq('type', filters.type);
      }
      if (filters?.projectId) {
        query = query.eq('project_id', filters.projectId);
      }
      if (filters?.seriesId) {
        query = query.eq('series_id', filters.seriesId);
      }
      if (filters?.isGlobal !== undefined) {
        query = query.eq('is_global', filters.isGlobal);
      }

      const { data, error } = await query.order('updated_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Error fetching voice profiles:', error);
      return [];
    }
  }

  /**
   * Analyze text for voice consistency
   */

  async analyzeVoiceConsistency(
    content: string,
    profileId: string,
    projectId?: string,
    chapterId?: string
  ): Promise<{
    score: number;
    suggestions: VoiceConsistencySuggestion[];
    deviations: VoiceDeviation[];
  }> {
    try {
      // Get voice profile
      const { data: profile, error: profileError } = await supabase
        .from('voice_profiles')
        .select('*')
        .eq('id', profileId)
        .single();

      if (profileError || !profile) {
        throw profileError || new Error('Profile not found');
      }

      // Convert profile to expected format
      const voiceProfile = {
        id: profile.id,
        projectId: profile.project_id || '',
        patterns: profile.patterns as VoicePattern,
        sampleTexts: profile.sample_texts || [],
        confidence: (profile.confidence || 0) * 100,
        lastUpdated: new Date(profile.updated_at),
      };

      // Analyze content
      const suggestions = await this.voiceAnalyzer.analyzeVoiceMatch(content, voiceProfile);

      // Calculate consistency score
      const deviations = suggestions.filter(s => s.severity === 'suggestion');
      const score = Math.max(0, 1 - (deviations.length * 0.05));

      // Save consistency check if project/chapter provided
      if (projectId) {
        await supabase
          .from('voice_consistency_checks')
          .insert({
            project_id: projectId,
            chapter_id: chapterId,
            voice_profile_id: profileId,
            consistency_score: score,
            deviations: deviations as unknown as Database['public']['Tables']['voice_consistency_checks']['Insert']['deviations'],
            suggestions: suggestions as unknown as Database['public']['Tables']['voice_consistency_checks']['Insert']['suggestions'],
            content_analyzed: content.substring(0, 1000), // First 1000 chars
            word_count: content.split(/\s+/).length,
          });
      }

      return {
        score,
        suggestions,
        deviations,
      };
    } catch (error) {
      logger.error('Error analyzing voice consistency:', error);
      return {
        score: 0,
        suggestions: [],
        deviations: [],
      };
    }
  }

  /**
   * Apply voice profile to content generation
   */
  async getVoiceGuidelines(profileId: string): Promise<string> {
    try {
      const { data: profile, error } = await supabase
        .from('voice_profiles')
        .select('*')
        .eq('id', profileId)
        .single();

      if (error || !profile) {
        throw error || new Error('Profile not found');
      }

      const patterns = profile.patterns as VoicePattern;
      
      return `
Voice Guidelines for "${profile.name}":

SENTENCE STRUCTURE:
- Average sentence length: ${patterns.sentenceStructure?.averageLength || 15} words
- Complexity level: ${patterns.sentenceStructure?.complexityScore || 50}/100
- Vary sentence lengths by ${patterns.sentenceStructure?.variationScore || 30}%

VOCABULARY:
- Formality level: ${patterns.vocabulary?.formalityScore || 50}/100
- Common phrases to use: ${patterns.vocabulary?.commonPhrases?.join(', ') || 'none identified'}
- Signature words: ${patterns.vocabulary?.signatureWords?.join(', ') || 'none identified'}

STYLE:
- Descriptiveness: ${patterns.style?.descriptiveness || 50}/100
- Dialogue ratio: ${patterns.style?.dialogueRatio || 25}%
- Action vs introspection: ${patterns.style?.actionRatio || 50}/${patterns.style?.introspectionRatio || 50}

TONE:
- Emotional range: ${patterns.tone?.emotionalRange?.join(', ') || 'neutral'}
- Intensity: ${patterns.tone?.intensity || 50}/100
- Maintain ${patterns.tone?.consistency || 80}% consistency

RHYTHM:
- Paragraph variation: ${patterns.rhythm?.paragraphLengthVariation || 30}%
- Transition words: ${patterns.rhythm?.transitionWords?.join(', ') || 'standard transitions'}
- Pacing: ${patterns.rhythm?.pacingScore || 50}/100
`;
    } catch (error) {
      logger.error('Error getting voice guidelines:', error);
      return '';
    }
  }

  /**
   * Track character continuity across series
   */
  async updateCharacterContinuity(
    seriesId: string,
    characterName: string,
    data: {
      status?: 'active' | 'inactive' | 'deceased' | 'written_out' | 'mentioned_only';
      lastAppearanceBook?: number;
      statusChangeBook?: number;
      statusChangeReason?: string;
      voiceProfileId?: string;
    }
  ): Promise<SeriesCharacterContinuity | null> {
    try {
      // Check if character continuity exists
      const { data: existing, error: checkError } = await supabase
        .from('series_character_continuity')
        .select('*')
        .eq('series_id', seriesId)
        .eq('character_name', characterName)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existing) {
        // Update existing
        const { data: updated, error: updateError } = await supabase
          .from('series_character_continuity')
          .update({
            ...data,
            updated_at: new Date().toISOString(),
          })
          .eq('id', existing.id)
          .select()
          .single();

        if (updateError) throw updateError;
        return updated;
      } else {
        // Create new
        const { data: created, error: createError } = await supabase
          .from('series_character_continuity')
          .insert({
            series_id: seriesId,
            character_name: characterName,
            first_appearance_book: 1,
            ...data,
          })
          .select()
          .single();

        if (createError) throw createError;
        return created;
      }
    } catch (error) {
      logger.error('Error updating character continuity:', error);
      return null;
    }
  }

  /**
   * Get character continuity for a series
   */
  async getSeriesCharacterContinuity(seriesId: string): Promise<SeriesCharacterContinuity[]> {
    try {
      const { data, error } = await supabase
        .from('series_character_continuity')
        .select('*')
        .eq('series_id', seriesId)
        .order('character_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Error fetching character continuity:', error);
      return [];
    }
  }

  /**
   * Clone a voice profile
   */
  async cloneVoiceProfile(
    profileId: string,
    newName: string,
    targetScope?: {
      projectId?: string;
      seriesId?: string;
      characterId?: string;
    }
  ): Promise<VoiceProfile | null> {
    try {
      // Get original profile
      const { data: original, error: fetchError } = await supabase
        .from('voice_profiles')
        .select('*')
        .eq('id', profileId)
        .single();

      if (fetchError || !original) {
        throw fetchError || new Error('Original profile not found');
      }

      // Create new profile
      const { data: cloned, error: createError } = await supabase
        .from('voice_profiles')
        .insert({
          name: newName,
          description: `Cloned from "${original.name}"`,
          type: original.type,
          patterns: original.patterns,
          sample_texts: original.sample_texts,
          confidence: original.confidence,
          training_method: original.training_method,
          training_samples_count: original.training_samples_count,
          total_words_analyzed: original.total_words_analyzed,
          is_global: false,
          project_id: targetScope?.projectId,
          series_id: targetScope?.seriesId,
          character_id: targetScope?.characterId,
          parent_profile_id: profileId,
          version: (original.version || 0) + 1,
        })
        .select()
        .single();

      if (createError) throw createError;
      return cloned;
    } catch (error) {
      logger.error('Error cloning voice profile:', error);
      return null;
    }
  }
}