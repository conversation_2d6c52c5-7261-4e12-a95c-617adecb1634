import { NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';
import { applyRateLimit } from '@/lib/rate-limiter-unified';

// Validation schema
const searchSchema = z.object({
  q: z.string().min(1).max(100),
  projectId: z.string().uuid(),
  page: z.string().optional().default('1').transform(val => parseInt(val, 10)),
  limit: z.string().optional().default('20').transform(val => Math.min(parseInt(val, 10), 50)),
  contentTypes: z.string().optional().transform(val => val ? val.split(',') : []),
  dateRange: z.enum(['all', 'today', 'week', 'month', 'custom']).optional().default('all'),
  sortBy: z.enum(['relevance', 'date', 'title', 'type']).optional().default('relevance'),
  includeArchived: z.string().optional().default('false').transform(val => val === 'true')
});

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'search', cost: 1 });
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Parse and validate query parameters
    const searchParams = Object.fromEntries(request.nextUrl.searchParams);
    const validationResult = searchSchema.safeParse(searchParams);

    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid search parameters',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400);
    }

    const { 
      q: query, 
      projectId, 
      page, 
      limit, 
      contentTypes, 
      dateRange, 
      sortBy, 
      includeArchived 
    } = validationResult.data;

    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (!projectAccess) {
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single();

      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403);
      }
    }

    // Calculate date filter
    let dateFilter = null;
    if (dateRange !== 'all') {
      const now = new Date();
      switch (dateRange) {
        case 'today':
          dateFilter = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          dateFilter = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          dateFilter = new Date(now.setMonth(now.getMonth() - 1));
          break;
      }
    }

    // Search across different content types
    const searchPromises = [];
    const searchQuery = `%${query.toLowerCase()}%`;

    // Search chapters
    if (contentTypes.length === 0 || contentTypes.includes('chapter')) {
      const chaptersQuery = supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count, updated_at')
        .eq('project_id', projectId)
        .or(`title.ilike.${searchQuery},content.ilike.${searchQuery}`)
        .limit(limit);

      if (dateFilter) {
        chaptersQuery.gte('updated_at', dateFilter.toISOString());
      }

      if (!includeArchived) {
        chaptersQuery.eq('is_archived', false);
      }

      searchPromises.push(
        chaptersQuery.then(({ data, error }) => {
          if (error) {
            logger.error('Error searching chapters:', error);
            return [];
          }
          return (data || []).map(chapter => ({
            id: chapter.id,
            type: 'chapter' as const,
            title: chapter.title || `Chapter ${chapter.chapter_number}`,
            content: chapter.content || '',
            excerpt: getExcerpt(chapter.content || '', query),
            relevanceScore: calculateRelevance(chapter.title + ' ' + chapter.content, query),
            highlights: getHighlights(chapter.content || '', query),
            metadata: {
              chapterNumber: chapter.chapter_number,
              wordCount: chapter.word_count,
              lastModified: chapter.updated_at
            }
          }));
        })
      );
    }

    // Search characters
    if (contentTypes.length === 0 || contentTypes.includes('character')) {
      const charactersQuery = supabase
        .from('characters')
        .select('id, name, description, role, backstory, personality, appearance, updated_at')
        .eq('project_id', projectId)
        .or(`name.ilike.${searchQuery},description.ilike.${searchQuery},backstory.ilike.${searchQuery}`);

      if (dateFilter) {
        charactersQuery.gte('updated_at', dateFilter.toISOString());
      }

      searchPromises.push(
        charactersQuery.then(({ data, error }) => {
          if (error) {
            logger.error('Error searching characters:', error);
            return [];
          }
          return (data || []).map(character => {
            const searchableContent = `${character.name} ${character.description || ''} ${character.backstory || ''}`;
            return {
              id: character.id,
              type: 'character' as const,
              title: character.name,
              content: searchableContent,
              excerpt: getExcerpt(character.description || character.backstory || '', query),
              relevanceScore: calculateRelevance(searchableContent, query),
              highlights: getHighlights(searchableContent, query),
              metadata: {
                characterRole: character.role,
                lastModified: character.updated_at
              }
            };
          });
        })
      );
    }

    // Search locations
    if (contentTypes.length === 0 || contentTypes.includes('location')) {
      const locationsQuery = supabase
        .from('locations')
        .select('id, name, description, location_type, significance, updated_at')
        .eq('project_id', projectId)
        .or(`name.ilike.${searchQuery},description.ilike.${searchQuery},significance.ilike.${searchQuery}`);

      if (dateFilter) {
        locationsQuery.gte('updated_at', dateFilter.toISOString());
      }

      searchPromises.push(
        locationsQuery.then(({ data, error }) => {
          if (error) {
            logger.error('Error searching locations:', error);
            return [];
          }
          return (data || []).map(location => {
            const searchableContent = `${location.name} ${location.description || ''} ${location.significance || ''}`;
            return {
              id: location.id,
              type: 'location' as const,
              title: location.name,
              content: searchableContent,
              excerpt: getExcerpt(location.description || location.significance || '', query),
              relevanceScore: calculateRelevance(searchableContent, query),
              highlights: getHighlights(searchableContent, query),
              metadata: {
                locationType: location.location_type,
                lastModified: location.updated_at
              }
            };
          });
        })
      );
    }

    // Search story bible
    if (contentTypes.length === 0 || contentTypes.includes('story_bible')) {
      const storyBibleQuery = supabase
        .from('story_bible')
        .select('id, entry_type, entry_key, entry_data, updated_at')
        .eq('project_id', projectId)
        .or(`entry_key.ilike.${searchQuery},entry_data::text.ilike.${searchQuery}`);

      if (dateFilter) {
        storyBibleQuery.gte('updated_at', dateFilter.toISOString());
      }

      searchPromises.push(
        storyBibleQuery.then(({ data, error }) => {
          if (error) {
            logger.error('Error searching story bible:', error);
            return [];
          }
          return (data || []).map(entry => {
            const content = JSON.stringify(entry.entry_data);
            return {
              id: entry.id,
              type: 'story_bible' as const,
              title: entry.entry_key || entry.entry_type,
              content,
              excerpt: getExcerpt(content, query),
              relevanceScore: calculateRelevance(content, query),
              highlights: getHighlights(content, query),
              metadata: {
                entryType: entry.entry_type,
                lastModified: entry.updated_at
              }
            };
          });
        })
      );
    }

    // Execute all searches in parallel
    const searchResults = await Promise.all(searchPromises);
    let allResults = searchResults.flat();

    // Sort results
    switch (sortBy) {
      case 'relevance':
        allResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
        break;
      case 'date':
        allResults.sort((a, b) => 
          new Date(b.metadata.lastModified).getTime() - new Date(a.metadata.lastModified).getTime()
        );
        break;
      case 'title':
        allResults.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'type':
        allResults.sort((a, b) => a.type.localeCompare(b.type));
        break;
    }

    // Paginate results
    const startIdx = (page - 1) * limit;
    const paginatedResults = allResults.slice(startIdx, startIdx + limit);

    return UnifiedResponse.success({
      results: paginatedResults,
      total: allResults.length,
      page,
      pageSize: limit,
      totalPages: Math.ceil(allResults.length / limit)
    });
  } catch (error) {
    logger.error('Error in content search:', error);
    return UnifiedResponse.error({
      message: 'Failed to search content',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

// Helper functions
function getExcerpt(content: string, query: string): string {
  const lowerContent = content.toLowerCase();
  const lowerQuery = query.toLowerCase();
  const index = lowerContent.indexOf(lowerQuery);
  
  if (index === -1) {
    return content.slice(0, 150) + (content.length > 150 ? '...' : '');
  }
  
  const start = Math.max(0, index - 50);
  const end = Math.min(content.length, index + query.length + 50);
  
  return (start > 0 ? '...' : '') + 
         content.slice(start, end) + 
         (end < content.length ? '...' : '');
}

function getHighlights(content: string, query: string): string[] {
  const highlights: string[] = [];
  const lowerContent = content.toLowerCase();
  const lowerQuery = query.toLowerCase();
  const words = lowerQuery.split(' ').filter(w => w.length > 2);
  
  for (const word of words) {
    let index = 0;
    while ((index = lowerContent.indexOf(word, index)) !== -1 && highlights.length < 3) {
      const start = Math.max(0, index - 20);
      const end = Math.min(content.length, index + word.length + 20);
      const highlight = content.slice(start, end);
      const highlighted = highlight.replace(
        new RegExp(`(${word})`, 'gi'),
        '<mark>$1</mark>'
      );
      highlights.push(highlighted);
      index += word.length;
    }
  }
  
  return highlights;
}

function calculateRelevance(content: string, query: string): number {
  const lowerContent = content.toLowerCase();
  const lowerQuery = query.toLowerCase();
  const words = lowerQuery.split(' ').filter(w => w.length > 0);
  
  let score = 0;
  
  // Exact match bonus
  if (lowerContent.includes(lowerQuery)) {
    score += 10;
  }
  
  // Word matches
  for (const word of words) {
    const matches = (lowerContent.match(new RegExp(word, 'gi')) || []).length;
    score += matches * 2;
  }
  
  // Title match bonus (assuming title is at the beginning)
  if (lowerContent.slice(0, 100).includes(lowerQuery)) {
    score += 5;
  }
  
  return score;
}