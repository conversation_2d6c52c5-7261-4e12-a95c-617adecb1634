"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  Target, 
  TrendingUp, 
  Clock, 
  BookOpen,
  Award,
  Zap,
  Users,
  CheckCircle
} from "lucide-react";
import { DEMO_ANALYTICS } from "@/lib/demo/mock-data";

// Extract mock data from centralized source
const { stats: mockStats, progress: mockProgress, chapterStats: mockChapterStats, goals: mockGoals } = DEMO_ANALYTICS;

const mockInsights = [
  {
    type: 'productivity',
    title: 'Peak Writing Hours',
    description: 'You write 34% more words between 9-11 AM',
    icon: Clock,
    color: 'blue'
  },
  {
    type: 'quality',
    title: 'Dialogue Strength',
    description: 'Your dialogue scenes score 15% higher than average',
    icon: Users,
    color: 'green'
  },
  {
    type: 'pacing',
    title: 'Chapter Pacing',
    description: 'Consider varying chapter lengths for better rhythm',
    icon: BarChart3,
    color: 'amber'
  },
  {
    type: 'consistency',
    title: 'Character Voice',
    description: 'Aria&apos;s voice consistency: 94% across all chapters',
    icon: Award,
    color: 'purple'
  }
];

export function DemoAnalytics() {
  const [_selectedTimeframe, _setSelectedTimeframe] = useState('week');
  
  const completionPercentage = Math.round((mockStats.totalWords / mockStats.targetWords) * 100);
  const dailyProgress = Math.round((mockStats.todayWords / mockStats.dailyGoal) * 100);

  return (
    <div className="max-w-6xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-2">
              <BarChart3 className="w-6 h-6 text-primary" />
              Writing Analytics & Progress
            </CardTitle>
            <Badge variant="outline" className="border-primary/50 text-primary">
              Demo Data
            </Badge>
          </div>
          
          <p className="text-muted-foreground mt-2">
            Track your writing progress, analyze patterns, and get insights to improve your productivity and story quality.
          </p>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="progress">Progress</TabsTrigger>
              <TabsTrigger value="chapters">Chapters</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="border-border bg-card">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Words</p>
                        <p className="text-2xl font-bold text-primary">{mockStats.totalWords.toLocaleString()}</p>
                      </div>
                      <BookOpen className="w-8 h-8 text-primary" />
                    </div>
                    <div className="mt-2">
                      <Progress value={completionPercentage} className="h-2" />
                      <p className="text-xs text-muted-foreground mt-1">{completionPercentage}% of target</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border bg-card">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Today&apos;s Progress</p>
                        <p className="text-2xl font-bold text-success">{mockStats.todayWords.toLocaleString()}</p>
                      </div>
                      <Target className="w-8 h-8 text-success" />
                    </div>
                    <div className="mt-2">
                      <Progress value={dailyProgress} className="h-2 [&>div]:bg-success" />
                      <p className="text-xs text-muted-foreground mt-1">{dailyProgress}% of daily goal</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border bg-card">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Writing Streak</p>
                        <p className="text-2xl font-bold text-metric">{mockStats.streak}</p>
                      </div>
                      <Zap className="w-8 h-8 text-metric" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">days in a row</p>
                  </CardContent>
                </Card>

                <Card className="border-border bg-card">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Chapters Done</p>
                        <p className="text-2xl font-bold text-info">{mockStats.chaptersComplete}/{mockStats.totalChapters}</p>
                      </div>
                      <CheckCircle className="w-8 h-8 text-info" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">{Math.round((mockStats.chaptersComplete / mockStats.totalChapters) * 100)}% complete</p>
                  </CardContent>
                </Card>
              </div>

              {/* Goals Progress */}
              <Card className="border-border bg-card">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Writing Goals
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {mockGoals.map((goal, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{goal.type} Goal</span>
                          <span className="text-sm text-muted-foreground">
                            {goal.current.toLocaleString()} / {goal.target.toLocaleString()} {goal.unit}
                          </span>
                        </div>
                        <Progress 
                          value={goal.progress} 
                          className={`h-2 ${
                            goal.progress >= 100 ? '[&>div]:bg-success' :
                            goal.progress >= 80 ? '[&>div]:bg-warning' :
                            '[&>div]:bg-info'
                          }`}
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>{goal.progress}% complete</span>
                          <span>{goal.progress >= 100 ? '🎉 Goal achieved!' : `${goal.target - goal.current} ${goal.unit} to go`}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card className="border-border bg-card">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Recent Writing Sessions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {DEMO_ANALYTICS.recentSessions.map((session, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded border border-border">
                        <div>
                          <p className="text-sm font-medium">{session.chapter}</p>
                          <p className="text-xs text-muted-foreground">{session.time}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{session.words} words</p>
                          <p className="text-xs text-muted-foreground">{session.duration}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="progress" className="space-y-6">
              <Card className="border-border bg-card">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    7-Day Writing Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockProgress.map((day, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">{new Date(day.date).toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}</span>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{day.words} words</span>
                            <span>{day.time}h</span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <div className="flex-1">
                            <Progress value={(day.words / 2500) * 100} className="h-2" />
                          </div>
                          <div className="w-16">
                            <Progress value={(day.time / 4) * 100} className="h-2 [&>div]:bg-metric" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex justify-between text-xs text-muted-foreground">
                    <span>Words per day</span>
                    <span>Hours per day</span>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="chapters" className="space-y-6">
              <Card className="border-border bg-card">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    Chapter Progress & Quality
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {mockChapterStats.map((chapter) => (
                      <div key={chapter.chapter} className="p-4 bg-muted/50 rounded border border-border">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <h4 className="font-medium">Chapter {chapter.chapter}: {chapter.title}</h4>
                            <p className="text-sm text-muted-foreground">{chapter.words.toLocaleString()} / {chapter.target.toLocaleString()} words</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className={`text-xs ${
                              chapter.status === 'Complete' ? 'border-success/50 text-success' :
                              chapter.status === 'In Progress' ? 'border-warning/50 text-primary' :
                              ''
                            }`}>
                              {chapter.status}
                            </Badge>
                            {chapter.quality > 0 && (
                              <Badge variant="outline" className="text-xs border-metric/50 text-metric">
                                {chapter.quality}% Quality
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="space-y-1">
                          <Progress 
                            value={(chapter.words / chapter.target) * 100} 
                            className={`h-2 ${
                              chapter.status === 'Complete' ? '[&>div]:bg-success' :
                              chapter.status === 'In Progress' ? '[&>div]:bg-warning' :
                              '[&>div]:bg-muted'
                            }`}
                          />
                          {chapter.quality > 0 && (
                            <Progress value={chapter.quality} className="h-1 [&>div]:bg-achievement" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="insights" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mockInsights.map((insight, index) => {
                  const IconComponent = insight.icon;
                  return (
                    <Card key={index} className={`border-${insight.color}-500/20 bg-${insight.color}-500/10`}>
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <IconComponent className={`w-5 h-5 text-${insight.color}-500 mt-0.5`} />
                          <div>
                            <h4 className={`font-medium text-${insight.color}-400`}>{insight.title}</h4>
                            <p className={`text-sm text-${insight.color}-200/80 mt-1`}>{insight.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              <Card className="border-border bg-card">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Writing Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {[
                      { title: 'First Chapter', desc: 'Completed your first chapter', earned: true },
                      { title: 'Streak Master', desc: '20+ day writing streak', earned: true },
                      { title: 'Word Warrior', desc: 'Wrote 50,000+ words', earned: false },
                      { title: 'Quality Writer', desc: '90%+ average quality score', earned: true },
                      { title: 'Consistent Creator', desc: 'Met daily goal 30 days', earned: false },
                      { title: 'Story Architect', desc: 'Completed story outline', earned: true }
                    ].map((achievement, index) => (
                      <div key={index} className={`p-3 rounded border ${
                        achievement.earned 
                          ? 'border-warning/50 bg-warning/10' 
                          : 'border-border bg-card'
                      }`}>
                        <div className="flex items-center gap-2 mb-1">
                          <Award className={`w-4 h-4 ${
                            achievement.earned ? 'text-primary' : 'text-muted-foreground'
                          }`} />
                          <h5 className={`font-medium text-sm ${
                            achievement.earned ? 'text-primary' : 'text-muted-foreground'
                          }`}>
                            {achievement.title}
                          </h5>
                        </div>
                        <p className="text-xs text-muted-foreground">{achievement.desc}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
