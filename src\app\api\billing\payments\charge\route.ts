import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server';
import { createTypedServerClient } from '@/lib/supabase';
import { stripe, STRIPE_PRICES } from '@/lib/stripe';
import { config } from '@/lib/config';
import type Stripe from 'stripe';
import type { SubscriptionStatus } from '@/lib/db/types/enums';
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'

// Extended Stripe subscription type with period properties
interface StripeSubscriptionWithPeriods extends Stripe.Subscription {
  current_period_start: number;
  current_period_end: number;
}

// Validation schemas
const chargeRequestSchema = z.discriminatedUnion('type', [
  // Subscription charge
  z.object({
    type: z.literal('subscription'),
    paymentMethodId: z.string().regex(/^pm_[a-zA-Z0-9_]+$/, 'Invalid payment method ID'),
    tierId: z.string().refine(
      (id) => Object.values(STRIPE_PRICES).includes(id),
      'Invalid subscription tier'
    )
  }),
  // One-time payment charge
  z.object({
    type: z.literal('one_time'),
    paymentMethodId: z.string().regex(/^pm_[a-zA-Z0-9_]+$/, 'Invalid payment method ID'),
    amount: z.number()
      .int('Amount must be an integer')
      .min(50, 'Amount must be at least 50 cents')
      .max(999999, 'Amount exceeds maximum allowed'),
    currency: z.string()
      .regex(/^[a-z]{3}$/, 'Currency must be a 3-letter ISO code')
      .default('usd'),
    description: baseSchemas.description.max(500).optional()
  })
]);

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: chargeRequestSchema,
    rateLimitKey: 'payment-charge',
    rateLimitCost: 15, // Very high cost for payment operations
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const body = await req.json();
      
      // Check if user has an active subscription when trying to create another
      if (body.type === 'subscription') {
        const supabase = await createTypedServerClient();
        const { data: existingSubscription } = await supabase
          .from('user_subscriptions')
          .select('id, status')
          .eq('user_id', user.id)
          .in('status', ['active', 'trialing'])
          .single();

        if (existingSubscription) {
          return { 
            valid: false, 
            error: 'You already have an active subscription. Please cancel it first.' 
          };
        }
      }

      // Check for recent charge attempts to prevent abuse
      const supabase = await createTypedServerClient();
      const recentCharges = await supabase
        .from('payment_attempts')
        .select('id')
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 300000).toISOString()) // Last 5 minutes
        .limit(5);

      if (recentCharges.data && recentCharges.data.length >= 5) {
        return { 
          valid: false, 
          error: 'Too many payment attempts. Please wait a few minutes.' 
        };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const body = context.body;

  try {
    const supabase = await createTypedServerClient();

    // Record payment attempt
    await supabase
      .from('payment_attempts')
      .insert({
        user_id: user.id,
        type: body.type,
        amount: body.type === 'one_time' ? body.amount : null,
        metadata: {
          clientIP: context.clientIP,
          tierId: body.type === 'subscription' ? body.tierId : null
        }
      });

    // Get or create customer
    let customer: Stripe.Customer;
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (profile?.stripe_customer_id) {
      try {
        customer = await stripe.customers.retrieve(profile.stripe_customer_id) as Stripe.Customer;
        
        // Check if customer was deleted in Stripe
        if ((customer as any).deleted) {
          throw new Error('Customer was deleted');
        }
      } catch (error) {
        // Customer doesn't exist or was deleted, create new one
        logger.warn('Stripe customer not found, creating new', {
          userId: user.id,
          customerId: profile.stripe_customer_id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        
        customer = await stripe.customers.create({
          email: user.email!,
          metadata: { userId: user.id }
        });
        
        // Update profile with new customer ID
        await supabase
          .from('profiles')
          .update({ stripe_customer_id: customer.id })
          .eq('id', user.id);
      }
    } else {
      customer = await stripe.customers.create({
        email: user.email!,
        metadata: { userId: user.id }
      });

      // Save customer ID to profile
      await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email!,
          stripe_customer_id: customer.id
        });
    }

    // Attach payment method to customer
    try {
      await stripe.paymentMethods.attach(body.paymentMethodId, {
        customer: customer.id,
      });
    } catch (error) {
      logger.error('Failed to attach payment method', {
        userId: user.id,
        customerId: customer.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP: context.clientIP
      });
      
      if (error instanceof Error && error.message.includes('already been attached')) {
        // Payment method already attached, continue
      } else {
        return UnifiedResponse.error(
          'Failed to attach payment method. Please try a different card.',
          400
        );
      }
    }

    if (body.type === 'subscription') {
      // Create subscription with proper error handling
      let subscription: Stripe.Subscription;
      try {
        subscription = await stripe.subscriptions.create({
          customer: customer.id,
          items: [{ price: body.tierId }],
          default_payment_method: body.paymentMethodId,
          payment_behavior: 'default_incomplete',
          expand: ['latest_invoice.payment_intent'],
          metadata: {
            userId: user.id,
            tierId: body.tierId,
            createdAt: new Date().toISOString()
          }
        });
      } catch (error) {
        logger.error('Failed to create subscription', {
          userId: user.id,
          tierId: body.tierId,
          error: error instanceof Error ? error.message : 'Unknown error',
          clientIP: context.clientIP
        });
        
        return UnifiedResponse.error(
          'Failed to create subscription. Please check your payment details.',
          400
        );
      }

      // Save subscription to database
      const stripeSubscription = subscription as unknown as StripeSubscriptionWithPeriods;
      const { error: dbError } = await supabase.from('user_subscriptions').insert({
        user_id: user.id,
        tier_id: body.tierId,
        status: stripeSubscription.status as SubscriptionStatus,
        stripe_subscription_id: stripeSubscription.id,
        stripe_customer_id: customer.id,
        current_period_start: new Date(stripeSubscription.current_period_start * TIME_MS.SECOND),
        current_period_end: new Date(stripeSubscription.current_period_end * TIME_MS.SECOND),
        cancel_at_period_end: stripeSubscription.cancel_at_period_end
      });

      if (dbError) {
        logger.error('Failed to save subscription to database', {
          userId: user.id,
          subscriptionId: subscription.id,
          error: dbError.message,
          clientIP: context.clientIP
        });
        // Don't fail the request - subscription was created in Stripe
      }

      logger.info('Subscription created successfully', {
        userId: user.id,
        subscriptionId: subscription.id,
        tierId: body.tierId,
        status: subscription.status,
        clientIP: context.clientIP
      });

      const latestInvoice = subscription.latest_invoice as Stripe.Invoice;
      const paymentIntent = latestInvoice?.payment_intent as Stripe.PaymentIntent;

      return UnifiedResponse.success({
        subscriptionId: subscription.id,
        status: subscription.status,
        clientSecret: paymentIntent?.client_secret || null,
        requiresAction: subscription.status === 'incomplete' && paymentIntent?.status === 'requires_action',
        message: 'Subscription created successfully'
      });
    } else {
      // One-time payment
      let paymentIntent: Stripe.PaymentIntent;
      try {
        paymentIntent = await stripe.paymentIntents.create({
          amount: body.amount,
          currency: body.currency || 'usd',
          customer: customer.id,
          payment_method: body.paymentMethodId,
          confirmation_method: 'manual',
          confirm: true,
          description: body.description || `Payment from ${user.email}`,
          metadata: {
            userId: user.id,
            type: 'one_time_payment',
            createdAt: new Date().toISOString()
          }
        });
      } catch (error) {
        logger.error('Failed to create payment intent', {
          userId: user.id,
          amount: body.amount,
          error: error instanceof Error ? error.message : 'Unknown error',
          clientIP: context.clientIP
        });
        
        return UnifiedResponse.error(
          'Payment failed. Please check your payment details.',
          400
        );
      }

      // Store payment intent in database
      await supabase
        .from('payment_intents')
        .insert({
          id: paymentIntent.id,
          user_id: user.id,
          amount: body.amount,
          currency: body.currency || 'usd',
          status: paymentIntent.status,
          description: body.description,
          metadata: {
            type: 'one_time',
            clientIP: context.clientIP
          }
        });

      logger.info('One-time payment processed', {
        userId: user.id,
        paymentIntentId: paymentIntent.id,
        amount: body.amount,
        status: paymentIntent.status,
        clientIP: context.clientIP
      });

      return UnifiedResponse.success({
        paymentIntentId: paymentIntent.id,
        status: paymentIntent.status,
        clientSecret: paymentIntent.client_secret,
        requiresAction: paymentIntent.status === 'requires_action',
        message: 'Payment processed successfully'
      });
    }
  } catch (error) {
    logger.error('Stripe charge error:', error, {
      userId: user.id,
      type: body.type,
      clientIP: context.clientIP
    });
    
    if (error instanceof Stripe.errors.StripeError) {
      return UnifiedResponse.error(
        error.message || 'Payment processing error',
        error.statusCode || 400
      );
    }
    
    return UnifiedResponse.error('Failed to process payment');
  }
});

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': config.app.url,
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // 24 hours
    },
  });
}