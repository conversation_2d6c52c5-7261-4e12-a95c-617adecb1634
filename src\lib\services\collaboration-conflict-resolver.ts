import { logger } from './logger'
import type { CollaborationChange } from './unified-collaboration-service'

export interface ConflictResolutionStrategy {
  type: 'merge' | 'last-write-wins' | 'first-write-wins' | 'manual'
  priority?: 'owner' | 'editor' | 'timestamp'
}

export interface TextConflict {
  id: string
  sessionId: string
  documentId: string
  changes: CollaborationChange[]
  resolvedChange?: CollaborationChange
  status: 'pending' | 'resolved' | 'rejected'
  timestamp: number
}

export interface ConflictResolutionResult {
  success: boolean
  mergedChange?: CollaborationChange
  conflicts?: TextConflict[]
  requiresManualResolution?: boolean
}

export class CollaborationConflictResolver {
  private static instance: CollaborationConflictResolver
  private pendingConflicts: Map<string, TextConflict[]> = new Map()
  private resolutionStrategy: ConflictResolutionStrategy = {
    type: 'merge',
    priority: 'timestamp'
  }

  private constructor() {}

  static getInstance(): CollaborationConflictResolver {
    if (!CollaborationConflictResolver.instance) {
      CollaborationConflictResolver.instance = new CollaborationConflictResolver()
    }
    return CollaborationConflictResolver.instance
  }

  /**
   * Set the conflict resolution strategy
   */
  setStrategy(strategy: ConflictResolutionStrategy): void {
    this.resolutionStrategy = strategy
    logger.info('Conflict resolution strategy updated', { strategy })
  }

  /**
   * Detect and resolve conflicts between multiple changes
   */
  async resolveConflicts(
    documentId: string,
    changes: CollaborationChange[]
  ): Promise<ConflictResolutionResult> {
    if (changes.length === 0) {
      return { success: true }
    }

    if (changes.length === 1) {
      return { success: true, mergedChange: changes[0] }
    }

    // Sort changes by timestamp
    const sortedChanges = [...changes].sort((a, b) => a.timestamp - b.timestamp)

    // Detect overlapping changes
    const conflicts = this.detectConflicts(sortedChanges)

    if (conflicts.length === 0) {
      // No conflicts, apply changes in order
      return {
        success: true,
        mergedChange: this.mergeNonConflictingChanges(sortedChanges)
      }
    }

    // Handle conflicts based on strategy
    switch (this.resolutionStrategy.type) {
      case 'merge':
        return this.mergeConflicts(documentId, conflicts, sortedChanges)
      case 'last-write-wins':
        return this.lastWriteWins(conflicts, sortedChanges)
      case 'first-write-wins':
        return this.firstWriteWins(conflicts, sortedChanges)
      case 'manual':
        return this.requireManualResolution(documentId, conflicts)
      default:
        return { success: false, conflicts }
    }
  }

  /**
   * Detect overlapping changes that conflict
   */
  private detectConflicts(changes: CollaborationChange[]): TextConflict[] {
    const conflicts: TextConflict[] = []
    
    for (let i = 0; i < changes.length; i++) {
      for (let j = i + 1; j < changes.length; j++) {
        const change1 = changes[i]
        const change2 = changes[j]
        
        if (!change1 || !change2) continue
        
        if (this.rangesOverlap(change1.range, change2.range)) {
          // Check if conflict already exists
          const existingConflict = conflicts.find(c => 
            c.changes.some(ch => ch.userId === change1.userId) &&
            c.changes.some(ch => ch.userId === change2.userId)
          )
          
          if (existingConflict) {
            // Add to existing conflict
            if (!existingConflict.changes.find(ch => ch === change1)) {
              existingConflict.changes.push(change1)
            }
            if (!existingConflict.changes.find(ch => ch === change2)) {
              existingConflict.changes.push(change2)
            }
          } else {
            // Create new conflict
            conflicts.push({
              id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              sessionId: change1.sessionId,
              documentId: '',
              changes: [change1, change2],
              status: 'pending',
              timestamp: Date.now()
            })
          }
        }
      }
    }
    
    return conflicts
  }

  /**
   * Check if two ranges overlap
   */
  private rangesOverlap(
    range1: CollaborationChange['range'],
    range2: CollaborationChange['range']
  ): boolean {
    // If ranges are on different lines, check for line overlap
    if (range1.endLine < range2.startLine || range2.endLine < range1.startLine) {
      return false
    }
    
    // If on same line, check column overlap
    if (range1.startLine === range2.startLine && range1.endLine === range2.endLine) {
      return !(range1.endColumn < range2.startColumn || range2.endColumn < range1.startColumn)
    }
    
    // Ranges span multiple lines and overlap
    return true
  }

  /**
   * Merge non-conflicting changes
   */
  private mergeNonConflictingChanges(changes: CollaborationChange[]): CollaborationChange {
    // For now, return the last change
    // In a real implementation, this would merge all changes
    return changes[changes.length - 1]!
  }

  /**
   * Merge conflicting changes using operational transformation
   */
  private async mergeConflicts(
    documentId: string,
    conflicts: TextConflict[],
    allChanges: CollaborationChange[]
  ): Promise<ConflictResolutionResult> {
    try {
      // Apply operational transformation to merge changes
      const mergedChanges: CollaborationChange[] = []
      const unresolvedConflicts: TextConflict[] = []
      
      for (const conflict of conflicts) {
        const merged = await this.operationalTransform(conflict.changes)
        if (merged) {
          conflict.resolvedChange = merged
          conflict.status = 'resolved'
          mergedChanges.push(merged)
        } else {
          conflict.status = 'pending'
          unresolvedConflicts.push(conflict)
        }
      }
      
      // Store unresolved conflicts
      if (unresolvedConflicts.length > 0) {
        this.pendingConflicts.set(documentId, unresolvedConflicts)
      }
      
      return {
        success: unresolvedConflicts.length === 0,
        mergedChange: mergedChanges.length > 0 ? mergedChanges[0] : undefined,
        conflicts: unresolvedConflicts,
        requiresManualResolution: unresolvedConflicts.length > 0
      }
    } catch (error) {
      logger.error('Failed to merge conflicts', error)
      return {
        success: false,
        conflicts,
        requiresManualResolution: true
      }
    }
  }

  /**
   * Operational transformation to merge changes
   */
  private async operationalTransform(
    changes: CollaborationChange[]
  ): Promise<CollaborationChange | null> {
    if (changes.length !== 2) {
      // Complex multi-way merge not implemented yet
      return null
    }
    
    const [change1, change2] = changes
    if (!change1 || !change2) return null
    
    // Handle different operation types
    if (change1.type === 'insert' && change2.type === 'insert') {
      return this.mergeInserts(change1, change2)
    } else if (change1.type === 'delete' && change2.type === 'delete') {
      return this.mergeDeletes(change1, change2)
    } else if (
      (change1.type === 'insert' && change2.type === 'delete') ||
      (change1.type === 'delete' && change2.type === 'insert')
    ) {
      // Insert/delete conflicts require manual resolution
      return null
    } else if (change1.type === 'replace' || change2.type === 'replace') {
      return this.mergeReplaces(change1, change2)
    }
    
    return null
  }

  /**
   * Merge two insert operations
   */
  private mergeInserts(
    change1: CollaborationChange,
    change2: CollaborationChange
  ): CollaborationChange {
    // If inserts are at the same position, concatenate based on timestamp
    if (
      change1.range.startLine === change2.range.startLine &&
      change1.range.startColumn === change2.range.startColumn
    ) {
      const [first, second] = change1.timestamp < change2.timestamp 
        ? [change1, change2] 
        : [change2, change1]
      
      return {
        ...first,
        text: first.text + second.text,
        timestamp: Date.now()
      }
    }
    
    // If inserts are at different positions, apply the earlier one first
    return change1.timestamp < change2.timestamp ? change1 : change2
  }

  /**
   * Merge two delete operations
   */
  private mergeDeletes(
    change1: CollaborationChange,
    change2: CollaborationChange
  ): CollaborationChange {
    // Calculate the union of deleted ranges
    const startLine = Math.min(change1.range.startLine, change2.range.startLine)
    const endLine = Math.max(change1.range.endLine, change2.range.endLine)
    const startColumn = change1.range.startLine === startLine 
      ? change1.range.startColumn 
      : change2.range.startColumn
    const endColumn = change1.range.endLine === endLine 
      ? change1.range.endColumn 
      : change2.range.endColumn
    
    return {
      ...change1,
      range: {
        startLine,
        startColumn,
        endLine,
        endColumn
      },
      timestamp: Date.now()
    }
  }

  /**
   * Merge replace operations
   */
  private mergeReplaces(
    change1: CollaborationChange,
    change2: CollaborationChange
  ): CollaborationChange | null {
    // Replace operations that overlap typically can't be auto-merged
    // Use priority-based resolution
    if (this.resolutionStrategy.priority === 'timestamp') {
      return change1.timestamp > change2.timestamp ? change1 : change2
    }
    
    // Otherwise require manual resolution
    return null
  }

  /**
   * Last-write-wins resolution strategy
   */
  private lastWriteWins(
    conflicts: TextConflict[],
    allChanges: CollaborationChange[]
  ): ConflictResolutionResult {
    const latestChange = allChanges.reduce((latest, current) => 
      current.timestamp > latest.timestamp ? current : latest
    )
    
    // Mark all conflicts as resolved
    conflicts.forEach(conflict => {
      conflict.resolvedChange = latestChange
      conflict.status = 'resolved'
    })
    
    return {
      success: true,
      mergedChange: latestChange
    }
  }

  /**
   * First-write-wins resolution strategy
   */
  private firstWriteWins(
    conflicts: TextConflict[],
    allChanges: CollaborationChange[]
  ): ConflictResolutionResult {
    const earliestChange = allChanges.reduce((earliest, current) => 
      current.timestamp < earliest.timestamp ? current : earliest
    )
    
    // Mark all conflicts as resolved
    conflicts.forEach(conflict => {
      conflict.resolvedChange = earliestChange
      conflict.status = 'resolved'
    })
    
    return {
      success: true,
      mergedChange: earliestChange
    }
  }

  /**
   * Mark conflicts for manual resolution
   */
  private requireManualResolution(
    documentId: string,
    conflicts: TextConflict[]
  ): ConflictResolutionResult {
    // Store conflicts for UI to handle
    this.pendingConflicts.set(documentId, conflicts)
    
    return {
      success: false,
      conflicts,
      requiresManualResolution: true
    }
  }

  /**
   * Get pending conflicts for a document
   */
  getPendingConflicts(documentId: string): TextConflict[] {
    return this.pendingConflicts.get(documentId) || []
  }

  /**
   * Resolve a conflict manually
   */
  resolveConflictManually(
    documentId: string,
    conflictId: string,
    resolution: CollaborationChange
  ): void {
    const conflicts = this.pendingConflicts.get(documentId) || []
    const conflictIndex = conflicts.findIndex(c => c.id === conflictId)
    
    if (conflictIndex !== -1) {
      conflicts[conflictIndex]!.resolvedChange = resolution
      conflicts[conflictIndex]!.status = 'resolved'
      
      // Remove resolved conflicts
      const remainingConflicts = conflicts.filter(c => c.status === 'pending')
      if (remainingConflicts.length > 0) {
        this.pendingConflicts.set(documentId, remainingConflicts)
      } else {
        this.pendingConflicts.delete(documentId)
      }
      
      logger.info('Conflict resolved manually', { documentId, conflictId })
    }
  }

  /**
   * Clear all conflicts for a document
   */
  clearConflicts(documentId: string): void {
    this.pendingConflicts.delete(documentId)
  }
}

export const conflictResolver = CollaborationConflictResolver.getInstance()