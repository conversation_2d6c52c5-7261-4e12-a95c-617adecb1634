import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { WritingAgent } from '@/lib/agents/writing-agent';
import { VoiceAwareWritingAgent } from '@/lib/agents/voice-aware-writing-agent';
import { ContentGenerator } from '@/lib/services/content-generator';
import { ContextManager } from '@/lib/services/context-manager';
import type { BookContext, ChapterOutline, SceneContent } from '@/lib/agents/types';
import type { ProjectSettings } from '@/lib/types/project-settings';

// Mock dependencies
jest.mock('@/lib/agents/writing-agent');
jest.mock('@/lib/agents/voice-aware-writing-agent');
jest.mock('@/lib/services/content-generator');
jest.mock('@/lib/services/context-manager');

describe('Content Generation Pipeline Tests', () => {
  let orchestrator: AdvancedAgentOrchestrator;
  let mockWritingAgent: jest.Mocked<WritingAgent>;
  let mockVoiceAwareWritingAgent: jest.Mocked<VoiceAwareWritingAgent>;
  let mockContentGenerator: jest.Mocked<ContentGenerator>;
  let mockContextManager: jest.Mocked<ContextManager>;
  
  const mockProjectSettings: ProjectSettings = {
    primaryGenre: 'fantasy',
    secondaryGenres: ['adventure'],
    targetAudience: 'adult',
    writingStyle: 'descriptive',
    narrativeVoice: 'third-person',
    tense: 'past',
    pacing: 'medium',
    violenceLevel: 'moderate',
    romanceLevel: 'low',
    profanityLevel: 'mild',
    themeDepth: 'deep',
    worldBuildingDepth: 'extensive',
    characterComplexity: 'complex',
    plotComplexity: 'complex',
    tone: 'serious',
    dialogueStyle: 'natural',
    descriptionLevel: 'detailed',
    useDeepPOV: true,
    showDontTell: true,
    varyProse: true,
    useSymbolism: true,
    useCliffhangers: true,
    useForeshadowing: true,
    useFlashbacks: false,
    useUnreliableNarrator: false,
    protagonistTypes: ['hero'],
    antagonistTypes: ['villain'],
    supportingRoles: ['mentor', 'sidekick'],
    majorThemes: ['courage', 'friendship'],
    minorThemes: ['sacrifice'],
    culturalElements: [],
    magicSystemType: 'soft',
    technologyLevel: 'medieval',
    politicalSystem: 'monarchy',
    economicSystem: 'feudal',
    geographyType: 'earth-like',
    pacingPreference: 'medium'
  };

  const mockContext: BookContext = {
    projectId: 'test-project',
    settings: mockProjectSettings,
    projectSelections: mockProjectSettings,
    storyPrompt: 'A hero saves the world from darkness',
    targetWordCount: 80000,
    targetChapters: 20
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    orchestrator = new AdvancedAgentOrchestrator(3);
    
    mockWritingAgent = WritingAgent.prototype as jest.Mocked<WritingAgent>;
    mockVoiceAwareWritingAgent = VoiceAwareWritingAgent.prototype as jest.Mocked<VoiceAwareWritingAgent>;
    mockContentGenerator = ContentGenerator.prototype as jest.Mocked<ContentGenerator>;
    mockContextManager = ContextManager.prototype as jest.Mocked<ContextManager>;
  });

  describe('Sequential Chapter Generation', () => {
    it('should generate chapters in correct order maintaining continuity', async () => {
      const chapterOutlines: ChapterOutline[] = [
        {
          chapterNumber: 1,
          title: 'The Beginning',
          outline: 'Hero discovers their destiny',
          targetWordCount: 4000,
          scenes: [
            {
              sceneNumber: 1,
              description: 'Opening scene',
              characters: ['Hero'],
              location: 'Village',
              purpose: 'Establish setting',
              conflict: 'Internal doubt',
              expectedWordCount: 2000
            }
          ],
          characterArcs: ['Hero introduction'],
          plotPoints: ['Inciting incident'],
          themes: ['courage']
        },
        {
          chapterNumber: 2,
          title: 'The Call',
          outline: 'Hero receives the call to adventure',
          targetWordCount: 4000,
          scenes: [
            {
              sceneNumber: 1,
              description: 'Meeting the mentor',
              characters: ['Hero', 'Mentor'],
              location: 'Tavern',
              purpose: 'Introduce mentor',
              conflict: 'Reluctance to accept',
              expectedWordCount: 2000
            }
          ],
          characterArcs: ['Hero meets mentor'],
          plotPoints: ['Call to adventure'],
          themes: ['destiny']
        }
      ];

      const generatedChapters: any[] = [];
      let previousEnding = '';

      // Mock content generation to track continuity
      mockContentGenerator.generateContent = jest.fn().mockImplementation(async (request) => {
        const chapterNumber = request.context?.chapterNumber as number || 1;
        const content = `Chapter ${chapterNumber} content. Previous ending: "${previousEnding}"`;
        const ending = `End of chapter ${chapterNumber}.`;
        
        generatedChapters.push({
          chapterNumber,
          content,
          ending,
          previousEnding
        });

        previousEnding = ending;

        return {
          chapterNumber,
          title: chapterOutlines[chapterNumber - 1].title,
          content,
          wordCount: 4000,
          ending,
          scenes: [],
          metadata: {
            readingTime: 15,
            sentimentScore: 0.7,
            paceScore: 0.6
          }
        };
      });

      // Generate chapters sequentially
      for (const outline of chapterOutlines) {
        await mockContentGenerator.generateContent({
          type: 'chapter',
          prompt: `Generate chapter ${outline.chapterNumber}`,
          context: {
            chapterNumber: outline.chapterNumber,
            outline,
            previousEnding
          },
          projectId: mockContext.projectId
        });
      }

      // Verify chapters were generated in order
      expect(generatedChapters).toHaveLength(2);
      expect(generatedChapters[0].chapterNumber).toBe(1);
      expect(generatedChapters[1].chapterNumber).toBe(2);

      // Verify continuity was maintained
      expect(generatedChapters[1].previousEnding).toBe('End of chapter 1.');
      expect(generatedChapters[1].content).toContain('Previous ending: "End of chapter 1."');
    });

    it('should handle character voice consistency across chapters', async () => {
      const characterVoices = new Map([
        ['Hero', {
          speakingStyle: 'Direct and brave',
          vocabulary: 'Simple but heartfelt',
          mannerisms: ['Scratches head when thinking', 'Clenches fists when determined']
        }],
        ['Mentor', {
          speakingStyle: 'Wise and measured',
          vocabulary: 'Archaic and philosophical',
          mannerisms: ['Strokes beard', 'Speaks in riddles']
        }]
      ]);

      const dialogueGenerations: any[] = [];

      // Mock voice-aware content generation
      mockVoiceAwareWritingAgent.generateChapter = jest.fn().mockImplementation(async (params) => {
        const { chapterOutline, characterStates } = params;
        
        // Simulate dialogue generation with voice profiles
        const dialogue = [];
        for (const [character, voice] of characterVoices) {
          dialogue.push({
            character,
            line: `Dialogue in ${voice.speakingStyle} style`,
            context: voice
          });
        }

        dialogueGenerations.push({
          chapterNumber: chapterOutline.chapterNumber,
          dialogue,
          characterStates
        });

        return {
          success: true,
          data: {
            chapterNumber: chapterOutline.chapterNumber,
            content: `Chapter with consistent character voices`,
            dialogue,
            wordCount: 4000
          }
        };
      });

      // Generate multiple chapters
      for (let i = 1; i <= 3; i++) {
        await mockVoiceAwareWritingAgent.generateChapter({
          chapterOutline: {
            chapterNumber: i,
            title: `Chapter ${i}`,
            outline: 'Chapter outline',
            targetWordCount: 4000,
            scenes: []
          },
          previousChapterEnding: '',
          characterStates: characterVoices,
          storyContext: {
            storyStructure: {} as any,
            completedChapters: []
          }
        });
      }

      // Verify voice consistency
      expect(dialogueGenerations).toHaveLength(3);
      
      // Check that character voices remain consistent across chapters
      for (const generation of dialogueGenerations) {
        const heroDialogue = generation.dialogue.find((d: any) => d.character === 'Hero');
        expect(heroDialogue.line).toContain('Direct and brave');
        
        const mentorDialogue = generation.dialogue.find((d: any) => d.character === 'Mentor');
        expect(mentorDialogue.line).toContain('Wise and measured');
      }
    });
  });

  describe('Parallel Scene Generation', () => {
    it('should generate independent scenes in parallel', async () => {
      const scenes: SceneContent[] = [
        {
          sceneNumber: 1,
          content: '',
          wordCount: 0,
          characters: ['Hero'],
          location: 'Forest',
          emotionalTone: 'tense',
          plotProgression: 'Hero gets lost'
        },
        {
          sceneNumber: 2,
          content: '',
          wordCount: 0,
          characters: ['Villain'],
          location: 'Castle',
          emotionalTone: 'menacing',
          plotProgression: 'Villain plans attack'
        },
        {
          sceneNumber: 3,
          content: '',
          wordCount: 0,
          characters: ['Mentor'],
          location: 'Tower',
          emotionalTone: 'contemplative',
          plotProgression: 'Mentor senses danger'
        }
      ];

      const startTimes: Record<number, number> = {};
      const endTimes: Record<number, number> = {};

      // Mock parallel scene generation
      mockContentGenerator.generateContent = jest.fn().mockImplementation(async (request) => {
        const sceneNumber = request.context?.sceneNumber as number;
        startTimes[sceneNumber] = Date.now();
        
        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 100));
        
        endTimes[sceneNumber] = Date.now();
        
        return {
          sceneNumber,
          content: `Scene ${sceneNumber} content`,
          wordCount: 1000
        };
      });

      // Generate scenes in parallel
      const scenePromises = scenes.map(scene => 
        mockContentGenerator.generateContent({
          type: 'scene',
          prompt: `Generate scene ${scene.sceneNumber}`,
          context: { sceneNumber: scene.sceneNumber, scene },
          projectId: mockContext.projectId
        })
      );

      const startTime = Date.now();
      await Promise.all(scenePromises);
      const totalTime = Date.now() - startTime;

      // Verify parallel execution
      expect(totalTime).toBeLessThan(200); // Should be ~100ms, not 300ms
      
      // Check overlap in execution times
      const times = Object.keys(startTimes).map(Number).sort();
      for (let i = 0; i < times.length - 1; i++) {
        const currentEnd = endTimes[times[i]];
        const nextStart = startTimes[times[i + 1]];
        
        // Next scene should start before current scene ends (parallel execution)
        expect(nextStart).toBeLessThan(currentEnd);
      }
    });

    it('should merge parallel scene results correctly', async () => {
      const sceneResults: any[] = [];

      mockContentGenerator.generateContent = jest.fn().mockImplementation(async (request) => {
        const result = {
          sceneNumber: request.context?.sceneNumber,
          content: `Scene ${request.context?.sceneNumber} content`,
          wordCount: 1000,
          characters: request.context?.scene?.characters || [],
          emotionalBeats: ['tension', 'resolution']
        };
        
        sceneResults.push(result);
        return result;
      });

      // Generate multiple scenes
      const scenes = Array.from({ length: 5 }, (_, i) => ({
        sceneNumber: i + 1,
        characters: [`Character${i + 1}`]
      }));

      await Promise.all(scenes.map(scene =>
        mockContentGenerator.generateContent({
          type: 'scene',
          prompt: 'Generate scene',
          context: { sceneNumber: scene.sceneNumber, scene },
          projectId: mockContext.projectId
        })
      ));

      // Verify all scenes were generated
      expect(sceneResults).toHaveLength(5);
      
      // Verify scene order can be reconstructed
      const sortedScenes = sceneResults.sort((a, b) => a.sceneNumber - b.sceneNumber);
      for (let i = 0; i < sortedScenes.length; i++) {
        expect(sortedScenes[i].sceneNumber).toBe(i + 1);
      }
      
      // Calculate total word count
      const totalWordCount = sceneResults.reduce((sum, scene) => sum + scene.wordCount, 0);
      expect(totalWordCount).toBe(5000);
    });
  });

  describe('Quality Control Pipeline', () => {
    it('should perform quality checks on generated content', async () => {
      const qualityChecks = {
        consistency: { score: 0, issues: [] as string[] },
        pacing: { score: 0, issues: [] as string[] },
        characterization: { score: 0, issues: [] as string[] },
        prose: { score: 0, issues: [] as string[] }
      };

      // Mock quality assessment
      const assessQuality = (content: string, type: string) => {
        const checks = { ...qualityChecks };
        
        // Simulate quality scoring
        checks.consistency.score = content.includes('consistent') ? 0.9 : 0.5;
        checks.pacing.score = content.length > 3000 ? 0.8 : 0.6;
        checks.characterization.score = content.includes('character') ? 0.85 : 0.4;
        checks.prose.score = content.includes('descriptive') ? 0.9 : 0.7;
        
        // Flag issues
        if (checks.consistency.score < 0.7) {
          checks.consistency.issues.push('Inconsistent character behavior detected');
        }
        if (checks.pacing.score < 0.7) {
          checks.pacing.issues.push('Pacing too slow in middle section');
        }
        
        return checks;
      };

      const contentWithQuality: any[] = [];

      mockContentGenerator.generateContent = jest.fn().mockImplementation(async (request) => {
        const content = `Generated ${request.type} content with consistent character development and descriptive prose.`;
        const quality = assessQuality(content, request.type as string);
        
        const result = {
          type: request.type,
          content,
          quality,
          wordCount: content.split(' ').length
        };
        
        contentWithQuality.push(result);
        return result;
      });

      // Generate content and assess quality
      const contentTypes = ['chapter', 'scene', 'dialogue'];
      for (const type of contentTypes) {
        await mockContentGenerator.generateContent({
          type: type as any,
          prompt: `Generate ${type}`,
          context: {},
          projectId: mockContext.projectId
        });
      }

      // Verify quality checks were performed
      expect(contentWithQuality).toHaveLength(3);
      
      for (const content of contentWithQuality) {
        expect(content.quality.consistency.score).toBeGreaterThan(0.8);
        expect(content.quality.prose.score).toBeGreaterThan(0.8);
        expect(content.quality.consistency.issues).toHaveLength(0);
      }
    });

    it('should retry content generation on quality failure', async () => {
      let attemptCount = 0;
      const minQualityScore = 0.7;

      mockContentGenerator.generateContent = jest.fn().mockImplementation(async () => {
        attemptCount++;
        
        // First attempt fails quality check
        if (attemptCount === 1) {
          return {
            content: 'Poor quality content',
            qualityScore: 0.4,
            issues: ['Inconsistent tone', 'Weak character voice']
          };
        }
        
        // Second attempt passes
        return {
          content: 'High quality content with consistent tone and strong character voice',
          qualityScore: 0.85,
          issues: []
        };
      });

      // Implement retry logic
      let result;
      let retries = 0;
      const maxRetries = 3;
      
      while (retries < maxRetries) {
        result = await mockContentGenerator.generateContent({
          type: 'chapter',
          prompt: 'Generate chapter',
          context: {},
          projectId: mockContext.projectId
        });
        
        if (result.qualityScore >= minQualityScore) {
          break;
        }
        
        retries++;
      }

      expect(attemptCount).toBe(2);
      expect(result.qualityScore).toBeGreaterThan(minQualityScore);
      expect(result.issues).toHaveLength(0);
    });
  });

  describe('Content Pipeline Performance', () => {
    it('should process large batches efficiently', async () => {
      const batchSize = 50;
      const processedItems: number[] = [];

      mockContentGenerator.generateContent = jest.fn().mockImplementation(async (request) => {
        const itemNumber = request.context?.itemNumber as number;
        processedItems.push(itemNumber);
        
        // Simulate variable processing time
        await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
        
        return {
          itemNumber,
          content: `Item ${itemNumber} content`,
          wordCount: 100
        };
      });

      const startTime = Date.now();
      
      // Process in batches with concurrency control
      const batchPromises = [];
      const concurrencyLimit = 10;
      
      for (let i = 0; i < batchSize; i += concurrencyLimit) {
        const batch = Array.from({ length: Math.min(concurrencyLimit, batchSize - i) }, (_, j) => i + j);
        
        const batchPromise = Promise.all(
          batch.map(itemNumber =>
            mockContentGenerator.generateContent({
              type: 'scene',
              prompt: 'Generate content',
              context: { itemNumber },
              projectId: mockContext.projectId
            })
          )
        );
        
        batchPromises.push(batchPromise);
        
        // Wait for batch to complete before starting next
        await batchPromise;
      }

      const totalTime = Date.now() - startTime;
      
      // Verify all items were processed
      expect(processedItems).toHaveLength(batchSize);
      expect(new Set(processedItems).size).toBe(batchSize); // All unique
      
      // Should complete in reasonable time with parallelization
      const expectedMaxTime = (batchSize / concurrencyLimit) * 50 * 1.5; // Allow 50% overhead
      expect(totalTime).toBeLessThan(expectedMaxTime);
    });

    it('should handle memory-intensive content generation', async () => {
      const memorySnapshots: number[] = [];
      
      // Mock memory-intensive operation
      mockContentGenerator.generateContent = jest.fn().mockImplementation(async (request) => {
        // Simulate large content generation
        const largeContent = new Array(10000).fill('word').join(' ');
        
        // Track memory usage (simulated)
        const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
        memorySnapshots.push(memoryUsage);
        
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 10));
        
        return {
          content: largeContent.substring(0, 1000), // Return truncated version
          wordCount: 10000,
          memoryUsageMB: memoryUsage
        };
      });

      // Generate multiple large chapters
      const chapterCount = 20;
      for (let i = 0; i < chapterCount; i++) {
        await mockContentGenerator.generateContent({
          type: 'chapter',
          prompt: `Generate large chapter ${i}`,
          context: { chapterNumber: i },
          projectId: mockContext.projectId
        });
        
        // Simulate garbage collection between chapters
        if (i % 5 === 0 && global.gc) {
          global.gc();
        }
      }

      // Verify memory usage remains stable
      if (memorySnapshots.length > 10) {
        const firstHalf = memorySnapshots.slice(0, 10);
        const secondHalf = memorySnapshots.slice(-10);
        
        const avgFirstHalf = firstHalf.reduce((a, b) => a + b) / firstHalf.length;
        const avgSecondHalf = secondHalf.reduce((a, b) => a + b) / secondHalf.length;
        
        // Memory growth should be controlled (less than 50% increase)
        const memoryGrowth = (avgSecondHalf - avgFirstHalf) / avgFirstHalf;
        expect(memoryGrowth).toBeLessThan(0.5);
      }
    });
  });
});