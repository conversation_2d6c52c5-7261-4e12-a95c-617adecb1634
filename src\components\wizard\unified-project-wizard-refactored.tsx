'use client';

import { useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  ChevronLeft, 
  ChevronRight, 
  ArrowRight,
  ArrowLeft,
  HelpCircle,
  Lightbulb
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { wizardSteps, guidedTourContent } from './wizard-config';
import { useWizardLogic } from './use-wizard-logic';
import { 
  StepBasics,
  StepGenreStyle,
  StepStructurePacing,
  StepCharactersWorld
} from './steps';
import { StepThemesContent } from './steps/step-themes-content';
import { StepTechnicalSpecs } from './steps/step-technical-specs';
import { StepPayment } from './steps/step-payment';
import { ANIMATION_DURATION } from '@/lib/constants'

export type WizardMode = 'live' | 'demo';
export type WizardDisplay = 'page' | 'modal' | 'guided';

interface UnifiedProjectWizardProps {
  mode?: WizardMode;
  display?: WizardDisplay;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  guided?: boolean;
  onComplete?: (projectId: string) => void;
  demoTheme?: 'fantasy' | 'mystery' | 'romance' | 'scifi';
}

export function UnifiedProjectWizard({ 
  mode = 'live',
  display = 'page',
  open = true,
  onOpenChange,
  guided = false,
  onComplete,
  demoTheme = 'fantasy'
}: UnifiedProjectWizardProps) {
  const {
    currentStep,
    setCurrentStep,
    formData,
    updateFormData,
    isGenerating,
    canProceed,
    nextStep,
    prevStep,
    handleGenerate,
    universes,
    series,
    isLoadingUniverses,
    isLoadingSeries
  } = useWizardLogic({ mode, demoTheme, onComplete });

  const progress = ((currentStep + 1) / wizardSteps.length) * 100;

  const handleClose = useCallback(() => {
    if (onOpenChange) {
      const confirmed = window.confirm("Are you sure you want to cancel? Your progress will be lost.");
      if (confirmed) {
        onOpenChange(false);
      }
    }
  }, [onOpenChange]);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return <StepBasics formData={formData} updateFormData={updateFormData} mode={mode} />;
      case 1:
        return <StepGenreStyle formData={formData} updateFormData={updateFormData} mode={mode} />;
      case 2:
        return <StepStructurePacing formData={formData} updateFormData={updateFormData} mode={mode} />;
      case 3:
        return (
          <StepCharactersWorld 
            formData={formData} 
            updateFormData={updateFormData} 
            mode={mode}
            universes={universes}
            series={series}
            isLoadingUniverses={isLoadingUniverses}
            isLoadingSeries={isLoadingSeries}
          />
        );
      case 4:
        return <StepThemesContent formData={formData} updateFormData={updateFormData} mode={mode} />;
      case 5:
        return <StepTechnicalSpecs formData={formData} updateFormData={updateFormData} mode={mode} />;
      case 6:
        return (
          <StepPayment 
            formData={formData} 
            mode={mode} 
            isGenerating={isGenerating}
            onComplete={handleGenerate}
          />
        );
      default:
        return null;
    }
  };

  const renderGuidedWrapper = (children: React.ReactNode) => {
    if (!guided) return children;

    const tourContent = guidedTourContent[currentStep];
    
    return (
      <div className="space-y-6">
        <Alert className="border-primary/20 bg-primary/5">
          <Lightbulb className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-3">
              <p className="font-medium">{tourContent.title}</p>
              <p className="text-sm">{tourContent.description}</p>
              <ul className="space-y-1 mt-2">
                {tourContent.tips.map((tip, index) => (
                  <li key={index} className="text-sm flex items-start gap-2">
                    <span className="text-primary mt-1">•</span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
        {children}
      </div>
    );
  };

  const renderWizardContent = () => {
    const content = (
      <div className="space-y-6">
        {/* Progress Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">
              {mode === 'demo' ? 'Create Demo Project' : 'Create New Project'}
            </h2>
            {display === 'modal' && (
              <Button 
                variant="ghost" 
                size="icon"
                onClick={handleClose}
                className="h-8 w-8"
              >
                ×
              </Button>
            )}
          </div>
          
          <Progress value={progress} className="h-2" />
          
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Step {currentStep + 1} of {wizardSteps.length}</span>
            <span>{wizardSteps[currentStep].title}</span>
          </div>
        </div>

        {/* Step Navigation Pills */}
        <div className="flex gap-2 flex-wrap">
          {wizardSteps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            const isClickable = mode === 'demo' || (index < currentStep && index !== 6);
            
            return (
              <button
                key={step.id}
                onClick={() => isClickable && setCurrentStep(index)}
                disabled={!isClickable}
                className={`
                  flex items-center gap-2 px-3 py-1.5 rounded-full text-sm transition-all
                  ${isActive ? 'bg-primary text-primary-foreground' : ''}
                  ${isCompleted && !isActive ? 'bg-primary/20 text-primary' : ''}
                  ${!isActive && !isCompleted ? 'bg-muted text-muted-foreground' : ''}
                  ${isClickable ? 'cursor-pointer hover:bg-primary/30' : 'cursor-not-allowed opacity-60'}
                `}
              >
                <Icon className="h-3.5 w-3.5" />
                <span className="hidden md:inline">{step.title}</span>
              </button>
            );
          })}
        </div>

        {/* Step Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: ANIMATION_DURATION.NORMAL }}
            className="min-h-[400px]"
          >
            {renderGuidedWrapper(renderCurrentStep())}
          </motion.div>
        </AnimatePresence>

        {/* Navigation Footer */}
        <div className="flex items-center justify-between pt-6 border-t">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                toast({
                  title: "Need Help?",
                  description: "Check out our guide or contact support for assistance.",
                });
              }}
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
          </div>

          {currentStep < 6 ? (
            <Button
              onClick={nextStep}
              disabled={!canProceed()}
              className="gap-2"
            >
              Next
              <ArrowRight className="h-4 w-4" />
            </Button>
          ) : null}
        </div>

        {mode === 'demo' && (
          <Alert>
            <AlertDescription className="text-sm">
              <strong>Demo Mode:</strong> This is a preview of the project creation process. 
              Some features are limited in demo mode.
            </AlertDescription>
          </Alert>
        )}
      </div>
    );

    return content;
  };

  // Render based on display mode
  if (display === 'modal') {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {renderWizardContent()}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <div className="container-wide max-w-5xl mx-auto py-6 sm:py-8 lg:py-10">
      {renderWizardContent()}
    </div>
  );
}