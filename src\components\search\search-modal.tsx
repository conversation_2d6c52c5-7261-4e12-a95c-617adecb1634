'use client'

import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ContentSearchInterface } from './content-search-interface'
import { useHotkeys } from '@/hooks/use-hotkeys'
import { useRouter } from 'next/navigation'
import type { SearchResult } from './content-search-interface'

interface SearchModalProps {
  projectId?: string
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function SearchModal({ 
  projectId, 
  open: controlledOpen, 
  onOpenChange 
}: SearchModalProps) {
  const router = useRouter()
  const [internalOpen, setInternalOpen] = useState(false)
  
  // Use controlled or uncontrolled state
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen

  // Register global keyboard shortcut
  useHotkeys('meta+k', (e) => {
    e.preventDefault()
    setOpen(true)
  })

  useHotkeys('ctrl+k', (e) => {
    e.preventDefault()
    setOpen(true)
  })

  // Close on Escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        setOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [open, setOpen])

  const handleResultSelect = (result: SearchResult) => {
    // Navigate based on result type
    switch (result.type) {
      case 'chapter':
        router.push(`/projects/${projectId}/chapters/${result.id}`)
        break
      case 'character':
        router.push(`/projects/${projectId}/characters/${result.id}`)
        break
      case 'location':
        router.push(`/projects/${projectId}/locations#${result.id}`)
        break
      case 'story_bible':
        router.push(`/projects/${projectId}/story-bible#${result.id}`)
        break
      case 'note':
        router.push(`/projects/${projectId}/notes/${result.id}`)
        break
    }
    
    // Close modal after navigation
    setOpen(false)
  }

  if (!projectId) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden p-0">
        <DialogHeader className="px-6 pt-6 pb-0">
          <DialogTitle className="sr-only">Search Content</DialogTitle>
        </DialogHeader>
        <div className="px-6 pb-6">
          <ContentSearchInterface
            projectId={projectId}
            onResultSelect={handleResultSelect}
            className="border-0 shadow-none"
          />
        </div>
        <div className="border-t px-6 py-3 bg-muted/30 text-xs text-muted-foreground">
          <span className="inline-flex items-center gap-1">
            Press <kbd className="px-1.5 py-0.5 bg-background border rounded text-xs">⌘K</kbd> or 
            <kbd className="px-1.5 py-0.5 bg-background border rounded text-xs ml-1">Ctrl+K</kbd> to open search
          </span>
        </div>
      </DialogContent>
    </Dialog>
  )
}