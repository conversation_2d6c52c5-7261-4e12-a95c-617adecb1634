# Email Service Consolidation Plan

## Current State Analysis

We have 4 different email service implementations:

1. **`/lib/email.ts`** (267 lines)
   - Simple wrapper using maileroo
   - Exports: `sendEmail()`, `sendCollaborationInvite()`, `sendWelcomeEmail()`, etc.
   - Uses direct maileroo functions

2. **`/lib/services/email-service.ts`** (388 lines)
   - Wrapper around maileroo-email-service
   - Exports: `EmailService` class, `emailService` singleton, `EmailTemplates` const
   - Duplicates template definitions

3. **`/lib/services/email/maileroo-email-service.ts`** (662 lines)
   - Full implementation with queue support
   - Exports: `MailerooEmailService` class, `mailerooEmailService` singleton
   - Has email queue processing, templates, and preferences

4. **`/lib/email/service.ts`** (239+ lines)
   - Provider pattern implementation
   - Exports: `EmailService` class with different structure
   - Uses MailerooProvider

## Import Analysis

Current usage:
- `/api/email/send/route.ts` → uses #3 (maileroo-email-service)
- `/api/user/export-data/route.ts` → uses #3
- `/api/email/queue/process/route.ts` → uses #4
- `/api/email/preferences/route.ts` → uses #4
- `/api/cron/process-email-queue/route.ts` → uses services/index export

## Consolidation Strategy

### Keep Only One Implementation

**Decision**: Keep `/lib/services/email/maileroo-email-service.ts` as the main implementation because:
- It has the most complete feature set (queue, templates, preferences)
- It's already being used by critical routes
- It has proper TypeScript types and validation

### Migration Steps

1. **Update imports in routes using #4**:
   - `/api/email/queue/process/route.ts`
   - `/api/email/preferences/route.ts`

2. **Remove duplicate files**:
   - `/lib/email.ts`
   - `/lib/services/email-service.ts` 
   - `/lib/email/service.ts`

3. **Update exports in `/lib/services/index.ts`**:
   - Ensure it exports from the correct location

4. **Clean up unused email-related files**:
   - Check for any orphaned template or type files

### File Structure After Consolidation

```
src/lib/services/email/
├── maileroo-email-service.ts  (main implementation)
├── templates/                  (email templates)
│   ├── welcome.ts
│   ├── achievement.ts
│   └── ...
└── types.ts                   (shared types)

src/lib/email/providers/
├── maileroo.ts               (provider implementation)
└── base-provider.ts          (interface)
```

## Benefits

1. **Single source of truth** for email functionality
2. **Reduced confusion** about which service to use
3. **Easier maintenance** - only one place to update
4. **Smaller bundle size** - removes ~900 lines of duplicate code
5. **Consistent behavior** across the application

## Testing Plan

1. Test all email-sending routes after migration
2. Verify email queue processing still works
3. Check email preferences are respected
4. Ensure all templates render correctly