'use client'

import { useState, useMemo, useCallback } from 'react'
import { ChevronLeft, ChevronRight, Calendar, Plus, Filter, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths, startOfWeek, endOfWeek, parseISO } from 'date-fns'

export interface TimelineEvent {
  id: string
  type: 'plot' | 'character' | 'world' | 'reference' | 'deadline' | 'milestone'
  title: string
  description: string
  date: string // ISO date string
  chapter?: number
  scene?: number
  characters: string[]
  location?: string
  importance: 'low' | 'medium' | 'high' | 'critical'
  verified: boolean
  color?: string
}

interface TimelineCalendarViewProps {
  events: TimelineEvent[]
  onEventSelect?: (event: TimelineEvent) => void
  onEventCreate?: (date: Date) => void
  onEventUpdate?: (event: TimelineEvent) => void
  onEventDelete?: (eventId: string) => void
  readOnly?: boolean
}

const EVENT_TYPE_COLORS = {
  plot: 'bg-blue-500 hover:bg-blue-600',
  character: 'bg-green-500 hover:bg-green-600',
  world: 'bg-purple-500 hover:bg-purple-600',
  reference: 'bg-orange-500 hover:bg-orange-600',
  deadline: 'bg-red-500 hover:bg-red-600',
  milestone: 'bg-yellow-500 hover:bg-yellow-600'
}

const IMPORTANCE_OPACITY = {
  low: 'opacity-50',
  medium: 'opacity-75',
  high: 'opacity-90',
  critical: 'opacity-100'
}

export function TimelineCalendarView({ 
  events, 
  onEventSelect, 
  onEventCreate, 
  onEventUpdate, 
  onEventDelete,
  readOnly = false 
}: TimelineCalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null)
  const [viewMode, setViewMode] = useState<'month' | 'week'>('month')
  const [filterType, setFilterType] = useState<'all' | TimelineEvent['type']>('all')
  const [filterImportance, setFilterImportance] = useState<'all' | TimelineEvent['importance']>('all')

  // Filter events
  const filteredEvents = useMemo(() => {
    return events.filter(event => {
      if (filterType !== 'all' && event.type !== filterType) return false
      if (filterImportance !== 'all' && event.importance !== filterImportance) return false
      return true
    })
  }, [events, filterType, filterImportance])

  // Get events for a specific date
  const getEventsForDate = useCallback((date: Date) => {
    return filteredEvents.filter(event => {
      try {
        const eventDate = parseISO(event.date)
        return isSameDay(eventDate, date)
      } catch {
        return false
      }
    })
  }, [filteredEvents])

  // Generate calendar days
  const calendarDays = useMemo(() => {
    const start = startOfMonth(currentDate)
    const end = endOfMonth(currentDate)
    const startWeek = startOfWeek(start)
    const endWeek = endOfWeek(end)
    
    return eachDayOfInterval({ start: startWeek, end: endWeek })
  }, [currentDate])

  const handlePreviousMonth = () => {
    setCurrentDate(prev => subMonths(prev, 1))
  }

  const handleNextMonth = () => {
    setCurrentDate(prev => addMonths(prev, 1))
  }

  const handleDateClick = (date: Date) => {
    const dayEvents = getEventsForDate(date)
    if (dayEvents.length === 1) {
      setSelectedEvent(dayEvents[0])
      onEventSelect?.(dayEvents[0])
    } else if (dayEvents.length === 0 && onEventCreate && !readOnly) {
      onEventCreate(date)
    }
  }

  const handleEventClick = (event: TimelineEvent, e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedEvent(event)
    onEventSelect?.(event)
  }

  const renderEventDot = (event: TimelineEvent) => {
    const colorClass = EVENT_TYPE_COLORS[event.type] || 'bg-gray-500 hover:bg-gray-600'
    const opacityClass = IMPORTANCE_OPACITY[event.importance]
    
    return (
      <div
        key={event.id}
        className={cn(
          'w-2 h-2 rounded-full cursor-pointer transition-all hover:scale-110',
          colorClass,
          opacityClass
        )}
        title={event.title}
        onClick={(e) => handleEventClick(event, e)}
      />
    )
  }

  const renderDayCell = (date: Date) => {
    const dayEvents = getEventsForDate(date)
    const isCurrentMonth = isSameMonth(date, currentDate)
    const isToday = isSameDay(date, new Date())
    
    return (
      <div
        key={date.toISOString()}
        className={cn(
          'min-h-[120px] p-2 border border-border cursor-pointer hover:bg-muted/50 transition-colors',
          !isCurrentMonth && 'text-muted-foreground bg-muted/20',
          isToday && 'bg-accent/20 border-accent'
        )}
        onClick={() => handleDateClick(date)}
      >
        <div className="flex items-center justify-between mb-2">
          <span className={cn(
            'text-sm font-medium',
            isToday && 'text-accent-foreground'
          )}>
            {format(date, 'd')}
          </span>
          {dayEvents.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {dayEvents.length}
            </Badge>
          )}
        </div>
        
        <div className="space-y-1">
          {dayEvents.slice(0, 3).map(event => (
            <div
              key={event.id}
              className={cn(
                'text-xs p-1 rounded truncate cursor-pointer transition-colors',
                EVENT_TYPE_COLORS[event.type],
                'text-white',
                IMPORTANCE_OPACITY[event.importance]
              )}
              onClick={(e) => handleEventClick(event, e)}
              title={event.title}
            >
              {event.title}
            </div>
          ))}
          {dayEvents.length > 3 && (
            <div className="text-xs text-muted-foreground">
              +{dayEvents.length - 3} more
            </div>
          )}
        </div>
        
        {/* Event dots for additional visual indication */}
        <div className="flex flex-wrap gap-1 mt-2">
          {dayEvents.slice(0, 6).map(renderEventDot)}
          {dayEvents.length > 6 && (
            <div className="text-xs text-muted-foreground">...</div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Timeline Calendar</h2>
          <p className="text-muted-foreground">
            View your story events in calendar format
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Filters */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Event Type</label>
                  <Select value={filterType} onValueChange={(value) => setFilterType(value as any)}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="plot">Plot Events</SelectItem>
                      <SelectItem value="character">Character Events</SelectItem>
                      <SelectItem value="world">World Events</SelectItem>
                      <SelectItem value="reference">Reference Events</SelectItem>
                      <SelectItem value="deadline">Deadlines</SelectItem>
                      <SelectItem value="milestone">Milestones</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">Importance</label>
                  <Select value={filterImportance} onValueChange={(value) => setFilterImportance(value as any)}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Levels</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          {/* View Mode Toggle */}
          <Select value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="week">Week</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Calendar Navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handlePreviousMonth}>
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>
          <Button variant="outline" size="sm" onClick={handleNextMonth}>
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
        
        <h3 className="text-xl font-semibold">
          {format(currentDate, 'MMMM yyyy')}
        </h3>
        
        <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
          Today
        </Button>
      </div>

      {/* Calendar Grid */}
      <Card>
        <CardContent className="p-0">
          {/* Weekday Headers */}
          <div className="grid grid-cols-7 border-b">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-4 text-center font-medium border-r last:border-r-0">
                {day}
              </div>
            ))}
          </div>
          
          {/* Calendar Days */}
          <div className="grid grid-cols-7">
            {calendarDays.map(renderDayCell)}
          </div>
        </CardContent>
      </Card>

      {/* Event Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Event Types</CardTitle>
          <CardDescription>Color coding for different event types</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            {Object.entries(EVENT_TYPE_COLORS).map(([type, colorClass]) => (
              <div key={type} className="flex items-center gap-2">
                <div className={cn('w-4 h-4 rounded', colorClass)} />
                <span className="text-sm capitalize">{type}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Event Details Dialog */}
      {selectedEvent && (
        <Dialog open={!!selectedEvent} onOpenChange={() => setSelectedEvent(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <div className={cn('w-4 h-4 rounded', EVENT_TYPE_COLORS[selectedEvent.type])} />
                {selectedEvent.title}
              </DialogTitle>
              <DialogDescription>
                {format(parseISO(selectedEvent.date), 'MMMM d, yyyy')}
                {selectedEvent.chapter && ` • Chapter ${selectedEvent.chapter}`}
                {selectedEvent.scene && ` • Scene ${selectedEvent.scene}`}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="capitalize">
                  {selectedEvent.type}
                </Badge>
                <Badge variant={
                  selectedEvent.importance === 'critical' ? 'destructive' :
                  selectedEvent.importance === 'high' ? 'default' :
                  selectedEvent.importance === 'medium' ? 'secondary' : 'outline'
                }>
                  {selectedEvent.importance}
                </Badge>
                {selectedEvent.verified && (
                  <Badge variant="secondary">Verified</Badge>
                )}
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Description</h4>
                <p className="text-sm text-muted-foreground">
                  {selectedEvent.description}
                </p>
              </div>
              
              {selectedEvent.characters.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2">Characters</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedEvent.characters.map(character => (
                      <Badge key={character} variant="outline" className="text-xs">
                        {character}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {selectedEvent.location && (
                <div>
                  <h4 className="font-semibold mb-2">Location</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.location}
                  </p>
                </div>
              )}
              
              {!readOnly && (
                <div className="flex items-center gap-2 pt-4 border-t">
                  <Button
                    size="sm"
                    onClick={() => {
                      onEventUpdate?.(selectedEvent)
                      setSelectedEvent(null)
                    }}
                  >
                    Edit Event
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => {
                      onEventDelete?.(selectedEvent.id)
                      setSelectedEvent(null)
                    }}
                  >
                    Delete Event
                  </Button>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}