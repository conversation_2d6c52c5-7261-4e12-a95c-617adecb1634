import { NextRequest } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { GET, POST } from '@/app/api/projects/[id]/locations/route'
import { UnifiedResponse } from '@/lib/api/unified-response'

// Mock dependencies
jest.mock('@/lib/supabase')
jest.mock('@/lib/services/logger')

const mockSupabase = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn()
}

const mockCreateServerClient = createServerClient as jest.MockedFunction<typeof createServerClient>
mockCreateServerClient.mockResolvedValue(mockSupabase as any)

describe('Location API Integration Tests', () => {
  const mockUserId = 'test-user-123'
  const mockProjectId = 'test-project-456'
  const mockUser = { user: { id: mockUserId } }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/projects/[id]/locations', () => {
    it('should return locations for authenticated user', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock locations data
      const mockLocations = [
        {
          id: 'loc-1',
          name: 'The Shire',
          description: 'A peaceful land',
          location_type: 'region',
          parent_location: null,
          series: null,
          universe: null
        },
        {
          id: 'loc-2', 
          name: 'Bag End',
          description: 'A hobbit hole',
          location_type: 'building',
          parent_location: { id: 'loc-1', name: 'The Shire' },
          series: null,
          universe: null
        }
      ]

      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ data: mockLocations, error: null })
      }

      mockSupabase.from.mockReturnValue(mockQuery)

      // Create request
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`)
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await GET(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.locations).toHaveLength(2)
      expect(data.data.locations[0].name).toBe('The Shire')
      expect(mockSupabase.from).toHaveBeenCalledWith('locations')
      expect(mockQuery.eq).toHaveBeenCalledWith('project_id', mockProjectId)
    })

    it('should return 401 for unauthenticated request', async () => {
      // Mock auth failure
      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: new Error('Not authenticated') 
      })

      // Create request
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`)
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await GET(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('AUTHENTICATION_ERROR')
    })

    it('should handle database errors gracefully', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock database error
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ 
          data: null, 
          error: new Error('Database connection failed') 
        })
      }

      mockSupabase.from.mockReturnValue(mockQuery)

      // Create request
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`)
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await GET(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('DATABASE_ERROR')
    })
  })

  describe('POST /api/projects/[id]/locations', () => {
    const validLocationData = {
      name: 'Rivendell',
      description: 'Elven realm',
      locationType: 'city',
      features: ['Waterfall', 'Council Chamber'],
      significance: 'Last Homely House',
      isShareable: true
    }

    it('should create a new location successfully', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock project ownership check
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { id: mockProjectId }, 
          error: null 
        })
      }

      // Mock location creation
      const locationQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: 'new-loc-123',
            project_id: mockProjectId,
            name: validLocationData.name,
            description: validLocationData.description,
            location_type: validLocationData.locationType,
            features: validLocationData.features,
            significance: validLocationData.significance,
            is_shareable: validLocationData.isShareable,
            parent_location: null
          },
          error: null
        })
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'projects') return projectQuery
        if (table === 'locations') return locationQuery
        return null
      })

      // Create request
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`, {
        method: 'POST',
        body: JSON.stringify(validLocationData)
      })
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await POST(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.location.name).toBe(validLocationData.name)
      expect(locationQuery.insert).toHaveBeenCalledWith(expect.objectContaining({
        project_id: mockProjectId,
        name: validLocationData.name,
        location_type: validLocationData.locationType,
        is_shareable: validLocationData.isShareable
      }))
    })

    it('should validate required fields', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock project ownership check
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { id: mockProjectId }, 
          error: null 
        })
      }

      mockSupabase.from.mockReturnValue(projectQuery)

      // Create request with invalid data (missing name)
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`, {
        method: 'POST',
        body: JSON.stringify({
          description: 'Missing name field'
        })
      })
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await POST(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('VALIDATION_ERROR')
    })

    it('should handle parent location relationships', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock project ownership check
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { id: mockProjectId }, 
          error: null 
        })
      }

      // Mock location creation with parent
      const locationQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: 'new-loc-456',
            project_id: mockProjectId,
            name: 'Council Chamber',
            parent_location_id: 'loc-parent-123',
            location_type: 'room',
            parent_location: {
              id: 'loc-parent-123',
              name: 'Rivendell'
            }
          },
          error: null
        })
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'projects') return projectQuery
        if (table === 'locations') return locationQuery
        return null
      })

      // Create request with parent location
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`, {
        method: 'POST',
        body: JSON.stringify({
          name: 'Council Chamber',
          parentLocationId: 'loc-parent-123',
          locationType: 'room'
        })
      })
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await POST(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.location.parent_location_id).toBe('loc-parent-123')
      expect(data.data.location.parent_location.name).toBe('Rivendell')
    })

    it('should reject creation for unauthorized project', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock project ownership check - user doesn't own project
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: null, 
          error: new Error('Not found') 
        })
      }

      mockSupabase.from.mockReturnValue(projectQuery)

      // Create request
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`, {
        method: 'POST',
        body: JSON.stringify(validLocationData)
      })
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await POST(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('NOT_FOUND')
    })
  })

  describe('Location Type Validation', () => {
    it('should accept all valid location types', async () => {
      const validTypes = ['world', 'continent', 'country', 'region', 'city', 'building', 'room', 'other']
      
      for (const locationType of validTypes) {
        // Mock auth and project check
        mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })
        
        const projectQuery = {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({ 
            data: { id: mockProjectId }, 
            error: null 
          })
        }

        const locationQuery = {
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: {
              id: `loc-${locationType}`,
              name: `Test ${locationType}`,
              location_type: locationType
            },
            error: null
          })
        }

        mockSupabase.from.mockImplementation((table) => {
          if (table === 'projects') return projectQuery
          if (table === 'locations') return locationQuery
          return null
        })

        const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`, {
          method: 'POST',
          body: JSON.stringify({
            name: `Test ${locationType}`,
            locationType
          })
        })
        const params = { params: Promise.resolve({ id: mockProjectId }) }

        const response = await POST(request, params)
        expect(response.status).toBe(201)
      }
    })
  })

  describe('Series and Universe Integration', () => {
    it('should handle series and universe associations', async () => {
      // Mock auth
      mockSupabase.auth.getUser.mockResolvedValue({ data: mockUser, error: null })

      // Mock queries
      const projectQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { id: mockProjectId }, 
          error: null 
        })
      }

      const locationQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: 'new-loc-789',
            name: 'Middle Earth',
            location_type: 'world',
            series_id: 'series-123',
            universe_id: 'universe-456'
          },
          error: null
        })
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'projects') return projectQuery
        if (table === 'locations') return locationQuery
        return null
      })

      // Create request
      const request = new NextRequest(`http://localhost/api/projects/${mockProjectId}/locations`, {
        method: 'POST',
        body: JSON.stringify({
          name: 'Middle Earth',
          locationType: 'world',
          seriesId: 'series-123',
          universeId: 'universe-456'
        })
      })
      const params = { params: Promise.resolve({ id: mockProjectId }) }

      // Execute
      const response = await POST(request, params)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(201)
      expect(data.data.location.series_id).toBe('series-123')
      expect(data.data.location.universe_id).toBe('universe-456')
    })
  })
})