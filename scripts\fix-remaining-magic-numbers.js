const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Mapping of remaining magic numbers to their constant replacements
const replacements = [
  // Time constants (milliseconds)
  { pattern: /(\b1000\b)(?!.*\/\/.*KB|.*bytes)/g, replacement: 'TIME_MS.SECOND', import: "import { TIME_MS } from '@/lib/constants'" },
  { pattern: /\b3000\b(?!.*tokens)/g, replacement: 'TIME_MS.TYPING_TIMEOUT', import: "import { TIME_MS } from '@/lib/constants'" },
  { pattern: /\b5000\b(?!.*characters)/g, replacement: 'TIME_MS.TOAST_DURATION', import: "import { TIME_MS } from '@/lib/constants'" },
  { pattern: /1000\s*\*\s*retryCountRef/g, replacement: 'TIME_MS.RETRY_DELAY_BASE * retryCountRef', import: "import { TIME_MS } from '@/lib/constants'" },
  
  // Time constants (seconds)
  { pattern: /\b3600\b/g, replacement: 'TIME_SECONDS.HOUR', import: "import { TIME_SECONDS } from '@/lib/constants'" },
  { pattern: /\b86400000\b/g, replacement: '(TIME_SECONDS.DAY * TIME_MS.SECOND)', import: "import { TIME_SECONDS, TIME_MS } from '@/lib/constants'" },
  
  // Size limits
  { pattern: /\b50000\b(?!.*words)/g, replacement: 'SIZE_LIMITS.MAX_DOCUMENT_CHARS', import: "import { SIZE_LIMITS } from '@/lib/constants'" },
  { pattern: /\b100000\b/g, replacement: 'SIZE_LIMITS.LARGE_DOCUMENT_THRESHOLD', import: "import { SIZE_LIMITS } from '@/lib/constants'" },
  { pattern: /\b5000\b(?!.*ms)/g, replacement: 'SIZE_LIMITS.SIZE_CHANGE_THRESHOLD', import: "import { SIZE_LIMITS } from '@/lib/constants'" },
  { pattern: /\b8000\b/g, replacement: 'SIZE_LIMITS.EMBEDDING_TEXT_LIMIT', import: "import { SIZE_LIMITS } from '@/lib/constants'" },
  { pattern: /\*\s*2\s*\/\s*1024/g, replacement: '* SIZE_LIMITS.CHAR_TO_KB_RATIO / SIZE_LIMITS.KB_PER_MB', import: "import { SIZE_LIMITS } from '@/lib/constants'" },
  
  // Token limits
  { pattern: /\b32000\b/g, replacement: 'TOKEN_LIMITS.GPT4_MAX', import: "import { TOKEN_LIMITS } from '@/lib/constants'" },
  { pattern: /estimatedTokens:\s*1000\b/g, replacement: 'estimatedTokens: TOKEN_LIMITS.COMPLETION_DEFAULT', import: "import { TOKEN_LIMITS } from '@/lib/constants'" },
  { pattern: /estimatedTokens:\s*1500\b/g, replacement: 'estimatedTokens: DEMO_VALUES.MEDIUM_REQUEST', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /estimatedTokens:\s*2000\b/g, replacement: 'estimatedTokens: DEMO_VALUES.LARGE_REQUEST', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /estimatedTokens:\s*2500\b/g, replacement: 'estimatedTokens: DEMO_VALUES.XLARGE_REQUEST', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /estimatedTokens:\s*3000\b/g, replacement: 'estimatedTokens: DEMO_VALUES.HUGE_REQUEST', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  
  // Demo values
  { pattern: /todayWordCount:\s*1250/g, replacement: 'todayWordCount: DEMO_VALUES.DAILY_WORD_COUNT', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /dailyGoal:\s*1000/g, replacement: 'dailyGoal: DEMO_VALUES.DAILY_GOAL', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /wordCount:\s*1200/g, replacement: 'wordCount: DEMO_VALUES.WEEK_MON', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /wordCount:\s*1500/g, replacement: 'wordCount: DEMO_VALUES.WEEK_WED', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /wordCount:\s*1100/g, replacement: 'wordCount: DEMO_VALUES.WEEK_THU', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /wordCount:\s*1300/g, replacement: 'wordCount: DEMO_VALUES.WEEK_FRI', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  { pattern: /wordCount:\s*1250/g, replacement: 'wordCount: DEMO_VALUES.WEEK_SUN', import: "import { DEMO_VALUES } from '@/lib/constants'" },
  
  // Toast remove delay
  { pattern: /const\s+TOAST_REMOVE_DELAY\s*=\s*5000/g, replacement: 'const TOAST_REMOVE_DELAY = TIME_MS.TOAST_DURATION', import: "import { TIME_MS } from '@/lib/constants'" },
];

function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf-8');
  const originalContent = content;
  let importsToAdd = new Set();
  let changesMade = false;
  
  // Apply replacements
  replacements.forEach(({ pattern, replacement, import: importStatement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      if (importStatement) {
        importsToAdd.add(importStatement);
      }
      changesMade = true;
    }
  });
  
  if (!changesMade) {
    return false;
  }
  
  // Add imports if needed
  if (importsToAdd.size > 0) {
    const importStatements = Array.from(importsToAdd).join('\n');
    
    // Find the last import statement
    const importRegex = /^import[\s\S]*?from[\s\S]*?$/gm;
    const imports = content.match(importRegex);
    
    if (imports && imports.length > 0) {
      const lastImport = imports[imports.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport);
      const insertPosition = lastImportIndex + lastImport.length;
      
      // Check if imports already exist
      const existingImports = imports.join('\n');
      const newImports = [];
      
      importsToAdd.forEach(imp => {
        const moduleMatch = imp.match(/from\s+['"]([^'"]+)['"]/);
        if (moduleMatch && !existingImports.includes(moduleMatch[1])) {
          newImports.push(imp);
        }
      });
      
      if (newImports.length > 0) {
        content = content.slice(0, insertPosition) + '\n' + newImports.join('\n') + content.slice(insertPosition);
      }
    } else {
      // No imports found, add at the beginning
      content = importStatements + '\n\n' + content;
    }
  }
  
  fs.writeFileSync(filePath, content, 'utf-8');
  return true;
}

// Process all TypeScript files
const files = glob.sync('src/**/*.{ts,tsx}', {
  ignore: [
    'src/**/*.test.{ts,tsx}',
    'src/**/*.spec.{ts,tsx}',
    'src/**/*.d.ts',
    'src/lib/constants/**/*',
    'src/lib/db/types.ts',
  ]
});

let updatedCount = 0;
files.forEach(file => {
  if (updateFile(file)) {
    console.log(`Updated: ${file}`);
    updatedCount++;
  }
});

console.log(`\nTotal files updated: ${updatedCount}`);