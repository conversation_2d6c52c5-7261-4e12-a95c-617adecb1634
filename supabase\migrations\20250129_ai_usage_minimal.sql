-- Minimal version: Just create the tables needed for achievements
-- Run this if you're having issues with the more complex migration

-- Create AI usage logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS ai_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  agent_type VARCHAR(100) NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10, 4) DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_user ON ai_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_created ON ai_usage_logs(created_at DESC);

-- Enable RLS
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;

-- Drop and recreate policies
DROP POLICY IF EXISTS "Users can view their own AI usage" ON ai_usage_logs;
DROP POLICY IF EXISTS "Users can insert their own AI usage" ON ai_usage_logs;

CREATE POLICY "Users can view their own AI usage" ON ai_usage_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own AI usage" ON ai_usage_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Grant permissions
GRANT SELECT, INSERT ON ai_usage_logs TO authenticated;

-- Create writing sessions table
CREATE TABLE IF NOT EXISTS writing_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  session_date DATE NOT NULL,
  words_written INTEGER DEFAULT 0,
  duration_minutes INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, session_date)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user ON writing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_date ON writing_sessions(session_date DESC);

-- Enable RLS
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;

-- Drop and recreate policies
DROP POLICY IF EXISTS "Users can view their own writing sessions" ON writing_sessions;
DROP POLICY IF EXISTS "Users can manage their own writing sessions" ON writing_sessions;

CREATE POLICY "Users can view their own writing sessions" ON writing_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own writing sessions" ON writing_sessions
  FOR ALL USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON writing_sessions TO authenticated;

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE 'Tables created successfully!';
    RAISE NOTICE '- ai_usage_logs: For tracking AI agent usage';
    RAISE NOTICE '- writing_sessions: For tracking daily writing streaks';
    RAISE NOTICE 'The achievement system can now track AI usage and writing streaks.';
END $$;