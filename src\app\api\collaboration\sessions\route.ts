import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { verifyProjectOwnership } from '@/lib/api/collaboration-middleware'
import { ServiceManager } from '@/lib/services/service-manager'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId } = body

    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      )
    }

    // Verify user owns or can edit the project
    const authResult = await verifyProjectOwnership(projectId)
    if (!authResult.success) {
      return authResult.response!
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationService = await serviceManager.getCollaborationHub()
    if (!collaborationService) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationService.createSession(
      projectId,
      authResult.user.id
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to create session' },
        { status: 400 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      sessionId: result.data 
    })
  } catch (error) {
    logger.error('Collaboration session creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationService = await serviceManager.getCollaborationHub()
    if (!collaborationService) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationService.getSessionHistory(sessionId)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to get session history' },
        { status: 400 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      history: result.data 
    })
  } catch (error) {
    logger.error('Collaboration session history error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}