import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { verifyProjectOwnership } from '@/lib/api/collaboration-middleware'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { ServiceManager } from '@/lib/services/service-manager'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'
import { createTypedServerClient } from '@/lib/supabase'

// Validation schemas
const createSessionSchema = z.object({
  projectId: baseSchemas.uuid,
  name: baseSchemas.title.optional(),
  description: baseSchemas.description.optional(),
  settings: z.object({
    allowAnonymous: z.boolean().default(false),
    maxParticipants: z.number().min(2).max(50).default(10),
    autoSaveInterval: z.number().min(30).max(600).default(120), // seconds
    sessionTimeout: z.number().min(1800).max(86400).default(7200) // 30min to 24hrs
  }).optional()
});

const sessionQuerySchema = z.object({
  sessionId: baseSchemas.uuid.optional(),
  projectId: baseSchemas.uuid.optional(),
  active: z.enum(['true', 'false']).transform(val => val === 'true').optional()
});

export async function POST(request: NextRequest) {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: createSessionSchema,
    rateLimitKey: 'collaboration-session-create',
    rateLimitCost: 5,
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const { projectId, name, description, settings } = context.body;

  try {
    // Verify user owns or can edit the project
    const authResult = await verifyProjectOwnership(projectId);
    if (!authResult.success) {
      return authResult.response!;
    }

    const user = authResult.user!;

    // Check for existing active sessions
    const supabase = await createTypedServerClient();
    const { data: activeSessions, error: sessionError } = await supabase
      .from('collaboration_sessions')
      .select('id, name, created_at')
      .eq('project_id', projectId)
      .eq('is_active', true)
      .eq('created_by', user.id);

    if (sessionError) {
      throw sessionError;
    }

    // Limit active sessions per project
    if (activeSessions && activeSessions.length >= 3) {
      logger.warn('Too many active collaboration sessions', {
        projectId,
        userId: user.id,
        activeCount: activeSessions.length,
        clientIP: context.clientIP
      });
      
      return UnifiedResponse.error(
        'Too many active collaboration sessions. Please end an existing session first.',
        429,
        { activeSessions: activeSessions.map(s => ({ id: s.id, name: s.name })) }
      );
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationService = await serviceManager.getCollaborationHub()
    if (!collaborationService) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationService.createSession(
      projectId,
      user.id,
      {
        name: name || `Collaboration Session ${new Date().toLocaleDateString()}`,
        description,
        settings
      }
    );

    if (!result.success) {
      logger.error('Failed to create collaboration session', {
        projectId,
        userId: user.id,
        error: result.error,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error(result.error || 'Failed to create session');
    }

    logger.info('Collaboration session created', {
      sessionId: result.data,
      projectId,
      userId: user.id,
      hasSettings: !!settings,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      sessionId: result.data,
      message: 'Collaboration session created successfully',
      shareUrl: `${process.env.NEXT_PUBLIC_APP_URL}/collaborate/${result.data}`
    });
  } catch (error) {
    logger.error('Collaboration session creation error:', error, {
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to create collaboration session');
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    sessionId: searchParams.get('sessionId'),
    projectId: searchParams.get('projectId'),
    active: searchParams.get('active')
  };

  // Validate query parameters
  const parseResult = sessionQuerySchema.safeParse(queryParams);
  if (!parseResult.success) {
    return UnifiedResponse.error('Invalid query parameters', 400, parseResult.error.errors);
  }

  const { sessionId, projectId, active } = parseResult.data;

  if (!sessionId && !projectId) {
    return UnifiedResponse.error('Either sessionId or projectId is required', 400);
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'collaboration-session-list',
    rateLimitCost: 2,
    maxRequestSize: 1024
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    // Authentication
    const authResult = await authenticateUser();
    if (!authResult.success || !authResult.user) {
      return authResult.response!;
    }

    const user = authResult.user;

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationService = await serviceManager.getCollaborationHub()
    if (!collaborationService) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    if (sessionId) {
      // Get specific session history
      const result = await collaborationService.getSessionHistory(sessionId);

      if (!result.success) {
        logger.error('Failed to get session history', {
          sessionId,
          userId: user.id,
          error: result.error,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error(result.error || 'Failed to get session history');
      }

      logger.debug('Retrieved session history', {
        sessionId,
        userId: user.id,
        eventCount: result.data?.length || 0,
        clientIP: context.clientIP
      });

      return UnifiedResponse.success({ 
        history: result.data || [],
        sessionId
      });
    } else if (projectId) {
      // Get all sessions for a project
      const supabase = await createTypedServerClient();
      
      // Verify user has access to the project
      const { data: project } = await supabase
        .from('projects')
        .select('user_id')
        .eq('id', projectId)
        .single();

      if (!project) {
        return UnifiedResponse.error('Project not found', 404);
      }

      const isOwner = project.user_id === user.id;
      if (!isOwner) {
        const { data: collaborator } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();

        if (!collaborator) {
          return UnifiedResponse.error('Access denied to this project', 403);
        }
      }

      // Get sessions
      let query = supabase
        .from('collaboration_sessions')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (active !== undefined) {
        query = query.eq('is_active', active);
      }

      const { data: sessions, error } = await query;

      if (error) {
        throw error;
      }

      logger.debug('Retrieved project sessions', {
        projectId,
        userId: user.id,
        sessionCount: sessions?.length || 0,
        activeFilter: active,
        clientIP: context.clientIP
      });

      return UnifiedResponse.success({ 
        sessions: sessions || [],
        projectId
      });
    }
  } catch (error) {
    logger.error('Collaboration session error:', error, {
      sessionId,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to retrieve collaboration sessions');
  }
}