import { describe, it, expect, jest } from '@jest/globals';
import { ContextManager } from '@/lib/services/context-manager';

// Mock logger is already globally mocked in jest-setup

describe('ContextManager.validateContinuity', () => {
  it('awaits character consistency checks for multiple characters', async () => {
    const manager = new ContextManager();

    const context = {
      projectId: 'project-1',
      storyBible: {
        characters: [
          { name: '<PERSON>', personality: 'shy' },
          { name: '<PERSON>', personality: 'bold' }
        ],
        worldBuilding: [],
        plotlines: [],
        themes: [],
        style: {}
      },
      continuity: {
        timeline: [],
        relationships: [],
        consistency: 100
      },
      memory: {
        shortTerm: [],
        longTerm: [],
        compressed: []
      }
    };

    // Mock dependent methods
    jest.spyOn(manager, 'getProjectContext').mockResolvedValue({ success: true, data: context } as any);
    jest.spyOn(manager as any, 'checkTimelineConsistency').mockResolvedValue([]);
    jest.spyOn(manager as any, 'checkWorldConsistency').mockResolvedValue([]);

    const checkCharacterConsistency = jest
      .spyOn(manager as any, 'checkCharacterConsistency')
      .mockImplementation(async (_content: string, character: any) => {
        await new Promise(res => setTimeout(res, 10));
        return [{
          type: 'personality',
          description: `${character.name} issue`,
          severity: 'low'
        }];
      });

    const result = await manager.validateContinuity(
      'project-1',
      'Alice went home. Bob stayed behind.',
      1
    );

    expect(result.success).toBe(true);
    expect(checkCharacterConsistency).toHaveBeenCalledTimes(2);
    expect(result.data?.issues).toEqual([
      { type: 'personality', description: 'Alice issue', severity: 'low' },
      { type: 'personality', description: 'Bob issue', severity: 'low' }
    ]);
  });
});
