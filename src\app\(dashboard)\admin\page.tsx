'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { createClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { 
  Users, 
  CreditCard, 
  BookOpen, 
  AlertCircle,
  Activity,
  TrendingUp,
  Shield,
  RefreshCw
} from 'lucide-react'
import { UsersManagement } from '@/components/admin/users-management'
import { SubscriptionsOverview } from '@/components/admin/subscriptions-overview'
import { SystemHealth } from '@/components/admin/system-health'
import { AIUsageStats } from '@/components/admin/ai-usage-stats'
import { DB_TABLES } from '@/lib/config'
import { TIME_MS } from '@/lib/constants'

interface AdminStats {
  totalUsers: number
  activeSubscriptions: number
  totalProjects: number
  monthlyRevenue: number
  aiUsageThisMonth: number
  systemStatus: 'healthy' | 'degraded' | 'error'
}

export default function AdminDashboard() {
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()
  const supabase = createClient()

  useEffect(() => {
    checkAdminAccess()
    loadStats()
  }, [])

  const checkAdminAccess = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      router.push('/login')
      return
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from(DB_TABLES.PROFILES)
      .select('role')
      .eq('id', user.id)
      .single()

    if (profile?.role !== 'admin') {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page.',
        variant: 'destructive'
      })
      router.push('/dashboard')
      return
    }

    setIsAdmin(true)
  }

  const loadStats = async () => {
    try {
      // Load various statistics
      const [
        usersResult,
        subscriptionsResult,
        projectsResult,
        revenueResult,
        aiUsageResult
      ] = await Promise.all([
        supabase.from(DB_TABLES.USERS).select('id', { count: 'exact' }),
        supabase.from(DB_TABLES.SUBSCRIPTIONS).select('id', { count: 'exact' }).eq('status', 'active'),
        supabase.from(DB_TABLES.PROJECTS).select('id', { count: 'exact' }),
        loadMonthlyRevenue(),
        loadAIUsage()
      ])

      setStats({
        totalUsers: usersResult.count || 0,
        activeSubscriptions: subscriptionsResult.count || 0,
        totalProjects: projectsResult.count || 0,
        monthlyRevenue: revenueResult,
        aiUsageThisMonth: aiUsageResult,
        systemStatus: 'healthy'
      })
    } catch (error) {
      toast({
        title: 'Error loading stats',
        description: 'Failed to load dashboard statistics',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadMonthlyRevenue = async (): Promise<number> => {
    // Calculate revenue from active subscriptions
    const { data } = await supabase
      .from(DB_TABLES.SUBSCRIPTIONS)
      .select('tier')
      .eq('status', 'active')

    if (!data) return 0

    // Map tiers to prices (simplified)
    const tierPrices: Record<string, number> = {
      starter: 0,
      writer: 9,
      author: 29,
      professional: 49,
      studio: 89
    }

    return data.reduce((total, sub) => total + (tierPrices[sub.tier] || 0), 0)
  }

  const loadAIUsage = async (): Promise<number> => {
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const { data } = await supabase
      .from(DB_TABLES.AI_USAGE)
      .select('words_generated')
      .gte('timestamp', startOfMonth.toISOString())

    if (!data) return 0

    return data.reduce((total, log) => total + (log.words_generated || 0), 0)
  }

  if (isAdmin === false || loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        {loading ? (
          <>
            <Skeleton className="h-8 w-48" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Skeleton key={i} className="h-32" />
              ))}
            </div>
          </>
        ) : null}
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">Monitor and manage BookScribe AI</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={stats?.systemStatus === 'healthy' ? 'default' : 'destructive'}>
            <Activity className="w-3 h-3 mr-1" />
            System {stats?.systemStatus}
          </Badge>
          <Button 
            onClick={() => {
              setLoading(true)
              loadStats()
            }}
            size="sm"
            variant="outline"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Registered accounts</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.activeSubscriptions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Paying customers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats?.monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Recurring revenue</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalProjects.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Books being written</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Usage This Month</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(stats?.aiUsageThisMonth / TIME_MS.SECOND).toFixed(0)}k</div>
            <p className="text-xs text-muted-foreground">Words generated</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Admin Actions</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => router.push('/api/admin/export-data')}
            >
              Export Data
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Management Tabs */}
      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="ai-usage">AI Usage</TabsTrigger>
          <TabsTrigger value="system">System Health</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <UsersManagement />
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <SubscriptionsOverview />
        </TabsContent>

        <TabsContent value="ai-usage" className="space-y-4">
          <AIUsageStats />
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <SystemHealth />
        </TabsContent>
      </Tabs>
    </div>
  )
}