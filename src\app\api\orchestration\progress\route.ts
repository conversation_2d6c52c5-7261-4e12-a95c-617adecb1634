import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { orchestratorInstances } from '@/lib/agents/orchestrator-instances';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware';
import { logger } from '@/lib/services/logger'
import { z } from 'zod';
import { baseSchemas } from '@/lib/validation/common-schemas';
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

// Validation schema for progress action
const progressActionSchema = z.object({
  projectId: baseSchemas.uuid,
  action: z.enum(['cancel', 'pause', 'resume'])
});

export const GET = UnifiedAuthService.withAuth(async (request) => {
  const { searchParams } = new URL(request.url);
  const projectId = searchParams.get('projectId');

  if (!projectId) {
    return NextResponse.json(
      { error: 'Project ID is required' },
      { status: 400 }
    );
  }

  // Validate UUID format
  if (!baseSchemas.uuid.safeParse(projectId).success) {
    return NextResponse.json(
      { error: 'Invalid project ID format' },
      { status: 400 }
    );
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'authenticated',
    rateLimitCost: 1,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      // Verify user owns the project
      const project = await verifyProjectAccess(projectId, user.id)
      if (!project) {
        return { valid: false, error: PROJECT_ACCESS_ERROR }
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const orchestrator = orchestratorInstances.get(projectId);
    
    if (!orchestrator) {
      return NextResponse.json(
        { error: 'No active orchestration found for this project' },
        { status: 404 }
      );
    }

    const progress = orchestrator.getOrchestrationStatus();
    
    logger.debug('Orchestration progress retrieved', {
      projectId,
      userId: user.id,
      status: progress.status,
      clientIP: context.clientIP
    });

    return NextResponse.json({
      success: true,
      data: progress
    });

  } catch (error) {
    logger.error('Error getting orchestration progress:', error, {
      projectId,
      userId: user.id,
      clientIP: context.clientIP
    });
    
    return NextResponse.json(
      { error: 'Failed to get orchestration progress' },
      { status: 500 }
    );
  }
});

export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: progressActionSchema,
    rateLimitKey: 'authenticated',
    rateLimitCost: 5,
    maxBodySize: 1024,
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const body = await req.json();
      const user = req.user;
      
      if (!user) return { valid: false, error: 'Authentication required' };

      // Verify user owns the project
      const project = await verifyProjectAccess(body.projectId, user.id)
      if (!project) {
        return { valid: false, error: PROJECT_ACCESS_ERROR }
      }

      // Check if orchestration exists
      if (!orchestratorInstances.has(body.projectId)) {
        return { valid: false, error: 'No active orchestration found for this project' };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, action } = context.body;

  try {
    const orchestrator = orchestratorInstances.get(projectId);
    
    if (!orchestrator) {
      return NextResponse.json(
        { error: 'No active orchestration found for this project' },
        { status: 404 }
      );
    }

    let message = '';
    
    switch (action) {
      case 'cancel':
        await orchestrator.cancelOrchestration();
        orchestratorInstances.delete(projectId);
        message = 'Orchestration cancelled';
        break;
        
      case 'pause':
        orchestrator.pauseOrchestration();
        message = 'Orchestration paused';
        break;
        
      case 'resume':
        orchestrator.resumeOrchestration();
        message = 'Orchestration resumed';
        break;
    }

    logger.info('Orchestration action performed', {
      projectId,
      userId: user.id,
      action,
      clientIP: context.clientIP
    });

    return NextResponse.json({
      success: true,
      message
    });

  } catch (error) {
    logger.error('Error controlling orchestration:', error, {
      projectId,
      userId: user.id,
      action,
      clientIP: context.clientIP
    });
    
    return NextResponse.json(
      { error: 'Failed to control orchestration' },
      { status: 500 }
    );
  }
});