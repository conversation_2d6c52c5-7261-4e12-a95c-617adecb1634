import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'

export const POST = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()

    const { data: notification, error } = await supabase
      .from('notifications')
      .update({ 
        read: true, 
        read_at: new Date().toISOString() 
      })
      .eq('id', params.id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error marking notification as read:', error)
      return NextResponse.json(
        { error: 'Failed to update notification' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      id: notification.id,
      read: notification.read,
      read_at: notification.read_at
    })
  } catch (error) {
    console.error('Error in notification read:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})