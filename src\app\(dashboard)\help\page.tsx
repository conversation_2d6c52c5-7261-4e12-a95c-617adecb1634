'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { 
  HelpCircle, 
  BookOpen, 
  MessageSquare, 
  Mail, 
  FileText,
  Zap,
  Shield,
  CreditCard,
  Users,
  Settings,
  Search,
  X
} from 'lucide-react'
import Link from 'next/link'

const faqs = [
  {
    category: "Getting Started",
    icon: Zap,
    questions: [
      {
        q: "How do I create my first project?",
        a: "Click the 'New Project' button on your dashboard or use the floating action button. Our project wizard will guide you through setting up your novel's details, genre, and writing preferences."
      },
      {
        q: "What's the difference between a standalone project and a series?",
        a: "A standalone project is a single book. A series allows you to connect multiple books with shared characters, locations, and continuity. You can convert a standalone to a series later if needed."
      },
      {
        q: "How do AI agents help with writing?",
        a: "Our AI agents assist with different aspects of writing: Story Architect creates plot structures, Character Developer builds detailed profiles, Chapter Planner outlines scenes, and the Writing Agent generates content while maintaining consistency."
      }
    ]
  },
  {
    category: "Writing Features",
    icon: BookOpen,
    questions: [
      {
        q: "How does the Story Bible work?",
        a: "The Story Bible is your central repository for characters, locations, and plot threads. It maintains consistency across your entire project and can be shared across books in a series."
      },
      {
        q: "Can I import existing manuscripts?",
        a: "Yes! Use the import feature (upload icon) in the editor to import .docx, .txt, or .md files. The system will automatically split your content into chapters."
      },
      {
        q: "How do voice profiles maintain consistency?",
        a: "Voice profiles analyze your writing style and character voices. They help maintain consistent tone, vocabulary, and speech patterns throughout your novel."
      }
    ]
  },
  {
    category: "Account & Billing",
    icon: CreditCard,
    questions: [
      {
        q: "What's included in the free plan?",
        a: "The free plan includes basic writing features, limited AI assistance, and one active project. Perfect for trying out BookScribe before upgrading."
      },
      {
        q: "How do I upgrade my subscription?",
        a: "Go to Settings > Subscription or visit the Pricing page. Choose your plan and follow the secure checkout process."
      },
      {
        q: "Can I cancel anytime?",
        a: "Yes, you can cancel your subscription anytime from the Settings page. You'll retain access until the end of your billing period."
      }
    ]
  }
]

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')
  
  // Filter FAQs based on search query
  const filteredFaqs = useMemo(() => {
    if (!searchQuery.trim()) return faqs
    
    const query = searchQuery.toLowerCase()
    return faqs.map(category => ({
      ...category,
      questions: category.questions.filter(
        item => 
          item.q.toLowerCase().includes(query) || 
          item.a.toLowerCase().includes(query)
      )
    })).filter(category => category.questions.length > 0)
  }, [searchQuery])
  
  // Check if any results were found
  const hasResults = filteredFaqs.length > 0
  
  const handleSearch = () => {
    // Search is already reactive through filteredFaqs
  }
  
  const clearSearch = () => {
    setSearchQuery('')
  }
  return (
    <div className="container-wide py-6 sm:py-8 lg:py-10 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Help & Support</h1>
        <p className="text-muted-foreground">
          Find answers to common questions and get the support you need
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 sm:gap-5 lg:gap-6 md:grid-cols-3 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <FileText className="h-5 w-5" />
              Documentation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Comprehensive guides and tutorials
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/docs">View Docs</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <MessageSquare className="h-5 w-5" />
              Community
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Connect with other writers
            </p>
            <Button variant="outline" size="sm" asChild>
              <a href="https://discord.gg/bookscribe" target="_blank" rel="noopener noreferrer">
                Join Discord
              </a>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Mail className="h-5 w-5" />
              Email Support
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Get help from our team
            </p>
            <Button variant="outline" size="sm" asChild>
              <a href="mailto:<EMAIL>">Contact Us</a>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Search Help Articles</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input 
                placeholder="Search for help..." 
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pr-8"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
                  onClick={clearSearch}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
          {searchQuery && !hasResults && (
            <p className="text-sm text-muted-foreground mt-4">
              No results found for "{searchQuery}". Try different keywords.
            </p>
          )}
        </CardContent>
      </Card>

      {/* FAQs */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
          <CardDescription>
            Quick answers to common questions about BookScribe
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredFaqs.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-6 last:mb-0">
              <h3 className="flex items-center gap-2 text-lg font-semibold mb-3">
                <category.icon className="h-5 w-5" />
                {category.category}
              </h3>
              <Accordion type="single" collapsible className="w-full">
                {category.questions.map((item, index) => (
                  <AccordionItem key={index} value={`item-${categoryIndex}-${index}`}>
                    <AccordionTrigger className="text-left">
                      {item.q}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground">
                      {item.a}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Additional Resources */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Still need help?</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <h4 className="font-medium">Privacy & Security</h4>
              <p className="text-sm text-muted-foreground">
                Learn about how we protect your data and manuscripts.{' '}
                <Link href="/privacy" className="text-primary hover:underline">
                  Privacy Policy
                </Link>
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <Users className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <h4 className="font-medium">Feature Requests</h4>
              <p className="text-sm text-muted-foreground">
                Have an idea for BookScribe? We'd love to hear it!{' '}
                <a href="https://feedback.bookscribe.ai" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">
                  Submit Feedback
                </a>
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <Settings className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <h4 className="font-medium">Account Settings</h4>
              <p className="text-sm text-muted-foreground">
                Manage your account, subscription, and preferences.{' '}
                <Link href="/settings" className="text-primary hover:underline">
                  Go to Settings
                </Link>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}