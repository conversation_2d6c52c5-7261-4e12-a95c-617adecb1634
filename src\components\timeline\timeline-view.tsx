'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  AlertTriangle, 
  CheckCircle2, 
  User, 
  MapPin, 
  BookOpen,
  Zap,
  RefreshCw,
  Settings,
  Filter
} from 'lucide-react';

interface TimelineEvent {
  id: string;
  type: 'plot' | 'character' | 'world' | 'reference';
  title: string;
  description: string;
  timestamp: {
    type: 'absolute' | 'relative' | 'seasonal' | 'vague';
    value: string;
    parsedDate?: Date;
    uncertainty: number;
  };
  chapter: number;
  scene?: number;
  characters: string[];
  location?: string;
  importance: number;
  verified: boolean;
  conflicts: TimelineConflict[];
}

interface TimelineConflict {
  id: string;
  type: 'chronological' | 'logical' | 'duration' | 'character_age' | 'seasonal' | 'reference';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedEvents: string[];
  suggestions: string[];
  autoFixable: boolean;
}

interface ValidationResult {
  isValid: boolean;
  conflicts: TimelineConflict[];
  warnings: string[];
  suggestions: string[];
  confidence: number;
}

interface TimelineViewProps {
  projectId: string;
  onEventSelect?: (event: TimelineEvent) => void;
  onConflictResolve?: (conflictId: string) => void;
}

export function TimelineView({ projectId, onEventSelect }: TimelineViewProps) {
  const [events, setEvents] = useState<TimelineEvent[]>([]);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null);
  const [viewMode, setViewMode] = useState<'linear' | 'chapter' | 'character'>('linear');
  const [filterType, setFilterType] = useState<'all' | 'plot' | 'character' | 'world' | 'conflicts'>('all');
  const [loading, setLoading] = useState(false);

  const fetchTimelineData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/timeline/events?projectId=${projectId}`);
      if (response.ok) {
        const data = await response.json();
        setEvents(data.events || []);
        setValidation(data.validation || null);
      }
    } catch (error) {
      logger.error('Error fetching timeline data:', error);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchTimelineData();
  }, [fetchTimelineData]);

  const handleValidateTimeline = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/timeline/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      });
      
      if (response.ok) {
        const data = await response.json();
        setValidation(data.validation);
      }
    } catch (error) {
      logger.error('Error validating timeline:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAutoFix = async (conflictIds: string[]) => {
    setLoading(true);
    try {
      const response = await fetch('/api/timeline/autofix', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId, conflictIds })
      });
      
      if (response.ok) {
        await fetchTimelineData();
      }
    } catch (error) {
      logger.error('Error auto-fixing conflicts:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredEvents = useMemo(() => {
    let filtered = events;

    if (filterType !== 'all') {
      if (filterType === 'conflicts') {
        filtered = events.filter(e => e.conflicts.length > 0);
      } else {
        filtered = events.filter(e => e.type === filterType);
      }
    }

    return filtered.sort((a, b) => {
      if (viewMode === 'linear' && a.timestamp.parsedDate && b.timestamp.parsedDate) {
        return a.timestamp.parsedDate.getTime() - b.timestamp.parsedDate.getTime();
      }
      return a.chapter - b.chapter;
    });
  }, [events, filterType, viewMode]);

  const groupedEvents = useMemo(() => {
    if (viewMode === 'character') {
      const groups = new Map<string, TimelineEvent[]>();
      filteredEvents.forEach(event => {
        event.characters.forEach(character => {
          if (!groups.has(character)) groups.set(character, []);
          groups.get(character)!.push(event);
        });
      });
      return groups;
    } else if (viewMode === 'chapter') {
      const groups = new Map<number, TimelineEvent[]>();
      filteredEvents.forEach(event => {
        if (!groups.has(event.chapter)) groups.set(event.chapter, []);
        groups.get(event.chapter)!.push(event);
      });
      return groups;
    }
    return new Map();
  }, [filteredEvents, viewMode]);

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'plot': return <Zap className="w-4 h-4" />;
      case 'character': return <User className="w-4 h-4" />;
      case 'world': return <MapPin className="w-4 h-4" />;
      case 'reference': return <BookOpen className="w-4 h-4" />;
      default: return <Calendar className="w-4 h-4" />;
    }
  };

  const getConflictColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-error bg-error-light border-red-200';
      case 'high': return 'text-warning bg-orange-50 border-orange-200';
      case 'medium': return 'text-warning bg-warning-light border-yellow-200';
      case 'low': return 'text-info bg-info-light border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: TimelineEvent['timestamp']) => {
    if (timestamp.parsedDate) {
      return timestamp.parsedDate.toLocaleDateString();
    }
    return timestamp.value;
  };

  const renderEventCard = (event: TimelineEvent) => (
    <Card 
      key={event.id} 
      className={`mb-4 cursor-pointer transition-all hover:shadow-md ${
        event.conflicts.length > 0 ? 'border-red-200 bg-error-light' : ''
      } ${selectedEvent?.id === event.id ? 'ring-2 ring-blue-500' : ''}`}
      onClick={() => {
        setSelectedEvent(event);
        onEventSelect?.(event);
      }}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            {getEventIcon(event.type)}
            <div>
              <CardTitle className="text-sm">{event.title}</CardTitle>
              <CardDescription className="text-xs">
                Chapter {event.chapter} &quot; {formatTimestamp(event.timestamp)}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className="text-xs">
              {event.type}
            </Badge>
            {event.conflicts.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {event.conflicts.length} conflict{event.conflicts.length > 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-sm text-gray-600 mb-2">{event.description}</p>
        
        {event.characters.length > 0 && (
          <div className="flex items-center space-x-1 mb-2">
            <User className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-500">
              {event.characters.join(', ')}
            </span>
          </div>
        )}

        {event.location && (
          <div className="flex items-center space-x-1 mb-2">
            <MapPin className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-500">{event.location}</span>
          </div>
        )}

        {event.conflicts.length > 0 && (
          <div className="space-y-1">
            {event.conflicts.map(conflict => (
              <Alert key={conflict.id} className={`p-2 ${getConflictColor(conflict.severity)}`}>
                <AlertTriangle className="w-3 h-3" />
                <AlertDescription className="text-xs ml-2">
                  {conflict.description}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderLinearView = () => (
    <div className="space-y-4">
      {filteredEvents.map(renderEventCard)}
    </div>
  );

  const renderGroupedView = () => (
    <div className="space-y-6">
      {Array.from(groupedEvents.entries()).map(([key, groupEvents]) => (
        <div key={key}>
          <h3 className="font-semibold mb-3 text-lg">
            {viewMode === 'character' ? `Character: ${key}` : `Chapter ${key}`}
          </h3>
          <div className="space-y-4 pl-4 border-l-2 border-gray-200">
            {groupEvents.map(renderEventCard)}
          </div>
        </div>
      ))}
    </div>
  );

  const conflictSummary = validation?.conflicts.reduce((acc, conflict) => {
    acc[conflict.severity] = (acc[conflict.severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>) || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Timeline Validation</h2>
          <p className="text-gray-600">
            Track chronological consistency across your story
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            onClick={handleValidateTimeline} 
            disabled={loading}
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Validate
          </Button>
        </div>
      </div>

      {/* Validation Summary */}
      {validation && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                {validation.isValid ? (
                  <CheckCircle2 className="w-5 h-5 text-success" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-error" />
                )}
                <span>Timeline Status</span>
              </CardTitle>
              <Badge variant={validation.isValid ? "default" : "destructive"}>
                {Math.round(validation.confidence * 100)}% Confidence
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            {validation.conflicts.length > 0 ? (
              <div className="space-y-4">
                <div className="grid grid-cols-4 gap-4">
                  {Object.entries(conflictSummary).map(([severity, count]) => (
                    <div key={severity} className="text-center p-3 rounded-lg border">
                      <div className={`text-2xl font-bold ${getConflictColor(severity).split(' ')[0]}`}>
                        {count}
                      </div>
                      <div className="text-sm capitalize">{severity}</div>
                    </div>
                  ))}
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button 
                    onClick={() => handleAutoFix(validation.conflicts.filter(c => c.autoFixable).map(c => c.id))}
                    disabled={loading || !validation.conflicts.some(c => c.autoFixable)}
                    size="sm"
                  >
                    Auto-Fix Available Issues
                  </Button>
                  <span className="text-sm text-gray-500">
                    {validation.conflicts.filter(c => c.autoFixable).length} auto-fixable
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-success"> No timeline conflicts detected</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select 
              value={filterType} 
              onChange={(e) => setFilterType(e.target.value as 'all' | 'plot' | 'character' | 'world' | 'conflicts')}
              className="border rounded px-2 py-1 text-sm"
            >
              <option value="all">All Events</option>
              <option value="plot">Plot Events</option>
              <option value="character">Character Events</option>
              <option value="world">World Events</option>
              <option value="conflicts">Events with Conflicts</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4 text-gray-500" />
            <select 
              value={viewMode} 
              onChange={(e) => setViewMode(e.target.value as 'linear' | 'chapter' | 'character')}
              className="border rounded px-2 py-1 text-sm"
            >
              <option value="linear">Linear Timeline</option>
              <option value="chapter">By Chapter</option>
              <option value="character">By Character</option>
            </select>
          </div>
        </div>

        <div className="text-sm text-gray-500">
          {filteredEvents.length} events shown
        </div>
      </div>

      {/* Timeline Content */}
      <ScrollArea className="h-[600px]">
        {viewMode === 'linear' ? renderLinearView() : renderGroupedView()}
      </ScrollArea>

      {/* Event Details Panel */}
      {selectedEvent && (
        <Card>
          <CardHeader>
            <CardTitle>Event Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Basic Information</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Title:</strong> {selectedEvent.title}</div>
                  <div><strong>Type:</strong> {selectedEvent.type}</div>
                  <div><strong>Chapter:</strong> {selectedEvent.chapter}</div>
                  <div><strong>Timestamp:</strong> {formatTimestamp(selectedEvent.timestamp)}</div>
                  <div><strong>Importance:</strong> {Math.round(selectedEvent.importance * 100)}%</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Context</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Characters:</strong> {selectedEvent.characters.join(', ') || 'None'}</div>
                  <div><strong>Location:</strong> {selectedEvent.location || 'Not specified'}</div>
                  <div><strong>Verified:</strong> {selectedEvent.verified ? 'Yes' : 'No'}</div>
                  <div><strong>Conflicts:</strong> {selectedEvent.conflicts.length}</div>
                </div>
              </div>
            </div>
            
            <div className="mt-4">
              <h4 className="font-semibold mb-2">Description</h4>
              <p className="text-sm text-gray-600">{selectedEvent.description}</p>
            </div>

            {selectedEvent.conflicts.length > 0 && (
              <div className="mt-4">
                <h4 className="font-semibold mb-2">Conflicts</h4>
                <div className="space-y-2">
                  {selectedEvent.conflicts.map(conflict => (
                    <div key={conflict.id} className={`p-3 rounded-lg border ${getConflictColor(conflict.severity)}`}>
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline">{conflict.type}</Badge>
                        <Badge variant="outline">{conflict.severity}</Badge>
                      </div>
                      <p className="text-sm mb-2">{conflict.description}</p>
                      {conflict.suggestions.length > 0 && (
                        <div>
                          <strong className="text-xs">Suggestions:</strong>
                          <ul className="text-xs mt-1 ml-4 list-disc">
                            {conflict.suggestions.map((suggestion, idx) => (
                              <li key={idx}>{suggestion}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {conflict.autoFixable && (
                        <Button 
                          size="sm" 
                          className="mt-2"
                          onClick={() => handleAutoFix([conflict.id])}
                        >
                          Auto-Fix
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}