'use client';

import { useState, useEffect } from 'react';
import { logger } from '@/lib/services/logger';

import type { DevelopmentGridData } from '@/lib/types/character-development';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Star,
  Calendar,
  Heart,
  Target
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Scatter<PERSON>hart,
  Scatter
} from 'recharts';
import { CharacterDevelopmentGrid } from './character-development-grid';
import { ArcPatternAnalyzer } from './arc-pattern-analyzer';
import type { CharacterArcData } from '@/lib/types/analysis';
import type { ChartTooltipProps } from '@/lib/types/charts';

interface CharacterArcVisualizerProps {
  characterArcs: CharacterArcData[];
  projectId: string;
  className?: string;
}

interface ArcChartDataPoint {
  chapter: number;
  development: number;
  confidence: number;
  agency: number;
  relationships: number;
  emotionalState: string;
  [key: string]: unknown;
}

interface SignificanceChartDataPoint {
  chapter: number;
  significance: number;
  event: string;
  type: string;
  [key: string]: unknown;
}

export function CharacterArcVisualizer({ characterArcs, projectId, className }: CharacterArcVisualizerProps) {
  const [selectedCharacter, setSelectedCharacter] = useState<string>(characterArcs[0]?.characterId || '');
  const [viewMode, setViewMode] = useState<'development' | 'comparison' | 'milestones' | 'grid' | 'pattern'>('development');
  const [gridData, setGridData] = useState<DevelopmentGridData | null>(null);
  const [isLoadingGrid, setIsLoadingGrid] = useState(false);

  const selectedArc = characterArcs.find(arc => arc.characterId === selectedCharacter);

  // Fetch grid data when grid view is selected
  useEffect(() => {
    if (viewMode === 'grid' && selectedCharacter && projectId) {
      setIsLoadingGrid(true);
      fetch(`/api/analysis/character-development-grid?characterId=${selectedCharacter}&projectId=${projectId}`)
        .then(response => response.json())
        .then(data => {
          setGridData(data);
          setIsLoadingGrid(false);
        })
        .catch(error => {
          logger.error('Failed to fetch grid data:', error);
          setIsLoadingGrid(false);
        });
    }
  }, [viewMode, selectedCharacter, projectId]);
  
  const getArcTypeColor = (arcType: string) => {
    switch (arcType) {
      case 'positive_change':
        return '#22c55e'; // Green
      case 'negative_change':
        return '#ef4444'; // Red
      case 'flat_arc':
        return '#3b82f6'; // Blue
      case 'corruption':
        return '#a855f7'; // Purple
      case 'redemption':
        return '#f59e0b'; // Orange
      default:
        return '#6b7280'; // Gray
    }
  };

  const getArcTypeLabel = (arcType: string) => {
    return arcType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'protagonist':
        return <Star className="w-4 h-4 text-warning" />;
      case 'antagonist':
        return <Target className="w-4 h-4 text-error" />;
      case 'supporting':
        return <Heart className="w-4 h-4 text-info" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };


  const getMilestoneIcon = (type: string) => {
    switch (type) {
      case 'growth':
        return <TrendingUp className="w-4 h-4 text-success" />;
      case 'setback':
        return <TrendingDown className="w-4 h-4 text-error" />;
      case 'revelation':
        return <Star className="w-4 h-4 text-warning" />;
      case 'decision':
        return <Target className="w-4 h-4 text-info" />;
      case 'conflict':
        return <Calendar className="w-4 h-4 text-purple-500" />;
      default:
        return <Minus className="w-4 h-4" />;
    }
  };

  const protagonists = characterArcs.filter(arc => arc.role === 'protagonist');
  const antagonists = characterArcs.filter(arc => arc.role === 'antagonist');
  const supporting = characterArcs.filter(arc => arc.role === 'supporting');

  const avgConsistency = characterArcs.reduce((acc, arc) => acc + arc.consistency, 0) / characterArcs.length;

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Character Arc Analysis
          </CardTitle>
          <CardDescription>
            Track character development, emotional journeys, and story arc completion
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-warning-light dark:bg-yellow-950/20 rounded-lg">
              <Star className="w-5 h-5 mx-auto mb-1 text-warning" />
              <div className="text-lg font-bold text-warning">{protagonists.length}</div>
              <div className="text-xs text-warning">Protagonists</div>
            </div>
            <div className="text-center p-3 bg-error-light dark:bg-red-950/20 rounded-lg">
              <Target className="w-5 h-5 mx-auto mb-1 text-error" />
              <div className="text-lg font-bold text-error">{antagonists.length}</div>
              <div className="text-xs text-error">Antagonists</div>
            </div>
            <div className="text-center p-3 bg-info-light dark:bg-blue-950/20 rounded-lg">
              <Heart className="w-5 h-5 mx-auto mb-1 text-info" />
              <div className="text-lg font-bold text-info">{supporting.length}</div>
              <div className="text-xs text-info">Supporting</div>
            </div>
            <div className="text-center p-3 bg-success-light dark:bg-green-950/20 rounded-lg">
              <TrendingUp className="w-5 h-5 mx-auto mb-1 text-success" />
              <div className="text-lg font-bold text-success">{Math.round(avgConsistency)}%</div>
              <div className="text-xs text-success">Avg Consistency</div>
            </div>
          </div>

          {/* Character Selection */}
          <div className="flex items-center space-x-4 mb-4">
            <Select value={selectedCharacter} onValueChange={setSelectedCharacter}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select character" />
              </SelectTrigger>
              <SelectContent>
                {characterArcs.map((arc) => (
                  <SelectItem key={arc.characterId} value={arc.characterId}>
                    <div className="flex items-center space-x-2">
                      {getRoleIcon(arc.role)}
                      <span>{arc.characterName}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={viewMode} onValueChange={(value: 'development' | 'comparison' | 'milestones' | 'grid' | 'pattern') => setViewMode(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="development">Development Curve</SelectItem>
                <SelectItem value="comparison">Character Comparison</SelectItem>
                <SelectItem value="milestones">Milestone Timeline</SelectItem>
                <SelectItem value="grid">Development Grid</SelectItem>
                <SelectItem value="pattern">Pattern Analysis</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Character Details */}
      {selectedArc && !['grid', 'pattern'].includes(viewMode) && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarFallback>
                    {selectedArc.characterName.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <span>{selectedArc.characterName}</span>
                    {getRoleIcon(selectedArc.role)}
                  </CardTitle>
                  <CardDescription>
                    {getArcTypeLabel(selectedArc.arcType)} • {selectedArc.consistency}% Consistency
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge 
                  style={{ backgroundColor: getArcTypeColor(selectedArc.arcType) + '20', 
                          color: getArcTypeColor(selectedArc.arcType) }}
                >
                  {getArcTypeLabel(selectedArc.arcType)}
                </Badge>
                <Badge variant={selectedArc.completed ? 'default' : 'outline'}>
                  {selectedArc.completed ? 'Complete' : 'In Progress'}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Themes */}
            {selectedArc.themes.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Character Themes:</h4>
                <div className="flex flex-wrap gap-1">
                  {selectedArc.themes.map((theme, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {theme}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Visualization */}
            <div className="h-80">
              {viewMode === 'development' && (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={selectedArc.progression}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="chapter" 
                      label={{ value: 'Chapter', position: 'insideBottom', offset: -10 }}
                    />
                    <YAxis 
                      label={{ value: 'Development Level', angle: -90, position: 'insideLeft' }}
                      domain={[0, 100]}
                    />
                    <Tooltip
                      content={(props: ChartTooltipProps) => {
                        const { active, payload, label } = props;
                        if (active && payload && payload.length && payload[0]) {
                          const data = payload[0].payload as ArcChartDataPoint;
                          return (
                            <div className="bg-white dark:bg-slate-800 p-3 border rounded shadow">
                              <p className="font-medium">Chapter {label}</p>
                              <p className="text-info">Development: {data.development}%</p>
                              <p className="text-success">Confidence: {data.confidence}%</p>
                              <p className="text-purple-600">Agency: {data.agency}%</p>
                              <p className="text-warning">Relationships: {data.relationships}%</p>
                              <p className="text-slate-600">State: {String(data.emotionalState)}</p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="development" 
                      stroke={getArcTypeColor(selectedArc.arcType)}
                      strokeWidth={3}
                      name="Overall Development"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="confidence" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      name="Confidence"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="agency" 
                      stroke="#8b5cf6" 
                      strokeWidth={2}
                      name="Agency"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="relationships" 
                      stroke="#f59e0b" 
                      strokeWidth={2}
                      name="Relationships"
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}

              {viewMode === 'comparison' && (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="chapter" 
                      domain={[1, Math.max(...characterArcs.flatMap(arc => arc.progression.map(p => p.chapter)))]}
                    />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    {characterArcs.map((arc) => (
                      <Line
                        key={arc.characterId}
                        type="monotone"
                        data={arc.progression}
                        dataKey="development"
                        stroke={getArcTypeColor(arc.arcType)}
                        strokeWidth={arc.characterId === selectedCharacter ? 3 : 2}
                        opacity={arc.characterId === selectedCharacter ? 1 : 0.5}
                        name={arc.characterName}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              )}

              {viewMode === 'milestones' && (
                <ResponsiveContainer width="100%" height="100%">
                  <ScatterChart data={selectedArc.milestones}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="chapter" />
                    <YAxis dataKey="significance" domain={[0, 100]} />
                    <Tooltip
                      content={(props: ChartTooltipProps) => {
                        const { active, payload } = props;
                        if (active && payload && payload.length && payload[0]) {
                          const data = payload[0].payload as SignificanceChartDataPoint;
                          return (
                            <div className="bg-white dark:bg-slate-800 p-3 border rounded shadow">
                              <p className="font-medium">Chapter {data.chapter}</p>
                              <p className="text-info">{String(data.event)}</p>
                              <p className="text-slate-600">Significance: {data.significance}%</p>
                              <Badge className="mt-1">{String(data.type)}</Badge>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Scatter 
                      dataKey="significance" 
                      fill={getArcTypeColor(selectedArc.arcType)}
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Development Grid View */}
      {viewMode === 'grid' && selectedArc && (
        <div className="mt-6">
          {isLoadingGrid ? (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-gray-500">Loading development grid...</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : gridData ? (
            <CharacterDevelopmentGrid
              gridData={gridData}
              onDimensionClick={(dimensionId, chapter) => {
                logger.info('Dimension clicked:', dimensionId, chapter);
              }}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-gray-500">
                  <p>No grid data available</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Pattern Analysis View */}
      {viewMode === 'pattern' && selectedArc && (
        <div className="mt-6">
          <ArcPatternAnalyzer
            characterId={selectedArc.characterId}
            projectId={projectId}
            onAnalysisUpdate={(pattern) => {
              logger.info('Pattern analysis updated:', pattern);
            }}
          />
        </div>
      )}

      {/* Milestones Timeline */}
      {selectedArc && selectedArc.milestones.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Character Milestones
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {selectedArc.milestones
                .sort((a, b) => a.chapter - b.chapter)
                .map((milestone, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center space-x-2 min-w-0">
                    {getMilestoneIcon(milestone.type)}
                    <Badge variant="outline" className="text-xs">
                      Ch. {milestone.chapter}
                    </Badge>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{milestone.event}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {milestone.type}
                      </Badge>
                      <span className="text-xs text-slate-600">
                        {milestone.significance}% significance
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}