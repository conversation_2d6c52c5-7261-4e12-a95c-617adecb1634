'use client'

import { useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Trophy, Star, Zap, Award } from 'lucide-react'
import { cn } from '@/lib/utils'
import confetti from 'canvas-confetti'

interface Achievement {
  id: string
  name: string
  description: string
  points: number
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  category: string
  icon?: string
  unlocked_at?: string
}

interface AchievementUnlockModalProps {
  achievement: Achievement | null
  isOpen: boolean
  onClose: () => void
}

export function AchievementUnlockModal({ 
  achievement, 
  isOpen, 
  onClose 
}: AchievementUnlockModalProps) {
  
  useEffect(() => {
    if (isOpen && achievement) {
      // Trigger confetti animation
      const duration = 3 * 1000
      const animationEnd = Date.now() + duration
      const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 }

      function randomInRange(min: number, max: number) {
        return Math.random() * (max - min) + min
      }

      const interval: any = setInterval(function() {
        const timeLeft = animationEnd - Date.now()

        if (timeLeft <= 0) {
          return clearInterval(interval)
        }

        const particleCount = 50 * (timeLeft / duration)
        
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
        })
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
        })
      }, 250)
    }
  }, [isOpen, achievement])

  if (!achievement) return null

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze':
        return 'text-orange-600 bg-orange-100 border-orange-300'
      case 'silver':
        return 'text-gray-600 bg-gray-100 border-gray-300'
      case 'gold':
        return 'text-yellow-600 bg-yellow-100 border-yellow-300'
      case 'platinum':
        return 'text-purple-600 bg-purple-100 border-purple-300'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-300'
    }
  }

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'bronze':
        return <Trophy className="h-20 w-20 text-orange-600" />
      case 'silver':
        return <Award className="h-20 w-20 text-gray-600" />
      case 'gold':
        return <Star className="h-20 w-20 text-yellow-600" />
      case 'platinum':
        return <Zap className="h-20 w-20 text-purple-600" />
      default:
        return <Trophy className="h-20 w-20 text-gray-600" />
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold">
            Achievement Unlocked!
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col items-center space-y-6 py-6">
          <div className="relative">
            <div className="absolute inset-0 animate-pulse">
              <div className={cn(
                "h-full w-full rounded-full blur-xl opacity-30",
                getTierColor(achievement.tier)
              )} />
            </div>
            <div className="relative">
              {getTierIcon(achievement.tier)}
            </div>
          </div>
          
          <div className="text-center space-y-2">
            <h3 className="text-xl font-semibold">{achievement.name}</h3>
            <p className="text-muted-foreground">{achievement.description}</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge 
              variant="outline" 
              className={cn("capitalize", getTierColor(achievement.tier))}
            >
              {achievement.tier}
            </Badge>
            <Badge variant="secondary">
              +{achievement.points} points
            </Badge>
            <Badge variant="outline" className="capitalize">
              {achievement.category}
            </Badge>
          </div>
          
          <Button 
            onClick={onClose} 
            size="lg" 
            className="w-full"
          >
            Awesome!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}