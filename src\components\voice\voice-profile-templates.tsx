'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Check, BookOpen, Sparkles, Users, Globe, Heart, Sword } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { voiceTemplates as voiceTemplateConfig, getPopularTemplates } from '@/lib/config/voice-templates'

export interface VoiceProfileTemplate {
  id: string
  name: string
  description: string
  category: 'genre' | 'style' | 'author' | 'custom'
  icon: React.ReactNode
  metrics: {
    sentenceComplexity: number
    vocabularyLevel: number
    emotionalIntensity: number
    dialogueDensity: number
    descriptiveDensity: number
    pacing: number
    formalityLevel: number
  }
  patterns: {
    sentencePatterns: string[]
    vocabularyPatterns: string[]
    narrativeDevices: string[]
    commonPhrases: string[]
  }
  tags: string[]
  popularity: number
}

// Convert configuration templates to the UI format
const configuredTemplates: VoiceProfileTemplate[] = voiceTemplateConfig.map(template => ({
  id: template.id,
  name: template.name,
  description: template.description,
  category: 'genre' as const,
  icon: getCategoryIcon(template.category),
  metrics: convertToMetrics(template.characteristics),
  patterns: {
    sentencePatterns: template.examples.sentencePatterns,
    vocabularyPatterns: template.examples.vocabularyExamples,
    narrativeDevices: template.examples.styleNotes,
    commonPhrases: template.examples.sentencePatterns.slice(0, 3)
  },
  tags: template.characteristics.tone,
  popularity: template.popularity
}))

function getCategoryIcon(category: string): React.ReactNode {
  switch (category) {
    case 'Fiction':
    case 'Literary':
      return <BookOpen className="h-5 w-5" />
    case 'Genre Fiction':
    case 'Mystery':
    case 'Thriller':
      return <Sword className="h-5 w-5" />
    case 'Romance':
      return <Heart className="h-5 w-5" />
    case 'Science Fiction':
      return <Globe className="h-5 w-5" />
    case 'Young Adult':
      return <Users className="h-5 w-5" />
    default:
      return <Sparkles className="h-5 w-5" />
  }
}

function convertToMetrics(characteristics: any) {
  const formalityMap: Record<string, number> = { 
    very_formal: 0.9, formal: 0.7, neutral: 0.5, casual: 0.3, very_casual: 0.1 
  }
  const pacingMap: Record<string, number> = { 
    very_slow: 0.1, slow: 0.3, moderate: 0.5, fast: 0.7, very_fast: 0.9 
  }
  const vocabularyMap: Record<string, number> = { 
    simple: 0.2, moderate: 0.5, advanced: 0.7, technical: 0.8, literary: 0.9 
  }
  const structureMap: Record<string, number> = { 
    simple: 0.2, moderate: 0.5, complex: 0.8, varied: 0.7 
  }
  
  return {
    sentenceComplexity: structureMap[characteristics.sentenceStructure] || 0.5,
    vocabularyLevel: vocabularyMap[characteristics.vocabulary] || 0.5,
    emotionalIntensity: characteristics.tone.includes('emotional') ? 0.8 : 0.5,
    dialogueDensity: characteristics.tone.includes('dialogue') ? 0.8 : 0.5,
    descriptiveDensity: characteristics.pacing === 'slow' ? 0.8 : 0.5,
    pacing: pacingMap[characteristics.pacing] || 0.5,
    formalityLevel: formalityMap[characteristics.formality] || 0.5
  }
}

// Legacy templates kept for backward compatibility
const legacyTemplates: VoiceProfileTemplate[] = [
  // Genre Templates
  {
    id: 'thriller-noir',
    name: 'Noir Thriller',
    description: 'Dark, atmospheric prose with short, punchy sentences. Heavy on mood and tension.',
    category: 'genre',
    icon: <Sword className="h-5 w-5" />,
    metrics: {
      sentenceComplexity: 0.3,
      vocabularyLevel: 0.6,
      emotionalIntensity: 0.8,
      dialogueDensity: 0.7,
      descriptiveDensity: 0.5,
      pacing: 0.9,
      formalityLevel: 0.3
    },
    patterns: {
      sentencePatterns: ['Short. Sharp. Dangerous.', 'The [noun] [verb]ed like [metaphor].'],
      vocabularyPatterns: ['shadow', 'darkness', 'blood', 'silence', 'danger'],
      narrativeDevices: ['Metaphorical descriptions', 'Fragmented thoughts', 'Present tense urgency'],
      commonPhrases: ['The night was...', 'Something wasn\'t right', 'Time was running out']
    },
    tags: ['Dark', 'Suspenseful', 'Atmospheric', 'Fast-paced'],
    popularity: 85
  },
  {
    id: 'fantasy-epic',
    name: 'Epic Fantasy',
    description: 'Rich, descriptive prose with complex world-building. Formal tone with mythic undertones.',
    category: 'genre',
    icon: <Globe className="h-5 w-5" />,
    metrics: {
      sentenceComplexity: 0.8,
      vocabularyLevel: 0.9,
      emotionalIntensity: 0.6,
      dialogueDensity: 0.4,
      descriptiveDensity: 0.9,
      pacing: 0.4,
      formalityLevel: 0.8
    },
    patterns: {
      sentencePatterns: ['The ancient [noun] stood testament to...', 'In the days of [era], when [mythic event]...'],
      vocabularyPatterns: ['realm', 'prophecy', 'ancient', 'mystical', 'ethereal'],
      narrativeDevices: ['Epic similes', 'Historical exposition', 'Prophetic language'],
      commonPhrases: ['It was written...', 'The old magic stirred', 'Destiny awaited']
    },
    tags: ['Epic', 'Descriptive', 'Mythic', 'World-building'],
    popularity: 92
  },
  {
    id: 'romance-contemporary',
    name: 'Contemporary Romance',
    description: 'Emotional, character-driven prose focusing on relationships and feelings.',
    category: 'genre',
    icon: <Heart className="h-5 w-5" />,
    metrics: {
      sentenceComplexity: 0.5,
      vocabularyLevel: 0.5,
      emotionalIntensity: 0.9,
      dialogueDensity: 0.8,
      descriptiveDensity: 0.6,
      pacing: 0.6,
      formalityLevel: 0.3
    },
    patterns: {
      sentencePatterns: ['Her heart [verb]ed when...', 'He couldn\'t help but [action]...'],
      vocabularyPatterns: ['heart', 'love', 'desire', 'warmth', 'tender'],
      narrativeDevices: ['Internal monologue', 'Sensory descriptions', 'Emotional metaphors'],
      commonPhrases: ['Butterflies in stomach', 'Heart skipped a beat', 'Lost in his eyes']
    },
    tags: ['Emotional', 'Character-driven', 'Dialogue-heavy', 'Intimate'],
    popularity: 88
  },

  // Style Templates
  {
    id: 'minimalist-modern',
    name: 'Minimalist Modern',
    description: 'Clean, sparse prose. Says more with less. Hemingway-inspired brevity.',
    category: 'style',
    icon: <BookOpen className="h-5 w-5" />,
    metrics: {
      sentenceComplexity: 0.2,
      vocabularyLevel: 0.4,
      emotionalIntensity: 0.4,
      dialogueDensity: 0.6,
      descriptiveDensity: 0.2,
      pacing: 0.7,
      formalityLevel: 0.4
    },
    patterns: {
      sentencePatterns: ['Subject verb object.', 'It was [adjective].'],
      vocabularyPatterns: ['simple', 'clear', 'direct', 'basic'],
      narrativeDevices: ['Understatement', 'Implication', 'White space'],
      commonPhrases: ['It was.', 'He said nothing.', 'The sun rose.']
    },
    tags: ['Minimalist', 'Clean', 'Direct', 'Understated'],
    popularity: 76
  },
  {
    id: 'literary-ornate',
    name: 'Literary Ornate',
    description: 'Rich, layered prose with complex metaphors and philosophical depth.',
    category: 'style',
    icon: <Sparkles className="h-5 w-5" />,
    metrics: {
      sentenceComplexity: 0.9,
      vocabularyLevel: 0.95,
      emotionalIntensity: 0.7,
      dialogueDensity: 0.3,
      descriptiveDensity: 0.85,
      pacing: 0.3,
      formalityLevel: 0.9
    },
    patterns: {
      sentencePatterns: ['Like [elaborate metaphor], the [subject] [complex verb phrase]...'],
      vocabularyPatterns: ['ephemeral', 'transcendent', 'ineffable', 'luminous'],
      narrativeDevices: ['Extended metaphors', 'Symbolism', 'Stream of consciousness'],
      commonPhrases: ['In the manner of...', 'One might say...', 'As if to suggest...']
    },
    tags: ['Complex', 'Metaphorical', 'Philosophical', 'Dense'],
    popularity: 71
  },

  // Author-Inspired Templates
  {
    id: 'king-horror',
    name: 'King-esque Horror',
    description: 'Conversational tone with building dread. Focus on ordinary people in extraordinary situations.',
    category: 'author',
    icon: <Users className="h-5 w-5" />,
    metrics: {
      sentenceComplexity: 0.6,
      vocabularyLevel: 0.6,
      emotionalIntensity: 0.8,
      dialogueDensity: 0.7,
      descriptiveDensity: 0.7,
      pacing: 0.6,
      formalityLevel: 0.3
    },
    patterns: {
      sentencePatterns: ['The thing about [ordinary object] was...', 'He/She had always been...'],
      vocabularyPatterns: ['ordinary', 'strange', 'wrong', 'crawling', 'ancient'],
      narrativeDevices: ['Backstory integration', 'Folksy observations', 'Building unease'],
      commonPhrases: ['Something was wrong', 'It started with...', 'Looking back...']
    },
    tags: ['Conversational', 'Character-focused', 'Building dread', 'Relatable'],
    popularity: 89
  },
  {
    id: 'austen-regency',
    name: 'Austen-esque Regency',
    description: 'Witty, formal prose with social commentary. Focus on manners and relationships.',
    category: 'author',
    icon: <BookOpen className="h-5 w-5" />,
    metrics: {
      sentenceComplexity: 0.8,
      vocabularyLevel: 0.8,
      emotionalIntensity: 0.5,
      dialogueDensity: 0.8,
      descriptiveDensity: 0.5,
      pacing: 0.4,
      formalityLevel: 0.95
    },
    patterns: {
      sentencePatterns: ['It is a truth universally acknowledged...', 'One must suppose that...'],
      vocabularyPatterns: ['propriety', 'society', 'manner', 'disposition', 'countenance'],
      narrativeDevices: ['Free indirect discourse', 'Social irony', 'Wit'],
      commonPhrases: ['It must be said...', 'One cannot but...', 'In such circumstances...']
    },
    tags: ['Formal', 'Witty', 'Social', 'Elegant'],
    popularity: 82
  }
]

// Combine configured templates with legacy templates
const voiceTemplates = [...configuredTemplates, ...legacyTemplates]

interface VoiceProfileTemplatesProps {
  onSelectTemplate: (template: VoiceProfileTemplate) => void
  selectedTemplateId?: string
}

export function VoiceProfileTemplates({ onSelectTemplate, selectedTemplateId }: VoiceProfileTemplatesProps) {
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'genre' | 'style' | 'author'>('all')
  const [searchQuery, setSearchQuery] = useState('')

  const filteredTemplates = voiceTemplates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    const matchesSearch = searchQuery === '' || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCategory && matchesSearch
  }).sort((a, b) => b.popularity - a.popularity)

  const categoryColors = {
    genre: 'bg-purple-500/10 text-purple-700 dark:text-purple-300',
    style: 'bg-info/10 text-blue-700 dark:text-blue-300',
    author: 'bg-success/10 text-success dark:text-green-300',
    custom: 'bg-gray-500/10 text-gray-700 dark:text-gray-300'
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-x-2">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('all')}
          >
            All
          </Button>
          <Button
            variant={selectedCategory === 'genre' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('genre')}
          >
            Genre
          </Button>
          <Button
            variant={selectedCategory === 'style' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('style')}
          >
            Style
          </Button>
          <Button
            variant={selectedCategory === 'author' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('author')}
          >
            Author-Inspired
          </Button>
        </div>
        <input
          type="text"
          placeholder="Search templates..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="px-3 py-1.5 text-sm rounded-md border bg-background"
        />
      </div>

      <ScrollArea className="h-[600px] pr-4">
        <div className="grid gap-4">
          {filteredTemplates.map((template) => (
            <Card 
              key={template.id}
              className={`cursor-pointer transition-colors ${
                selectedTemplateId === template.id 
                  ? 'ring-2 ring-primary' 
                  : 'hover:bg-accent/50'
              }`}
              onClick={() => onSelectTemplate(template)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      {template.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg flex items-center gap-2">
                        {template.name}
                        {selectedTemplateId === template.id && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </div>
                  </div>
                  <Badge className={categoryColors[template.category]}>
                    {template.category}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-1">
                    {template.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Complexity:</span>
                        <span>{Math.round(template.metrics.sentenceComplexity * 100)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Vocabulary:</span>
                        <span>{Math.round(template.metrics.vocabularyLevel * 100)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Emotion:</span>
                        <span>{Math.round(template.metrics.emotionalIntensity * 100)}%</span>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Dialogue:</span>
                        <span>{Math.round(template.metrics.dialogueDensity * 100)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Description:</span>
                        <span>{Math.round(template.metrics.descriptiveDensity * 100)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Pacing:</span>
                        <span>{Math.round(template.metrics.pacing * 100)}%</span>
                      </div>
                    </div>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    Popularity: {template.popularity}%
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

// Hook to create a voice profile from a template
export function useVoiceProfileTemplate() {
  const createFromTemplate = async (template: VoiceProfileTemplate, profileName: string) => {
    try {
      // This would call the API to create a voice profile with the template's metrics
      const response = await fetch('/api/voice-profiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: profileName,
          description: `Based on ${template.name} template`,
          metrics: template.metrics,
          patterns: template.patterns,
          template_id: template.id,
          is_active: true
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create voice profile')
      }

      const profile = await response.json()
      
      toast({
        title: 'Voice profile created',
        description: `"${profileName}" has been created from the ${template.name} template.`
      })

      return profile
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create voice profile from template',
        variant: 'destructive'
      })
      throw error
    }
  }

  return { createFromTemplate }
}