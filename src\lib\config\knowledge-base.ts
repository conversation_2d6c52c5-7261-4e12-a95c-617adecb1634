/**
 * Knowledge Base Configuration
 * Constants and settings for the knowledge base system
 */

export const K<PERSON><PERSON><PERSON>DGE_BASE_CONFIG = {
  // Entry types
  ENTRY_TYPES: {
    KNOWLEDGE_ITEM: 'knowledge_item',
    CHARACTER_PROFILE: 'character_profile',
    LOCATION_DETAIL: 'location_detail',
    PLOT_POINT: 'plot_point',
    THEME_NOTE: 'theme_note',
    WORLD_BUILDING: 'world_building',
    TIMELINE_EVENT: 'timeline_event',
    RESEARCH_NOTE: 'research_note',
  },
  
  // Item types (for knowledge items)
  ITEM_TYPES: {
    CHARACTER: 'character',
    LOCATION: 'location',
    STORY_ARC: 'story-arc',
    THEME: 'theme',
    CONFLICT: 'conflict',
    WORLD_BUILDING: 'world-building',
    TIMELINE: 'timeline',
    SETTING: 'setting',
    PLOT_DEVICE: 'plot-device',
    RESEARCH: 'research',
    NOTE: 'note',
  },
  
  // Importance levels
  IMPORTANCE_LEVELS: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
  },
  
  // Default values
  DEFAULTS: {
    TYPE: 'note',
    IMPORTANCE: 'medium',
    CATEGORY: 'general',
  },
  
  // Entry key patterns
  ENTRY_KEY_PATTERNS: {
    KNOWLEDGE_ITEM: (type: string, timestamp: number, suffix?: string) => 
      `knowledge_${type}_${timestamp}${suffix ? `_${suffix}` : ''}`,
    CHARACTER: (id: string) => `character_${id}`,
    LOCATION: (id: string) => `location_${id}`,
    PLOT_POINT: (chapter: number, index: number) => `plot_${chapter}_${index}`,
  },
  
  // UI Labels
  LABELS: {
    'character': 'Characters',
    'location': 'Locations',
    'story-arc': 'Story Arcs',
    'theme': 'Themes',
    'conflict': 'Conflicts',
    'world-building': 'World Building',
    'timeline': 'Timeline',
    'setting': 'Settings',
    'plot-device': 'Plot Devices',
    'research': 'Research',
    'note': 'General Notes',
  },
  
  // Search configuration
  SEARCH: {
    MIN_QUERY_LENGTH: 2,
    DEBOUNCE_MS: 300,
    MAX_RESULTS: 50,
  },
  
  // Validation
  VALIDATION: {
    TITLE_MAX_LENGTH: 200,
    CONTENT_MAX_LENGTH: 5000,
    TAG_MAX_LENGTH: 30,
    MAX_TAGS: 10,
    MAX_CONNECTIONS: 20,
  },
} as const;

// Type exports
export type KnowledgeItemType = typeof KNOWLEDGE_BASE_CONFIG.ITEM_TYPES[keyof typeof KNOWLEDGE_BASE_CONFIG.ITEM_TYPES];
export type EntryType = typeof KNOWLEDGE_BASE_CONFIG.ENTRY_TYPES[keyof typeof KNOWLEDGE_BASE_CONFIG.ENTRY_TYPES];
export type ImportanceLevel = typeof KNOWLEDGE_BASE_CONFIG.IMPORTANCE_LEVELS[keyof typeof KNOWLEDGE_BASE_CONFIG.IMPORTANCE_LEVELS];

// Helper functions
export function getItemTypeLabel(type: KnowledgeItemType): string {
  return KNOWLEDGE_BASE_CONFIG.LABELS[type] || type;
}

export function generateEntryKey(type: string, suffix?: string): string {
  return KNOWLEDGE_BASE_CONFIG.ENTRY_KEY_PATTERNS.KNOWLEDGE_ITEM(
    type,
    Date.now(),
    suffix || Math.random().toString(36).substr(2, 9)
  );
}