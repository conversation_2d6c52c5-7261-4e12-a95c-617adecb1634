'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Toggle } from '@/components/ui/toggle'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { Calendar, TrendingUp, TrendingDown, Minus, PenTool, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'

interface WordCountEntry {
  date: string
  word_count: number
  project_id: string
  project_title: string
}

interface WordCountTrackerProps {
  seriesId: string
}

export function WordCountTracker({ seriesId }: WordCountTrackerProps) {
  const [loading, setLoading] = useState(true)
  const [wordCounts, setWordCounts] = useState<WordCountEntry[]>([])
  const [viewMode, setViewMode] = useState<'daily' | 'weekly'>('daily')
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const { toast } = useToast()

  useEffect(() => {
    loadWordCounts()
  }, [seriesId, selectedYear])

  const loadWordCounts = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/series/${seriesId}/word-counts?year=${selectedYear}`)
      if (!response.ok) throw new Error('Failed to load word counts')
      
      const data = await response.json()
      setWordCounts(data.wordCounts || [])
    } catch (error) {
      logger.error('Error loading word counts:', error)
      toast({
        title: 'Error',
        description: 'Failed to load word count data',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const processedData = useMemo(() => {
    const startDate = new Date(selectedYear, 0, 1)
    const endDate = new Date(selectedYear, 11, 31)
    const data: Record<string, number> = {}

    if (viewMode === 'daily') {
      // Initialize all days with 0
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        data[d.toISOString().split('T')[0]] = 0
      }

      // Fill in actual word counts
      wordCounts.forEach(entry => {
        const date = entry.date.split('T')[0]
        data[date] = (data[date] || 0) + entry.word_count
      })
    } else {
      // Weekly view
      for (let week = 0; week < 52; week++) {
        data[`${selectedYear}-W${week + 1}`] = 0
      }

      wordCounts.forEach(entry => {
        const date = new Date(entry.date)
        const weekNumber = getWeekNumber(date)
        const weekKey = `${selectedYear}-W${weekNumber}`
        data[weekKey] = (data[weekKey] || 0) + entry.word_count
      })
    }

    return data
  }, [wordCounts, viewMode, selectedYear])

  const getWeekNumber = (date: Date): number => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
    const dayNum = d.getUTCDay() || 7
    d.setUTCDate(d.getUTCDate() + 4 - dayNum)
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1))
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7)
  }

  const getMonthLabel = (monthIndex: number): string => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    return months[monthIndex]
  }

  const getDayLabel = (dayIndex: number): string => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    return days[dayIndex]
  }

  const getContributionLevel = (count: number): string => {
    if (count === 0) return 'bg-muted'
    if (count < 500) return 'bg-green-200 dark:bg-green-900'
    if (count < 1000) return 'bg-green-400 dark:bg-green-700'
    if (count < 2000) return 'bg-green-600 dark:bg-green-500'
    return 'bg-green-800 dark:bg-green-300'
  }

  const calculateStats = () => {
    const values = Object.values(processedData)
    const activeDays = values.filter(v => v > 0).length
    const totalWords = values.reduce((sum, v) => sum + v, 0)
    const avgWords = activeDays > 0 ? Math.round(totalWords / activeDays) : 0
    const maxWords = Math.max(...values, 0)

    // Calculate current streak
    let currentStreak = 0
    const today = new Date()
    for (let d = new Date(today); d >= startDate; d.setDate(d.getDate() - 1)) {
      const dateKey = d.toISOString().split('T')[0]
      if (processedData[dateKey] > 0) {
        currentStreak++
      } else {
        break
      }
    }

    // Calculate longest streak
    let longestStreak = 0
    let tempStreak = 0
    const sortedDates = Object.keys(processedData).sort()
    sortedDates.forEach(date => {
      if (processedData[date] > 0) {
        tempStreak++
        longestStreak = Math.max(longestStreak, tempStreak)
      } else {
        tempStreak = 0
      }
    })

    return {
      totalWords,
      activeDays,
      avgWords,
      maxWords,
      currentStreak,
      longestStreak
    }
  }

  const stats = calculateStats()
  const startDate = new Date(selectedYear, 0, 1)

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <PenTool className="h-5 w-5" />
              Word Count Tracker
            </CardTitle>
            <CardDescription>
              Track your daily writing progress across all books in this series
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Toggle
              pressed={viewMode === 'weekly'}
              onPressedChange={(pressed) => setViewMode(pressed ? 'weekly' : 'daily')}
              size="sm"
            >
              {viewMode === 'weekly' ? 'Weekly' : 'Daily'}
            </Toggle>
            <Select value={selectedYear.toString()} onValueChange={(v) => setSelectedYear(parseInt(v))}>
              <SelectTrigger className="w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[0, 1, 2].map(offset => {
                  const year = new Date().getFullYear() - offset
                  return (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.totalWords.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Total Words</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.activeDays}</div>
            <div className="text-xs text-muted-foreground">Active Days</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.avgWords.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Avg/Day</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.maxWords.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Best Day</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.currentStreak}</div>
            <div className="text-xs text-muted-foreground">Current Streak</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.longestStreak}</div>
            <div className="text-xs text-muted-foreground">Longest Streak</div>
          </div>
        </div>

        {/* Contribution Graph */}
        <div className="overflow-x-auto">
          <TooltipProvider>
            <div className="inline-block">
              {/* Month labels */}
              <div className="flex gap-[3px] mb-1 pl-[30px]">
                {Array.from({ length: 12 }, (_, i) => {
                  const firstDay = new Date(selectedYear, i, 1).getDay()
                  return (
                    <div
                      key={i}
                      className="text-xs text-muted-foreground"
                      style={{ width: `${viewMode === 'daily' ? (new Date(selectedYear, i + 1, 0).getDate() + Math.floor(firstDay / 7)) * 13 : 52 / 12 * 13}px` }}
                    >
                      {getMonthLabel(i)}
                    </div>
                  )
                })}
              </div>

              {/* Day labels and grid */}
              <div className="flex gap-[3px]">
                {/* Day labels */}
                <div className="flex flex-col gap-[3px] pr-1">
                  <div className="h-[10px]" />
                  {[1, 3, 5].map(day => (
                    <div key={day} className="text-xs text-muted-foreground h-[10px] flex items-center">
                      {getDayLabel(day)}
                    </div>
                  ))}
                </div>

                {/* Contribution grid */}
                {viewMode === 'daily' ? (
                  <div className="flex gap-[3px]">
                    {Array.from({ length: 53 }, (_, weekIndex) => (
                      <div key={weekIndex} className="flex flex-col gap-[3px]">
                        {Array.from({ length: 7 }, (_, dayIndex) => {
                          const date = new Date(startDate)
                          date.setDate(date.getDate() + weekIndex * 7 + dayIndex - startDate.getDay())
                          
                          if (date.getFullYear() !== selectedYear) {
                            return <div key={dayIndex} className="w-[10px] h-[10px]" />
                          }

                          const dateKey = date.toISOString().split('T')[0]
                          const count = processedData[dateKey] || 0

                          return (
                            <Tooltip key={dayIndex}>
                              <TooltipTrigger asChild>
                                <div
                                  className={cn(
                                    "w-[10px] h-[10px] rounded-sm transition-colors cursor-pointer hover:ring-2 hover:ring-offset-1 hover:ring-primary",
                                    getContributionLevel(count)
                                  )}
                                />
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-sm">
                                  <div className="font-medium">{count.toLocaleString()} words</div>
                                  <div className="text-muted-foreground">{date.toLocaleDateString()}</div>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          )
                        })}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex gap-[3px]">
                    {Array.from({ length: 52 }, (_, weekIndex) => {
                      const weekKey = `${selectedYear}-W${weekIndex + 1}`
                      const count = processedData[weekKey] || 0

                      return (
                        <Tooltip key={weekIndex}>
                          <TooltipTrigger asChild>
                            <div
                              className={cn(
                                "w-[10px] h-[87px] rounded-sm transition-colors cursor-pointer hover:ring-2 hover:ring-offset-1 hover:ring-primary",
                                getContributionLevel(count)
                              )}
                            />
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-sm">
                              <div className="font-medium">{count.toLocaleString()} words</div>
                              <div className="text-muted-foreground">Week {weekIndex + 1}</div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>
          </TooltipProvider>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>Less</span>
            <div className="flex gap-1">
              <div className={cn("w-[10px] h-[10px] rounded-sm", "bg-muted")} />
              <div className={cn("w-[10px] h-[10px] rounded-sm", "bg-green-200 dark:bg-green-900")} />
              <div className={cn("w-[10px] h-[10px] rounded-sm", "bg-green-400 dark:bg-green-700")} />
              <div className={cn("w-[10px] h-[10px] rounded-sm", "bg-green-600 dark:bg-green-500")} />
              <div className={cn("w-[10px] h-[10px] rounded-sm", "bg-green-800 dark:bg-green-300")} />
            </div>
            <span>More</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}