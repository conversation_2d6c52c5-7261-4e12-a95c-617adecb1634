'use client';

import { useState, useEffect, useRef } from 'react';
import { Collaborator } from '@/lib/collaboration/collaboration-manager';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface CollaboratorCursor {
  collaborator: Collaborator;
  position: { top: number; left: number };
}

interface CollaboratorCursorsProps {
  collaborators: Collaborator[];
  editorRef?: React.RefObject<HTMLElement>;
  className?: string;
}

export function CollaboratorCursors({
  collaborators,
  editorRef,
  className,
}: CollaboratorCursorsProps) {
  const [cursors, setCursors] = useState<CollaboratorCursor[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Update cursor positions
  useEffect(() => {
    if (!editorRef?.current) return;

    const updateCursors = () => {
      const editorRect = editorRef.current?.getBoundingClientRect();
      if (!editorRect) return;

      const newCursors: CollaboratorCursor[] = collaborators
        .filter(c => c.cursor)
        .map(collaborator => {
          const { line, column } = collaborator.cursor!;
          
          // This is a simplified calculation - in a real implementation,
          // you'd need to calculate the actual position based on the editor's
          // text layout, font metrics, etc.
          const lineHeight = 24; // Approximate line height
          const charWidth = 8; // Approximate character width
          
          const top = line * lineHeight;
          const left = column * charWidth;

          return {
            collaborator,
            position: { top, left },
          };
        });

      setCursors(newCursors);
    };

    updateCursors();
    
    // Update positions when collaborators change
    const interval = setInterval(updateCursors, 100);
    return () => clearInterval(interval);
  }, [collaborators, editorRef]);

  return (
    <div
      ref={containerRef}
      className={cn('pointer-events-none absolute inset-0', className)}
    >
      <AnimatePresence>
        {cursors.map(({ collaborator, position }) => (
          <CollaboratorCursorItem
            key={collaborator.id}
            collaborator={collaborator}
            position={position}
          />
        ))}
      </AnimatePresence>

      {/* Render selections */}
      {collaborators
        .filter(c => c.selection)
        .map(collaborator => (
          <CollaboratorSelection
            key={`selection-${collaborator.id}`}
            collaborator={collaborator}
          />
        ))}
    </div>
  );
}

interface CollaboratorCursorItemProps {
  collaborator: Collaborator;
  position: { top: number; left: number };
}

function CollaboratorCursorItem({
  collaborator,
  position,
}: CollaboratorCursorItemProps) {
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    // Show tooltip briefly when cursor moves
    setShowTooltip(true);
    const timer = setTimeout(() => setShowTooltip(false), 2000);
    return () => clearTimeout(timer);
  }, [position]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.2 }}
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 50,
      }}
      className="pointer-events-none"
    >
      {/* Cursor line */}
      <div
        className="w-0.5 h-5"
        style={{ backgroundColor: collaborator.color }}
      >
        {/* Cursor flag */}
        <div
          className="absolute -top-1 left-0 h-3 px-1 text-[10px] text-white font-medium rounded-t whitespace-nowrap flex items-center"
          style={{ backgroundColor: collaborator.color }}
        >
          {collaborator.name.split(' ')[0]}
        </div>
      </div>

      {/* Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute left-0 -top-8 bg-background border rounded px-2 py-1 text-xs shadow-lg whitespace-nowrap"
          >
            {collaborator.name}
            {collaborator.status === 'idle' && (
              <span className="text-muted-foreground ml-1">(idle)</span>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

interface CollaboratorSelectionProps {
  collaborator: Collaborator;
}

function CollaboratorSelection({ collaborator }: CollaboratorSelectionProps) {
  if (!collaborator.selection) return null;

  // This is a simplified implementation - in reality, you'd need to
  // calculate the actual selection bounds based on the text layout
  const { start, end } = collaborator.selection;
  
  return (
    <div
      className="absolute pointer-events-none"
      style={{
        backgroundColor: collaborator.color,
        opacity: 0.2,
        // These would be calculated based on actual text positions
        top: start.line * 24,
        left: start.column * 8,
        width: (end.column - start.column) * 8,
        height: (end.line - start.line + 1) * 24,
      }}
    />
  );
}

// Component to show collaborator avatars
interface CollaboratorAvatarsProps {
  collaborators: Collaborator[];
  maxDisplay?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function CollaboratorAvatars({
  collaborators,
  maxDisplay = 3,
  size = 'md',
  className,
}: CollaboratorAvatarsProps) {
  const displayCollaborators = collaborators.slice(0, maxDisplay);
  const extraCount = collaborators.length - maxDisplay;

  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-10 w-10 text-base',
  };

  return (
    <div className={cn('flex -space-x-2', className)}>
      {displayCollaborators.map((collaborator) => (
        <div
          key={collaborator.id}
          className={cn(
            'relative inline-flex items-center justify-center rounded-full ring-2 ring-background',
            sizeClasses[size]
          )}
          style={{ backgroundColor: collaborator.color }}
          title={`${collaborator.name}${
            collaborator.status === 'idle' ? ' (idle)' : ''
          }`}
        >
          {collaborator.avatar ? (
            <img
              src={collaborator.avatar}
              alt={collaborator.name}
              className="h-full w-full rounded-full object-cover"
            />
          ) : (
            <span className="font-medium text-white">
              {collaborator.name.charAt(0).toUpperCase()}
            </span>
          )}
          
          {/* Status indicator */}
          <span
            className={cn(
              'absolute bottom-0 right-0 block h-2 w-2 rounded-full ring-2 ring-background',
              collaborator.status === 'active' ? 'bg-success' : 'bg-warning'
            )}
          />
        </div>
      ))}
      
      {extraCount > 0 && (
        <div
          className={cn(
            'relative inline-flex items-center justify-center rounded-full bg-muted text-muted-foreground ring-2 ring-background',
            sizeClasses[size]
          )}
        >
          <span className="font-medium">+{extraCount}</span>
        </div>
      )}
    </div>
  );
}