/**
 * Time constants in milliseconds
 */
export const TIME_MS = {
  SECOND: 1000,
  TYPING_TIMEOUT: 3000,      // 3 seconds for typing indicators
  TOAST_DURATION: 5000,      // 5 seconds for toast messages
  RETRY_DELAY_BASE: 1000,    // 1 second base retry delay
  IDLE_TIMEOUT: 60000,       // 1 minute idle timeout
  SESSION_TIMEOUT: 1800000,  // 30 minutes session timeout
  AUTOSAVE_INTERVAL: 30000,  // 30 seconds autosave
  PRESENCE_UPDATE: 5000,     // 5 seconds presence update
} as const

/**
 * Time constants in seconds
 */
export const TIME_SECONDS = {
  MINUTE: 60,
  HOUR: 3600,
  DAY: 86400,
  WEEK: 604800,
  MONTH: 2592000,
  YEAR: 31536000,
} as const

/**
 * Common durations for calculations
 */
export const DURATIONS = {
  SESSION_HOURS: 30,    // 30 hour sessions for calculations
  DEFAULT_HOURS: 24,    // 24 hours default
  WEEK_DAYS: 7,         // 7 days in a week
} as const