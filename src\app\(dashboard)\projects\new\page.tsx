'use client'

import { useSearchParams } from 'next/navigation'
import { UnifiedProjectWizard } from '@/components/wizard/unified-project-wizard'

export default function NewProjectPage() {
  const searchParams = useSearchParams()
  const isGuided = searchParams.get('guided') === 'true'

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container-full flex h-16 md:h-20 items-center justify-between">
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">Create New Project</h1>
        </div>
      </header>
      
      <main className="container-full py-4 sm:py-6 md:py-8 lg:py-10 xl:py-12">
        <UnifiedProjectWizard 
          mode="live"
          display="page"
          guided={isGuided}
        />
      </main>
    </div>
  )
}