import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { sendCollaborationInvite } from '@/lib/services/email/maileroo-email-service'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'
import { v4 as uuidv4 } from 'uuid'

// Validation schema for invite request
const inviteSchema = z.object({
  projectId: baseSchemas.uuid,
  email: baseSchemas.email,
  role: z.enum(['editor', 'viewer']),
  message: baseSchemas.description.max(500).optional()
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: inviteSchema,
    rateLimitKey: 'collaboration-invite',
    rateLimitCost: 5,
    maxBodySize: 2 * 1024, // 2KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const body = await req.json();
      const { projectId, email } = body;

      // Prevent self-invitation
      const supabase = await createTypedServerClient();
      const { data: userData } = await supabase
        .from('users')
        .select('email')
        .eq('id', user.id)
        .single();

      if (userData?.email === email) {
        return { valid: false, error: 'Cannot invite yourself to a project' };
      }

      // Verify project exists and user has permission
      const { data: project } = await supabase
        .from('projects')
        .select('user_id, status')
        .eq('id', projectId)
        .single();

      if (!project) {
        return { valid: false, error: 'Project not found' };
      }

      if (project.status === 'archived') {
        return { valid: false, error: 'Cannot invite to archived project' };
      }

      const isOwner = project.user_id === user.id;
      
      if (!isOwner) {
        // Check if user has team management permissions
        const { data: collaborator } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();
        
        if (!collaborator || collaborator.role !== 'editor') {
          return { valid: false, error: 'Only project owner or editors can invite collaborators' };
        }
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, email, role, message } = context.body;
  
  try {
    const supabase = await createTypedServerClient();
    
    // Get project details (already validated in customValidator)
    const { data: project } = await supabase
      .from('projects')
      .select('user_id, title')
      .eq('id', projectId)
      .single();
    
    if (!project) {
      return UnifiedResponse.error('Project not found', 404);
    }
    
    // Check subscription limits
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('tier')
      .eq('user_id', project.user_id)
      .eq('status', 'active')
      .single()
    
    const maxCollaborators = subscription?.tier === 'studio' ? 5 : 
                           subscription?.tier === 'professional' ? 2 : 0
    
    if (maxCollaborators === 0) {
      return handleAPIError(new AuthorizationError())
    }
    
    // Count current collaborators
    const { count } = await supabase
      .from('project_collaborators')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('status', 'active')
    
    if (count && count >= maxCollaborators) {
      logger.warn('Collaboration limit reached', {
        userId: user.id,
        projectId,
        currentCount: count,
        limit: maxCollaborators,
        tier: subscription?.tier || 'free',
        clientIP: context.clientIP
      });
      return UnifiedResponse.error(
        `You have reached the limit of ${maxCollaborators} collaborators for your plan`,
        403,
        { currentCount: count, limit: maxCollaborators, tier: subscription?.tier || 'free' }
      );
    }
    
    // Check if user is already a collaborator
    const { data: existingCollab } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single()
    
    if (existingCollab) {
      const { data: existing } = await supabase
        .from('project_collaborators')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', existingCollab.id)
        .single()
      
      if (existing) {
        return UnifiedResponse.error('User is already a collaborator on this project', 409);
      }
    }
    
    // Check if invitation already exists
    const { data: existingInvite } = await supabase
      .from('project_invitations')
      .select('id')
      .eq('project_id', projectId)
      .eq('email', email)
      .eq('status', 'pending')
      .gte('expires_at', new Date().toISOString())
      .single()
    
    if (existingInvite) {
      return UnifiedResponse.error('An active invitation already exists for this email', 409);
    }
    
    // Create invitation token
    const token = uuidv4()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days expiry
    
    // Create invitation record
    const { error: inviteError } = await supabase
      .from('project_invitations')
      .insert({
        project_id: projectId,
        email,
        role,
        token,
        inviter_id: user.id,
        status: 'pending',
        expires_at: expiresAt.toISOString(),
        message: message || null,
        metadata: {
          inviterIP: context.clientIP,
          invitedAt: new Date().toISOString()
        }
      })
    
    if (inviteError) throw inviteError
    
    // Get inviter details
    const { data: inviter } = await supabase
      .from('users')
      .select('email, profiles(display_name)')
      .eq('id', user.id)
      .single()
    
    const inviterName = inviter?.profiles?.display_name || inviter?.email || 'A BookScribe user'
    const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${token}`
    
    // Send invitation email
    await sendCollaborationInvite(
      email,
      project.title,
      inviterName,
      role,
      inviteUrl
    )
    
    logger.info('Collaboration invitation sent', {
      projectId,
      invitedEmail: email,
      role,
      inviterId: user.id,
      hasMessage: !!message,
      clientIP: context.clientIP
    })
    
    return UnifiedResponse.success({
      message: 'Invitation sent successfully',
      expiresAt: expiresAt.toISOString()
    });
    
  } catch (error) {
    logger.error('Error sending invitation', error, {
      userId: user.id,
      projectId,
      email,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to send invitation');
  }
});