import { test, expect, Page } from '@playwright/test';
import { createTestUser, deleteTestUser, TestUser } from '../helpers/test-users';
import { createTestProject, deleteTestProject, TestProject } from '../helpers/test-projects';
import { simulateSlowNetwork, simulatePacketLoss } from '../helpers/network-helpers';

describe('Conflict Resolution E2E Tests', () => {
  let user1: TestUser;
  let user2: TestUser;
  let project: TestProject;

  test.beforeAll(async () => {
    user1 = await createTestUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'User One'
    });

    user2 = await createTestUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'User Two'
    });

    project = await createTestProject({
      title: 'Conflict Resolution Test',
      description: 'Testing conflict resolution',
      ownerId: user1.id
    });

    await addCollaborator(project.id, user1.id, user2.email, 'editor');
  });

  test.afterAll(async () => {
    await deleteTestProject(project.id);
    await deleteTestUser(user1.id);
    await deleteTestUser(user2.id);
  });

  test('should detect and resolve simultaneous edits to same line', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();

    await loginUser(page1, user1);
    await loginUser(page2, user2);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);

    // Set initial content
    const initialContent = 'This is the original line that both users will edit.';
    await page1.fill('[data-testid="editor-content"]', initialContent);
    
    // Wait for sync
    await page2.waitForFunction((content) => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      return editor?.textContent === content;
    }, initialContent);

    // Simulate network delay for page2 to create conflict scenario
    await simulateSlowNetwork(context2, 2000); // 2 second delay

    // Both users start editing the same line simultaneously
    // User 1 replaces "original" with "modified"
    await page1.click('[data-testid="editor-content"]');
    await page1.keyboard.press('Control+F');
    await page1.fill('[data-testid="find-input"]', 'original');
    await page1.fill('[data-testid="replace-input"]', 'modified');
    await page1.click('[data-testid="replace-btn"]');

    // User 2 replaces "original" with "updated" (will conflict)
    await page2.click('[data-testid="editor-content"]');
    await page2.keyboard.press('Control+F');
    await page2.fill('[data-testid="find-input"]', 'original');
    await page2.fill('[data-testid="replace-input"]', 'updated');
    await page2.click('[data-testid="replace-btn"]');

    // Wait for conflict detection
    await expect(page2.locator('[data-testid="conflict-detected"]')).toBeVisible({ timeout: 5000 });

    // Conflict resolution UI should appear
    const conflictDialog = page2.locator('[data-testid="conflict-resolution-dialog"]');
    await expect(conflictDialog).toBeVisible();

    // Should show both versions
    await expect(conflictDialog.locator('[data-testid="your-version"]')).toContainText('updated');
    await expect(conflictDialog.locator('[data-testid="their-version"]')).toContainText('modified');

    // User 2 chooses to merge both changes
    await page2.click('[data-testid="merge-both-btn"]');

    // Wait for resolution
    await expect(conflictDialog).not.toBeVisible();

    // Both pages should now have merged content
    const mergedContent = await page1.textContent('[data-testid="editor-content"]');
    expect(mergedContent).toContain('modified');
    expect(mergedContent).toContain('updated');

    await context1.close();
    await context2.close();
  });

  test('should handle delete vs edit conflicts', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();

    await loginUser(page1, user1);
    await loginUser(page2, user2);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);

    // Set initial content with multiple paragraphs
    const initialContent = `Paragraph 1: This will remain unchanged.

Paragraph 2: This paragraph will cause a conflict.

Paragraph 3: This will also remain unchanged.`;

    await page1.fill('[data-testid="editor-content"]', initialContent);
    await page2.waitForFunction((content) => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      return editor?.textContent === content;
    }, initialContent);

    // User 1 deletes paragraph 2
    await page1.click('[data-testid="editor-content"]');
    await page1.keyboard.press('Control+F');
    await page1.fill('[data-testid="find-input"]', 'Paragraph 2: This paragraph will cause a conflict.');
    await page1.keyboard.press('Escape'); // Close find
    await page1.keyboard.press('Control+A'); // Select found text
    await page1.keyboard.press('Delete');

    // Simultaneously, User 2 edits paragraph 2
    await simulateSlowNetwork(context2, 1000);
    await page2.click('[data-testid="editor-content"]');
    await page2.keyboard.press('Control+F');
    await page2.fill('[data-testid="find-input"]', 'will cause a conflict');
    await page2.fill('[data-testid="replace-input"]', 'has been edited by user 2');
    await page2.click('[data-testid="replace-btn"]');

    // Conflict should be detected
    await expect(page2.locator('[data-testid="delete-edit-conflict"]')).toBeVisible({ timeout: 5000 });

    // Resolution options should be available
    const conflictOptions = page2.locator('[data-testid="conflict-options"]');
    await expect(conflictOptions.locator('[data-testid="keep-deletion-btn"]')).toBeVisible();
    await expect(conflictOptions.locator('[data-testid="keep-edit-btn"]')).toBeVisible();
    await expect(conflictOptions.locator('[data-testid="keep-both-btn"]')).toBeVisible();

    // User 2 chooses to keep their edit
    await page2.click('[data-testid="keep-edit-btn"]');

    // Verify resolution
    const finalContent = await page2.textContent('[data-testid="editor-content"]');
    expect(finalContent).toContain('Paragraph 1');
    expect(finalContent).toContain('has been edited by user 2');
    expect(finalContent).toContain('Paragraph 3');

    await context1.close();
    await context2.close();
  });

  test('should resolve conflicts in structured content (chapters/scenes)', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();

    await loginUser(page1, user1);
    await loginUser(page2, user2);

    // Navigate to chapter management
    await page1.goto(`/projects/${project.id}/chapters`);
    await page2.goto(`/projects/${project.id}/chapters`);

    // Create initial chapters
    await page1.click('[data-testid="add-chapter-btn"]');
    await page1.fill('[data-testid="chapter-title-input"]', 'Chapter 1: Introduction');
    await page1.click('[data-testid="save-chapter-btn"]');

    await page1.click('[data-testid="add-chapter-btn"]');
    await page1.fill('[data-testid="chapter-title-input"]', 'Chapter 2: Development');
    await page1.click('[data-testid="save-chapter-btn"]');

    // Wait for sync
    await page2.waitForSelector('[data-testid="chapter-item"]:nth-child(2)');

    // Both users try to reorder chapters simultaneously
    // User 1 moves Chapter 2 before Chapter 1
    const chapter2User1 = page1.locator('[data-testid="chapter-item"]:has-text("Chapter 2")');
    const chapter1User1 = page1.locator('[data-testid="chapter-item"]:has-text("Chapter 1")');
    await chapter2User1.dragTo(chapter1User1);

    // User 2 edits Chapter 2 title
    await page2.click('[data-testid="chapter-item"]:has-text("Chapter 2") [data-testid="edit-chapter-btn"]');
    await page2.fill('[data-testid="chapter-title-input"]', 'Chapter 2: The Journey');
    await page2.click('[data-testid="save-chapter-btn"]');

    // Wait for conflict resolution
    await expect(page2.locator('[data-testid="structure-conflict"]')).toBeVisible({ timeout: 5000 });

    // Should show conflict details
    const conflictInfo = page2.locator('[data-testid="conflict-info"]');
    await expect(conflictInfo).toContainText('Chapter order has been changed');
    await expect(conflictInfo).toContainText('You edited: Chapter 2');

    // Apply both changes
    await page2.click('[data-testid="apply-all-changes-btn"]');

    // Verify final state
    await page1.reload();
    await page2.reload();

    // Both should show reordered chapters with updated title
    const firstChapter = await page1.locator('[data-testid="chapter-item"]:first-child').textContent();
    expect(firstChapter).toContain('Chapter 2: The Journey');

    await context1.close();
    await context2.close();
  });

  test('should handle three-way merge conflicts', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const context3 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    const page3 = await context3.newPage();

    // Create third user
    const user3 = await createTestUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'User Three'
    });
    await addCollaborator(project.id, user1.id, user3.email, 'editor');

    await loginUser(page1, user1);
    await loginUser(page2, user2);
    await loginUser(page3, user3);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);
    await page3.goto(`/projects/${project.id}/editor`);

    // Set base content
    const baseContent = 'The quick brown fox jumps over the lazy dog.';
    await page1.fill('[data-testid="editor-content"]', baseContent);

    // Wait for sync
    await Promise.all([
      page2.waitForFunction((content) => {
        const editor = document.querySelector('[data-testid="editor-content"]');
        return editor?.textContent === content;
      }, baseContent),
      page3.waitForFunction((content) => {
        const editor = document.querySelector('[data-testid="editor-content"]');
        return editor?.textContent === content;
      }, baseContent)
    ]);

    // Simulate network delays for conflict scenario
    await simulateSlowNetwork(context2, 1500);
    await simulateSlowNetwork(context3, 2000);

    // Three different edits to the same sentence
    // User 1: changes "quick" to "fast"
    await page1.click('[data-testid="editor-content"]');
    const content1 = baseContent.replace('quick', 'fast');
    await page1.fill('[data-testid="editor-content"]', content1);

    // User 2: changes "brown" to "red"
    await page2.click('[data-testid="editor-content"]');
    const content2 = baseContent.replace('brown', 'red');
    await page2.fill('[data-testid="editor-content"]', content2);

    // User 3: changes "lazy" to "sleepy"
    await page3.click('[data-testid="editor-content"]');
    const content3 = baseContent.replace('lazy', 'sleepy');
    await page3.fill('[data-testid="editor-content"]', content3);

    // Wait for three-way conflict detection
    await expect(page3.locator('[data-testid="three-way-conflict"]')).toBeVisible({ timeout: 5000 });

    // Should show all three versions
    const conflictView = page3.locator('[data-testid="three-way-merge-view"]');
    await expect(conflictView.locator('[data-testid="version-1"]')).toContainText('fast');
    await expect(conflictView.locator('[data-testid="version-2"]')).toContainText('red');
    await expect(conflictView.locator('[data-testid="version-3"]')).toContainText('sleepy');

    // User 3 accepts merged version
    await page3.click('[data-testid="accept-merged-btn"]');

    // All pages should eventually have the merged content
    const expectedMerged = 'The fast red fox jumps over the sleepy dog.';
    
    await Promise.all([
      page1.waitForFunction((content) => {
        const editor = document.querySelector('[data-testid="editor-content"]');
        return editor?.textContent === content;
      }, expectedMerged),
      page2.waitForFunction((content) => {
        const editor = document.querySelector('[data-testid="editor-content"]');
        return editor?.textContent === content;
      }, expectedMerged)
    ]);

    await deleteTestUser(user3.id);
    await context1.close();
    await context2.close();
    await context3.close();
  });

  test('should track conflict resolution history', async ({ browser }) => {
    const context1 = await browser.newContext();
    const page1 = await context1.newPage();

    await loginUser(page1, user1);
    await page1.goto(`/projects/${project.id}/activity`);

    // Filter for conflict resolutions
    await page1.click('[data-testid="activity-filter-btn"]');
    await page1.click('[data-testid="filter-conflicts"]');

    // Should show recent conflict resolutions
    const conflictItems = page1.locator('[data-testid="conflict-resolution-item"]');
    await expect(conflictItems).toHaveCount(3, { timeout: 5000 }); // From previous tests

    // Check details of a conflict resolution
    const firstConflict = conflictItems.first();
    await expect(firstConflict).toContainText('Conflict resolved');
    await expect(firstConflict).toContainText('User One');
    await expect(firstConflict).toContainText('User Two');

    // Click to see details
    await firstConflict.click('[data-testid="view-details-btn"]');

    const detailsModal = page1.locator('[data-testid="conflict-details-modal"]');
    await expect(detailsModal).toBeVisible();
    await expect(detailsModal).toContainText('Original content');
    await expect(detailsModal).toContainText('Conflicting changes');
    await expect(detailsModal).toContainText('Resolution method');
    await expect(detailsModal).toContainText('Final result');

    await context1.close();
  });

  test('should provide conflict prevention warnings', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();

    await loginUser(page1, user1);
    await loginUser(page2, user2);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);

    // Set content
    await page1.fill('[data-testid="editor-content"]', 'This is a test paragraph for conflict prevention.');
    await page2.waitForFunction((content) => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      return editor?.textContent === content;
    }, 'This is a test paragraph for conflict prevention.');

    // User 1 starts editing
    await page1.click('[data-testid="editor-content"]');
    await page1.keyboard.press('Control+A');

    // User 2 should see editing indicator
    await expect(page2.locator('[data-testid="user-editing-warning"]')).toBeVisible();
    await expect(page2.locator('[data-testid="user-editing-warning"]')).toContainText('User One is editing this section');

    // If User 2 tries to edit the same area
    await page2.click('[data-testid="editor-content"]');
    
    // Should show conflict prevention dialog
    const preventionDialog = page2.locator('[data-testid="conflict-prevention-dialog"]');
    await expect(preventionDialog).toBeVisible();
    await expect(preventionDialog).toContainText('User One is currently editing this area');
    await expect(preventionDialog).toContainText('Editing now may cause conflicts');

    // Options should be available
    await expect(preventionDialog.locator('[data-testid="wait-btn"]')).toBeVisible();
    await expect(preventionDialog.locator('[data-testid="edit-anyway-btn"]')).toBeVisible();
    await expect(preventionDialog.locator('[data-testid="edit-elsewhere-btn"]')).toBeVisible();

    await context1.close();
    await context2.close();
  });
});

// Helper functions
async function loginUser(page: Page, user: TestUser) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('[data-testid="login-btn"]');
  await page.waitForURL('/dashboard');
}

async function addCollaborator(projectId: string, ownerId: string, collaboratorEmail: string, role: string) {
  console.log(`Adding ${collaboratorEmail} as ${role} to project ${projectId}`);
}