import type { Config } from "tailwindcss"

const config = {
  darkMode: "class",
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // Literary theme colors
        literary: {
          gold: "hsl(var(--literary-gold))",
          amber: "hsl(var(--literary-amber))",
          parchment: "hsl(var(--literary-parchment))",
          ink: "hsl(var(--literary-ink))",
          quill: "hsl(var(--literary-quill))",
        },
        // Status colors
        status: {
          completed: "hsl(var(--status-completed))",
          'in-progress': "hsl(var(--status-in-progress))",
          draft: "hsl(var(--status-draft))",
          published: "hsl(var(--status-published))",
          default: "hsl(var(--status-default))",
        },
        // Warm color palette for Writer's Sanctuary
        warm: {
          50: "hsl(45, 50%, 97%)",
          100: "hsl(42, 45%, 95%)",
          200: "hsl(40, 35%, 88%)",
          300: "hsl(40, 30%, 80%)",
          400: "hsl(35, 35%, 65%)",
          500: "hsl(30, 45%, 50%)",
          600: "hsl(25, 55%, 40%)",
          700: "hsl(25, 65%, 30%)",
          800: "hsl(25, 75%, 20%)",
          900: "hsl(25, 85%, 15%)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        xl: "calc(var(--radius) + 4px)",
        "2xl": "calc(var(--radius) + 8px)",
      },
      fontFamily: {
        'serif': ['Crimson Text', 'Georgia', 'serif'],  // Primary reading font
        'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'sans-serif'],  // UI font
        'mono': ['JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'monospace'],  // Code/editor font
        // Aliases for semantic usage
        'literary': ['Crimson Text', 'Georgia', 'serif'],  // Alias for backwards compatibility
        'reading': ['Crimson Text', 'Georgia', 'serif'],  // For main content
        'ui': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'sans-serif'],  // For interface
        'code': ['JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'monospace'],  // For code blocks
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        gradient: {
          "0%, 100%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
        },
        shimmer: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-10px)" },
        },
        pulse: {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: ".5" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        gradient: "gradient 8s ease infinite",
        shimmer: "shimmer 2s linear infinite",
        float: "float 3s ease-in-out infinite",
        pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config