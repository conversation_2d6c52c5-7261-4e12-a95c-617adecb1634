# =========================================
# BookScribe AI Environment Configuration
# =========================================
# Copy this file to .env.local and fill in your values
# Only essential variables are included here

# ==========================================
# Required Configuration
# ==========================================

# Supabase (Database & Auth) - REQUIRED
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI (AI Features) - REQUIRED
OPENAI_API_KEY=sk-your_openai_api_key

# ==========================================
# Optional Services
# ==========================================

# Stripe (Payments) - Optional
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Alternative AI Providers - Optional
GEMINI_API_KEY=your_google_gemini_key
XAI_API_KEY=your_xai_api_key

# Email Service - Optional (Maileroo)
MAILEROO_API_KEY=your_maileroo_api_key
MAILEROO_API_URL=https://api.maileroo.com/v1
MAILEROO_FROM_EMAIL=<EMAIL>
MAILEROO_FROM_NAME=BookScribe AI
# Legacy SendGrid (being phased out)
# SENDGRID_API_KEY=SG.your_sendgrid_api_key
EMAIL_FROM_ADDRESS=<EMAIL>
TEST_EMAIL_ADDRESS=<EMAIL>

# Cron Jobs - Optional
CRON_SECRET=your_cron_secret_for_workers

# Error Monitoring - Optional
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development

# ==========================================
# Development Options
# ==========================================

# Development Features - Optional
NEXT_PUBLIC_DEMO_MODE=false
NEXT_PUBLIC_DEV_BYPASS_AUTH=false

# Application URLs (defaults work for local dev)
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api