'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  MapPin, 
  Clock, 
  Navigation,
  ArrowRight,
  Map,
  Edit3,
  Home,
  Building,
  Trees,
  Mountain
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { SearchResult } from '../content-search-interface'

interface LocationPreviewProps {
  result: SearchResult
  onNavigate: () => void
  onEdit?: () => void
  className?: string
}

export function LocationPreview({ 
  result, 
  onNavigate, 
  onEdit,
  className 
}: LocationPreviewProps) {
  const locationType = result.metadata.locationType || 'other'
  
  const locationIcons = {
    city: Building,
    building: Home,
    region: Map,
    nature: Trees,
    landmark: Mountain,
    other: MapPin
  }

  const locationColors = {
    city: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
    building: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    region: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    nature: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300',
    landmark: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    other: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
  }

  const Icon = locationIcons[locationType as keyof typeof locationIcons] || MapPin

  return (
    <Card className={cn("hover:shadow-lg transition-shadow", className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              "p-2 rounded-lg",
              locationColors[locationType as keyof typeof locationColors]
            )}>
              <Icon className="w-5 h-5" />
            </div>
            <div>
              <CardTitle className="text-lg">{result.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                <Badge 
                  variant="outline" 
                  className="capitalize text-xs"
                >
                  {locationType}
                </Badge>
              </div>
            </div>
          </div>
          <Badge variant="secondary">Location</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Description */}
        <div className="prose prose-sm dark:prose-invert max-w-none">
          <p className="line-clamp-3 text-muted-foreground">
            {result.excerpt}
          </p>
        </div>

        {/* Map Preview (Placeholder) */}
        <div className="relative h-32 bg-muted/30 rounded-lg overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <Map className="w-8 h-8 text-muted-foreground/50 mx-auto mb-2" />
              <p className="text-xs text-muted-foreground">Map Preview</p>
            </div>
          </div>
          <div className="absolute top-2 right-2">
            <Badge variant="secondary" className="text-xs">
              Interactive
            </Badge>
          </div>
        </div>

        {/* Metadata */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span>Updated {new Date(result.metadata.lastModified).toLocaleDateString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <Navigation className="w-3 h-3" />
            <span>{Math.floor(Math.random() * 10) + 1} scenes</span>
          </div>
        </div>

        {/* Highlights */}
        {result.highlights.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Matching descriptions:</p>
            <div className="space-y-1">
              {result.highlights.slice(0, 2).map((highlight, idx) => (
                <div
                  key={idx}
                  className="text-sm p-2 bg-muted/50 rounded"
                  dangerouslySetInnerHTML={{ __html: highlight }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button 
            variant="default" 
            size="sm" 
            onClick={onNavigate}
            className="flex-1"
          >
            <MapPin className="w-4 h-4 mr-2" />
            View Location
          </Button>
          {onEdit && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onEdit}
              className="flex-1"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}