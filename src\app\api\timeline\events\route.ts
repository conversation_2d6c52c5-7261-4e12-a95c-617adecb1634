import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server';
import { createTypedServerClient } from '@/lib/supabase';
import { getTimelineValidator } from '@/lib/timeline/timeline-instances';
import { withProjectAccess } from '@/lib/api/auth-helpers';
import { logger } from '@/lib/services/logger'

export const GET = withProjectAccess(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    const validator = getTimelineValidator(projectId);
    
    // Get all events from database
    const supabase = await createTypedServerClient();
    const { data: events, error } = await supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId)
      .eq('entry_type', 'timeline_event')
      .order('chapter_introduced', { ascending: true });

    if (error) throw error;
    
    // Validate timeline
    const validation = await validator.validateTimeline();
    
    return NextResponse.json({
      success: true,
      events,
      validation
    });

  } catch (error) {
    logger.error('Error getting timeline events:', error);
    return NextResponse.json(
      { error: 'Failed to get timeline events' },
      { status: 500 }
    );
  }
}, 'viewer')

export const POST = withProjectAccess(async (request: NextRequest) => {
  try {
    const { projectId, chapterContent, chapterNumber } = await request.json();
    
    if (!projectId || !chapterContent || !chapterNumber) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    const validator = getTimelineValidator(projectId);
    
    // Extract timeline events from chapter content
    const extractedEvents = await validator.extractTimelineFromChapter(
      chapterContent,
      chapterNumber
    );

    // Add events to timeline
    const eventIds = [];
    for (const event of extractedEvents) {
      const eventId = await validator.addEvent(event);
      eventIds.push(eventId);
    }

    // Validate updated timeline
    const validation = await validator.validateTimeline();
    
    return NextResponse.json({
      success: true,
      extractedEvents: eventIds.length,
      validation
    });

  } catch (error) {
    logger.error('Error adding timeline events:', error);
    return NextResponse.json(
      { error: 'Failed to add timeline events' },
      { status: 500 }
    );
  }
}, 'editor')

