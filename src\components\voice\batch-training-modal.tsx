'use client'

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useToast } from '@/hooks/use-toast'
import {
  BookOpen,
  MessageSquare,
  FileText,
  Database,
  Loader2,
  CheckCircle2,
  Search,
  AlertCircle
} from 'lucide-react'

interface Chapter {
  id: string
  chapter_number: number
  title: string
  actual_word_count: number
  status: string
}

interface StoryBibleEntry {
  id: string
  category: string
  title: string
  content: string
}

interface BatchTrainingModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  projectId: string
  profileId: string
  profileType: 'author' | 'character' | 'narrator'
  onTrainingComplete?: () => void
}

export function BatchTrainingModal({
  open,
  onOpenChange,
  projectId,
  profileId,
  profileType,
  onTrainingComplete
}: BatchTrainingModalProps) {
  const [activeTab, setActiveTab] = useState('chapters')
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [storyBibleEntries, setStoryBibleEntries] = useState<StoryBibleEntry[]>([])
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [isTraining, setIsTraining] = useState(false)
  const [characterName, setCharacterName] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const { toast } = useToast()

  useEffect(() => {
    if (open) {
      loadContent()
    }
  }, [open, projectId])

  const loadContent = async () => {
    setIsLoading(true)
    try {
      // Load chapters
      const chaptersResponse = await fetch(`/api/projects/${projectId}/chapters`)
      if (chaptersResponse.ok) {
        const data = await chaptersResponse.json()
        setChapters(data.chapters || [])
      }

      // Load story bible entries
      const storyBibleResponse = await fetch(`/api/projects/${projectId}/story-bible`)
      if (storyBibleResponse.ok) {
        const data = await storyBibleResponse.json()
        setStoryBibleEntries(data.entries || [])
      }
    } catch (error) {
      logger.error('Error loading content:', error)
      toast({ title: 'Error', description: 'Failed to load project content', variant: 'destructive' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSelectAll = (type: string) => {
    const items = type === 'chapters' ? chapters : storyBibleEntries
    const newSelected = new Set(selectedItems)
    
    items.forEach(item => {
      newSelected.add(`${type}:${item.id}`)
    })
    
    setSelectedItems(newSelected)
  }

  const handleDeselectAll = (type: string) => {
    const newSelected = new Set(
      Array.from(selectedItems).filter(id => !id.startsWith(`${type}:`))
    )
    setSelectedItems(newSelected)
  }

  const handleItemToggle = (itemId: string, type: string) => {
    const fullId = `${type}:${itemId}`
    const newSelected = new Set(selectedItems)
    
    if (newSelected.has(fullId)) {
      newSelected.delete(fullId)
    } else {
      newSelected.add(fullId)
    }
    
    setSelectedItems(newSelected)
  }

  const handleTrain = async () => {
    if (selectedItems.size === 0) {
      toast({ title: 'Error', description: 'Please select at least one item to train from', variant: 'destructive' })
      return
    }

    setIsTraining(true)
    
    try {
      // Group selected items by type
      const groupedItems: Record<string, string[]> = {}
      
      selectedItems.forEach(item => {
        const [type, id] = item.split(':')
        if (!groupedItems[type]) {
          groupedItems[type] = []
        }
        groupedItems[type].push(id)
      })

      // Train from each content type
      for (const [type, ids] of Object.entries(groupedItems)) {
        let contentType = type
        
        if (type === 'chapters' && profileType === 'character' && characterName) {
          contentType = 'character_dialogue'
        } else if (type === 'storyBible') {
          contentType = 'story_bible'
        }

        const response = await fetch(`/api/voice-profiles/${profileId}/train-from-content`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contentType,
            contentIds: ids,
            characterName: profileType === 'character' ? characterName : undefined
          })
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Training failed')
        }

        const result = await response.json()
        toast({ title: 'Success', description: `Trained with ${result.trainedSamples} samples from ${type}` })
      }

      if (onTrainingComplete) {
        onTrainingComplete()
      }
      
      onOpenChange(false)
      setSelectedItems(new Set())
    } catch (error) {
      logger.error('Training error:', error)
      toast({ title: 'Error', description: error instanceof Error ? error.message : 'Failed to train voice profile', variant: 'destructive' })
    } finally {
      setIsTraining(false)
    }
  }

  const filteredChapters = chapters.filter(ch => 
    !searchQuery || 
    ch.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    `Chapter ${ch.chapter_number}`.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const filteredStoryBible = storyBibleEntries.filter(entry =>
    !searchQuery ||
    entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    entry.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Train from Project Content</DialogTitle>
          <DialogDescription>
            Select chapters or story bible entries to train your voice profile
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {profileType === 'character' && (
            <div>
              <Label htmlFor="characterName">Character Name (for dialogue extraction)</Label>
              <Input
                id="characterName"
                value={characterName}
                onChange={(e) => setCharacterName(e.target.value)}
                placeholder="Enter character name as it appears in the text"
                className="mt-1"
              />
            </div>
          )}

          <div>
            <Input
              placeholder="Search content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
              prefix={<Search className="h-4 w-4" />}
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-6 sm:py-8 lg:py-10">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="chapters">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Chapters ({chapters.length})
                </TabsTrigger>
                <TabsTrigger value="storyBible">
                  <FileText className="h-4 w-4 mr-2" />
                  Story Bible ({storyBibleEntries.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="chapters" className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {profileType === 'character' 
                      ? 'Select chapters to extract dialogue from'
                      : 'Select chapters to train from their content'
                    }
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleSelectAll('chapters')}
                    >
                      Select All
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeselectAll('chapters')}
                    >
                      Deselect All
                    </Button>
                  </div>
                </div>

                <ScrollArea className="h-[300px] border rounded-lg p-4">
                  <div className="space-y-2">
                    {filteredChapters.map((chapter) => {
                      const isSelected = selectedItems.has(`chapters:${chapter.id}`)
                      
                      return (
                        <div
                          key={chapter.id}
                          className="flex items-center space-x-3 py-2 px-3 rounded-lg hover:bg-muted/50 transition-colors"
                        >
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => handleItemToggle(chapter.id, 'chapters')}
                          />
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                Chapter {chapter.chapter_number}
                              </span>
                              {chapter.title && (
                                <span className="text-muted-foreground">
                                  - {chapter.title}
                                </span>
                              )}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {chapter.actual_word_count.toLocaleString()} words
                              </Badge>
                              <Badge 
                                variant={chapter.status === 'complete' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {chapter.status}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </ScrollArea>

                {profileType === 'character' && characterName && (
                  <Alert>
                    <MessageSquare className="h-4 w-4" />
                    <AlertDescription>
                      Will extract dialogue spoken by "{characterName}" from selected chapters
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              <TabsContent value="storyBible" className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    Select story bible entries to include in training
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleSelectAll('storyBible')}
                    >
                      Select All
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeselectAll('storyBible')}
                    >
                      Deselect All
                    </Button>
                  </div>
                </div>

                <ScrollArea className="h-[300px] border rounded-lg p-4">
                  <div className="space-y-2">
                    {filteredStoryBible.map((entry) => {
                      const isSelected = selectedItems.has(`storyBible:${entry.id}`)
                      
                      return (
                        <div
                          key={entry.id}
                          className="flex items-center space-x-3 py-2 px-3 rounded-lg hover:bg-muted/50 transition-colors"
                        >
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => handleItemToggle(entry.id, 'storyBible')}
                          />
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{entry.title}</span>
                              <Badge variant="outline" className="text-xs">
                                {entry.category}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                              {entry.content}
                            </p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          )}

          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {selectedItems.size} items selected
              </span>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isTraining}
              >
                Cancel
              </Button>
              <Button
                onClick={handleTrain}
                disabled={isTraining || selectedItems.size === 0}
              >
                {isTraining ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Training...
                  </>
                ) : (
                  <>
                    <Database className="h-4 w-4 mr-2" />
                    Train from Selected
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}