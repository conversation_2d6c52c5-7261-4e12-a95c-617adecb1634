/**
 * UI dimension constants
 */
export const UI_DIMENSIONS = {
  PANEL_MIN_WIDTH_RATIO: 0.15, // 15% of viewport (was 20%)
  PANEL_MAX_WIDTH_RATIO: 0.8, // 80% of viewport (was 60%)
  MO<PERSON>LE_PANEL_MIN_WIDTH: 280,
  TABLET_PANEL_MIN_WIDTH: 320,
  DESKTOP_PANEL_MIN_WIDTH: 360,
  MAX_FILE_SIZE_LINES: 700,
  // Container widths
  CONTAINER_MAX_WIDTH: 2200, // px
  CONTAINER_WIDE_MAX_WIDTH: 2560, // px
  CONTAINER_FULL_PADDING_MIN: 16, // px (1rem)
  CONTAINER_FULL_PADDING_MAX: 64, // px (4rem)
} as const

/**
 * UI state opacity values
 */
export const UI_OPACITY = {
  DISABLED: 0.3,
  INACTIVE: 0.5,
  HOVER: 0.7,
  ACTIVE: 1,
} as const

/**
 * Celebration/confetti physics constants
 */
export const CELEBRATION_PHYSICS = {
  GRAVITY: 0.5,
  DECAY_NORMAL: 0.94,
  DECAY_SLOW: 0.91,
  DECAY_FAST: 0.92,
  SCALAR_SMALL: 0.75,
  SCALAR_NORMAL: 0.8,
  SCALAR_LARGE: 1.2,
  DRIFT: 0.5,
  ORIGIN_Y: 0.7,
} as const

/**
 * Priority values for sitemap
 */
export const SITEMAP_PRIORITY = {
  HOMEPAGE: 1,
  IMPORTANT_PAGE: 0.8,
  STANDARD_PAGE: 0.5,
} as const

/**
 * Line height values
 */
export const LINE_HEIGHT = {
  TIGHT: 1.2,
  NORMAL: 1.5,
  RELAXED: 1.8,
} as const