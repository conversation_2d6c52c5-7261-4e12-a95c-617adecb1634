const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeIndexes() {
  console.log('🔍 Analyzing database indexes...\n');

  try {
    // Query to get all indexes
    const { data: indexes, error } = await supabase.rpc('get_all_indexes');
    
    if (error) {
      // Fallback to direct SQL query
      const query = `
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes
        WHERE schemaname = 'public'
        ORDER BY tablename, indexname;
      `;
      
      const { data, error: queryError } = await supabase.rpc('exec_sql', { query });
      
      if (queryError) {
        console.error('Error fetching indexes:', queryError);
        return;
      }
      
      analyzeResults(data);
    } else {
      analyzeResults(indexes);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

function analyzeResults(indexes) {
  // Group indexes by table
  const indexesByTable = indexes.reduce((acc, idx) => {
    const table = idx.tablename;
    if (!acc[table]) acc[table] = [];
    acc[table].push({
      name: idx.indexname,
      definition: idx.indexdef
    });
    return acc;
  }, {});

  // Tables we're interested in
  const targetTables = [
    'voice_profiles',
    'voice_consistency_checks',
    'locations',
    'story_bible',
    'story_bibles',
    'writing_sessions',
    'writing_goals',
    'writing_goal_progress',
    'projects',
    'chapters',
    'characters'
  ];

  // Check each table
  const report = {
    existingIndexes: {},
    missingTables: [],
    recommendations: []
  };

  for (const table of targetTables) {
    if (indexesByTable[table]) {
      report.existingIndexes[table] = indexesByTable[table];
      console.log(`\n📊 Table: ${table}`);
      console.log(`   Found ${indexesByTable[table].length} indexes:`);
      indexesByTable[table].forEach(idx => {
        console.log(`   - ${idx.name}`);
      });
    } else {
      report.missingTables.push(table);
      console.log(`\n❌ Table not found: ${table}`);
    }
  }

  // Analyze missing indexes based on common query patterns
  console.log('\n\n📋 Index Recommendations:\n');

  // Voice profiles recommendations
  if (indexesByTable['voice_profiles']) {
    const vpIndexes = indexesByTable['voice_profiles'].map(i => i.name);
    if (!vpIndexes.some(i => i.includes('user_id'))) {
      report.recommendations.push('CREATE INDEX idx_voice_profiles_user_id ON voice_profiles(user_id);');
    }
    if (!vpIndexes.some(i => i.includes('project_id'))) {
      report.recommendations.push('CREATE INDEX idx_voice_profiles_project_id ON voice_profiles(project_id);');
    }
  }

  // Locations recommendations
  if (indexesByTable['locations']) {
    const locIndexes = indexesByTable['locations'].map(i => i.name);
    if (!locIndexes.some(i => i.includes('project_id'))) {
      report.recommendations.push('CREATE INDEX idx_locations_project_id ON locations(project_id);');
    }
    if (!locIndexes.some(i => i.includes('parent_id'))) {
      report.recommendations.push('CREATE INDEX idx_locations_parent_id ON locations(parent_id);');
    }
  }

  // Story bible recommendations
  if (indexesByTable['story_bible']) {
    const sbIndexes = indexesByTable['story_bible'].map(i => i.name);
    if (!sbIndexes.some(i => i.includes('project_id'))) {
      report.recommendations.push('CREATE INDEX idx_story_bible_project_id ON story_bible(project_id);');
    }
    if (!sbIndexes.some(i => i.includes('entry_type'))) {
      report.recommendations.push('CREATE INDEX idx_story_bible_entry_type ON story_bible(entry_type);');
    }
  }

  if (report.recommendations.length > 0) {
    console.log('Recommended indexes to add:');
    report.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });
  } else {
    console.log('✅ All recommended indexes are already in place!');
  }

  // Save report
  const reportPath = path.join(__dirname, 'index-analysis-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 Full report saved to: ${reportPath}`);

  // Check for query performance issues
  console.log('\n\n🚀 Query Performance Analysis:\n');
  analyzePotentialSlowQueries(indexesByTable);
}

function analyzePotentialSlowQueries(indexesByTable) {
  const slowQueryPatterns = [
    {
      description: 'Voice profiles filtered by user and project',
      tables: ['voice_profiles'],
      requiredIndexes: ['user_id', 'project_id'],
      recommendation: 'Consider composite index on (user_id, project_id)'
    },
    {
      description: 'Voice consistency checks ordered by date',
      tables: ['voice_consistency_checks'],
      requiredIndexes: ['created_at'],
      recommendation: 'Add index on created_at DESC for time-based queries'
    },
    {
      description: 'Locations hierarchy traversal',
      tables: ['locations'],
      requiredIndexes: ['parent_id', 'project_id'],
      recommendation: 'Composite index on (project_id, parent_id) for tree queries'
    },
    {
      description: 'Story bible entries by type',
      tables: ['story_bible'],
      requiredIndexes: ['project_id', 'entry_type'],
      recommendation: 'Composite index on (project_id, entry_type)'
    },
    {
      description: 'Writing sessions analytics',
      tables: ['writing_sessions'],
      requiredIndexes: ['user_id', 'started_at'],
      recommendation: 'Index on (user_id, started_at DESC) for user activity queries'
    }
  ];

  slowQueryPatterns.forEach(pattern => {
    console.log(`\n🔍 ${pattern.description}`);
    const allIndexesExist = pattern.tables.every(table => {
      if (!indexesByTable[table]) return false;
      const indexes = indexesByTable[table].map(i => i.name.toLowerCase());
      return pattern.requiredIndexes.every(reqIdx => 
        indexes.some(idx => idx.includes(reqIdx.toLowerCase()))
      );
    });

    if (allIndexesExist) {
      console.log('   ✅ Indexes exist');
    } else {
      console.log('   ⚠️  Missing indexes - ' + pattern.recommendation);
    }
  });
}

// Execute the analysis
analyzeIndexes();