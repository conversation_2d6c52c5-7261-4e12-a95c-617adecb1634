import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { useCelebration } from '@/contexts/celebration-context';
import { createClient } from '@/lib/supabase/client';
import { logger } from '@/lib/services/logger';
import { FormData, initialFormData, demoDataSets } from './wizard-config';

interface UseWizardLogicProps {
  mode?: 'live' | 'demo';
  demoTheme?: keyof typeof demoDataSets;
  onComplete?: (projectId: string) => void;
}

export function useWizardLogic({ mode = 'live', demoTheme = 'fantasy', onComplete }: UseWizardLogicProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const { celebrate } = useCelebration();
  const supabase = createClient();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [universes, setUniverses] = useState<Array<{ id: string; name: string; description?: string }>>([]);
  const [series, setSeries] = useState<Array<{ id: string; title: string; universe_id?: string }>>([]);
  const [isLoadingUniverses, setIsLoadingUniverses] = useState(false);
  const [isLoadingSeries, setIsLoadingSeries] = useState(false);
  
  // Initialize form data based on mode
  const [formData, setFormData] = useState<FormData>(() => {
    if (mode === 'demo') {
      return demoDataSets[demoTheme];
    }
    return initialFormData;
  });

  const updateFormData = useCallback((field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const canProceed = useCallback(() => {
    const currentStepId = ['basics', 'genre', 'structure', 'characters', 'themes', 'technical', 'payment'][currentStep];
    
    switch (currentStepId) {
      case 'basics':
        return formData.title.trim() !== '' && formData.description.trim() !== '';
      case 'genre':
        return formData.genre !== '' && formData.tone !== '';
      case 'structure':
        return formData.structure !== '' && formData.pacing !== '';
      case 'characters':
        return formData.protagonist.trim() !== '' && formData.setting.trim() !== '';
      case 'themes':
        return formData.themes.trim() !== '';
      default:
        return true;
    }
  }, [currentStep, formData]);

  const nextStep = useCallback(() => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep]);

  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const loadUniverses = async () => {
    setIsLoadingUniverses(true);
    try {
      const response = await fetch('/api/universes');
      if (response.ok) {
        const data = await response.json();
        setUniverses(data.universes || []);
      }
    } catch (error) {
      logger.error('Failed to load universes:', error);
    } finally {
      setIsLoadingUniverses(false);
    }
  };

  const loadSeries = async () => {
    setIsLoadingSeries(true);
    try {
      const response = await fetch('/api/series');
      if (response.ok) {
        const data = await response.json();
        setSeries(data.series || []);
      }
    } catch (error) {
      logger.error('Failed to load series:', error);
    } finally {
      setIsLoadingSeries(false);
    }
  };

  useEffect(() => {
    if (mode === 'live' && user) {
      loadUniverses();
      loadSeries();
    }
  }, [mode, user]);

  const handleGenerate = async () => {
    setIsGenerating(true);
    
    try {
      // Prepare the data for submission
      const projectData = {
        title: formData.title,
        description: formData.description,
        settings: {
          genre: formData.genre,
          subgenre: formData.subgenre,
          tone: formData.tone,
          targetAudience: formData.targetAudience,
          protagonist: formData.protagonist,
          antagonist: formData.antagonist,
          setting: formData.setting,
          themes: formData.themes.split(',').map(t => t.trim()),
          contentWarnings: formData.triggerWarnings?.split(',').map(t => t.trim()) || [],
          targetWordCount: parseInt(formData.wordCount),
          targetChapterCount: parseInt(formData.chapters),
          chapterStructure: formData.structure,
          pacing: formData.pacing,
          narrativeStyle: {
            voice: formData.narrativeVoice,
            tense: formData.tense,
            povCharacters: formData.povCharacters?.split(',').map(t => t.trim())
          },
          worldbuilding: {
            timePeriod: formData.timePeriod,
            universe_id: formData.universeId !== 'none' ? formData.universeId : null
          }
        },
        series_id: formData.seriesId !== 'none' ? formData.seriesId : null,
        status: mode === 'demo' ? 'demo' : 'planning'
      };

      // Create the project
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(projectData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create project');
      }

      const projectId = data.project.id;

      // For demo mode, we're done
      if (mode === 'demo') {
        celebrate({
          title: "Demo Project Created! 🎉",
          message: "Your demo project is ready. Explore BookScribe's features!"
        });
        
        if (onComplete) {
          onComplete(projectId);
        } else {
          router.push(`/projects/${projectId}/overview`);
        }
        return;
      }

      // For live mode, initiate payment process
      const paymentResponse = await fetch('/api/billing/payments/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          projectId,
          productType: 'project',
          successUrl: `${window.location.origin}/projects/${projectId}/overview?payment=success`,
          cancelUrl: `${window.location.origin}/projects?payment=cancelled`
        })
      });

      const paymentData = await paymentResponse.json();

      if (!paymentResponse.ok) {
        throw new Error(paymentData.error || 'Failed to create payment session');
      }

      // Redirect to Stripe checkout
      window.location.href = paymentData.url;

    } catch (error) {
      logger.error('Failed to create project:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create project. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    currentStep,
    setCurrentStep,
    formData,
    updateFormData,
    isGenerating,
    canProceed,
    nextStep,
    prevStep,
    handleGenerate,
    universes,
    series,
    isLoadingUniverses,
    isLoadingSeries
  };
}