# BookScribe Database Schema Reference

## Table of Contents
- [Overview](#overview)
- [Database Architecture](#database-architecture)
- [Core Tables](#core-tables)
- [Relationship Diagrams](#relationship-diagrams)
- [Table Specifications](#table-specifications)
- [Indexes and Performance](#indexes-and-performance)
- [Row-Level Security](#row-level-security)
- [Migration Strategy](#migration-strategy)

## Overview

BookScribe uses PostgreSQL via Supabase as its primary database. The schema is designed to support:
- Multi-project novel writing
- Real-time collaboration
- AI-powered content generation
- Comprehensive analytics
- Series and universe management
- Version control for content

## Database Architecture

### Design Principles
1. **Normalization**: Properly normalized to 3NF where appropriate
2. **JSONB Usage**: Strategic use for flexible, schema-less data
3. **UUID Primary Keys**: For distributed systems compatibility
4. **Soft Deletes**: Most entities use status fields instead of hard deletes
5. **Audit Trail**: Comprehensive timestamp tracking
6. **Performance**: Strategic indexing and materialized views

### Schema Organization

```mermaid
graph TB
    subgraph "User Domain"
        users[users]
        profiles[profiles]
        user_settings[user_settings]
        subscriptions[subscriptions]
    end
    
    subgraph "Content Domain"
        projects[projects]
        chapters[chapters]
        characters[characters]
        story_arcs[story_arcs]
        story_bibles[story_bibles]
    end
    
    subgraph "Series Domain"
        series[series]
        universes[universes]
        universe_characters[universe_characters]
    end
    
    subgraph "Collaboration Domain"
        project_collaborators[project_collaborators]
        collaboration_sessions[collaboration_sessions]
        collaboration_edits[collaboration_edits]
    end
    
    subgraph "Analytics Domain"
        writing_sessions[writing_sessions]
        word_count_history[word_count_history]
        analytics_events[analytics_events]
    end
    
    subgraph "AI Domain"
        ai_generations[ai_generations]
        voice_profiles[voice_profiles]
        agent_logs[agent_logs]
    end
```

## Core Tables

### User and Authentication

#### users (Supabase Auth)
Managed by Supabase Auth, extended by profiles table.

#### profiles
```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  username VARCHAR(50) UNIQUE,
  display_name VARCHAR(100),
  bio TEXT,
  avatar_url TEXT,
  writer_type VARCHAR(50), -- 'novelist', 'short_story', 'screenwriter'
  experience_level VARCHAR(50), -- 'beginner', 'intermediate', 'advanced'
  genres_of_interest TEXT[],
  writing_goals JSONB,
  social_links JSONB,
  is_public BOOLEAN DEFAULT false,
  onboarding_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Project Management

#### projects
The central table for all writing projects.

```sql
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Genre & Style
  primary_genre VARCHAR(100),
  subgenre VARCHAR(100),
  narrative_voice VARCHAR(50),
  tense VARCHAR(20),
  tone_options TEXT[],
  writing_style VARCHAR(50),
  
  -- Story Structure
  structure_type VARCHAR(50),
  pacing_preference VARCHAR(50),
  timeline_complexity VARCHAR(50),
  
  -- Technical Specs
  target_word_count INTEGER,
  current_word_count INTEGER DEFAULT 0,
  target_chapters INTEGER,
  
  -- Series Info
  series_id UUID REFERENCES series(id),
  series_order INTEGER,
  
  -- Status
  status TEXT DEFAULT 'planning',
  visibility VARCHAR(20) DEFAULT 'private',
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  last_edited_at TIMESTAMP,
  published_at TIMESTAMP
);
```

#### chapters
Content storage with version control support.

```sql
CREATE TABLE chapters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_number INTEGER NOT NULL,
  title TEXT,
  
  -- Content
  content TEXT,
  outline TEXT,
  
  -- Metrics
  target_word_count INTEGER,
  actual_word_count INTEGER DEFAULT 0,
  
  -- AI Planning Data
  scenes_data JSONB,
  character_states JSONB,
  plot_advancement JSONB,
  ai_notes JSONB,
  
  -- POV
  pov_character VARCHAR(100),
  
  -- Status
  status TEXT DEFAULT 'planned',
  quality_score JSONB,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(project_id, chapter_number)
);
```

#### characters
Comprehensive character profiles with AI-generated data.

```sql
CREATE TABLE characters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  universe_id UUID REFERENCES universes(id),
  
  -- Basic Info
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  description TEXT,
  
  -- AI-Generated Profile
  backstory TEXT,
  personality_traits JSONB,
  character_arc JSONB,
  relationships JSONB,
  voice_data JSONB,
  
  -- Visual
  appearance JSONB,
  image_url TEXT,
  
  -- Metadata
  is_shared BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Series and Universe Management

#### series
Multi-book series organization.

```sql
CREATE TABLE series (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  universe_id UUID REFERENCES universes(id),
  
  title VARCHAR(255) NOT NULL,
  description TEXT,
  planned_books INTEGER,
  
  -- Series Metadata
  genre VARCHAR(100),
  target_audience VARCHAR(50),
  series_arc JSONB,
  
  -- Publishing
  status VARCHAR(50) DEFAULT 'planning',
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### universes
Shared world-building across projects.

```sql
CREATE TABLE universes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  name VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- World Rules
  magic_system JSONB,
  technology_level VARCHAR(100),
  physics_rules JSONB,
  
  -- Geography
  world_map_url TEXT,
  locations JSONB,
  
  -- History
  timeline JSONB,
  major_events JSONB,
  
  -- Culture
  cultures JSONB,
  languages JSONB,
  religions JSONB,
  
  -- Sharing
  is_public BOOLEAN DEFAULT false,
  collaborators UUID[],
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Collaboration

#### project_collaborators
Team member management for projects.

```sql
CREATE TABLE project_collaborators (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  role VARCHAR(50) NOT NULL, -- 'owner', 'editor', 'reviewer', 'viewer'
  permissions JSONB,
  
  -- Invitation
  invited_by UUID REFERENCES auth.users(id),
  invitation_token VARCHAR(255) UNIQUE,
  invitation_accepted BOOLEAN DEFAULT false,
  
  -- Activity
  last_accessed TIMESTAMP,
  contribution_stats JSONB,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(project_id, user_id)
);
```

#### collaboration_sessions
Real-time editing session tracking.

```sql
CREATE TABLE collaboration_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  
  -- Session Info
  session_token VARCHAR(255) UNIQUE,
  is_active BOOLEAN DEFAULT true,
  
  -- Participants
  participants JSONB, -- Array of user info with cursors
  
  -- Timing
  started_at TIMESTAMP DEFAULT NOW(),
  ended_at TIMESTAMP,
  last_activity TIMESTAMP DEFAULT NOW()
);
```

### Analytics and Tracking

#### writing_sessions
Detailed writing activity tracking.

```sql
CREATE TABLE writing_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id),
  
  -- Metrics
  words_written INTEGER DEFAULT 0,
  words_edited INTEGER DEFAULT 0,
  words_deleted INTEGER DEFAULT 0,
  
  -- Time
  duration_seconds INTEGER,
  active_writing_time INTEGER,
  
  -- AI Usage
  ai_assistance_used BOOLEAN DEFAULT false,
  ai_suggestions_count INTEGER DEFAULT 0,
  ai_words_generated INTEGER DEFAULT 0,
  
  -- Context
  session_type VARCHAR(50),
  device_type VARCHAR(50),
  
  started_at TIMESTAMP DEFAULT NOW(),
  ended_at TIMESTAMP
);
```

#### word_count_history
Historical word count tracking.

```sql
CREATE TABLE word_count_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  
  -- Counts
  total_words INTEGER NOT NULL,
  chapter_words JSONB, -- {chapter_id: word_count}
  
  -- Daily Stats
  words_added INTEGER DEFAULT 0,
  words_removed INTEGER DEFAULT 0,
  net_change INTEGER DEFAULT 0,
  
  -- Snapshot
  recorded_at DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(project_id, recorded_at)
);
```

### AI and Generation

#### ai_generations
Track all AI-generated content.

```sql
CREATE TABLE ai_generations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id),
  
  -- Generation Details
  agent_type VARCHAR(50) NOT NULL,
  model_used VARCHAR(50),
  prompt TEXT,
  system_prompt TEXT,
  
  -- Output
  generated_content TEXT,
  structured_output JSONB,
  
  -- Metrics
  tokens_used INTEGER,
  words_generated INTEGER,
  generation_time_ms INTEGER,
  
  -- Quality
  quality_score FLOAT,
  user_rating INTEGER,
  was_used BOOLEAN DEFAULT false,
  
  -- Cost
  estimated_cost DECIMAL(10, 4),
  
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### voice_profiles
Character voice consistency profiles.

```sql
CREATE TABLE voice_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  character_id UUID REFERENCES characters(id) ON DELETE CASCADE,
  
  -- Voice Characteristics
  vocabulary_level VARCHAR(50),
  speech_patterns JSONB,
  common_phrases TEXT[],
  dialogue_examples TEXT[],
  
  -- Personality in Speech
  formality_level INTEGER, -- 1-10
  humor_style VARCHAR(50),
  emotional_expression VARCHAR(50),
  
  -- Linguistic Features
  dialect VARCHAR(100),
  accent_notes TEXT,
  grammatical_quirks TEXT[],
  
  -- AI Training Data
  training_samples JSONB,
  model_parameters JSONB,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Version Control

#### chapter_versions
Complete version history for chapters.

```sql
CREATE TABLE chapter_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
  
  version_number INTEGER NOT NULL,
  content TEXT NOT NULL,
  
  -- Change Tracking
  change_type VARCHAR(50), -- 'manual', 'ai_generated', 'ai_edited'
  change_summary TEXT,
  diff_data JSONB,
  
  -- Metrics
  word_count INTEGER,
  quality_metrics JSONB,
  
  -- Attribution
  created_by UUID REFERENCES auth.users(id),
  ai_agent VARCHAR(50),
  
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(chapter_id, version_number)
);
```

### Achievements and Gamification

#### achievements
Available achievements in the system.

```sql
CREATE TABLE achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Achievement Info
  name VARCHAR(100) NOT NULL UNIQUE,
  category VARCHAR(50) NOT NULL,
  description TEXT,
  
  -- Requirements
  criteria JSONB NOT NULL,
  points INTEGER DEFAULT 10,
  
  -- Display
  icon VARCHAR(50),
  rarity VARCHAR(20), -- 'common', 'rare', 'epic', 'legendary'
  
  -- Availability
  is_active BOOLEAN DEFAULT true,
  available_from DATE,
  available_until DATE,
  
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### user_achievements
User achievement progress and unlocks.

```sql
CREATE TABLE user_achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
  
  -- Progress
  progress JSONB,
  progress_percentage INTEGER DEFAULT 0,
  
  -- Unlock Status
  unlocked BOOLEAN DEFAULT false,
  unlocked_at TIMESTAMP,
  
  -- Notification
  notified BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, achievement_id)
);
```

## Relationship Diagrams

### Project Relationships

```mermaid
erDiagram
    projects ||--o{ chapters : contains
    projects ||--o{ characters : defines
    projects ||--o{ story_arcs : structures
    projects ||--|| story_bibles : maintains
    projects ||--o{ project_collaborators : shares
    projects }o--|| series : belongs_to
    projects }o--|| universes : uses
    
    chapters ||--o{ chapter_versions : versioned
    chapters ||--o{ writing_sessions : tracks
    
    characters }o--|| voice_profiles : has
    characters }o--|| universes : shared_in
    
    users ||--o{ projects : owns
    users ||--o{ writing_sessions : performs
    users ||--o{ ai_generations : requests
```

### Collaboration Flow

```mermaid
sequenceDiagram
    participant User
    participant Project
    participant Collaborator
    participant Session
    participant Edit
    
    User->>Project: Create/Own
    User->>Collaborator: Invite
    Collaborator->>Project: Accept & Join
    Collaborator->>Session: Start Editing
    Session->>Edit: Track Changes
    Edit->>Project: Update Content
```

## Indexes and Performance

### Primary Indexes
```sql
-- User lookups
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_profiles_username ON profiles(username);

-- Content queries
CREATE INDEX idx_chapters_project_id ON chapters(project_id);
CREATE INDEX idx_chapters_status ON chapters(status);
CREATE INDEX idx_characters_project_id ON characters(project_id);
CREATE INDEX idx_characters_universe_id ON characters(universe_id);

-- Collaboration
CREATE INDEX idx_collaborators_project_user ON project_collaborators(project_id, user_id);
CREATE INDEX idx_collaboration_active ON collaboration_sessions(is_active);

-- Analytics
CREATE INDEX idx_sessions_user_project ON writing_sessions(user_id, project_id);
CREATE INDEX idx_word_history_date ON word_count_history(project_id, recorded_at);

-- AI tracking
CREATE INDEX idx_ai_user_project ON ai_generations(user_id, project_id);
CREATE INDEX idx_ai_agent_type ON ai_generations(agent_type);
```

### Composite Indexes
```sql
-- Frequently joined queries
CREATE INDEX idx_chapters_project_status ON chapters(project_id, status);
CREATE INDEX idx_projects_user_status ON projects(user_id, status);
CREATE INDEX idx_achievements_user_unlocked ON user_achievements(user_id, unlocked);
```

### Performance Views
```sql
-- Project overview materialized view
CREATE MATERIALIZED VIEW project_overview AS
SELECT 
  p.id,
  p.title,
  p.user_id,
  p.current_word_count,
  COUNT(DISTINCT ch.id) as chapter_count,
  COUNT(DISTINCT c.id) as character_count,
  MAX(ch.updated_at) as last_chapter_update
FROM projects p
LEFT JOIN chapters ch ON ch.project_id = p.id
LEFT JOIN characters c ON c.project_id = p.id
GROUP BY p.id;

-- Refresh strategy
CREATE INDEX idx_project_overview_user ON project_overview(user_id);
```

## Row-Level Security

### Security Policies

```sql
-- Projects: Users can only see their own or shared projects
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view shared projects" ON projects
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_collaborators
      WHERE project_id = projects.id
      AND user_id = auth.uid()
      AND invitation_accepted = true
    )
  );

-- Chapters: Inherit project permissions
CREATE POLICY "Users can view project chapters" ON chapters
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = chapters.project_id
      AND (
        projects.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM project_collaborators
          WHERE project_id = projects.id
          AND user_id = auth.uid()
        )
      )
    )
  );

-- AI Generations: Users see only their own
CREATE POLICY "Users can view own AI generations" ON ai_generations
  FOR SELECT USING (auth.uid() = user_id);
```

## Migration Strategy

### Version Control
1. All migrations are numbered sequentially
2. Each migration is idempotent (can be run multiple times)
3. Rollback scripts are provided for each migration
4. Schema changes are tested in staging first

### Migration Best Practices
```sql
-- Example migration with rollback
-- UP
BEGIN;
  ALTER TABLE projects ADD COLUMN new_feature JSONB;
  CREATE INDEX idx_projects_new_feature ON projects(new_feature);
COMMIT;

-- DOWN
BEGIN;
  DROP INDEX IF EXISTS idx_projects_new_feature;
  ALTER TABLE projects DROP COLUMN IF EXISTS new_feature;
COMMIT;
```

### Data Migration Patterns
1. **Add nullable columns** first
2. **Backfill data** in batches
3. **Add constraints** after data is clean
4. **Remove old columns** in separate migration

## Performance Considerations

### Query Optimization
1. Use indexes for all foreign keys
2. Partial indexes for filtered queries
3. JSONB GIN indexes for JSON searches
4. Materialized views for complex aggregations

### Data Archival
1. Old writing sessions archived after 1 year
2. AI generation logs compressed after 6 months
3. Deleted projects soft-deleted for 30 days
4. Version history limited to last 100 versions

### Connection Pooling
- Supabase manages connection pooling
- Read replicas for analytics queries
- Separate connections for real-time subscriptions