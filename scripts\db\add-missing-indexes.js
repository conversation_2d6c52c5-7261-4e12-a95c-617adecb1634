#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Analyzing database for missing indexes...\n');

// Define indexes needed for optimal performance
const requiredIndexes = [
  // User-related indexes
  {
    table: 'users',
    name: 'idx_users_email',
    columns: ['email'],
    unique: true,
    description: 'Fast user lookup by email'
  },
  {
    table: 'users',
    name: 'idx_users_created_at',
    columns: ['created_at'],
    description: 'User analytics and sorting'
  },
  
  // Projects indexes
  {
    table: 'projects',
    name: 'idx_projects_user_id',
    columns: ['user_id'],
    description: 'Fast project listing by user'
  },
  {
    table: 'projects',
    name: 'idx_projects_status',
    columns: ['status'],
    description: 'Filter projects by status'
  },
  {
    table: 'projects',
    name: 'idx_projects_created_at',
    columns: ['created_at'],
    description: 'Sort projects by creation date'
  },
  {
    table: 'projects',
    name: 'idx_projects_user_status',
    columns: ['user_id', 'status'],
    description: 'Composite index for user project queries'
  },
  
  // Chapters indexes
  {
    table: 'chapters',
    name: 'idx_chapters_project_id',
    columns: ['project_id'],
    description: 'Fast chapter listing by project'
  },
  {
    table: 'chapters',
    name: 'idx_chapters_order',
    columns: ['project_id', 'order_index'],
    description: 'Chapter ordering within project'
  },
  {
    table: 'chapters',
    name: 'idx_chapters_updated_at',
    columns: ['updated_at'],
    description: 'Recent changes tracking'
  },
  
  // Characters indexes
  {
    table: 'characters',
    name: 'idx_characters_project_id',
    columns: ['project_id'],
    description: 'Fast character listing by project'
  },
  {
    table: 'characters',
    name: 'idx_characters_user_id',
    columns: ['user_id'],
    description: 'User character management'
  },
  
  // Series indexes
  {
    table: 'series',
    name: 'idx_series_user_id',
    columns: ['user_id'],
    description: 'Fast series listing by user'
  },
  {
    table: 'series_books',
    name: 'idx_series_books_series_id',
    columns: ['series_id'],
    description: 'Books in series'
  },
  {
    table: 'series_books',
    name: 'idx_series_books_order',
    columns: ['series_id', 'order_in_series'],
    description: 'Book ordering in series'
  },
  
  // Collaboration indexes
  {
    table: 'project_collaborators',
    name: 'idx_collaborators_project_id',
    columns: ['project_id'],
    description: 'Project team listing'
  },
  {
    table: 'project_collaborators',
    name: 'idx_collaborators_user_id',
    columns: ['user_id'],
    description: 'User collaboration listing'
  },
  {
    table: 'project_collaborators',
    name: 'idx_collaborators_status',
    columns: ['project_id', 'status'],
    description: 'Active collaborators'
  },
  
  // Analytics indexes
  {
    table: 'writing_sessions',
    name: 'idx_sessions_user_id',
    columns: ['user_id'],
    description: 'User writing sessions'
  },
  {
    table: 'writing_sessions',
    name: 'idx_sessions_date',
    columns: ['user_id', 'started_at'],
    description: 'Session analytics by date'
  },
  {
    table: 'word_count_history',
    name: 'idx_word_count_project',
    columns: ['project_id', 'recorded_at'],
    description: 'Project word count tracking'
  },
  {
    table: 'word_count_history',
    name: 'idx_word_count_chapter',
    columns: ['chapter_id', 'recorded_at'],
    description: 'Chapter word count tracking'
  },
  
  // Search and content indexes
  {
    table: 'content_embeddings',
    name: 'idx_embeddings_content_type',
    columns: ['content_type', 'content_id'],
    description: 'Fast embedding lookup'
  },
  {
    table: 'content_embeddings',
    name: 'idx_embeddings_project',
    columns: ['project_id'],
    description: 'Project content search'
  },
  
  // Achievements indexes
  {
    table: 'user_achievements',
    name: 'idx_achievements_user',
    columns: ['user_id', 'unlocked_at'],
    description: 'User achievement history'
  },
  {
    table: 'user_achievements',
    name: 'idx_achievements_type',
    columns: ['achievement_id', 'user_id'],
    unique: true,
    description: 'Prevent duplicate achievements'
  },
  
  // Notifications indexes
  {
    table: 'notifications',
    name: 'idx_notifications_user',
    columns: ['user_id', 'created_at'],
    description: 'User notification feed'
  },
  {
    table: 'notifications',
    name: 'idx_notifications_unread',
    columns: ['user_id', 'read'],
    where: 'read = false',
    description: 'Unread notification count'
  },
  
  // Task queue indexes
  {
    table: 'processing_tasks',
    name: 'idx_tasks_status',
    columns: ['status', 'scheduled_for'],
    where: "status = 'pending'",
    description: 'Pending task queue'
  },
  {
    table: 'processing_tasks',
    name: 'idx_tasks_user',
    columns: ['user_id', 'created_at'],
    description: 'User task history'
  },
  
  // Full text search indexes
  {
    table: 'chapters',
    name: 'idx_chapters_fts',
    columns: ['content'],
    type: 'gin',
    operator: 'gin_trgm_ops',
    description: 'Full text search on chapter content'
  },
  {
    table: 'characters',
    name: 'idx_characters_fts',
    columns: ['name', 'bio'],
    type: 'gin',
    operator: 'gin_trgm_ops',
    description: 'Full text search on characters'
  }
];

async function checkExistingIndexes() {
  const { data, error } = await supabase.rpc('get_indexes', {});
  
  if (error) {
    // Create the helper function if it doesn't exist
    await supabase.rpc('exec', {
      query: `
        CREATE OR REPLACE FUNCTION get_indexes()
        RETURNS TABLE (
          schemaname text,
          tablename text,
          indexname text,
          indexdef text
        )
        LANGUAGE sql
        AS $$
          SELECT 
            schemaname,
            tablename,
            indexname,
            indexdef
          FROM pg_indexes
          WHERE schemaname = 'public'
        $$;
      `
    });
    
    // Try again
    const retry = await supabase.rpc('get_indexes', {});
    return retry.data || [];
  }
  
  return data || [];
}

async function createIndex(index) {
  let query = `CREATE`;
  
  if (index.unique) {
    query += ` UNIQUE`;
  }
  
  query += ` INDEX IF NOT EXISTS ${index.name} ON ${index.table}`;
  
  if (index.type === 'gin') {
    query += ` USING ${index.type}`;
  }
  
  query += ` (${index.columns.join(', ')})`;
  
  if (index.operator) {
    query += ` ${index.operator}`;
  }
  
  if (index.where) {
    query += ` WHERE ${index.where}`;
  }
  
  query += `;`;
  
  console.log(`📝 Creating index: ${index.name}`);
  console.log(`   Query: ${query}`);
  
  try {
    const { error } = await supabase.rpc('exec', { query });
    
    if (error) {
      console.error(`❌ Failed to create ${index.name}:`, error.message);
      return false;
    }
    
    console.log(`✅ Created index: ${index.name}`);
    return true;
  } catch (err) {
    console.error(`❌ Error creating ${index.name}:`, err.message);
    return false;
  }
}

async function analyzeIndexUsage() {
  // Get index usage statistics
  const usageQuery = `
    SELECT 
      schemaname,
      tablename,
      indexname,
      idx_scan as index_scans,
      idx_tup_read as tuples_read,
      idx_tup_fetch as tuples_fetched
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
    ORDER BY idx_scan DESC;
  `;
  
  const { data: usage, error } = await supabase.rpc('exec', { 
    query: usageQuery, 
    return_data: true 
  });
  
  if (!error && usage) {
    console.log('\n📊 Current Index Usage Statistics:');
    console.log('━'.repeat(80));
    
    usage.forEach(idx => {
      console.log(`${idx.indexname.padEnd(40)} Scans: ${idx.index_scans.toString().padStart(10)} Reads: ${idx.tuples_read.toString().padStart(10)}`);
    });
  }
}

async function suggestAdditionalIndexes() {
  // Find missing indexes based on query patterns
  const missingIndexQuery = `
    SELECT 
      schemaname,
      tablename,
      attname,
      n_distinct,
      correlation
    FROM pg_stats
    WHERE schemaname = 'public'
      AND n_distinct > 100
      AND correlation < 0.5
    ORDER BY n_distinct DESC
    LIMIT 10;
  `;
  
  const { data: candidates, error } = await supabase.rpc('exec', { 
    query: missingIndexQuery, 
    return_data: true 
  });
  
  if (!error && candidates && candidates.length > 0) {
    console.log('\n💡 Additional Index Suggestions:');
    console.log('━'.repeat(80));
    
    candidates.forEach(col => {
      console.log(`Consider indexing: ${col.tablename}.${col.attname} (distinct values: ${col.n_distinct})`);
    });
  }
}

async function main() {
  try {
    // First check what indexes already exist
    console.log('📋 Checking existing indexes...\n');
    const existingIndexes = await checkExistingIndexes();
    const existingNames = new Set(existingIndexes.map(idx => idx.indexname));
    
    console.log(`Found ${existingIndexes.length} existing indexes\n`);
    
    // Filter out indexes that already exist
    const indexesToCreate = requiredIndexes.filter(idx => !existingNames.has(idx.name));
    
    if (indexesToCreate.length === 0) {
      console.log('✅ All required indexes already exist!');
    } else {
      console.log(`📝 Creating ${indexesToCreate.length} missing indexes...\n`);
      
      let created = 0;
      let failed = 0;
      
      for (const index of indexesToCreate) {
        const success = await createIndex(index);
        if (success) {
          created++;
        } else {
          failed++;
        }
      }
      
      console.log('\n📊 Summary:');
      console.log(`   Created: ${created}`);
      console.log(`   Failed: ${failed}`);
      console.log(`   Already existed: ${requiredIndexes.length - indexesToCreate.length}`);
    }
    
    // Analyze current index usage
    await analyzeIndexUsage();
    
    // Suggest additional indexes
    await suggestAdditionalIndexes();
    
    console.log('\n✅ Database index optimization complete!');
    
  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// Create RPC helper if needed
async function ensureRPCHelper() {
  const helperQuery = `
    CREATE OR REPLACE FUNCTION exec(query text, return_data boolean DEFAULT false)
    RETURNS json
    LANGUAGE plpgsql
    AS $$
    DECLARE
      result json;
    BEGIN
      IF return_data THEN
        EXECUTE query INTO result;
        RETURN result;
      ELSE
        EXECUTE query;
        RETURN json_build_object('success', true);
      END IF;
    EXCEPTION
      WHEN OTHERS THEN
        RETURN json_build_object('error', SQLERRM);
    END;
    $$;
  `;
  
  try {
    await supabase.rpc('exec', { query: helperQuery });
  } catch (err) {
    // Helper might not exist yet, try direct SQL
    console.log('Creating RPC helper function...');
  }
}

// Run the script
ensureRPCHelper().then(() => main());