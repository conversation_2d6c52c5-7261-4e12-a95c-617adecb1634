const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { NextRequest } = require('next/server')

// Polyfill Response.json for environments that lack it
if (typeof Response.json !== 'function') {
  Response.json = (data, init) =>
    new Response(JSON.stringify(data), {
      ...(init || {}),
      headers: { 'content-type': 'application/json', ...(init?.headers || {}) },
    })
}

jest.mock('@/lib/supabase', () => ({
  createTypedServerClient: jest.fn(),
}))


jest.mock('@/lib/auth', () => ({

  authenticateUser: jest.fn(),
  handleRouteError: jest.fn((error) =>
    new Response(JSON.stringify({ error: String(error) }), { status: 500 })
  ),
}))

jest.mock('@/lib/ai/vercel-ai-client', () => ({
  vercelAIClient: {
    generateTextWithFallback: jest.fn(),
  },
}))

jest.mock('@/lib/rate-limiter-unified', () => ({
  applyRateLimit: jest.fn().mockResolvedValue(null),
}))

jest.mock('@/lib/api/error-handler', () => ({
  handleAPIError: jest.fn((error) =>
    new Response(JSON.stringify({ error: String(error) }), { status: 400 })
  ),
  ValidationError: class ValidationError extends Error {},
  NotFoundError: class NotFoundError extends Error {},
}))

jest.mock('@/lib/services/logger', () => ({
  logger: { warn: jest.fn(), error: jest.fn(), info: jest.fn() },
}))

const { POST, resetSummaryCache } = require('@/app/api/analysis/book-summary/route')

const { createTypedServerClient } = require('@/lib/supabase')

const { authenticateUser } = require('@/lib/auth')

const { vercelAIClient } = require('@/lib/ai/vercel-ai-client')

const mockSupabase = {
  from: jest.fn((table: string) => {
    if (table === 'projects') {
      return {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: {
                title: 'Test',
                genre: 'fantasy',
                subgenre: 'epic',
                description: 'desc',
                target_audience: 'general',
                word_count: 1000,
              },
              error: null,
            }),
          }),
        }),
      }
    }
    if (table === 'chapters') {
      return {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: [{ chapter_number: 1, title: 'Ch1', summary: 'Sum1' }],
              error: null,
            }),
          }),
        }),
      }
    }
    if (table === 'story_bible') {
      return {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { themes: [], characters: [] },
              error: null,
            }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ data: null, error: null }),
        }),
      }
    }
    if (table === 'book_summaries') {
      return {
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({ error: null }),
        }),
        insert: jest.fn().mockResolvedValue({ error: null }),
      }
    }
    return {}
  }),
}

describe('Book Summary API Route caching', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    resetSummaryCache()
    createTypedServerClient.mockResolvedValue(mockSupabase)
    authenticateUser.mockResolvedValue({ success: true })
    vercelAIClient.generateTextWithFallback.mockResolvedValue('Generated summary')
  })

  afterEach(() => {
    resetSummaryCache()
  })

  it('caches summary for subsequent requests', async () => {
    const body = {
      projectId: 'proj1',
      summaryType: 'elevator-pitch',
      targetAudience: 'general',
      tone: 'casual',
    }

    const request1 = new NextRequest('http://localhost/api/analysis/book-summary', {
      method: 'POST',
      body: JSON.stringify(body),
    })

    const response1 = await POST(request1)
    const data1 = await response1.json()

    expect(data1.fromCache).toBe(false)

    const request2 = new NextRequest('http://localhost/api/analysis/book-summary', {
      method: 'POST',
      body: JSON.stringify(body),
    })

    const response2 = await POST(request2)
    const data2 = await response2.json()

    expect(data2.fromCache).toBe(true)
    expect(data2.summary).toBe(data1.summary)
    expect(vercelAIClient.generateTextWithFallback).toHaveBeenCalledTimes(1)
  })
})
