'use client'

import { useState, useEffect, useCallback } from 'react'
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Target, Clock, Calendar, Edit, CheckCircle2, Loader2 } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { ComponentErrorBoundary } from '@/components/error/component-error-boundary'

interface WritingGoal {
  id: string
  goal_type: 'daily' | 'weekly' | 'monthly' | 'project'
  target_words: number
  project_id?: string | null
  start_date: string
  end_date?: string | null
  is_active: boolean
  created_at: string
  updated_at: string
  progress?: {
    date: string
    words_written: number
    sessions_count: number
  }[]
  statistics?: {
    totalWords: number
    completionRate: number
    currentStreak: number
  }
}

interface WritingGoalsProps {
  projectId?: string
}

function WritingGoalsComponent({ projectId }: WritingGoalsProps) {
  const [goals, setGoals] = useState<WritingGoal[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddingGoal, setIsAddingGoal] = useState(false)
  const { toast } = useToast()

  const fetchGoals = useCallback(async () => {
    try {
      const params = new URLSearchParams({
        active: 'true',
        include_progress: 'true'
      })
      if (projectId) {
        params.append('project_id', projectId)
      }

      const response = await fetch(`/api/writing-goals?${params}`)
      if (!response.ok) throw new Error('Failed to fetch goals')
      
      const data = await response.json()
      setGoals(data.goals || [])
    } catch (error) {
      logger.error('Error fetching goals:', error);
      toast({
        title: 'Error',
        description: 'Failed to load writing goals',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [projectId, toast])

  useEffect(() => {
    fetchGoals()
  }, [fetchGoals])

  const getGoalIcon = (type: WritingGoal['goal_type']) => {
    switch (type) {
      case 'daily':
        return <Clock className="h-4 w-4" />
      case 'weekly':
        return <Calendar className="h-4 w-4" />
      case 'monthly':
        return <Calendar className="h-4 w-4" />
      case 'project':
        return <Target className="h-4 w-4" />
    }
  }

  const getGoalProgress = (goal: WritingGoal) => {
    // Get today's progress
    const today = new Date().toISOString().split('T')[0]
    const todayProgress = goal.progress?.find(p => p.date === today)
    const current = todayProgress?.words_written || 0
    return Math.min((current / goal.target_words) * 100, 100)
  }


  const formatDeadline = (endDate?: string | null) => {
    if (!endDate) return null
    const deadline = new Date(endDate)
    const days = Math.ceil((deadline.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
    if (days < 0) return 'Ended'
    if (days === 0) return 'Today'
    if (days === 1) return 'Tomorrow'
    if (days < 7) return `${days} days`
    if (days < 30) return `${Math.floor(days / 7)} weeks`
    return `${Math.floor(days / 30)} months`
  }

  const handleCreateGoal = async (goalData: {
    goal_type: WritingGoal['goal_type']
    target_words: number
    end_date?: string
  }) => {
    try {
      const response = await fetch('/api/writing-goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...goalData,
          project_id: projectId || null,
          start_date: new Date().toISOString().split('T')[0]
        })
      })

      if (!response.ok) throw new Error('Failed to create goal')
      
      const { goal: _goal } = await response.json()
      await fetchGoals() // Refresh goals list
      setIsAddingGoal(false)
      toast({
        title: 'Goal created',
        description: 'Your new writing goal has been set.',
      })
    } catch (error) {
      logger.error('Error creating goal:', error);
      toast({
        title: 'Error',
        description: 'Failed to create writing goal',
        variant: 'destructive'
      })
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg">Writing Goals</CardTitle>
          <CardDescription>Track your progress across different timeframes</CardDescription>
        </div>
        <Dialog open={isAddingGoal} onOpenChange={setIsAddingGoal}>
          <DialogTrigger asChild>
            <Button size="sm" variant="outline">
              <Target className="h-4 w-4 mr-2" />
              Set Goal
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Set Writing Goal</DialogTitle>
              <DialogDescription>
                Create a new writing goal to track your progress
              </DialogDescription>
            </DialogHeader>
            <GoalForm onSubmit={handleCreateGoal} />
          </DialogContent>
        </Dialog>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        )}
        
        {!loading && goals.map((goal) => (
          <div key={goal.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm">
                {getGoalIcon(goal.goal_type)}
                <span className="font-medium capitalize">{goal.goal_type} Goal</span>
                {formatDeadline(goal.end_date) && (
                  <span className="text-muted-foreground">
                    • {formatDeadline(goal.end_date)} left
                  </span>
                )}
              </div>
              <Button size="sm" variant="ghost" className="h-7 w-7 p-0">
                <Edit className="h-3 w-3" />
              </Button>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">
                  {goal.statistics?.totalWords.toLocaleString() || 0} / {goal.target_words.toLocaleString()} words
                </span>
                <span className="font-medium">{Math.round(getGoalProgress(goal))}%</span>
              </div>
              <Progress value={getGoalProgress(goal)} className="h-2" />
              {goal.statistics?.currentStreak && goal.statistics.currentStreak > 0 && (
                <p className="text-xs text-muted-foreground">
                  🔥 {goal.statistics.currentStreak} day streak
                </p>
              )}
            </div>
            
            {getGoalProgress(goal) >= 100 && (
              <div className="flex items-center gap-1 text-xs text-success">
                <CheckCircle2 className="h-3 w-3" />
                Goal achieved!
              </div>
            )}
          </div>
        ))}
        
        {!loading && goals.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No goals set yet</p>
            <p className="text-xs mt-1">Click &ldquo;Set Goal&rdquo; to get started</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function GoalForm({ onSubmit }: { 
  onSubmit: (goal: {
    goal_type: WritingGoal['goal_type']
    target_words: number
    end_date?: string
  }) => void 
}) {
  const [goalType, setGoalType] = useState<WritingGoal['goal_type']>('daily')
  const [targetWords, setTargetWords] = useState('')
  const [endDate, setEndDate] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    try {
      await onSubmit({
        goal_type: goalType,
        target_words: parseInt(targetWords),
        end_date: endDate || undefined
      })
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="goal-type">Goal Type</Label>
        <Select value={goalType} onValueChange={(v) => setGoalType(v as WritingGoal['goal_type'])}>
          <SelectTrigger id="goal-type">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="daily">Daily</SelectItem>
            <SelectItem value="weekly">Weekly</SelectItem>
            <SelectItem value="monthly">Monthly</SelectItem>
            <SelectItem value="project">Project</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="target-words">Target Words</Label>
        <Input
          id="target-words"
          type="number"
          value={targetWords}
          onChange={(e) => setTargetWords(e.target.value)}
          placeholder="1000"
          min="1"
          required
        />
        <p className="text-xs text-muted-foreground">
          {goalType === 'daily' && 'Words to write each day'}
          {goalType === 'weekly' && 'Words to write each week'}
          {goalType === 'monthly' && 'Words to write each month'}
          {goalType === 'project' && 'Total words for this project'}
        </p>
      </div>
      
      {goalType === 'project' && (
        <div className="space-y-2">
          <Label htmlFor="end-date">End Date (optional)</Label>
          <Input
            id="end-date"
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            min={new Date().toISOString().split('T')[0]}
          />
        </div>
      )}
      
      <DialogFooter>
        <Button type="submit" disabled={submitting}>
          {submitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          Create Goal
        </Button>
      </DialogFooter>
    </form>
  )
}

// Export wrapped component with error boundary
export function WritingGoals(props: WritingGoalsProps) {
  return (
    <ComponentErrorBoundary componentName="Writing Goals">
      <WritingGoalsComponent {...props} />
    </ComponentErrorBoundary>
  )
}