/**
 * Achievement and goal multipliers
 */
export const ACHIEVEMENT_MULTIPLIERS = {
  DAILY_CHALLENGE: 1.5,  // Write 50% more than average
  WEEKLY_CHALLENGE: 1.2, // Write 20% more than weekly average
  STREAK_CONTENT_BOOST: 0.4, // 0.4% more content per day of streak
} as const

/**
 * Quality score thresholds
 */
export const QUALITY_THRESHOLDS = {
  MINIMUM_ACCEPTABLE: 0.5,
  GOOD: 0.7,
  EXCELLENT: 0.9,
} as const

/**
 * Chapter expansion factors
 */
export const CHAPTER_EXPANSION = {
  DEFAULT_FACTOR: 1.5,
  MIN_FACTOR: 1.2,
  MAX_FACTOR: 3.0,
} as const

/**
 * Word count constants
 */
export const WORD_COUNT = {
  DEFAULT_CHAPTER: 4000,
  DEFAULT_NOVEL: 80000,
  DEFAULT_CHAPTERS: 20,
  MIN_CHAPTER: 1000,
  MAX_CHAPTER: 10000,
} as const

/**
 * Concurrency limits
 */
export const CONCURRENCY_LIMITS = {
  MIN_BATCH_SIZE: 1,
  MAX_BATCH_SIZE: 5,
  DEFAULT_BATCH_SIZE: 3,
} as const