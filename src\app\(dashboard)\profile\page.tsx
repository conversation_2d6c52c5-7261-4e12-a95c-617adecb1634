import { createClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ProfileForm } from '@/components/profile/profile-form'
import { SubscriptionCard } from '@/components/profile/subscription-card'
import { UsageStatsCard } from '@/components/profile/usage-stats-card'

export default async function ProfilePage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  // Get user profile
  // const { data: _profile } = await supabase
  //   .from('profiles')
  //   .select('*')
  //   .eq('id', user.id)
  //   .single()

  // Get subscription
  const { data: subscription } = await supabase
    .from('user_subscriptions')
    .select('*')
    .eq('user_id', user.id)
    .eq('status', 'active')
    .single()

  // Get current usage
  const periodStart = new Date().toISOString().slice(0, 7)
  const { data: usage } = await supabase
    .from('usage_tracking')
    .select('*')
    .eq('user_id', user.id)
    .eq('period_start', periodStart)
    .single()

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container-wide flex h-16 items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <Link href="/dashboard">
              <Button variant="ghost">← Back to Dashboard</Button>
            </Link>
            <h1 className="text-2xl font-bold">Profile Settings</h1>
          </div>
        </div>
      </header>
      
      <main className="container-wide py-6 sm:py-8 lg:py-10">
        <div className="max-w-4xl mx-auto grid gap-8 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <ProfileForm user={user} />
          </div>
          
          <div className="space-y-6">
            <SubscriptionCard subscription={subscription} />
            <UsageStatsCard usage={usage} subscription={subscription} />
          </div>
        </div>
      </main>
    </div>
  )
}