'use client';

import { useState } from 'react';
import { logger } from '@/lib/services/logger';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Download, 
  FileText, 
  BookOpen, 
  File,
  Printer,
  Share,
  ChevronDown,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface QuickExportPresetsProps {
  projectId: string;
  projectTitle: string;
  chapters?: Array<{ 
    id: string; 
    chapter_number: number; 
    title?: string; 
    content?: string;
    word_count?: number;
  }>;
  variant?: 'button' | 'dropdown' | 'cards';
  showPreview?: boolean;
}

interface ExportPreset {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  format: 'pdf' | 'docx' | 'epub' | 'txt' | 'md';
  settings: ExportSettings;
  badge?: string;
  popular?: boolean;
}

interface ExportSettings {
  format: string;
  includeTitle: boolean;
  includeChapterNumbers: boolean;
  includeMetadata: boolean;
  pageBreaks: 'chapter' | 'section' | 'none';
  fontSize: number;
  fontFamily: string;
  margins: 'narrow' | 'normal' | 'wide';
  lineSpacing: number;
  paperSize: 'a4' | 'letter' | 'legal';
}

const exportPresets: ExportPreset[] = [
  {
    id: 'manuscript-submission',
    name: 'Manuscript Submission',
    description: 'Industry-standard formatting for submissions',
    icon: <FileText className="w-4 h-4" />,
    format: 'docx',
    badge: 'Popular',
    popular: true,
    settings: {
      format: 'docx',
      includeTitle: true,
      includeChapterNumbers: true,
      includeMetadata: true,
      pageBreaks: 'chapter',
      fontSize: 12,
      fontFamily: 'Times New Roman',
      margins: 'normal',
      lineSpacing: 2,
      paperSize: 'letter',
    },
  },
  {
    id: 'ebook-ready',
    name: 'eBook Ready',
    description: 'Optimized EPUB for digital publishing',
    icon: <BookOpen className="w-4 h-4" />,
    format: 'epub',
    badge: 'Best for Publishing',
    popular: true,
    settings: {
      format: 'epub',
      includeTitle: true,
      includeChapterNumbers: true,
      includeMetadata: true,
      pageBreaks: 'chapter',
      fontSize: 14,
      fontFamily: 'Georgia',
      margins: 'normal',
      lineSpacing: 1.5,
      paperSize: 'a4',
    },
  },
  {
    id: 'print-ready-pdf',
    name: 'Print-Ready PDF',
    description: 'Professional PDF for print-on-demand',
    icon: <Printer className="w-4 h-4" />,
    format: 'pdf',
    badge: 'POD Ready',
    settings: {
      format: 'pdf',
      includeTitle: true,
      includeChapterNumbers: true,
      includeMetadata: true,
      pageBreaks: 'chapter',
      fontSize: 11,
      fontFamily: 'Minion Pro',
      margins: 'narrow',
      lineSpacing: 1.4,
      paperSize: 'a4',
    },
  },
  {
    id: 'beta-reader',
    name: 'Beta Reader',
    description: 'Easy-to-read format for feedback',
    icon: <Share className="w-4 h-4" />,
    format: 'pdf',
    badge: 'Feedback Ready',
    settings: {
      format: 'pdf',
      includeTitle: true,
      includeChapterNumbers: true,
      includeMetadata: false,
      pageBreaks: 'chapter',
      fontSize: 13,
      fontFamily: 'Arial',
      margins: 'wide',
      lineSpacing: 1.8,
      paperSize: 'letter',
    },
  },
  {
    id: 'backup-archive',
    name: 'Backup Archive',
    description: 'Complete text backup in multiple formats',
    icon: <File className="w-4 h-4" />,
    format: 'txt',
    settings: {
      format: 'txt',
      includeTitle: true,
      includeChapterNumbers: true,
      includeMetadata: true,
      pageBreaks: 'section',
      fontSize: 12,
      fontFamily: 'Courier New',
      margins: 'normal',
      lineSpacing: 1,
      paperSize: 'letter',
    },
  },
  {
    id: 'quick-share',
    name: 'Quick Share',
    description: 'Simple Word document for sharing',
    icon: <Download className="w-4 h-4" />,
    format: 'docx',
    settings: {
      format: 'docx',
      includeTitle: true,
      includeChapterNumbers: true,
      includeMetadata: false,
      pageBreaks: 'chapter',
      fontSize: 12,
      fontFamily: 'Calibri',
      margins: 'normal',
      lineSpacing: 1.5,
      paperSize: 'letter',
    },
  },
];

export function QuickExportPresets({
  projectId,
  projectTitle,
  chapters = [],
  variant = 'button',
  showPreview: _showPreview = false,
}: QuickExportPresetsProps) {
  const [_exportingPreset, setExportingPreset] = useState<string | null>(null);
  const [exportStatus, setExportStatus] = useState<{[key: string]: 'idle' | 'exporting' | 'success' | 'error'}>({});

  const handleExport = async (preset: ExportPreset) => {
    setExportingPreset(preset.id);
    setExportStatus(prev => ({ ...prev, [preset.id]: 'exporting' }));

    try {
      // Call the export API with the preset settings
      const response = await fetch(`/api/projects/${projectId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: preset.format,
          settings: preset.settings,
          chapters: chapters.map(ch => ch.id),
          presetName: preset.name,
        }),
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${projectTitle}-${preset.name.toLowerCase().replace(/\s+/g, '-')}.${preset.format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setExportStatus(prev => ({ ...prev, [preset.id]: 'success' }));
      
      toast({
        title: 'Export Complete',
        description: `${preset.name} export downloaded successfully`,
      });

      // Reset success status after 3 seconds
      setTimeout(() => {
        setExportStatus(prev => ({ ...prev, [preset.id]: 'idle' }));
      }, 3000);

    } catch (error) {
      logger.error('Export error:', error);
      setExportStatus(prev => ({ ...prev, [preset.id]: 'error' }));
      
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'Unknown export error',
        variant: 'destructive',
      });

      // Reset error status after 5 seconds
      setTimeout(() => {
        setExportStatus(prev => ({ ...prev, [preset.id]: 'idle' }));
      }, 5000);
    } finally {
      setExportingPreset(null);
    }
  };

  const getStatusIcon = (presetId: string) => {
    const status = exportStatus[presetId] || 'idle';
    switch (status) {
      case 'exporting':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-success" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error" />;
      default:
        return null;
    }
  };

  const isExporting = (presetId: string) => exportStatus[presetId] === 'exporting';

  if (variant === 'dropdown') {
    const popularPresets = exportPresets.filter(p => p.popular);
    const otherPresets = exportPresets.filter(p => !p.popular);

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="gap-2">
            <Download className="w-4 h-4" />
            Quick Export
            <ChevronDown className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          {popularPresets.map((preset) => (
            <DropdownMenuItem
              key={preset.id}
              onClick={() => handleExport(preset)}
              disabled={isExporting(preset.id)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                {preset.icon}
                <div>
                  <div className="font-medium">{preset.name}</div>
                  <div className="text-xs text-muted-foreground">{preset.format.toUpperCase()}</div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                {preset.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {preset.badge}
                  </Badge>
                )}
                {getStatusIcon(preset.id)}
              </div>
            </DropdownMenuItem>
          ))}
          
          {otherPresets.length > 0 && (
            <>
              <DropdownMenuSeparator />
              {otherPresets.map((preset) => (
                <DropdownMenuItem
                  key={preset.id}
                  onClick={() => handleExport(preset)}
                  disabled={isExporting(preset.id)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    {preset.icon}
                    <div>
                      <div className="font-medium">{preset.name}</div>
                      <div className="text-xs text-muted-foreground">{preset.format.toUpperCase()}</div>
                    </div>
                  </div>
                  {getStatusIcon(preset.id)}
                </DropdownMenuItem>
              ))}
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (variant === 'cards') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {exportPresets.map((preset) => (
          <Card key={preset.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {preset.icon}
                  <CardTitle className="text-base">{preset.name}</CardTitle>
                </div>
                {preset.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {preset.badge}
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                {preset.description}
              </p>
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-xs">
                  {preset.format.toUpperCase()}
                </Badge>
                <Button
                  size="sm"
                  onClick={() => handleExport(preset)}
                  disabled={isExporting(preset.id)}
                  className="gap-2"
                >
                  {getStatusIcon(preset.id) || <Download className="w-4 h-4" />}
                  Export
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Default button variant - just show the most popular preset
  const popularPreset = exportPresets.find(p => p.id === 'manuscript-submission');
  if (!popularPreset) return null;

  return (
    <Button
      onClick={() => handleExport(popularPreset)}
      disabled={isExporting(popularPreset.id)}
      className="gap-2"
      variant="outline"
    >
      {getStatusIcon(popularPreset.id) || popularPreset.icon}
      {isExporting(popularPreset.id) ? 'Exporting...' : popularPreset.name}
    </Button>
  );
}

export default QuickExportPresets;