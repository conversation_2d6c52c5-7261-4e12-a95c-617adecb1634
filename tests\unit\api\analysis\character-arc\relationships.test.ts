import { describe, it, expect } from '@jest/globals'

jest.mock('@/lib/auth/unified-auth-service', () => ({
  UnifiedAuthService: { withAuth: (handler: any) => handler }
}))

jest.mock('@/lib/ai/openai-client', () => ({ openai: {} }), { virtual: true })
jest.mock('@/lib/supabase', () => ({ createTypedServerClient: jest.fn() }))
jest.mock('@/lib/services/logger', () => ({ logger: { error: jest.fn() } }))

import { extractRelationships } from '@/app/api/analysis/character-arc/route'

describe('extractRelationships', () => {
  it('identifies co-occurring characters and categorizes relationships', () => {
    const target = { id: '1', name: '<PERSON>' }
    const characters = [
      { id: '1', name: '<PERSON>' },
      { id: '2', name: '<PERSON>' },
      { id: '3', name: '<PERSON>' }
    ]

    const chapters = [
      { id: 'ch1', content: '<PERSON> helped <PERSON> escape the dungeon. <PERSON> thanked <PERSON>.' },
      { id: 'ch2', content: '<PERSON> confronted <PERSON> in a fierce battle.' },
      { id: 'ch3', content: '<PERSON> and <PERSON> planned together.' }
    ]

    const result = extractRelationships(target, chapters, characters)

    expect(result).toEqual([
      { characterId: '2', characterName: 'Bob', type: 'ally', interactions: 3 },
      { characterId: '3', characterName: 'Eve', type: 'rival', interactions: 1 }
    ])
  })

  it("doesn't match names inside other words", () => {
    const target = { id: '1', name: 'Sam' }
    const characters = [
      { id: '1', name: 'Sam' },
      { id: '2', name: 'Ann' }
    ]

    const chapters = [
      { id: 'ch1', content: 'Sam waited in the annex for Ann.' }
    ]

    const result = extractRelationships(target, chapters, characters)

    expect(result).toEqual([
      { characterId: '2', characterName: 'Ann', type: 'neutral', interactions: 1 }
    ])
  })
})
