import { Metadata } from 'next'
import { VoiceProfilesManager } from '@/components/voice/voice-profiles-manager'

export const metadata: Metadata = {
  title: 'Voice Profiles - BookScribe',
  description: 'Manage your writing voice profiles and style consistency'
}

export default function VoiceProfilesPage() {
  return (
    <div className="container-wide mx-auto py-6 sm:py-8 lg:py-10 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Voice Profiles</h1>
        <p className="text-muted-foreground">
          Create and manage voice profiles to maintain consistent writing style across your projects
        </p>
      </div>
      
      <VoiceProfilesManager />
    </div>
  )
}