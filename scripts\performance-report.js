#!/usr/bin/env node

/**
 * Performance Report Generator
 * 
 * Generates a comprehensive performance report from benchmark test results
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Performance thresholds for different operations
const PERFORMANCE_STANDARDS = {
  'agent-orchestration': {
    'Agent Initialization': { threshold: 100, unit: 'ms' },
    'Task Creation': { threshold: 50, unit: 'ms' },
    'Task Execution': { threshold: 5000, unit: 'ms' },
    'Concurrent Tasks': { threshold: 3000, unit: 'ms' },
    'Memory Usage': { threshold: 100, unit: 'MB' },
  },
  'memory-optimization': {
    'Context Compression': { threshold: 1000, unit: 'ms' },
    'Context Merging': { threshold: 100, unit: 'ms' },
    'Token Calculation': { threshold: 10, unit: 'ms' },
    'Memory Stats': { threshold: 500, unit: 'ms' },
    'Archive Operation': { threshold: 2000, unit: 'ms' },
  },
  'collaboration': {
    'Connection Setup': { threshold: 1000, unit: 'ms' },
    'Message Delivery': { threshold: 50, unit: 'ms' },
    'Presence Update': { threshold: 100, unit: 'ms' },
    'Conflict Detection': { threshold: 10, unit: 'ms' },
    'Conflict Resolution': { threshold: 100, unit: 'ms' },
    'Large Document Sync': { threshold: 2000, unit: 'ms' },
  },
};

function runBenchmarks() {
  console.log('🏃 Running performance benchmarks...\n');
  
  try {
    const output = execSync('npm run test:bench', { 
      encoding: 'utf8',
      stdio: 'pipe',
    });
    
    return parseBenchmarkOutput(output);
  } catch (error) {
    console.error('Error running benchmarks:', error.message);
    return null;
  }
}

function parseBenchmarkOutput(output) {
  const results = {
    'agent-orchestration': {},
    'memory-optimization': {},
    'collaboration': {},
  };

  // Parse console.log outputs from benchmark tests
  const lines = output.split('\n');
  let currentCategory = null;

  for (const line of lines) {
    // Detect category from test file names
    if (line.includes('agent-orchestration.bench.ts')) {
      currentCategory = 'agent-orchestration';
    } else if (line.includes('memory-optimization.bench.ts')) {
      currentCategory = 'memory-optimization';
    } else if (line.includes('collaboration.bench.ts')) {
      currentCategory = 'collaboration';
    }

    // Parse performance metrics from console outputs
    if (currentCategory && line.includes(':') && line.includes('ms')) {
      const match = line.match(/(.+):\s*([\d.]+)ms/);
      if (match) {
        const [, operation, time] = match;
        const cleanOperation = operation.replace(/^\s*console\.log\s*/, '').trim();
        results[currentCategory][cleanOperation] = parseFloat(time);
      }
    }
  }

  return results;
}

function generateReport(results) {
  if (!results) {
    return 'Failed to generate performance report due to benchmark errors.';
  }

  let report = `# BookScribe Performance Report\n\n`;
  report += `Generated on: ${new Date().toISOString()}\n\n`;

  let overallScore = 0;
  let totalMetrics = 0;

  for (const [category, metrics] of Object.entries(results)) {
    report += `## ${category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}\n\n`;
    
    const standards = PERFORMANCE_STANDARDS[category] || {};
    let categoryScore = 0;
    let categoryMetrics = 0;

    for (const [operation, time] of Object.entries(metrics)) {
      const standard = findMatchingStandard(operation, standards);
      const status = standard && time <= standard.threshold ? '✅ PASS' : '❌ FAIL';
      const threshold = standard ? `${standard.threshold}${standard.unit}` : 'N/A';
      
      report += `- **${operation}**: ${time}ms (Threshold: ${threshold}) ${status}\n`;
      
      if (standard) {
        const score = Math.max(0, 100 - (time / standard.threshold) * 100);
        categoryScore += score;
        categoryMetrics++;
      }
    }

    if (categoryMetrics > 0) {
      const avgScore = categoryScore / categoryMetrics;
      overallScore += avgScore;
      totalMetrics++;
      report += `\n**Category Score**: ${avgScore.toFixed(1)}/100\n\n`;
    }
  }

  const finalScore = totalMetrics > 0 ? overallScore / totalMetrics : 0;
  report += `## Overall Performance Score: ${finalScore.toFixed(1)}/100\n\n`;

  // Add recommendations
  report += generateRecommendations(results);

  return report;
}

function findMatchingStandard(operation, standards) {
  // Try exact match first
  if (standards[operation]) {
    return standards[operation];
  }

  // Try partial matches
  for (const [key, value] of Object.entries(standards)) {
    if (operation.toLowerCase().includes(key.toLowerCase()) || 
        key.toLowerCase().includes(operation.toLowerCase())) {
      return value;
    }
  }

  return null;
}

function generateRecommendations(results) {
  let recommendations = `## Performance Recommendations\n\n`;

  const issues = [];

  for (const [category, metrics] of Object.entries(results)) {
    const standards = PERFORMANCE_STANDARDS[category] || {};
    
    for (const [operation, time] of Object.entries(metrics)) {
      const standard = findMatchingStandard(operation, standards);
      
      if (standard && time > standard.threshold) {
        const severity = time > standard.threshold * 2 ? 'HIGH' : 'MEDIUM';
        issues.push({
          category,
          operation,
          time,
          threshold: standard.threshold,
          severity,
        });
      }
    }
  }

  if (issues.length === 0) {
    recommendations += `✅ All performance metrics are within acceptable thresholds!\n\n`;
  } else {
    recommendations += `Found ${issues.length} performance issues:\n\n`;
    
    for (const issue of issues) {
      recommendations += `### ${issue.severity} PRIORITY: ${issue.operation}\n`;
      recommendations += `- **Current**: ${issue.time}ms\n`;
      recommendations += `- **Target**: ${issue.threshold}ms\n`;
      recommendations += `- **Category**: ${issue.category}\n`;
      
      // Add specific recommendations
      const suggestion = getPerformanceSuggestion(issue.category, issue.operation);
      if (suggestion) {
        recommendations += `- **Suggestion**: ${suggestion}\n`;
      }
      
      recommendations += '\n';
    }
  }

  return recommendations;
}

function getPerformanceSuggestion(category, operation) {
  const suggestions = {
    'agent-orchestration': {
      'Agent Initialization': 'Consider lazy loading agents or implementing a warm-up cache',
      'Task Creation': 'Optimize task dependency resolution algorithm',
      'Task Execution': 'Implement task result caching and parallel execution optimization',
      'Memory Usage': 'Add memory cleanup between task executions and implement object pooling',
    },
    'memory-optimization': {
      'Context Compression': 'Implement streaming compression for large contexts',
      'Context Merging': 'Use more efficient data structures for deduplication',
      'Token Calculation': 'Cache token counts and implement batch processing',
      'Archive Operation': 'Implement background processing for archive operations',
    },
    'collaboration': {
      'Connection Setup': 'Optimize WebSocket connection pooling',
      'Message Delivery': 'Implement message batching and compression',
      'Presence Update': 'Throttle presence updates and use delta compression',
      'Conflict Detection': 'Optimize conflict detection algorithm with spatial indexing',
      'Large Document Sync': 'Implement incremental sync with change deltas',
    },
  };

  return suggestions[category]?.[operation] || 'Review algorithm efficiency and consider caching strategies';
}

function saveReport(report) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `performance-report-${timestamp}.md`;
  const filepath = path.join(__dirname, '..', 'performance-reports', filename);
  
  // Ensure directory exists
  const dir = path.dirname(filepath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  fs.writeFileSync(filepath, report);
  
  // Also save as latest
  const latestPath = path.join(dir, 'latest.md');
  fs.writeFileSync(latestPath, report);
  
  return filepath;
}

function main() {
  console.log('📊 BookScribe Performance Report Generator\n');
  
  const results = runBenchmarks();
  const report = generateReport(results);
  const filepath = saveReport(report);
  
  console.log('\n📈 Performance Report Generated!');
  console.log(`📄 Report saved to: ${filepath}`);
  
  // Display summary in console
  const lines = report.split('\n');
  const summaryStart = lines.findIndex(line => line.includes('Overall Performance Score'));
  if (summaryStart !== -1) {
    console.log('\n' + lines[summaryStart]);
  }
  
  // Exit with error code if performance is poor
  const scoreMatch = report.match(/Overall Performance Score: ([\d.]+)/);
  if (scoreMatch) {
    const score = parseFloat(scoreMatch[1]);
    if (score < 70) {
      console.log('\n❌ Performance below acceptable threshold (70)');
      process.exit(1);
    } else {
      console.log('\n✅ Performance meets requirements');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  runBenchmarks,
  generateReport,
  saveReport,
};