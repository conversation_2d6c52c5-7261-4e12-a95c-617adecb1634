import { createBrowserClient } from '@/lib/supabase'

/**
 * Wait for session to be fully established after login
 * This helps prevent race conditions where the dashboard loads before cookies are set
 */
export async function waitForSession(maxAttempts = 15, delayMs = 300): Promise<boolean> {
  const supabase = createBrowserClient()
  
  // Initial delay to let auth state settle
  await new Promise(resolve => setTimeout(resolve, 100))
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      // Try multiple methods to verify session
      const { data: { session } } = await supabase.auth.getSession()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (session && user) {
        // Double check by getting session again
        const { data: { session: verifySession } } = await supabase.auth.getSession()
        if (verifySession) {
          return true
        }
      }
    } catch (error) {
      console.warn(`Session check attempt ${i + 1} failed:`, error)
    }
    
    // Wait before next attempt
    await new Promise(resolve => setTimeout(resolve, delayMs))
  }
  
  return false
}

/**
 * Verify user session with retry logic
 */
export async function verifySession() {
  const supabase = createBrowserClient()
  
  try {
    // First try to get the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('Session error:', sessionError)
      return { user: null, error: sessionError }
    }
    
    if (!session) {
      // Try to refresh the session
      const { data: { session: refreshedSession }, error: refreshError } = await supabase.auth.refreshSession()
      
      if (refreshError || !refreshedSession) {
        return { user: null, error: refreshError || new Error('No session found') }
      }
      
      return { user: refreshedSession.user, error: null }
    }
    
    // Get the user to ensure it's valid
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { user: null, error: userError || new Error('User not found') }
    }
    
    return { user, error: null }
  } catch (error) {
    console.error('Session verification failed:', error)
    return { user: null, error }
  }
}