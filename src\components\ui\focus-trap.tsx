import * as React from "react"
import { trapFocus } from "@/lib/utils/accessibility-utils"

export interface FocusTrapProps {
  active?: boolean
  children: React.ReactNode
  className?: string
  returnFocus?: boolean
}

/**
 * Focus trap component for modals, dialogs, and other overlay content
 * Traps keyboard focus within the component while active
 */
export function FocusTrap({ 
  active = true, 
  children, 
  className,
  returnFocus = true 
}: FocusTrapProps) {
  const containerRef = React.useRef<HTMLDivElement>(null)
  const previousActiveElement = React.useRef<HTMLElement | null>(null)

  React.useEffect(() => {
    if (!active || !containerRef.current) return

    // Store the currently focused element
    previousActiveElement.current = document.activeElement as HTMLElement

    // Set up focus trap
    const cleanup = trapFocus(containerRef.current)

    return () => {
      cleanup()
      
      // Return focus to previous element if requested
      if (returnFocus && previousActiveElement.current) {
        previousActiveElement.current.focus()
      }
    }
  }, [active, returnFocus])

  if (!active) {
    return <>{children}</>
  }

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  )
}