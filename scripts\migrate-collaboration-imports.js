#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Files to update imports
const patterns = [
  'src/**/*.{ts,tsx}',
  '!src/lib/services/collaboration-*.ts', // Exclude old collaboration files
  '!src/lib/services/unified-collaboration-service.ts' // Exclude the new file
];

// Import mappings
const importMappings = {
  'collaboration-service': 'unified-collaboration-service',
  'collaboration-service-realtime': 'unified-collaboration-service',
  'collaboration-hub': 'unified-collaboration-service',
  'collaboration-hub-serverless': 'unified-collaboration-service'
};

// Service instance mappings
const serviceMappings = {
  'collaborationService': 'collaborationService',
  'collaborationHub': 'collaborationService',
  'collaborationHubServerless': 'collaborationService',
  'CollaborationService': 'UnifiedCollaborationService',
  'CollaborationHub': 'UnifiedCollaborationService',
  'CollaborationHubServerless': 'UnifiedCollaborationService'
};

function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Update import statements
  Object.entries(importMappings).forEach(([oldImport, newImport]) => {
    const regex = new RegExp(`from ['"]@/lib/services/${oldImport}['"]`, 'g');
    if (regex.test(content)) {
      content = content.replace(regex, `from '@/lib/services/${newImport}'`);
      modified = true;
    }
  });

  // Update service usage
  Object.entries(serviceMappings).forEach(([oldService, newService]) => {
    if (content.includes(oldService) && oldService !== newService) {
      const regex = new RegExp(`\\b${oldService}\\b`, 'g');
      content = content.replace(regex, newService);
      modified = true;
    }
  });

  // Update type imports - consolidate duplicate types
  const typeImportRegex = /import\s+(?:type\s+)?{\s*([^}]+)\s*}\s+from\s+['"]@\/lib\/services\/collaboration-[^'"]+['"]/g;
  const matches = [...content.matchAll(typeImportRegex)];
  
  if (matches.length > 0) {
    const allTypes = new Set();
    matches.forEach(match => {
      const types = match[1].split(',').map(t => t.trim());
      types.forEach(t => allTypes.add(t));
    });

    // Remove old imports
    matches.forEach(match => {
      content = content.replace(match[0], '');
    });

    // Add consolidated import
    if (allTypes.size > 0) {
      const importStatement = `import type { ${Array.from(allTypes).join(', ')} } from '@/lib/services/unified-collaboration-service'`;
      
      // Find a good place to insert (after other imports)
      const firstImportIndex = content.search(/^import/m);
      if (firstImportIndex !== -1) {
        const lines = content.split('\n');
        let insertIndex = 0;
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].startsWith('import')) {
            insertIndex = i + 1;
          } else if (insertIndex > 0 && lines[i].trim() === '') {
            break;
          }
        }
        lines.splice(insertIndex, 0, importStatement);
        content = lines.join('\n');
      }
    }
    
    modified = true;
  }

  if (modified) {
    // Clean up multiple blank lines
    content = content.replace(/\n\n\n+/g, '\n\n');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated: ${filePath}`);
    return true;
  }
  
  return false;
}

function main() {
  console.log('🔄 Migrating collaboration service imports...\n');
  
  let totalFiles = 0;
  let updatedFiles = 0;

  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { 
      cwd: path.join(__dirname, '..'),
      absolute: true,
      ignore: pattern.startsWith('!') ? [] : undefined
    });
    
    files.forEach(file => {
      totalFiles++;
      if (updateFile(file)) {
        updatedFiles++;
      }
    });
  });

  console.log(`\n✨ Migration complete!`);
  console.log(`📊 Updated ${updatedFiles} out of ${totalFiles} files scanned.`);
  
  // List old files that can be deleted
  console.log('\n🗑️  The following old collaboration files can now be deleted:');
  console.log('   - src/lib/services/collaboration-service.ts');
  console.log('   - src/lib/services/collaboration-service-realtime.ts');
  console.log('   - src/lib/services/collaboration-hub.ts');
  console.log('   - src/lib/services/collaboration-hub-serverless.ts');
  console.log('\n⚠️  Review the changes and delete these files manually after testing.');
}

main();