'use client';

import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
  BookOpen, 
  TrendingUp, 
  Users, 
  Target,
  Sparkles,
  ArrowRight
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface EmptyAnalyticsProps {
  hasProjects: boolean;
  userId: string;
}

export function EmptyAnalytics({ hasProjects, userId }: EmptyAnalyticsProps) {
  const router = useRouter();

  if (!hasProjects) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <Card className="border-2 border-dashed border-muted-foreground/20">
          <CardContent className="p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/10 flex items-center justify-center">
              <BookOpen className="w-8 h-8 text-primary" />
            </div>
            
            <h2 className="text-2xl font-literary-display text-foreground mb-4">
              Welcome to Your Writing Journey
            </h2>
            
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto">
              Start writing your first novel with AI-powered assistance. Your analytics will appear here as you make progress.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-medium mb-2">AI-Generated Structure</h3>
                <p className="text-sm text-muted-foreground">
                  Create complete story outlines, characters, and plot points
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-medium mb-2">Progress Tracking</h3>
                <p className="text-sm text-muted-foreground">
                  Monitor your daily writing habits and productivity
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                  <Users className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-medium mb-2">Quality Analysis</h3>
                <p className="text-sm text-muted-foreground">
                  Get real-time feedback on writing quality and consistency
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 justify-center">
              <Button 
                size="lg" 
                onClick={() => router.push('/project-wizard')}
                className="group"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Create Your First Project
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              
              <Button 
                size="lg" 
                variant="outline"
                onClick={() => router.push('/demo')}
              >
                <BookOpen className="w-4 h-4 mr-2" />
                Explore Demo
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Has projects but no writing sessions/data
  return (
    <div className="max-w-3xl mx-auto py-6 sm:py-8 lg:py-10">
      <Card className="border-dashed border-muted-foreground/20">
        <CardContent className="p-8 text-center">
          <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-primary/10 flex items-center justify-center">
            <Target className="w-6 h-6 text-primary" />
          </div>
          
          <h3 className="text-xl font-medium text-foreground mb-3">
            Start Writing to See Analytics
          </h3>
          
          <p className="text-muted-foreground mb-6">
            Your writing analytics will appear here once you begin creating content. 
            Start by generating your story structure or writing your first chapter.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button 
              size="sm"
              onClick={() => router.push('/projects')}
            >
              <BookOpen className="w-4 h-4 mr-2" />
              View My Projects
            </Button>
            
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => router.push('/project-wizard')}
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Create New Project
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}