'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { MemoryDashboard } from '@/components/memory/memory-dashboard'
import { PageLoading } from '@/components/ui/unified-loading'
import { UnifiedErrorBoundary } from '@/components/error/unified-error-boundary'
import { useAsync } from '@/hooks/use-async'
import { Brain, ArrowRight, BookOpen } from 'lucide-react'
import Link from 'next/link'

interface ProjectMemoryStats {
  projectId: string
  projectName: string
  totalTokens: number
  contexts: number
  lastOptimized?: string
}

export default function GlobalMemoryPage() {
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null)

  const { data: projects, loading, error } = useAsync(async () => {
    const response = await fetch('/api/projects')
    if (!response.ok) throw new Error('Failed to load projects')
    const data = await response.json()
    return data.projects || []
  }, [])

  const projectMemoryStats: ProjectMemoryStats[] = projects?.map((project: any) => ({
    projectId: project.id,
    projectName: project.name,
    totalTokens: Math.floor(Math.random() * 100000), // This would come from real memory stats
    contexts: Math.floor(Math.random() * 20) + 1,
    lastOptimized: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
  })) || []

  if (loading) {
    return <PageLoading text="Loading AI memory management..." />
  }

  if (error) {
    return (
      <div className="container-wide mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">Unable to load memory management</h2>
          <p className="text-muted-foreground mb-4">{error.message}</p>
          <Link href="/dashboard">
            <Button>Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <UnifiedErrorBoundary>
      <div className="container-wide mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">AI Memory Management</h1>
          <p className="text-muted-foreground">
            Manage AI context usage across all your projects for better performance and cost efficiency
          </p>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="project" disabled={!selectedProjectId}>
              Project Details
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Global Stats */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Memory Usage</CardTitle>
                  <Brain className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {projectMemoryStats.reduce((sum, p) => sum + p.totalTokens, 0).toLocaleString()} tokens
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Across {projectMemoryStats.length} projects
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Contexts</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {projectMemoryStats.reduce((sum, p) => sum + p.contexts, 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Across all projects
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Usage</CardTitle>
                  <Brain className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(projectMemoryStats.reduce((sum, p) => sum + p.totalTokens, 0) / (projectMemoryStats.length || 1)).toLocaleString()} tokens
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Per project
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Project List */}
            <Card>
              <CardHeader>
                <CardTitle>Projects</CardTitle>
                <CardDescription>
                  Select a project to view and manage its AI memory usage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {projectMemoryStats.map((project) => {
                    const usagePercentage = (project.totalTokens / 150000) * 100
                    const isNearLimit = usagePercentage > 80
                    
                    return (
                      <div
                        key={project.projectId}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent cursor-pointer transition-colors"
                        onClick={() => {
                          setSelectedProjectId(project.projectId)
                          // Switch to project tab
                          const tabsList = document.querySelector('[role="tablist"]')
                          const projectTab = tabsList?.querySelector('[value="project"]') as HTMLElement
                          projectTab?.click()
                        }}
                      >
                        <div className="flex-1">
                          <h4 className="font-medium">{project.projectName}</h4>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-sm text-muted-foreground">
                              {project.totalTokens.toLocaleString()} tokens
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {project.contexts} contexts
                            </span>
                            {project.lastOptimized && (
                              <span className="text-sm text-muted-foreground">
                                Last optimized: {new Date(project.lastOptimized).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="w-32">
                            <div className="h-2 bg-secondary rounded-full overflow-hidden">
                              <div
                                className={`h-full transition-all ${
                                  isNearLimit ? 'bg-warning' : 'bg-primary'
                                }`}
                                style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                              />
                            </div>
                            <span className="text-xs text-muted-foreground mt-1">
                              {Math.round(usagePercentage)}% of limit
                            </span>
                          </div>
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="project" className="space-y-6">
            {selectedProjectId && (
              <MemoryDashboard
                projectId={selectedProjectId}
                maxContextSize={150000}
                onOptimize={async (strategy) => {
                  console.log('Optimizing project:', selectedProjectId, 'with strategy:', strategy)
                }}
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </UnifiedErrorBoundary>
  )
}