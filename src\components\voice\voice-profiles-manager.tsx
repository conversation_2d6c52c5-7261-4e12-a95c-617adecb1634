'use client'

import { useState, useEffect } from 'react'
import { useDebounce } from '@/hooks/use-debounce'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { PaginationControls } from '@/components/ui/pagination'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import {
  Mic,
  BookOpen,
  User,
  Plus,
  Search,
  Edit,
  Trash2,
  Download,
  Loader2,
  AlertCircle,
  FileText
} from 'lucide-react'
import { VoiceTrainerEnhanced } from './voice-trainer-enhanced'

interface VoiceProfile {
  id: string
  name: string
  description?: string
  type: 'author' | 'character' | 'narrator'
  confidence: number
  training_samples_count: number
  total_words_analyzed: number
  created_at: string
  updated_at: string
  project_id?: string
  series_id?: string
  character_id?: string
  is_global: boolean
}

interface PaginationData {
  total: number
  limit: number
  offset: number
  page: number
  totalPages: number
}

interface VoiceProfilesManagerProps {
  projectId?: string
  seriesId?: string
  userId: string
}

export function VoiceProfilesManager({ projectId, seriesId, userId }: VoiceProfilesManagerProps) {
  const [profiles, setProfiles] = useState<VoiceProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [scopeFilter, setScopeFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    limit: 12,
    offset: 0,
    page: 1,
    totalPages: 1
  })
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [selectedProfile, setSelectedProfile] = useState<VoiceProfile | null>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)
  const { toast } = useToast()

  useEffect(() => {
    loadProfiles()
  }, [currentPage, debouncedSearchQuery, typeFilter, scopeFilter])

  const loadProfiles = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: ((currentPage - 1) * pagination.limit).toString(),
      })

      if (debouncedSearchQuery) {
        params.append('search', debouncedSearchQuery)
      }

      if (typeFilter !== 'all') {
        params.append('type', typeFilter)
      }

      if (scopeFilter === 'project' && projectId) {
        params.append('projectId', projectId)
      } else if (scopeFilter === 'series' && seriesId) {
        params.append('seriesId', seriesId)
      } else if (scopeFilter === 'global') {
        params.append('isGlobal', 'true')
      }

      const response = await fetch(`/api/voice-profiles?${params}`)
      if (!response.ok) throw new Error('Failed to load voice profiles')

      const data = await response.json()
      setProfiles(data.profiles || [])

      if (data.pagination) {
        const totalPages = Math.ceil(data.pagination.total / pagination.limit)
        setPagination({
          total: data.pagination.total,
          limit: data.pagination.limit,
          offset: data.pagination.offset,
          page: currentPage,
          totalPages
        })
      }
    } catch (error) {
      logger.error('Error loading voice profiles:', error)
      toast({
        title: "Error",
        description: "Failed to load voice profiles",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    setCurrentPage(1) // Reset to first page when searching
  }

  const handleDeleteProfile = async (profileId: string) => {
    try {
      const response = await fetch(`/api/voice-profiles/${profileId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete voice profile')

      setProfiles(profiles.filter(p => p.id !== profileId))
      toast({
        title: "Success",
        description: "Voice profile deleted successfully"
      })
    } catch (error) {
      logger.error('Error deleting voice profile:', error)
      toast({
        title: "Error",
        description: "Failed to delete voice profile",
        variant: "destructive"
      })
    }
  }

  const handleExportProfile = async (profile: VoiceProfile) => {
    try {
      const response = await fetch(`/api/voice-profiles/${profile.id}`)
      if (!response.ok) throw new Error('Failed to fetch profile')
      
      const { profile: fullProfile } = await response.json()
      
      const exportData = {
        name: fullProfile.name,
        description: fullProfile.description,
        type: fullProfile.type,
        patterns: fullProfile.patterns,
        exported_at: new Date().toISOString(),
        bookscribe_version: '1.0.0'
      }
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `voice-profile-${profile.name.toLowerCase().replace(/\s+/g, '-')}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Success",
        description: "Voice profile exported successfully"
      })
    } catch (error) {
      logger.error('Error exporting voice profile:', error)
      toast({
        title: "Error",
        description: "Failed to export voice profile",
        variant: "destructive"
      })
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'author':
        return <BookOpen className="h-4 w-4" />
      case 'character':
        return <User className="h-4 w-4" />
      case 'narrator':
        return <Mic className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-success'
    if (confidence >= 0.6) return 'text-warning'
    return 'text-error'
  }

  const getScopeLabel = (profile: VoiceProfile) => {
    if (profile.character_id) return 'Character'
    if (profile.project_id) return 'Project'
    if (profile.series_id) return 'Series'
    if (profile.is_global) return 'Global'
    return 'Unknown'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Voice Profiles</h2>
          <p className="text-sm text-muted-foreground">
            Manage your voice profiles for consistent writing style ({pagination.total} total)
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Profile
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search voice profiles..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All types" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="author">Author</SelectItem>
            <SelectItem value="character">Character</SelectItem>
            <SelectItem value="narrator">Narrator</SelectItem>
          </SelectContent>
        </Select>

        <Select value={scopeFilter} onValueChange={setScopeFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All scopes" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Scopes</SelectItem>
            <SelectItem value="global">Global</SelectItem>
            {seriesId && <SelectItem value="series">This Series</SelectItem>}
            {projectId && <SelectItem value="project">This Project</SelectItem>}
          </SelectContent>
        </Select>
      </div>

      {/* Profiles Grid */}
      {profiles.length === 0 ? (
        <div className="text-center py-12">
          <Mic className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {searchQuery || typeFilter !== 'all' || scopeFilter !== 'all' 
              ? 'No voice profiles found' 
              : 'No voice profiles created yet'
            }
          </h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            {searchQuery || typeFilter !== 'all' || scopeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Create your first voice profile to maintain consistent writing style'
            }
          </p>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Voice Profile
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-5 lg:gap-6">
          {profiles.map((profile) => (
            <Card key={profile.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(profile.type)}
                    <CardTitle className="text-lg">{profile.name}</CardTitle>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedProfile(profile)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleExportProfile(profile)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteProfile(profile.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="capitalize">
                    {profile.type}
                  </Badge>
                  <Badge variant="secondary">
                    {getScopeLabel(profile)}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent>
                {profile.description && (
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {profile.description}
                  </p>
                )}
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Confidence</span>
                    <span className={`font-medium ${getConfidenceColor(profile.confidence)}`}>
                      {Math.round(profile.confidence * 100)}%
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Samples</span>
                    <span className="font-medium">
                      {profile.training_samples_count}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Words Analyzed</span>
                    <span className="font-medium">
                      {profile.total_words_analyzed.toLocaleString()}
                    </span>
                  </div>
                </div>
                
                <div className="pt-3 mt-3 border-t text-xs text-muted-foreground">
                  Created {new Date(profile.created_at).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination Controls */}
      {pagination.totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={pagination.totalPages}
          onPageChange={handlePageChange}
          showInfo={true}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          className="mt-6"
        />
      )}

      {/* Create Profile Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl lg:max-w-3xl xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Create Voice Profile</DialogTitle>
          </DialogHeader>
          <VoiceTrainerEnhanced
            projectId={projectId}
            seriesId={seriesId}
            onProfileCreated={() => {
              setShowCreateDialog(false)
              loadProfiles()
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Profile Dialog */}
      {selectedProfile && (
        <Dialog open={!!selectedProfile} onOpenChange={() => setSelectedProfile(null)}>
          <DialogContent className="max-w-2xl lg:max-w-3xl xl:max-w-4xl">
            <DialogHeader>
              <DialogTitle>Edit Voice Profile</DialogTitle>
            </DialogHeader>
            <VoiceTrainerEnhanced
              projectId={projectId}
              seriesId={seriesId}
              existingProfile={selectedProfile}
              onProfileCreated={() => {
                setSelectedProfile(null)
                loadProfiles()
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}