import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

// GET - Fetch all universe rules for a series
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Get category filter from query params
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') || undefined

    const service = getSeriesService(true)
    const { rules, error } = await service.getSeriesUniverseRules(seriesId, category)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch universe rules' }, { status: 500 })
    }

    return NextResponse.json({ rules })
  } catch (error) {
    logger.error('Error in universe rules GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create a new universe rule
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.rule_category || !body.rule_title || !body.rule_description) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const service = getSeriesService(true)
    const { rule, error } = await service.createUniverseRule({
      series_id: seriesId,
      ...body
    })

    if (error) {
      return NextResponse.json({ error: 'Failed to create universe rule' }, { status: 500 })
    }

    return NextResponse.json({ rule }, { status: 201 })
  } catch (error) {
    logger.error('Error in universe rules POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}