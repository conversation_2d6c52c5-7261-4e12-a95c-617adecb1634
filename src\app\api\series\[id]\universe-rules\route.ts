import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'

// GET - Fetch all universe rules for a series
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get category filter from query params
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') || undefined

    const service = getSeriesService(true)
    const { rules, error } = await service.getSeriesUniverseRules(seriesId, category)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch universe rules' }, { status: 500 })
    }

    return NextResponse.json({ rules })
  } catch (error) {
    logger.error('Error in universe rules GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create a new universe rule
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.rule_category || !body.rule_title || !body.rule_description) {
      return NextResponse.json({ 
        error: 'rule_category, rule_title, and rule_description are required' 
      }, { status: 400 })
    }

    const service = getSeriesService(true)
    const { rule, error } = await service.createUniverseRule({
      series_id: seriesId,
      ...body
    })

    if (error) {
      return NextResponse.json({ error: 'Failed to create universe rule' }, { status: 500 })
    }

    return NextResponse.json({ rule }, { status: 201 })
  } catch (error) {
    logger.error('Error in universe rules POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}