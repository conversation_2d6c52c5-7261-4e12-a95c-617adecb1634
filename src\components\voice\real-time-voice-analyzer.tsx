'use client'

import { useEffect, useState, useRef, use<PERSON><PERSON>back } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  AlertCircle, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Volume2,
  VolumeX,
  RefreshCw,
  Settings,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { useDebounce } from '@/hooks/use-debounce'
import { toast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'

interface VoiceMetrics {
  consistency_score: number
  sentence_complexity: number
  vocabulary_level: number
  emotional_tone: number
  pacing: number
  dialogue_ratio: number
  readability_score: number
  avg_sentence_length: number
  unique_words_ratio: number
}

interface VoiceDeviation {
  metric: string
  current: number
  expected: number
  deviation: number
  severity: 'low' | 'medium' | 'high'
  suggestion: string
}

interface RealTimeVoiceAnalyzerProps {
  projectId: string
  chapterId?: string
  voiceProfileId: string
  content: string
  onDeviationDetected?: (deviations: VoiceDeviation[]) => void
  className?: string
}

export function RealTimeVoiceAnalyzer({
  projectId,
  chapterId,
  voiceProfileId,
  content,
  onDeviationDetected,
  className
}: RealTimeVoiceAnalyzerProps) {
  const [isEnabled, setIsEnabled] = useState(true)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [metrics, setMetrics] = useState<VoiceMetrics | null>(null)
  const [deviations, setDeviations] = useState<VoiceDeviation[]>([])
  const [voiceProfile, setVoiceProfile] = useState<any>(null)
  const [sensitivity, setSensitivity] = useState(0.15) // 15% deviation threshold
  const analysisRef = useRef<AbortController | null>(null)
  
  const debouncedContent = useDebounce(content, 1000) // Wait 1 second after typing stops

  // Fetch voice profile
  useEffect(() => {
    if (!voiceProfileId) return

    const fetchProfile = async () => {
      try {
        const response = await fetch(`/api/voice-profiles/${voiceProfileId}`)
        if (response.ok) {
          const profile = await response.json()
          setVoiceProfile(profile)
        }
      } catch (error) {
        logger.error('Failed to fetch voice profile', { voiceProfileId }, error as Error)
      }
    }

    fetchProfile()
  }, [voiceProfileId])

  // Analyze voice consistency
  const analyzeVoice = useCallback(async (text: string) => {
    if (!isEnabled || !voiceProfile || text.length < 100) {
      return
    }

    // Cancel previous analysis
    if (analysisRef.current) {
      analysisRef.current.abort()
    }

    const controller = new AbortController()
    analysisRef.current = controller

    setIsAnalyzing(true)

    try {
      const response = await fetch('/api/analysis/voice-consistency', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: text,
          voice_profile_id: voiceProfileId,
          project_id: projectId,
          chapter_id: chapterId
        }),
        signal: controller.signal
      })

      if (!response.ok) {
        throw new Error('Analysis failed')
      }

      const result = await response.json()
      
      setMetrics(result.metrics)
      
      // Calculate deviations
      const newDeviations = calculateDeviations(result.metrics, voiceProfile.metrics, sensitivity)
      setDeviations(newDeviations)
      
      if (onDeviationDetected && newDeviations.length > 0) {
        onDeviationDetected(newDeviations)
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        logger.error('Voice analysis error', { textLength: text.length }, error as Error)
      }
    } finally {
      setIsAnalyzing(false)
    }
  }, [isEnabled, voiceProfile, voiceProfileId, projectId, chapterId, sensitivity, onDeviationDetected])

  // Run analysis when content changes
  useEffect(() => {
    if (debouncedContent && debouncedContent.length >= 100) {
      analyzeVoice(debouncedContent)
    }
  }, [debouncedContent, analyzeVoice])

  // Calculate deviations from expected voice profile
  const calculateDeviations = (
    current: VoiceMetrics, 
    expected: any, 
    threshold: number
  ): VoiceDeviation[] => {
    const deviations: VoiceDeviation[] = []
    
    const metricMap: Record<string, { display: string; suggestion: string }> = {
      sentence_complexity: {
        display: 'Sentence Complexity',
        suggestion: current.sentence_complexity > expected.sentence_complexity
          ? 'Try using shorter, simpler sentences'
          : 'Consider adding more complex sentence structures'
      },
      vocabulary_level: {
        display: 'Vocabulary Level',
        suggestion: current.vocabulary_level > expected.vocabulary_level
          ? 'Use simpler, more accessible words'
          : 'Incorporate more sophisticated vocabulary'
      },
      emotional_tone: {
        display: 'Emotional Intensity',
        suggestion: current.emotional_tone > expected.emotional_tone
          ? 'Tone down the emotional intensity'
          : 'Add more emotional depth and feeling'
      },
      pacing: {
        display: 'Pacing',
        suggestion: current.pacing > expected.pacing
          ? 'Slow down the pacing with more description'
          : 'Pick up the pace with shorter sentences and more action'
      },
      dialogue_ratio: {
        display: 'Dialogue Balance',
        suggestion: current.dialogue_ratio > expected.dialogue_ratio
          ? 'Add more narrative and description'
          : 'Include more dialogue to break up narrative'
      }
    }

    Object.entries(metricMap).forEach(([key, config]) => {
      const currentValue = current[key as keyof VoiceMetrics]
      const expectedValue = expected[key]
      
      if (typeof currentValue === 'number' && typeof expectedValue === 'number') {
        const deviation = Math.abs(currentValue - expectedValue)
        const percentDeviation = deviation / expectedValue
        
        if (percentDeviation > threshold) {
          deviations.push({
            metric: config.display,
            current: currentValue,
            expected: expectedValue,
            deviation: percentDeviation,
            severity: percentDeviation > threshold * 2 ? 'high' : 
                     percentDeviation > threshold * 1.5 ? 'medium' : 'low',
            suggestion: config.suggestion
          })
        }
      }
    })

    return deviations.sort((a, b) => b.deviation - a.deviation)
  }

  const getConsistencyColor = (score: number) => {
    if (score >= 0.8) return 'text-success dark:text-green-400'
    if (score >= 0.6) return 'text-warning dark:text-yellow-400'
    return 'text-error dark:text-red-400'
  }

  const getDeviationIcon = (current: number, expected: number) => {
    const diff = current - expected
    if (Math.abs(diff) < 0.05) return <Minus className="h-4 w-4 text-gray-400" />
    if (diff > 0) return <TrendingUp className="h-4 w-4 text-warning" />
    return <TrendingDown className="h-4 w-4 text-info" />
  }

  if (!voiceProfile) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Select a voice profile to enable real-time analysis
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">
            Real-Time Voice Analysis
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => analyzeVoice(content)}
              disabled={isAnalyzing || !content}
            >
              <RefreshCw className={`h-3 w-3 ${isAnalyzing ? 'animate-spin' : ''}`} />
            </Button>
            <div className="flex items-center gap-2">
              <Label htmlFor="voice-enabled" className="text-xs">
                {isEnabled ? <Volume2 className="h-3 w-3" /> : <VolumeX className="h-3 w-3" />}
              </Label>
              <Switch
                id="voice-enabled"
                checked={isEnabled}
                onCheckedChange={setIsEnabled}
                className="scale-75"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 pb-4">
        {metrics && (
          <>
            {/* Overall Consistency Score */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Voice Consistency</span>
                <span className={`font-medium ${getConsistencyColor(metrics.consistency_score)}`}>
                  {Math.round(metrics.consistency_score * 100)}%
                </span>
              </div>
              <Progress 
                value={metrics.consistency_score * 100} 
                className="h-2"
              />
            </div>

            {/* Deviations Alert */}
            {deviations.length > 0 && (
              <Alert className="py-3">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  <div className="font-medium mb-2">
                    {deviations.length} voice {deviations.length === 1 ? 'deviation' : 'deviations'} detected
                  </div>
                  <div className="space-y-2">
                    {deviations.slice(0, 3).map((deviation, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <Badge 
                          variant={deviation.severity === 'high' ? 'destructive' : 
                                  deviation.severity === 'medium' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {Math.round(deviation.deviation * 100)}%
                        </Badge>
                        <div className="flex-1">
                          <div className="flex items-center gap-1 text-xs font-medium">
                            {deviation.metric}
                            {getDeviationIcon(deviation.current, deviation.expected)}
                          </div>
                          <p className="text-xs text-muted-foreground mt-0.5">
                            {deviation.suggestion}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </AlertDescription>
              </Alert>
              )}

            {/* Metrics Grid */}
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Complexity</span>
                  <span className="font-medium">
                    {Math.round(metrics.sentence_complexity * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Vocabulary</span>
                  <span className="font-medium">
                    {Math.round(metrics.vocabulary_level * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Emotion</span>
                  <span className="font-medium">
                    {Math.round(metrics.emotional_tone * 100)}%
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Pacing</span>
                  <span className="font-medium">
                    {Math.round(metrics.pacing * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Dialogue</span>
                  <span className="font-medium">
                    {Math.round(metrics.dialogue_ratio * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Readability</span>
                  <span className="font-medium">
                    {Math.round(metrics.readability_score)}
                  </span>
                </div>
              </div>
            </div>

            {/* Sensitivity Control */}
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Sensitivity</span>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 px-2 text-xs"
                  onClick={() => {
                    const newSensitivity = sensitivity === 0.1 ? 0.15 : 
                                          sensitivity === 0.15 ? 0.2 : 0.1
                    setSensitivity(newSensitivity)
                    toast({
                      description: `Sensitivity set to ${newSensitivity * 100}% deviation threshold`
                    })
                  }}
                >
                  <Settings className="h-3 w-3 mr-1" />
                  {sensitivity * 100}%
                </Button>
              </div>
            </div>
          </>
        )}

        {!metrics && isEnabled && content.length >= 100 && !isAnalyzing && (
          <div className="text-center py-4 text-sm text-muted-foreground">
            Analysis will begin shortly...
          </div>
        )}

        {content.length < 100 && (
          <div className="text-center py-4 text-sm text-muted-foreground">
            Write at least 100 characters to analyze voice consistency
          </div>
        )}
      </CardContent>
    </Card>
  )
}