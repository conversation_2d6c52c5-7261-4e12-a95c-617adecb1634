import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyCollaborationAccess } from '@/lib/api/collaboration-middleware'
import { ServiceManager } from '@/lib/services/service-manager'
import { generalLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for collaboration join (20 joins per hour)
    const clientIP = getClientIP(request)
    const rateLimitResult = generalLimiter.check(20, clientIP)
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult.remaining, rateLimitResult.reset)
    }

    const body = await request.json()
    const { sessionId, role = 'viewer' } = body

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Verify user has access to the collaboration session
    const authResult = await verifyCollaborationAccess(sessionId, 'view')
    if (!authResult.success) {
      return authResult.response!
    }

    // Enforce role restrictions based on user's project permissions
    if (role === 'editor' && !authResult.permissions?.canEdit) {
      return NextResponse.json(
        { error: 'Your project role only allows viewer access' },
        { status: 403 }
      )
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationService = await serviceManager.getCollaborationHub()
    if (!collaborationService) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationService.joinSession(
      sessionId,
      authResult.user!.id,
      role
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to join session' },
        { status: 400 }
      )
    }

    return NextResponse.json(result.data)
  } catch (error) {
    logger.error('Collaboration join error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}