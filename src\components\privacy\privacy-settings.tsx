'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { UnifiedLoadingState } from '@/components/ui/unified-loading';
import { UnifiedEmptyState } from '@/components/ui/unified-empty-state';
import { useToast } from '@/hooks/use-toast';
import { Shield, Download, Trash2, AlertCircle, CheckCircle, Clock, FileText } from 'lucide-react';
import { GDPRRequestType, GDPRRequestStatus } from '@/lib/privacy/gdpr-service';

interface ConsentSettings {
  marketing: boolean;
  analytics: boolean;
  thirdPartySharing: boolean;
  aiDataProcessing: boolean;
  performanceTracking: boolean;
}

interface GDPRRequest {
  id: string;
  type: GDPRRequestType;
  status: GDPRRequestStatus;
  requestedAt: string;
  processedAt?: string;
  downloadUrl?: string;
  reason?: string;
}

export function PrivacySettings() {
  const [consent, setConsent] = useState<ConsentSettings>({
    marketing: false,
    analytics: false,
    thirdPartySharing: false,
    aiDataProcessing: true,
    performanceTracking: false
  });
  
  const [requests, setRequests] = useState<GDPRRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [requestType, setRequestType] = useState<GDPRRequestType>(GDPRRequestType.ACCESS);
  const [requestReason, setRequestReason] = useState('');
  const [emailVerification, setEmailVerification] = useState('');
  const { toast } = useToast();

  // Load consent status and requests
  useEffect(() => {
    loadPrivacyData();
  }, []);

  const loadPrivacyData = async () => {
    try {
      setLoading(true);
      
      // Load consent status
      const consentRes = await fetch('/api/gdpr/consent');
      if (consentRes.ok) {
        const data = await consentRes.json();
        if (data.consent) {
          setConsent({
            marketing: data.consent.marketing,
            analytics: data.consent.analytics,
            thirdPartySharing: data.consent.thirdPartySharing,
            aiDataProcessing: data.consent.aiDataProcessing,
            performanceTracking: data.consent.performanceTracking
          });
        }
      }

      // Load GDPR requests
      const requestsRes = await fetch('/api/gdpr/request');
      if (requestsRes.ok) {
        const data = await requestsRes.json();
        setRequests(data.requests || []);
      }
    } catch (error) {
      toast({
        title: 'Failed to load privacy settings',
        description: 'Please try again later.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleConsentChange = async (key: keyof ConsentSettings, value: boolean) => {
    const newConsent = { ...consent, [key]: value };
    setConsent(newConsent);
    
    try {
      setSaving(true);
      const res = await fetch('/api/gdpr/consent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newConsent)
      });

      if (!res.ok) throw new Error('Failed to update consent');
      
      const data = await res.json();
      toast({
        title: 'Privacy preferences updated',
        description: data.message || 'Your consent settings have been saved.'
      });
    } catch (error) {
      toast({
        title: 'Failed to update preferences',
        description: 'Please try again later.',
        variant: 'destructive'
      });
      // Revert on error
      setConsent(consent);
    } finally {
      setSaving(false);
    }
  };

  const handleGDPRRequest = async () => {
    if (!emailVerification) {
      toast({
        title: 'Email verification required',
        description: 'Please enter your email address to verify your identity.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSaving(true);
      const res = await fetch('/api/gdpr/request', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: requestType,
          reason: requestReason,
          emailVerification,
          dataCategories: requestType === GDPRRequestType.ACCESS ? 
            ['personal_info', 'content_data', 'usage_data', 'ai_interactions'] : undefined
        })
      });

      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || 'Failed to submit request');
      }
      
      const data = await res.json();
      toast({
        title: 'Request submitted successfully',
        description: data.message
      });
      
      // Reset form
      setRequestReason('');
      setEmailVerification('');
      
      // Reload requests
      await loadPrivacyData();
    } catch (error) {
      toast({
        title: 'Failed to submit request',
        description: error instanceof Error ? error.message : 'Please try again later.',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (status: GDPRRequestStatus) => {
    const variants: Record<GDPRRequestStatus, { variant: 'default' | 'secondary' | 'destructive' | 'outline', icon: React.ReactNode }> = {
      [GDPRRequestStatus.PENDING]: { variant: 'secondary', icon: <Clock className="w-3 h-3" /> },
      [GDPRRequestStatus.IN_PROGRESS]: { variant: 'default', icon: <Clock className="w-3 h-3" /> },
      [GDPRRequestStatus.COMPLETED]: { variant: 'outline', icon: <CheckCircle className="w-3 h-3" /> },
      [GDPRRequestStatus.REJECTED]: { variant: 'destructive', icon: <AlertCircle className="w-3 h-3" /> },
      [GDPRRequestStatus.EXPIRED]: { variant: 'secondary', icon: <AlertCircle className="w-3 h-3" /> }
    };

    const { variant, icon } = variants[status];
    return (
      <Badge variant={variant} className="flex items-center gap-1">
        {icon}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (loading) {
    return <UnifiedLoadingState message="Loading privacy settings..." />;
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center gap-3">
        <Shield className="w-8 h-8 text-primary" />
        <div>
          <h1 className="text-2xl font-semibold">Privacy Settings</h1>
          <p className="text-muted-foreground">Manage your data and privacy preferences</p>
        </div>
      </div>

      <Tabs defaultValue="consent" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="consent">Consent Settings</TabsTrigger>
          <TabsTrigger value="requests">Data Requests</TabsTrigger>
          <TabsTrigger value="active">Active Requests</TabsTrigger>
        </TabsList>

        <TabsContent value="consent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Data Processing Consent</CardTitle>
              <CardDescription>
                Control how we process your data. Some features may be limited if you opt out of certain data processing.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="marketing">Marketing Communications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive updates about new features and writing tips
                    </p>
                  </div>
                  <Switch
                    id="marketing"
                    checked={consent.marketing}
                    onCheckedChange={(value) => handleConsentChange('marketing', value)}
                    disabled={saving}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="analytics">Analytics & Insights</Label>
                    <p className="text-sm text-muted-foreground">
                      Help us improve by sharing usage analytics
                    </p>
                  </div>
                  <Switch
                    id="analytics"
                    checked={consent.analytics}
                    onCheckedChange={(value) => handleConsentChange('analytics', value)}
                    disabled={saving}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="aiProcessing">AI Data Processing</Label>
                    <p className="text-sm text-muted-foreground">
                      Required for AI-powered writing features
                    </p>
                  </div>
                  <Switch
                    id="aiProcessing"
                    checked={consent.aiDataProcessing}
                    onCheckedChange={(value) => handleConsentChange('aiDataProcessing', value)}
                    disabled={saving}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="performance">Performance Tracking</Label>
                    <p className="text-sm text-muted-foreground">
                      Monitor app performance to ensure smooth experience
                    </p>
                  </div>
                  <Switch
                    id="performance"
                    checked={consent.performanceTracking}
                    onCheckedChange={(value) => handleConsentChange('performanceTracking', value)}
                    disabled={saving}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="thirdParty">Third-Party Sharing</Label>
                    <p className="text-sm text-muted-foreground">
                      We never share your content with third parties
                    </p>
                  </div>
                  <Switch
                    id="thirdParty"
                    checked={consent.thirdPartySharing}
                    onCheckedChange={(value) => handleConsentChange('thirdPartySharing', value)}
                    disabled={saving}
                  />
                </div>
              </div>

              {!consent.aiDataProcessing && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    AI-powered features will be disabled without data processing consent. This includes writing suggestions, character development, and story analysis.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Submit Data Request</CardTitle>
              <CardDescription>
                Exercise your data rights under GDPR. You can request access to your data, deletion, or other actions.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="requestType">Request Type</Label>
                <Select value={requestType} onValueChange={(value) => setRequestType(value as GDPRRequestType)}>
                  <SelectTrigger id="requestType">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={GDPRRequestType.ACCESS}>
                      <div className="flex items-center gap-2">
                        <Download className="w-4 h-4" />
                        <span>Access My Data</span>
                      </div>
                    </SelectItem>
                    <SelectItem value={GDPRRequestType.ERASURE}>
                      <div className="flex items-center gap-2">
                        <Trash2 className="w-4 h-4" />
                        <span>Delete My Account</span>
                      </div>
                    </SelectItem>
                    <SelectItem value={GDPRRequestType.PORTABILITY}>
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        <span>Export for Portability</span>
                      </div>
                    </SelectItem>
                    <SelectItem value={GDPRRequestType.RECTIFICATION}>
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        <span>Correct My Data</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason">Reason (Optional)</Label>
                <Textarea
                  id="reason"
                  placeholder="Provide additional context for your request..."
                  value={requestReason}
                  onChange={(e) => setRequestReason(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Verification</Label>
                <input
                  id="email"
                  type="email"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter your account email"
                  value={emailVerification}
                  onChange={(e) => setEmailVerification(e.target.value)}
                />
              </div>

              {requestType === GDPRRequestType.ERASURE && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Account deletion is permanent and cannot be undone. All your projects, characters, and content will be permanently deleted.
                  </AlertDescription>
                </Alert>
              )}

              <Button 
                onClick={handleGDPRRequest} 
                disabled={saving || !emailVerification}
                className="w-full"
              >
                {saving ? 'Submitting...' : 'Submit Request'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="active" className="space-y-4">
          {requests.length === 0 ? (
            <UnifiedEmptyState
              icon={Shield}
              title="No active requests"
              description="You haven't submitted any data requests yet."
            />
          ) : (
            <div className="space-y-4">
              {requests.map((request) => (
                <Card key={request.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        {request.type === GDPRRequestType.ACCESS && 'Data Access Request'}
                        {request.type === GDPRRequestType.ERASURE && 'Account Deletion Request'}
                        {request.type === GDPRRequestType.PORTABILITY && 'Data Portability Request'}
                        {request.type === GDPRRequestType.RECTIFICATION && 'Data Correction Request'}
                      </CardTitle>
                      {getStatusBadge(request.status)}
                    </div>
                    <CardDescription>
                      Submitted on {new Date(request.requestedAt).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {request.reason && (
                      <div>
                        <p className="text-sm font-medium">Reason</p>
                        <p className="text-sm text-muted-foreground">{request.reason}</p>
                      </div>
                    )}
                    
                    {request.status === GDPRRequestStatus.IN_PROGRESS && (
                      <div className="space-y-2">
                        <p className="text-sm text-muted-foreground">Processing your request...</p>
                        <Progress value={50} className="h-2" />
                      </div>
                    )}

                    {request.status === GDPRRequestStatus.COMPLETED && request.downloadUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = request.downloadUrl!}
                        className="w-full"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Download Data
                      </Button>
                    )}

                    {request.processedAt && (
                      <p className="text-xs text-muted-foreground">
                        Processed on {new Date(request.processedAt).toLocaleDateString()}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}