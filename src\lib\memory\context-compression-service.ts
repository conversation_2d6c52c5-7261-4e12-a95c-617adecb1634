import { logger } from '@/lib/services/logger'
import { createServerClient } from '@/lib/supabase'
import { openai } from '@/lib/ai/openai-client'
import type { CompressionStrategy } from './types'

interface CompressionContext {
  type: 'chapter' | 'character' | 'location' | 'event' | 'dialogue'
  content: string
  metadata: Record<string, any>
  importance: number
  lastAccessed: Date
}

interface CompressionResult {
  original: string
  compressed: string
  compressionRatio: number
  preservedElements: string[]
  tokensSaved: number
}

export class ContextCompressionService {
  private static instance: ContextCompressionService

  private constructor() {}

  static getInstance(): ContextCompressionService {
    if (!ContextCompressionService.instance) {
      ContextCompressionService.instance = new ContextCompressionService()
    }
    return ContextCompressionService.instance
  }

  /**
   * Compress content based on strategy and context type
   */
  async compressContent(
    content: string,
    contextType: CompressionContext['type'],
    strategy: CompressionStrategy = 'balanced'
  ): Promise<CompressionResult> {
    try {
      const startTokens = this.estimateTokens(content)
      
      let compressed: string
      let preservedElements: string[] = []

      switch (contextType) {
        case 'chapter':
          const chapterResult = await this.compressChapter(content, strategy)
          compressed = chapterResult.summary
          preservedElements = chapterResult.keyElements
          break
          
        case 'character':
          const charResult = await this.compressCharacter(content, strategy)
          compressed = charResult.profile
          preservedElements = charResult.traits
          break
          
        case 'dialogue':
          const dialogueResult = await this.compressDialogue(content, strategy)
          compressed = dialogueResult.summary
          preservedElements = dialogueResult.speakers
          break
          
        case 'location':
          const locationResult = await this.compressLocation(content, strategy)
          compressed = locationResult.description
          preservedElements = locationResult.features
          break
          
        case 'event':
          const eventResult = await this.compressEvent(content, strategy)
          compressed = eventResult.summary
          preservedElements = eventResult.outcomes
          break
          
        default:
          compressed = await this.genericCompress(content, strategy)
      }

      const endTokens = this.estimateTokens(compressed)
      
      return {
        original: content,
        compressed,
        compressionRatio: 1 - (endTokens / startTokens),
        preservedElements,
        tokensSaved: startTokens - endTokens
      }
    } catch (error) {
      logger.error('Content compression failed:', error)
      // Return original content if compression fails
      return {
        original: content,
        compressed: content,
        compressionRatio: 0,
        preservedElements: [],
        tokensSaved: 0
      }
    }
  }

  /**
   * Compress chapter content while preserving key narrative elements
   */
  private async compressChapter(content: string, strategy: CompressionStrategy): Promise<{
    summary: string
    keyElements: string[]
  }> {
    const targetLength = this.getTargetLength(content, strategy)
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are a narrative compression specialist. Compress the following chapter to approximately ${targetLength} words while preserving:
- Main plot events and turning points
- Character development moments
- Important dialogue exchanges
- Setting changes
- Foreshadowing and callbacks
Format: Provide a flowing narrative summary that maintains story continuity.`
          },
          {
            role: 'user',
            content
          }
        ],
        temperature: 0.3,
        max_tokens: Math.min(targetLength * 2, 4000)
      })

      const summary = response.choices[0].message.content || ''
      
      // Extract key elements
      const keyElements = this.extractKeyElements(content, summary)
      
      return { summary, keyElements }
    } catch (error) {
      logger.error('Chapter compression failed:', error)
      return {
        summary: this.fallbackCompress(content, targetLength),
        keyElements: []
      }
    }
  }

  /**
   * Compress character information while preserving personality
   */
  private async compressCharacter(content: string, strategy: CompressionStrategy): Promise<{
    profile: string
    traits: string[]
  }> {
    const targetLength = this.getTargetLength(content, strategy)
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `Compress this character profile to ${targetLength} words while preserving:
- Core personality traits
- Key relationships
- Motivations and goals
- Distinctive speech patterns
- Physical characteristics
- Character arc milestones`
          },
          {
            role: 'user',
            content
          }
        ],
        temperature: 0.3,
        max_tokens: targetLength * 2
      })

      const profile = response.choices[0].message.content || ''
      const traits = this.extractCharacterTraits(content)
      
      return { profile, traits }
    } catch (error) {
      logger.error('Character compression failed:', error)
      return {
        profile: this.fallbackCompress(content, targetLength),
        traits: []
      }
    }
  }

  /**
   * Compress dialogue while preserving voice and meaning
   */
  private async compressDialogue(content: string, strategy: CompressionStrategy): Promise<{
    summary: string
    speakers: string[]
  }> {
    const targetLength = this.getTargetLength(content, strategy)
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `Compress this dialogue to ${targetLength} words while preserving:
- Key information exchanged
- Emotional beats and subtext
- Character voice distinctions
- Plot-relevant revelations
Format: Use indirect discourse with occasional direct quotes for impactful lines.`
          },
          {
            role: 'user',
            content
          }
        ],
        temperature: 0.3,
        max_tokens: targetLength * 2
      })

      const summary = response.choices[0].message.content || ''
      const speakers = this.extractSpeakers(content)
      
      return { summary, speakers }
    } catch (error) {
      logger.error('Dialogue compression failed:', error)
      return {
        summary: this.fallbackCompress(content, targetLength),
        speakers: []
      }
    }
  }

  /**
   * Compress location descriptions
   */
  private async compressLocation(content: string, strategy: CompressionStrategy): Promise<{
    description: string
    features: string[]
  }> {
    const targetLength = this.getTargetLength(content, strategy)
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `Compress this location description to ${targetLength} words while preserving:
- Atmosphere and mood
- Key physical features
- Historical significance
- Character associations
- Sensory details`
          },
          {
            role: 'user',
            content
          }
        ],
        temperature: 0.3,
        max_tokens: targetLength * 2
      })

      const description = response.choices[0].message.content || ''
      const features = this.extractLocationFeatures(content)
      
      return { description, features }
    } catch (error) {
      logger.error('Location compression failed:', error)
      return {
        description: this.fallbackCompress(content, targetLength),
        features: []
      }
    }
  }

  /**
   * Compress event descriptions
   */
  private async compressEvent(content: string, strategy: CompressionStrategy): Promise<{
    summary: string
    outcomes: string[]
  }> {
    const targetLength = this.getTargetLength(content, strategy)
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `Compress this event to ${targetLength} words while preserving:
- What happened
- Who was involved
- Consequences and outcomes
- Emotional impact
- Plot significance`
          },
          {
            role: 'user',
            content
          }
        ],
        temperature: 0.3,
        max_tokens: targetLength * 2
      })

      const summary = response.choices[0].message.content || ''
      const outcomes = this.extractOutcomes(content)
      
      return { summary, outcomes }
    } catch (error) {
      logger.error('Event compression failed:', error)
      return {
        summary: this.fallbackCompress(content, targetLength),
        outcomes: []
      }
    }
  }

  /**
   * Generic compression for unspecified content types
   */
  private async genericCompress(content: string, strategy: CompressionStrategy): Promise<string> {
    const targetLength = this.getTargetLength(content, strategy)
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `Compress this text to approximately ${targetLength} words while preserving the most important information and maintaining coherence.`
          },
          {
            role: 'user',
            content
          }
        ],
        temperature: 0.3,
        max_tokens: targetLength * 2
      })

      return response.choices[0].message.content || this.fallbackCompress(content, targetLength)
    } catch (error) {
      logger.error('Generic compression failed:', error)
      return this.fallbackCompress(content, targetLength)
    }
  }

  /**
   * Fallback compression using simple truncation
   */
  private fallbackCompress(content: string, targetLength: number): string {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim())
    const targetSentences = Math.ceil(sentences.length * (targetLength / this.countWords(content)))
    
    return sentences.slice(0, targetSentences).join('. ') + '.'
  }

  /**
   * Get target length based on strategy
   */
  private getTargetLength(content: string, strategy: CompressionStrategy): number {
    const wordCount = this.countWords(content)
    
    switch (strategy) {
      case 'aggressive':
        return Math.max(100, Math.floor(wordCount * 0.2))
      case 'balanced':
        return Math.max(200, Math.floor(wordCount * 0.4))
      case 'conservative':
        return Math.max(300, Math.floor(wordCount * 0.6))
      default:
        return Math.floor(wordCount * 0.4)
    }
  }

  /**
   * Extract key narrative elements
   */
  private extractKeyElements(content: string, summary: string): string[] {
    const elements: string[] = []
    
    // Extract character names (capitalized words that appear multiple times)
    const words = content.split(/\s+/)
    const nameFreq = new Map<string, number>()
    
    words.forEach(word => {
      if (/^[A-Z][a-z]+/.test(word)) {
        nameFreq.set(word, (nameFreq.get(word) || 0) + 1)
      }
    })
    
    nameFreq.forEach((count, name) => {
      if (count > 2) elements.push(name)
    })
    
    return elements.slice(0, 10)
  }

  /**
   * Extract character traits
   */
  private extractCharacterTraits(content: string): string[] {
    const traits: string[] = []
    const traitKeywords = ['personality', 'trait', 'characteristic', 'quality', 'nature']
    
    const sentences = content.split(/[.!?]+/)
    sentences.forEach(sentence => {
      if (traitKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
        traits.push(sentence.trim())
      }
    })
    
    return traits.slice(0, 5)
  }

  /**
   * Extract speakers from dialogue
   */
  private extractSpeakers(content: string): string[] {
    const speakers = new Set<string>()
    const dialoguePattern = /^([A-Z][a-z]+):/gm
    
    let match
    while ((match = dialoguePattern.exec(content)) !== null) {
      speakers.add(match[1])
    }
    
    return Array.from(speakers)
  }

  /**
   * Extract location features
   */
  private extractLocationFeatures(content: string): string[] {
    const features: string[] = []
    const featureKeywords = ['feature', 'landmark', 'building', 'structure', 'area']
    
    const sentences = content.split(/[.!?]+/)
    sentences.forEach(sentence => {
      if (featureKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
        features.push(sentence.trim())
      }
    })
    
    return features.slice(0, 5)
  }

  /**
   * Extract event outcomes
   */
  private extractOutcomes(content: string): string[] {
    const outcomes: string[] = []
    const outcomeKeywords = ['result', 'consequence', 'outcome', 'led to', 'caused']
    
    const sentences = content.split(/[.!?]+/)
    sentences.forEach(sentence => {
      if (outcomeKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
        outcomes.push(sentence.trim())
      }
    })
    
    return outcomes.slice(0, 5)
  }

  /**
   * Estimate token count
   */
  private estimateTokens(text: string): number {
    // Rough estimate: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4)
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length
  }

  /**
   * Batch compress multiple contexts
   */
  async batchCompress(
    contexts: CompressionContext[],
    strategy: CompressionStrategy = 'balanced'
  ): Promise<CompressionResult[]> {
    const results: CompressionResult[] = []
    
    // Process in batches to avoid overwhelming the API
    const batchSize = 5
    for (let i = 0; i < contexts.length; i += batchSize) {
      const batch = contexts.slice(i, i + batchSize)
      const batchResults = await Promise.all(
        batch.map(ctx => this.compressContent(ctx.content, ctx.type, strategy))
      )
      results.push(...batchResults)
    }
    
    return results
  }
}

// Export singleton instance
export const contextCompressionService = ContextCompressionService.getInstance()