# Sentry Configuration
# Add these to your .env.local file

# Sentry DSN - Get this from your Sentry project settings
NEXT_PUBLIC_SENTRY_DSN=

# Optional: Different DSN for server-side (if you want to separate client/server errors)
SENTRY_DSN=

# Sentry Organization and Project (for source maps upload)
SENTRY_ORG=
SENTRY_PROJECT=

# Sentry Auth Token (for source maps and release management)
# Generate at: https://sentry.io/settings/account/api/auth-tokens/
SENTRY_AUTH_TOKEN=

# App Version (for release tracking)
NEXT_PUBLIC_APP_VERSION=1.0.0

# Enable Sentry in development (default: false)
SENTRY_DEBUG=false

# Sentry environment (auto-detected from NODE_ENV if not set)
# SENTRY_ENVIRONMENT=production

# Sample rate for performance monitoring (0.0 to 1.0)
# Lower values reduce Sentry quota usage
SENTRY_TRACES_SAMPLE_RATE=0.1

# Sample rate for session replays
SENTRY_REPLAY_SAMPLE_RATE=0.1

# Sample rate for session replays when errors occur
SENTRY_REPLAY_ON_ERROR_SAMPLE_RATE=1.0