'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { 
  Activity, 
  Database, 
  HardDrive, 
  Cpu,
  AlertTriangle,
  CheckCircle2,
  RefreshCw,
  Zap
} from 'lucide-react'

interface SystemMetrics {
  database: {
    status: 'healthy' | 'degraded' | 'error'
    connectionCount: number
    responseTime: number
    size: number
  }
  services: {
    ai: {
      status: 'healthy' | 'degraded' | 'error'
      lastResponse: number
      errorRate: number
    }
    email: {
      status: 'healthy' | 'degraded' | 'error'
      queueSize: number
      deliveryRate: number
    }
    collaboration: {
      status: 'healthy' | 'degraded' | 'error'
      activeSessions: number
      connectedUsers: number
    }
  }
  performance: {
    averageResponseTime: number
    errorRate: number
    uptime: number
  }
}

export function SystemHealth() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [checking, setChecking] = useState(false)
  const { toast } = useToast()
  const supabase = createClient()

  useEffect(() => {
    checkSystemHealth()
  }, [])

  const checkSystemHealth = async () => {
    setChecking(true)
    try {
      // Check database health
      const dbStart = Date.now()
      const { count: connectionCount } = await supabase
        .from('users')
        .select('id', { count: 'exact', head: true })
      const dbResponseTime = Date.now() - dbStart

      // Check AI service
      const aiStatus = await checkAIService()

      // Check email service status
      const emailStatus = await checkEmailService()

      // Check collaboration service
      const { count: activeSessions } = await supabase
        .from('collaboration_sessions')
        .select('id', { count: 'exact' })
        .eq('is_active', true)

      // Get database size from health API
      let dbSize = 0
      let systemUptime = 99.9
      let errorRate = 0
      
      try {
        const healthResponse = await fetch('/api/health')
        if (healthResponse.ok) {
          const healthData = await healthResponse.json()
          dbSize = healthData.databaseSize || 0
          systemUptime = healthData.uptime || 99.9
          errorRate = healthData.errorRate || 0
        }
      } catch (error) {
        logger.error('Failed to fetch health metrics:', error)
      }

      setMetrics({
        database: {
          status: dbResponseTime < 100 ? 'healthy' : dbResponseTime < 500 ? 'degraded' : 'error',
          connectionCount: connectionCount || 0,
          responseTime: dbResponseTime,
          size: dbSize
        },
        services: {
          ai: aiStatus,
          email: emailStatus,
          collaboration: {
            status: 'healthy',
            activeSessions: activeSessions || 0,
            connectedUsers: 0 // Would get from WebSocket server in production
          }
        },
        performance: {
          averageResponseTime: dbResponseTime,
          errorRate: errorRate,
          uptime: systemUptime
        }
      })
    } catch (error) {
      logger.error('Failed to check system health:', error)
      toast({
        title: 'Error',
        description: 'Failed to check system health',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
      setChecking(false)
    }
  }

  const checkAIService = async (): Promise<SystemMetrics['services']['ai']> => {
    try {
      const startTime = Date.now()
      const response = await fetch('/api/health/ai')
      const responseTime = Date.now() - startTime
      
      if (response.ok) {
        const data = await response.json()
        return {
          status: data.status || 'healthy',
          lastResponse: responseTime,
          errorRate: data.errorRate || 0
        }
      }
      
      return {
        status: 'error',
        lastResponse: responseTime,
        errorRate: 1
      }
    } catch (error) {
      logger.error('Failed to check AI service:', error)
      return {
        status: 'error',
        lastResponse: 0,
        errorRate: 1
      }
    }
  }

  const checkEmailService = async (): Promise<SystemMetrics['services']['email']> => {
    try {
      const response = await fetch('/api/health/email')
      
      if (response.ok) {
        const data = await response.json()
        return {
          status: data.status || 'healthy',
          queueSize: data.queueSize || 0,
          deliveryRate: data.deliveryRate || 99.5
        }
      }
      
      return {
        status: 'degraded',
        queueSize: 0,
        deliveryRate: 0
      }
    } catch (error) {
      logger.error('Failed to check email service:', error)
      return {
        status: 'error',
        queueSize: 0,
        deliveryRate: 0
      }
    }
  }

  const getStatusBadge = (status: 'healthy' | 'degraded' | 'error') => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-success"><CheckCircle2 className="w-3 h-3 mr-1" />Healthy</Badge>
      case 'degraded':
        return <Badge className="bg-warning"><AlertTriangle className="w-3 h-3 mr-1" />Degraded</Badge>
      case 'error':
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />Error</Badge>
    }
  }

  if (loading || !metrics) {
    return <div>Loading system health data...</div>
  }

  const overallStatus = 
    Object.values(metrics.services).some(s => s.status === 'error') || metrics.database.status === 'error'
      ? 'error'
      : Object.values(metrics.services).some(s => s.status === 'degraded') || metrics.database.status === 'degraded'
      ? 'degraded'
      : 'healthy'

  return (
    <div className="space-y-6">
      {/* Overall Status */}
      <Alert className={
        overallStatus === 'healthy' ? 'border-success' :
        overallStatus === 'degraded' ? 'border-warning' :
        'border-destructive'
      }>
        <Activity className="h-4 w-4" />
        <AlertTitle>System Status: {getStatusBadge(overallStatus)}</AlertTitle>
        <AlertDescription>
          {overallStatus === 'healthy' 
            ? 'All systems are operating normally.'
            : overallStatus === 'degraded'
            ? 'Some systems are experiencing degraded performance.'
            : 'System issues detected. Immediate attention required.'}
        </AlertDescription>
      </Alert>

      {/* Quick Actions */}
      <div className="flex gap-2">
        <Button 
          onClick={checkSystemHealth} 
          disabled={checking}
          size="sm"
          variant="outline"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${checking ? 'animate-spin' : ''}`} />
          Refresh Status
        </Button>
      </div>

      {/* Service Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Database Health */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Database</CardTitle>
              {getStatusBadge(metrics.database.status)}
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Response Time</span>
              <span>{metrics.database.responseTime}ms</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Active Connections</span>
              <span>{metrics.database.connectionCount}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Database Size</span>
              <span>{metrics.database.size}MB</span>
            </div>
          </CardContent>
        </Card>

        {/* AI Service Health */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">AI Service</CardTitle>
              {getStatusBadge(metrics.services.ai.status)}
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Response Time</span>
              <span>{metrics.services.ai.lastResponse}ms</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Error Rate</span>
              <span>{(metrics.services.ai.errorRate * 100).toFixed(2)}%</span>
            </div>
            <Progress value={100 - (metrics.services.ai.errorRate * 100)} className="h-2 mt-2" />
          </CardContent>
        </Card>

        {/* Email Service Health */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Email Service</CardTitle>
              {getStatusBadge(metrics.services.email.status)}
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Queue Size</span>
              <span>{metrics.services.email.queueSize}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Delivery Rate</span>
              <span>{metrics.services.email.deliveryRate}%</span>
            </div>
            <Progress value={metrics.services.email.deliveryRate} className="h-2 mt-2" />
          </CardContent>
        </Card>

        {/* Collaboration Service Health */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Collaboration</CardTitle>
              {getStatusBadge(metrics.services.collaboration.status)}
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Active Sessions</span>
              <span>{metrics.services.collaboration.activeSessions}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Connected Users</span>
              <span>{metrics.services.collaboration.connectedUsers}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>System performance over the last 24 hours</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm font-medium">System Uptime</span>
              <span className="text-sm text-muted-foreground">{metrics.performance.uptime}%</span>
            </div>
            <Progress value={metrics.performance.uptime} className="h-2" />
          </div>

          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm font-medium">Success Rate</span>
              <span className="text-sm text-muted-foreground">{(100 - metrics.performance.errorRate).toFixed(2)}%</span>
            </div>
            <Progress value={100 - metrics.performance.errorRate} className="h-2" />
          </div>

          <div className="pt-4 border-t">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-primary">{metrics.performance.averageResponseTime}ms</p>
                <p className="text-xs text-muted-foreground">Avg Response Time</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-primary">{metrics.performance.errorRate}%</p>
                <p className="text-xs text-muted-foreground">Error Rate</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-primary">{metrics.performance.uptime}%</p>
                <p className="text-xs text-muted-foreground">Uptime</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}