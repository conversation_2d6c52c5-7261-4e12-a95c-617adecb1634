import { z } from 'zod';

/**
 * Comprehensive API validation schemas for BookScribe
 * These schemas define the structure and constraints for API requests
 */

// Base schemas for common data types
export const baseSchemas = {
  uuid: z.string().uuid('Invalid UUID format'),
  email: z.string().email('Invalid email format').max(255),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password too long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  slug: z.string()
    .min(1, 'Slug cannot be empty')
    .max(100, 'Slug too long')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  title: z.string()
    .min(1, 'Title cannot be empty')
    .max(200, 'Title too long')
    .regex(/^[^<>'"]*$/, 'Title contains unsafe characters'),
  description: z.string()
    .max(2000, 'Description too long')
    .regex(/^[^<>]*$/, 'Description contains unsafe characters'),
  content: z.string()
    .max(500000, 'Content too long (max 500KB)')
    .refine(
      (content) => !/<script[^>]*>.*?<\/script>/gi.test(content),
      'Content contains unsafe script tags'
    ),
  url: z.string().url('Invalid URL format').max(2048),
  positiveInt: z.number().int().positive('Must be a positive integer'),
  nonNegativeInt: z.number().int().min(0, 'Cannot be negative'),
  dateString: z.string().datetime('Invalid date format'),
  pagination: z.object({
    page: z.coerce.number().int().min(1).max(1000).default(1),
    limit: z.coerce.number().int().min(1).max(100).default(20),
    sort: z.enum(['asc', 'desc']).default('desc'),
    sortBy: z.string().max(50).optional()
  }),
  fileUpload: z.object({
    name: z.string().min(1).max(255),
    size: z.number().max(50 * 1024 * 1024, 'File too large (max 50MB)'),
    type: z.string().regex(/^[a-z]+\/[a-z0-9\-\+]+$/i, 'Invalid MIME type')
  })
};

// Project-related schemas
export const projectSchemas = {
  create: z.object({
    title: baseSchemas.title,
    description: baseSchemas.description.optional(),
    genre: z.string().max(50).optional(),
    target_word_count: baseSchemas.positiveInt.max(10000000).optional(),
    target_chapters: baseSchemas.positiveInt.max(1000).optional(),
    is_playground: z.boolean().default(false),
    settings: z.record(z.any()).optional(),
    // Orchestration fields
    startOrchestration: z.boolean().optional(),
    storyPrompt: z.string().min(10).max(5000).optional(),
    projectSelections: z.object({
      genre: z.string().min(1).max(50),
      tone: z.string().min(1).max(50),
      style: z.string().min(1).max(50),
      pov: z.string().min(1).max(50),
      tense: z.string().min(1).max(50),
      themes: z.array(z.string()).max(10).optional(),
      subgenres: z.array(z.string()).max(10).optional()
    }).optional()
  }).refine((data) => {
    // If startOrchestration is true, require storyPrompt and projectSelections
    if (data.startOrchestration) {
      return !!(data.storyPrompt && data.projectSelections);
    }
    return true;
  }, {
    message: 'When startOrchestration is true, both storyPrompt and projectSelections are required'
  }),
  
  update: z.object({
    title: baseSchemas.title.optional(),
    description: baseSchemas.description.optional(),
    genre: z.string().max(50).optional(),
    target_word_count: baseSchemas.positiveInt.max(10000000).optional(),
    target_chapters: baseSchemas.positiveInt.max(1000).optional(),
    current_word_count: baseSchemas.nonNegativeInt.optional(),
    status: z.enum(['draft', 'in_progress', 'completed', 'published']).optional(),
    settings: z.record(z.any()).optional()
  }),
  
  query: z.object({
    ...baseSchemas.pagination.shape,
    search: z.string().max(200).optional(),
    genre: z.string().max(50).optional(),
    status: z.enum(['draft', 'in_progress', 'completed', 'published']).optional(),
    is_playground: z.coerce.boolean().optional()
  }),
  
  export: z.object({
    format: z.enum(['markdown', 'docx', 'epub', 'pdf']),
    include_metadata: z.boolean().default(true),
    include_comments: z.boolean().default(false),
    chapters: z.array(baseSchemas.uuid).optional()
  })
};

// Chapter-related schemas
export const chapterSchemas = {
  create: z.object({
    project_id: baseSchemas.uuid,
    title: baseSchemas.title,
    content: baseSchemas.content.optional(),
    order_index: baseSchemas.nonNegativeInt.optional(),
    notes: z.string().max(5000).optional(),
    tags: z.array(z.string().max(50)).max(20).optional()
  }),
  
  update: z.object({
    title: baseSchemas.title.optional(),
    content: baseSchemas.content.optional(),
    order_index: baseSchemas.nonNegativeInt.optional(),
    notes: z.string().max(5000).optional(),
    tags: z.array(z.string().max(50)).max(20).optional(),
    word_count: baseSchemas.nonNegativeInt.optional()
  }),
  
  query: z.object({
    ...baseSchemas.pagination.shape,
    project_id: baseSchemas.uuid.optional(),
    search: z.string().max(200).optional(),
    tags: z.array(z.string().max(50)).optional()
  })
};

// Character-related schemas
export const characterSchemas = {
  create: z.object({
    project_id: baseSchemas.uuid,
    name: z.string().min(1).max(100),
    description: baseSchemas.description.optional(),
    personality: z.string().max(2000).optional(),
    background: z.string().max(5000).optional(),
    appearance: z.string().max(1000).optional(),
    voice_notes: z.string().max(2000).optional(),
    role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']).optional(),
    importance: z.number().min(1).max(10).optional(),
    relationships: z.array(z.object({
      character_id: baseSchemas.uuid,
      relationship_type: z.string().max(50),
      description: z.string().max(500).optional()
    })).optional()
  }),
  
  update: z.object({
    name: z.string().min(1).max(100).optional(),
    description: baseSchemas.description.optional(),
    personality: z.string().max(2000).optional(),
    background: z.string().max(5000).optional(),
    appearance: z.string().max(1000).optional(),
    voice_notes: z.string().max(2000).optional(),
    role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']).optional(),
    importance: z.number().min(1).max(10).optional()
  }),
  
  query: z.object({
    ...baseSchemas.pagination.shape,
    project_id: baseSchemas.uuid.optional(),
    search: z.string().max(200).optional(),
    role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']).optional()
  })
};

// AI-related schemas
export const aiSchemas = {
  generate: z.object({
    prompt: z.string().min(1).max(5000),
    context: z.string().max(100000).optional(),
    model: z.enum(['gpt-4', 'gpt-4o-mini', 'claude-3-sonnet']).optional(),
    max_tokens: z.number().int().min(1).max(8000).optional(),
    temperature: z.number().min(0).max(2).optional(),
    project_id: baseSchemas.uuid.optional(),
    chapter_id: baseSchemas.uuid.optional()
  }),
  
  chat: z.object({
    message: z.string().min(1).max(5000),
    conversation_id: baseSchemas.uuid.optional(),
    context: z.object({
      project_id: baseSchemas.uuid.optional(),
      chapter_id: baseSchemas.uuid.optional(),
      character_ids: z.array(baseSchemas.uuid).optional(),
      session_id: z.string().optional()
    }).optional(),
    settings: z.object({
      model: z.enum(['gpt-4', 'gpt-4o-mini', 'claude-3-sonnet']).optional(),
      temperature: z.number().min(0).max(2).optional(),
      max_tokens: z.number().int().min(1).max(8000).optional()
    }).optional()
  }),
  
  suggestions: z.object({
    content: z.string().min(1).max(10000),
    type: z.enum(['grammar', 'style', 'plot', 'character', 'dialogue']),
    project_id: baseSchemas.uuid,
    chapter_id: baseSchemas.uuid.optional()
  })
};

// User and profile schemas
export const userSchemas = {
  register: z.object({
    email: baseSchemas.email,
    password: baseSchemas.password,
    full_name: z.string().min(1).max(100),
    terms_accepted: z.boolean().refine(val => val === true, 'Must accept terms')
  }),
  
  login: z.object({
    email: baseSchemas.email,
    password: z.string().min(1).max(128),
    remember_me: z.boolean().optional()
  }),
  
  updateProfile: z.object({
    full_name: z.string().min(1).max(100).optional(),
    bio: z.string().max(500).optional(),
    website: baseSchemas.url.optional(),
    writing_goals: z.object({
      daily_word_count: baseSchemas.positiveInt.max(50000).optional(),
      weekly_word_count: baseSchemas.positiveInt.max(350000).optional(),
      preferred_writing_time: z.enum(['morning', 'afternoon', 'evening', 'night']).optional()
    }).optional(),
    preferences: z.object({
      theme: z.enum(['light', 'dark', 'auto']).optional(),
      editor_font_size: z.number().min(8).max(24).optional(),
      auto_save_interval: z.number().min(10).max(300).optional(),
      notifications_enabled: z.boolean().optional()
    }).optional()
  }),
  
  changePassword: z.object({
    current_password: z.string().min(1),
    new_password: baseSchemas.password,
    confirm_password: z.string().min(1)
  }).refine(
    data => data.new_password === data.confirm_password,
    { message: "Passwords don't match", path: ["confirm_password"] }
  )
};

// Analytics and metrics schemas
export const analyticsSchemas = {
  session: z.object({
    project_id: baseSchemas.uuid,
    chapter_id: baseSchemas.uuid.optional(),
    start_time: baseSchemas.dateString,
    end_time: baseSchemas.dateString.optional(),
    words_added: baseSchemas.nonNegativeInt.optional(),
    words_deleted: baseSchemas.nonNegativeInt.optional(),
    keystrokes: baseSchemas.nonNegativeInt.optional(),
    breaks_taken: baseSchemas.nonNegativeInt.optional()
  }),
  
  query: z.object({
    ...baseSchemas.pagination.shape,
    project_id: baseSchemas.uuid.optional(),
    start_date: baseSchemas.dateString.optional(),
    end_date: baseSchemas.dateString.optional(),
    metric_type: z.enum(['words', 'time', 'sessions', 'productivity']).optional()
  }),
  
  goals: z.object({
    type: z.enum(['daily', 'weekly', 'monthly', 'project']),
    target: baseSchemas.positiveInt,
    metric: z.enum(['words', 'time', 'chapters']),
    project_id: baseSchemas.uuid.optional(),
    deadline: baseSchemas.dateString.optional()
  })
};

// Reference materials schemas
export const referenceSchemas = {
  upload: z.object({
    project_id: baseSchemas.uuid,
    title: baseSchemas.title,
    description: baseSchemas.description.optional(),
    file: baseSchemas.fileUpload,
    tags: z.array(z.string().max(50)).max(10).optional(),
    category: z.enum(['research', 'inspiration', 'outline', 'notes', 'other']).optional()
  }),
  
  update: z.object({
    title: baseSchemas.title.optional(),
    description: baseSchemas.description.optional(),
    tags: z.array(z.string().max(50)).max(10).optional(),
    category: z.enum(['research', 'inspiration', 'outline', 'notes', 'other']).optional(),
    summary: z.string().max(2000).optional()
  }),
  
  query: z.object({
    ...baseSchemas.pagination.shape,
    project_id: baseSchemas.uuid.optional(),
    search: z.string().max(200).optional(),
    category: z.enum(['research', 'inspiration', 'outline', 'notes', 'other']).optional(),
    tags: z.array(z.string().max(50)).optional()
  })
};

// Collaboration schemas
export const collaborationSchemas = {
  invite: z.object({
    project_id: baseSchemas.uuid,
    email: baseSchemas.email,
    role: z.enum(['viewer', 'commenter', 'editor', 'admin']),
    message: z.string().max(500).optional(),
    expires_at: baseSchemas.dateString.optional()
  }),
  
  updatePermissions: z.object({
    role: z.enum(['viewer', 'commenter', 'editor', 'admin']),
    permissions: z.object({
      can_view: z.boolean(),
      can_comment: z.boolean(),
      can_edit: z.boolean(),
      can_invite: z.boolean(),
      can_manage: z.boolean()
    }).optional()
  }),
  
  session: z.object({
    project_id: baseSchemas.uuid,
    chapter_id: baseSchemas.uuid.optional(),
    type: z.enum(['real-time', 'async']),
    max_participants: z.number().int().min(1).max(50).optional()
  })
};

// Admin schemas
export const adminSchemas = {
  userManagement: z.object({
    action: z.enum(['suspend', 'unsuspend', 'delete', 'promote', 'demote']),
    user_id: baseSchemas.uuid,
    reason: z.string().min(1).max(500),
    duration: z.number().positive().optional() // days
  }),
  
  systemSettings: z.object({
    maintenance_mode: z.boolean().optional(),
    registration_enabled: z.boolean().optional(),
    max_projects_per_user: z.number().int().min(1).max(1000).optional(),
    max_file_size: z.number().int().min(1048576).max(1073741824).optional(), // 1MB to 1GB
    ai_rate_limits: z.object({
      requests_per_hour: z.number().int().min(1).max(10000),
      tokens_per_day: z.number().int().min(1000).max(1000000)
    }).optional()
  }),
  
  bulkOperations: z.object({
    operation: z.enum(['export', 'backup', 'cleanup', 'migration']),
    filters: z.object({
      date_range: z.object({
        start: baseSchemas.dateString,
        end: baseSchemas.dateString
      }).optional(),
      user_ids: z.array(baseSchemas.uuid).optional(),
      project_ids: z.array(baseSchemas.uuid).optional()
    }).optional(),
    options: z.record(z.any()).optional()
  })
};

// Export all schemas as a convenient object
export const apiSchemas = {
  base: baseSchemas,
  projects: projectSchemas,
  chapters: chapterSchemas,
  characters: characterSchemas,
  ai: aiSchemas,
  users: userSchemas,
  analytics: analyticsSchemas,
  references: referenceSchemas,
  collaboration: collaborationSchemas,
  admin: adminSchemas
};

// Helper function to get schema by path
export function getSchemaByPath(path: string, operation: string = 'create'): z.ZodSchema | null {
  const [category, action] = path.split('.');
  const schema = (apiSchemas as any)[category]?.[action || operation];
  return schema || null;
}

// Validation helper functions
export const validationHelpers = {
  /**
   * Validates file upload based on allowed types and size
   */
  validateFileUpload: (file: any, allowedTypes: string[], maxSize: number = 50 * 1024 * 1024) => {
    const schema = z.object({
      name: z.string().min(1).max(255),
      size: z.number().max(maxSize, `File too large (max ${Math.round(maxSize / 1024 / 1024)}MB)`),
      type: z.string().refine(
        type => allowedTypes.some(allowed => type.toLowerCase().includes(allowed.toLowerCase())),
        `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
      )
    });
    
    return schema.safeParse(file);
  },

  /**
   * Validates pagination parameters with custom limits
   */
  validatePagination: (params: any, maxLimit: number = 100) => {
    const schema = z.object({
      page: z.coerce.number().int().min(1).max(1000).default(1),
      limit: z.coerce.number().int().min(1).max(maxLimit).default(20),
      sort: z.enum(['asc', 'desc']).default('desc'),
      sortBy: z.string().max(50).optional()
    });
    
    return schema.safeParse(params);
  },

  /**
   * Validates search parameters
   */
  validateSearch: (params: any) => {
    const schema = z.object({
      q: z.string().min(1).max(200),
      type: z.enum(['projects', 'chapters', 'characters', 'all']).default('all'),
      filters: z.record(z.any()).optional()
    });
    
    return schema.safeParse(params);
  }
};

export default apiSchemas;