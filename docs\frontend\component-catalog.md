# BookScribe Component Catalog

## Overview

This catalog documents all React components in the BookScribe application, organized by category with usage examples, props, and design patterns. All components follow TypeScript best practices and the Writer's Sanctuary design theme.

## UI Components (Base)

### Button

**Location**: `src/components/ui/button.tsx`

```typescript
interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'literary'
  size?: 'default' | 'sm' | 'lg' | 'icon' | 'responsive'
  asChild?: boolean
}

// Usage
<Button variant="literary" size="lg">
  Start Writing
</Button>

<Button variant="outline" size="icon">
  <Save className="h-4 w-4" />
</Button>
```

### Card

**Location**: `src/components/ui/card.tsx`

```typescript
// Compound component pattern
<Card>
  <CardHeader>
    <CardTitle>Chapter Progress</CardTitle>
    <CardDescription>Track your writing journey</CardDescription>
  </CardHeader>
  <CardContent>
    <Progress value={75} />
  </CardContent>
  <CardFooter className="flex justify-between">
    <span>15,000 words</span>
    <span>75% complete</span>
  </CardFooter>
</Card>
```

### Input

**Location**: `src/components/ui/input.tsx`

```typescript
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean
}

// Usage
<Input 
  type="text" 
  placeholder="Enter chapter title..."
  className="font-literary-serif"
/>
```

### Select

**Location**: `src/components/ui/select.tsx`

```typescript
// Usage with React Hook Form
<Select value={genre} onValueChange={setGenre}>
  <SelectTrigger>
    <SelectValue placeholder="Choose genre" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="fantasy">Fantasy</SelectItem>
    <SelectItem value="mystery">Mystery</SelectItem>
    <SelectItem value="romance">Romance</SelectItem>
  </SelectContent>
</Select>
```

### Tabs

**Location**: `src/components/ui/tabs.tsx`

```typescript
<Tabs defaultValue="outline" className="w-full">
  <TabsList className="grid w-full grid-cols-3">
    <TabsTrigger value="outline">Outline</TabsTrigger>
    <TabsTrigger value="characters">Characters</TabsTrigger>
    <TabsTrigger value="notes">Notes</TabsTrigger>
  </TabsList>
  <TabsContent value="outline">
    <OutlineEditor />
  </TabsContent>
  <TabsContent value="characters">
    <CharacterManager />
  </TabsContent>
  <TabsContent value="notes">
    <NotesPanel />
  </TabsContent>
</Tabs>
```

### Dialog

**Location**: `src/components/ui/dialog.tsx`

```typescript
<Dialog>
  <DialogTrigger asChild>
    <Button>Create New Project</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Create New Project</DialogTitle>
      <DialogDescription>
        Start your next literary masterpiece
      </DialogDescription>
    </DialogHeader>
    <CreateProjectForm />
  </DialogContent>
</Dialog>
```

### Toast

**Location**: `src/components/ui/toast.tsx`

```typescript
// Usage via hook
const { toast } = useToast()

toast({
  title: "Chapter saved",
  description: "Your work has been saved automatically",
  duration: 3000
})
```

### Skeleton

**Location**: `src/components/ui/skeleton.tsx`

```typescript
// Loading state component
<div className="space-y-2">
  <Skeleton className="h-4 w-[250px]" />
  <Skeleton className="h-4 w-[200px]" />
  <Skeleton className="h-4 w-[150px]" />
</div>
```

### Alert

**Location**: `src/components/ui/alert.tsx`

```typescript
<Alert variant="warning">
  <AlertTriangle className="h-4 w-4" />
  <AlertTitle>Unsaved Changes</AlertTitle>
  <AlertDescription>
    You have unsaved changes. Save before leaving?
  </AlertDescription>
</Alert>
```

## Editor Components

### MonacoEditor

**Location**: `src/components/editor/lazy-monaco-editor.tsx`

```typescript
interface MonacoEditorProps {
  value: string
  onChange: (value: string) => void
  language?: string
  theme?: string
  options?: monaco.editor.IStandaloneEditorConstructionOptions
  onMount?: (editor: monaco.editor.IStandaloneCodeEditor) => void
}

// Usage
<MonacoEditor
  value={content}
  onChange={handleContentChange}
  language="markdown"
  theme="writers-sanctuary"
  options={{
    fontSize: 16,
    fontFamily: 'JetBrains Mono',
    lineHeight: 1.8,
    wordWrap: 'on'
  }}
/>
```

### CollaborativeMonacoEditor

**Location**: `src/components/editor/collaborative-monaco-editor.tsx`

```typescript
interface CollaborativeEditorProps extends MonacoEditorProps {
  projectId: string
  chapterId: string
  userId: string
  onCollaboratorJoin?: (user: User) => void
  onCollaboratorLeave?: (user: User) => void
}

// Real-time collaborative editing
<CollaborativeMonacoEditor
  projectId={project.id}
  chapterId={chapter.id}
  userId={user.id}
  value={content}
  onChange={handleChange}
  onCollaboratorJoin={(user) => {
    toast({ title: `${user.name} joined the session` })
  }}
/>
```

### FormattingToolbar

**Location**: `src/components/editor/formatting-toolbar.tsx`

```typescript
interface FormattingToolbarProps {
  onFormat: (format: FormatType) => void
  activeFormats?: FormatType[]
}

// Text formatting controls
<FormattingToolbar
  onFormat={(format) => applyFormat(format)}
  activeFormats={['bold', 'italic']}
/>
```

### SelectionMenu

**Location**: `src/components/editor/selection-menu.tsx`

```typescript
// Context menu for selected text
<SelectionMenu
  selection={selectedText}
  position={selectionPosition}
  onAction={(action) => {
    switch(action) {
      case 'ai-improve':
        improveWithAI(selectedText)
        break
      case 'add-comment':
        addComment(selectedText)
        break
    }
  }}
/>
```

### StoryBiblePanel

**Location**: `src/components/editor/story-bible-panel.tsx`

```typescript
interface StoryBiblePanelProps {
  projectId: string
  isOpen: boolean
  onClose: () => void
}

// Reference panel for story consistency
<StoryBiblePanel
  projectId={project.id}
  isOpen={showStoryBible}
  onClose={() => setShowStoryBible(false)}
/>
```

### AIAssistantChat

**Location**: `src/components/editor/ai-chat-assistant.tsx`

```typescript
interface AIAssistantProps {
  projectId: string
  context?: string
  onSuggestionAccept?: (suggestion: string) => void
}

// AI writing assistant
<AIAssistantChat
  projectId={project.id}
  context={currentChapterContent}
  onSuggestionAccept={(suggestion) => {
    insertTextAtCursor(suggestion)
  }}
/>
```

## Analytics Components

### AnalyticsPanel

**Location**: `src/components/analytics/analytics-panel.tsx`

```typescript
interface AnalyticsPanelProps {
  projectId?: string
  timeRange: TimeRange
  metrics?: MetricType[]
}

// Main analytics dashboard
<AnalyticsPanel
  projectId={project.id}
  timeRange="last30days"
  metrics={['wordCount', 'writingTime', 'quality']}
/>
```

### WritingProgressChart

**Location**: `src/components/analytics/writing-progress-chart.tsx`

```typescript
// Visualize writing progress over time
<WritingProgressChart
  data={progressData}
  target={project.targetWordCount}
  showTrendline
  height={300}
/>
```

### QualityMetricsDisplay

**Location**: `src/components/analytics/quality-metrics-display.tsx`

```typescript
interface QualityMetrics {
  overall: number
  readability: number
  vocabulary: number
  sentenceVariety: number
  dialogueBalance: number
}

// Display content quality scores
<QualityMetricsDisplay
  metrics={qualityMetrics}
  showBreakdown
  compareToAverage
/>
```

### VoiceConsistencyMetrics

**Location**: `src/components/analytics/components/voice-consistency-metrics.tsx`

```typescript
// Character voice analysis
<VoiceConsistencyMetrics
  characters={characters}
  dialogueSamples={samples}
  showRecommendations
/>
```

## Project Components

### ProjectCard

**Location**: `src/components/projects/project-card.tsx`

```typescript
interface ProjectCardProps {
  project: Project
  onEdit?: () => void
  onDelete?: () => void
  showProgress?: boolean
}

// Project display card
<ProjectCard
  project={project}
  showProgress
  onEdit={() => router.push(`/projects/${project.id}`)}
/>
```

### ProjectsList

**Location**: `src/components/projects/projects-list.tsx`

```typescript
// List of user projects
<ProjectsList
  projects={projects}
  viewMode="grid" // or "list"
  sortBy="lastModified"
  onProjectSelect={(project) => navigateToProject(project)}
/>
```

### GenerateStructureButton

**Location**: `src/components/project/generate-structure-button.tsx`

```typescript
// AI story structure generation
<GenerateStructureButton
  projectId={project.id}
  onGenerate={async (structure) => {
    await saveStructure(structure)
    toast({ title: "Story structure generated!" })
  }}
/>
```

### GenerateChapterButton

**Location**: `src/components/project/generate-chapter-button.tsx`

```typescript
// AI chapter generation
<GenerateChapterButton
  projectId={project.id}
  chapterNumber={currentChapter}
  context={storyContext}
  onGenerate={(content) => setChapterContent(content)}
/>
```

## Character Components

### CharacterManager

**Location**: `src/components/characters/character-manager.tsx`

```typescript
interface CharacterManagerProps {
  projectId: string
  characters: Character[]
  onUpdate: (character: Character) => void
  onDelete: (characterId: string) => void
}

// Character creation and management
<CharacterManager
  projectId={project.id}
  characters={characters}
  onUpdate={updateCharacter}
  onDelete={deleteCharacter}
/>
```

### CharacterCard

**Location**: `src/components/characters/character-card.tsx`

```typescript
// Individual character display
<CharacterCard
  character={character}
  showRelationships
  showArcProgress
  onEdit={() => openCharacterEditor(character)}
/>
```

## Series Components

### SeriesCard

**Location**: `src/components/series/series-card.tsx`

```typescript
// Series overview card
<SeriesCard
  series={series}
  showBookCount
  showProgress
  onManage={() => router.push(`/series/${series.id}`)}
/>
```

### SeriesConsistencyDashboard

**Location**: `src/components/series/series-consistency-dashboard.tsx`

```typescript
// Track consistency across series
<SeriesConsistencyDashboard
  seriesId={series.id}
  checkTypes={['timeline', 'characters', 'worldbuilding']}
  onIssueFound={(issue) => handleConsistencyIssue(issue)}
/>
```

### SeriesCharacterMap

**Location**: `src/components/series/series-character-map.tsx`

```typescript
// Visualize character appearances across books
<SeriesCharacterMap
  series={series}
  characters={seriesCharacters}
  showRelationships
  interactive
/>
```

## Universe Components

### UniverseForm

**Location**: `src/components/universe/universe-form.tsx`

```typescript
// Create/edit universe settings
<UniverseForm
  universe={universe}
  onSubmit={async (data) => {
    await saveUniverse(data)
    toast({ title: "Universe saved" })
  }}
/>
```

### CreateUniverseDialog

**Location**: `src/components/universe/create-universe-dialog.tsx`

```typescript
// Universe creation modal
<CreateUniverseDialog
  isOpen={showCreateUniverse}
  onClose={() => setShowCreateUniverse(false)}
  onCreated={(universe) => {
    setSelectedUniverse(universe)
    router.push(`/universes/${universe.id}`)
  }}
/>
```

## Settings Components

### SettingsProvider

**Location**: `src/components/settings/settings-provider.tsx`

```typescript
// Global settings context
<SettingsProvider>
  <App />
</SettingsProvider>
```

### EditorSettingsSection

**Location**: `src/components/settings/editor-settings-section.tsx`

```typescript
// Editor preferences UI
<EditorSettingsSection
  settings={editorSettings}
  onChange={(newSettings) => updateEditorSettings(newSettings)}
/>
```

## Theme Components

### ThemeProvider

**Location**: `src/components/theme-provider.tsx`

```typescript
// Theme context provider
<ThemeProvider>
  <html lang="en" suppressHydrationWarning>
    <body>{children}</body>
  </html>
</ThemeProvider>
```

### ThemeToggle

**Location**: `src/components/ui/theme-toggle.tsx`

```typescript
// Theme switcher UI
<ThemeToggle
  themes={['writers-sanctuary-light', 'evening-study-dark']}
  showLabel
/>
```

### ThemePreviewColors

**Location**: `src/components/customization/theme-preview-colors.tsx`

```typescript
// Preview theme colors
<ThemePreviewColors
  theme={selectedTheme}
  showColorValues
  interactive
/>
```

## Collaboration Components

### CollaborationStatus

**Location**: `src/components/collaboration/collaboration-status.tsx`

```typescript
// Show active collaborators
<CollaborationStatus
  projectId={project.id}
  showAvatars
  maxDisplay={5}
/>
```

### RealtimeIndicator

**Location**: `src/components/editor/realtime-indicator.tsx`

```typescript
// Real-time sync status
<RealtimeIndicator
  isConnected={isConnected}
  collaboratorCount={collaborators.length}
  lastSync={lastSyncTime}
/>
```

## Loading Components

### LoadingSpinner

**Location**: `src/components/loading/loading-spinner.tsx`

```typescript
// Animated loading spinner
<LoadingSpinner 
  size="lg" 
  className="text-primary" 
/>
```

### EditorPanelSkeleton

**Location**: `src/components/loading/skeleton-loader.tsx`

```typescript
// Editor loading skeleton
<EditorPanelSkeleton
  showSidebar
  showToolbar
/>
```

## Error Components

### APIErrorBoundary

**Location**: `src/components/error/api-error-boundary.tsx`

```typescript
// API error handling wrapper
<APIErrorBoundary
  fallback={<ErrorFallback />}
  onError={(error) => logError(error)}
>
  <App />
</APIErrorBoundary>
```

### ServiceError

**Location**: `src/components/error/service-error.tsx`

```typescript
// Service unavailable error
<ServiceError
  service="AI Generation"
  retry={() => retryGeneration()}
  showSupport
/>
```

## Onboarding Components

### WelcomeTour

**Location**: `src/components/onboarding/welcome-tour.tsx`

```typescript
// First-time user tour
<WelcomeTour
  steps={tourSteps}
  onComplete={() => markTourCompleted()}
  skipable
/>
```

### InteractiveTutorial

**Location**: `src/components/onboarding/interactive-tutorial.tsx`

```typescript
// Feature-specific tutorials
<InteractiveTutorial
  feature="ai-assistant"
  onComplete={() => unlockFeature('ai-assistant')}
/>
```

### FirstTimeUserWrapper

**Location**: `src/components/onboarding/first-time-user-wrapper.tsx`

```typescript
// Wrap components for first-time users
<FirstTimeUserWrapper>
  <Dashboard />
</FirstTimeUserWrapper>
```

## Accessibility Components

### AccessibleWrapper

**Location**: `src/components/accessibility/accessible-wrapper.tsx`

```typescript
// Enhance component accessibility
<AccessibleWrapper
  role="main"
  ariaLabel="Editor workspace"
>
  <EditorLayout />
</AccessibleWrapper>
```

### SkipLink

**Location**: `src/components/ui/skip-link.tsx`

```typescript
// Skip navigation link
<SkipLink href="#main-content">
  Skip to main content
</SkipLink>
```

### Announcement

**Location**: `src/components/ui/announcement.tsx`

```typescript
// Screen reader announcements
<Announcement
  message="Chapter saved successfully"
  priority="polite"
/>
```

## Utility Components

### AsyncBoundary

**Location**: `src/components/ui/async-boundary.tsx`

```typescript
// Async component wrapper
<AsyncBoundary
  isLoading={isLoading}
  error={error}
  fallback={<Skeleton />}
>
  <DataComponent />
</AsyncBoundary>
```

### SuspenseWrapper

**Location**: `src/components/suspense-wrapper.tsx`

```typescript
// React Suspense wrapper
<SuspenseWrapper fallback={<Loading />}>
  <LazyComponent />
</SuspenseWrapper>
```

### ConnectionStatus

**Location**: `src/components/connection-status.tsx`

```typescript
// Network connection indicator
<ConnectionStatus
  showDetails
  position="bottom-right"
/>
```

## Component Best Practices

### 1. TypeScript Usage
- Define explicit prop interfaces
- Avoid `any` types
- Use generics for reusable components
- Export prop types for external use

### 2. Accessibility
- Include proper ARIA labels
- Ensure keyboard navigation
- Provide focus indicators
- Test with screen readers

### 3. Performance
- Use React.memo for expensive renders
- Implement proper loading states
- Lazy load heavy components
- Optimize re-renders

### 4. Styling
- Use Tailwind utility classes
- Maintain consistent spacing
- Follow theme variables
- Ensure responsive design

### 5. Error Handling
- Implement error boundaries
- Show user-friendly messages
- Provide recovery actions
- Log errors appropriately