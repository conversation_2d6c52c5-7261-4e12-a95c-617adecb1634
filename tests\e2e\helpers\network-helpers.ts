import { BrowserContext, Page } from '@playwright/test';

/**
 * Simulate slow network conditions
 */
export async function simulateSlowNetwork(
  context: BrowserContext,
  latency: number = 1000,
  downloadThroughput?: number,
  uploadThroughput?: number
): Promise<void> {
  // Set up network conditions for all pages in context
  await context.route('**/*', async (route) => {
    // Add artificial delay
    await new Promise(resolve => setTimeout(resolve, latency));
    await route.continue();
  });

  // Note: Playwright doesn't directly support bandwidth throttling
  // You might need to use Chrome DevTools Protocol for more advanced throttling
}

/**
 * Simulate network offline/online
 */
export async function setNetworkOffline(context: BrowserContext, offline: boolean): Promise<void> {
  await context.setOffline(offline);
}

/**
 * Simulate packet loss by randomly failing requests
 */
export async function simulatePacketLoss(
  context: BrowserContext,
  lossPercentage: number = 10
): Promise<void> {
  await context.route('**/*', async (route) => {
    const randomValue = Math.random() * 100;
    
    if (randomValue < lossPercentage) {
      // Simulate packet loss by aborting request
      await route.abort('failed');
    } else {
      await route.continue();
    }
  });
}

/**
 * Simulate intermittent connectivity
 */
export async function simulateIntermittentConnection(
  context: BrowserContext,
  offlineDuration: number = 2000,
  onlineDuration: number = 5000,
  cycles: number = 3
): Promise<void> {
  for (let i = 0; i < cycles; i++) {
    // Go offline
    await context.setOffline(true);
    await new Promise(resolve => setTimeout(resolve, offlineDuration));
    
    // Go online
    await context.setOffline(false);
    await new Promise(resolve => setTimeout(resolve, onlineDuration));
  }
}

/**
 * Simulate variable network latency
 */
export async function simulateVariableLatency(
  context: BrowserContext,
  minLatency: number = 100,
  maxLatency: number = 2000
): Promise<void> {
  await context.route('**/*', async (route) => {
    // Random latency between min and max
    const latency = Math.random() * (maxLatency - minLatency) + minLatency;
    await new Promise(resolve => setTimeout(resolve, latency));
    await route.continue();
  });
}

/**
 * Block specific API endpoints
 */
export async function blockEndpoints(
  context: BrowserContext,
  endpoints: string[]
): Promise<void> {
  for (const endpoint of endpoints) {
    await context.route(`**/${endpoint}`, async (route) => {
      await route.abort('blocked');
    });
  }
}

/**
 * Simulate API rate limiting
 */
export async function simulateRateLimit(
  context: BrowserContext,
  endpoint: string,
  requestsPerMinute: number = 10
): Promise<void> {
  const requestTimes: number[] = [];
  const windowMs = 60 * 1000; // 1 minute

  await context.route(`**/${endpoint}`, async (route) => {
    const now = Date.now();
    
    // Remove old requests outside the window
    const cutoff = now - windowMs;
    const recentRequests = requestTimes.filter(time => time > cutoff);
    
    if (recentRequests.length >= requestsPerMinute) {
      // Rate limit exceeded
      await route.fulfill({
        status: 429,
        headers: {
          'Retry-After': '60',
          'X-RateLimit-Limit': requestsPerMinute.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': new Date(now + windowMs).toISOString()
        },
        body: JSON.stringify({
          error: 'Rate limit exceeded',
          message: 'Too many requests, please try again later'
        })
      });
    } else {
      // Track request time and continue
      requestTimes.push(now);
      await route.continue();
    }
  });
}

/**
 * Simulate server errors
 */
export async function simulateServerError(
  context: BrowserContext,
  endpoint: string,
  errorRate: number = 50,
  errorCode: number = 500
): Promise<void> {
  await context.route(`**/${endpoint}`, async (route) => {
    const shouldError = Math.random() * 100 < errorRate;
    
    if (shouldError) {
      await route.fulfill({
        status: errorCode,
        body: JSON.stringify({
          error: `Server error ${errorCode}`,
          message: 'An unexpected error occurred'
        })
      });
    } else {
      await route.continue();
    }
  });
}

/**
 * Monitor network requests
 */
export async function monitorNetworkRequests(
  page: Page,
  filter?: (url: string) => boolean
): Promise<{ url: string; method: string; status: number; duration: number }[]> {
  const requests: any[] = [];

  page.on('request', request => {
    if (!filter || filter(request.url())) {
      requests.push({
        url: request.url(),
        method: request.method(),
        startTime: Date.now(),
        request
      });
    }
  });

  page.on('response', response => {
    const requestData = requests.find(r => r.request === response.request());
    if (requestData) {
      requestData.status = response.status();
      requestData.duration = Date.now() - requestData.startTime;
      delete requestData.request;
      delete requestData.startTime;
    }
  });

  return requests;
}

/**
 * Wait for network idle
 */
export async function waitForNetworkIdle(
  page: Page,
  timeout: number = 5000,
  maxInflightRequests: number = 0
): Promise<void> {
  await page.waitForLoadState('networkidle', { timeout });
}

/**
 * Simulate CDN failure
 */
export async function simulateCDNFailure(
  context: BrowserContext,
  cdnPatterns: string[] = ['cdn.', 'static.', 'assets.']
): Promise<void> {
  for (const pattern of cdnPatterns) {
    await context.route(`**/*${pattern}*`, async (route) => {
      await route.abort('failed');
    });
  }
}

/**
 * Simulate WebSocket disconnection
 */
export async function simulateWebSocketDisconnect(page: Page): Promise<void> {
  await page.evaluate(() => {
    // Find and close all WebSocket connections
    const sockets = (window as any).__websockets || [];
    sockets.forEach((socket: WebSocket) => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.close();
      }
    });
  });
}

/**
 * Get network metrics
 */
export async function getNetworkMetrics(page: Page): Promise<{
  totalRequests: number;
  failedRequests: number;
  totalBytes: number;
  averageLatency: number;
}> {
  const metrics = await page.evaluate(() => {
    const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    const totalRequests = entries.length;
    const failedRequests = entries.filter(e => e.responseEnd === 0).length;
    const totalBytes = entries.reduce((sum, e) => sum + (e.transferSize || 0), 0);
    const latencies = entries
      .filter(e => e.responseEnd > 0)
      .map(e => e.responseEnd - e.startTime);
    const averageLatency = latencies.length > 0 
      ? latencies.reduce((a, b) => a + b) / latencies.length 
      : 0;

    return {
      totalRequests,
      failedRequests,
      totalBytes,
      averageLatency
    };
  });

  return metrics;
}