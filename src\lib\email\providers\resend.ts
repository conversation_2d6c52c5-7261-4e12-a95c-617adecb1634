import { Resend } from 'resend'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ailResult, EmailOptions } from '../types'
import { logger } from '@/lib/services/logger'
import { config } from '@/lib/config/unified-env'

export class ResendProvider implements EmailProvider {
  name = 'Resend'
  private client: Resend | null = null

  constructor(private apiKey?: string) {
    if (this.apiKey) {
      this.client = new Resend(this.apiKey)
    }
  }

  async verifyConfiguration(): Promise<boolean> {
    if (!this.client) {
      logger.error('Resend: No API key configured')
      return false
    }

    try {
      // Test the API key by attempting to get domains
      const response = await fetch('https://api.resend.com/domains', {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      })
      
      return response.ok
    } catch (error) {
      logger.error('Resend configuration verification failed:', error)
      return false
    }
  }

  async sendEmail(options: EmailOptions & { subject: string; html: string; text: string }): Promise<EmailResult> {
    if (!this.client) {
      return {
        id: '',
        success: false,
        error: 'Resend provider not configured',
        provider: this.name,
      }
    }

    try {
      const defaultFrom = config.email?.fromName
        ? `${config.email.fromName} <${config.email.from}>`
        : config.email?.from
      const { data, error } = await this.client.emails.send({
        from: options.from || defaultFrom,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: options.subject,
        html: options.html,
        text: options.text,
        reply_to: options.replyTo,
        cc: options.cc,
        bcc: options.bcc,
        attachments: options.attachments?.map(att => ({
          filename: att.filename,
          content: att.content,
        })),
        headers: options.headers,
        tags: options.tags?.reduce((acc, tag, index) => {
          acc[`tag${index}`] = tag
          return acc
        }, {} as Record<string, string>),
      })

      if (error) {
        throw error
      }

      return {
        id: data?.id || '',
        success: true,
        provider: this.name,
      }
    } catch (error) {
      logger.error('Resend send email error:', error)
      return {
        id: '',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send email',
        provider: this.name,
      }
    }
  }
}