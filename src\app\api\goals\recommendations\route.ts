import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import { createClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'

interface UserStats {
  avgDailyWords: number
  currentStreak: number
  avgQualityScore: number
  projectProgress: number
  completedGoals: number
  activeGoals: number
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return handleAPIError(new AuthenticationError())
    }

    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')

    // Fetch user's writing stats
    const stats = await getUserStats(supabase, user.id, projectId)

    // Generate personalized recommendations
    const recommendations = generatePersonalizedRecommendations(stats)

    return NextResponse.json({ 
      recommendations,
      stats 
    })
  } catch (error) {
    logger.error('Error generating recommendations:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

async function getUserStats(supabase: any, userId: string, projectId?: string | null): Promise<UserStats> {
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * TIME_MS.SECOND)

  // Fetch writing sessions for word count stats
  let sessionsQuery = supabase
    .from('writing_sessions')
    .select('word_count, created_at')
    .eq('user_id', userId)
    .gte('created_at', thirtyDaysAgo.toISOString())

  if (projectId) {
    sessionsQuery = sessionsQuery.eq('project_id', projectId)
  }

  const { data: sessions } = await sessionsQuery

  // Calculate average daily words
  const dailyWords: Record<string, number> = {}
  sessions?.forEach((session: any) => {
    const date = new Date(session.created_at).toDateString()
    dailyWords[date] = (dailyWords[date] || 0) + (session.word_count || 0)
  })
  const avgDailyWords = Object.values(dailyWords).length > 0
    ? Math.round(Object.values(dailyWords).reduce((a, b) => a + b, 0) / Object.values(dailyWords).length)
    : 500

  // Calculate current streak
  const sortedDates = Object.keys(dailyWords).sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
  let currentStreak = 0
  const today = new Date().toDateString()
  const yesterday = new Date(Date.now() - 24 * 60 * 60 * TIME_MS.SECOND).toDateString()
  
  if (sortedDates[0] === today || sortedDates[0] === yesterday) {
    currentStreak = 1
    for (let i = 1; i < sortedDates.length; i++) {
      const currentDate = new Date(sortedDates[i])
      const prevDate = new Date(sortedDates[i - 1])
      const dayDiff = (prevDate.getTime() - currentDate.getTime()) / (24 * 60 * 60 * TIME_MS.SECOND)
      
      if (dayDiff === 1) {
        currentStreak++
      } else {
        break
      }
    }
  }

  // Fetch quality scores
  let chaptersQuery = supabase
    .from('chapters')
    .select('ai_analysis')
    .eq('user_id', userId)
    .not('ai_analysis', 'is', null)

  if (projectId) {
    chaptersQuery = chaptersQuery.eq('project_id', projectId)
  }

  const { data: chapters } = await chaptersQuery
  const qualityScores = chapters?.map((ch: any) => ch.ai_analysis?.quality_score || 0).filter((s: number) => s > 0) || []
  const avgQualityScore = qualityScores.length > 0
    ? Math.round(qualityScores.reduce((a: number, b: number) => a + b, 0) / qualityScores.length)
    : 75

  // Fetch project progress
  let projectProgress = 0
  if (projectId) {
    const { data: project } = await supabase
      .from('projects')
      .select('current_word_count, target_word_count')
      .eq('id', projectId)
      .single()

    if (project?.target_word_count > 0) {
      projectProgress = Math.round((project.current_word_count / project.target_word_count) * 100)
    }
  }

  // Fetch goal stats
  const { data: goals } = await supabase
    .from('writing_goals')
    .select('status')
    .eq('user_id', userId)

  const completedGoals = goals?.filter((g: any) => g.status === 'completed').length || 0
  const activeGoals = goals?.filter((g: any) => g.status === 'active').length || 0

  return {
    avgDailyWords,
    currentStreak,
    avgQualityScore,
    projectProgress,
    completedGoals,
    activeGoals
  }
}

function generatePersonalizedRecommendations(stats: UserStats) {
  const recommendations = []

  // Word Count Recommendations
  if (stats.avgDailyWords < TIME_MS.SECOND) {
    recommendations.push({
      id: 'wc-boost-1',
      type: 'word_count',
      title: 'Boost Daily Output',
      description: `Increase to ${Math.round(stats.avgDailyWords * 1.5)} words daily for a week`,
      targetValue: Math.round(stats.avgDailyWords * 1.5) * 7,
      unit: 'words',
      difficulty: 'medium',
      estimatedDays: 7,
      category: 'Writing Output',
      basedOn: `Currently averaging ${stats.avgDailyWords} words/day`,
      priority: 2
    })
  }

  recommendations.push({
    id: 'wc-challenge-1',
    type: 'word_count',
    title: 'Weekly Writing Challenge',
    description: `Write ${Math.round(stats.avgDailyWords * 7 * 1.3)} words this week`,
    targetValue: Math.round(stats.avgDailyWords * 7 * 1.3),
    unit: 'words',
    difficulty: stats.avgDailyWords > 1500 ? 'hard' : 'medium',
    estimatedDays: 7,
    category: 'Writing Output',
    basedOn: '30% increase from your weekly average',
    priority: 3
  })

  // Streak Recommendations
  if (stats.currentStreak === 0) {
    recommendations.push({
      id: 'streak-start-1',
      type: 'streak',
      title: 'Start a Writing Habit',
      description: 'Write for 3 consecutive days',
      targetValue: 3,
      unit: 'days',
      difficulty: 'easy',
      estimatedDays: 3,
      category: 'Consistency',
      priority: 1
    })
  } else if (stats.currentStreak < 7) {
    recommendations.push({
      id: 'streak-week-1',
      type: 'streak',
      title: 'Build Your Streak',
      description: `Reach a ${Math.max(7, stats.currentStreak + 3)}-day writing streak`,
      targetValue: Math.max(7, stats.currentStreak + 3),
      unit: 'days',
      difficulty: 'easy',
      estimatedDays: Math.max(7, stats.currentStreak + 3) - stats.currentStreak,
      category: 'Consistency',
      basedOn: `Current streak: ${stats.currentStreak} days`,
      priority: 1
    })
  } else {
    recommendations.push({
      id: 'streak-extend-1',
      type: 'streak',
      title: 'Extend Your Streak',
      description: `Reach a ${stats.currentStreak + 7}-day writing streak`,
      targetValue: stats.currentStreak + 7,
      unit: 'days',
      difficulty: 'medium',
      estimatedDays: 7,
      category: 'Consistency',
      basedOn: `Current streak: ${stats.currentStreak} days`,
      priority: 2
    })
  }

  // Quality Recommendations
  if (stats.avgQualityScore < 70) {
    recommendations.push({
      id: 'quality-improve-1',
      type: 'quality',
      title: 'Improve Writing Quality',
      description: 'Achieve 75% quality score on next chapter',
      targetValue: 75,
      unit: 'score',
      difficulty: 'medium',
      estimatedDays: 7,
      category: 'Writing Quality',
      basedOn: `Current average: ${stats.avgQualityScore}%`,
      priority: 1
    })
  } else if (stats.avgQualityScore < 85) {
    recommendations.push({
      id: 'quality-excel-1',
      type: 'quality',
      title: 'Quality Excellence',
      description: `Achieve ${Math.min(stats.avgQualityScore + 10, 90)}% quality score consistently`,
      targetValue: Math.min(stats.avgQualityScore + 10, 90),
      unit: 'score',
      difficulty: 'hard',
      estimatedDays: 14,
      category: 'Writing Quality',
      basedOn: `Current average: ${stats.avgQualityScore}%`,
      priority: 2
    })
  }

  // Project Progress Recommendations
  if (stats.projectProgress > 0 && stats.projectProgress < 25) {
    recommendations.push({
      id: 'milestone-quarter-1',
      type: 'milestone',
      title: 'Reach 25% Completion',
      description: 'Complete first quarter of your project',
      targetValue: 25,
      unit: 'percent',
      difficulty: 'medium',
      estimatedDays: 30,
      category: 'Project Progress',
      priority: 2
    })
  } else if (stats.projectProgress >= 25 && stats.projectProgress < 50) {
    recommendations.push({
      id: 'milestone-half-1',
      type: 'milestone',
      title: 'Halfway Milestone',
      description: 'Reach 50% project completion',
      targetValue: 50,
      unit: 'percent',
      difficulty: 'medium',
      estimatedDays: 45,
      category: 'Project Progress',
      priority: 2
    })
  }

  // Chapter completion recommendation
  recommendations.push({
    id: 'chapter-complete-1',
    type: 'chapter',
    title: 'Chapter Completion',
    description: 'Complete 2 chapters this month',
    targetValue: 2,
    unit: 'chapters',
    difficulty: 'medium',
    estimatedDays: 30,
    category: 'Project Progress',
    priority: 3
  })

  // Sort by priority and return top recommendations
  return recommendations
    .sort((a, b) => (a.priority || 99) - (b.priority || 99))
    .slice(0, 12)
}