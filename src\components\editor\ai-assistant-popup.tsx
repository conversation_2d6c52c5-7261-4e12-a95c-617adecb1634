'use client'

import { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { logger } from '@/lib/services/logger'
import { 
  Lightbulb, 
  X,
  Wand2,
  Edit3,
  ArrowRight,
  BookOpen,
  Sparkles,
  MessageSquare,
  RefreshCw,
  Copy,
  Check
} from 'lucide-react'

interface AiAssistantPopupProps {
  content: string
  cursorPosition: number
  onInsertSuggestion: (text: string) => void
  onClose: () => void
  visible: boolean
}

type ActionType = 'continue' | 'improve' | 'expand' | 'summarize' | 'rephrase' | 'custom'

interface ActionButton {
  id: ActionType
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  color: string
}

const actionButtons: ActionButton[] = [
  {
    id: 'continue',
    label: 'Continue Writing',
    icon: ArrowRight,
    description: 'Continue from where you left off',
    color: 'bg-info hover:bg-info'
  },
  {
    id: 'improve',
    label: 'Improve This',
    icon: Edit3,
    description: 'Enhance the selected text',
    color: 'bg-success hover:bg-success'
  },
  {
    id: 'expand',
    label: 'Expand This',
    icon: Sparkles,
    description: 'Add more detail and depth',
    color: 'bg-purple-500 hover:bg-purple-600'
  },
  {
    id: 'summarize',
    label: 'Summarize',
    icon: BookOpen,
    description: 'Create a concise summary',
    color: 'bg-orange-500 hover:bg-orange-600'
  },
  {
    id: 'rephrase',
    label: 'Rephrase',
    icon: RefreshCw,
    description: 'Say it differently',
    color: 'bg-info hover:bg-info'
  },
  {
    id: 'custom',
    label: 'Custom Prompt',
    icon: MessageSquare,
    description: 'Tell AI what you need',
    color: 'bg-gray-500 hover:bg-gray-600'
  }
]

export function AiAssistantPopup({
  content,
  cursorPosition,
  onInsertSuggestion,
  onClose,
  visible
}: AiAssistantPopupProps) {
  const [selectedAction, setSelectedAction] = useState<ActionType | null>(null)
  const [customPrompt, setCustomPrompt] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [generatedText, setGeneratedText] = useState('')
  const [copied, setCopied] = useState(false)

  const getContext = useCallback(() => {
    // Get context around cursor position
    const beforeCursor = content.substring(Math.max(0, cursorPosition - 500), cursorPosition)
    const afterCursor = content.substring(cursorPosition, cursorPosition + 100)
    return { beforeCursor, afterCursor }
  }, [content, cursorPosition])

  const handleAction = useCallback(async (actionType: ActionType) => {
    if (actionType === 'custom' && !customPrompt.trim()) {
      setSelectedAction('custom')
      return
    }

    setIsProcessing(true)
    setSelectedAction(actionType)
    
    try {
      const { beforeCursor, afterCursor } = getContext()
      
      const response = await fetch('/api/agents/edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: actionType,
          beforeCursor,
          afterCursor,
          customPrompt: actionType === 'custom' ? customPrompt : undefined,
          fullContent: content
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setGeneratedText(data.editedText || data.suggestion || '')
      } else {
        logger.error('AI Assistant error:', data.error)
        setGeneratedText('')
      }
    } catch (error) {
      logger.error('Error calling AI assistant:', error)
      setGeneratedText('')
    } finally {
      setIsProcessing(false)
    }
  }, [content, cursorPosition, customPrompt, getContext])

  const handleInsert = useCallback(() => {
    if (generatedText) {
      onInsertSuggestion(generatedText)
      setGeneratedText('')
      setSelectedAction(null)
      setCustomPrompt('')
      onClose()
    }
  }, [generatedText, onInsertSuggestion, onClose])

  const handleCopy = useCallback(async () => {
    if (generatedText) {
      await navigator.clipboard.writeText(generatedText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }, [generatedText])

  const handleReset = useCallback(() => {
    setSelectedAction(null)
    setGeneratedText('')
    setCustomPrompt('')
  }, [])

  if (!visible) return null

  return (
    <Card className="absolute top-16 right-16 w-96 max-h-[80vh] z-50 shadow-xl border-2">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-primary/10 rounded-full">
              <Lightbulb className="h-4 w-4 text-primary" />
            </div>
            <CardTitle className="text-base">AI Writing Assistant</CardTitle>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Action Buttons Grid */}
        {!selectedAction && !generatedText && (
          <div className="grid grid-cols-2 gap-3">
            {actionButtons.map((action) => {
              const Icon = action.icon
              return (
                <Button
                  key={action.id}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 hover:border-primary/50 transition-all"
                  onClick={() => handleAction(action.id)}
                  disabled={isProcessing}
                >
                  <div className={`p-2 rounded-full text-white ${action.color}`}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-sm">{action.label}</div>
                    <div className="text-xs text-muted-foreground">{action.description}</div>
                  </div>
                </Button>
              )
            })}
          </div>
        )}

        {/* Custom Prompt Input */}
        {selectedAction === 'custom' && !generatedText && (
          <div className="space-y-3">
            <div className="text-sm font-medium">What would you like the AI to do?</div>
            <Textarea
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="E.g., Make this more emotional, add sensory details, change the tone to be more mysterious..."
              className="min-h-[100px]"
              autoFocus
            />
            <div className="flex gap-2">
              <Button
                onClick={() => handleAction('custom')}
                disabled={!customPrompt.trim() || isProcessing}
                className="flex-1"
              >
                {isProcessing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    Generate
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={handleReset}>
                Back
              </Button>
            </div>
          </div>
        )}

        {/* Processing State */}
        {isProcessing && selectedAction !== 'custom' && (
          <div className="flex flex-col items-center justify-center py-8 space-y-3">
            <div className="p-3 bg-primary/10 rounded-full animate-pulse">
              <Wand2 className="h-6 w-6 text-primary animate-spin" />
            </div>
            <p className="text-sm text-muted-foreground">
              {selectedAction === 'continue' && 'Continuing your story...'}
              {selectedAction === 'improve' && 'Improving your text...'}
              {selectedAction === 'expand' && 'Expanding your content...'}
              {selectedAction === 'summarize' && 'Creating summary...'}
              {selectedAction === 'rephrase' && 'Rephrasing your text...'}
            </p>
          </div>
        )}

        {/* Generated Text Result */}
        {generatedText && !isProcessing && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                AI Suggestion
              </Badge>
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  title="Copy to clipboard"
                >
                  {copied ? (
                    <Check className="h-4 w-4 text-success" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            
            <div className="p-3 bg-muted rounded-lg max-h-64 overflow-y-auto">
              <p className="text-sm whitespace-pre-wrap">{generatedText}</p>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={handleInsert} className="flex-1">
                <Check className="h-4 w-4 mr-2" />
                Insert Text
              </Button>
              <Button 
                variant="outline" 
                onClick={() => handleAction(selectedAction!)}
                title="Try again"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button variant="outline" onClick={handleReset}>
                Back
              </Button>
            </div>
          </div>
        )}

        {/* Context Preview */}
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            Cursor position: {cursorPosition} | Content length: {content.length}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}