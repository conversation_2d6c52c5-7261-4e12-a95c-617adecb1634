/**
 * Streaming Service
 * Unified service for handling all streaming operations with proper typing
 */

import { EventEmitter } from 'events';
import {
  StreamEvent,
  StreamEventType,
  StreamHandler,
  StreamMetadata,
  StreamProgress,
  StreamToken,
  SSEMessage,
  SSEStreamOptions,
  createStreamId,
  calculateProgress,
  estimateTimeRemaining,
  streamEventSchema,
  ContentGenerationRequest,
  StructuredStreamResponse,
  ChapterStreamData,
  CharacterStreamData,
  AnalysisStreamData,
  AgentStreamData
} from '@/lib/types/streaming-types';
import { logger } from './logger';
import { trackError, trackFeatureUsage } from '@/lib/monitoring/sentry';
import { TIME_MS } from '@/lib/constants/time'
import { DEMO_VALUES } from '@/lib/constants'

export class StreamingService extends EventEmitter {
  private activeStreams: Map<string, StreamContext> = new Map();
  private readonly MAX_CONCURRENT_STREAMS = 5;

  constructor() {
    super();
    this.setMaxListeners(20);
  }

  /**
   * Stream content generation with typed responses
   */
  async streamContent<T = string>(
    request: ContentGenerationRequest,
    handler: StreamHandler<T>
  ): Promise<StructuredStreamResponse<T>> {
    const streamId = createStreamId();
    const context = this.createStreamContext(streamId, request);

    try {
      this.validateConcurrentStreams();
      this.activeStreams.set(streamId, context);

      trackFeatureUsage('streaming_content', {
        contentType: request.contentType,
        model: request.model,
        streaming: true
      });

      const response = await fetch('/api/ai/stream-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          streamId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await this.processStream<T>(response, context, handler);
    } catch (error) {
      this.handleStreamError(streamId, error as Error, handler);
      throw error;
    } finally {
      this.activeStreams.delete(streamId);
    }
  }

  /**
   * Stream chapter generation with structured data
   */
  async streamChapter(
    prompt: string,
    context: ContentGenerationRequest['context'],
    handler: StreamHandler<ChapterStreamData>
  ): Promise<StructuredStreamResponse<ChapterStreamData>> {
    return this.streamContent<ChapterStreamData>(
      {
        prompt,
        contentType: 'chapter',
        context,
        streaming: true,
        estimatedTokens: TIME_MS.TYPING_TIMEOUT
      },
      handler
    );
  }

  /**
   * Stream character development
   */
  async streamCharacter(
    characterName: string,
    context: ContentGenerationRequest['context'],
    handler: StreamHandler<CharacterStreamData>
  ): Promise<StructuredStreamResponse<CharacterStreamData>> {
    return this.streamContent<CharacterStreamData>(
      {
        prompt: `Develop a comprehensive character profile for ${characterName}`,
        contentType: 'character',
        context,
        streaming: true,
        estimatedTokens: DEMO_VALUES.LARGE_REQUEST
      },
      handler
    );
  }

  /**
   * Stream content analysis
   */
  async streamAnalysis(
    content: string,
    analysisType: string,
    handler: StreamHandler<AnalysisStreamData>
  ): Promise<StructuredStreamResponse<AnalysisStreamData>> {
    return this.streamContent<AnalysisStreamData>(
      {
        prompt: `Analyze the following content for ${analysisType}: ${content}`,
        contentType: 'general',
        streaming: true,
        estimatedTokens: DEMO_VALUES.MEDIUM_REQUEST
      },
      handler
    );
  }

  /**
   * Stream agent processing
   */
  async streamAgent(
    agentType: string,
    input: unknown,
    handler: StreamHandler<AgentStreamData>
  ): Promise<StructuredStreamResponse<AgentStreamData>> {
    return this.streamContent<AgentStreamData>(
      {
        prompt: JSON.stringify({ agentType, input }),
        contentType: 'general',
        streaming: true,
        estimatedTokens: DEMO_VALUES.XLARGE_REQUEST
      },
      handler
    );
  }

  /**
   * Process SSE stream with typed events
   */
  async processSSEStream(
    options: SSEStreamOptions,
    handler: StreamHandler
  ): Promise<void> {
    const streamId = createStreamId();
    const eventSource = new EventSource(options.url);

    try {
      await new Promise<void>((resolve, reject) => {
        eventSource.onopen = () => {
          logger.info(`SSE stream opened: ${streamId}`);
        };

        eventSource.onmessage = (event) => {
          try {
            const message: SSEMessage = {
              data: event.data,
              id: event.lastEventId,
              event: event.type
            };

            if (options.onMessage) {
              options.onMessage(message);
            }

            // Parse and handle typed events
            const parsedData = JSON.parse(event.data);
            this.handleStreamEvent(parsedData, handler);

            if (parsedData.type === StreamEventType.COMPLETE) {
              resolve();
            }
          } catch (error) {
            logger.error('Error parsing SSE message:', error);
          }
        };

        eventSource.onerror = (error) => {
          logger.error('SSE stream error:', error);
          if (options.onError) {
            options.onError(new Error('SSE stream error'));
          }
          reject(error);
        };

        // Handle abort signal
        if (options.signal) {
          options.signal.addEventListener('abort', () => {
            eventSource.close();
            reject(new Error('Stream aborted'));
          });
        }
      });
    } finally {
      eventSource.close();
      if (options.onClose) {
        options.onClose();
      }
    }
  }

  /**
   * Create a readable stream processor
   */
  createStreamProcessor<T>(): TransformStream<Uint8Array, StreamEvent> {
    let buffer = '';

    return new TransformStream({
      transform(chunk, controller) {
        const text = new TextDecoder().decode(chunk);
        buffer += text;

        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim().startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              const event = streamEventSchema.parse(data);
              controller.enqueue(event);
            } catch (error) {
              logger.error('Failed to parse stream event:', error);
            }
          }
        }
      },

      flush(controller) {
        if (buffer.trim()) {
          try {
            const data = JSON.parse(buffer);
            const event = streamEventSchema.parse(data);
            controller.enqueue(event);
          } catch (error) {
            logger.error('Failed to parse final stream data:', error);
          }
        }
      }
    });
  }

  // Private methods
  private createStreamContext(
    streamId: string,
    request: ContentGenerationRequest
  ): StreamContext {
    return {
      streamId,
      request,
      startTime: Date.now(),
      tokens: 0,
      content: '',
      metadata: {
        model: request.model || 'gpt-4',
        temperature: request.temperature || 0.7,
        maxTokens: request.maxTokens || 2000,
        streamId,
        startTime: Date.now()
      }
    };
  }

  private async processStream<T>(
    response: Response,
    context: StreamContext,
    handler: StreamHandler<T>
  ): Promise<StructuredStreamResponse<T>> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    // Emit start event
    this.emitStreamEvent({
      type: StreamEventType.START,
      timestamp: Date.now(),
      streamId: context.streamId,
      metadata: context.metadata
    }, handler);

    let structuredData: T | undefined;
    const startTime = Date.now();
    let lastProgressUpdate = startTime;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const events = this.parseStreamChunk(chunk);

        for (const event of events) {
          switch (event.type) {
            case 'token':
              context.tokens++;
              context.content += event.data.token;
              
              this.emitStreamEvent({
                type: StreamEventType.TOKEN,
                timestamp: Date.now(),
                streamId: context.streamId,
                data: {
                  token: event.data.token,
                  timestamp: Date.now(),
                  index: context.tokens
                }
              }, handler);

              // Update progress periodically
              if (Date.now() - lastProgressUpdate > 100) {
                this.emitProgressUpdate(context, handler);
                lastProgressUpdate = Date.now();
              }
              break;

            case 'structure':
              structuredData = event.data as T;
              this.emitStreamEvent({
                type: StreamEventType.STRUCTURE,
                timestamp: Date.now(),
                streamId: context.streamId,
                data: {
                  partial: !done,
                  structure: structuredData
                }
              }, handler);
              break;

            case 'quality':
              this.emitStreamEvent({
                type: StreamEventType.QUALITY,
                timestamp: Date.now(),
                streamId: context.streamId,
                data: event.data
              }, handler);
              break;

            case 'metadata':
              this.emitStreamEvent({
                type: StreamEventType.METADATA,
                timestamp: Date.now(),
                streamId: context.streamId,
                data: event.data
              }, handler);
              break;
          }
        }
      }

      // Final progress update
      this.emitProgressUpdate(context, handler, true);

      // Emit completion
      const duration = Date.now() - startTime;
      this.emitStreamEvent({
        type: StreamEventType.COMPLETE,
        timestamp: Date.now(),
        streamId: context.streamId,
        data: {
          content: context.content,
          tokens: context.tokens,
          duration,
          metadata: context.metadata
        }
      }, handler);

      return {
        content: context.content,
        structured: structuredData as T,
        metadata: {
          model: context.metadata.model,
          tokens: context.tokens,
          processingTime: duration
        }
      };
    } catch (error) {
      this.handleStreamError(context.streamId, error as Error, handler);
      throw error;
    }
  }

  private parseStreamChunk(chunk: string): Array<{ type: string; data: unknown }> {
    const events: Array<{ type: string; data: unknown }> = [];
    const lines = chunk.split('\n').filter(line => line.trim());

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          const data = JSON.parse(line.slice(6));
          events.push(data);
        } catch {
          // Skip invalid JSON
        }
      }
    }

    return events;
  }

  private emitStreamEvent(event: StreamEvent, handler: StreamHandler): void {
    // Emit to handler
    switch (event.type) {
      case StreamEventType.START:
        handler.onStart?.(event.metadata);
        break;
      case StreamEventType.TOKEN:
        handler.onToken?.(event.data);
        break;
      case StreamEventType.PROGRESS:
        handler.onProgress?.(event.data);
        break;
      case StreamEventType.QUALITY:
        handler.onQuality?.(event.data.score, event.data.metrics);
        break;
      case StreamEventType.STRUCTURE:
        handler.onStructure?.(event.data.structure, event.data.partial);
        break;
      case StreamEventType.COMPLETE:
        handler.onComplete?.(event.data.content, event.data.metadata);
        break;
      case StreamEventType.ERROR:
        handler.onError?.(event.data);
        break;
      case StreamEventType.ABORT:
        handler.onAbort?.(event.data.reason, event.data.partial);
        break;
    }

    // Emit to EventEmitter
    this.emit('stream:event', event);
    this.emit(`stream:${event.type}`, event);
  }

  private emitProgressUpdate(
    context: StreamContext,
    handler: StreamHandler,
    isFinal = false
  ): void {
    const elapsed = Date.now() - context.startTime;
    const tokensPerSecond = elapsed > 0 ? (context.tokens / elapsed) * TIME_MS.SECOND : 0;
    const estimatedTokens = context.request.estimatedTokens || 2000;
    const percentComplete = isFinal ? 100 : calculateProgress(context.tokens, estimatedTokens);
    const estimatedTimeRemaining = isFinal ? 0 : estimateTimeRemaining(
      context.tokens,
      estimatedTokens,
      tokensPerSecond
    );

    const progress: StreamProgress = {
      tokens: context.tokens,
      estimatedTokens,
      percentComplete,
      tokensPerSecond,
      estimatedTimeRemaining
    };

    this.emitStreamEvent({
      type: StreamEventType.PROGRESS,
      timestamp: Date.now(),
      streamId: context.streamId,
      data: progress
    }, handler);
  }

  private handleStreamError(
    streamId: string,
    error: Error,
    handler: StreamHandler
  ): void {
    const streamError = {
      code: 'STREAM_ERROR',
      message: error.message,
      details: { streamId },
      recoverable: false
    };

    this.emitStreamEvent({
      type: StreamEventType.ERROR,
      timestamp: Date.now(),
      streamId,
      data: streamError
    }, handler);

    trackError(error, {
      tags: { component: 'streaming', streamId }
    });
  }

  private handleStreamEvent(data: unknown, handler: StreamHandler): void {
    try {
      const event = streamEventSchema.parse(data);
      this.emitStreamEvent(event, handler);
    } catch (error) {
      logger.error('Invalid stream event:', error);
    }
  }

  private validateConcurrentStreams(): void {
    if (this.activeStreams.size >= this.MAX_CONCURRENT_STREAMS) {
      throw new Error(
        `Maximum concurrent streams (${this.MAX_CONCURRENT_STREAMS}) reached`
      );
    }
  }

  // Public utility methods
  cancelStream(streamId: string, reason = 'User cancelled'): void {
    const context = this.activeStreams.get(streamId);
    if (context) {
      this.emitStreamEvent({
        type: StreamEventType.ABORT,
        timestamp: Date.now(),
        streamId,
        data: {
          reason,
          partial: context.content
        }
      }, {} as StreamHandler);
      
      this.activeStreams.delete(streamId);
    }
  }

  getActiveStreams(): string[] {
    return Array.from(this.activeStreams.keys());
  }

  isStreamActive(streamId: string): boolean {
    return this.activeStreams.has(streamId);
  }
}

// Internal types
interface StreamContext {
  streamId: string;
  request: ContentGenerationRequest;
  startTime: number;
  tokens: number;
  content: string;
  metadata: StreamMetadata;
}

// Export singleton instance
export const streamingService = new StreamingService();