# BookScribe Memory Optimization Architecture

## Overview

The memory optimization system in BookScribe is designed to intelligently manage AI context across long-form writing projects. It ensures that AI agents maintain consistency while staying within token limits, even for novels exceeding 100k+ words.

## How Memory Management Works

### 1. Unified Context System (Single Source of Truth)

All project data flows through a centralized `BookContext` that serves as the single source of truth:

```typescript
interface BookContext {
  projectId: string
  settings: ProjectSettings
  storyPrompt?: string
  targetWordCount?: number
  
  // All content is pulled from these core sources
  chapters: Chapter[]
  storyBible: StoryBibleEntry[]
  characters: Character[]
  locations: Location[]
  timeline: TimelineEvent[]
  references: ReferenceDocument[]
}
```

### 2. Memory Hierarchy

The system uses a hierarchical approach to prioritize content:

```
Priority 1: Active Context (What user is currently working on)
├── Current chapter
├── Previous/next chapters
└── Recently edited content

Priority 2: Essential Story Elements
├── Main character profiles
├── Key plot points
├── Active story arcs
└── Current timeline events

Priority 3: Reference Material
├── Story bible entries
├── World-building details
├── Character relationships
└── Location descriptions

Priority 4: Historical Content
├── Earlier chapters
├── Completed arcs
└── Archived notes
└── Old versions
```

### 3. Automatic Memory Optimization

When memory usage exceeds 80% of token limits:

1. **Compression Strategy**
   - Summarizes older chapters while preserving key plot points
   - Condenses character descriptions to essential traits
   - Merges similar story bible entries
   - Archives completed story arcs

2. **Context Window Management**
   ```
   [Active Window: 40% tokens]
   - Current chapter full text
   - Active character states
   - Immediate plot context
   
   [Reference Window: 30% tokens]  
   - Character summaries
   - Location descriptions
   - Story bible excerpts
   
   [Historical Window: 20% tokens]
   - Chapter summaries
   - Past event references
   - Completed arc outcomes
   
   [Buffer: 10% tokens]
   - Safety margin for responses
   ```

### 4. Integration with All Systems

#### Story Bible Integration
```typescript
// Story bible entries are automatically indexed and compressed
const storyBibleContext = await memoryManager.getRelevantContext({
  type: 'story_bible',
  relevantTo: currentChapter,
  maxTokens: 5000
})
```

#### Character Information Management
```typescript
// Character data is dynamically loaded based on who appears in current scene
const activeCharacters = await memoryManager.getActiveCharacters({
  chapterId: current.id,
  includeRelationships: true,
  compressionLevel: 'moderate'
})
```

#### Timeline & Events
```typescript
// Timeline events are filtered to show only relevant time periods
const relevantTimeline = await memoryManager.getTimelineContext({
  startDate: currentScene.date - 30, // 30 days before
  endDate: currentScene.date + 7,    // 7 days after
  importance: 'high'
})
```

### 5. Smart Context Loading

The system intelligently loads context based on what's needed:

```typescript
// When writing a character dialogue
if (isWritingDialogue) {
  memoryManager.prioritize([
    'character.voice_profile',
    'character.personality',
    'character.relationships',
    'recent_dialogue_samples'
  ])
}

// When describing a location
if (isDescribingLocation) {
  memoryManager.prioritize([
    'location.description',
    'location.atmosphere',
    'location.history',
    'previous_scenes_here'
  ])
}
```

### 6. Embedding-Based Retrieval

All content is embedded for semantic search:

1. **Content Indexing**
   - Chapters are chunked and embedded
   - Story bible entries create semantic vectors
   - Character profiles generate personality embeddings

2. **Relevant Context Retrieval**
   ```typescript
   // Find relevant context using semantic similarity
   const relevantContext = await embeddingService.findSimilar({
     query: currentParagraph,
     sources: ['chapters', 'story_bible', 'characters'],
     limit: 10,
     threshold: 0.8
   })
   ```

### 7. Memory Dashboard Features

The Memory Dashboard provides:

- **Real-time Usage Monitoring**: See token usage across all content types
- **Optimization Controls**: Manual and automatic compression options
- **Context Preview**: View what context AI sees for current work
- **Usage History**: Track memory usage patterns over time
- **Cache Management**: Clear outdated embeddings and contexts

### 8. Auto-Save Integration

During auto-save, the system:
1. Checks current memory usage
2. If above 80%, triggers automatic optimization
3. Compresses older content while preserving recent work
4. Updates embeddings for changed content
5. Maintains writing flow without interruption

## Benefits of This Architecture

1. **Consistency**: Single source of truth ensures all AI agents see the same data
2. **Scalability**: Can handle novels of any length through intelligent compression
3. **Performance**: Selective loading keeps responses fast
4. **Flexibility**: Adapts context based on current writing needs
5. **Transparency**: Users can see and control what context AI uses

## Example: Writing Chapter 50 of a 100k Word Novel

```
Total Content: 100,000 words (~133k tokens)
AI Context Limit: 100k tokens

Memory Manager automatically creates:
- Full text of Chapter 49, 50, 51 (6k tokens)
- Summaries of Chapters 1-48 (10k tokens)  
- Active character profiles (8k tokens)
- Current story arc details (5k tokens)
- Relevant story bible entries (4k tokens)
- Timeline for current period (2k tokens)
- Available context: 35k tokens used, 65k available
```

This ensures the AI has all necessary context while staying well within limits!