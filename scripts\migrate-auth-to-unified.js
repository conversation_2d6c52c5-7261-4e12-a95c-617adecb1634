const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all API route files
const apiRoutes = glob.sync('src/app/api/**/*.ts', {
  ignore: ['**/route.refactored.ts', '**/*.test.ts', '**/*.spec.ts']
});

console.log(`Found ${apiRoutes.length} API route files to check`);

let updatedCount = 0;
let skippedCount = 0;
let errorCount = 0;

apiRoutes.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Skip if already using UnifiedAuthService
    if (content.includes('UnifiedAuthService')) {
      console.log(`✓ Already migrated: ${filePath}`);
      skippedCount++;
      return;
    }
    
    // Skip if it's a webhook or public route
    if (filePath.includes('/webhooks/') || filePath.includes('/health/')) {
      console.log(`⚠️  Skipping public route: ${filePath}`);
      skippedCount++;
      return;
    }
    
    // Check if it uses the old auth pattern
    if (!content.includes('createServerClient') || !content.includes('supabase.auth.getUser()')) {
      console.log(`⚠️  No auth pattern found: ${filePath}`);
      skippedCount++;
      return;
    }
    
    console.log(`🔄 Migrating: ${filePath}`);
    
    // Add UnifiedAuthService import if not present
    if (!content.includes("import { UnifiedAuthService")) {
      const lastImportMatch = content.match(/import.*from.*\n(?!import)/);
      if (lastImportMatch) {
        const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
        content = content.slice(0, insertPosition) + 
          "import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'\n" +
          content.slice(insertPosition);
      }
    }
    
    // Pattern 1: export async function GET/POST/PUT/DELETE
    content = content.replace(
      /export async function (GET|POST|PUT|DELETE|PATCH)\s*\(\s*request:\s*NextRequest\s*\)\s*{\s*try\s*{\s*const supabase = await createServerClient\(\)\s*\n\s*\/\/\s*Check authentication\s*\n\s*const\s*{\s*data:\s*{\s*user\s*}\s*}\s*=\s*await\s*supabase\.auth\.getUser\(\)\s*\n\s*if\s*\(\s*!user\s*\)\s*{\s*[^}]+}\s*/g,
      (match, method) => {
        return `export const ${method} = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!
    const supabase = await createServerClient()
    `;
      }
    );
    
    // Pattern 2: Handle closing braces
    if (content !== originalContent) {
      // Count opening and closing braces to find where to add the closing parenthesis
      const functionMatches = content.match(/export const (GET|POST|PUT|DELETE|PATCH) = UnifiedAuthService\.withAuth/g) || [];
      
      functionMatches.forEach((match) => {
        const method = match.match(/(GET|POST|PUT|DELETE|PATCH)/)[1];
        
        // Find the corresponding closing brace
        const startIndex = content.indexOf(match);
        let braceCount = 0;
        let foundStart = false;
        let endIndex = -1;
        
        for (let i = startIndex; i < content.length; i++) {
          if (content[i] === '{') {
            if (!foundStart) foundStart = true;
            braceCount++;
          } else if (content[i] === '}') {
            braceCount--;
            if (foundStart && braceCount === 0) {
              endIndex = i;
              break;
            }
          }
        }
        
        if (endIndex !== -1 && content[endIndex + 1] !== ')') {
          content = content.slice(0, endIndex + 1) + ')' + content.slice(endIndex + 1);
        }
      });
    }
    
    // Save the file if changes were made
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated: ${filePath}`);
      updatedCount++;
    } else {
      console.log(`⚠️  Could not migrate: ${filePath} - Manual update needed`);
      errorCount++;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    errorCount++;
  }
});

console.log('\n📊 Migration Summary:');
console.log(`✅ Updated: ${updatedCount} files`);
console.log(`⏭️  Skipped: ${skippedCount} files`);
console.log(`❌ Errors: ${errorCount} files`);
console.log('\nNote: Some files may need manual review, especially those with complex auth patterns.');