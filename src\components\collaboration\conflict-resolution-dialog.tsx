'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Users, Clock, FileText, AlertTriangle, CheckCircle2 } from 'lucide-react'
import type { CollaborationChange } from '@/lib/services/unified-collaboration-service'

interface ConflictData {
  id: string
  changes: CollaborationChange[]
  userNames?: Map<string, string>
}

interface ConflictResolutionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  conflicts: ConflictData[]
  onResolve: (conflictId: string, resolution: CollaborationChange) => void
  onResolveAll: (strategy: 'mine' | 'theirs' | 'newest' | 'oldest') => void
}

export function ConflictResolutionDialog({
  open,
  onOpenChange,
  conflicts,
  onResolve,
  onResolveAll
}: ConflictResolutionDialogProps) {
  const [selectedResolutions, setSelectedResolutions] = useState<Map<string, string>>(new Map())
  const [resolveAllStrategy, setResolveAllStrategy] = useState<string>('')

  const handleResolve = (conflictId: string, changeId: string) => {
    const conflict = conflicts.find(c => c.id === conflictId)
    if (!conflict) return

    const selectedChange = conflict.changes.find(c => 
      `${c.userId}-${c.timestamp}` === changeId
    )
    if (!selectedChange) return

    onResolve(conflictId, selectedChange)
  }

  const handleResolveAll = () => {
    if (!resolveAllStrategy) return
    onResolveAll(resolveAllStrategy as 'mine' | 'theirs' | 'newest' | 'oldest')
  }

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString()
  }

  const getChangePreview = (change: CollaborationChange) => {
    const lines = change.text.split('\n')
    const preview = lines.slice(0, 3).join('\n')
    return lines.length > 3 ? `${preview}...` : preview
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-warning" />
            Resolve Editing Conflicts
          </DialogTitle>
          <DialogDescription>
            Multiple users edited the same sections. Choose which changes to keep.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {conflicts.length > 1 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Multiple Conflicts Detected</AlertTitle>
              <AlertDescription className="space-y-3">
                <p>You have {conflicts.length} conflicts to resolve.</p>
                <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
                  <Label>Resolve all conflicts:</Label>
                  <RadioGroup 
                    value={resolveAllStrategy} 
                    onValueChange={setResolveAllStrategy}
                    className="flex gap-4 sm:gap-5 lg:gap-6"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="mine" id="mine" />
                      <Label htmlFor="mine">Keep all my changes</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="theirs" id="theirs" />
                      <Label htmlFor="theirs">Keep all their changes</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="newest" id="newest" />
                      <Label htmlFor="newest">Keep newest changes</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="oldest" id="oldest" />
                      <Label htmlFor="oldest">Keep oldest changes</Label>
                    </div>
                  </RadioGroup>
                  <Button 
                    size="sm" 
                    onClick={handleResolveAll}
                    disabled={!resolveAllStrategy}
                  >
                    Apply to All
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-6">
              {conflicts.map((conflict, index) => {
                const selectedValue = selectedResolutions.get(conflict.id) || ''
                return (
                  <Card key={conflict.id}>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center justify-between">
                        <span>Conflict {index + 1} of {conflicts.length}</span>
                        <Badge variant="outline">
                          {conflict.changes.length} versions
                        </Badge>
                      </CardTitle>
                      <CardDescription>
                        Choose which version to keep for this section
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Tabs defaultValue="compare" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="compare">Compare Changes</TabsTrigger>
                          <TabsTrigger value="preview">Preview Result</TabsTrigger>
                        </TabsList>
                        <TabsContent value="compare" className="space-y-3">
                          <RadioGroup 
                            value={selectedValue}
                            onValueChange={(value) => {
                              const newSelections = new Map(selectedResolutions)
                              newSelections.set(conflict.id, value)
                              setSelectedResolutions(newSelections)
                            }}
                          >
                            {conflict.changes.map((change) => {
                              const changeId = `${change.userId}-${change.timestamp}`
                              const userName = conflict.userNames?.get(change.userId) || 'Unknown User'
                              return (
                                <div key={changeId} className="space-y-2">
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value={changeId} id={changeId} />
                                    <Label 
                                      htmlFor={changeId} 
                                      className="flex-1 cursor-pointer"
                                    >
                                      <Card className="p-3">
                                        <div className="flex items-center justify-between mb-2">
                                          <div className="flex items-center gap-2">
                                            <Users className="h-4 w-4" />
                                            <span className="font-medium">{userName}</span>
                                          </div>
                                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                            <Clock className="h-3 w-3" />
                                            {formatTimestamp(change.timestamp)}
                                          </div>
                                        </div>
                                        <div className="space-y-1">
                                          <div className="flex items-center gap-2 text-sm">
                                            <Badge variant="secondary" className="text-xs">
                                              {change.type}
                                            </Badge>
                                            <span className="text-muted-foreground">
                                              Lines {change.range.startLine}-{change.range.endLine}
                                            </span>
                                          </div>
                                          <pre className="text-sm bg-muted p-2 rounded-md overflow-x-auto">
                                            {getChangePreview(change)}
                                          </pre>
                                        </div>
                                      </Card>
                                    </Label>
                                  </div>
                                </div>
                              )
                            })}
                          </RadioGroup>
                        </TabsContent>
                        <TabsContent value="preview">
                          {selectedValue ? (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <FileText className="h-4 w-4" />
                                <span>Preview of selected version</span>
                              </div>
                              <pre className="text-sm bg-muted p-4 rounded-md overflow-x-auto">
                                {conflict.changes.find(c => 
                                  `${c.userId}-${c.timestamp}` === selectedValue
                                )?.text || ''}
                              </pre>
                            </div>
                          ) : (
                            <div className="text-center py-6 sm:py-8 lg:py-10 text-muted-foreground">
                              Select a version to preview
                            </div>
                          )}
                        </TabsContent>
                      </Tabs>
                      <div className="mt-4 flex justify-end">
                        <Button
                          size="sm"
                          onClick={() => handleResolve(conflict.id, selectedValue)}
                          disabled={!selectedValue}
                        >
                          <CheckCircle2 className="h-4 w-4 mr-2" />
                          Apply This Resolution
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={() => {
              // Apply all selected resolutions
              selectedResolutions.forEach((changeId, conflictId) => {
                handleResolve(conflictId, changeId)
              })
              onOpenChange(false)
            }}
            disabled={selectedResolutions.size !== conflicts.length}
          >
            Resolve {selectedResolutions.size} of {conflicts.length} Conflicts
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}