import { NextResponse } from 'next/server'
import { logger } from '@/lib/services/logger'

export interface ApiError extends Error {
  statusCode?: number
  code?: string
}

export function createApiError(
  message: string,
  statusCode: number = 500,
  code?: string
): ApiError {
  const error: ApiError = new Error(message)
  error.statusCode = statusCode
  error.code = code
  return error
}

export function handleApiError(error: unknown) {
  // Log the error
  logger.error('API Error:', error)

  // Default error response
  let statusCode = 500
  let message = 'Internal Server Error'
  let code = 'INTERNAL_ERROR'

  // Handle different error types
  if (error instanceof Error) {
    message = error.message
    
    if ('statusCode' in error && typeof error.statusCode === 'number') {
      statusCode = error.statusCode
    }
    
    if ('code' in error && typeof error.code === 'string') {
      code = error.code
    }
  }

  // Common error mappings
  if (message.includes('Unauthorized')) {
    statusCode = 401
    code = 'UNAUTHORIZED'
  } else if (message.includes('Forbidden')) {
    statusCode = 403
    code = 'FORBIDDEN'
  } else if (message.includes('Not found')) {
    statusCode = 404
    code = 'NOT_FOUND'
  } else if (message.includes('Bad request')) {
    statusCode = 400
    code = 'BAD_REQUEST'
  }

  // Return error response
  return NextResponse.json(
    {
      error: {
        message: process.env.NODE_ENV === 'production' ? 'An error occurred' : message,
        code,
        timestamp: new Date().toISOString(),
      }
    },
    { status: statusCode }
  )
}

// Wrapper for API route handlers
type ApiHandler = (request: Request, context?: unknown) => Promise<NextResponse>

export function withErrorHandler<T extends ApiHandler>(
  handler: T
): T {
  return (async (...args) => {
    try {
      return await handler(...args)
    } catch (error) {
      return handleApiError(error)
    }
  }) as T
}