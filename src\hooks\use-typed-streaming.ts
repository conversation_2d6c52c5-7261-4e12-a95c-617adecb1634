/**
 * Enhanced React Hook for Typed Streaming
 * Provides strongly-typed streaming functionality with the new streaming service
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { streamingService } from '@/lib/services/streaming-service';
import {
  StreamHandler,
  StreamProgress,
  StreamToken,
  StreamError,
  ContentGenerationRequest,
  StructuredStreamResponse,
  ChapterStreamData,
  CharacterStreamData,
  AnalysisStreamData,
  AgentStreamData,
  ContentType
} from '@/lib/types/streaming-types';
import { useToast } from '@/components/ui/use-toast';
import { trackFeatureUsage } from '@/lib/monitoring/sentry';

// Hook state interface
interface StreamingState<T> {
  content: string;
  structured?: T;
  isStreaming: boolean;
  isLoading: boolean;
  progress: StreamProgress;
  error: StreamError | null;
  quality?: number;
  metadata?: Record<string, unknown>;
}

// Hook options
interface UseTypedStreamingOptions<T> {
  onComplete?: (content: string, structured?: T) => void;
  onError?: (error: StreamError) => void;
  onProgress?: (progress: StreamProgress) => void;
  onToken?: (token: StreamToken) => void;
  onQuality?: (quality: number) => void;
  onStructure?: (structure: T, partial: boolean) => void;
  autoRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

// Base hook for typed streaming
export function useTypedStreaming<T = unknown>(
  options: UseTypedStreamingOptions<T> = {}
) {
  const [state, setState] = useState<StreamingState<T>>({
    content: '',
    isStreaming: false,
    isLoading: false,
    progress: {
      tokens: 0,
      estimatedTokens: 1000,
      percentComplete: 0,
      tokensPerSecond: 0,
      estimatedTimeRemaining: 0
    },
    error: null
  });

  const streamIdRef = useRef<string | null>(null);
  const retryCountRef = useRef(0);
  const { toast } = useToast();

  // Create stream handler
  const createHandler = useCallback((): StreamHandler<T> => ({
    onStart: (metadata) => {
      setState(prev => ({
        ...prev,
        isLoading: false,
        isStreaming: true,
        metadata
      }));
    },
    onToken: (token) => {
      setState(prev => ({
        ...prev,
        content: prev.content + token.token
      }));
      options.onToken?.(token);
    },
    onProgress: (progress) => {
      setState(prev => ({
        ...prev,
        progress
      }));
      options.onProgress?.(progress);
    },
    onQuality: (quality, metrics) => {
      setState(prev => ({
        ...prev,
        quality,
        metadata: { ...prev.metadata, qualityMetrics: metrics }
      }));
      options.onQuality?.(quality);
    },
    onStructure: (structure, partial) => {
      setState(prev => ({
        ...prev,
        structured: structure
      }));
      options.onStructure?.(structure, partial);
    },
    onComplete: (content, metadata) => {
      setState(prev => ({
        ...prev,
        content,
        isStreaming: false,
        isLoading: false,
        progress: { ...prev.progress, percentComplete: 100 }
      }));
      retryCountRef.current = 0;
      options.onComplete?.(content, state.structured);
    },
    onError: (error) => {
      setState(prev => ({
        ...prev,
        error,
        isStreaming: false,
        isLoading: false
      }));

      if (options.autoRetry && retryCountRef.current < (options.maxRetries || 3)) {
        retryCountRef.current++;
        setTimeout(() => {
          retryLastRequest();
        }, options.retryDelay || 1000 * retryCountRef.current);
      } else {
        options.onError?.(error);
        toast({
          title: 'Streaming Error',
          description: error.message,
          variant: 'destructive'
        });
      }
    },
    onAbort: (reason, partial) => {
      setState(prev => ({
        ...prev,
        content: partial,
        isStreaming: false,
        isLoading: false
      }));
    }
  }), [options, state.structured, toast]);

  const lastRequestRef = useRef<ContentGenerationRequest | null>(null);

  const retryLastRequest = useCallback(async () => {
    if (lastRequestRef.current) {
      await stream(lastRequestRef.current);
    }
  }, []);

  // Main streaming function
  const stream = useCallback(async (request: ContentGenerationRequest) => {
    setState(prev => ({
      ...prev,
      content: '',
      structured: undefined,
      isLoading: true,
      isStreaming: false,
      error: null,
      progress: {
        tokens: 0,
        estimatedTokens: request.estimatedTokens || 1000,
        percentComplete: 0,
        tokensPerSecond: 0,
        estimatedTimeRemaining: 0
      }
    }));

    lastRequestRef.current = request;

    try {
      const response = await streamingService.streamContent<T>(
        request,
        createHandler()
      );

      streamIdRef.current = null;
      return response;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        isStreaming: false,
        error: {
          code: 'STREAM_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          recoverable: false
        }
      }));
      throw error;
    }
  }, [createHandler]);

  // Cancel streaming
  const cancel = useCallback(() => {
    if (streamIdRef.current) {
      streamingService.cancelStream(streamIdRef.current);
      streamIdRef.current = null;
    }
    setState(prev => ({
      ...prev,
      isStreaming: false,
      isLoading: false
    }));
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      content: '',
      structured: undefined,
      isStreaming: false,
      isLoading: false,
      progress: {
        tokens: 0,
        estimatedTokens: 1000,
        percentComplete: 0,
        tokensPerSecond: 0,
        estimatedTimeRemaining: 0
      },
      error: null,
      quality: undefined,
      metadata: undefined
    });
    streamIdRef.current = null;
    retryCountRef.current = 0;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamIdRef.current) {
        streamingService.cancelStream(streamIdRef.current);
      }
    };
  }, []);

  return {
    ...state,
    stream,
    cancel,
    reset,
    retry: retryLastRequest
  };
}

// Specialized hook for chapter streaming
export function useChapterStreaming(options?: UseTypedStreamingOptions<ChapterStreamData>) {
  const streaming = useTypedStreaming<ChapterStreamData>(options);

  const streamChapter = useCallback(async (
    prompt: string,
    context?: ContentGenerationRequest['context']
  ) => {
    trackFeatureUsage('chapter_streaming', { hasContext: !!context });

    return streaming.stream({
      prompt,
      contentType: ContentType.CHAPTER,
      context,
      estimatedTokens: 3000,
      streaming: true
    });
  }, [streaming]);

  return {
    ...streaming,
    streamChapter
  };
}

// Specialized hook for character streaming
export function useCharacterStreaming(options?: UseTypedStreamingOptions<CharacterStreamData>) {
  const streaming = useTypedStreaming<CharacterStreamData>(options);

  const streamCharacter = useCallback(async (
    characterName: string,
    context?: ContentGenerationRequest['context']
  ) => {
    trackFeatureUsage('character_streaming', { hasContext: !!context });

    return streaming.stream({
      prompt: `Develop a comprehensive character profile for ${characterName}`,
      contentType: ContentType.CHARACTER,
      context,
      estimatedTokens: 2000,
      streaming: true
    });
  }, [streaming]);

  return {
    ...streaming,
    streamCharacter
  };
}

// Specialized hook for analysis streaming
export function useAnalysisStreaming(options?: UseTypedStreamingOptions<AnalysisStreamData>) {
  const streaming = useTypedStreaming<AnalysisStreamData>(options);

  const streamAnalysis = useCallback(async (
    content: string,
    analysisType: string,
    context?: ContentGenerationRequest['context']
  ) => {
    trackFeatureUsage('analysis_streaming', { analysisType });

    return streaming.stream({
      prompt: `Analyze the following content for ${analysisType}: ${content}`,
      contentType: ContentType.GENERAL,
      context,
      estimatedTokens: 1500,
      streaming: true
    });
  }, [streaming]);

  return {
    ...streaming,
    streamAnalysis
  };
}

// Specialized hook for agent streaming
export function useAgentStreaming(options?: UseTypedStreamingOptions<AgentStreamData>) {
  const streaming = useTypedStreaming<AgentStreamData>(options);

  const streamAgent = useCallback(async (
    agentType: string,
    input: unknown,
    context?: ContentGenerationRequest['context']
  ) => {
    trackFeatureUsage('agent_streaming', { agentType });

    return streaming.stream({
      prompt: JSON.stringify({ agentType, input }),
      contentType: ContentType.GENERAL,
      context,
      estimatedTokens: 2500,
      streaming: true
    });
  }, [streaming]);

  return {
    ...streaming,
    streamAgent
  };
}

// Hook for SSE streaming
export function useSSEStreaming<T = unknown>(
  url: string,
  options: UseTypedStreamingOptions<T> = {}
) {
  const [state, setState] = useState<StreamingState<T>>({
    content: '',
    isStreaming: false,
    isLoading: false,
    progress: {
      tokens: 0,
      estimatedTokens: 1000,
      percentComplete: 0,
      tokensPerSecond: 0,
      estimatedTimeRemaining: 0
    },
    error: null
  });

  const eventSourceRef = useRef<EventSource | null>(null);
  const { toast } = useToast();

  const connect = useCallback(async (body?: unknown) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const streamUrl = response.headers.get('X-Stream-URL') || url;
      eventSourceRef.current = new EventSource(streamUrl);

      eventSourceRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          // Handle typed events...
          // Similar to the handler in useTypedStreaming
        } catch (error) {
          console.error('Failed to parse SSE data:', error);
        }
      };

      eventSourceRef.current.onerror = () => {
        setState(prev => ({
          ...prev,
          isStreaming: false,
          isLoading: false,
          error: {
            code: 'SSE_ERROR',
            message: 'Connection lost',
            recoverable: true
          }
        }));
      };

      setState(prev => ({
        ...prev,
        isLoading: false,
        isStreaming: true
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: {
          code: 'CONNECTION_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          recoverable: false
        }
      }));
      
      toast({
        title: 'Connection Error',
        description: 'Failed to establish streaming connection',
        variant: 'destructive'
      });
    }
  }, [url, toast]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setState(prev => ({
      ...prev,
      isStreaming: false,
      isLoading: false
    }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  return {
    ...state,
    connect,
    disconnect
  };
}