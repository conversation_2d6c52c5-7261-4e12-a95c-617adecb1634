-- Complete fix for achievement system
-- This handles the case where policies exist but tables don't

-- 1. Drop existing policies first (they might exist without tables)
DROP POLICY IF EXISTS "Achievements are public" ON achievements;
DROP POLICY IF EXISTS "Users can view their own achievements" ON user_achievements;
DROP POLICY IF EXISTS "System can insert user achievements" ON user_achievements;
DROP POLICY IF EXISTS "Users can view their own progress" ON achievement_progress;
DROP POLICY IF EXISTS "System can manage achievement progress" ON achievement_progress;

-- 2. Drop existing functions and triggers
DROP TRIGGER IF EXISTS check_achievements_on_chapter_update ON chapters;
DROP TRIGGER IF EXISTS check_achievements_on_project_create ON projects;
DROP TRIGGER IF EXISTS check_achievements_on_ai_usage ON ai_usage_logs;
DROP FUNCTION IF EXISTS check_achievements_on_update() CASCADE;
DROP FUNCTION IF EXISTS check_and_unlock_achievements(UUID) CASCADE;
DROP FUNCTION IF EXISTS track_achievement_progress(UUI<PERSON>, VA<PERSON>HA<PERSON>, VA<PERSON>HA<PERSON>, INTE<PERSON><PERSON>) CASCADE;

-- 3. Drop tables if they exist
DROP TABLE IF EXISTS user_achievements CASCADE;
DROP TABLE IF EXISTS achievement_progress CASCADE;
DROP TABLE IF EXISTS achievements CASCADE;

-- 4. Now create everything fresh

-- Achievement definitions table
CREATE TABLE achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  points INTEGER DEFAULT 10,
  tier VARCHAR(20) CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')) DEFAULT 'bronze',
  category VARCHAR(50) CHECK (category IN ('writing', 'streak', 'community', 'exploration', 'mastery')),
  criteria JSONB NOT NULL,
  icon VARCHAR(50),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User achievements tracking
CREATE TABLE user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ DEFAULT NOW(),
  progress INTEGER DEFAULT 0,
  metadata JSONB,
  UNIQUE(user_id, achievement_id)
);

-- Achievement progress tracking
CREATE TABLE achievement_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_code VARCHAR(100) NOT NULL,
  progress_key VARCHAR(100) NOT NULL,
  progress_value INTEGER DEFAULT 0,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, achievement_code, progress_key)
);

-- Create indexes
CREATE INDEX idx_user_achievements_user ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_unlocked ON user_achievements(unlocked_at DESC);
CREATE INDEX idx_achievement_progress_user ON achievement_progress(user_id);

-- Enable RLS
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievement_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Achievements are public" ON achievements
  FOR SELECT USING (true);

CREATE POLICY "Users can view their own achievements" ON user_achievements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert user achievements" ON user_achievements
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view their own progress" ON achievement_progress
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage achievement progress" ON achievement_progress
  FOR ALL USING (true);

-- Insert initial achievements
INSERT INTO achievements (code, title, description, points, tier, category, criteria, icon) VALUES
  -- Writing achievements
  ('first_words', 'First Words', 'Write your first 100 words', 10, 'bronze', 'writing', '{"type": "word_count", "value": 100}', 'edit'),
  ('novice_writer', 'Novice Writer', 'Write 1,000 words', 25, 'bronze', 'writing', '{"type": "word_count", "value": 1000}', 'edit-2'),
  ('apprentice_writer', 'Apprentice Writer', 'Write 10,000 words', 50, 'silver', 'writing', '{"type": "word_count", "value": 10000}', 'edit-3'),
  ('journeyman_writer', 'Journeyman Writer', 'Write 50,000 words', 100, 'gold', 'writing', '{"type": "word_count", "value": 50000}', 'book'),
  ('master_writer', 'Master Writer', 'Write 100,000 words', 200, 'platinum', 'writing', '{"type": "word_count", "value": 100000}', 'book-open'),
  
  -- Chapter achievements
  ('chapter_one', 'Chapter One', 'Complete your first chapter', 20, 'bronze', 'writing', '{"type": "chapters_completed", "value": 1}', 'bookmark'),
  ('five_chapters', 'Five Chapters', 'Complete 5 chapters', 50, 'silver', 'writing', '{"type": "chapters_completed", "value": 5}', 'bookmarks'),
  ('ten_chapters', 'Ten Chapters', 'Complete 10 chapters', 100, 'gold', 'writing', '{"type": "chapters_completed", "value": 10}', 'library'),
  
  -- Project achievements
  ('first_project', 'First Project', 'Create your first project', 15, 'bronze', 'exploration', '{"type": "projects_created", "value": 1}', 'folder-plus'),
  ('multi_project', 'Multi-Project Author', 'Create 5 projects', 40, 'silver', 'exploration', '{"type": "projects_created", "value": 5}', 'folders'),
  
  -- Series achievements
  ('series_starter', 'Series Starter', 'Create your first series', 30, 'silver', 'exploration', '{"type": "series_created", "value": 1}', 'link'),
  ('series_master', 'Series Master', 'Complete a 3-book series', 150, 'gold', 'mastery', '{"type": "series_books_completed", "value": 3}', 'link-2');

-- Create a simplified check function that handles missing tables gracefully
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE (newly_unlocked UUID[]) AS $$
DECLARE
  v_newly_unlocked UUID[];
  v_achievement RECORD;
  v_current_progress INTEGER;
  v_already_unlocked BOOLEAN;
BEGIN
  v_newly_unlocked := ARRAY[]::UUID[];
  
  -- Check each achievement
  FOR v_achievement IN SELECT * FROM achievements LOOP
    -- Check if already unlocked
    SELECT EXISTS(
      SELECT 1 FROM user_achievements 
      WHERE user_id = p_user_id AND achievement_id = v_achievement.id
    ) INTO v_already_unlocked;
    
    IF NOT v_already_unlocked THEN
      v_current_progress := 0;
      
      -- Check criteria based on type
      BEGIN
        CASE v_achievement.criteria->>'type'
          WHEN 'word_count' THEN
            SELECT COALESCE(SUM(word_count), 0)
            FROM chapters c
            JOIN projects p ON p.id = c.project_id
            WHERE p.user_id = p_user_id
            INTO v_current_progress;
            
          WHEN 'chapters_completed' THEN
            SELECT COUNT(*)
            FROM chapters c
            JOIN projects p ON p.id = c.project_id
            WHERE p.user_id = p_user_id AND c.status = 'published'
            INTO v_current_progress;
            
          WHEN 'projects_created' THEN
            SELECT COUNT(*)
            FROM projects
            WHERE user_id = p_user_id
            INTO v_current_progress;
            
          WHEN 'series_created' THEN
            SELECT COUNT(*)
            FROM series
            WHERE user_id = p_user_id
            INTO v_current_progress;
        END CASE;
      EXCEPTION
        WHEN OTHERS THEN
          v_current_progress := 0;
      END;
      
      -- Check if criteria met
      IF v_current_progress >= COALESCE((v_achievement.criteria->>'value')::INTEGER, 0) THEN
        -- Unlock achievement
        INSERT INTO user_achievements (user_id, achievement_id)
        VALUES (p_user_id, v_achievement.id)
        ON CONFLICT (user_id, achievement_id) DO NOTHING;
        
        -- Only add to newly_unlocked if insert succeeded
        IF FOUND THEN
          v_newly_unlocked := array_append(v_newly_unlocked, v_achievement.id);
        END IF;
      END IF;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT v_newly_unlocked;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION check_and_unlock_achievements(UUID) TO authenticated;

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE 'Achievement system installed successfully!';
END $$;