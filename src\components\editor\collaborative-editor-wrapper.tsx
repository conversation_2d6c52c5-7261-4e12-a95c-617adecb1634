'use client';

import { useEffect, useRef } from 'react';
import { UnifiedMonacoEditor, UnifiedMonacoEditorProps } from './unified-monaco-editor';
import { useCollaboration } from '@/hooks/use-collaboration';
import { CollaboratorCursors, CollaboratorAvatars } from '../collaboration/collaborator-cursors';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, UserPlus, Settings, Wifi, WifiOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import type { editor } from 'monaco-editor';

interface CollaborativeEditorWrapperProps extends UnifiedMonacoEditorProps {
  projectId: string;
  chapterId?: string;
  documentId: string;
  enableCollaboration?: boolean;
  showCollaborators?: boolean;
  onCollaborationStateChange?: (isConnected: boolean) => void;
}

export function CollaborativeEditorWrapper({
  projectId,
  chapterId,
  documentId,
  enableCollaboration = true,
  showCollaborators = true,
  onCollaborationStateChange,
  value,
  onChange,
  ...editorProps
}: CollaborativeEditorWrapperProps) {
  const { toast } = useToast();
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const editorContainerRef = useRef<HTMLDivElement>(null);
  const lastContentRef = useRef<string>(value || '');

  const {
    isConnected,
    isConnecting,
    isSyncing,
    collaborators,
    content,
    insertText,
    deleteText,
    replaceText,
    updateCursor,
    updateSelection,
    connect,
    disconnect,
    error,
  } = useCollaboration({
    projectId,
    chapterId,
    documentId,
    initialContent: value || '',
    onContentChange: (newContent) => {
      // Update the editor with remote changes
      if (newContent !== lastContentRef.current) {
        lastContentRef.current = newContent;
        onChange?.(newContent);
      }
    },
    enabled: enableCollaboration,
  });

  // Notify parent of connection state changes
  useEffect(() => {
    onCollaborationStateChange?.(isConnected);
  }, [isConnected, onCollaborationStateChange]);

  // Show error toast if connection fails
  useEffect(() => {
    if (error) {
      toast({
        title: 'Collaboration Error',
        description: error.message,
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  // Handle local edits and propagate to collaboration system
  const handleEditorChange = (newValue: string | undefined) => {
    const newContent = newValue || '';
    
    // Detect the type of change and send appropriate operation
    const oldContent = lastContentRef.current;
    
    if (newContent.length > oldContent.length) {
      // Text was inserted
      const insertPos = findInsertPosition(oldContent, newContent);
      if (insertPos !== -1) {
        const insertedText = newContent.substring(insertPos, insertPos + (newContent.length - oldContent.length));
        insertText(insertPos, insertedText);
      }
    } else if (newContent.length < oldContent.length) {
      // Text was deleted
      const deletePos = findDeletePosition(oldContent, newContent);
      if (deletePos !== -1) {
        const deleteLength = oldContent.length - newContent.length;
        deleteText(deletePos, deleteLength);
      }
    } else if (newContent !== oldContent) {
      // Text was replaced (same length but different content)
      const { pos, length } = findReplaceRange(oldContent, newContent);
      if (pos !== -1) {
        const replacementText = newContent.substring(pos, pos + length);
        replaceText(pos, length, replacementText);
      }
    }
    
    lastContentRef.current = newContent;
    onChange?.(newContent);
  };

  // Enhanced editor mount handler that integrates with Monaco
  const handleEditorMount = (editor: editor.IStandaloneCodeEditor, monaco: any) => {
    editorRef.current = editor;

    // Track cursor position changes
    editor.onDidChangeCursorPosition((e) => {
      if (isConnected) {
        updateCursor({
          line: e.position.lineNumber,
          column: e.position.column,
        });
      }
    });

    // Track selection changes
    editor.onDidChangeCursorSelection((e) => {
      if (isConnected && !e.selection.isEmpty()) {
        const startPos = e.selection.getStartPosition();
        const endPos = e.selection.getEndPosition();
        
        updateSelection({
          start: { line: startPos.lineNumber, column: startPos.column },
          end: { line: endPos.lineNumber, column: endPos.column },
        });
      }
    });

    // Call the original onMount if provided
    if (editorProps.onMount) {
      editorProps.onMount(editor, monaco);
    }
  };

  return (
    <div className="relative h-full">
      {/* Collaboration status bar */}
      {enableCollaboration && (
        <div className="absolute top-2 left-2 z-20 flex items-center gap-2">
          <Card className="px-3 py-1.5 bg-background/95 backdrop-blur-sm border shadow-sm">
            <div className="flex items-center gap-2">
              {/* Connection status */}
              <div className="flex items-center gap-1.5">
                {isConnecting ? (
                  <>
                    <div className="w-2 h-2 bg-warning rounded-full animate-pulse" />
                    <span className="text-xs text-muted-foreground">Connecting...</span>
                  </>
                ) : isConnected ? (
                  <>
                    <Wifi className="w-3 h-3 text-success" />
                    <span className="text-xs text-muted-foreground">Connected</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="w-3 h-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Offline</span>
                  </>
                )}
              </div>

              {/* Sync indicator */}
              {isSyncing && (
                <>
                  <div className="h-3 w-px bg-border" />
                  <Badge variant="secondary" className="text-xs px-1.5 py-0">
                    Syncing...
                  </Badge>
                </>
              )}

              {/* Collaborator count */}
              {isConnected && collaborators.length > 0 && (
                <>
                  <div className="h-3 w-px bg-border" />
                  <div className="flex items-center gap-1">
                    <Users className="w-3 h-3 text-muted-foreground" />
                    <span className="text-xs font-medium">{collaborators.length}</span>
                  </div>
                </>
              )}

              {/* Connection toggle */}
              <div className="h-3 w-px bg-border" />
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={() => isConnected ? disconnect() : connect()}
              >
                {isConnected ? (
                  <span className="text-xs">Disconnect</span>
                ) : (
                  <span className="text-xs">Connect</span>
                )}
              </Button>
            </div>
          </Card>

          {/* Collaborator avatars */}
          {showCollaborators && collaborators.length > 0 && (
            <CollaboratorAvatars
              collaborators={collaborators}
              size="sm"
              maxDisplay={5}
            />
          )}
        </div>
      )}

      {/* Main editor with collaboration-aware content */}
      <div ref={editorContainerRef} className="h-full">
        <UnifiedMonacoEditor
          {...editorProps}
          value={isConnected ? content : value}
          onChange={handleEditorChange}
          onMount={handleEditorMount}
          enableRealtime={false} // Disable built-in realtime to use our implementation
        />
        
        {/* Collaborator cursors overlay */}
        {enableCollaboration && isConnected && collaborators.length > 0 && (
          <CollaboratorCursors
            collaborators={collaborators}
            editorRef={editorContainerRef}
            className="pointer-events-none"
          />
        )}
      </div>

      {/* Invite collaborators button */}
      {enableCollaboration && isConnected && (
        <Button
          variant="outline"
          size="sm"
          className="absolute bottom-4 right-4 shadow-md"
          onClick={() => {
            // Copy collaboration link to clipboard
            const collaborationUrl = `${window.location.origin}/projects/${projectId}/collaborate/${documentId}`;
            navigator.clipboard.writeText(collaborationUrl);
            toast({
              title: 'Collaboration link copied',
              description: 'Share this link with others to collaborate in real-time',
            });
          }}
        >
          <UserPlus className="w-4 h-4 mr-2" />
          Invite Collaborators
        </Button>
      )}
    </div>
  );
}

// Helper functions to detect text changes
function findInsertPosition(oldText: string, newText: string): number {
  let i = 0;
  while (i < oldText.length && oldText[i] === newText[i]) {
    i++;
  }
  return i;
}

function findDeletePosition(oldText: string, newText: string): number {
  let i = 0;
  while (i < newText.length && oldText[i] === newText[i]) {
    i++;
  }
  return i;
}

function findReplaceRange(oldText: string, newText: string): { pos: number; length: number } {
  let start = 0;
  while (start < oldText.length && oldText[start] === newText[start]) {
    start++;
  }
  
  let oldEnd = oldText.length - 1;
  let newEnd = newText.length - 1;
  while (oldEnd > start && newEnd > start && oldText[oldEnd] === newText[newEnd]) {
    oldEnd--;
    newEnd--;
  }
  
  return {
    pos: start,
    length: oldEnd - start + 1,
  };
}