import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { withRateLimit } from '@/lib/rate-limiter-unified'
import { handleAPIError, ValidationError, type APIErrorResponse } from '@/lib/api/error-handler'
import { createTypedServerClient } from '@/lib/supabase'
import { AI_MODELS, AI_TEMPERATURE } from '@/lib/config/ai-settings'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'
import { type StandardResponse } from '@/lib/api/types'

// Edit text schema
const editTextSchema = z.object({
  action: z.enum(['improve', 'expand', 'rewrite', 'suggest', 'continue', 'summarize', 'rephrase', 'custom']),
  selectedText: z.string().optional(),
  beforeCursor: z.string().optional(),
  afterCursor: z.string().optional(),
  fullContent: z.string().optional(),
  prompt: z.string().optional(),
  customPrompt: z.string().optional(),
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  const rateLimitResponse = await withRateLimit(request, {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 30, // 30 editing requests per hour
    message: 'Too many editing requests. Please wait before trying again.'
  })
  
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  
  try {
    const user = request.user!
    const body = await request.json()
    const validatedData = editTextSchema.parse(body)
    const { action, selectedText, beforeCursor, afterCursor, fullContent, prompt, customPrompt } = validatedData
    
    const supabase = await createTypedServerClient()

    // For continuation actions, we need context, not selected text
    if (action === 'continue' && !beforeCursor && !fullContent) {
      return handleAPIError(new ValidationError('Context required for continuation'))
    }
    
    // For other actions, we need selected text
    if (action !== 'continue' && action !== 'custom' && !selectedText) {
      return handleAPIError(new ValidationError('No text selected'))
    }

    let systemPrompt = ''
    let userPrompt = ''

    switch (action) {
      case 'improve':
        systemPrompt = 'You are an expert writing editor specializing in fiction. Enhance clarity, flow, and impact while preserving the author\'s voice and style.'
        userPrompt = `Improve this text while maintaining its tone and voice:\n\n"${selectedText}"\n\nProvide only the improved version.`
        break
        
      case 'expand':
        systemPrompt = 'You are an expert writing editor. Add vivid details, sensory elements, and deeper development while maintaining the original style.'
        userPrompt = `Expand this text with richer detail and depth:\n\n"${selectedText}"\n\nProvide only the expanded version.`
        break
        
      case 'rewrite':
        systemPrompt = 'You are an expert writing editor. Completely rewrite the given text while maintaining the core meaning and improving the style and flow.'
        userPrompt = `Please rewrite this text:\n\n"${selectedText}"\n\nProvide only the rewritten version without explanations.`
        break
        
      case 'suggest':
        systemPrompt = 'You are an expert writing editor. Provide 2-3 alternative ways to write the given text, each with a different style or approach.'
        userPrompt = `Please provide alternative versions of this text:\n\n"${selectedText}"\n\nFormat as: Option 1: [text]\nOption 2: [text]\nOption 3: [text]`
        break
        
      case 'continue':
        systemPrompt = 'You are an expert creative writer. Continue the narrative seamlessly, matching style, voice, pacing, and tone. Create 1-3 engaging paragraphs.'
        const contextBefore = beforeCursor || fullContent?.substring(Math.max(0, (fullContent?.length || 0) - TIME_MS.SECOND))
        const contextAfter = afterCursor || ''
        userPrompt = `Continue from here:\n\nBefore: "${contextBefore}"\n${contextAfter ? `\nAfter: "${contextAfter}"` : ''}\n\nWrite the continuation naturally.`
        break
        
      case 'summarize':
        systemPrompt = 'You are an expert writing editor. Create a concise summary of the given text, capturing the key points and main ideas.'
        userPrompt = `Please summarize this text:\n\n"${selectedText}"\n\nProvide only the summary without explanations.`
        break
        
      case 'rephrase':
        systemPrompt = 'You are an expert writing editor. Rephrase the given text to say the same thing in a different way, maintaining the meaning but changing the words and structure.'
        userPrompt = `Please rephrase this text:\n\n"${selectedText}"\n\nProvide only the rephrased version without explanations.`
        break
        
      case 'custom':
        if (!customPrompt && !prompt) {
          return handleAPIError(new ValidationError('Custom prompt required'))
        }
        systemPrompt = 'You are an expert writing editor. Follow the user\'s instructions to edit the given text.'
        const instruction = customPrompt || prompt
        const textToEdit = selectedText || beforeCursor || ''
        userPrompt = `${textToEdit ? `Original text: "${textToEdit}"\n\n` : ''}Instructions: ${instruction}\n\nProvide only the result without explanations.`
        break
        
      default:
        return handleAPIError(new ValidationError('Invalid action'))
    }

    // Check if client accepts streaming
    const acceptsStreaming = request.headers.get('accept') === 'text/event-stream'
    
    let editedText: string
    let tokensUsed: number | undefined

    try {
      if (acceptsStreaming) {
        // Use streaming for real-time feedback
        let fullText = ''
        const stream = await vercelAIClient.streamTextWithFallback(
          userPrompt,
          {
            systemPrompt,
            model: AI_MODELS.FAST,
            temperature: AI_TEMPERATURE.TASKS.EDITING || 0.3,
            maxTokens: 2000
          },
          {
            onToken: (token) => {
              fullText += token
            },
            onComplete: (content) => {
              fullText = content
            }
          }
        )
        editedText = fullText
      } else {
        // Non-streaming response with fallback
        editedText = await vercelAIClient.generateTextWithFallback(
          userPrompt,
          {
            systemPrompt,
            model: AI_MODELS.FAST,
            temperature: AI_TEMPERATURE.TASKS.EDITING || 0.3,
            maxTokens: 2000
          }
        )
      }
    } catch (error) {
      logger.error('Text editing failed:', error)
      return NextResponse.json<StandardResponse>(
        { success: false, error: 'Failed to generate edited text' },
        { status: 500 }
      )
    }

    if (!editedText) {
      return NextResponse.json<StandardResponse>(
        { success: false, error: 'No response from AI' },
        { status: 500 }
      )
    }

    // Log the editing session
    await supabase.from('editing_sessions').insert({
      user_id: user.id,
      selected_text: selectedText || beforeCursor || fullContent?.substring(0, TIME_MS.SECOND) || '',
      ai_prompt: customPrompt || prompt || action,
      ai_response: editedText,
      action_type: action
    })

    return NextResponse.json<StandardResponse>({
      success: true,
      data: {
        editedText,
        suggestion: editedText, // For backward compatibility
        action,
        tokensUsed
      }
    })
  } catch (error) {
    logger.error('Error in edit API:', error)
    
    if (error instanceof z.ZodError) {
      return handleAPIError(new ValidationError('Invalid request data'))
    }
    
    return NextResponse.json<StandardResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
})