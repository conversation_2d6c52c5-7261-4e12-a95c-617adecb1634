'use client'

import { useState, useEffect, useCallback } from 'react'
import { logger } from '@/lib/services/logger'
import type { TimelineEvent } from '@/components/timeline/timeline-calendar-view'

interface UseTimelineCalendarOptions {
  projectId: string
  autoLoad?: boolean
}

interface UseTimelineCalendarReturn {
  events: TimelineEvent[]
  loading: boolean
  error: string | null
  loadEvents: () => Promise<void>
  createEvent: (event: Omit<TimelineEvent, 'id'>) => Promise<TimelineEvent | null>
  updateEvent: (event: TimelineEvent) => Promise<TimelineEvent | null>
  deleteEvent: (eventId: string) => Promise<boolean>
  refreshEvents: () => Promise<void>
}

export function useTimelineCalendar(options: UseTimelineCalendarOptions): UseTimelineCalendarReturn {
  const [events, setEvents] = useState<TimelineEvent[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadEvents = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/timeline/events?projectId=${options.projectId}`)
      if (!response.ok) {
        throw new Error('Failed to load timeline events')
      }
      
      const data = await response.json()
      
      // Transform timeline events to calendar format
      const calendarEvents: TimelineEvent[] = (data.events || []).map((event: any) => ({
        id: event.id,
        type: mapEventTypeToCalendarType(event.type),
        title: event.title,
        description: event.description,
        date: event.timestamp?.parsedDate ? new Date(event.timestamp.parsedDate).toISOString() : new Date().toISOString(),
        chapter: event.chapter,
        scene: event.scene,
        characters: event.characters || [],
        location: event.location,
        importance: mapImportanceLevel(event.importance),
        verified: event.verified || false
      }))
      
      setEvents(calendarEvents)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load timeline events'
      setError(errorMessage)
      logger.error('Error loading timeline events for calendar:', err)
    } finally {
      setLoading(false)
    }
  }, [options.projectId])

  const createEvent = useCallback(async (eventData: Omit<TimelineEvent, 'id'>): Promise<TimelineEvent | null> => {
    try {
      setError(null)
      
      // Transform calendar event to timeline format
      const timelineEvent = {
        projectId: options.projectId,
        type: eventData.type,
        title: eventData.title,
        description: eventData.description,
        timestamp: {
          type: 'absolute' as const,
          value: eventData.date,
          parsedDate: new Date(eventData.date),
          uncertainty: 0
        },
        chapter: eventData.chapter || 1,
        scene: eventData.scene,
        characters: eventData.characters,
        location: eventData.location,
        importance: mapCalendarImportanceToNumber(eventData.importance),
        verified: eventData.verified
      }
      
      const response = await fetch('/api/timeline/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(timelineEvent)
      })
      
      if (!response.ok) {
        throw new Error('Failed to create timeline event')
      }
      
      const result = await response.json()
      const newCalendarEvent: TimelineEvent = {
        ...eventData,
        id: result.event.id
      }
      
      // Update local state
      setEvents(prev => [...prev, newCalendarEvent])
      
      return newCalendarEvent
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create timeline event'
      setError(errorMessage)
      logger.error('Error creating timeline event:', err)
      return null
    }
  }, [options.projectId])

  const updateEvent = useCallback(async (event: TimelineEvent): Promise<TimelineEvent | null> => {
    try {
      setError(null)
      
      const response = await fetch(`/api/timeline/events/${event.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: event.title,
          description: event.description,
          timestamp: {
            type: 'absolute' as const,
            value: event.date,
            parsedDate: new Date(event.date),
            uncertainty: 0
          },
          chapter: event.chapter,
          scene: event.scene,
          characters: event.characters,
          location: event.location,
          importance: mapCalendarImportanceToNumber(event.importance),
          verified: event.verified
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to update timeline event')
      }
      
      // Update local state
      setEvents(prev => prev.map(e => e.id === event.id ? event : e))
      
      return event
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update timeline event'
      setError(errorMessage)
      logger.error('Error updating timeline event:', err)
      return null
    }
  }, [])

  const deleteEvent = useCallback(async (eventId: string): Promise<boolean> => {
    try {
      setError(null)
      
      const response = await fetch(`/api/timeline/events/${eventId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete timeline event')
      }
      
      // Update local state
      setEvents(prev => prev.filter(e => e.id !== eventId))
      
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete timeline event'
      setError(errorMessage)
      logger.error('Error deleting timeline event:', err)
      return false
    }
  }, [])

  const refreshEvents = useCallback(async () => {
    await loadEvents()
  }, [loadEvents])

  // Auto-load events on mount if enabled
  useEffect(() => {
    if (options.autoLoad !== false) {
      loadEvents()
    }
  }, [loadEvents, options.autoLoad])

  return {
    events,
    loading,
    error,
    loadEvents,
    createEvent,
    updateEvent,
    deleteEvent,
    refreshEvents
  }
}

// Helper functions to map between timeline and calendar formats
function mapEventTypeToCalendarType(timelineType: string): TimelineEvent['type'] {
  switch (timelineType) {
    case 'plot': return 'plot'
    case 'character': return 'character'
    case 'world': return 'world'
    case 'reference': return 'reference'
    default: return 'plot'
  }
}

function mapImportanceLevel(importance: number): TimelineEvent['importance'] {
  if (importance >= 0.9) return 'critical'
  if (importance >= 0.7) return 'high'
  if (importance >= 0.4) return 'medium'
  return 'low'
}

function mapCalendarImportanceToNumber(importance: TimelineEvent['importance']): number {
  switch (importance) {
    case 'critical': return 1.0
    case 'high': return 0.8
    case 'medium': return 0.6
    case 'low': return 0.3
    default: return 0.5
  }
}