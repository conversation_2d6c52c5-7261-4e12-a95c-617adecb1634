import { NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

// Validation schemas
const viewportSchema = z.object({
  center: z.object({
    lat: z.number(),
    lng: z.number()
  }),
  zoom: z.number().min(1).max(20),
  bounds: z.object({
    north: z.number(),
    south: z.number(),
    east: z.number(),
    west: z.number()
  }).optional(),
  rotation: z.number().optional(),
  pitch: z.number().optional()
});

const mapPreferencesSchema = z.object({
  viewport: viewportSchema,
  mapStyle: z.enum(['default', 'satellite', 'terrain', 'dark', 'light']).optional(),
  showLabels: z.boolean().optional(),
  showGrid: z.boolean().optional(),
  showConnections: z.boolean().optional(),
  clusterMarkers: z.boolean().optional(),
  visibleLayers: z.array(z.string()).optional(),
  locationTypeFilters: z.array(z.string()).optional(),
  sidebarCollapsed: z.boolean().optional(),
  minimapVisible: z.boolean().optional(),
  toolbarPosition: z.enum(['top', 'bottom', 'left', 'right']).optional(),
  lastViewedLocationId: z.string().uuid().optional().nullable()
});

// GET endpoint - Load map preferences
export const GET = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: projectId } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (!projectAccess) {
      // Check for collaborator access
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single();

      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403);
      }
    }

    // Get or create map preferences
    const { data: preferences, error } = await supabase
      .rpc('get_or_create_user_map_preferences', {
        p_user_id: user.id,
        p_project_id: projectId
      });

    if (error) {
      logger.error('Error fetching map preferences:', error);
      throw error;
    }

    // Transform database format to API format
    const response = {
      id: preferences.id,
      viewport: preferences.viewport || {
        center: { lat: 0, lng: 0 },
        zoom: 10
      },
      mapStyle: preferences.map_style,
      showLabels: preferences.show_labels,
      showGrid: preferences.show_grid,
      showConnections: preferences.show_connections,
      clusterMarkers: preferences.cluster_markers,
      visibleLayers: preferences.visible_layers || [],
      locationTypeFilters: preferences.location_type_filters || [],
      sidebarCollapsed: preferences.sidebar_collapsed,
      minimapVisible: preferences.minimap_visible,
      toolbarPosition: preferences.toolbar_position,
      lastViewedLocationId: preferences.last_viewed_location_id,
      lastInteraction: preferences.last_interaction
    };

    return UnifiedResponse.success(response);
  } catch (error) {
    logger.error('Error in map preferences GET:', error);
    return UnifiedResponse.error({
      message: 'Failed to fetch map preferences',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

// PUT endpoint - Save map preferences
export const PUT = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: projectId } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Parse and validate request body
    const body = await request.json();
    const validationResult = mapPreferencesSchema.safeParse(body);

    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid map preferences data',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400);
    }

    const preferences = validationResult.data;

    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (!projectAccess) {
      // Check for collaborator access
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single();

      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403);
      }
    }

    // Transform API format to database format
    const updateData = {
      viewport: preferences.viewport,
      map_style: preferences.mapStyle,
      show_labels: preferences.showLabels,
      show_grid: preferences.showGrid,
      show_connections: preferences.showConnections,
      cluster_markers: preferences.clusterMarkers,
      visible_layers: preferences.visibleLayers,
      location_type_filters: preferences.locationTypeFilters,
      sidebar_collapsed: preferences.sidebarCollapsed,
      minimap_visible: preferences.minimapVisible,
      toolbar_position: preferences.toolbarPosition,
      last_viewed_location_id: preferences.lastViewedLocationId
    };

    // Upsert preferences
    const { data: updated, error } = await supabase
      .from('user_map_preferences')
      .upsert({
        user_id: user.id,
        project_id: projectId,
        ...updateData
      }, {
        onConflict: 'user_id,project_id'
      })
      .select()
      .single();

    if (error) {
      logger.error('Error updating map preferences:', error);
      throw error;
    }

    // Transform back to API format
    const response = {
      id: updated.id,
      viewport: updated.viewport,
      mapStyle: updated.map_style,
      showLabels: updated.show_labels,
      showGrid: updated.show_grid,
      showConnections: updated.show_connections,
      clusterMarkers: updated.cluster_markers,
      visibleLayers: updated.visible_layers || [],
      locationTypeFilters: updated.location_type_filters || [],
      sidebarCollapsed: updated.sidebar_collapsed,
      minimapVisible: updated.minimap_visible,
      toolbarPosition: updated.toolbar_position,
      lastViewedLocationId: updated.last_viewed_location_id,
      lastInteraction: updated.last_interaction
    };

    return UnifiedResponse.success(response);
  } catch (error) {
    logger.error('Error in map preferences PUT:', error);
    return UnifiedResponse.error({
      message: 'Failed to update map preferences',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

// DELETE endpoint - Reset map preferences to defaults
export const DELETE = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: projectId } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Delete user's preferences for this project
    const { error } = await supabase
      .from('user_map_preferences')
      .delete()
      .eq('user_id', user.id)
      .eq('project_id', projectId);

    if (error) {
      logger.error('Error deleting map preferences:', error);
      throw error;
    }

    return UnifiedResponse.success({
      message: 'Map preferences reset to defaults'
    });
  } catch (error) {
    logger.error('Error in map preferences DELETE:', error);
    return UnifiedResponse.error({
      message: 'Failed to reset map preferences',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});