import { createServerClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import { WritingGoalsDashboard } from '@/components/goals/writing-goals-dashboard'

export default async function GoalsPage() {
  const supabase = await createServerClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return (
    <div className="container py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Writing Goals</h1>
        <p className="text-muted-foreground">
          Set targets, track progress, and build consistent writing habits
        </p>
      </div>
      
      <WritingGoalsDashboard />
    </div>
  )
}