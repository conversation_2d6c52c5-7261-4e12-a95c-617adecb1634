'use client'

import { useEffect } from 'react'
import { logger } from '@/lib/services/logger';

import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'

export default function AuthError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    logger.error('Auth error:', error);
  }, [error])

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-amber-50 to-stone-100">
      <div className="mx-auto max-w-md text-center p-8 bg-white rounded-lg shadow-lg">
        <AlertCircle className="mx-auto h-10 w-10 text-warning mb-4" />
        <h2 className="mb-2 text-xl font-semibold">Authentication Error</h2>
        <p className="mb-4 text-sm text-gray-600">
          We encountered an error with authentication. Please try again.
        </p>
        <div className="flex gap-3 justify-center">
          <Button onClick={reset} variant="default" className="bg-warning hover:bg-amber-700">
            Try Again
          </Button>
          <Button onClick={() => window.location.href = '/'} variant="outline">
            Go Home
          </Button>
        </div>
      </div>
    </div>
  )
}