import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { AdvancedAgentOrchestrator, TaskDefinition, TaskResult } from '@/lib/agents/advanced-orchestrator';
import type { ProjectSettings } from '@/lib/types/project-settings';

describe('Agent Orchestration Integration Tests', () => {
  let orchestrator: AdvancedAgentOrchestrator;
  
  const mockProjectSettings: ProjectSettings = {
    primaryGenre: 'fantasy',
    secondaryGenres: ['adventure'],
    targetAudience: 'adult',
    writingStyle: 'descriptive',
    narrativeVoice: 'third-person',
    tense: 'past',
    pacing: 'medium',
    violenceLevel: 'moderate',
    romanceLevel: 'low',
    profanityLevel: 'mild',
    themeDepth: 'deep',
    worldBuildingDepth: 'extensive',
    characterComplexity: 'complex',
    plotComplexity: 'complex',
    tone: 'serious',
    dialogueStyle: 'natural',
    descriptionLevel: 'detailed',
    useDeepPOV: true,
    showDontTell: true,
    varyProse: true,
    useSymbolism: true,
    useCliffhangers: true,
    useForeshadowing: true,
    useFlashbacks: false,
    useUnreliableNarrator: false,
    protagonistTypes: ['hero'],
    antagonistTypes: ['villain'],
    supportingRoles: ['mentor', 'sidekick'],
    majorThemes: ['courage', 'friendship'],
    minorThemes: ['sacrifice'],
    culturalElements: [],
    magicSystemType: 'soft',
    technologyLevel: 'medieval',
    politicalSystem: 'monarchy',
    economicSystem: 'feudal',
    geographyType: 'earth-like',
    pacingPreference: 'medium'
  };

  beforeEach(() => {
    orchestrator = new AdvancedAgentOrchestrator(3); // 3 concurrent tasks
  });

  describe('Complex Dependency Resolution', () => {
    it('should handle diamond dependency pattern', async () => {
      const executionOrder: string[] = [];
      
      // Create diamond pattern: A -> B,C -> D
      const tasks: TaskDefinition[] = [
        {
          id: 'A',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'B',
          type: 'story_analysis',
          dependencies: ['A'],
          priority: 'medium',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'C',
          type: 'story_analysis',
          dependencies: ['A'],
          priority: 'medium',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'D',
          type: 'story_analysis',
          dependencies: ['B', 'C'],
          priority: 'low',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        }
      ];

      // Mock executeTask to track execution order
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task: TaskDefinition) => {
        executionOrder.push(task.id);
        return {
          taskId: task.id,
          success: true,
          result: { id: task.id },
          duration: 10,
          agentUsed: 'test'
        };
      });

      await orchestrator['executeTaskPipeline'](tasks);

      // A should execute first
      expect(executionOrder[0]).toBe('A');
      
      // B and C should execute after A (order may vary due to parallelism)
      expect(executionOrder.slice(1, 3).sort()).toEqual(['B', 'C']);
      
      // D should execute last
      expect(executionOrder[3]).toBe('D');
    });

    it('should handle circular dependency detection', async () => {
      // Create circular dependency: A -> B -> C -> A
      const tasks: TaskDefinition[] = [
        {
          id: 'A',
          type: 'story_analysis',
          dependencies: ['C'], // Circular!
          priority: 'high',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'B',
          type: 'story_analysis',
          dependencies: ['A'],
          priority: 'medium',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'C',
          type: 'story_analysis',
          dependencies: ['B'],
          priority: 'low',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        }
      ];

      const results = await orchestrator['executeTaskPipeline'](tasks);
      
      // Should detect deadlock and not hang indefinitely
      expect(results.length).toBe(0); // No tasks could complete due to circular dependency
    });

    it('should handle failed dependency gracefully', async () => {
      const tasks: TaskDefinition[] = [
        {
          id: 'A',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'B',
          type: 'story_analysis',
          dependencies: ['A'],
          priority: 'medium',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        }
      ];

      // Mock A to fail
      let taskCount = 0;
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task: TaskDefinition) => {
        taskCount++;
        if (task.id === 'A') {
          return {
            taskId: task.id,
            success: false,
            error: 'Task A failed',
            duration: 10,
            agentUsed: 'test'
          };
        }
        return {
          taskId: task.id,
          success: true,
          result: { id: task.id },
          duration: 10,
          agentUsed: 'test'
        };
      });

      await orchestrator['executeTaskPipeline'](tasks);

      // B should not execute since A failed
      expect(taskCount).toBe(1); // Only A executed
    });
  });

  describe('Parallel Execution Patterns', () => {
    it('should execute independent tasks in parallel', async () => {
      const startTimes: Record<string, number> = {};
      const endTimes: Record<string, number> = {};
      
      // Create independent tasks
      const tasks: TaskDefinition[] = [
        {
          id: 'Task1',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 100,
          agent: 'test',
          payload: {}
        },
        {
          id: 'Task2',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 100,
          agent: 'test',
          payload: {}
        },
        {
          id: 'Task3',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 100,
          agent: 'test',
          payload: {}
        }
      ];

      // Mock executeTask with timing
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task: TaskDefinition) => {
        startTimes[task.id] = Date.now();
        await new Promise(resolve => setTimeout(resolve, 100));
        endTimes[task.id] = Date.now();
        
        return {
          taskId: task.id,
          success: true,
          result: { id: task.id },
          duration: 100,
          agentUsed: 'test'
        };
      });

      const startTime = Date.now();
      await orchestrator['executeTaskPipeline'](tasks);
      const totalTime = Date.now() - startTime;

      // With parallel execution of 3 tasks (100ms each) and max concurrency of 3,
      // total time should be around 100ms, not 300ms
      expect(totalTime).toBeLessThan(200); // Allow some overhead
      
      // Check that tasks overlapped in execution
      const task1Start = startTimes['Task1'];
      const task2Start = startTimes['Task2'];
      const task3Start = startTimes['Task3'];
      const maxStartDiff = Math.max(
        Math.abs(task1Start - task2Start),
        Math.abs(task2Start - task3Start),
        Math.abs(task1Start - task3Start)
      );
      
      // Tasks should start within 50ms of each other
      expect(maxStartDiff).toBeLessThan(50);
    });

    it('should respect concurrency limits', async () => {
      let currentlyRunning = 0;
      let maxConcurrent = 0;
      
      // Create more tasks than concurrency limit
      const tasks: TaskDefinition[] = Array.from({ length: 10 }, (_, i) => ({
        id: `Task${i}`,
        type: 'story_analysis' as const,
        dependencies: [],
        priority: 'high' as const,
        estimatedDuration: 50,
        agent: 'test',
        payload: {}
      }));

      // Mock executeTask to track concurrency
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task: TaskDefinition) => {
        currentlyRunning++;
        maxConcurrent = Math.max(maxConcurrent, currentlyRunning);
        
        await new Promise(resolve => setTimeout(resolve, 50));
        
        currentlyRunning--;
        
        return {
          taskId: task.id,
          success: true,
          result: { id: task.id },
          duration: 50,
          agentUsed: 'test'
        };
      });

      await orchestrator['executeTaskPipeline'](tasks);

      // Should never exceed concurrency limit of 3
      expect(maxConcurrent).toBeLessThanOrEqual(3);
      expect(maxConcurrent).toBeGreaterThan(1); // Should use parallelism
    });
  });

  describe('Progress and Event Tracking', () => {
    it('should emit detailed progress events during execution', async () => {
      const events: Array<{ type: string; data: any }> = [];
      
      orchestrator.on('orchestration:started', (data) => 
        events.push({ type: 'started', data })
      );
      orchestrator.on('task:started', (data) => 
        events.push({ type: 'task-started', data })
      );
      orchestrator.on('orchestration:progress', (data) => 
        events.push({ type: 'progress', data })
      );
      orchestrator.on('orchestration:completed', (data) => 
        events.push({ type: 'completed', data })
      );

      const tasks: TaskDefinition[] = [
        {
          id: 'Task1',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'Task2',
          type: 'story_analysis',
          dependencies: ['Task1'],
          priority: 'medium',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        }
      ];

      orchestrator['executeTask'] = jest.fn().mockResolvedValue({
        taskId: 'test',
        success: true,
        result: {},
        duration: 10,
        agentUsed: 'test'
      });

      await orchestrator['executeTaskPipeline'](tasks);

      // Check event sequence
      expect(events[0].type).toBe('started');
      expect(events[0].data.totalTasks).toBe(2);
      
      // Should have task started events
      const taskStartEvents = events.filter(e => e.type === 'task-started');
      expect(taskStartEvents.length).toBe(2);
      
      // Should have progress updates
      const progressEvents = events.filter(e => e.type === 'progress');
      expect(progressEvents.length).toBeGreaterThan(0);
      
      // Should complete
      const completedEvent = events.find(e => e.type === 'completed');
      expect(completedEvent?.data.successful).toBe(2);
    });

    it('should track accurate time estimates', async () => {
      const progressUpdates: any[] = [];
      
      orchestrator.on('orchestration:progress', (progress) => {
        progressUpdates.push(progress);
      });

      const tasks: TaskDefinition[] = [
        {
          id: 'FastTask',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 50,
          agent: 'test',
          payload: {}
        },
        {
          id: 'SlowTask',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 200,
          agent: 'test',
          payload: {}
        }
      ];

      let taskCount = 0;
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task: TaskDefinition) => {
        const duration = task.id === 'FastTask' ? 50 : 200;
        await new Promise(resolve => setTimeout(resolve, duration));
        taskCount++;
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration,
          agentUsed: 'test'
        };
      });

      await orchestrator['executeTaskPipeline'](tasks);

      // Check that time estimates were reasonable
      const firstUpdate = progressUpdates[0];
      const lastUpdate = progressUpdates[progressUpdates.length - 1];
      
      expect(firstUpdate.estimatedTimeRemaining).toBeGreaterThan(0);
      expect(lastUpdate.completedTasks).toBe(2);
      expect(lastUpdate.failedTasks).toBe(0);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should continue processing after individual task failures', async () => {
      const completedTasks: string[] = [];
      
      const tasks: TaskDefinition[] = [
        {
          id: 'Task1',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'Task2',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        },
        {
          id: 'Task3',
          type: 'story_analysis',
          dependencies: [],
          priority: 'high',
          estimatedDuration: 10,
          agent: 'test',
          payload: {}
        }
      ];

      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task: TaskDefinition) => {
        if (task.id === 'Task2') {
          throw new Error('Task2 failed');
        }
        
        completedTasks.push(task.id);
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration: 10,
          agentUsed: 'test'
        };
      });

      const results = await orchestrator['executeTaskPipeline'](tasks);

      // Should complete Task1 and Task3 despite Task2 failing
      expect(completedTasks).toContain('Task1');
      expect(completedTasks).toContain('Task3');
      expect(completedTasks).not.toContain('Task2');
      
      // Should have 2 successful and 1 failed
      expect(results.filter(r => r.success).length).toBe(2);
      expect(results.filter(r => !r.success).length).toBe(1);
    });

    it('should handle memory pressure gracefully', async () => {
      // Create many small tasks to test memory handling
      const tasks: TaskDefinition[] = Array.from({ length: 100 }, (_, i) => ({
        id: `Task${i}`,
        type: 'story_analysis' as const,
        dependencies: i > 0 ? [`Task${i - 1}`] : [],
        priority: 'medium' as const,
        estimatedDuration: 1,
        agent: 'test',
        payload: { data: new Array(1000).fill('test') } // Some memory usage
      }));

      let completedCount = 0;
      orchestrator['executeTask'] = jest.fn().mockImplementation(async () => {
        completedCount++;
        return {
          taskId: `Task${completedCount}`,
          success: true,
          result: {},
          duration: 1,
          agentUsed: 'test'
        };
      });

      const results = await orchestrator['executeTaskPipeline'](tasks);

      expect(results.length).toBe(100);
      expect(completedCount).toBe(100);
    });
  });

  describe('Cancellation and Cleanup', () => {
    it('should properly clean up on cancellation', async () => {
      let cancelledTasks = 0;
      
      const tasks: TaskDefinition[] = Array.from({ length: 10 }, (_, i) => ({
        id: `Task${i}`,
        type: 'story_analysis' as const,
        dependencies: [],
        priority: 'medium' as const,
        estimatedDuration: 100,
        agent: 'test',
        payload: {}
      }));

      orchestrator['executeTask'] = jest.fn().mockImplementation(async () => {
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(resolve, 100);
          orchestrator.once('orchestration:cancelled', () => {
            clearTimeout(timeout);
            cancelledTasks++;
            reject(new Error('Cancelled'));
          });
        });
        
        return {
          taskId: 'test',
          success: true,
          result: {},
          duration: 100,
          agentUsed: 'test'
        };
      });

      // Start execution
      const executionPromise = orchestrator['executeTaskPipeline'](tasks);

      // Cancel after a short delay
      setTimeout(() => {
        orchestrator.cancelOrchestration();
      }, 50);

      await executionPromise;

      // Some tasks should have been cancelled
      expect(cancelledTasks).toBeGreaterThan(0);
      expect(orchestrator['taskQueue'].length).toBe(0); // Queue should be cleared
    });
  });
});