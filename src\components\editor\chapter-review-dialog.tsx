'use client'

import { useState, useEffect, useMemo } from 'react'
import { logger } from '@/lib/services/logger';

import type { UserChanges as UserChangesType } from '@/hooks/use-chapter-generation'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  FileText, 
  Eye, 
  Edit3, 
  ArrowRight, 
  CheckCircle2, 
  AlertCircle,
  Lightbulb
} from 'lucide-react'

interface ChapterReviewPanelProps {
  isOpen: boolean
  onClose: () => void
  originalContent: string
  editedContent: string
  chapterTitle: string
  chapterNumber: number
  projectId: string
  chapterId: string
  onSubmitChanges: (changes: UserChangesType) => Promise<boolean>
  onApproveGeneration: () => Promise<boolean>
}

interface ChangeAnalysis {
  type: 'plot' | 'character' | 'pacing' | 'style' | 'dialogue' | 'setting' | 'other'
  description: string
  impact: 'low' | 'medium' | 'high'
  affectedElements: string[]
  textChange: {
    from: string
    to: string
    startIndex: number
    endIndex: number
  }
}

interface LocalUserChanges {
  chapterId: string
  changes: ChangeAnalysis[]
  userNotes: string
  significantChanges: string[]
  requestsPlanUpdate: boolean
}

export function ChapterReviewDialog({
  isOpen,
  onClose,
  originalContent,
  editedContent,
  chapterTitle,
  chapterId,
  onSubmitChanges,
  onApproveGeneration
}: ChapterReviewPanelProps) {
  const [userNotes, setUserNotes] = useState('')
  const [selectedChangeTypes, setSelectedChangeTypes] = useState<string[]>([])
  const [requestsPlanUpdate, setRequestsPlanUpdate] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [viewMode, setViewMode] = useState<'side-by-side' | 'diff'>('side-by-side')

  // Analyze changes between original and edited content
  const changeAnalysis = useMemo(() => {
    if (!originalContent || !editedContent || originalContent === editedContent) {
      return []
    }

    return analyzeTextChanges(originalContent, editedContent)
  }, [originalContent, editedContent])

  const hasSignificantChanges = changeAnalysis.length > 0
  const hasHighImpactChanges = changeAnalysis.some(change => change.impact === 'high')

  // Auto-detect change types
  useEffect(() => {
    if (changeAnalysis.length > 0) {
      const detectedTypes = [...new Set(changeAnalysis.map(change => change.type))]
      setSelectedChangeTypes(detectedTypes)
      
      // Auto-suggest plan update for high-impact changes
      if (hasHighImpactChanges) {
        setRequestsPlanUpdate(true)
      }
    }
  }, [changeAnalysis, hasHighImpactChanges])

  const handleSubmitChanges = async () => {
    if (!hasSignificantChanges) return

    setIsSubmitting(true)
    try {
      const significantChanges = changeAnalysis
        .filter(change => change.impact === 'high' || selectedChangeTypes.includes(change.type))
        .map(change => change.description)

      const userChanges: LocalUserChanges = {
        chapterId,
        changes: changeAnalysis,
        userNotes,
        significantChanges,
        requestsPlanUpdate
      }

      await onSubmitChanges(userChanges)
      onClose()
    } catch (error) {
      logger.error('Failed to submit changes:', error);
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleApprove = async () => {
    setIsSubmitting(true)
    try {
      await onApproveGeneration()
      onClose()
    } catch (error) {
      logger.error('Failed to approve generation:', error);
    } finally {
      setIsSubmitting(false)
    }
  }

  const getDiffDisplay = () => {
    if (!hasSignificantChanges) {
      return <div className="text-center py-6 sm:py-8 lg:py-10 text-muted-foreground">No significant changes detected</div>
    }

    return (
      <div className="space-y-4">
        {changeAnalysis.map((change, index) => (
          <div key={index} className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant={change.impact === 'high' ? 'destructive' : change.impact === 'medium' ? 'default' : 'secondary'}>
                {change.type}
              </Badge>
              <Badge variant="outline">{change.impact} impact</Badge>
              {change.affectedElements.map(element => (
                <Badge key={element} variant="outline" className="text-xs">
                  {element}
                </Badge>
              ))}
            </div>
            
            <p className="text-sm mb-3">{change.description}</p>
            
            <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6 text-xs">
              <div>
                <Label className="text-error">Original:</Label>
                <div className="bg-error-light p-2 rounded border mt-1 line-through">
                  {change.textChange.from.substring(0, 150)}{change.textChange.from.length > 150 ? '...' : ''}
                </div>
              </div>
              <div>
                <Label className="text-success">Edited:</Label>
                <div className="bg-success-light p-2 rounded border mt-1">
                  {change.textChange.to.substring(0, 150)}{change.textChange.to.length > 150 ? '...' : ''}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px] xl:max-w-[1400px] 2xl:max-w-[1600px] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Review: {chapterTitle}
          </DialogTitle>
          <DialogDescription>
            Review the AI-generated chapter and your edits. Submit changes to update future chapter plans.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Status and Controls */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
              <div className="flex items-center gap-2">
                {hasSignificantChanges ? (
                  <AlertCircle className="h-4 w-4 text-warning" />
                ) : (
                  <CheckCircle2 className="h-4 w-4 text-success" />
                )}
                <span className="text-sm font-medium">
                  {hasSignificantChanges 
                    ? `${changeAnalysis.length} changes detected`
                    : 'No significant changes'
                  }
                </span>
              </div>
              
              {hasHighImpactChanges && (
                <Badge variant="destructive" className="text-xs">
                  High Impact Changes
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'side-by-side' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('side-by-side')}
              >
                <Eye className="h-4 w-4 mr-1" />
                Side by Side
              </Button>
              <Button
                variant={viewMode === 'diff' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('diff')}
              >
                <Edit3 className="h-4 w-4 mr-1" />
                Changes
              </Button>
            </div>
          </div>

          {/* Content Display */}
          <div className="flex-1 overflow-hidden">
            {viewMode === 'side-by-side' ? (
              <div className="h-full grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6 p-4">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Original Generated Content
                  </Label>
                  <ScrollArea className="h-full border rounded-lg">
                    <div className="p-4 text-sm whitespace-pre-wrap">
                      {originalContent}
                    </div>
                  </ScrollArea>
                </div>
                
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Edit3 className="h-4 w-4" />
                    Your Edited Version
                  </Label>
                  <ScrollArea className="h-full border rounded-lg">
                    <div className="p-4 text-sm whitespace-pre-wrap">
                      {editedContent}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            ) : (
              <div className="h-full p-4">
                <ScrollArea className="h-full">
                  {getDiffDisplay()}
                </ScrollArea>
              </div>
            )}
          </div>

          {/* Change Analysis and Controls */}
          {hasSignificantChanges && (
            <div className="border-t p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                <div className="space-y-3">
                  <Label>Change Categories Detected:</Label>
                  <div className="flex flex-wrap gap-2">
                    {['plot', 'character', 'pacing', 'style', 'dialogue', 'setting'].map(type => {
                      const isDetected = changeAnalysis.some(change => change.type === type)
                      const isSelected = selectedChangeTypes.includes(type)
                      
                      return (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox 
                            id={type}
                            checked={isSelected}
                            disabled={!isDetected}
                            onCheckedChange={(checked) => {
                              if (checked === true) {
                                setSelectedChangeTypes([...selectedChangeTypes, type])
                              } else {
                                setSelectedChangeTypes(selectedChangeTypes.filter(t => t !== type))
                              }
                            }}
                          />
                          <Label 
                            htmlFor={type}
                            className={`capitalize text-sm ${!isDetected ? 'text-muted-foreground' : ''}`}
                          >
                            {type}
                          </Label>
                        </div>
                      )
                    })}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="plan-update"
                      checked={requestsPlanUpdate}
                      onCheckedChange={(checked) => setRequestsPlanUpdate(checked === true)}
                    />
                    <Label htmlFor="plan-update" className="text-sm">
                      Request plan adjustment for future chapters
                    </Label>
                  </div>
                  
                  {requestsPlanUpdate && (
                    <div className="text-xs text-info bg-info-light p-2 rounded">
                      <Lightbulb className="h-3 w-3 inline mr-1" />
                      AI will analyze your changes and adjust upcoming chapter outlines accordingly.
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="user-notes">Additional Notes (Optional):</Label>
                <Textarea
                  id="user-notes"
                  value={userNotes}
                  onChange={(e) => setUserNotes(e.target.value)}
                  placeholder="Explain your changes or provide context for the AI planning system..."
                  className="min-h-[80px]"
                />
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="border-t p-4 flex justify-between">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            
            <div className="flex gap-2">
              {!hasSignificantChanges ? (
                <Button onClick={handleApprove} disabled={isSubmitting}>
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Approving...' : 'Approve Generation'}
                </Button>
              ) : (
                <>
                  <Button variant="outline" onClick={handleApprove} disabled={isSubmitting}>
                    Accept As-Is
                  </Button>
                  <Button onClick={handleSubmitChanges} disabled={isSubmitting}>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    {isSubmitting ? 'Submitting...' : 'Submit Changes & Update Plan'}
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Helper function to analyze text changes
function analyzeTextChanges(original: string, edited: string): ChangeAnalysis[] {
  const changes: ChangeAnalysis[] = []
  
  // Simple word-level diff analysis
  const originalWords = original.split(/\s+/)
  const editedWords = edited.split(/\s+/)
  
  // Basic change detection logic
  if (originalWords.length !== editedWords.length) {
    changes.push({
      type: 'pacing',
      description: `Chapter length changed significantly (${originalWords.length} → ${editedWords.length} words)`,
      impact: Math.abs(originalWords.length - editedWords.length) > 100 ? 'high' : 'medium',
      affectedElements: ['word count', 'pacing'],
      textChange: {
        from: `${originalWords.length} words`,
        to: `${editedWords.length} words`,
        startIndex: 0,
        endIndex: 0
      }
    })
  }

  // Detect dialogue changes
  const originalDialogue = (original.match(/"/g) || []).length
  const editedDialogue = (edited.match(/"/g) || []).length
  
  if (Math.abs(originalDialogue - editedDialogue) > 2) {
    changes.push({
      type: 'dialogue',
      description: `Significant dialogue changes detected`,
      impact: 'medium',
      affectedElements: ['character voice', 'dialogue'],
      textChange: {
        from: `${originalDialogue / 2} dialogue exchanges`,
        to: `${editedDialogue / 2} dialogue exchanges`,
        startIndex: 0,
        endIndex: 0
      }
    })
  }

  // Detect character name changes/additions
  const characterPattern = /\b[A-Z][a-z]+\b/g
  const originalNames = new Set(original.match(characterPattern) || [])
  const editedNames = new Set(edited.match(characterPattern) || [])
  
  const addedNames = [...editedNames].filter(name => !originalNames.has(name))
  const removedNames = [...originalNames].filter(name => !editedNames.has(name))
  
  if (addedNames.length > 0 || removedNames.length > 0) {
    changes.push({
      type: 'character',
      description: `Character presence modified: ${addedNames.length ? `Added: ${addedNames.join(', ')}` : ''} ${removedNames.length ? `Removed: ${removedNames.join(', ')}` : ''}`,
      impact: 'high',
      affectedElements: [...addedNames, ...removedNames],
      textChange: {
        from: removedNames.join(', '),
        to: addedNames.join(', '),
        startIndex: 0,
        endIndex: 0
      }
    })
  }

  // Detect style/tone changes (basic analysis)
  const originalSentiments = analyzeBasicSentiment(original)
  const editedSentiments = analyzeBasicSentiment(edited)
  
  if (originalSentiments.tone !== editedSentiments.tone) {
    changes.push({
      type: 'style',
      description: `Tone shift detected: ${originalSentiments.tone} → ${editedSentiments.tone}`,
      impact: 'medium',
      affectedElements: ['tone', 'style'],
      textChange: {
        from: `${originalSentiments.tone} tone`,
        to: `${editedSentiments.tone} tone`,
        startIndex: 0,
        endIndex: 0
      }
    })
  }

  return changes
}

function analyzeBasicSentiment(text: string): { tone: string; confidence: number } {
  const positiveWords = ['happy', 'joy', 'smile', 'laugh', 'love', 'wonderful', 'amazing', 'great']
  const negativeWords = ['sad', 'angry', 'fear', 'hate', 'terrible', 'awful', 'bad', 'wrong']
  const actionWords = ['run', 'fight', 'chase', 'attack', 'battle', 'strike', 'charge']
  
  const words = text.toLowerCase().split(/\s+/)
  
  const positiveCount = words.filter(word => positiveWords.includes(word)).length
  const negativeCount = words.filter(word => negativeWords.includes(word)).length
  const actionCount = words.filter(word => actionWords.includes(word)).length
  
  if (actionCount > positiveCount && actionCount > negativeCount) {
    return { tone: 'action-packed', confidence: 0.6 }
  } else if (positiveCount > negativeCount) {
    return { tone: 'positive', confidence: 0.6 }
  } else if (negativeCount > positiveCount) {
    return { tone: 'dark', confidence: 0.6 }
  } else {
    return { tone: 'neutral', confidence: 0.3 }
  }
}