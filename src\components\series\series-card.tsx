'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  BookOpen, 
  MoreVertical, 
  Edit, 
  Trash, 
  Users, 
  Globe,
  TrendingUp,
  Calendar,
  FileText,
  Plus
} from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { toast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import type { Series } from '@/lib/db/types'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { TIME_MS } from '@/lib/constants'

interface SeriesCardProps {
  series: Series & {
    projects?: Array<{
      project: {
        id: string
        title: string
        status: string
        current_word_count: number
        target_word_count: number
      }
    }>
    universe?: {
      id: string
      name: string
    }
  }
  onUpdate: () => void
}

export function SeriesCard({ series, onUpdate }: SeriesCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      const supabase = createClient()
      const { error } = await supabase
        .from('series')
        .delete()
        .eq('id', series.id)

      if (error) throw error

      toast({
        title: 'Series Deleted',
        description: `"${series.title}" has been deleted`
      })
      
      onUpdate()
    } catch (error) {
      logger.error('Failed to delete series', error)
      toast({
        title: 'Error',
        description: 'Failed to delete series',
        variant: 'destructive'
      })
    } finally {
      setIsDeleting(false)
      setShowDeleteDialog(false)
    }
  }

  const totalWords = series.projects?.reduce(
    (sum, p) => sum + (p.project.current_word_count || 0), 
    0
  ) || 0

  const completedBooks = series.projects?.filter(
    p => p.project.status === 'completed'
  ).length || 0

  const progress = series.planned_books 
    ? (completedBooks / series.planned_books) * 100
    : 0

  return (
    <>
      <Card className="group hover:shadow-lg transition-all duration-200">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-xl mb-1 group-hover:text-primary transition-colors">
                <Link href={`/series/${series.id}`}>
                  {series.title}
                </Link>
              </CardTitle>
              <CardDescription className="line-clamp-2">
                {series.description || 'No description provided'}
              </CardDescription>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/series/${series.id}`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Series
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/series/${series.id}/continuity`}>
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Continuity Check
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  Delete Series
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Metadata Badges */}
          <div className="flex flex-wrap gap-2 mt-3">
            <Badge variant="secondary">
              {series.genre}
            </Badge>
            <Badge variant="outline">
              {series.target_audience}
            </Badge>
            {series.universe && (
              <Badge variant="outline" className="gap-1">
                <Globe className="h-3 w-3" />
                {series.universe.name}
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Progress */}
          {series.planned_books && series.planned_books > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Series Progress</span>
                <span className="text-muted-foreground">
                  {completedBooks} of {series.planned_books} books
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {/* Stats */}
          <div className="grid grid-cols-3 gap-2 text-center">
            <div className="space-y-1">
              <div className="text-2xl font-bold">
                {series.projects?.length || 0}
              </div>
              <div className="text-xs text-muted-foreground">Books</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold">
                {series.character_count || 0}
              </div>
              <div className="text-xs text-muted-foreground">Characters</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold">
                {Math.round(totalWords / TIME_MS.SECOND)}k
              </div>
              <div className="text-xs text-muted-foreground">Words</div>
            </div>
          </div>

          {/* Books List */}
          {series.projects && series.projects.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Books in Series</h4>
              <div className="space-y-1">
                {series.projects.slice(0, 3).map(({ project }) => (
                  <Link
                    key={project.id}
                    href={`/projects/${project.id}`}
                    className="flex items-center gap-2 p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="flex-1 text-sm truncate">{project.title}</span>
                    <Badge variant="outline" className="text-xs">
                      {project.status}
                    </Badge>
                  </Link>
                ))}
                {series.projects.length > 3 && (
                  <p className="text-xs text-muted-foreground pl-6">
                    +{series.projects.length - 3} more books
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button variant="outline" size="sm" className="flex-1" asChild>
              <Link href={`/series/${series.id}`}>
                <BookOpen className="mr-2 h-4 w-4" />
                Manage
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="flex-1" asChild>
              <Link href={`/projects/new?seriesId=${series.id}`}>
                <Plus className="mr-2 h-4 w-4" />
                Add Book
              </Link>
            </Button>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Created {new Date(series.created_at).toLocaleDateString()}
            </div>
            {series.settings?.timeline_type && (
              <Badge variant="secondary" className="text-xs">
                {series.settings.timeline_type}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Series</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{series.title}"? This action cannot be undone.
              The books in this series will not be deleted, but they will no longer be associated with this series.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Series'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}