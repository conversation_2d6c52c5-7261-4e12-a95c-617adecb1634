# BookScribe Testing Strategy Documentation

## Overview

BookScribe employs a comprehensive testing strategy combining unit tests, integration tests, end-to-end tests, and performance testing. The testing framework ensures code quality, prevents regressions, and maintains reliability across the entire application stack.

## Testing Architecture

### Testing Stack

- **Unit Testing**: Jest with React Testing Library
- **Integration Testing**: Jest with Supertest
- **E2E Testing**: Playwright
- **Performance Testing**: Lighthouse CI
- **Visual Testing**: <PERSON> (planned)
- **API Testing**: Postman/Newman
- **Load Testing**: k6 (planned)

### Test Organization

```
tests/
├── unit/                    # Unit tests
│   ├── api/                # API route tests
│   ├── components/         # React component tests
│   ├── lib/               # Library/utility tests
│   │   ├── agents/        # AI agent tests
│   │   ├── auth/          # Authentication tests
│   │   └── services/      # Service tests
│   └── hooks/             # React hook tests
├── integration/           # Integration tests
│   ├── api/              # API integration tests
│   ├── database/         # Database tests
│   └── services/         # Service integration tests
├── e2e/                   # End-to-end tests
│   ├── auth/             # Authentication flows
│   ├── projects/         # Project workflows
│   ├── editor/           # Editor functionality
│   └── billing/          # Payment flows
├── fixtures/              # Test data and mocks
├── utils/                 # Test utilities
└── config/                # Test configurations
```

## Unit Testing

### Component Testing

```typescript
// tests/unit/components/editor/monaco-editor.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { MonacoEditor } from '@/components/editor/monaco-editor'
import { vi } from 'vitest'

describe('MonacoEditor', () => {
  const mockOnChange = vi.fn()
  const defaultProps = {
    value: 'Initial content',
    onChange: mockOnChange,
    language: 'markdown',
    theme: 'writers-sanctuary'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders with initial content', async () => {
    render(<MonacoEditor {...defaultProps} />)
    
    await waitFor(() => {
      const editor = screen.getByRole('textbox')
      expect(editor).toHaveValue('Initial content')
    })
  })

  it('calls onChange when content is modified', async () => {
    render(<MonacoEditor {...defaultProps} />)
    
    const editor = screen.getByRole('textbox')
    fireEvent.change(editor, { target: { value: 'New content' } })
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith('New content')
    })
  })

  it('applies custom theme', async () => {
    render(<MonacoEditor {...defaultProps} theme="midnight-ink" />)
    
    await waitFor(() => {
      const editorContainer = screen.getByTestId('monaco-container')
      expect(editorContainer).toHaveClass('monaco-theme-midnight-ink')
    })
  })
})
```

### Hook Testing

```typescript
// tests/unit/hooks/use-auto-save.test.ts
import { renderHook, act } from '@testing-library/react'
import { useAutoSave } from '@/hooks/use-auto-save'
import { vi } from 'vitest'

describe('useAutoSave', () => {
  const mockSave = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    vi.useRealTimers()
  })

  it('should trigger save after delay', () => {
    const { result } = renderHook(() => 
      useAutoSave({
        content: 'Initial content',
        onSave: mockSave,
        delay: 5000,
        enabled: true
      })
    )

    // Change content
    act(() => {
      result.current.updateContent('Updated content')
    })

    // Fast-forward time
    act(() => {
      vi.advanceTimersByTime(5000)
    })

    expect(mockSave).toHaveBeenCalledWith('Updated content')
  })

  it('should cancel save when disabled', () => {
    const { result, rerender } = renderHook(
      ({ enabled }) => useAutoSave({
        content: 'Content',
        onSave: mockSave,
        delay: 5000,
        enabled
      }),
      { initialProps: { enabled: true } }
    )

    act(() => {
      result.current.updateContent('New content')
    })

    // Disable auto-save
    rerender({ enabled: false })

    act(() => {
      vi.advanceTimersByTime(5000)
    })

    expect(mockSave).not.toHaveBeenCalled()
  })
})
```

### Service Testing

```typescript
// tests/unit/lib/services/ai-orchestrator.test.ts
import { AIOrchestrator } from '@/lib/services/ai-orchestrator'
import { ContentGenerator } from '@/lib/services/content-generator'
import { vi } from 'vitest'

vi.mock('@/lib/services/content-generator')

describe('AIOrchestrator', () => {
  let orchestrator: AIOrchestrator
  let mockContentGenerator: vi.Mocked<ContentGenerator>

  beforeEach(() => {
    mockContentGenerator = {
      generateContent: vi.fn(),
      validateContent: vi.fn()
    } as any

    orchestrator = new AIOrchestrator({
      contentGenerator: mockContentGenerator
    })
  })

  describe('orchestrateGeneration', () => {
    it('should coordinate content generation', async () => {
      const request = {
        type: 'chapter',
        projectId: 'project-123',
        prompt: 'Write chapter 1'
      }

      mockContentGenerator.generateContent.mockResolvedValue({
        success: true,
        data: 'Generated chapter content'
      })

      const result = await orchestrator.orchestrateGeneration(request)

      expect(mockContentGenerator.generateContent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'chapter',
          prompt: 'Write chapter 1'
        })
      )
      expect(result.success).toBe(true)
      expect(result.data).toBe('Generated chapter content')
    })

    it('should handle generation errors', async () => {
      mockContentGenerator.generateContent.mockRejectedValue(
        new Error('API limit exceeded')
      )

      const result = await orchestrator.orchestrateGeneration({
        type: 'scene',
        projectId: 'project-123',
        prompt: 'Write a scene'
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('API limit exceeded')
    })
  })
})
```

## Integration Testing

### API Integration Tests

```typescript
// tests/integration/api/projects.test.ts
import request from 'supertest'
import { createMocks } from 'node-mocks-http'
import { POST, GET } from '@/app/api/projects/route'
import { createTestUser, cleanupTestData } from '@/tests/utils/test-helpers'

describe('Projects API Integration', () => {
  let testUser: TestUser
  
  beforeAll(async () => {
    testUser = await createTestUser()
  })
  
  afterAll(async () => {
    await cleanupTestData(testUser.id)
  })

  describe('POST /api/projects', () => {
    it('should create a new project', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${testUser.token}`
        },
        body: {
          title: 'Test Novel',
          genre: 'fantasy',
          targetWordCount: 80000
        }
      })

      await POST(req as any)
      
      const data = JSON.parse(res._getData())
      expect(res._getStatusCode()).toBe(201)
      expect(data.project).toMatchObject({
        title: 'Test Novel',
        genre: 'fantasy',
        targetWordCount: 80000,
        userId: testUser.id
      })
    })

    it('should enforce project limits', async () => {
      // Create maximum allowed projects
      for (let i = 0; i < 5; i++) {
        await createProject(testUser.id, `Project ${i}`)
      }

      const { req, res } = createMocks({
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${testUser.token}`
        },
        body: {
          title: 'Exceeds Limit',
          genre: 'mystery'
        }
      })

      await POST(req as any)
      
      expect(res._getStatusCode()).toBe(403)
      expect(JSON.parse(res._getData())).toMatchObject({
        error: 'Project limit exceeded'
      })
    })
  })
})
```

### Database Integration Tests

```typescript
// tests/integration/database/chapter-versions.test.ts
import { createServerClient } from '@/lib/supabase'
import { VersionHistoryService } from '@/lib/version-history'
import { setupTestDatabase, teardownTestDatabase } from '@/tests/utils/db-helpers'

describe('Chapter Versions Database Integration', () => {
  let supabase: SupabaseClient
  let versionService: VersionHistoryService
  let testData: TestData
  
  beforeAll(async () => {
    testData = await setupTestDatabase()
    supabase = await createServerClient()
    versionService = new VersionHistoryService()
  })
  
  afterAll(async () => {
    await teardownTestDatabase(testData)
  })

  it('should create and retrieve chapter versions', async () => {
    // Create initial version
    const version1 = await versionService.createManualVersion(
      testData.chapterId,
      testData.userId,
      'Initial draft'
    )
    
    // Update content and create another version
    await updateChapterContent(testData.chapterId, 'Updated content')
    
    const version2 = await versionService.createManualVersion(
      testData.chapterId,
      testData.userId,
      'Revised draft'
    )
    
    // Retrieve all versions
    const versions = await versionService.getChapterVersions(
      testData.chapterId,
      testData.userId
    )
    
    expect(versions).toHaveLength(2)
    expect(versions[0].version_number).toBe(2)
    expect(versions[1].version_number).toBe(1)
  })

  it('should handle concurrent version creation', async () => {
    const promises = Array(5).fill(null).map((_, i) => 
      versionService.createManualVersion(
        testData.chapterId,
        testData.userId,
        `Concurrent version ${i}`
      )
    )
    
    const results = await Promise.allSettled(promises)
    const successful = results.filter(r => r.status === 'fulfilled')
    
    expect(successful.length).toBeGreaterThanOrEqual(1)
    
    // Verify version numbers are sequential
    const versions = await versionService.getChapterVersions(
      testData.chapterId,
      testData.userId
    )
    
    const versionNumbers = versions.map(v => v.version_number)
    expect(versionNumbers).toEqual(
      [...new Set(versionNumbers)].sort((a, b) => b - a)
    )
  })
})
```

## End-to-End Testing

### E2E Test Structure

```typescript
// tests/e2e/editor/writing-flow.spec.ts
import { test, expect } from '@playwright/test'
import { loginAsTestUser, createTestProject } from '../helpers'

test.describe('Writing Flow', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsTestUser(page)
    await createTestProject(page, 'E2E Test Novel')
  })

  test('complete chapter writing workflow', async ({ page }) => {
    // Navigate to project
    await page.click('text=E2E Test Novel')
    await page.waitForURL(/\/projects\/.*/)
    
    // Open editor
    await page.click('text=Write Chapter 1')
    await expect(page.locator('.monaco-editor')).toBeVisible()
    
    // Type content
    const editor = page.locator('.monaco-editor textarea')
    await editor.fill('Chapter 1: The Beginning\n\nIt was a dark and stormy night...')
    
    // Wait for auto-save
    await page.waitForTimeout(5000)
    await expect(page.locator('text=Saved')).toBeVisible()
    
    // Use AI assistance
    await page.click('[data-testid="ai-assistant-toggle"]')
    await page.fill('[data-testid="ai-prompt"]', 'Continue this opening')
    await page.click('text=Generate')
    
    // Wait for AI response
    await expect(page.locator('[data-testid="ai-suggestion"]')).toBeVisible({
      timeout: 30000
    })
    
    // Accept suggestion
    await page.click('text=Insert')
    
    // Verify content updated
    const content = await editor.inputValue()
    expect(content).toContain('Chapter 1: The Beginning')
    expect(content.length).toBeGreaterThan(100)
    
    // Create version
    await page.click('[data-testid="version-menu"]')
    await page.click('text=Create Version')
    await page.fill('[data-testid="version-description"]', 'First draft')
    await page.click('text=Save Version')
    
    await expect(page.locator('text=Version created')).toBeVisible()
  })

  test('collaborative editing', async ({ page, context }) => {
    // First user starts editing
    await page.goto('/projects/test-project/write')
    const editor1 = page.locator('.monaco-editor textarea')
    await editor1.fill('User 1 typing...')
    
    // Second user joins
    const page2 = await context.newPage()
    await loginAsTestUser(page2, 'testuser2')
    await page2.goto('/projects/test-project/write')
    
    // Verify presence indicators
    await expect(page.locator('[data-testid="collaborator-avatar"]')).toBeVisible()
    await expect(page2.locator('[data-testid="collaborator-avatar"]')).toBeVisible()
    
    // Second user types
    const editor2 = page2.locator('.monaco-editor textarea')
    await editor2.fill('User 2 also typing...')
    
    // Verify real-time sync
    await page.waitForTimeout(1000)
    const content1 = await editor1.inputValue()
    const content2 = await editor2.inputValue()
    expect(content1).toBe(content2)
  })
})
```

### Visual Regression Testing

```typescript
// tests/e2e/visual/theme-consistency.spec.ts
import { test, expect } from '@playwright/test'
import { themes } from '@/lib/themes'

test.describe('Theme Visual Consistency', () => {
  for (const theme of themes) {
    test(`${theme.name} theme screenshots`, async ({ page }) => {
      await page.goto('/')
      
      // Apply theme
      await page.evaluate((themeName) => {
        document.documentElement.className = themeName
      }, theme.id)
      
      // Take screenshots of key pages
      await page.screenshot({ 
        path: `tests/screenshots/${theme.id}-landing.png`,
        fullPage: true 
      })
      
      await page.goto('/dashboard')
      await page.screenshot({ 
        path: `tests/screenshots/${theme.id}-dashboard.png` 
      })
      
      await page.goto('/projects/sample/write')
      await page.screenshot({ 
        path: `tests/screenshots/${theme.id}-editor.png` 
      })
      
      // Compare with baseline
      expect(await page.screenshot()).toMatchSnapshot(`${theme.id}-editor.png`)
    })
  }
})
```

## Test Data Management

### Fixtures

```typescript
// tests/fixtures/projects.ts
export const mockProject = {
  id: 'proj_123',
  title: 'Test Novel',
  genre: 'fantasy',
  targetWordCount: 80000,
  targetChapters: 20,
  currentWordCount: 15000,
  userId: 'user_123',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-15')
}

export const mockChapter = {
  id: 'chap_123',
  projectId: 'proj_123',
  chapterNumber: 1,
  title: 'The Beginning',
  content: 'It was a dark and stormy night...',
  wordCount: 3000,
  status: 'draft' as const,
  outline: 'Hero discovers mysterious artifact'
}

export const createMockProject = (overrides?: Partial<Project>): Project => ({
  ...mockProject,
  id: `proj_${Date.now()}`,
  ...overrides
})
```

### Test Utilities

```typescript
// tests/utils/test-helpers.ts
import { createServerClient } from '@/lib/supabase'

export async function createTestUser(
  email = `test_${Date.now()}@example.com`
): Promise<TestUser> {
  const supabase = await createServerClient()
  
  // Create user
  const { data: authData, error } = await supabase.auth.signUp({
    email,
    password: 'TestPassword123!'
  })
  
  if (error) throw error
  
  // Create profile
  await supabase.from('profiles').insert({
    id: authData.user!.id,
    email,
    subscription_tier: 'free'
  })
  
  return {
    id: authData.user!.id,
    email,
    token: authData.session!.access_token
  }
}

export async function cleanupTestData(userId: string): Promise<void> {
  const supabase = await createServerClient()
  
  // Delete in correct order due to foreign keys
  await supabase.from('ai_generations').delete().eq('user_id', userId)
  await supabase.from('chapters').delete().eq('user_id', userId)
  await supabase.from('projects').delete().eq('user_id', userId)
  await supabase.from('profiles').delete().eq('id', userId)
  
  // Delete auth user
  await supabase.auth.admin.deleteUser(userId)
}
```

## Mocking Strategies

### API Mocking

```typescript
// tests/mocks/openai.ts
import { vi } from 'vitest'

export const mockOpenAI = {
  chat: {
    completions: {
      create: vi.fn().mockImplementation(async ({ messages, model }) => ({
        id: 'chatcmpl-123',
        object: 'chat.completion',
        created: Date.now(),
        model,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: 'Mocked AI response for testing'
          },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150
        }
      }))
    }
  }
}

// Usage in tests
vi.mock('openai', () => ({
  default: vi.fn(() => mockOpenAI)
}))
```

### Database Mocking

```typescript
// tests/mocks/supabase.ts
export const mockSupabase = {
  from: vi.fn((table: string) => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({ data: mockData[table], error: null }),
    execute: vi.fn().mockResolvedValue({ data: [mockData[table]], error: null })
  })),
  
  auth: {
    getUser: vi.fn().mockResolvedValue({
      data: { user: mockUser },
      error: null
    }),
    signIn: vi.fn().mockResolvedValue({
      data: { user: mockUser, session: mockSession },
      error: null
    })
  }
}
```

## Performance Testing

### Load Testing Configuration

```javascript
// tests/performance/load-test.js
import http from 'k6/http'
import { check, sleep } from 'k6'

export const options = {
  stages: [
    { duration: '30s', target: 20 },  // Ramp up
    { duration: '1m', target: 20 },   // Stay at 20 users
    { duration: '30s', target: 50 },  // Ramp up more
    { duration: '2m', target: 50 },   // Stay at 50 users
    { duration: '30s', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
  }
}

export default function() {
  // Test AI generation endpoint
  const payload = JSON.stringify({
    prompt: 'Write a short scene',
    type: 'scene',
    projectId: 'test-project'
  })
  
  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${__ENV.TEST_TOKEN}`
    }
  }
  
  const res = http.post(
    'http://localhost:3000/api/ai/generate',
    payload,
    params
  )
  
  check(res, {
    'status is 200': (r) => r.status === 200,
    'response has content': (r) => JSON.parse(r.body).content !== undefined,
    'response time < 500ms': (r) => r.timings.duration < 500
  })
  
  sleep(1)
}
```

## CI/CD Testing Pipeline

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit -- --coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: supabase/postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup test database
        run: |
          npm run db:test:setup
          npm run db:test:seed
      
      - name: Run integration tests
        env:
          DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
        run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: playwright-report/
```

## Test Coverage Requirements

### Coverage Thresholds

```javascript
// jest.config.js
module.exports = {
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/lib/agents/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    },
    './src/app/api/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    }
  }
}
```

### Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# View coverage in browser
npm run test:coverage:html

# Generate coverage badge
npm run test:coverage:badge
```

## Best Practices

### 1. Test Structure
- **Arrange-Act-Assert**: Clear test structure
- **One assertion per test**: Focused tests
- **Descriptive names**: Clear test intentions
- **DRY principles**: Reusable test utilities

### 2. Test Data
- **Factories over fixtures**: Dynamic test data
- **Cleanup after tests**: No test pollution
- **Realistic data**: Representative scenarios
- **Minimal setup**: Only required data

### 3. Async Testing
```typescript
// Good: Proper async handling
it('should handle async operations', async () => {
  const result = await someAsyncFunction()
  expect(result).toBeDefined()
})

// Bad: Missing async/await
it('should handle async operations', () => {
  someAsyncFunction().then(result => {
    expect(result).toBeDefined()
  })
})
```

### 4. Mocking
- **Mock external dependencies**: APIs, databases
- **Don't mock what you own**: Test actual code
- **Reset mocks between tests**: Clean state
- **Verify mock calls**: Ensure interactions

## Testing Checklist

### Before Commit
- [ ] All tests pass locally
- [ ] New features have tests
- [ ] Test coverage maintained
- [ ] No console.logs in tests
- [ ] Mocks properly cleaned up

### Code Review
- [ ] Tests are readable
- [ ] Edge cases covered
- [ ] Performance considered
- [ ] Security tested
- [ ] Accessibility verified

### Release
- [ ] Full test suite passes
- [ ] E2E tests on staging
- [ ] Performance benchmarks met
- [ ] Visual regression checked
- [ ] Load tests acceptable

## Future Improvements

### Planned Enhancements
1. **Contract Testing**: API contract validation
2. **Mutation Testing**: Test quality verification
3. **Property-Based Testing**: Generative test cases
4. **Accessibility Testing**: Automated a11y checks
5. **Security Testing**: Penetration testing
6. **Chaos Engineering**: Failure injection testing