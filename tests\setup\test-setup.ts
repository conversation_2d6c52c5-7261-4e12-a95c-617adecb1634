// Mock Stripe to avoid fetch issues in tests
jest.mock('@/lib/stripe', () => ({
  stripe: {
    customers: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn()
    },
    subscriptions: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
      cancel: jest.fn()
    },
    billingPortal: {
      sessions: {
        create: jest.fn()
      }
    },
    checkout: {
      sessions: {
        create: jest.fn()
      }
    }
  }
}));

// Mock subscription module
jest.mock('@/lib/subscription', () => ({
  getUserTier: jest.fn().mockReturnValue('standard'),
  getAIModelForTask: jest.fn().mockReturnValue('gpt-4o-mini'),
  checkFeatureAccess: jest.fn().mockReturnValue(true),
  getUserSubscription: jest.fn().mockResolvedValue({
    tier: 'standard',
    status: 'active'
  })
}));

// Mock AI client
jest.mock('@/lib/ai/vercel-ai-client', () => ({
  vercelAIClient: {
    chatCompletion: jest.fn(),
    streamChatCompletion: jest.fn()
  },
  VercelAIConfig: {
    model: 'gpt-4o-mini',
    temperature: 0.7
  },
  StreamingOptions: {}
}));

// Mock logger
jest.mock('@/lib/services/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock OpenAI
jest.mock('openai', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue({
            choices: [{
              message: {
                content: 'Test response',
                role: 'assistant'
              },
              finish_reason: 'stop'
            }],
            usage: {
              prompt_tokens: 100,
              completion_tokens: 50,
              total_tokens: 150
            }
          })
        }
      }
    }))
  };
});

// Add global fetch for Node.js environment
if (typeof global.fetch === 'undefined') {
  global.fetch = jest.fn();
}

export {};