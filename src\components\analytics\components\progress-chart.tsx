'use client'

import { memo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { LineChart, Line, BarChart, Bar, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'
import { Skeleton } from '@/components/ui/skeleton'

interface ProgressChartProps {
  title: string
  data: Array<{
    date: string
    [key: string]: string | number
  }>
  lines?: Array<{
    dataKey: string
    color: string
    name: string
  }>
  type?: 'line' | 'bar' | 'area'
  height?: number
  loading?: boolean
  showLegend?: boolean
  yAxisLabel?: string
}

export const ProgressChart = memo(function ProgressChart({
  title,
  data,
  lines = [{ dataKey: 'value', color: 'hsl(var(--primary))', name: 'Value' }],
  type = 'line',
  height = 300,
  loading = false,
  showLegend = false,
  yAxisLabel
}: ProgressChartProps) {
  const [chartType, setChartType] = useState(type)

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="w-full" style={{ height }} />
        </CardContent>
      </Card>
    )
  }

  // Check if we have meaningful data
  const hasData = data.length > 0 && data.some(item => 
    lines.some(line => (item[line.dataKey] as number) > 0)
  )
  
  if (!hasData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-6 sm:py-8 lg:py-10">
          <div className="text-muted-foreground">
            <p className="text-lg mb-2">No data available</p>
            <p className="text-sm">
              Data will appear here once you start writing.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const ChartComponent = chartType === 'line' 
    ? LineChart 
    : chartType === 'bar' 
    ? BarChart 
    : AreaChart

  const DataComponent = chartType === 'line'
    ? Line
    : chartType === 'bar'
    ? Bar
    : Area

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          <Select value={chartType} onValueChange={(value: 'line' | 'bar' | 'area') => setChartType(value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="line">Line</SelectItem>
              <SelectItem value="bar">Bar</SelectItem>
              <SelectItem value="area">Area</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <ChartComponent data={data}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              className="text-xs"
              tick={{ fill: 'hsl(var(--muted-foreground))' }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fill: 'hsl(var(--muted-foreground))' }}
              label={yAxisLabel ? { 
                value: yAxisLabel, 
                angle: -90, 
                position: 'insideLeft',
                style: { fill: 'hsl(var(--muted-foreground))' }
              } : undefined}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'hsl(var(--card))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '6px'
              }}
              labelStyle={{ color: 'hsl(var(--foreground))' }}
            />
            {showLegend && <Legend />}
            {lines.map((line) => (
              <DataComponent
                key={line.dataKey}
                type="monotone"
                dataKey={line.dataKey}
                stroke={line.color}
                fill={line.color}
                strokeWidth={2}
                name={line.name}
                fillOpacity={chartType === 'area' ? 0.3 : 1}
              />
            ))}
          </ChartComponent>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
})