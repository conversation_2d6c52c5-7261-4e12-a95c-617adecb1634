# Supabase Integration Documentation

## Overview

Supabase serves as BookScribe's complete backend platform, providing PostgreSQL database, real-time subscriptions, authentication, file storage, and edge functions. This documentation covers all aspects of the Supabase integration.

## Integration Architecture

### System Overview

```mermaid
graph TB
    subgraph "BookScribe Application"
        Client[Next.js Client]
        API[API Routes]
        Services[Service Layer]
        Auth[Auth System]
    end
    
    subgraph "Supabase Services"
        Database[(PostgreSQL)]
        Realtime[Realtime]
        Storage[Storage]
        EdgeFunc[Edge Functions]
        AuthService[Auth Service]
    end
    
    subgraph "Security Layer"
        RLS[Row Level Security]
        Policies[RLS Policies]
        JWT[JWT Tokens]
    end
    
    Client --> AuthService
    API --> Database
    Services --> Database
    Services --> Realtime
    Client --> Realtime
    API --> Storage
    
    Database --> RLS
    RLS --> Policies
    AuthService --> JWT
```

## Configuration

### Environment Variables
```bash
# Required
NEXT_PUBLIC_SUPABASE_URL=https://[project-id].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...
SUPABASE_SERVICE_ROLE_KEY=eyJ...

# Optional
SUPABASE_JWT_SECRET=your-jwt-secret
NEXT_PUBLIC_SUPABASE_STORAGE_URL=https://[project-id].supabase.co/storage/v1
```

### Client Configuration
```typescript
// lib/supabase/client.ts
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

export function createSupabaseClient() {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true
      },
      global: {
        headers: {
          'x-application-name': 'bookscribe'
        }
      }
    }
  );
}

// Server client with service role
export function createServiceClient() {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false
      }
    }
  );
}
```

## Database Integration

### Schema Overview
```sql
-- Core tables with RLS
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  title TEXT NOT NULL,
  description TEXT,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE chapters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_number INTEGER NOT NULL,
  title TEXT,
  content TEXT,
  word_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(project_id, chapter_number)
);

-- Enable RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;
```

### Row Level Security (RLS)
```sql
-- Projects policies
CREATE POLICY "Users can view own projects" ON projects
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create own projects" ON projects
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own projects" ON projects
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete own projects" ON projects
  FOR DELETE USING (user_id = auth.uid());

-- Collaboration policies
CREATE POLICY "Collaborators can view projects" ON projects
  FOR SELECT USING (
    user_id = auth.uid() OR
    id IN (
      SELECT project_id FROM project_collaborators
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );
```

### Database Functions
```sql
-- Function to update word counts
CREATE OR REPLACE FUNCTION update_chapter_word_count()
RETURNS TRIGGER AS $$
BEGIN
  NEW.word_count := array_length(
    string_to_array(NEW.content, ' '), 
    1
  );
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_word_count
  BEFORE INSERT OR UPDATE OF content ON chapters
  FOR EACH ROW
  EXECUTE FUNCTION update_chapter_word_count();

-- Function to track project activity
CREATE OR REPLACE FUNCTION track_project_activity()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE projects 
  SET updated_at = NOW() 
  WHERE id = NEW.project_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Real-time Subscriptions

### Setting Up Subscriptions
```typescript
class RealtimeManager {
  private subscriptions = new Map<string, RealtimeChannel>();
  
  subscribeToProject(projectId: string, callbacks: {
    onChapterUpdate?: (chapter: Chapter) => void;
    onCollaboratorJoin?: (user: User) => void;
    onPresenceChange?: (presence: PresenceState) => void;
  }) {
    const channel = supabase.channel(`project:${projectId}`)
      // Database changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chapters',
          filter: `project_id=eq.${projectId}`
        },
        (payload) => {
          if (callbacks.onChapterUpdate) {
            callbacks.onChapterUpdate(payload.new as Chapter);
          }
        }
      )
      // Presence tracking
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        if (callbacks.onPresenceChange) {
          callbacks.onPresenceChange(state);
        }
      })
      // Broadcast events
      .on('broadcast', { event: 'cursor' }, (payload) => {
        this.handleCursorUpdate(payload);
      })
      .subscribe();
    
    this.subscriptions.set(projectId, channel);
    return channel;
  }
  
  unsubscribe(projectId: string) {
    const channel = this.subscriptions.get(projectId);
    if (channel) {
      supabase.removeChannel(channel);
      this.subscriptions.delete(projectId);
    }
  }
}
```

### Presence & Collaboration
```typescript
interface CollaborationPresence {
  userId: string;
  userName: string;
  cursorPosition?: {
    chapter: number;
    offset: number;
  };
  selection?: {
    start: number;
    end: number;
  };
  status: 'active' | 'idle' | 'away';
  lastSeen: Date;
}

class CollaborationManager {
  async trackPresence(
    projectId: string,
    presence: CollaborationPresence
  ) {
    const channel = supabase.channel(`project:${projectId}`);
    
    await channel.track({
      user: presence,
      online_at: new Date().toISOString()
    });
  }
  
  broadcastCursor(projectId: string, cursor: CursorPosition) {
    const channel = supabase.channel(`project:${projectId}`);
    
    channel.send({
      type: 'broadcast',
      event: 'cursor',
      payload: {
        userId: this.userId,
        cursor
      }
    });
  }
}
```

## Authentication

### Auth Configuration
```typescript
interface AuthConfig {
  providers: ['email', 'google', 'github'];
  redirectTo: string;
  
  email: {
    confirmationRequired: true;
    passwordMinLength: 8;
    passwordRequirements: {
      minLength: 8;
      requireUppercase: true;
      requireNumbers: true;
      requireSpecialChars: true;
    };
  };
  
  session: {
    expiryMargin: 60; // seconds
    autoRefresh: true;
  };
}
```

### Auth Implementation
```typescript
class SupabaseAuth {
  // Sign up
  async signUp(email: string, password: string, metadata?: UserMetadata) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    });
    
    if (error) throw error;
    return data;
  }
  
  // Sign in
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) throw error;
    return data;
  }
  
  // OAuth sign in
  async signInWithProvider(provider: 'google' | 'github') {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        scopes: provider === 'github' ? 'read:user user:email' : undefined
      }
    });
    
    if (error) throw error;
    return data;
  }
  
  // Session management
  async getSession() {
    const { data: { session } } = await supabase.auth.getSession();
    return session;
  }
  
  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }
}
```

### Auth Middleware
```typescript
// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });
  
  const {
    data: { session },
  } = await supabase.auth.getSession();
  
  // Protected routes
  if (!session && req.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/login', req.url));
  }
  
  // Authenticated users shouldn't see auth pages
  if (session && req.nextUrl.pathname.startsWith('/login')) {
    return NextResponse.redirect(new URL('/dashboard', req.url));
  }
  
  return res;
}
```

## Storage Integration

### Storage Configuration
```typescript
const STORAGE_BUCKETS = {
  exports: {
    name: 'exports',
    public: false,
    fileSizeLimit: 50 * 1024 * 1024, // 50MB
    allowedMimeTypes: ['application/pdf', 'application/epub+zip', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  },
  covers: {
    name: 'book-covers',
    public: true,
    fileSizeLimit: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp']
  },
  references: {
    name: 'reference-materials',
    public: false,
    fileSizeLimit: 20 * 1024 * 1024, // 20MB
    allowedMimeTypes: ['application/pdf', 'image/*', 'text/plain']
  }
};
```

### File Upload Implementation
```typescript
class StorageManager {
  async uploadFile(
    bucket: string,
    path: string,
    file: File,
    options?: UploadOptions
  ): Promise<StorageFile> {
    // Validate file
    this.validateFile(bucket, file);
    
    // Generate unique path
    const uniquePath = this.generateUniquePath(path, file.name);
    
    // Upload file
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(uniquePath, file, {
        cacheControl: '3600',
        upsert: options?.upsert || false,
        contentType: file.type
      });
    
    if (error) throw error;
    
    // Get public URL if needed
    if (STORAGE_BUCKETS[bucket].public) {
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(uniquePath);
      
      return { ...data, publicUrl };
    }
    
    return data;
  }
  
  async getSignedUrl(
    bucket: string,
    path: string,
    expiresIn = 3600
  ): Promise<string> {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn);
    
    if (error) throw error;
    return data.signedUrl;
  }
  
  async deleteFile(bucket: string, paths: string[]): Promise<void> {
    const { error } = await supabase.storage
      .from(bucket)
      .remove(paths);
    
    if (error) throw error;
  }
}
```

## Edge Functions

### Function Deployment
```typescript
// supabase/functions/generate-export/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

serve(async (req: Request) => {
  try {
    const { projectId, format } = await req.json();
    
    // Create Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );
    
    // Fetch project data
    const { data: project } = await supabase
      .from('projects')
      .select('*, chapters(*)')
      .eq('id', projectId)
      .single();
    
    // Generate export based on format
    const exportData = await generateExport(project, format);
    
    // Store in storage
    const { data: upload } = await supabase.storage
      .from('exports')
      .upload(`${projectId}/${Date.now()}.${format}`, exportData);
    
    return new Response(
      JSON.stringify({ success: true, url: upload.path }),
      { headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    );
  }
});
```

### Function Invocation
```typescript
class EdgeFunctionClient {
  async invokeFunction<T = any>(
    functionName: string,
    payload: any
  ): Promise<T> {
    const { data, error } = await supabase.functions.invoke(functionName, {
      body: payload,
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    if (error) throw error;
    return data;
  }
}
```

## Database Types

### Type Generation
```bash
# Generate TypeScript types from database
npx supabase gen types typescript --project-id [project-id] > types/database.types.ts
```

### Type-Safe Queries
```typescript
import type { Database } from '@/types/database.types';

type Project = Database['public']['Tables']['projects']['Row'];
type InsertProject = Database['public']['Tables']['projects']['Insert'];
type UpdateProject = Database['public']['Tables']['projects']['Update'];

class ProjectRepository {
  async getProject(id: string): Promise<Project | null> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }
  
  async createProject(project: InsertProject): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .insert(project)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
}
```

## Performance Optimization

### Query Optimization
```typescript
// Efficient data fetching with select
const { data: projects } = await supabase
  .from('projects')
  .select(`
    id,
    title,
    chapters!inner (
      id,
      title,
      word_count
    )
  `)
  .eq('user_id', userId)
  .order('created_at', { ascending: false })
  .limit(10);

// Use database functions for complex operations
const { data: stats } = await supabase
  .rpc('calculate_project_stats', { project_id: projectId });
```

### Connection Pooling
```typescript
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    db: {
      pooling: {
        max: 10,
        min: 2,
        acquireTimeoutMs: 30000
      }
    }
  }
);
```

## Monitoring & Debugging

### Query Logging
```typescript
if (process.env.NODE_ENV === 'development') {
  supabase.channel('debug').on('*', (payload) => {
    console.log('Supabase Event:', payload);
  }).subscribe();
}
```

### Performance Monitoring
```sql
-- Monitor slow queries
CREATE OR REPLACE FUNCTION log_slow_queries()
RETURNS event_trigger AS $$
DECLARE
  query_text text;
  duration interval;
BEGIN
  -- Log queries taking longer than 1 second
  IF current_setting('log_min_duration_statement')::interval > interval '1 second' THEN
    RAISE LOG 'Slow query detected: % (duration: %)', 
      current_query(), 
      clock_timestamp() - statement_timestamp();
  END IF;
END;
$$ LANGUAGE plpgsql;
```

## Best Practices

### Security
1. Always use RLS for data protection
2. Validate data on both client and server
3. Use service role key only on server
4. Implement rate limiting for API calls
5. Audit sensitive operations

### Performance
1. Use select to fetch only needed columns
2. Implement proper indexes
3. Use database functions for complex logic
4. Cache frequently accessed data
5. Monitor query performance

### Development
1. Use TypeScript types from database
2. Handle errors gracefully
3. Implement retry logic for network errors
4. Use transactions for related operations
5. Test RLS policies thoroughly

## Troubleshooting

### Common Issues

1. **RLS Policy Violations**
   - Check auth.uid() in policies
   - Verify user permissions
   - Test with service role key

2. **Real-time Not Working**
   - Check table has replica identity
   - Verify RLS policies allow SELECT
   - Check WebSocket connection

3. **Storage Upload Failures**
   - Verify bucket policies
   - Check file size limits
   - Validate MIME types

4. **Auth Session Issues**
   - Check token expiration
   - Verify redirect URLs
   - Test refresh token flow

## Future Enhancements

### Planned Features
1. **Vector Search**: AI-powered content search
2. **Database Webhooks**: External integrations
3. **Branching**: Database branching for testing
4. **Advanced RLS**: Dynamic policy generation
5. **Performance Insights**: Query optimization AI