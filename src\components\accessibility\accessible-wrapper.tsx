/**
 * Accessible Component Wrappers
 * Provides enhanced accessibility features for common UI patterns
 */

import React, { forwardRef } from 'react';
import { Button as BaseButton, ButtonProps } from '@/components/ui/button';
import { Input as BaseInput } from '@/components/ui/input';
import { Select as BaseSelect, SelectProps } from '@/components/ui/select';
import { Textarea as BaseTextarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { getAriaLabel } from '@/lib/accessibility/aria-labels';

// Accessible Button
interface AccessibleButtonProps extends ButtonProps {
  label?: string;
  ariaLabel?: string;
  ariaPressed?: boolean;
  ariaExpanded?: boolean;
  ariaControls?: string;
  ariaDescribedBy?: string;
  loading?: boolean;
}

export const AccessibleButton = forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  ({ 
    label, 
    ariaLabel, 
    ariaPressed,
    ariaExpanded,
    ariaControls,
    ariaDescribedBy,
    loading,
    disabled,
    children,
    className,
    ...props 
  }, ref) => {
    const effectiveAriaLabel = ariaLabel || label || (typeof children === 'string' ? children : undefined);
    
    return (
      <BaseButton
        ref={ref}
        aria-label={effectiveAriaLabel}
        aria-pressed={ariaPressed}
        aria-expanded={ariaExpanded}
        aria-controls={ariaControls}
        aria-describedby={ariaDescribedBy}
        aria-busy={loading}
        disabled={disabled || loading}
        className={className}
        {...props}
      >
        {loading && <span className="sr-only">Loading...</span>}
        {children}
      </BaseButton>
    );
  }
);
AccessibleButton.displayName = 'AccessibleButton';

// Accessible Input
interface AccessibleInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  hint?: string;
  required?: boolean;
}

export const AccessibleInput = forwardRef<HTMLInputElement, AccessibleInputProps>(
  ({ label, error, hint, required, className, id, ...props }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = error ? `${inputId}-error` : undefined;
    const hintId = hint ? `${inputId}-hint` : undefined;
    
    return (
      <div className="space-y-2">
        {label && (
          <label htmlFor={inputId} className="text-sm font-medium">
            {label}
            {required && <span className="text-destructive ml-1" aria-label="required">*</span>}
          </label>
        )}
        <BaseInput
          ref={ref}
          id={inputId}
          aria-label={!label ? props['aria-label'] || props.placeholder : undefined}
          aria-invalid={!!error}
          aria-describedby={cn(errorId, hintId)}
          aria-required={required}
          className={cn(
            error && 'border-destructive focus:ring-destructive',
            className
          )}
          {...props}
        />
        {hint && !error && (
          <p id={hintId} className="text-sm text-muted-foreground">
            {hint}
          </p>
        )}
        {error && (
          <p id={errorId} className="text-sm text-destructive" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  }
);
AccessibleInput.displayName = 'AccessibleInput';

// Accessible Textarea
interface AccessibleTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  hint?: string;
  characterCount?: boolean;
  maxCharacters?: number;
}

export const AccessibleTextarea = forwardRef<HTMLTextAreaElement, AccessibleTextareaProps>(
  ({ label, error, hint, characterCount, maxCharacters, className, id, ...props }, ref) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = error ? `${textareaId}-error` : undefined;
    const hintId = hint ? `${textareaId}-hint` : undefined;
    const countId = characterCount ? `${textareaId}-count` : undefined;
    const currentLength = props.value ? String(props.value).length : 0;
    
    return (
      <div className="space-y-2">
        {label && (
          <label htmlFor={textareaId} className="text-sm font-medium">
            {label}
            {props.required && <span className="text-destructive ml-1" aria-label="required">*</span>}
          </label>
        )}
        <BaseTextarea
          ref={ref}
          id={textareaId}
          aria-label={!label ? props['aria-label'] || props.placeholder : undefined}
          aria-invalid={!!error}
          aria-describedby={cn(errorId, hintId, countId)}
          aria-required={props.required}
          className={cn(
            error && 'border-destructive focus:ring-destructive',
            className
          )}
          {...props}
        />
        {hint && !error && (
          <p id={hintId} className="text-sm text-muted-foreground">
            {hint}
          </p>
        )}
        {error && (
          <p id={errorId} className="text-sm text-destructive" role="alert">
            {error}
          </p>
        )}
        {characterCount && (
          <p id={countId} className="text-sm text-muted-foreground text-right">
            <span aria-live="polite">
              {currentLength}{maxCharacters && ` / ${maxCharacters}`} characters
            </span>
          </p>
        )}
      </div>
    );
  }
);
AccessibleTextarea.displayName = 'AccessibleTextarea';

// Accessible Icon Button
interface AccessibleIconButtonProps extends AccessibleButtonProps {
  icon: React.ReactNode;
  screenReaderText: string;
}

export const AccessibleIconButton = forwardRef<HTMLButtonElement, AccessibleIconButtonProps>(
  ({ icon, screenReaderText, ...props }, ref) => {
    return (
      <AccessibleButton
        ref={ref}
        ariaLabel={screenReaderText}
        {...props}
      >
        {icon}
        <span className="sr-only">{screenReaderText}</span>
      </AccessibleButton>
    );
  }
);
AccessibleIconButton.displayName = 'AccessibleIconButton';

// Accessible Loading Spinner
interface AccessibleSpinnerProps {
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function AccessibleSpinner({ 
  label = 'Loading...', 
  size = 'md',
  className 
}: AccessibleSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };
  
  return (
    <div 
      role="status" 
      aria-label={label}
      className={cn('inline-flex items-center', className)}
    >
      <svg
        className={cn('animate-spin', sizeClasses[size])}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      <span className="sr-only">{label}</span>
    </div>
  );
}

// Accessible Progress Bar
interface AccessibleProgressProps {
  value: number;
  max?: number;
  label?: string;
  showValue?: boolean;
  className?: string;
}

export function AccessibleProgress({
  value,
  max = 100,
  label,
  showValue = true,
  className
}: AccessibleProgressProps) {
  const percentage = Math.round((value / max) * 100);
  
  return (
    <div className={cn('space-y-1', className)}>
      {(label || showValue) && (
        <div className="flex justify-between text-sm">
          {label && <span>{label}</span>}
          {showValue && <span>{percentage}%</span>}
        </div>
      )}
      <div
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={label || 'Progress'}
        className="h-2 w-full bg-secondary rounded-full overflow-hidden"
      >
        <div
          className="h-full bg-primary transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}

// Skip to Content Link
export function SkipToContent({ href = '#main-content' }: { href?: string }) {
  return (
    <a
      href={href}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-background focus:text-foreground focus:rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
    >
      Skip to main content
    </a>
  );
}

// Accessible Alert
interface AccessibleAlertProps {
  type?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  children: React.ReactNode;
  onClose?: () => void;
  className?: string;
}

export function AccessibleAlert({
  type = 'info',
  title,
  children,
  onClose,
  className
}: AccessibleAlertProps) {
  const roleMap = {
    info: 'status',
    success: 'status',
    warning: 'alert',
    error: 'alert'
  };
  
  const ariaLiveMap = {
    info: 'polite',
    success: 'polite',
    warning: 'polite',
    error: 'assertive'
  };
  
  return (
    <div
      role={roleMap[type] as 'status' | 'alert'}
      aria-live={ariaLiveMap[type] as 'polite' | 'assertive'}
      className={cn(
        'relative rounded-lg border p-4',
        {
          'bg-info-light border-blue-200 text-blue-800': type === 'info',
          'bg-success-light border-green-200 text-green-800': type === 'success',
          'bg-warning-light border-yellow-200 text-yellow-800': type === 'warning',
          'bg-error-light border-red-200 text-red-800': type === 'error',
        },
        className
      )}
    >
      {title && (
        <h3 className="font-medium mb-1">{title}</h3>
      )}
      <div>{children}</div>
      {onClose && (
        <button
          onClick={onClose}
          aria-label="Close alert"
          className="absolute top-2 right-2 p-1 rounded hover:bg-black/10"
        >
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
}