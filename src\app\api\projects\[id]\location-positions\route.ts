import { NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

// Validation schemas
const locationPositionSchema = z.object({
  locationId: z.string().uuid(),
  x: z.number(),
  y: z.number(),
  zIndex: z.number().optional(),
  markerStyle: z.object({
    color: z.string().optional(),
    icon: z.string().optional(),
    size: z.enum(['small', 'medium', 'large']).optional(),
    shape: z.enum(['circle', 'square', 'pin', 'star']).optional()
  }).optional(),
  isDefault: z.boolean().optional()
});

const batchUpdateSchema = z.object({
  positions: z.array(locationPositionSchema),
  clearExisting: z.boolean().optional()
});

// GET endpoint - Load location positions
export const GET = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: projectId } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const includeDefaults = searchParams.get('includeDefaults') === 'true';

    // Build query
    let query = supabase
      .from('location_positions')
      .select(`
        id,
        location_id,
        user_id,
        x_coordinate,
        y_coordinate,
        z_index,
        marker_style,
        is_default,
        updated_at,
        locations!inner(
          id,
          name,
          location_type
        )
      `)
      .eq('project_id', projectId);

    if (includeDefaults) {
      // Get user's positions and default positions
      query = query.or(`user_id.eq.${user.id},is_default.eq.true`);
    } else {
      // Only get user's positions
      query = query.eq('user_id', user.id);
    }

    const { data: positions, error } = await query;

    if (error) {
      logger.error('Error fetching location positions:', error);
      throw error;
    }

    // Transform to API format
    const response = positions?.map(pos => ({
      id: pos.id,
      locationId: pos.location_id,
      locationName: pos.locations.name,
      locationType: pos.locations.location_type,
      x: pos.x_coordinate,
      y: pos.y_coordinate,
      zIndex: pos.z_index,
      markerStyle: pos.marker_style || {},
      isDefault: pos.is_default,
      isUserPosition: pos.user_id === user.id,
      updatedAt: pos.updated_at
    })) || [];

    return UnifiedResponse.success({
      positions: response,
      total: response.length
    });
  } catch (error) {
    logger.error('Error in location positions GET:', error);
    return UnifiedResponse.error({
      message: 'Failed to fetch location positions',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

// POST endpoint - Save location positions (batch)
export const POST = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: projectId } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Parse and validate request body
    const body = await request.json();
    const validationResult = batchUpdateSchema.safeParse(body);

    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid position data',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400);
    }

    const { positions, clearExisting } = validationResult.data;

    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    let canSetDefaults = false;
    if (!projectAccess) {
      // Check for collaborator access
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('role')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single();

      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403);
      }

      // Only admins and project owners can set default positions
      canSetDefaults = collaboratorAccess.role === 'admin';
    } else {
      // Project owner can set defaults
      canSetDefaults = true;
    }

    // Clear existing positions if requested
    if (clearExisting) {
      const { error: deleteError } = await supabase
        .from('location_positions')
        .delete()
        .eq('project_id', projectId)
        .eq('user_id', user.id);

      if (deleteError) {
        logger.error('Error clearing existing positions:', deleteError);
      }
    }

    // Transform and prepare positions for insert
    const positionsToInsert = positions.map(pos => ({
      location_id: pos.locationId,
      user_id: user.id,
      project_id: projectId,
      x_coordinate: pos.x,
      y_coordinate: pos.y,
      z_index: pos.zIndex || 0,
      marker_style: pos.markerStyle || {},
      is_default: canSetDefaults && pos.isDefault === true
    }));

    // Upsert positions
    const { data: inserted, error } = await supabase
      .from('location_positions')
      .upsert(positionsToInsert, {
        onConflict: 'location_id,user_id'
      })
      .select();

    if (error) {
      logger.error('Error saving location positions:', error);
      throw error;
    }

    return UnifiedResponse.success({
      saved: inserted?.length || 0,
      positions: inserted
    });
  } catch (error) {
    logger.error('Error in location positions POST:', error);
    return UnifiedResponse.error({
      message: 'Failed to save location positions',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

// PUT endpoint - Update single location position
export const PUT = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: projectId } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Parse and validate request body
    const body = await request.json();
    const validationResult = locationPositionSchema.safeParse(body);

    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid position data',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400);
    }

    const position = validationResult.data;

    // Check if user can set defaults
    let canSetDefaults = false;
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', projectId)
      .single();

    if (project?.user_id === user.id) {
      canSetDefaults = true;
    } else {
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('role')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single();

      canSetDefaults = collaborator?.role === 'admin';
    }

    // Upsert position
    const { data: updated, error } = await supabase
      .from('location_positions')
      .upsert({
        location_id: position.locationId,
        user_id: user.id,
        project_id: projectId,
        x_coordinate: position.x,
        y_coordinate: position.y,
        z_index: position.zIndex || 0,
        marker_style: position.markerStyle || {},
        is_default: canSetDefaults && position.isDefault === true
      }, {
        onConflict: 'location_id,user_id'
      })
      .select()
      .single();

    if (error) {
      logger.error('Error updating location position:', error);
      throw error;
    }

    return UnifiedResponse.success({
      position: {
        id: updated.id,
        locationId: updated.location_id,
        x: updated.x_coordinate,
        y: updated.y_coordinate,
        zIndex: updated.z_index,
        markerStyle: updated.marker_style,
        isDefault: updated.is_default
      }
    });
  } catch (error) {
    logger.error('Error in location position PUT:', error);
    return UnifiedResponse.error({
      message: 'Failed to update location position',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});

// DELETE endpoint - Delete location position
export const DELETE = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: projectId } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Get location ID from query params
    const searchParams = request.nextUrl.searchParams;
    const locationId = searchParams.get('locationId');

    if (!locationId) {
      return UnifiedResponse.error({
        message: 'Location ID is required',
        code: 'VALIDATION_ERROR'
      }, undefined, 400);
    }

    // Delete position
    const { error } = await supabase
      .from('location_positions')
      .delete()
      .eq('location_id', locationId)
      .eq('user_id', user.id)
      .eq('project_id', projectId);

    if (error) {
      logger.error('Error deleting location position:', error);
      throw error;
    }

    return UnifiedResponse.success({
      message: 'Location position deleted successfully'
    });
  } catch (error) {
    logger.error('Error in location position DELETE:', error);
    return UnifiedResponse.error({
      message: 'Failed to delete location position',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});