'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Sun } from 'lucide-react'
import dynamic from 'next/dynamic'

// Dynamically import the actual theme toggle to prevent SSR issues
const ThemeToggle = dynamic(
  () => import('./theme-toggle').then(mod => ({ default: mod.ThemeToggle })),
  { 
    ssr: false,
    loading: () => <ThemeTogglePlaceholder />
  }
)

// Placeholder that matches the actual button size to prevent layout shift
function ThemeTogglePlaceholder() {
  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-9 w-9"
      aria-label="Loading theme toggle"
      disabled
    >
      <Sun className="h-4 w-4" />
    </Button>
  )
}

export function ClientThemeToggle() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // During SSR and initial hydration, show placeholder
  if (!mounted) {
    return <ThemeTogglePlaceholder />
  }

  // After hydration, render the actual theme toggle
  return <ThemeToggle />
}