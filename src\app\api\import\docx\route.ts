import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'
import {
  validateFileComprehensive,
  sanitizeTextInput
} from '@/lib/file-upload-security'
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'
// mammoth will be imported dynamically

interface ParsedChapter {
  title: string
  content: string
  wordCount: number
  chapterNumber: number
}

// Maximum file size: 10MB
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Validation schema for form data
const importFormSchema = z.object({
  projectId: baseSchemas.uuid,
  parseChapters: z.enum(['true', 'false']).transform(val => val === 'true').optional().default('true')
});

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  // Enhanced request validation for file uploads
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'file-upload',
    rateLimitCost: 5,
    maxBodySize: MAX_FILE_SIZE,
    allowedContentTypes: ['multipart/form-data'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      try {
        const formData = await req.formData();
        const projectId = formData.get('projectId') as string;
        
        if (!projectId) {
          return { valid: false, error: 'Project ID is required' };
        }

        // Validate project ID format
        if (!baseSchemas.uuid.safeParse(projectId).success) {
          return { valid: false, error: 'Invalid project ID format' };
        }

        // Verify user owns the project
        const project = await verifyProjectAccess(projectId, user.id)
        if (!project) {
          return { valid: false, error: PROJECT_ACCESS_ERROR };
        }

        // Check if project is in a valid state for import
        if (project.status === 'archived') {
          return { valid: false, error: 'Cannot import to archived project' };
        }

        return { valid: true };
      } catch (error) {
        return { valid: false, error: 'Invalid form data' };
      }
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const parseChapters = formData.get('parseChapters') === 'true';

    // Validate form data
    const formDataObj = {
      projectId,
      parseChapters: formData.get('parseChapters') || 'true'
    };

    const validatedFormData = importFormSchema.parse(formDataObj);

    if (!file) {
      return UnifiedResponse.error('File is required', 400);
    }

    // Log import attempt
    logger.info('DOCX import initiated', {
      userId: user.id,
      projectId: validatedFormData.projectId,
      fileName: file.name,
      fileSize: file.size,
      parseChapters: validatedFormData.parseChapters,
      clientIP: context.clientIP
    });

    // Validate file type and security
    const fileValidation = await validateFileComprehensive(file);
    if (!fileValidation.isValid) {
      logger.warn('File validation failed', {
        userId: user.id,
        fileName: file.name,
        error: fileValidation.error,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error(fileValidation.error || 'Invalid file', 400);
    }

    // Ensure it's a DOCX file
    const validMimeTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];
    
    if (!validMimeTypes.includes(file.type)) {
      logger.warn('Invalid file type for DOCX import', {
        userId: user.id,
        fileName: file.name,
        mimeType: file.type,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('File must be a DOCX document', 400);
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return UnifiedResponse.error(`File size exceeds maximum limit of ${MAX_FILE_SIZE / 1024 / 1024}MB`, 400);
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extract text from Word document (server-side only)
    if (typeof window !== 'undefined') {
      throw new Error('DOCX parsing is only available on server-side');
    }
    
    const mammoth = (await import('mammoth')).default;
    const result = await mammoth.extractRawText({ buffer });
    const text = result.value;

    // Validate extracted text
    if (!text || text.trim().length === 0) {
      return UnifiedResponse.error('No text content found in document', 400);
    }

    // Check for suspicious content
    if (RequestValidationMiddleware['detectMaliciousContent'](text)) {
      logger.warn('Malicious content detected in DOCX', {
        userId: user.id,
        fileName: file.name,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Document contains potentially malicious content', 400);
    }

    // Parse chapters if requested
    let chapters: ParsedChapter[] = [];
    
    if (validatedFormData.parseChapters) {
      chapters = parseChaptersFromText(text);
    } else {
      // Treat entire document as one chapter
      chapters = [{
        title: sanitizeTextInput(file.name.replace(/\.(docx?|doc)$/i, ''), 100),
        content: text,
        wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
        chapterNumber: 1
      }];
    }

    // Create chapters in database
    const supabase = await createTypedServerClient();
    const createdChapters = [];
    const errors = [];

    for (const chapter of chapters) {
      try {
        const { data, error } = await supabase
          .from('chapters')
          .insert({
            project_id: validatedFormData.projectId,
            chapter_number: chapter.chapterNumber,
            title: chapter.title,
            content: chapter.content,
            actual_word_count: chapter.wordCount,
            status: 'imported',
            created_by: user.id
          })
          .select()
          .single();

        if (error) {
          logger.error('Error creating chapter:', error, {
            userId: user.id,
            projectId: validatedFormData.projectId,
            chapterNumber: chapter.chapterNumber
          });
          errors.push(`Chapter ${chapter.chapterNumber}: ${error.message}`);
          continue;
        }

        createdChapters.push(data);
      } catch (error) {
        logger.error('Unexpected error creating chapter:', error, {
          userId: user.id,
          projectId: validatedFormData.projectId,
          chapterNumber: chapter.chapterNumber
        });
        errors.push(`Chapter ${chapter.chapterNumber}: Failed to create`);
      }
    }

    // Update project word count
    const totalWordCount = chapters.reduce((sum, ch) => sum + ch.wordCount, 0);
    const { error: updateError } = await supabase
      .from('projects')
      .update({
        word_count: totalWordCount,
        chapters_count: chapters.length,
        updated_at: new Date().toISOString()
      })
      .eq('id', validatedFormData.projectId);

    if (updateError) {
      logger.error('Error updating project word count:', updateError, {
        userId: user.id,
        projectId: validatedFormData.projectId
      });
    }

    logger.info('DOCX import completed', {
      userId: user.id,
      projectId: validatedFormData.projectId,
      chaptersImported: createdChapters.length,
      totalChapters: chapters.length,
      totalWordCount,
      errors: errors.length,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      chaptersImported: createdChapters.length,
      totalWordCount,
      chapters: createdChapters.map(ch => ({
        id: ch.id,
        title: ch.title,
        chapterNumber: ch.chapter_number,
        wordCount: ch.actual_word_count
      })),
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    logger.error('DOCX import error:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    
    return UnifiedResponse.error('Failed to import DOCX file');
  }
});

function parseChaptersFromText(text: string): ParsedChapter[] {
  const chapters: ParsedChapter[] = [];
  
  // Common chapter patterns
  const chapterPatterns = [
    /^Chapter\s+(\d+)(?:\s*[-:]\s*(.*))?$/im,
    /^CHAPTER\s+(\d+)(?:\s*[-:]\s*(.*))?$/im,
    /^Chapter\s+([A-Z]+)(?:\s*[-:]\s*(.*))?$/im,
    /^(\d+)\.?\s+(.*)$/m,
    /^Part\s+(\d+)(?:\s*[-:]\s*(.*))?$/im
  ];

  // Split text by chapter markers
  let currentChapter: ParsedChapter | null = null;
  const lines = text.split('\n');
  let chapterNumber = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]?.trim() || '';
    let isChapterStart = false;
    let title = '';

    // Check if line matches any chapter pattern
    for (const pattern of chapterPatterns) {
      const match = line.match(pattern);
      if (match) {
        isChapterStart = true;
        chapterNumber++;
        title = match[2] || `Chapter ${chapterNumber}`;
        break;
      }
    }

    // Also check for lines that are all caps and short (likely chapter titles)
    if (!isChapterStart && line.length > 0 && line.length < 50 && line === line.toUpperCase()) {
      isChapterStart = true;
      chapterNumber++;
      title = line;
    }

    if (isChapterStart) {
      // Save previous chapter if exists
      if (currentChapter && currentChapter.content.trim()) {
        chapters.push(currentChapter);
      }

      // Start new chapter
      currentChapter = {
        title: sanitizeTextInput(title.trim(), 100),
        content: '',
        wordCount: 0,
        chapterNumber
      };
    } else if (currentChapter) {
      // Add line to current chapter
      currentChapter.content += line + '\n';
    } else if (!currentChapter && line.trim()) {
      // No chapter marker found yet, create a default first chapter
      chapterNumber = 1;
      currentChapter = {
        title: 'Chapter 1',
        content: line + '\n',
        wordCount: 0,
        chapterNumber
      };
    }
  }

  // Add the last chapter
  if (currentChapter && currentChapter.content.trim()) {
    chapters.push(currentChapter);
  }

  // Calculate word counts and sanitize content
  for (const chapter of chapters) {
    chapter.content = sanitizeTextInput(chapter.content.trim(), 1000000); // 1MB max per chapter
    chapter.wordCount = chapter.content.split(/\s+/).filter(word => word.length > 0).length;
  }

  // If no chapters found, return the entire text as one chapter
  if (chapters.length === 0) {
    chapters.push({
      title: 'Imported Content',
      content: sanitizeTextInput(text, 1000000),
      wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
      chapterNumber: 1
    });
  }

  return chapters;
}