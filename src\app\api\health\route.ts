import { NextResponse } from 'next/server'
import { handleAPIError, AuthorizationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { apiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter-unified'
import { config } from '@/lib/config'
import { RATE_LIMITS } from '@/lib/constants'
import { applyRateLimit } from '@/lib/rate-limiter-unified'

export async function GET(request: NextRequest) {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'default' })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

  try {
    // Check if this is a basic health check (for Docker/load balancers)
    const isBasicHealthCheck = request.nextUrl.searchParams.get('basic') === 'true';

    if (isBasicHealthCheck) {
      // Simple health check for Docker/load balancers - no auth required
      return NextResponse.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    }

    // Rate limiting for detailed health checks
    const clientIP = getClientIP(request);
    const rateLimitResult = apiLimiter.check(RATE_LIMITS.SERVICE_ORCHESTRATOR_WRITE, clientIP); // 10 requests per hour

    if (!rateLimitResult.success) {
      return createRateLimitResponse();
    }

    // Authentication required for detailed system health information
    const authResult = await authenticateUser();
    if (!authResult.success) {
      return authResult.response!;
    }

    // Check if user has admin role
    const supabase = await createTypedServerClient()
    const { data: profile } = await supabase
      .from('users')
      .select('subscription_tier')
      .eq('id', authResult.user?.id || '')
      .single()

    // For now, consider 'enterprise' tier users as admins
    // In production, you should have a proper admin role field
    if (profile?.subscription_tier !== 'enterprise') {
      return handleAPIError(new AuthorizationError())
    }

    const startTime = Date.now()
    
    // Check database connection
    const { error: dbError } = await supabase
      .from('projects')
      .select('count')
      .limit(1)
      .single()
    
    const dbLatency = Date.now() - startTime
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: config.build.version || '0.1.0',
      environment: config.build.phase || 'development',
      services: {
        database: {
          status: dbError ? 'unhealthy' : 'healthy',
          latency: dbLatency,
          error: dbError?.message || null,
        },
      },
      uptime: process.uptime(),
    }

    const status = dbError ? 503 : 200
    
    return NextResponse.json(health, { status })
  } catch (error) {
    return handleRouteError(error, 'Health Check')
  }
}