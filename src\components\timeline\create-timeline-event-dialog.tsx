'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Switch } from '@/components/ui/switch'
import { CalendarIcon, X } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import type { TimelineEvent } from '@/components/timeline/timeline-calendar-view'

interface CreateTimelineEventDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onEventCreate?: (event: Omit<TimelineEvent, 'id'>) => Promise<void>
  initialDate?: Date
  initialEvent?: Partial<TimelineEvent>
  projectCharacters?: string[]
  projectLocations?: string[]
}

export function CreateTimelineEventDialog({
  open,
  onOpenChange,
  onEventCreate,
  initialDate,
  initialEvent,
  projectCharacters = [],
  projectLocations = []
}: CreateTimelineEventDialogProps) {
  const [formData, setFormData] = useState({
    type: 'plot' as TimelineEvent['type'],
    title: '',
    description: '',
    date: initialDate || new Date(),
    chapter: undefined as number | undefined,
    scene: undefined as number | undefined,
    characters: [] as string[],
    location: '',
    importance: 'medium' as TimelineEvent['importance'],
    verified: false
  })
  const [loading, setLoading] = useState(false)
  const [newCharacter, setNewCharacter] = useState('')
  const [showDatePicker, setShowDatePicker] = useState(false)

  // Update form when initial data changes
  useEffect(() => {
    if (initialEvent) {
      setFormData(prev => ({
        ...prev,
        ...initialEvent,
        date: initialEvent.date ? new Date(initialEvent.date) : prev.date
      }))
    } else if (initialDate) {
      setFormData(prev => ({ ...prev, date: initialDate }))
    }
  }, [initialEvent, initialDate])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) return
    
    setLoading(true)
    try {
      const eventData: Omit<TimelineEvent, 'id'> = {
        type: formData.type,
        title: formData.title.trim(),
        description: formData.description.trim(),
        date: formData.date.toISOString(),
        chapter: formData.chapter,
        scene: formData.scene,
        characters: formData.characters,
        location: formData.location.trim() || undefined,
        importance: formData.importance,
        verified: formData.verified
      }
      
      await onEventCreate?.(eventData)
      
      // Reset form
      setFormData({
        type: 'plot',
        title: '',
        description: '',
        date: new Date(),
        chapter: undefined,
        scene: undefined,
        characters: [],
        location: '',
        importance: 'medium',
        verified: false
      })
      
      onOpenChange(false)
    } catch (error) {
      console.error('Error creating event:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddCharacter = () => {
    if (newCharacter.trim() && !formData.characters.includes(newCharacter.trim())) {
      setFormData(prev => ({
        ...prev,
        characters: [...prev.characters, newCharacter.trim()]
      }))
      setNewCharacter('')
    }
  }

  const handleRemoveCharacter = (character: string) => {
    setFormData(prev => ({
      ...prev,
      characters: prev.characters.filter(c => c !== character)
    }))
  }

  const handleCharacterFromProject = (character: string) => {
    if (!formData.characters.includes(character)) {
      setFormData(prev => ({
        ...prev,
        characters: [...prev.characters, character]
      }))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Timeline Event</DialogTitle>
          <DialogDescription>
            Add a new event to your story timeline
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Event Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter event title"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="type">Event Type</Label>
              <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as TimelineEvent['type'] }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="plot">Plot Event</SelectItem>
                  <SelectItem value="character">Character Event</SelectItem>
                  <SelectItem value="world">World Event</SelectItem>
                  <SelectItem value="reference">Reference Event</SelectItem>
                  <SelectItem value="deadline">Deadline</SelectItem>
                  <SelectItem value="milestone">Milestone</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date and Location */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Event Date</Label>
              <Popover open={showDatePicker} onOpenChange={setShowDatePicker}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !formData.date && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.date ? format(formData.date, 'PPP') : 'Pick a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.date}
                    onSelect={(date) => {
                      if (date) {
                        setFormData(prev => ({ ...prev, date }))
                        setShowDatePicker(false)
                      }
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="importance">Importance</Label>
              <Select value={formData.importance} onValueChange={(value) => setFormData(prev => ({ ...prev, importance: value as TimelineEvent['importance'] }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what happens in this event"
              rows={3}
            />
          </div>

          {/* Chapter and Scene */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="chapter">Chapter (Optional)</Label>
              <Input
                id="chapter"
                type="number"
                min="1"
                value={formData.chapter || ''}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  chapter: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                placeholder="Chapter number"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="scene">Scene (Optional)</Label>
              <Input
                id="scene"
                type="number"
                min="1"
                value={formData.scene || ''}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  scene: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                placeholder="Scene number"
              />
            </div>
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location (Optional)</Label>
            <div className="flex gap-2">
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                placeholder="Where does this event take place?"
                className="flex-1"
              />
              {projectLocations.length > 0 && (
                <Select onValueChange={(value) => setFormData(prev => ({ ...prev, location: value }))}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="From project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projectLocations.map(location => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          {/* Characters */}
          <div className="space-y-2">
            <Label>Characters Involved</Label>
            
            {/* Selected Characters */}
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.characters.map(character => (
                <Badge key={character} variant="secondary" className="flex items-center gap-1">
                  {character}
                  <X 
                    className="w-3 h-3 cursor-pointer hover:text-destructive" 
                    onClick={() => handleRemoveCharacter(character)}
                  />
                </Badge>
              ))}
            </div>
            
            {/* Add New Character */}
            <div className="flex gap-2">
              <Input
                value={newCharacter}
                onChange={(e) => setNewCharacter(e.target.value)}
                placeholder="Add character name"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddCharacter())}
                className="flex-1"
              />
              <Button type="button" variant="outline" onClick={handleAddCharacter}>
                Add
              </Button>
            </div>
            
            {/* Project Characters */}
            {projectCharacters.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {projectCharacters
                  .filter(char => !formData.characters.includes(char))
                  .map(character => (
                    <Badge 
                      key={character} 
                      variant="outline" 
                      className="cursor-pointer hover:bg-secondary"
                      onClick={() => handleCharacterFromProject(character)}
                    >
                      + {character}
                    </Badge>
                  ))}
              </div>
            )}
          </div>

          {/* Verified Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="verified"
              checked={formData.verified}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, verified: checked }))}
            />
            <Label htmlFor="verified">Mark as verified</Label>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.title.trim()}>
              {loading ? 'Creating...' : 'Create Event'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}