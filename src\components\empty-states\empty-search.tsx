'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Search, 
  SearchX,
  Filter,
  RefreshCw
} from 'lucide-react'

interface EmptySearchProps {
  searchQuery: string
  onClearSearch?: () => void
  onResetFilters?: () => void
  hasFilters?: boolean
}

export function EmptySearch({ 
  searchQuery, 
  onClearSearch,
  onResetFilters,
  hasFilters = false 
}: EmptySearchProps) {
  return (
    <div className="max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto py-12">
      <Card className="border-2 border-dashed border-muted-foreground/20">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
            <SearchX className="w-8 h-8 text-muted-foreground" />
          </div>
          
          <h3 className="text-xl font-medium text-foreground mb-3">
            No results found
          </h3>
          
          <p className="text-muted-foreground mb-6">
            {searchQuery 
              ? `We couldn't find anything matching "${searchQuery}"`
              : "No items match your current filters"
            }
          </p>
          
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Try adjusting your search or filters:
            </p>
            
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Check for spelling mistakes</li>
              <li>• Use different keywords</li>
              <li>• Remove some filters</li>
            </ul>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center mt-6">
            {onClearSearch && searchQuery && (
              <Button 
                size="sm"
                variant="outline"
                onClick={onClearSearch}
              >
                <Search className="w-4 h-4 mr-2" />
                Clear Search
              </Button>
            )}
            
            {onResetFilters && hasFilters && (
              <Button 
                size="sm"
                variant="outline"
                onClick={onResetFilters}
              >
                <Filter className="w-4 h-4 mr-2" />
                Reset Filters
              </Button>
            )}
            
            <Button 
              size="sm"
              variant="outline"
              onClick={() => window.location.reload()}
            >
              <RefreshCw className="w-4 w-4 mr-2" />
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}