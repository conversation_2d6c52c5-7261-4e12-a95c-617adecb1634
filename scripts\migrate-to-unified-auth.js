#!/usr/bin/env node

/**
 * Migration script to update API routes to use UnifiedAuthService
 * This script helps identify and update authentication patterns
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Patterns to search for
const authPatterns = [
  {
    name: 'requireAuth',
    pattern: /requireAuth\s*\(/g,
    replacement: 'UnifiedAuthService.withAuth'
  },
  {
    name: 'authenticateUser',
    pattern: /authenticateUser\s*\(/g,
    replacement: 'UnifiedAuthService.authenticateUser'
  },
  {
    name: 'authenticateUserForProject',
    pattern: /authenticateUserForProject\s*\(/g,
    replacement: 'UnifiedAuthService.authenticateProjectAccess'
  },
  {
    name: 'authenticateUserForSeries',
    pattern: /authenticateUserForSeries\s*\(/g,
    replacement: 'UnifiedAuthService.authenticateSeriesAccess'
  },
  {
    name: 'authenticateAdmin',
    pattern: /authenticateAdmin\s*\(/g,
    replacement: 'UnifiedAuthService.authenticateAdmin'
  },
  {
    name: 'withAuth wrapper',
    pattern: /export\s+const\s+\w+\s*=\s*withAuth\s*\(/g,
    replacement: 'UnifiedAuthService.withAuth'
  },
  {
    name: 'withAdmin wrapper',
    pattern: /export\s+const\s+\w+\s*=\s*withAdmin\s*\(/g,
    replacement: 'UnifiedAuthService.withAdmin'
  },
  {
    name: 'withProjectAccess wrapper',
    pattern: /export\s+const\s+\w+\s*=\s*withProjectAccess\s*\(/g,
    replacement: 'UnifiedAuthService.withProjectAccess'
  }
];

// Import statements to update
const importPatterns = [
  {
    old: /@\/lib\/api\/auth-helpers/g,
    new: '@/lib/auth/unified-auth-service'
  },
  {
    old: /@\/lib\/api\/auth-middleware/g,
    new: '@/lib/auth/unified-auth-service'
  },
  {
    old: /@\/lib\/auth(?!\/unified-auth-service)/g,
    new: '@/lib/auth/unified-auth-service'
  }
];

async function findFilesToUpdate() {
  const apiPath = path.join(__dirname, '../src/app/api');
  const files = glob.sync('**/*.ts', { cwd: apiPath, absolute: true });
  
  const filesToUpdate = [];
  
  for (const file of files) {
    // Skip the word-counts route we already updated
    if (file.includes('series/[id]/word-counts/route.ts')) continue;
    if (file.includes('achievements/route.ts')) continue;
    if (file.includes('ai/chat/route.ts')) continue;
    if (file.includes('projects/[id]/export/route.ts')) continue;
    
    const content = fs.readFileSync(file, 'utf8');
    
    // Check if file uses any auth patterns
    const hasAuthPattern = authPatterns.some(p => p.pattern.test(content));
    const hasImportPattern = importPatterns.some(p => p.old.test(content));
    
    if (hasAuthPattern || hasImportPattern) {
      filesToUpdate.push({
        path: file,
        relativePath: path.relative(apiPath, file),
        patterns: authPatterns.filter(p => p.pattern.test(content)).map(p => p.name),
        hasImports: hasImportPattern
      });
    }
  }
  
  return filesToUpdate;
}

async function main() {
  console.log('🔍 Searching for API routes using old authentication patterns...\n');
  
  const files = await findFilesToUpdate();
  
  console.log(`Found ${files.length} files that need updating:\n`);
  
  files.forEach((file, index) => {
    console.log(`${index + 1}. ${file.relativePath}`);
    console.log(`   Patterns: ${file.patterns.join(', ')}`);
    console.log(`   Has imports to update: ${file.hasImports ? 'Yes' : 'No'}\n`);
  });
  
  // Create a summary report
  const report = {
    totalFiles: files.length,
    timestamp: new Date().toISOString(),
    files: files.map(f => ({
      path: f.relativePath,
      patterns: f.patterns,
      hasImports: f.hasImports
    }))
  };
  
  const reportPath = path.join(__dirname, 'auth-migration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n✅ Migration report saved to: ${reportPath}`);
  console.log('\nNext steps:');
  console.log('1. Review the files listed above');
  console.log('2. Update each file to use UnifiedAuthService');
  console.log('3. Test the updated endpoints');
  console.log('4. Remove old authentication files when complete');
}

main().catch(console.error);