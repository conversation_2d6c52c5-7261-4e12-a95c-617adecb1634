import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { logger } from '@/lib/services/logger';

// Mock console methods
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();
const mockConsoleInfo = jest.spyOn(console, 'info').mockImplementation();
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();

// Mock external error reporting service
jest.mock('@sentry/nextjs', () => ({
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  withScope: jest.fn((callback) => callback({ setContext: jest.fn(), setTag: jest.fn() })),
}));

describe('Logger Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment
    process.env.NODE_ENV = 'test';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('log levels', () => {
    it('should log debug messages', () => {
      logger.debug('Debug message', { extra: 'data' });

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG]'),
        'Debug message',
        { extra: 'data' }
      );
    });

    it('should log info messages', () => {
      logger.info('Info message', { userId: '123' });

      expect(mockConsoleInfo).toHaveBeenCalledWith(
        expect.stringContaining('[INFO]'),
        'Info message',
        { userId: '123' }
      );
    });

    it('should log warning messages', () => {
      logger.warn('Warning message', { count: 5 });

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        expect.stringContaining('[WARN]'),
        'Warning message',
        { count: 5 }
      );
    });

    it('should log error messages', () => {
      const error = new Error('Test error');
      logger.error('Error occurred', error, { context: 'test' });

      expect(mockConsoleError).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR]'),
        'Error occurred',
        error,
        { context: 'test' }
      );
    });
  });

  describe('environment-based logging', () => {
    it('should suppress debug logs in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      logger.debug('Debug in production');

      expect(mockConsoleLog).not.toHaveBeenCalled();

      process.env.NODE_ENV = originalEnv;
    });

    it('should allow all log levels in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      logger.debug('Debug in dev');
      logger.info('Info in dev');
      logger.warn('Warn in dev');
      logger.error('Error in dev');

      expect(mockConsoleLog).toHaveBeenCalledTimes(1);
      expect(mockConsoleInfo).toHaveBeenCalledTimes(1);
      expect(mockConsoleWarn).toHaveBeenCalledTimes(1);
      expect(mockConsoleError).toHaveBeenCalledTimes(1);

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('structured logging', () => {
    it('should format logs with timestamp', () => {
      logger.info('Timestamped log');

      const logCall = mockConsoleInfo.mock.calls[0][0];
      expect(logCall).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });

    it('should include context in logs', () => {
      const context = {
        userId: 'user-123',
        projectId: 'project-456',
        action: 'create-chapter',
      };

      logger.info('User action', context);

      expect(mockConsoleInfo).toHaveBeenCalledWith(
        expect.any(String),
        'User action',
        context
      );
    });

    it('should handle circular references in context', () => {
      const circular: any = { a: 1 };
      circular.self = circular;

      expect(() => {
        logger.info('Circular reference', circular);
      }).not.toThrow();
    });
  });

  describe('error handling and reporting', () => {
    it('should capture exceptions with Sentry in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';
      
      const { captureException } = await import('@sentry/nextjs');
      const error = new Error('Production error');

      logger.error('Critical error', error);

      expect(captureException).toHaveBeenCalledWith(error);

      process.env.NODE_ENV = originalEnv;
    });

    it('should add context to error reports', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const { withScope } = await import('@sentry/nextjs');
      const error = new Error('Contextual error');
      const context = { userId: 'user-123', action: 'save' };

      logger.error('Error with context', error, context);

      expect(withScope).toHaveBeenCalled();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('performance logging', () => {
    it('should measure execution time', async () => {
      const operation = 'database-query';
      const timer = logger.startTimer(operation);

      // Simulate async operation
      await new Promise(resolve => setTimeout(resolve, 100));

      timer.end();

      expect(mockConsoleInfo).toHaveBeenCalledWith(
        expect.stringContaining('[INFO]'),
        expect.stringContaining(operation),
        expect.objectContaining({
          duration: expect.any(Number),
        })
      );
    });

    it('should include metadata with timer', () => {
      const timer = logger.startTimer('api-call', { endpoint: '/api/test' });
      timer.end();

      expect(mockConsoleInfo).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(String),
        expect.objectContaining({
          endpoint: '/api/test',
          duration: expect.any(Number),
        })
      );
    });
  });

  describe('log filtering and sanitization', () => {
    it('should sanitize sensitive data', () => {
      const sensitiveData = {
        password: 'secret123',
        apiKey: 'sk-1234567890',
        creditCard: '****************',
        email: '<EMAIL>',
        name: 'John Doe',
      };

      logger.info('User data', sensitiveData);

      const loggedData = mockConsoleInfo.mock.calls[0][2];
      expect(loggedData.password).toBe('[REDACTED]');
      expect(loggedData.apiKey).toBe('[REDACTED]');
      expect(loggedData.creditCard).toBe('[REDACTED]');
      expect(loggedData.email).toBe('use***@example.com');
      expect(loggedData.name).toBe('John Doe'); // Names should not be redacted
    });

    it('should handle nested sensitive data', () => {
      const nestedData = {
        user: {
          profile: {
            password: 'secret',
            preferences: {
              apiKey: 'key123',
            },
          },
        },
      };

      logger.info('Nested data', nestedData);

      const loggedData = mockConsoleInfo.mock.calls[0][2];
      expect(loggedData.user.profile.password).toBe('[REDACTED]');
      expect(loggedData.user.profile.preferences.apiKey).toBe('[REDACTED]');
    });
  });

  describe('batch logging', () => {
    it('should batch multiple logs for performance', async () => {
      const batchLogger = logger.createBatch();

      batchLogger.add('info', 'First message');
      batchLogger.add('warn', 'Second message');
      batchLogger.add('error', 'Third message');

      await batchLogger.flush();

      expect(mockConsoleInfo).toHaveBeenCalledTimes(1);
      expect(mockConsoleWarn).toHaveBeenCalledTimes(1);
      expect(mockConsoleError).toHaveBeenCalledTimes(1);
    });

    it('should auto-flush batch after timeout', async () => {
      const batchLogger = logger.createBatch({ flushInterval: 100 });

      batchLogger.add('info', 'Auto flush message');

      await new Promise(resolve => setTimeout(resolve, 150));

      expect(mockConsoleInfo).toHaveBeenCalled();
    });
  });

  describe('log correlation', () => {
    it('should generate and track correlation IDs', () => {
      const correlationId = logger.createCorrelationId();
      const correlatedLogger = logger.withCorrelationId(correlationId);

      correlatedLogger.info('Correlated message 1');
      correlatedLogger.info('Correlated message 2');

      expect(mockConsoleInfo).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(String),
        expect.objectContaining({
          correlationId,
        })
      );
    });

    it('should propagate correlation ID through async operations', async () => {
      const correlationId = logger.createCorrelationId();
      const correlatedLogger = logger.withCorrelationId(correlationId);

      await Promise.all([
        async () => correlatedLogger.info('Async 1'),
        async () => correlatedLogger.info('Async 2'),
      ].map(fn => fn()));

      const calls = mockConsoleInfo.mock.calls;
      expect(calls).toHaveLength(2);
      expect(calls[0][2].correlationId).toBe(correlationId);
      expect(calls[1][2].correlationId).toBe(correlationId);
    });
  });

  describe('log levels configuration', () => {
    it('should respect configured log level', () => {
      const originalLevel = process.env.LOG_LEVEL;
      process.env.LOG_LEVEL = 'warn';

      logger.debug('Should not appear');
      logger.info('Should not appear');
      logger.warn('Should appear');
      logger.error('Should appear');

      expect(mockConsoleLog).not.toHaveBeenCalled();
      expect(mockConsoleInfo).not.toHaveBeenCalled();
      expect(mockConsoleWarn).toHaveBeenCalledTimes(1);
      expect(mockConsoleError).toHaveBeenCalledTimes(1);

      process.env.LOG_LEVEL = originalLevel;
    });
  });

  describe('custom formatters', () => {
    it('should support custom log formatters', () => {
      const customLogger = logger.withFormatter((level, message, context) => {
        return `[CUSTOM] ${level}: ${message}`;
      });

      customLogger.info('Formatted message');

      expect(mockConsoleInfo).toHaveBeenCalledWith(
        '[CUSTOM] INFO: Formatted message',
        undefined
      );
    });
  });
});