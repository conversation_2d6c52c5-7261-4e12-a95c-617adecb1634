import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { UnifiedResponse } from '@/lib/utils/response'
import { logger } from '@/lib/services/logger'

const memorySettingsSchema = z.object({
  autoOptimizationEnabled: z.boolean(),
  optimizationThreshold: z.number().min(0).max(100),
  compressionStrategy: z.enum(['aggressive', 'balanced', 'conservative']),
  retentionPolicy: z.object({
    maxAge: z.number().min(1).max(365), // days
    maxContexts: z.number().min(10).max(1000),
    priorityWeights: z.object({
      recency: z.number().min(0).max(1),
      frequency: z.number().min(0).max(1),
      importance: z.number().min(0).max(1)
    })
  }),
  notifications: z.object({
    nearLimit: z.boolean(),
    optimizationComplete: z.boolean(),
    compressionSuggestions: z.boolean()
  })
})

const querySchema = z.object({
  projectId: z.string().uuid()
})

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    
    if (!projectId) {
      return UnifiedResponse.error('Project ID is required', 400)
    }

    const validation = querySchema.safeParse({ projectId })
    if (!validation.success) {
      return UnifiedResponse.error('Invalid project ID format', 400)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Get memory settings
    const { data: settings, error: settingsError } = await supabase
      .from('memory_settings')
      .select('*')
      .eq('project_id', projectId)
      .single()

    if (settingsError && settingsError.code !== 'PGRST116') { // Not found is ok
      logger.error('Error fetching memory settings:', settingsError)
      return UnifiedResponse.error('Failed to fetch memory settings')
    }

    // Return default settings if none exist
    const defaultSettings = {
      autoOptimizationEnabled: true,
      optimizationThreshold: 80,
      compressionStrategy: 'balanced',
      retentionPolicy: {
        maxAge: 90,
        maxContexts: 100,
        priorityWeights: {
          recency: 0.4,
          frequency: 0.3,
          importance: 0.3
        }
      },
      notifications: {
        nearLimit: true,
        optimizationComplete: false,
        compressionSuggestions: true
      }
    }

    return UnifiedResponse.success({
      projectId,
      settings: settings?.settings || defaultSettings,
      isDefault: !settings
    })
  } catch (error) {
    logger.error('Memory settings GET error:', error)
    return UnifiedResponse.error('Failed to fetch memory settings')
  }
})

export const PUT = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const { projectId, ...settingsData } = body

    if (!projectId) {
      return UnifiedResponse.error('Project ID is required', 400)
    }

    const projectValidation = querySchema.safeParse({ projectId })
    if (!projectValidation.success) {
      return UnifiedResponse.error('Invalid project ID format', 400)
    }

    const settingsValidation = memorySettingsSchema.safeParse(settingsData)
    if (!settingsValidation.success) {
      return UnifiedResponse.error('Invalid settings data', 400, settingsValidation.error.errors)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Upsert memory settings
    const { data: updatedSettings, error: updateError } = await supabase
      .from('memory_settings')
      .upsert({
        project_id: projectId,
        settings: settingsValidation.data,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'project_id'
      })
      .select()
      .single()

    if (updateError) {
      logger.error('Error updating memory settings:', updateError)
      return UnifiedResponse.error('Failed to update memory settings')
    }

    // Log settings update
    logger.info('Memory settings updated', {
      projectId,
      userId: user.id,
      changes: settingsValidation.data
    })

    return UnifiedResponse.success({
      message: 'Memory settings updated successfully',
      settings: updatedSettings.settings
    })
  } catch (error) {
    logger.error('Memory settings PUT error:', error)
    return UnifiedResponse.error('Failed to update memory settings')
  }
})