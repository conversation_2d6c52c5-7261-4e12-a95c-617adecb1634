'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { 
  BookOpen, 
  Users, 
  Target, 
  Clock, 
  Lightbulb,
  CheckCircle,
  ArrowRight,
  Filter,
  Sparkles
} from 'lucide-react'
import { projectTemplates, type ProjectTemplate, getAllGenres } from '@/lib/project-templates'
import { createClient } from '@/lib/supabase'

interface TemplateBrowserProps {
  onTemplateSelect?: (template: ProjectTemplate) => void
}

const difficultyColors = {
  beginner: 'bg-success',
  intermediate: 'bg-warning', 
  advanced: 'bg-error'
}

const difficultyLabels = {
  beginner: 'Beginner Friendly',
  intermediate: 'Intermediate',
  advanced: 'Advanced'
}

export function TemplateBrowser({ onTemplateSelect }: TemplateBrowserProps) {
  const [selectedGenre, setSelectedGenre] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')
  const [, setSelectedTemplate] = useState<ProjectTemplate | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const router = useRouter()

  const filteredTemplates = projectTemplates.filter(template => {
    const genreMatch = selectedGenre === 'all' || template.genre === selectedGenre
    const difficultyMatch = selectedDifficulty === 'all' || template.difficulty === selectedDifficulty
    return genreMatch && difficultyMatch
  })

  const handleUseTemplate = async (template: ProjectTemplate) => {
    if (onTemplateSelect) {
      onTemplateSelect(template)
      return
    }

    setIsCreating(true)
    try {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/login')
        return
      }

      // Create project with template selections
      const projectData = {
        user_id: user.id,
        title: `New ${template.name} Project`,
        description: `A ${template.genre} story based on the ${template.name} template.`,
        ...template.selections
      }

      const { data: project, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single()

      if (error) throw error

      // Add template characters as starting points
      for (const archetype of template.characterArchetypes) {
        await supabase.from('characters').insert({
          project_id: project.id,
          name: archetype.name,
          role: archetype.role,
          description: archetype.description,
          personality_traits: { traits: archetype.traits }
        })
      }

      // Add template plot structure
      for (const plotPoint of template.plotStructure) {
        await supabase.from('story_arcs').insert({
          project_id: project.id,
          act_number: plotPoint.act,
          description: plotPoint.description,
          key_events: plotPoint.keyEvents
        })
      }

      // Create initial chapters based on template
      const wordsPerChapter = Math.floor(template.estimatedWordCount / template.estimatedChapters)
      for (let i = 1; i <= Math.min(5, template.estimatedChapters); i++) {
        await supabase.from('chapters').insert({
          project_id: project.id,
          chapter_number: i,
          title: `Chapter ${i}`,
          target_word_count: wordsPerChapter,
          status: 'planned'
        })
      }

      router.push(`/projects/${project.id}?template=${template.id}`)
    } catch (error) {
      logger.error('Error creating project from template:', error);
      alert('Failed to create project. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Project Templates</h2>
            <p className="text-muted-foreground">
              Start with a proven story structure tailored to your genre
            </p>
          </div>
          <Badge variant="secondary" className="gap-1">
            <Sparkles className="h-3 w-3" />
            {filteredTemplates.length} Templates
          </Badge>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 sm:gap-5 lg:gap-6">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={selectedGenre} onValueChange={setSelectedGenre}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Genres" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Genres</SelectItem>
                {getAllGenres().map(genre => (
                  <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Levels" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="beginner">Beginner</SelectItem>
              <SelectItem value="intermediate">Intermediate</SelectItem>
              <SelectItem value="advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-6 md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="group hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{template.genre}</Badge>
                    <div className={`h-2 w-2 rounded-full ${difficultyColors[template.difficulty]}`} />
                    <span className="text-xs text-muted-foreground">
                      {difficultyLabels[template.difficulty]}
                    </span>
                  </div>
                </div>
              </div>
              <CardDescription className="text-sm">
                {template.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6 text-sm">
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3 text-muted-foreground" />
                  <span>{template.estimatedWordCount.toLocaleString()} words</span>
                </div>
                <div className="flex items-center gap-1">
                  <BookOpen className="h-3 w-3 text-muted-foreground" />
                  <span>{template.estimatedChapters} chapters</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3 text-muted-foreground" />
                  <span>{template.characterArchetypes.length} archetypes</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span>{template.plotStructure.length} acts</span>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Story Elements:</p>
                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {template.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{template.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setSelectedTemplate(template)}
                    >
                      Preview
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl lg:max-w-3xl xl:max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <BookOpen className="h-5 w-5" />
                        {template.name}
                      </DialogTitle>
                      <DialogDescription>
                        {template.description}
                      </DialogDescription>
                    </DialogHeader>

                    <Tabs defaultValue="overview" className="space-y-4">
                      <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="characters">Characters</TabsTrigger>
                        <TabsTrigger value="structure">Structure</TabsTrigger>
                        <TabsTrigger value="prompts">Prompts</TabsTrigger>
                      </TabsList>

                      <TabsContent value="overview" className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                          <div>
                            <h4 className="font-semibold">Genre & Style</h4>
                            <p className="text-sm text-muted-foreground">{template.genre}</p>
                            <p className="text-sm text-muted-foreground">{template.selections.narrativeVoice}</p>
                            <p className="text-sm text-muted-foreground">{template.selections.writingStyle}</p>
                          </div>
                          <div>
                            <h4 className="font-semibold">Target Specs</h4>
                            <p className="text-sm text-muted-foreground">{template.estimatedWordCount.toLocaleString()} words</p>
                            <p className="text-sm text-muted-foreground">{template.estimatedChapters} chapters</p>
                            <p className="text-sm text-muted-foreground">{template.selections.targetAudience}</p>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold mb-2">Story Tags</h4>
                          <div className="flex flex-wrap gap-1">
                            {template.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-2">Major Themes</h4>
                          <div className="flex flex-wrap gap-1">
                            {template.selections.majorThemes.map((theme, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {theme}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="characters" className="space-y-4">
                        {template.characterArchetypes.map((character, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <h4 className="font-semibold">{character.name}</h4>
                            <p className="text-sm text-muted-foreground mb-2">{character.role}</p>
                            <p className="text-sm mb-2">{character.description}</p>
                            <div className="flex flex-wrap gap-1">
                              {character.traits.map((trait, traitIndex) => (
                                <Badge key={traitIndex} variant="outline" className="text-xs">
                                  {trait}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        ))}
                      </TabsContent>

                      <TabsContent value="structure" className="space-y-4">
                        {template.plotStructure.map((act, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold">Act {act.act}</h4>
                              <Badge variant="secondary" className="text-xs">
                                {act.wordCountPercentage}% of story
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mb-3">{act.description}</p>
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Key Events:</p>
                              <ul className="text-sm text-muted-foreground space-y-1">
                                {act.keyEvents.map((event, eventIndex) => (
                                  <li key={eventIndex} className="flex items-start gap-2">
                                    <CheckCircle className="h-3 w-3 mt-1 text-success" />
                                    {event}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        ))}
                      </TabsContent>

                      <TabsContent value="prompts" className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2">
                            <Lightbulb className="h-4 w-4" />
                            Story Starter Ideas
                          </h4>
                          <div className="space-y-3">
                            {template.storyPrompts.map((prompt, index) => (
                              <div key={index} className="border rounded-lg p-3">
                                <p className="text-sm">{prompt}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>

                    <Separator />

                    <div className="flex gap-2 justify-end">
                      <Button 
                        onClick={() => handleUseTemplate(template)}
                        disabled={isCreating}
                        className="gap-2"
                      >
                        {isCreating ? (
                          <>
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                            Creating...
                          </>
                        ) : (
                          <>
                            Use This Template
                            <ArrowRight className="h-4 w-4" />
                          </>
                        )}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button 
                  size="sm" 
                  className="flex-1 gap-1"
                  onClick={() => handleUseTemplate(template)}
                  disabled={isCreating}
                >
                  {isCreating ? (
                    <div className="h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  ) : (
                    <>
                      Use Template
                      <ArrowRight className="h-3 w-3" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="font-semibold mb-2">No templates found</h3>
          <p className="text-muted-foreground">
            Try adjusting your filters to see more templates.
          </p>
        </div>
      )}
    </div>
  )
}