'use client'

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import {
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Clock,
  Users,
  GitBranch,
  Globe,
  BookOpen,
  Info,
  AlertCircle,
  XCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

interface ConsistencyCheckerProps {
  projectId: string
  chapterId?: string
  content?: string
  onIssueClick?: (issue: ConsistencyIssue) => void
}

interface ConsistencyIssue {
  type: 'character' | 'timeline' | 'plot' | 'world' | 'relationship' | 'style'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  location: string
  suggestion: string
}

interface ConsistencyReport {
  overallScore: number
  issues: ConsistencyIssue[]
  characterConsistency: number
  timelineConsistency: number
  plotConsistency: number
  worldConsistency: number
  styleConsistency: number
  timestamp?: string
}

export function ConsistencyChecker({ 
  projectId, 
  chapterId, 
  content,
  onIssueClick 
}: ConsistencyCheckerProps) {
  const [isChecking, setIsChecking] = useState(false)
  const [report, setReport] = useState<ConsistencyReport | null>(null)
  const [selectedType, setSelectedType] = useState<'all' | 'character' | 'timeline' | 'plot' | 'world' | 'style'>('all')
  const [checkType, setCheckType] = useState<'chapter' | 'book'>('chapter')
  const { toast } = useToast()

  const runConsistencyCheck = async (type: 'chapter' | 'book' = 'chapter') => {
    if (type === 'chapter' && (!chapterId || !content)) {
      toast({
        title: 'Cannot check chapter',
        description: 'No chapter content available for consistency check',
        variant: 'destructive'
      })
      return
    }

    setIsChecking(true)
    try {
      const response = await fetch('/api/consistency/check', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          chapterId,
          content,
          checkType: type
        })
      })

      if (!response.ok) {
        throw new Error('Failed to run consistency check')
      }

      const data = await response.json()
      if (data.success && data.report) {
        setReport(data.report)
        setCheckType(type)
      }
    } catch (error) {
      logger.error('Consistency check error:', error)
      toast({
        title: 'Consistency check failed',
        description: 'Please try again later',
        variant: 'destructive'
      })
    } finally {
      setIsChecking(false)
    }
  }

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'character': return Users
      case 'timeline': return Clock
      case 'plot': return GitBranch
      case 'world': return Globe
      case 'style': return BookOpen
      default: return AlertTriangle
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-info bg-info-light border-blue-200'
      case 'medium': return 'text-warning bg-warning-light border-yellow-200'
      case 'high': return 'text-warning bg-orange-50 border-orange-200'
      case 'critical': return 'text-error bg-error-light border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low': return Info
      case 'medium': return AlertCircle
      case 'high': return AlertTriangle
      case 'critical': return XCircle
      default: return Info
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-success'
    if (score >= 75) return 'text-info'
    if (score >= 60) return 'text-warning'
    return 'text-error'
  }

  const filteredIssues = report?.issues.filter(issue => 
    selectedType === 'all' || issue.type === selectedType
  ) || []

  const consistencyMetrics = report ? [
    { label: 'Characters', value: report.characterConsistency, icon: Users },
    { label: 'Timeline', value: report.timelineConsistency, icon: Clock },
    { label: 'Plot', value: report.plotConsistency, icon: GitBranch },
    { label: 'World', value: report.worldConsistency, icon: Globe },
    { label: 'Style', value: report.styleConsistency, icon: BookOpen },
  ] : []

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Consistency Checker
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => runConsistencyCheck('chapter')}
              disabled={isChecking || !content}
            >
              Check Chapter
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => runConsistencyCheck('book')}
              disabled={isChecking}
            >
              Check Book
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isChecking ? (
          <div className="space-y-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
        ) : report ? (
          <div className="space-y-6">
            {/* Overall Score */}
            <div className="text-center space-y-2">
              <div className={cn("text-5xl font-bold", getScoreColor(report.overallScore))}>
                {report.overallScore}%
              </div>
              <p className="text-sm text-muted-foreground">
                Overall Consistency Score
              </p>
              {report.timestamp && (
                <p className="text-xs text-muted-foreground">
                  Last checked: {new Date(report.timestamp).toLocaleString()}
                </p>
              )}
            </div>

            {/* Consistency Metrics */}
            <div className="grid grid-cols-2 gap-3">
              {consistencyMetrics.map((metric, idx) => {
                const Icon = metric.icon
                return (
                  <div key={idx} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm flex items-center gap-1">
                        <Icon className="h-3 w-3" />
                        {metric.label}
                      </span>
                      <span className={cn("text-sm font-medium", getScoreColor(metric.value))}>
                        {metric.value}%
                      </span>
                    </div>
                    <Progress value={metric.value} className="h-2" />
                  </div>
                )
              })}
            </div>

            {/* Issues */}
            {report.issues.length > 0 && (
              <>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">
                      Consistency Issues ({report.issues.length})
                    </h4>
                    <Badge variant="outline">
                      {checkType === 'chapter' ? 'Chapter Check' : 'Book Check'}
                    </Badge>
                  </div>
                  
                  <Tabs value={selectedType} onValueChange={(v) => setSelectedType(v as any)}>
                    <TabsList className="grid w-full grid-cols-6">
                      <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
                      <TabsTrigger value="character" className="text-xs">Char</TabsTrigger>
                      <TabsTrigger value="timeline" className="text-xs">Time</TabsTrigger>
                      <TabsTrigger value="plot" className="text-xs">Plot</TabsTrigger>
                      <TabsTrigger value="world" className="text-xs">World</TabsTrigger>
                      <TabsTrigger value="style" className="text-xs">Style</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                <ScrollArea className="h-[300px] w-full">
                  <div className="space-y-3 pr-4">
                    {filteredIssues.map((issue, idx) => {
                      const Icon = getIssueIcon(issue.type)
                      const SeverityIcon = getSeverityIcon(issue.severity)
                      
                      return (
                        <div
                          key={idx}
                          className={cn(
                            "p-3 rounded-lg border cursor-pointer transition-colors",
                            getSeverityColor(issue.severity),
                            "hover:opacity-90"
                          )}
                          onClick={() => onIssueClick?.(issue)}
                        >
                          <div className="flex items-start gap-2">
                            <Icon className="h-4 w-4 mt-0.5" />
                            <div className="flex-1 space-y-1">
                              <div className="flex items-center gap-2">
                                <SeverityIcon className="h-3 w-3" />
                                <span className="text-xs font-medium uppercase">
                                  {issue.severity}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  • {issue.location}
                                </span>
                              </div>
                              <p className="text-sm font-medium">{issue.description}</p>
                              <p className="text-xs opacity-75">{issue.suggestion}</p>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </ScrollArea>
              </>
            )}

            {report.issues.length === 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>Excellent consistency!</AlertTitle>
                <AlertDescription>
                  No consistency issues found in your {checkType}.
                </AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          <div className="text-center py-6 sm:py-8 lg:py-10">
            <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              Check your writing for consistency issues across characters, timeline, plot, and more.
            </p>
            <div className="flex justify-center gap-2">
              <Button 
                onClick={() => runConsistencyCheck('chapter')} 
                disabled={!content}
                variant="outline"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Current Chapter
              </Button>
              <Button 
                onClick={() => runConsistencyCheck('book')}
                variant="outline"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Entire Book
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}