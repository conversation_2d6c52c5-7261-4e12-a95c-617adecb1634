import { AnalyticsCard } from '../components/analytics-card'
import { ProgressChart } from '../components/progress-chart'
import { HeatmapCalendar } from '../components/heatmap-calendar'
import { 
  FileText, 
  Zap, 
  BookOpen, 
  TrendingUp, 
  Clock,
  Activity
} from 'lucide-react'
import type { AnalyticsData } from '@/types/analytics'

interface OverviewSectionProps {
  data: AnalyticsData | null
  isLoading: boolean
}

export function OverviewSection({ data, isLoading }: OverviewSectionProps) {
  const overview = data?.overview || {
    totalWords: 0,
    totalSessions: 0,
    activeProjects: 0,
    writingStreak: 0,
    averageWordsPerDay: 0,
    timeSpentWriting: 0
  }

  // Convert minutes to hours and minutes
  const hours = Math.floor(overview.timeSpentWriting / 60)
  const minutes = overview.timeSpentWriting % 60
  const timeDisplay = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`

  // Prepare chart data
  const chartData = data?.activity?.dailyProgress || []

  return (
    <>
      {/* Key Metrics Grid */}
      <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-6">
        <AnalyticsCard
          title="Total Words"
          value={overview.totalWords}
          subtitle="All time"
          icon={FileText}
          loading={isLoading}
          trend={{
            value: 12,
            direction: 'up'
          }}
        />
        
        <AnalyticsCard
          title="Writing Streak"
          value={overview.writingStreak}
          subtitle="Days in a row"
          icon={Zap}
          loading={isLoading}
          valueClassName={overview.writingStreak > 0 ? 'text-warning' : ''}
        />
        
        <AnalyticsCard
          title="Active Projects"
          value={overview.activeProjects}
          subtitle="In progress"
          icon={BookOpen}
          loading={isLoading}
        />
        
        <AnalyticsCard
          title="Avg Words/Day"
          value={overview.averageWordsPerDay}
          subtitle="Daily average"
          icon={TrendingUp}
          loading={isLoading}
          trend={{
            value: 8,
            direction: 'up'
          }}
        />
        
        <AnalyticsCard
          title="Writing Sessions"
          value={overview.totalSessions}
          subtitle="Total sessions"
          icon={Activity}
          loading={isLoading}
        />
        
        <AnalyticsCard
          title="Time Writing"
          value={timeDisplay}
          subtitle="Total time"
          icon={Clock}
          loading={isLoading}
        />
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        <ProgressChart
          title="Daily Word Count"
          data={chartData}
          lines={[
            { dataKey: 'words', color: 'hsl(var(--primary))', name: 'Words Written' }
          ]}
          type="area"
          height={250}
          loading={isLoading}
          yAxisLabel="Words"
        />
        
        <HeatmapCalendar
          title="Writing Activity"
          data={data?.activity?.heatmapData || []}
          loading={isLoading}
          months={3}
        />
      </div>
    </>
  )
}