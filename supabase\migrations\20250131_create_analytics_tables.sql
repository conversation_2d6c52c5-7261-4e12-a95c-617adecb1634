-- Create writing sessions table
CREATE TABLE IF NOT EXISTS writing_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ NOT NULL,
    words_written INTEGER NOT NULL DEFAULT 0,
    chapters_edited INTEGER[] DEFAULT '{}',
    ai_interactions INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_session_times CHECK (end_time > start_time),
    CONSTRAINT positive_words CHECK (words_written >= 0)
);

-- Create user streaks table
CREATE TABLE IF NOT EXISTS user_streaks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_writing_date DATE,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create quality scores table
CREATE TABLE IF NOT EXISTS quality_scores (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    overall_score DECIMAL(5,2) DEFAULT 0,
    readability_score DECIMAL(5,2) DEFAULT 0,
    consistency_score DECIMAL(5,2) DEFAULT 0,
    pacing_score DECIMAL(5,2) DEFAULT 0,
    engagement_score DECIMAL(5,2) DEFAULT 0,
    calculated_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create AI usage logs table
CREATE TABLE IF NOT EXISTS ai_usage_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    agent_type TEXT NOT NULL,
    interaction_type TEXT,
    interaction_count INTEGER DEFAULT 1,
    tokens_used INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create daily analytics cache table
CREATE TABLE IF NOT EXISTS daily_analytics_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    word_count INTEGER DEFAULT 0,
    session_count INTEGER DEFAULT 0,
    active_projects TEXT[] DEFAULT '{}',
    avg_session_duration_minutes INTEGER DEFAULT 0,
    peak_hour INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT unique_user_date UNIQUE (user_id, date)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user_id ON writing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_project_id ON writing_sessions(project_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_start_time ON writing_sessions(start_time DESC);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user_time ON writing_sessions(user_id, start_time DESC);

CREATE INDEX IF NOT EXISTS idx_quality_scores_project_id ON quality_scores(project_id);
CREATE INDEX IF NOT EXISTS idx_quality_scores_calculated_at ON quality_scores(calculated_at DESC);

CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_user_id ON ai_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_created_at ON ai_usage_logs(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_daily_analytics_cache_user_date ON daily_analytics_cache(user_id, date DESC);

-- Function to update user streak
CREATE OR REPLACE FUNCTION update_user_streak(p_user_id UUID)
RETURNS void AS $$
DECLARE
    v_last_date DATE;
    v_current_streak INTEGER;
    v_longest_streak INTEGER;
    v_today DATE := CURRENT_DATE;
BEGIN
    -- Get current streak data
    SELECT last_writing_date, current_streak, longest_streak
    INTO v_last_date, v_current_streak, v_longest_streak
    FROM user_streaks
    WHERE user_id = p_user_id;

    -- If no record exists, create one
    IF NOT FOUND THEN
        INSERT INTO user_streaks (user_id, current_streak, longest_streak, last_writing_date)
        VALUES (p_user_id, 1, 1, v_today);
        RETURN;
    END IF;

    -- Update streak based on last writing date
    IF v_last_date = v_today THEN
        -- Already wrote today, no update needed
        RETURN;
    ELSIF v_last_date = v_today - INTERVAL '1 day' THEN
        -- Consecutive day, increment streak
        v_current_streak := v_current_streak + 1;
        v_longest_streak := GREATEST(v_longest_streak, v_current_streak);
    ELSE
        -- Streak broken, reset to 1
        v_current_streak := 1;
    END IF;

    -- Update the record
    UPDATE user_streaks
    SET current_streak = v_current_streak,
        longest_streak = v_longest_streak,
        last_writing_date = v_today,
        updated_at = NOW()
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get daily analytics
CREATE OR REPLACE FUNCTION get_daily_analytics(
    p_user_id UUID,
    p_start_date DATE,
    p_end_date DATE
)
RETURNS TABLE(
    date DATE,
    word_count INTEGER,
    session_count INTEGER,
    active_projects INTEGER,
    avg_session_duration DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH daily_data AS (
        SELECT 
            DATE(ws.start_time) as session_date,
            SUM(ws.words_written) as total_words,
            COUNT(DISTINCT ws.id) as total_sessions,
            COUNT(DISTINCT ws.project_id) as project_count,
            AVG(EXTRACT(EPOCH FROM (ws.end_time - ws.start_time)) / 3600) as avg_duration
        FROM writing_sessions ws
        WHERE ws.user_id = p_user_id
        AND DATE(ws.start_time) BETWEEN p_start_date AND p_end_date
        GROUP BY DATE(ws.start_time)
    ),
    date_series AS (
        SELECT generate_series(p_start_date, p_end_date, '1 day'::interval)::date AS date
    )
    SELECT 
        ds.date,
        COALESCE(dd.total_words, 0)::INTEGER,
        COALESCE(dd.total_sessions, 0)::INTEGER,
        COALESCE(dd.project_count, 0)::INTEGER,
        COALESCE(dd.avg_duration, 0)::DECIMAL
    FROM date_series ds
    LEFT JOIN daily_data dd ON ds.date = dd.session_date
    ORDER BY ds.date;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update streak after session insert
CREATE OR REPLACE FUNCTION trigger_update_streak()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM update_user_streak(NEW.user_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_streak_after_session
    AFTER INSERT ON writing_sessions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_streak();

-- Enable RLS
ALTER TABLE writing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_streaks ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_analytics_cache ENABLE ROW LEVEL SECURITY;

-- RLS policies for writing_sessions
CREATE POLICY "Users can view their own sessions" ON writing_sessions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own sessions" ON writing_sessions
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own sessions" ON writing_sessions
    FOR UPDATE USING (user_id = auth.uid());

-- RLS policies for user_streaks
CREATE POLICY "Users can view their own streaks" ON user_streaks
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own streaks" ON user_streaks
    FOR ALL USING (user_id = auth.uid());

-- RLS policies for quality_scores
CREATE POLICY "Users can view scores for their projects" ON quality_scores
    FOR SELECT USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
            UNION
            SELECT project_id FROM project_collaborators WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert scores for their projects" ON quality_scores
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
        )
    );

-- RLS policies for ai_usage_logs
CREATE POLICY "Users can view their own AI usage" ON ai_usage_logs
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own AI usage" ON ai_usage_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- RLS policies for daily_analytics_cache
CREATE POLICY "Users can view their own analytics cache" ON daily_analytics_cache
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Service role can manage analytics cache" ON daily_analytics_cache
    FOR ALL USING (auth.uid() IS NOT NULL);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON writing_sessions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON user_streaks TO authenticated;
GRANT SELECT, INSERT ON quality_scores TO authenticated;
GRANT SELECT, INSERT ON ai_usage_logs TO authenticated;
GRANT SELECT ON daily_analytics_cache TO authenticated;
GRANT INSERT, UPDATE, DELETE ON daily_analytics_cache TO service_role;

GRANT EXECUTE ON FUNCTION update_user_streak(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_daily_analytics(UUID, DATE, DATE) TO authenticated;