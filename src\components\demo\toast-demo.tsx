'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { 
  Save, 
  Trash2, 
  Download, 
  Upload, 
  Check, 
  X, 
  AlertCircle,
  Info,
  Sparkles,
  Loader2,
  Copy,
  Share2,
  BookOpen,
  Wand2
} from 'lucide-react';

export function ToastDemo() {
  const { toast } = useToast();
  const [loading, setLoading] = useState<string | null>(null);

  // Simulate async operations
  const simulateAsyncOperation = async (
    operation: string,
    duration: number = 2000,
    shouldFail: boolean = false
  ) => {
    setLoading(operation);
    await new Promise(resolve => setTimeout(resolve, duration));
    setLoading(null);
    if (shouldFail) {
      throw new Error('Operation failed');
    }
  };

  // Success operations
  const handleSave = async () => {
    try {
      await simulateAsyncOperation('save');
      toast({
        title: "Changes saved",
        description: "Your document has been saved successfully.",
      });
    } catch (error) {
      toast({
        title: "Save failed",
        description: "Unable to save your changes. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePublish = async () => {
    try {
      await simulateAsyncOperation('publish');
      toast({
        title: "Published successfully",
        description: "Your book is now live and available to readers.",
        action: (
          <Button variant="outline" size="sm" onClick={() => window.open('/book/view', '_blank')}>
            View Book
          </Button>
        ),
      });
    } catch (error) {
      toast({
        title: "Publication failed",
        description: "There was an error publishing your book.",
        variant: "destructive",
      });
    }
  };

  const handleExport = async () => {
    try {
      await simulateAsyncOperation('export');
      toast({
        title: "Export complete",
        description: "Your manuscript has been exported as EPUB.",
        action: (
          <Button variant="outline" size="sm">
            <Download className="h-3 w-3 mr-1" />
            Download
          </Button>
        ),
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Unable to export your manuscript.",
        variant: "destructive",
      });
    }
  };

  // AI operations
  const handleAIGeneration = async () => {
    try {
      // Show initial toast
      const { dismiss } = toast({
        title: "Generating content",
        description: "AI is writing your chapter...",
        duration: 100000, // Long duration
      });
      
      await simulateAsyncOperation('ai-generate', 3000);
      dismiss();
      
      toast({
        title: "Chapter generated",
        description: "AI has successfully generated a new chapter with 2,543 words.",
        action: (
          <Button variant="outline" size="sm">
            Review
          </Button>
        ),
      });
    } catch (error) {
      toast({
        title: "Generation failed",
        description: "AI encountered an error. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Collaboration operations
  const handleShare = async () => {
    try {
      await simulateAsyncOperation('share', 1000);
      toast({
        title: "Link copied",
        description: "Collaboration link has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Sharing failed",
        description: "Unable to create sharing link.",
        variant: "destructive",
      });
    }
  };

  // Delete operations
  const handleDelete = async () => {
    try {
      await simulateAsyncOperation('delete', 1500);
      toast({
        title: "Chapter deleted",
        description: "The chapter has been permanently removed.",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Deletion failed",
        description: "Unable to delete the chapter. It may be referenced elsewhere.",
        variant: "destructive",
      });
    }
  };

  // Information toasts
  const showInfoToast = () => {
    toast({
      title: "Pro tip",
      description: "Press Ctrl+S to quickly save your work at any time.",
    });
  };

  const showWarningToast = () => {
    toast({
      title: "Unsaved changes",
      description: "You have unsaved changes. Do you want to save before leaving?",
      action: (
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            Discard
          </Button>
          <Button size="sm">
            Save
          </Button>
        </div>
      ),
    });
  };

  // Progress toast
  const showProgressToast = async () => {
    const { dismiss, update } = toast({
      title: "Analyzing manuscript",
      description: "0% complete",
      duration: 100000,
    });

    for (let i = 0; i <= 100; i += 20) {
      await new Promise(resolve => setTimeout(resolve, 500));
      update({
        id: undefined,
        title: "Analyzing manuscript",
        description: `${i}% complete`,
      });
    }

    dismiss();
    toast({
      title: "Analysis complete",
      description: "Your manuscript has been analyzed successfully.",
    });
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Toast Notifications Demo</h1>
        <p className="text-muted-foreground">
          Examples of toast notifications for various user actions in BookScribe
        </p>
      </div>

      <div className="grid gap-6">
        {/* Success Operations */}
        <Card>
          <CardHeader>
            <CardTitle>Success Operations</CardTitle>
            <CardDescription>
              Notifications for successful user actions
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-4">
            <Button 
              onClick={handleSave}
              disabled={loading === 'save'}
            >
              {loading === 'save' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Document
            </Button>
            
            <Button 
              onClick={handlePublish}
              disabled={loading === 'publish'}
              variant="outline"
            >
              {loading === 'publish' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <BookOpen className="h-4 w-4 mr-2" />
              )}
              Publish Book
            </Button>
            
            <Button 
              onClick={handleExport}
              disabled={loading === 'export'}
              variant="outline"
            >
              {loading === 'export' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              Export EPUB
            </Button>
            
            <Button 
              onClick={handleShare}
              disabled={loading === 'share'}
              variant="outline"
            >
              {loading === 'share' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Share2 className="h-4 w-4 mr-2" />
              )}
              Share Link
            </Button>
          </CardContent>
        </Card>

        {/* AI Operations */}
        <Card>
          <CardHeader>
            <CardTitle>AI Operations</CardTitle>
            <CardDescription>
              Notifications for AI-powered features
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-4">
            <Button 
              onClick={handleAIGeneration}
              disabled={loading === 'ai-generate'}
              variant="outline"
            >
              {loading === 'ai-generate' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Wand2 className="h-4 w-4 mr-2" />
              )}
              Generate Chapter
            </Button>
            
            <Button 
              onClick={showProgressToast}
              variant="outline"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Analyze Manuscript
            </Button>
          </CardContent>
        </Card>

        {/* Error & Warning Operations */}
        <Card>
          <CardHeader>
            <CardTitle>Errors & Warnings</CardTitle>
            <CardDescription>
              Notifications for errors and important warnings
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-4">
            <Button 
              onClick={handleDelete}
              disabled={loading === 'delete'}
              variant="destructive"
            >
              {loading === 'delete' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete Chapter
            </Button>
            
            <Button 
              onClick={() => {
                toast({
                  title: "Connection lost",
                  description: "Unable to connect to the server. Please check your internet connection.",
                  variant: "destructive",
                });
              }}
              variant="outline"
            >
              <X className="h-4 w-4 mr-2" />
              Network Error
            </Button>
            
            <Button 
              onClick={showWarningToast}
              variant="outline"
            >
              <AlertCircle className="h-4 w-4 mr-2" />
              Show Warning
            </Button>
            
            <Button 
              onClick={() => {
                toast({
                  title: "Rate limit exceeded",
                  description: "You've reached your AI generation limit. Upgrade to Pro for unlimited access.",
                  variant: "destructive",
                  action: (
                    <Button variant="outline" size="sm">
                      Upgrade
                    </Button>
                  ),
                });
              }}
              variant="outline"
            >
              <AlertCircle className="h-4 w-4 mr-2" />
              Rate Limit
            </Button>
          </CardContent>
        </Card>

        {/* Informational */}
        <Card>
          <CardHeader>
            <CardTitle>Informational</CardTitle>
            <CardDescription>
              Helpful tips and status updates
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-4">
            <Button 
              onClick={showInfoToast}
              variant="outline"
            >
              <Info className="h-4 w-4 mr-2" />
              Show Tip
            </Button>
            
            <Button 
              onClick={() => {
                toast({
                  title: "Auto-save enabled",
                  description: "Your work will be saved automatically every 30 seconds.",
                });
              }}
              variant="outline"
            >
              <Check className="h-4 w-4 mr-2" />
              Feature Status
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Best Practices */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Toast Notification Best Practices</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>• Keep messages concise and actionable</p>
          <p>• Use success toasts for completed actions</p>
          <p>• Include action buttons for follow-up tasks</p>
          <p>• Show progress for long-running operations</p>
          <p>• Use destructive variant for errors and failures</p>
          <p>• Auto-dismiss after 5 seconds (configurable)</p>
          <p>• Provide undo options for destructive actions when possible</p>
        </CardContent>
      </Card>
    </div>
  );
}