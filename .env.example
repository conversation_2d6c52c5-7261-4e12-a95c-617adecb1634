# ===============================================
# BookScribe Environment Configuration
# ===============================================
# Copy this file to .env.local and fill in values

# --- Database & Auth (Required) ---
NEXT_PUBLIC_SUPABASE_URL=https://xxxxxxxxxxxxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
DATABASE_URL=postgresql://postgres:[password]@db.[project-id].supabase.co:5432/postgres

# --- AI Services (Required) ---
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Alternative AI Providers (Optional)
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxx
GOOGLE_GEMINI_API_KEY=xxxxxxxxxxxxxxxxxxxxx
XAI_API_KEY=xxxxxxxxxxxxxxxxxxxxx

# --- Email Service (Required) ---
MAILEROO_API_KEY=xxxxxxxxxxxxxxxxxxxxx
MAILEROO_API_URL=https://api.maileroo.com/v1
MAILEROO_FROM_EMAIL=<EMAIL>
MAILEROO_FROM_NAME=BookScribe AI

# --- Payments (Required for production) ---
STRIPE_SECRET_KEY=sk_live_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_xxxxxxxxxxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxx

# Stripe Price IDs
STRIPE_PRICE_STARTER_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_STARTER_YEARLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_PROFESSIONAL_MONTHLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_PROFESSIONAL_YEARLY=price_xxxxxxxxxxxxx
STRIPE_PRICE_ENTERPRISE_MONTHLY=price_xxxxxxxxxxxxx

# --- Application URLs ---
NEXT_PUBLIC_APP_URL=https://bookscribe.ai
NEXT_PUBLIC_API_URL=https://bookscribe.ai/api
NEXT_PUBLIC_MARKETING_URL=https://bookscribe.ai

# --- Feature Flags ---
NEXT_PUBLIC_ENABLE_DEMO_MODE=false
NEXT_PUBLIC_ENABLE_COLLABORATION=true
NEXT_PUBLIC_ENABLE_VOICE_FEATURES=true
NEXT_PUBLIC_ENABLE_UNIVERSE_SHARING=true
NEXT_PUBLIC_ENABLE_ACHIEVEMENTS=true

# --- Development ---
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
NEXT_PUBLIC_SHOW_DEBUG_INFO=false
NEXT_TELEMETRY_DISABLED=1

# --- Analytics & Monitoring (Optional) ---
SENTRY_DSN=https://<EMAIL>/xxxxx
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/xxxxx
SENTRY_ORG=bookscribe
SENTRY_PROJECT=bookscribe-app
SENTRY_AUTH_TOKEN=xxxxxxxxxxxxxxxxxxxxx

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# PostHog
NEXT_PUBLIC_POSTHOG_KEY=phc_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# --- Cron Jobs ---
CRON_SECRET=xxxxxxxxxxxxxxxxxxxxx

# --- Rate Limiting ---
RATE_LIMIT_WINDOW_MS=3600000
RATE_LIMIT_MAX_REQUESTS_FREE=100
RATE_LIMIT_MAX_REQUESTS_STARTER=1000
RATE_LIMIT_MAX_REQUESTS_PRO=10000

# --- Node.js Configuration ---
NODE_ENV=development
NODE_OPTIONS=--max-old-space-size=4096