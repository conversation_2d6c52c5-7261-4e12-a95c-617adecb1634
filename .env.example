# =========================================
# BookScribe AI Environment Configuration
# =========================================
# Copy this file to .env.local and fill in your values

# ==========================================
# Core Configuration
# ==========================================

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_WS_URL=ws://localhost:8080

# Environment
NODE_ENV=development

# ==========================================
# Database (Supabase)
# ==========================================

NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_WEBHOOK_SECRET=your_webhook_secret
SUPABASE_JWT_SECRET=your_jwt_secret

# ==========================================
# AI Configuration
# ==========================================

# OpenAI
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_URL=https://api.openai.com/v1

# AI Model Selection
AI_MODEL_PRIMARY=gpt-4-turbo-preview
AI_MODEL_SECONDARY=gpt-4
AI_MODEL_FAST=gpt-3.5-turbo
AI_MODEL_EMBEDDING=text-embedding-3-small

# AI Temperature Settings
AI_TEMP_CREATIVE=0.9
AI_TEMP_BALANCED=0.7
AI_TEMP_FOCUSED=0.5
AI_TEMP_ANALYTICAL=0.3
AI_TEMP_DETERMINISTIC=0.1

# AI Token Limits
AI_TOKEN_LIMIT_CHAPTER=10000
AI_TOKEN_LIMIT_SCENE=8000
AI_TOKEN_LIMIT_CHARACTER=6000
AI_TOKEN_LIMIT_DIALOGUE=4000
AI_TOKEN_LIMIT_SUMMARY=2000
AI_TOKEN_LIMIT_DEFAULT=4000

# AI Quality Thresholds
AI_QUALITY_EXCELLENT=95
AI_QUALITY_GOOD=90
AI_QUALITY_ACCEPTABLE=85
AI_QUALITY_POOR=70
AI_QUALITY_UNACCEPTABLE=50

# AI Minimum Quality Scores
AI_MIN_QUALITY_CHAPTER=85
AI_MIN_QUALITY_CHARACTER=90
AI_MIN_QUALITY_DIALOGUE=88
AI_MIN_QUALITY_SCENE=87
AI_MIN_QUALITY_PLOT=92

# AI Retry Configuration
AI_RETRY_MAX=3
AI_RETRY_DELAY=1000
AI_RETRY_MAX_DELAY=30000
AI_RETRY_BACKOFF=2

# Alternative AI Providers (Optional)
GENKIT_API_KEY=your_google_gemini_key
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1
XAI_API_KEY=your_xai_api_key
XAI_API_URL=https://api.x.ai/v1
NEXT_PUBLIC_AI_FALLBACK_ENABLED=true

# ==========================================
# Payment Processing (Stripe)
# ==========================================

STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_API_URL=https://api.stripe.com/v1

# Stripe Price IDs
STRIPE_PRICE_ID_BASIC=price_basic_monthly
STRIPE_PRICE_ID_PRO=price_pro_monthly
STRIPE_PRICE_ID_ENTERPRISE=price_enterprise_monthly

# ==========================================
# Rate Limiting
# ==========================================

# API Rate Limits (per hour)
API_RATE_LIMIT_DEFAULT=100
API_RATE_LIMIT_AUTH=1000
API_RATE_LIMIT_AI=30
API_RATE_LIMIT_ANALYSIS=10
API_RATE_LIMIT_WEBHOOKS=500

# Rate Limit Windows (milliseconds)
RATE_LIMIT_WINDOW=3600000
RATE_LIMIT_WINDOW_AI=3600000

# Burst Limits
BURST_LIMIT_DEFAULT=20
BURST_LIMIT_AI=5
BURST_WINDOW=60000

# ==========================================
# Performance Configuration
# ==========================================

# Timeouts (milliseconds)
TIMEOUT_API_DEFAULT=30000
TIMEOUT_AI_GENERATION=120000
TIMEOUT_FILE_UPLOAD=300000
TIMEOUT_WEBSOCKET=60000

# Batch Processing
BATCH_SIZE_DEFAULT=10
BATCH_SIZE_AI=3
BATCH_SIZE_DB=100

# Concurrency Limits
CONCURRENCY_AI_AGENTS=3
CONCURRENCY_UPLOADS=5
CONCURRENCY_EXPORTS=2

# ==========================================
# Cache Configuration
# ==========================================

# Cache TTL (seconds)
CACHE_TTL_DEFAULT=3600
CACHE_TTL_USER=300
CACHE_TTL_PROJECT=600
CACHE_TTL_AI=86400
CACHE_TTL_STATIC=604800

# Cache Size Limits
CACHE_MAX_MEMORY=104857600
CACHE_MAX_ITEMS=1000

# Cache Keys
CACHE_KEY_PREFIX=bookscribe
CACHE_KEY_VERSION=v1

# ==========================================
# Security Configuration
# ==========================================

# Session Configuration
SESSION_DURATION=86400000
SESSION_REFRESH=3600000
SESSION_MAX_AGE=604800000

# API Security
API_REQUIRE_AUTH=true
API_VALIDATE_ORIGIN=true
API_ALLOWED_ORIGINS=http://localhost:3000,https://bookscribe.ai

# Content Security
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=.txt,.doc,.docx,.md,.json
SANITIZE_HTML=true

# ==========================================
# Email Configuration
# ==========================================

# Email Addresses
EMAIL_FROM=<EMAIL>
EMAIL_SUPPORT=<EMAIL>
EMAIL_ADMIN=<EMAIL>
EMAIL_TEST=<EMAIL>

# Email Service
EMAIL_PROVIDER=resend
EMAIL_API_KEY=your_email_api_key
EMAIL_DOMAIN=bookscribe.ai

# Email Settings
EMAIL_BATCH_SIZE=100
EMAIL_RATE_LIMIT=100
EMAIL_RETRY=3

# ==========================================
# Collaboration Configuration
# ==========================================

# Session Settings
COLLAB_MAX_PARTICIPANTS=10
COLLAB_IDLE_TIMEOUT=1800000
COLLAB_LOCK_TIMEOUT=60000

# Conflict Resolution
COLLAB_CONFLICT_STRATEGY=operational-transform
COLLAB_AUTO_RESOLVE=true
COLLAB_BUFFER_WINDOW=100

# Real-time Settings
COLLAB_REALTIME_ENABLED=true
COLLAB_HEARTBEAT=30000
COLLAB_RECONNECT=5

# Collaboration Service URLs
COLLAB_SERVICE_URL=http://localhost:8081
ANALYTICS_SERVICE_URL=http://localhost:8082
EXPORT_SERVICE_URL=http://localhost:8083

# ==========================================
# Feature Flags
# ==========================================

# Core Features
FEATURE_REALTIME_COLLAB=true
FEATURE_AI_AGENTS=true
FEATURE_EXPORT_FORMATS=true
FEATURE_ANALYTICS=true

# Experimental Features
FEATURE_VOICE_NARRATION=false
FEATURE_AI_ILLUSTRATIONS=false
FEATURE_MULTIPLAYER=false
FEATURE_PLUGINS=false

# Development Features
DEBUG_MODE=false
SHOW_ERRORS=true
USE_MOCK_DATA=false
HOT_RELOAD=true

# ==========================================
# Monitoring & Analytics
# ==========================================

# Sentry Error Tracking
SENTRY_ENABLED=false
SENTRY_DSN=your_sentry_dsn
SENTRY_ENVIRONMENT=development
SENTRY_SAMPLE_RATE=1.0

# Analytics
ANALYTICS_ENABLED=false
ANALYTICS_PROVIDER=posthog
ANALYTICS_API_KEY=your_analytics_key

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_DESTINATION=console

# ==========================================
# Development Configuration
# ==========================================

# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Development Mode
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
DEV_USER_EMAIL=<EMAIL>
DEV_USER_ID=dev-user-001

# Demo Mode
NEXT_PUBLIC_DEMO_MODE=false