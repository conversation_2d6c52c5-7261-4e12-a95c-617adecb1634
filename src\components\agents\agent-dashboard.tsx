'use client'

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { VisualAgentPipeline } from './visual-agent-pipeline'
import { OrchestrationProgress } from '@/components/orchestration/orchestration-progress'
import { AgentPerformanceMetrics } from './agent-performance-metrics'
import { AgentConfigPanel } from './agent-config-panel'
import { ServiceHealthMonitor } from './service-health-monitor'
import { 
  Brain, 
  Activity, 
  Settings, 
  BarChart3, 
  Play,
  Pause,
  RefreshCw,
  AlertCircle,
  CheckCircle2
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { TIME_MS } from '@/lib/constants'

interface AgentDashboardProps {
  projectId: string
}

export function AgentDashboard({ projectId }: AgentDashboardProps) {
  const [activeTab, setActiveTab] = useState('pipeline')
  const [isOrchestrationRunning, setIsOrchestrationRunning] = useState(false)
  const [orchestrationTaskId, setOrchestrationTaskId] = useState<string | null>(null)
  const [serviceHealth, setServiceHealth] = useState<Record<string, boolean>>({})
  const { toast } = useToast()

  const startOrchestration = async () => {
    try {
      const response = await fetch('/api/orchestration/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      })

      if (response.ok) {
        const data = await response.json()
        setOrchestrationTaskId(data.taskId)
        setIsOrchestrationRunning(true)
        
        toast({
          title: 'Orchestration Started',
          description: 'AI agents are now working on your project',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to start orchestration',
        variant: 'destructive'
      })
    }
  }

  const stopOrchestration = async () => {
    if (!orchestrationTaskId) return

    try {
      const response = await fetch(`/api/orchestration/cancel/${orchestrationTaskId}`, {
        method: 'POST'
      })

      if (response.ok) {
        setIsOrchestrationRunning(false)
        setOrchestrationTaskId(null)
        
        toast({
          title: 'Orchestration Stopped',
          description: 'All agent tasks have been cancelled',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to stop orchestration',
        variant: 'destructive'
      })
    }
  }

  const checkServiceHealth = async () => {
    try {
      const response = await fetch('/api/services/health')
      if (response.ok) {
        const data = await response.json()
        setServiceHealth(data.services || {})
      }
    } catch (error) {
      logger.error('Failed to check service health:', error)
    }
  }

  // Check service health on mount
  useEffect(() => {
    checkServiceHealth()
    const interval = setInterval(checkServiceHealth, 30000) // Check every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const allServicesHealthy = Object.values(serviceHealth).every(status => status)

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Agent Control Center</CardTitle>
              <CardDescription>
                Manage and monitor AI agents for your project
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {allServicesHealthy ? (
                  <>
                    <CheckCircle2 className="h-5 w-5 text-success" />
                    <span className="text-sm text-success">All Systems Operational</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-5 w-5 text-warning" />
                    <span className="text-sm text-warning">Service Issues Detected</span>
                  </>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={checkServiceHealth}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Status
              </Button>
              
              {isOrchestrationRunning ? (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={stopOrchestration}
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Stop Agents
                </Button>
              ) : (
                <Button
                  size="sm"
                  onClick={startOrchestration}
                  disabled={!allServicesHealthy}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Agents
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Dashboard */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pipeline" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Pipeline
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Activity
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="config" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configuration
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pipeline" className="space-y-6">
          {/* Visual Pipeline */}
          <Card>
            <CardHeader>
              <CardTitle>Agent Pipeline Visualization</CardTitle>
              <CardDescription>
                Real-time view of agent processing pipeline
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VisualAgentPipeline projectId={projectId} />
            </CardContent>
          </Card>

          {/* Service Health */}
          <ServiceHealthMonitor services={serviceHealth} onRefresh={checkServiceHealth} />
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          {/* Orchestration Progress */}
          {orchestrationTaskId ? (
            <OrchestrationProgress
              taskId={orchestrationTaskId}
              onComplete={() => {
                setIsOrchestrationRunning(false)
                setOrchestrationTaskId(null)
              }}
            />
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No active orchestration. Start agents to see activity.
              </AlertDescription>
            </Alert>
          )}

          {/* Recent Agent Tasks */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Agent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <AgentActivityLog projectId={projectId} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <AgentPerformanceMetrics projectId={projectId} />
        </TabsContent>

        <TabsContent value="config" className="space-y-6">
          <AgentConfigPanel projectId={projectId} />
        </TabsContent>
      </Tabs>

      {/* Tips */}
      <Card className="bg-primary/5 border-primary/20">
        <CardHeader>
          <CardTitle className="text-lg">Agent System Tips</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p>• Agents work best when given clear project goals and structure</p>
          <p>• Multiple agents can work in parallel for faster results</p>
          <p>• Higher quality thresholds produce better content but take longer</p>
          <p>• Monitor performance metrics to optimize agent settings</p>
        </CardContent>
      </Card>
    </div>
  )
}

// Agent Activity Log Component
interface AgentActivity {
  id: string
  agent: string
  action: string
  timestamp: string
  status: 'completed' | 'running' | 'failed'
  duration: string
}

function AgentActivityLog({ projectId }: { projectId: string }) {
  const [activities, setActivities] = useState<AgentActivity[]>([])
  const [loading, setLoading] = useState(true)

  // Mock data for demonstration
  useEffect(() => {
    setTimeout(() => {
      setActivities([
        {
          id: '1',
          agent: 'Story Architect',
          action: 'Generated story structure',
          timestamp: new Date().toISOString(),
          status: 'completed',
          duration: '2m 15s'
        },
        {
          id: '2',
          agent: 'Character Developer',
          action: 'Created 5 character profiles',
          timestamp: new Date().toISOString(),
          status: 'completed',
          duration: '3m 42s'
        },
        {
          id: '3',
          agent: 'Chapter Planner',
          action: 'Planning chapter outlines',
          timestamp: new Date().toISOString(),
          status: 'running',
          duration: '1m 23s'
        }
      ])
      setLoading(false)
    }, TIME_MS.SECOND)
  }, [])

  if (loading) {
    return <div className="text-center py-8 text-muted-foreground">Loading activity...</div>
  }

  return (
    <div className="space-y-3">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center gap-3">
            <Brain className="h-5 w-5 text-primary" />
            <div>
              <p className="font-medium">{activity.agent}</p>
              <p className="text-sm text-muted-foreground">{activity.action}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-sm text-muted-foreground">{activity.duration}</span>
            <Badge variant={activity.status === 'completed' ? 'default' : 'secondary'}>
              {activity.status}
            </Badge>
          </div>
        </div>
      ))}
    </div>
  )
}