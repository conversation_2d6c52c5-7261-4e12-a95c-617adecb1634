import { useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { setUserContext, clearUserContext } from '@/lib/monitoring/sentry';

/**
 * Hook to sync authenticated user with <PERSON><PERSON> for better error tracking
 */
export function useSentryUser() {
  const { user, subscription } = useAuth();

  useEffect(() => {
    if (user) {
      // Set user context in Sentry
      setUserContext({
        id: user.id,
        email: user.email,
        username: user.user_metadata?.username,
        subscription: subscription?.status || 'free',
      });
    } else {
      // Clear user context when logged out
      clearUserContext();
    }
  }, [user, subscription]);
}