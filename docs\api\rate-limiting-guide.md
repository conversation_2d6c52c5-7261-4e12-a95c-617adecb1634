# Rate Limiting Guide

## Overview
All API endpoints should implement rate limiting to prevent abuse and ensure fair usage.

## Configuration Types

### Default
- Window: 15 minutes
- Limit: 100 requests
- Used for: Public endpoints

### Authenticated
- Window: 15 minutes  
- Limit: 1000 requests
- Used for: Protected endpoints requiring auth

### AI Generation
- Window: 60 minutes
- Limit: 20 requests
- Used for: Content generation endpoints

### AI Analysis
- Window: 60 minutes
- Limit: 50 requests
- Used for: Analysis endpoints

### Webhook
- Window: 15 minutes
- Limit: 100 requests  
- Used for: External webhook endpoints

## Implementation Examples

### Basic Rate Limiting
```typescript
import { applyRateLimit } from '@/lib/rate-limiter-unified'

export const GET = async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResponse = await applyRateLimit(request, { type: 'authenticated' })
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  
  // Continue with request processing
}
```

### Rate Limiting with Cost
```typescript
// For expensive operations
const rateLimitResponse = await applyRateLimit(request, { 
  type: 'ai-generation', 
  cost: 2 // Counts as 2 requests
})
```

### Combined with Auth
```typescript
export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Apply rate limiting after auth
  const rateLimitResponse = await applyRateLimit(request, { 
    type: 'authenticated' 
  })
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  
  // Process request
})
```

## Cost Guidelines

- **Cost 1** (default): Simple operations
- **Cost 2**: Moderate AI operations, complex queries
- **Cost 3**: Heavy AI generation, bulk operations
- **Cost 5**: Export operations, data dumps
- **Cost 10**: Admin operations, full exports

## Headers

Rate limit information is included in response headers:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp
- `Retry-After`: Seconds until retry (on 429)

## Best Practices

1. Always apply rate limiting before expensive operations
2. Use appropriate cost multipliers for resource-intensive endpoints
3. Consider different limits for authenticated vs public endpoints
4. Provide clear error messages when limits are exceeded
5. Log rate limit violations for monitoring
