import { createServerClient } from '@/lib/supabase'
import { logger } from './logger'
import type { SupabaseClient } from '@supabase/supabase-js'

interface IndexableContent {
  id: string
  type: 'chapter' | 'character' | 'location' | 'story_bible' | 'note'
  projectId: string
  title: string
  content: string
  metadata: Record<string, any>
  lastModified: string
}

interface IndexEntry {
  id: string
  projectId: string
  contentType: string
  contentId: string
  title: string
  content: string
  searchVector?: string
  metadata: Record<string, any>
  indexedAt: Date
}

export class ContentIndexingService {
  private supabase: SupabaseClient | null = null

  private async getSupabase() {
    if (!this.supabase) {
      this.supabase = await createServerClient()
    }
    return this.supabase
  }

  /**
   * Index a single chapter for embeddings
   */
  async indexChapter(chapterId: string, projectId: string, content: string): Promise<void> {
    try {
      await this.indexContent({
        id: chapterId,
        type: 'chapter',
        projectId,
        title: `Chapter`,
        content,
        metadata: {},
        lastModified: new Date().toISOString()
      })
    } catch (error) {
      logger.error('Failed to index chapter:', error)
      throw error
    }
  }

  /**
   * Index a single story bible entry for embeddings
   */
  async indexStoryBibleEntry(entryId: string, projectId: string, type: string, content: string): Promise<void> {
    try {
      await this.indexContent({
        id: entryId,
        type: 'story_bible',
        projectId,
        title: `${type} entry`,
        content,
        metadata: { entryType: type },
        lastModified: new Date().toISOString()
      })
    } catch (error) {
      logger.error('Failed to index story bible entry:', error)
      throw error
    }
  }

  /**
   * Index a single piece of content
   */
  async indexContent(content: IndexableContent): Promise<void> {
    try {
      const supabase = await this.getSupabase()
      
      // Prepare the content for indexing
      const indexEntry: Partial<IndexEntry> = {
        id: `${content.type}_${content.id}`,
        project_id: content.projectId,
        content_type: content.type,
        content_id: content.id,
        title: content.title,
        content: this.prepareContentForIndexing(content.content),
        metadata: content.metadata,
        indexed_at: new Date()
      }

      // Upsert the index entry
      const { error } = await supabase
        .from('content_index')
        .upsert(indexEntry, {
          onConflict: 'id'
        })

      if (error) {
        throw error
      }

      logger.info(`Indexed ${content.type} ${content.id}`)
    } catch (error) {
      logger.error('Failed to index content:', error)
      throw error
    }
  }

  /**
   * Index all content for a project
   */
  async indexProject(projectId: string): Promise<void> {
    try {
      const supabase = await this.getSupabase()
      
      // Index chapters
      await this.indexChapters(supabase, projectId)
      
      // Index characters
      await this.indexCharacters(supabase, projectId)
      
      // Index locations
      await this.indexLocations(supabase, projectId)
      
      // Index story bible entries
      await this.indexStoryBible(supabase, projectId)
      
      logger.info(`Completed indexing for project ${projectId}`)
    } catch (error) {
      logger.error('Failed to index project:', error)
      throw error
    }
  }

  /**
   * Remove content from index
   */
  async removeFromIndex(contentType: string, contentId: string): Promise<void> {
    try {
      const supabase = await this.getSupabase()
      
      const { error } = await supabase
        .from('content_index')
        .delete()
        .eq('content_type', contentType)
        .eq('content_id', contentId)

      if (error) {
        throw error
      }

      logger.info(`Removed ${contentType} ${contentId} from index`)
    } catch (error) {
      logger.error('Failed to remove content from index:', error)
      throw error
    }
  }

  /**
   * Update search vectors for all indexed content
   */
  async updateSearchVectors(projectId: string): Promise<void> {
    try {
      const supabase = await this.getSupabase()
      
      // This would typically call a database function that updates
      // the tsvector columns for full-text search
      const { error } = await supabase.rpc('update_search_vectors', {
        p_project_id: projectId
      })

      if (error && error.code !== '42883') { // Function doesn't exist
        throw error
      }

      logger.info(`Updated search vectors for project ${projectId}`)
    } catch (error) {
      logger.error('Failed to update search vectors:', error)
      throw error
    }
  }

  /**
   * Get indexing status for a project
   */
  async getIndexingStatus(projectId: string): Promise<{
    totalContent: number
    indexedContent: number
    lastIndexed: Date | null
    isComplete: boolean
  }> {
    try {
      const supabase = await this.getSupabase()
      
      // Count total content
      const [chapters, characters, locations, storyBible] = await Promise.all([
        supabase.from('chapters').select('id', { count: 'exact' }).eq('project_id', projectId),
        supabase.from('characters').select('id', { count: 'exact' }).eq('project_id', projectId),
        supabase.from('locations').select('id', { count: 'exact' }).eq('project_id', projectId),
        supabase.from('story_bible').select('id', { count: 'exact' }).eq('project_id', projectId)
      ])

      const totalContent = 
        (chapters.count || 0) + 
        (characters.count || 0) + 
        (locations.count || 0) + 
        (storyBible.count || 0)

      // Count indexed content
      const { count: indexedContent, data: indexData } = await supabase
        .from('content_index')
        .select('indexed_at', { count: 'exact' })
        .eq('project_id', projectId)
        .order('indexed_at', { ascending: false })
        .limit(1)

      const lastIndexed = indexData?.[0]?.indexed_at ? new Date(indexData[0].indexed_at) : null

      return {
        totalContent,
        indexedContent: indexedContent || 0,
        lastIndexed,
        isComplete: totalContent === (indexedContent || 0)
      }
    } catch (error) {
      logger.error('Failed to get indexing status:', error)
      throw error
    }
  }

  // Private helper methods
  private prepareContentForIndexing(content: string): string {
    // Remove excessive whitespace and clean up the content
    return content
      .replace(/\s+/g, ' ')
      .trim()
      .slice(0, 10000) // Limit content length for indexing
  }

  private async indexChapters(supabase: SupabaseClient, projectId: string): Promise<void> {
    const { data: chapters, error } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .eq('is_archived', false)

    if (error) throw error

    for (const chapter of chapters || []) {
      await this.indexContent({
        id: chapter.id,
        type: 'chapter',
        projectId: chapter.project_id,
        title: chapter.title || `Chapter ${chapter.chapter_number}`,
        content: chapter.content || '',
        metadata: {
          chapterNumber: chapter.chapter_number,
          wordCount: chapter.word_count,
          status: chapter.status
        },
        lastModified: chapter.updated_at
      })
    }
  }

  private async indexCharacters(supabase: SupabaseClient, projectId: string): Promise<void> {
    const { data: characters, error } = await supabase
      .from('characters')
      .select('*')
      .eq('project_id', projectId)

    if (error) throw error

    for (const character of characters || []) {
      const content = [
        character.name,
        character.description,
        character.backstory,
        character.personality,
        character.appearance
      ].filter(Boolean).join(' ')

      await this.indexContent({
        id: character.id,
        type: 'character',
        projectId: character.project_id,
        title: character.name,
        content,
        metadata: {
          role: character.role,
          traits: character.traits,
          goals: character.goals
        },
        lastModified: character.updated_at
      })
    }
  }

  private async indexLocations(supabase: SupabaseClient, projectId: string): Promise<void> {
    const { data: locations, error } = await supabase
      .from('locations')
      .select('*')
      .eq('project_id', projectId)

    if (error) throw error

    for (const location of locations || []) {
      const content = [
        location.name,
        location.description,
        location.significance,
        location.features
      ].filter(Boolean).join(' ')

      await this.indexContent({
        id: location.id,
        type: 'location',
        projectId: location.project_id,
        title: location.name,
        content,
        metadata: {
          locationType: location.location_type,
          parentLocationId: location.parent_location_id
        },
        lastModified: location.updated_at
      })
    }
  }

  private async indexStoryBible(supabase: SupabaseClient, projectId: string): Promise<void> {
    const { data: entries, error } = await supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId)

    if (error) throw error

    for (const entry of entries || []) {
      const content = [
        entry.entry_key,
        JSON.stringify(entry.entry_data)
      ].filter(Boolean).join(' ')

      await this.indexContent({
        id: entry.id,
        type: 'story_bible',
        projectId: entry.project_id,
        title: entry.entry_key || entry.entry_type,
        content,
        metadata: {
          entryType: entry.entry_type,
          entryData: entry.entry_data
        },
        lastModified: entry.updated_at
      })
    }
  }
}

// Export singleton instance
export const contentIndexingService = new ContentIndexingService()