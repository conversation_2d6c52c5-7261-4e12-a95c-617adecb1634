# BookScribe Locations System

## Overview

The Locations System enables authors to create, manage, and maintain consistency for all locations within their stories. It supports hierarchical location structures, detailed descriptions, and integration with timeline events and character movements.

## Architecture

### Database Schema

#### Locations Table
Core location storage with hierarchical support:

```sql
locations:
  - id: UUID (Primary Key)
  - universe_id: UUID - References universes
  - series_id: UUID - References series (optional)
  - project_id: UUID - References projects (optional)
  - name: VARCHAR(255) - Location name
  - description: TEXT - Detailed description
  - location_type: VARCHAR(50) - planet, continent, country, city, building, room
  - parent_location_id: UUID - References locations (hierarchical)
  - coordinates: JSONB - Map/grid coordinates
  - attributes: JSONB - Custom properties
  - time_period: JSONB - When location exists
  - climate: VARCHAR(50) - Climate type
  - population: INTEGER - Population size
  - culture: TEXT - Cultural description
  - significance: VARCHAR(20) - minor, notable, major, critical
  - images: TEXT[] - Image URLs
  - is_fictional: BOOLEAN - Real or fictional
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Location Metadata Table
Additional location information:

```sql
location_metadata:
  - id: UUID (Primary Key)
  - location_id: UUID - References locations
  - metadata_type: VARCHAR(50) - map, floorplan, history, politics
  - content: JSONB - Type-specific data
  - version: INTEGER
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

## Location Types & Hierarchy

### Hierarchical Structure
```
Universe
  └── Planet/World
      └── Continent
          └── Country/Kingdom
              └── Region/Province
                  └── City/Town
                      └── District/Neighborhood
                          └── Building
                              └── Room/Area
```

### Location Types

#### 1. World-Level
- Planets/Worlds
- Continents
- Oceans/Seas
- Major geographical features

#### 2. Political Divisions
- Countries/Kingdoms
- States/Provinces
- Counties/Shires
- Municipalities

#### 3. Settlements
- Cities
- Towns
- Villages
- Outposts

#### 4. Structures
- Buildings
- Landmarks
- Infrastructure
- Natural features

#### 5. Interior Spaces
- Rooms
- Areas
- Specific locations

## Location Attributes

### Standard Attributes
```json
{
  "attributes": {
    "size": {
      "area": 5000,
      "unit": "sq_km",
      "comparison": "About the size of Delaware"
    },
    "climate": {
      "type": "temperate",
      "seasons": ["spring", "summer", "fall", "winter"],
      "average_temp": {
        "summer": 25,
        "winter": -5,
        "unit": "celsius"
      }
    },
    "demographics": {
      "population": 1500000,
      "density": 300,
      "growth_rate": 0.02,
      "species_distribution": {
        "human": 0.7,
        "elf": 0.2,
        "dwarf": 0.1
      }
    },
    "economy": {
      "primary_industries": ["agriculture", "mining", "trade"],
      "currency": "gold crowns",
      "wealth_level": "moderate"
    },
    "governance": {
      "type": "monarchy",
      "ruler": "King Aldric III",
      "established": "1247 AE"
    },
    "culture": {
      "languages": ["Common", "Elvish"],
      "religions": ["Church of Light", "Old Ways"],
      "traditions": ["Harvest Festival", "Midwinter Celebration"]
    }
  }
}
```

### Custom Attributes
Authors can define custom attributes:
```json
{
  "magic_level": "high",
  "danger_rating": 7,
  "accessibility": "restricted",
  "notable_features": ["Crystal Caves", "Ancient Library"],
  "story_significance": "Main character's birthplace"
}
```

## API Endpoints

### Location Management

#### GET /api/projects/{id}/locations
Retrieve project locations:

```typescript
// Request
GET /api/projects/{id}/locations?type=city&parent_id=uuid

// Response
{
  locations: [
    {
      id: "uuid",
      name: "Dragonhearth",
      location_type: "city",
      parent_location: {
        id: "uuid",
        name: "Kingdom of Aetheria"
      },
      children_count: 12,
      description: "The capital city...",
      attributes: {...},
      significance: "major",
      usage_count: 45
    }
  ],
  total: 23,
  hierarchy: {
    // Nested location tree
  }
}
```

#### POST /api/projects/{id}/locations
Create new location:

```typescript
// Request
{
  name: "The Crystal Tavern",
  location_type: "building",
  parent_location_id: "uuid",
  description: "A popular gathering place...",
  attributes: {
    size: "medium",
    capacity: 50,
    notable_features: ["Crystal chandelier", "Secret basement"]
  }
}
```

#### PUT /api/locations/{id}
Update location details:

```typescript
// Request
{
  description: "Updated description...",
  attributes: {
    ...existing,
    new_attribute: "value"
  }
}
```

### Location Search

#### POST /api/search/locations
Advanced location search:

```typescript
// Request
{
  query: "tavern",
  filters: {
    location_type: ["building"],
    has_parent: true,
    significance: ["notable", "major"]
  },
  universe_id: "uuid"
}

// Response
{
  results: [
    {
      id: "uuid",
      name: "The Crystal Tavern",
      path: "Aetheria > Dragonhearth > Market District > The Crystal Tavern",
      relevance_score: 0.95
    }
  ]
}
```

### Location Analytics

#### GET /api/locations/{id}/usage
Track location usage:

```typescript
// Response
{
  location_id: "uuid",
  usage_stats: {
    total_mentions: 156,
    chapters: [1, 3, 7, 12],
    characters_visited: ["char_1", "char_2"],
    events_occurred: 12,
    last_used: "2024-01-15T10:30:00Z"
  },
  timeline: [
    {
      date: "story_date",
      event: "Character arrival",
      chapter: 3
    }
  ]
}
```

## Location Features

### Map Integration
```typescript
interface LocationMap {
  id: string;
  location_id: string;
  map_type: 'world' | 'region' | 'city' | 'building';
  image_url?: string;
  interactive_data?: {
    clickable_areas: MapArea[];
    zoom_levels: number[];
  };
  scale?: {
    value: number;
    unit: 'km' | 'miles' | 'meters';
  };
}
```

### Travel Calculations
```typescript
interface TravelRoute {
  from: string; // location_id
  to: string; // location_id
  distance: number;
  travel_time: {
    on_foot: number;
    horseback: number;
    vehicle: number;
    custom: Record<string, number>;
  };
  route_description: string;
  dangers?: string[];
}
```

### Climate & Weather
```typescript
interface LocationClimate {
  base_climate: 'arctic' | 'temperate' | 'tropical' | 'desert' | 'custom';
  seasons: Season[];
  current_weather?: {
    temperature: number;
    conditions: string;
    story_impact?: string;
  };
}
```

## UI Components

### Location Manager
```tsx
<LocationManager
  projectId={projectId}
  viewMode="hierarchy" | "map" | "list"
  onLocationSelect={handleSelect}
  allowCreate={true}
/>
```

### Location Tree
```tsx
<LocationTree
  rootLocation={worldLocation}
  expandedNodes={expanded}
  onNodeClick={handleNodeClick}
  showCounts={true}
/>
```

### Location Inspector
```tsx
<LocationInspector
  location={selectedLocation}
  showUsageStats={true}
  showRelatedEvents={true}
  editable={canEdit}
/>
```

### Location Map
```tsx
<LocationMap
  locations={locations}
  centerOn={currentLocation}
  showPaths={true}
  interactive={true}
  onLocationClick={handleMapClick}
/>
```

## Integration Features

### Character Movement Tracking
Track character locations over time:
```typescript
interface CharacterLocation {
  character_id: string;
  location_id: string;
  arrival_chapter: number;
  departure_chapter?: number;
  reason: string;
}
```

### Event Integration
Link locations to timeline events:
```typescript
interface LocationEvent {
  location_id: string;
  event_id: string;
  event_type: string;
  impact: 'minor' | 'major' | 'devastating';
}
```

### Consistency Checking
Validate location usage:
- Character travel time feasibility
- Location existence during events
- Capacity constraints
- Environmental consistency

## Performance Optimization

### Indexes
```sql
CREATE INDEX idx_locations_universe ON locations(universe_id);
CREATE INDEX idx_locations_series ON locations(series_id);
CREATE INDEX idx_locations_parent ON locations(parent_location_id);
CREATE INDEX idx_locations_type ON locations(location_type);
CREATE INDEX idx_locations_significance ON locations(significance);
```

### Caching
- Location hierarchy cached
- Frequently accessed locations in memory
- Map data cached client-side

## Security

### Row Level Security
```sql
-- Users can view locations in their universes/projects
CREATE POLICY "View locations" ON locations
  FOR SELECT USING (
    universe_id IN (
      SELECT universe_id FROM universe_members 
      WHERE user_id = auth.uid()
    ) OR
    project_id IN (
      SELECT id FROM projects 
      WHERE user_id = auth.uid()
    )
  );
```

## Future Enhancements

1. **3D Visualization**
   - 3D city/building models
   - Virtual tours
   - AR location viewing

2. **Advanced Mapping**
   - Custom map drawing tools
   - Terrain generation
   - Political boundary editor

3. **Location AI**
   - Auto-generate descriptions
   - Suggest location names
   - Climate/culture generation

4. **Reader Features**
   - Interactive story maps
   - Location glossary
   - Journey tracking

## Related Systems
- Timeline Events (location-based events)
- Character Management (character locations)
- Universe Management (location organization)
- Plot Threads (location significance)