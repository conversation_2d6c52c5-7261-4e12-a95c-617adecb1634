import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { UnifiedResponse } from '@/lib/utils/response'
import { getMemoryManager } from '@/lib/memory/memory-instances'
import { logger } from '@/lib/services/logger'

const querySchema = z.object({
  projectId: z.string().uuid()
})

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    
    if (!projectId) {
      return UnifiedResponse.error('Project ID is required', 400)
    }

    const validation = querySchema.safeParse({ projectId })
    if (!validation.success) {
      return UnifiedResponse.error('Invalid project ID format', 400)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Get memory stats from memory manager
    const memoryManager = getMemoryManager(projectId)
    const stats = memoryManager.getMemoryStats()
    
    return UnifiedResponse.success({
      projectId,
      ...stats
    })
  } catch (error) {
    logger.error('Memory stats error:', error)
    return UnifiedResponse.error('Failed to fetch memory statistics')
  }
})

