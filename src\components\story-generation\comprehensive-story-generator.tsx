'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { CheckCircle, Clock, AlertCircle, BookOpen, Users, Map, FileText } from 'lucide-react'
import type { GeneratedStoryStructure, GeneratedCharacter, GeneratedChapter } from '@/lib/services/comprehensive-story-generator'
import type { StoryBible } from '@/lib/agents/types'

interface StoryGenerationOptions {
  includeCharacters: boolean
  includeWorldBuilding: boolean
  includeStoryBible: boolean
  regenerateExisting: boolean
}

interface GenerationResult {
  structure?: GeneratedStoryStructure
  characters?: GeneratedCharacter[]
  chapters?: GeneratedChapter[]
  storyBible?: Partial<StoryBible>
}

interface ComprehensiveStoryGeneratorProps {
  projectId: string
  onComplete?: (result: GenerationResult) => void
}

export function ComprehensiveStoryGenerator({ projectId, onComplete }: ComprehensiveStoryGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState('')
  const [result, setResult] = useState<GenerationResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [options, setOptions] = useState<StoryGenerationOptions>({
    includeCharacters: true,
    includeWorldBuilding: true,
    includeStoryBible: true,
    regenerateExisting: false
  })

  const generateStory = async () => {
    setIsGenerating(true)
    setProgress(0)
    setError(null)
    setResult(null)

    try {
      setCurrentStep('Initializing story generation...')
      setProgress(10)

      const response = await fetch('/api/agents/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          action: 'generate_complete_story',
          options
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate story')
      }

      setCurrentStep('Processing generation results...')
      setProgress(90)

      const data = await response.json()
      
      if (data.success) {
        setResult(data.data)
        setProgress(100)
        setCurrentStep('Story generation complete!')
        onComplete?.(data.data)
      } else {
        throw new Error(data.error || 'Generation failed')
      }
    } catch (err) {
      logger.error('Story generation error:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  const renderGenerationProgress = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Generating Your Story
        </CardTitle>
        <CardDescription>
          Creating a comprehensive story structure, characters, and chapter outlines
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>{currentStep}</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-success" />
            <span>Story Structure</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-success" />
            <span>Character Development</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-success" />
            <span>Chapter Outlines</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-success" />
            <span>Story Bible</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderResults = () => {
    if (!result) return null

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-success" />
            Story Generation Complete
          </CardTitle>
          <CardDescription>
            Your comprehensive story has been generated and saved to your project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="structure">Structure</TabsTrigger>
              <TabsTrigger value="characters">Characters</TabsTrigger>
              <TabsTrigger value="chapters">Chapters</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{result.structure?.acts?.length || 0}</div>
                  <div className="text-sm text-muted-foreground">Acts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{result.characters?.length || 0}</div>
                  <div className="text-sm text-muted-foreground">Characters</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{result.chapters?.length || 0}</div>
                  <div className="text-sm text-muted-foreground">Chapters</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{result.structure?.themes?.length || 0}</div>
                  <div className="text-sm text-muted-foreground">Themes</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold">Story Premise</h4>
                <p className="text-sm text-muted-foreground">{result.structure?.premise}</p>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold">Themes</h4>
                <div className="flex flex-wrap gap-2">
                  {result.structure?.themes?.map((theme: string, index: number) => (
                    <Badge key={index} variant="secondary">{theme}</Badge>
                  ))}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="structure" className="space-y-4">
              <div className="space-y-4">
                {result.structure?.acts?.map((act, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="text-lg">{act.title}</CardTitle>
                      <CardDescription>{act.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="text-sm">
                          <strong>Chapters:</strong> {act.chapters?.join(', ')}
                        </div>
                        <div className="text-sm">
                          <strong>Word Count Target:</strong> {act.wordCountTarget?.toLocaleString()}
                        </div>
                        <div className="text-sm">
                          <strong>Key Events:</strong>
                          <ul className="list-disc list-inside mt-1">
                            {act.keyEvents?.map((event: string, eventIndex: number) => (
                              <li key={eventIndex}>{event}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="characters" className="space-y-4">
              <div className="grid gap-4">
                {result.characters?.map((character, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {character.name}
                        <Badge variant={character.role === 'protagonist' ? 'default' : 'secondary'}>
                          {character.role}
                        </Badge>
                      </CardTitle>
                      <CardDescription>{character.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div>
                          <strong>Character Arc:</strong> {character.characterArc}
                        </div>
                        {character.personality?.traits && (
                          <div>
                            <strong>Key Traits:</strong> {character.personality.traits.join(', ')}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="chapters" className="space-y-4">
              <div className="grid gap-4">
                {result.chapters?.slice(0, 5).map((chapter, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        {chapter.title}
                      </CardTitle>
                      <CardDescription>
                        Target: {chapter.wordCountTarget?.toLocaleString()} words
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{chapter.summary}</p>
                    </CardContent>
                  </Card>
                ))}
                {result.chapters?.length > 5 && (
                  <div className="text-center text-sm text-muted-foreground">
                    And {result.chapters.length - 5} more chapters...
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            Generation Failed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => setError(null)} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (isGenerating) {
    return renderGenerationProgress()
  }

  if (result) {
    return renderResults()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Comprehensive Story Generator
        </CardTitle>
        <CardDescription>
          Generate a complete story structure with characters, chapters, and world-building
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h4 className="font-semibold">Generation Options</h4>
          <div className="space-y-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.includeCharacters}
                onChange={(e) => setOptions(prev => ({ ...prev, includeCharacters: e.target.checked }))}
                className="rounded"
              />
              <span className="text-sm">Generate detailed character profiles</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.includeWorldBuilding}
                onChange={(e) => setOptions(prev => ({ ...prev, includeWorldBuilding: e.target.checked }))}
                className="rounded"
              />
              <span className="text-sm">Include world-building elements</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.includeStoryBible}
                onChange={(e) => setOptions(prev => ({ ...prev, includeStoryBible: e.target.checked }))}
                className="rounded"
              />
              <span className="text-sm">Create comprehensive story bible</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.regenerateExisting}
                onChange={(e) => setOptions(prev => ({ ...prev, regenerateExisting: e.target.checked }))}
                className="rounded"
              />
              <span className="text-sm">Regenerate existing content</span>
            </label>
          </div>
        </div>

        <Button onClick={generateStory} className="w-full" size="lg">
          Generate Complete Story
        </Button>
      </CardContent>
    </Card>
  )
}
