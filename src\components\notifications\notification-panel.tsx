'use client'

import { useState, useEffect } from 'react'
import { <PERSON>et, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>etHeader, Sheet<PERSON><PERSON>le, SheetDescription } from '@/components/ui/sheet'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { NotificationCard } from './notification-card'
import { CheckCheck, Settings, Loader2 } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { useRouter } from 'next/navigation'

interface Notification {
  id: string
  type: string
  title: string
  message: string
  data?: any
  read: boolean
  read_at?: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  expires_at?: string
  action_url?: string
  action_type?: 'navigate' | 'modal' | 'external'
  created_at: string
  updated_at: string
}

interface NotificationPanelProps {
  isOpen: boolean
  onClose: () => void
  onNotificationRead?: () => void
}

export function NotificationPanel({ 
  isOpen, 
  onClose,
  onNotificationRead 
}: NotificationPanelProps) {
  const router = useRouter()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all')
  const [markingAllRead, setMarkingAllRead] = useState(false)

  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
  }, [isOpen, activeTab])

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (activeTab === 'unread') {
        params.append('unread', 'true')
      }
      params.append('limit', '50')
      
      const response = await fetch(`/api/notifications?${params}`)
      if (response.ok) {
        const data = await response.json()
        setNotifications(data.notifications || [])
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST'
      })
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => 
            n.id === notificationId ? { ...n, read: true, read_at: new Date().toISOString() } : n
          )
        )
        onNotificationRead?.()
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      setMarkingAllRead(true)
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'POST'
      })
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => ({ ...n, read: true, read_at: new Date().toISOString() }))
        )
        onNotificationRead?.()
      }
    } catch (error) {
      console.error('Error marking all as read:', error)
    } finally {
      setMarkingAllRead(false)
    }
  }

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read if unread
    if (!notification.read) {
      await markAsRead(notification.id)
    }

    // Handle action
    if (notification.action_url) {
      if (notification.action_type === 'external') {
        window.open(notification.action_url, '_blank')
      } else {
        router.push(notification.action_url)
        onClose()
      }
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId))
      }
    } catch (error) {
      console.error('Error deleting notification:', error)
    }
  }

  const groupNotificationsByDate = (notifications: Notification[]) => {
    const groups: { [key: string]: Notification[] } = {}
    
    notifications.forEach(notification => {
      const date = new Date(notification.created_at)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      let key: string
      if (date.toDateString() === today.toDateString()) {
        key = 'Today'
      } else if (date.toDateString() === yesterday.toDateString()) {
        key = 'Yesterday'
      } else {
        key = date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })
      }
      
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(notification)
    })
    
    return groups
  }

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle>Notifications</SheetTitle>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  disabled={markingAllRead}
                >
                  {markingAllRead ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCheck className="h-4 w-4" />
                  )}
                  <span className="ml-2">Mark all read</span>
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push('/settings/notifications')}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <SheetDescription>
            Stay updated with your writing progress and collaborations
          </SheetDescription>
        </SheetHeader>

        <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'all' | 'unread')} className="mt-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="unread">
              Unread {unreadCount > 0 && `(${unreadCount})`}
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : notifications.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {activeTab === 'unread' ? 'No unread notifications' : 'No notifications yet'}
              </div>
            ) : (
              <ScrollArea className="h-[calc(100vh-200px)]">
                <div className="space-y-4">
                  {Object.entries(groupNotificationsByDate(notifications)).map(([date, dateNotifications]) => (
                    <div key={date}>
                      <h4 className="text-sm font-medium text-muted-foreground mb-2">
                        {date}
                      </h4>
                      <div className="space-y-2">
                        {dateNotifications.map((notification) => (
                          <NotificationCard
                            key={notification.id}
                            notification={notification}
                            onClick={() => handleNotificationClick(notification)}
                            onDelete={() => deleteNotification(notification.id)}
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>
      </SheetContent>
    </Sheet>
  )
}