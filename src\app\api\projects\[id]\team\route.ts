import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { withProjectAccess } from '@/lib/api/auth-helpers'
import { logger } from '@/lib/services/logger'

export const GET = withProjectAccess(async (
  req: NextRequest,
  { params }: { params: { id: string } }
) => {
  const projectId = params.id
  
  try {
    const supabase = createTypedServerClient()
    
    // Get project details
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id, title')
      .eq('id', projectId)
      .single()
    
    if (projectError) throw projectError
    
    // Get team members (owner + collaborators)
    const members = []
    
    // Add project owner
    const { data: owner } = await supabase
      .from('users')
      .select('id, email, profiles(display_name, avatar_url)')
      .eq('id', project.user_id)
      .single()
    
    if (owner) {
      members.push({
        id: owner.id,
        email: owner.email,
        name: owner.profiles?.display_name || owner.email,
        avatar_url: owner.profiles?.avatar_url,
        role: 'owner',
        status: 'active',
        joined_at: new Date().toISOString(),
        permissions: {
          can_write: true,
          can_manage_team: true,
          can_export: true,
          can_delete: true
        }
      })
    }
    
    // Get collaborators
    const { data: collaborators } = await supabase
      .from('project_collaborators')
      .select(`
        *,
        user:users!user_id(
          id,
          email,
          profiles(display_name, avatar_url)
        )
      `)
      .eq('project_id', projectId)
      .eq('status', 'active')
    
    if (collaborators) {
      collaborators.forEach(collab => {
        members.push({
          id: collab.user.id,
          email: collab.user.email,
          name: collab.user.profiles?.display_name || collab.user.email,
          avatar_url: collab.user.profiles?.avatar_url,
          role: collab.role,
          status: 'active',
          joined_at: collab.created_at,
          permissions: {
            can_write: collab.role === 'editor',
            can_manage_team: false,
            can_export: true,
            can_delete: false
          }
        })
      })
    }
    
    // Get pending invitations
    const { data: invitations } = await supabase
      .from('project_invitations')
      .select('email, role, created_at')
      .eq('project_id', projectId)
      .eq('status', 'pending')
      .gte('expires_at', new Date().toISOString())
    
    const pendingInvites = invitations?.map(inv => inv.email) || []
    
    return NextResponse.json({
      members,
      pendingInvites
    })
    
  } catch (error) {
    logger.error('Error loading team members', error)
    return NextResponse.json(
      { error: 'Failed to load team members' },
      { status: 500 }
    )
  }
}, 'viewer')

export const DELETE = withProjectAccess(async (
  req: NextRequest,
  { params }: { params: { id: string } }
) => {
  const projectId = params.id
  
  try {
    const { memberId } = await req.json()
    
    if (!memberId) {
      return handleAPIError(new ValidationError('Invalid request'))
    }
    
    const supabase = createTypedServerClient()
    
    // Remove collaborator
    const { error } = await supabase
      .from('project_collaborators')
      .delete()
      .eq('project_id', projectId)
      .eq('user_id', memberId)
    
    if (error) throw error
    
    logger.info('Team member removed', {
      projectId,
      memberId
    })
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    logger.error('Error removing team member', error)
    return NextResponse.json(
      { error: 'Failed to remove team member' },
      { status: 500 }
    )
  }
}, 'owner')