import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { orchestratorInstances } from '@/lib/agents/orchestrator-instances';
import { createServerSupabaseClient } from '@/lib/supabase';
import type { ProjectSettings } from '@/lib/types/project-settings';
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware';
import { z } from 'zod';
import { baseSchemas } from '@/lib/validation/common-schemas';
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

// Validation schema for orchestration start
const orchestrationStartSchema = z.object({
  projectId: baseSchemas.uuid,
  projectSelections: z.object({
    genre: z.string().min(1).max(50),
    tone: z.string().min(1).max(50),
    style: z.string().min(1).max(50),
    pov: z.string().min(1).max(50),
    tense: z.string().min(1).max(50),
    themes: z.array(z.string()).max(10).optional(),
    subgenres: z.array(z.string()).max(10).optional()
  }),
  storyPrompt: baseSchemas.description.min(10).max(5000),
  targetWordCount: baseSchemas.positiveInt.max(500000).optional().default(80000),
  targetChapters: baseSchemas.positiveInt.max(100).optional().default(20)
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: orchestrationStartSchema,
    rateLimitKey: 'authenticated',
    rateLimitCost: 10, // High cost for orchestration
    maxBodySize: 10 * 1024, // 10KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const body = await req.json();
      const user = req.user;
      
      if (!user) return { valid: false, error: 'Authentication required' };

      // Verify user owns the project
      const project = await verifyProjectAccess(body.projectId, user.id)
      if (!project) {
        return { valid: false, error: PROJECT_ACCESS_ERROR }
      }

      // Check if orchestration is already running
      if (orchestratorInstances.has(body.projectId)) {
        return { valid: false, error: 'Orchestration already running for this project' };
      }

      // Check project status
      if (project.status === 'outlined' || project.status === 'writing') {
        return { valid: false, error: 'Project already has content generated' };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const body = context.body;

  try {
    const { 
      projectId, 
      projectSelections, 
      storyPrompt, 
      targetWordCount, 
      targetChapters 
    } = body;


    // Create new orchestrator instance
    const orchestrator = new AdvancedAgentOrchestrator(3); // 3 concurrent tasks
    orchestratorInstances.set(projectId, orchestrator);

    // Set up event listeners for progress tracking
    orchestrator.on('orchestration:started', (data) => {
      logger.info(`=� Orchestration started for project ${projectId}:`, data);
    });

    orchestrator.on('task:started', (data) => {
      logger.info(`� Task started: ${data.taskId} (${data.type})`);
    });

    orchestrator.on('task:completed', (data) => {
      logger.info(` Task completed: ${data.taskId}`);
    });

    orchestrator.on('task:failed', (data) => {
      logger.info(`L Task failed: ${data.taskId} - ${data.error}`);
    });

    orchestrator.on('orchestration:completed', async (data) => {
      logger.info(`<� Orchestration completed for project ${projectId}:`, data);
      
      // Clean up the instance after completion
      setTimeout(() => {
        orchestratorInstances.delete(projectId);
      }, 30000); // Keep for 30 seconds for final status checks
    });

    orchestrator.on('orchestration:cancelled', () => {
      logger.info(`=� Orchestration cancelled for project ${projectId}`);
      orchestratorInstances.delete(projectId);
    });

    // Start orchestration asynchronously
    const orchestrationPromise = orchestrator.orchestrateProject(
      projectId,
      projectSelections as ProjectSettings,
      storyPrompt,
      targetWordCount || 80000,
      targetChapters || 20
    );

    // Don't await the orchestration - let it run in background
    orchestrationPromise.then(async (result) => {
      if (result.success && result.data) {
        // Store the completed book context in the database
        try {
          const supabase = await createServerSupabaseClient();
          const { error: updateError } = await supabase
            .from('projects')
            .update({
              book_context: result.data,
              status: 'outlined',
              updated_at: new Date().toISOString()
            })
            .eq('id', projectId);

          if (updateError) {
            logger.error('Failed to update project with book context:', updateError);
          } else {
            logger.info(`=� Book context saved for project ${projectId}`);
          }
        } catch (error) {
          logger.error('Error saving book context:', error);
        }
      }
    }).catch((error) => {
      logger.error(`Orchestration failed for project ${projectId}:`, error);
    });

    logger.info('Orchestration started successfully', {
      projectId,
      userId: user.id,
      wordCount: targetWordCount,
      chapters: targetChapters,
      clientIP: context.clientIP
    });

    return NextResponse.json({
      success: true,
      message: 'Orchestration started',
      projectId,
      estimatedDuration: '10-15 minutes',
      progressEndpoint: `/api/orchestration/progress?projectId=${projectId}`
    });

  } catch (error) {
    logger.error('Error starting orchestration:', error, {
      projectId: body?.projectId,
      userId: user.id,
      clientIP: context.clientIP
    });
    
    return NextResponse.json(
      { error: 'Failed to start orchestration' },
      { status: 500 }
    );
  }
});