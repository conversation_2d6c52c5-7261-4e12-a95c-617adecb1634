import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { format } from 'date-fns'
import { TIME_MS } from '@/lib/constants'
import { logger } from '@/lib/services/logger'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'

// Query validation schema
const sessionsQuerySchema = z.object({
  projectId: baseSchemas.uuid.optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}/, 'Invalid date format').optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}/, 'Invalid date format').optional(),
  type: z.enum(['overview', 'hourly', 'weekly', 'daily']).optional().default('overview')
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before or equal to end date'
});

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId'),
    startDate: searchParams.get('startDate'),
    endDate: searchParams.get('endDate'),
    type: searchParams.get('type')
  };

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    querySchema: sessionsQuerySchema,
    rateLimitKey: 'analytics-sessions',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // If projectId provided, verify access
      if (queryParams.projectId) {
        const supabase = await createTypedServerClient();
        const { data: project } = await supabase
          .from('projects')
          .select('id')
          .eq('id', queryParams.projectId)
          .eq('user_id', user.id)
          .single();
        
        if (!project) {
          const { data: collaborator } = await supabase
            .from('project_collaborators')
            .select('role')
            .eq('project_id', queryParams.projectId)
            .eq('user_id', user.id)
            .eq('status', 'active')
            .single();
          
          if (!collaborator) {
            return { valid: false, error: 'Access denied to this project' };
          }
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, startDate, endDate, type } = context.query;

  try {

    const supabase = await createTypedServerClient()

    // Build base query
    let query = supabase
      .from('writing_sessions')
      .select('*')
      .eq('user_id', user.id)

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data: sessions, error } = await query
      .order('created_at', { ascending: false })
      .limit(5000); // Limit to prevent excessive data

    if (error) {
      logger.error('Failed to fetch writing sessions:', error, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to fetch session analytics');
    }

    const response: any = {
      sessions,
      count: sessions.length
    }

    // Calculate different analytics based on type
    switch (type) {
      case 'overview':
        const totalWords = sessions.reduce((sum, s) => sum + (s.word_count || 0), 0)
        const totalDuration = sessions.reduce((sum, s) => sum + (s.duration || 0), 0)
        const avgSessionDuration = sessions.length > 0 ? Math.round(totalDuration / sessions.length / 60) : 0
        const avgWordsPerSession = sessions.length > 0 ? Math.round(totalWords / sessions.length) : 0

        response.overview = {
          totalWords,
          totalSessions: sessions.length,
          totalDuration: Math.round(totalDuration / 60), // in minutes
          avgSessionDuration, // in minutes
          avgWordsPerSession,
          avgWordsPerMinute: totalDuration > 0 ? Math.round(totalWords / (totalDuration / 60)) : 0
        }
        break

      case 'hourly':
        const hourlyData = new Array(24).fill(0).map((_, hour) => ({
          hour: `${hour}:00`,
          words: 0,
          sessions: 0
        }))

        sessions.forEach(session => {
          const hour = new Date(session.created_at).getHours()
          hourlyData[hour].words += session.word_count || 0
          hourlyData[hour].sessions += 1
        })

        response.hourlyPattern = hourlyData.map(h => ({
          date: h.hour,
          value: h.words
        }))
        break

      case 'weekly':
        const weeklyData = new Array(7).fill(0).map((_, day) => ({
          day: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][day],
          words: 0,
          sessions: 0
        }))

        sessions.forEach(session => {
          const day = new Date(session.created_at).getDay()
          weeklyData[day].words += session.word_count || 0
          weeklyData[day].sessions += 1
        })

        response.weeklyPattern = weeklyData.map(w => ({
          date: w.day,
          value: w.words
        }))
        break

      case 'daily':
        const dailyMap = new Map<string, { words: number; sessions: number }>()
        
        sessions.forEach(session => {
          const date = format(new Date(session.created_at), 'yyyy-MM-dd')
          const existing = dailyMap.get(date) || { words: 0, sessions: 0 }
          dailyMap.set(date, {
            words: existing.words + (session.word_count || 0),
            sessions: existing.sessions + 1
          })
        })

        response.dailyData = Array.from(dailyMap.entries())
          .map(([date, data]) => ({
            date: format(new Date(date), 'MMM dd'),
            value: data.words,
            sessions: data.sessions
          }))
          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        break
    }

    // Calculate writing streak
    const uniqueDays = new Set(
      sessions.map(s => format(new Date(s.created_at), 'yyyy-MM-dd'))
    )
    const sortedDays = Array.from(uniqueDays).sort().reverse()
    let streak = 0
    const today = format(new Date(), 'yyyy-MM-dd')
    
    for (let i = 0; i < sortedDays.length; i++) {
      const expectedDate = format(
        new Date(Date.now() - i * 24 * 60 * 60 * TIME_MS.SECOND),
        'yyyy-MM-dd'
      )
      if (sortedDays[i] === expectedDate) {
        streak++
      } else {
        break
      }
    }

    response.streak = streak

    // Fetch user streak data
    const { data: streakData } = await supabase
      .from('user_streaks')
      .select('current_streak, longest_streak')
      .eq('user_id', user.id)
      .single()

    response.currentStreak = streakData?.current_streak || streak
    response.longestStreak = streakData?.longest_streak || 0

    logger.info('Session analytics fetched', {
      userId: user.id,
      projectId,
      type,
      sessionCount: sessions.length,
      streak: response.streak,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success(response);

  } catch (error) {
    logger.error('Session analytics error:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to generate session analytics');
  }
});

// Body validation schema for POST
const createSessionSchema = z.object({
  projectId: baseSchemas.uuid,
  wordCount: z.number().int().min(0).max(50000),
  duration: z.number().min(0).max(86400).optional(), // Max 24 hours in seconds
  sessionType: z.enum(['writing', 'editing', 'planning', 'research']).optional().default('writing'),
  metadata: z.record(z.unknown()).optional()
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: createSessionSchema,
    rateLimitKey: 'session-create',
    rateLimitCost: 3,
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      const body = await req.json();
      const { projectId } = body;
      
      // Verify project access
      const supabase = await createTypedServerClient();
      const { data: project } = await supabase
        .from('projects')
        .select('id')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single();
      
      if (!project) {
        const { data: collaborator } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();
        
        if (!collaborator || collaborator.role === 'viewer') {
          return { valid: false, error: 'Insufficient permissions to create sessions' };
        }
      }
      
      // Check for duplicate sessions in the last minute
      const recentSessions = await supabase
        .from('writing_sessions')
        .select('id')
        .eq('user_id', user.id)
        .eq('project_id', projectId)
        .gte('created_at', new Date(Date.now() - 60000).toISOString())
        .limit(3);
      
      if (recentSessions.data && recentSessions.data.length >= 3) {
        return { 
          valid: false, 
          error: 'Too many session creations. Please wait a moment.' 
        };
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, wordCount, duration, sessionType, metadata } = context.body;

  try {

    const supabase = await createTypedServerClient()

    // Create new writing session
    const { data: session, error } = await supabase
      .from('writing_sessions')
      .insert({
        user_id: user.id,
        project_id: projectId,
        word_count: wordCount,
        duration: duration || 0,
        session_type: sessionType,
        metadata,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      logger.error('Failed to create writing session:', error, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to create writing session');
    }

    // Update project's last activity
    await supabase
      .from('projects')
      .update({ 
        updated_at: new Date().toISOString(),
        last_activity: new Date().toISOString()
      })
      .eq('id', projectId);

    // Update daily analytics
    const today = format(new Date(), 'yyyy-MM-dd');
    const { data: existingAnalytics } = await supabase
      .from('user_analytics')
      .select('*')
      .eq('user_id', user.id)
      .eq('date', today)
      .single();

    if (existingAnalytics) {
      await supabase
        .from('user_analytics')
        .update({
          total_words: existingAnalytics.total_words + wordCount,
          total_sessions: existingAnalytics.total_sessions + 1,
          total_time: existingAnalytics.total_time + (duration || 0),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingAnalytics.id);
    } else {
      await supabase
        .from('user_analytics')
        .insert({
          user_id: user.id,
          date: today,
          total_words: wordCount,
          total_sessions: 1,
          total_time: duration || 0
        });
    }

    logger.info('Writing session created', {
      sessionId: session.id,
      userId: user.id,
      projectId,
      wordCount,
      duration,
      sessionType,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      session,
      message: 'Writing session recorded successfully'
    });

  } catch (error) {
    logger.error('Create session error:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to create writing session');
  }
});