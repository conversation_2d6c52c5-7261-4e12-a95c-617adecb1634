'use client'

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import {
  Mic,
  RefreshCw,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  MessageSquare,
  Hash,
  Sparkles
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface VoiceAnalysisPanelProps {
  content: string
  projectId: string
  onSuggestionApply?: (suggestion: string) => void
}

interface VoiceMetric {
  label: string
  value: number
  target: number
  unit?: string
  description?: string
}

interface VoiceProfileData {
  id?: string
  name?: string
  type?: 'author' | 'character' | 'narrator'
  confidence?: number
  patterns?: {
    sentenceStructure?: {
      averageLength: number
      complexityScore: number
      variationScore: number
    }
    vocabulary?: {
      uniqueWords: number
      averageWordLength: number
      formalityScore: number
      commonPhrases: string[]
      signatureWords: string[]
    }
    style?: {
      descriptiveness: number
      dialogueRatio: number
      actionRatio: number
      introspectionRatio: number
      showVsTell: number
    }
    tone?: {
      emotionalRange: string[]
      intensity: number
      consistency: number
    }
    rhythm?: {
      punctuationPatterns: Record<string, number>
      paragraphLengthVariation: number
      pacingScore: number
    }
  }
  metrics?: VoiceMetric[]
  suggestions?: string[]
}

interface VoiceMatch {
  id: string
  text: string
  location: {
    start: number
    end: number
    line?: number
  }
  matchType: 'style' | 'vocabulary' | 'rhythm' | 'tone'
  confidence: number
  suggestion?: string
  severity: 'info' | 'warning' | 'error'
}

export function VoiceAnalysisPanel({ content, projectId, onSuggestionApply }: VoiceAnalysisPanelProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [voiceProfile, setVoiceProfile] = useState<VoiceProfileData | null>(null)
  const [voiceMatches, setVoiceMatches] = useState<VoiceMatch[]>([])
  const [isProfileLoading, setIsProfileLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load existing voice profile
  useEffect(() => {
    const loadVoiceProfile = async () => {
      setIsProfileLoading(true)
      try {
        const response = await fetch(`/api/projects/${projectId}/voice-profile`)
        if (response.ok) {
          const data = await response.json()
          setVoiceProfile(data.profile)
        }
      } catch (err) {
        logger.error('Error loading voice profile:', err)
      } finally {
        setIsProfileLoading(false)
      }
    }

    loadVoiceProfile()
  }, [projectId])

  const analyzeVoice = async () => {
    if (!content || content.length < 100) {
      setError('Please write at least 100 characters to analyze voice')
      return
    }

    setIsAnalyzing(true)
    setError(null)
    try {
      // Use API route for voice analysis
      const response = await fetch('/api/analysis/voice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          projectId,
          existingProfile: voiceProfile
        })
      })

      if (!response.ok) {
        throw new Error('Failed to analyze voice')
      }

      const data = await response.json()

      if (data.profile) {
        setVoiceProfile(data.profile)

        // Save profile if it's new
        if (!voiceProfile) {
          await fetch(`/api/projects/${projectId}/voice-profile`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ profile: data.profile })
          })
        }
      }

      if (data.matches) {
        setVoiceMatches(data.matches)
      }
    } catch (err) {
      setError('Failed to analyze voice. Please try again.')
      logger.error('Voice analysis error:', err)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const getVoiceMetrics = (): VoiceMetric[] => {
    if (!voiceProfile) return []
    
    const patterns = voiceProfile.patterns
    return [
      {
        label: 'Sentence Complexity',
        value: patterns.sentenceStructure.complexityScore,
        target: 65,
        description: 'Variety in sentence structure'
      },
      {
        label: 'Vocabulary Formality',
        value: patterns.vocabulary.formalityScore,
        target: patterns.vocabulary.formalityScore,
        description: 'Word choice sophistication'
      },
      {
        label: 'Dialogue Ratio',
        value: patterns.style.dialogueRatio,
        target: 40,
        unit: '%',
        description: 'Dialogue vs narrative balance'
      },
      {
        label: 'Emotional Intensity',
        value: patterns.tone.intensity,
        target: patterns.tone.intensity,
        description: 'Emotional expression strength'
      },
      {
        label: 'Rhythm Variation',
        value: patterns.rhythm.paragraphLengthVariation,
        target: 70,
        description: 'Paragraph length diversity'
      }
    ]
  }

  const getMatchSeverity = (match: any) => {
    // Voice matches are always suggestions, but we can categorize by aspect
    switch (match.voiceAspect) {
      case 'sentence_structure':
      case 'vocabulary':
        return 'warning'
      case 'tone':
      case 'rhythm':
        return 'info'
      default:
        return 'suggestion'
    }
  }

  const getMatchIcon = (aspect: string) => {
    switch (aspect) {
      case 'sentence_structure':
        return Hash
      case 'vocabulary':
        return MessageSquare
      case 'tone':
        return Sparkles
      case 'rhythm':
        return BarChart3
      default:
        return AlertTriangle
    }
  }

  if (isProfileLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            Voice Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            Voice Analysis
          </CardTitle>
          <Button
            size="sm"
            variant="outline"
            onClick={analyzeVoice}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Analyze
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!voiceProfile ? (
          <div className="text-center py-8">
            <Mic className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              No voice profile found. Write some content and analyze to create your unique voice profile.
            </p>
            <Button onClick={analyzeVoice} disabled={isAnalyzing || !content}>
              Create Voice Profile
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Voice Metrics */}
            <div>
              <h4 className="text-sm font-medium mb-3">Your Voice Metrics</h4>
              <div className="space-y-3">
                {getVoiceMetrics().map((metric, idx) => (
                  <div key={idx} className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">{metric.label}</span>
                      <span className="font-medium">
                        {metric.value}{metric.unit || ''} / {metric.target}{metric.unit || ''}
                      </span>
                    </div>
                    <Progress 
                      value={(metric.value / metric.target) * 100} 
                      className="h-2"
                    />
                    {metric.description && (
                      <p className="text-xs text-muted-foreground">{metric.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Voice Confidence */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Profile Confidence</span>
              <div className="flex items-center gap-2">
                <Progress value={voiceProfile.confidence} className="w-24 h-2" />
                <span className="text-sm font-medium">{voiceProfile.confidence}%</span>
              </div>
            </div>

            {/* Voice Matches */}
            {voiceMatches.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="text-sm font-medium mb-3">Voice Consistency Suggestions</h4>
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-3">
                      {voiceMatches.map((match, idx) => {
                        const Icon = getMatchIcon(match.voiceAspect)
                        const severity = getMatchSeverity(match)
                        
                        return (
                          <div 
                            key={idx}
                            className={cn(
                              "p-3 rounded-lg border space-y-2",
                              severity === 'warning' && "border-warning/50 bg-warning-light/50 dark:bg-yellow-900/10",
                              severity === 'info' && "border-info/50 bg-info-light/50 dark:bg-blue-900/10",
                              severity === 'suggestion' && "border-muted"
                            )}
                          >
                            <div className="flex items-start gap-2">
                              <Icon className="h-4 w-4 mt-0.5 text-muted-foreground" />
                              <div className="flex-1">
                                <p className="text-sm font-medium">{match.message}</p>
                                {match.explanation && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {match.explanation}
                                  </p>
                                )}
                              </div>
                            </div>
                            {match.replacement && onSuggestionApply && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onSuggestionApply(match.replacement)}
                                className="w-full"
                              >
                                Apply Suggestion
                              </Button>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </ScrollArea>
                </div>
              </>
            )}

            {/* Common Phrases */}
            {voiceProfile.patterns.vocabulary.commonPhrases.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="text-sm font-medium mb-2">Your Common Phrases</h4>
                  <div className="flex flex-wrap gap-2">
                    {voiceProfile.patterns.vocabulary.commonPhrases.slice(0, 5).map((phrase: string, idx: number) => (
                      <Badge key={idx} variant="secondary">
                        {phrase}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}