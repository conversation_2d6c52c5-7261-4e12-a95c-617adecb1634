'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  Wand2, 
  Upload, 
  BookOpen, 
  Loader2,
  AlertCircle,
  CheckCircle,
  FileText,
  Sparkles
} from 'lucide-react'
import { VoiceProfileTemplates, VoiceProfileTemplate } from './voice-profile-templates'
import { useVoiceProfileTemplate } from './voice-profile-templates'
import { toast } from '@/hooks/use-toast'

interface VoiceProfileCreationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  projectId?: string
  onProfileCreated?: (profileId: string) => void
}

export function VoiceProfileCreationDialog({
  open,
  onOpenChange,
  projectId,
  onProfileCreated
}: VoiceProfileCreationDialogProps) {
  const [activeTab, setActiveTab] = useState<'template' | 'sample' | 'manual'>('template')
  const [profileName, setProfileName] = useState('')
  const [profileDescription, setProfileDescription] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState<VoiceProfileTemplate | null>(null)
  const [sampleText, setSampleText] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  
  const { createFromTemplate } = useVoiceProfileTemplate()

  const handleAnalyzeSample = async () => {
    if (!sampleText || sampleText.length < 500) {
      toast({
        title: 'Sample too short',
        description: 'Please provide at least 500 characters of sample text',
        variant: 'destructive'
      })
      return
    }

    setIsAnalyzing(true)
    try {
      const response = await fetch('/api/analysis/voice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: sampleText })
      })

      if (!response.ok) throw new Error('Analysis failed')

      const result = await response.json()
      setAnalysisResult(result)
      
      // Auto-generate name if empty
      if (!profileName) {
        const style = result.style_analysis?.writing_style || 'Custom'
        const tone = result.style_analysis?.tone || 'Unique'
        setProfileName(`${style} ${tone} Voice`)
      }
    } catch (error) {
      toast({
        title: 'Analysis failed',
        description: 'Could not analyze the sample text',
        variant: 'destructive'
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleCreateProfile = async () => {
    if (!profileName) {
      toast({
        title: 'Name required',
        description: 'Please enter a name for the voice profile',
        variant: 'destructive'
      })
      return
    }

    setIsCreating(true)
    try {
      let profileId: string

      if (activeTab === 'template' && selectedTemplate) {
        // Create from template
        const profile = await createFromTemplate(selectedTemplate, profileName)
        profileId = profile.id
      } else if (activeTab === 'sample' && analysisResult) {
        // Create from sample analysis
        const response = await fetch('/api/voice-profiles', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: profileName,
            description: profileDescription || 'Created from writing sample',
            metrics: analysisResult.metrics,
            patterns: analysisResult.patterns,
            project_id: projectId,
            training_samples: [sampleText],
            is_active: true
          })
        })

        if (!response.ok) throw new Error('Failed to create profile')
        
        const profile = await response.json()
        profileId = profile.id
      } else {
        // Manual creation
        const response = await fetch('/api/voice-profiles', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: profileName,
            description: profileDescription,
            project_id: projectId,
            is_active: true
          })
        })

        if (!response.ok) throw new Error('Failed to create profile')
        
        const profile = await response.json()
        profileId = profile.id
      }

      toast({
        title: 'Voice profile created',
        description: `"${profileName}" has been created successfully`
      })

      onProfileCreated?.(profileId)
      onOpenChange(false)
      
      // Reset form
      setProfileName('')
      setProfileDescription('')
      setSelectedTemplate(null)
      setSampleText('')
      setAnalysisResult(null)
    } catch (error) {
      toast({
        title: 'Creation failed',
        description: 'Could not create the voice profile',
        variant: 'destructive'
      })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Create Voice Profile</DialogTitle>
          <DialogDescription>
            Create a voice profile to maintain consistent writing style throughout your project
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid gap-4 sm:gap-5 lg:gap-6">
            <div className="grid gap-2">
              <Label htmlFor="profile-name">Profile Name</Label>
              <Input
                id="profile-name"
                value={profileName}
                onChange={(e) => setProfileName(e.target.value)}
                placeholder="e.g., Dark Thriller Voice, Fantasy Epic Style"
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="profile-description">Description (Optional)</Label>
              <Textarea
                id="profile-description"
                value={profileDescription}
                onChange={(e) => setProfileDescription(e.target.value)}
                placeholder="Describe the voice characteristics..."
                rows={2}
              />
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="template" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                From Template
              </TabsTrigger>
              <TabsTrigger value="sample" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                From Sample
              </TabsTrigger>
              <TabsTrigger value="manual" className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Manual Setup
              </TabsTrigger>
            </TabsList>

            <TabsContent value="template" className="mt-4">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Choose a pre-built template to quickly set up your voice profile
                </div>
                <VoiceProfileTemplates
                  onSelectTemplate={setSelectedTemplate}
                  selectedTemplateId={selectedTemplate?.id}
                />
              </div>
            </TabsContent>

            <TabsContent value="sample" className="mt-4">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Paste a sample of your writing (or writing you want to emulate) to analyze its voice
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="sample-text">Writing Sample</Label>
                  <Textarea
                    id="sample-text"
                    value={sampleText}
                    onChange={(e) => setSampleText(e.target.value)}
                    placeholder="Paste at least 500 characters of writing..."
                    rows={8}
                    className="font-mono text-sm"
                  />
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      {sampleText.length} characters
                    </span>
                    <Button
                      size="sm"
                      onClick={handleAnalyzeSample}
                      disabled={sampleText.length < 500 || isAnalyzing}
                    >
                      {isAnalyzing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Analyzing...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Analyze Voice
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {analysisResult && (
                  <Alert className="mt-4">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <p className="font-medium">Voice Analysis Complete</p>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>Style: {analysisResult.style_analysis?.writing_style}</div>
                          <div>Tone: {analysisResult.style_analysis?.tone}</div>
                          <div>Complexity: {Math.round(analysisResult.metrics?.sentence_complexity * 100)}%</div>
                          <div>Emotion: {Math.round(analysisResult.metrics?.emotional_tone * 100)}%</div>
                        </div>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="manual" className="mt-4">
              <div className="space-y-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Create an empty voice profile that you can train later by adding writing samples
                  </AlertDescription>
                </Alert>
                
                <div className="text-sm text-muted-foreground">
                  You'll be able to:
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Add training samples to build the voice profile</li>
                    <li>Manually adjust voice metrics and patterns</li>
                    <li>Import voice characteristics from existing content</li>
                    <li>Fine-tune the profile as you write</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleCreateProfile}
            disabled={!profileName || isCreating || 
                     (activeTab === 'template' && !selectedTemplate) ||
                     (activeTab === 'sample' && !analysisResult)}
          >
            {isCreating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              'Create Profile'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}