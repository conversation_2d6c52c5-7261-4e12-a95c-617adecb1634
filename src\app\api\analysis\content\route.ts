import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { withRateLimit } from '@/lib/rate-limiter-unified';
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import { z } from 'zod';
import { logger } from '@/lib/services/logger';
import { contentAnalysisService, AnalysisType } from '@/lib/services/content-analysis-service';
import { type StandardResponse } from '@/lib/api/types';
import { TIME_MS } from '@/lib/constants';
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

// Request validation schema
const contentAnalysisSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  projectId: z.string().uuid('Invalid project ID'),
  chapterNumber: z.number().int().positive().optional(),
  analysisTypes: z.array(z.enum(['grammar', 'style', 'character', 'plot', 'pacing', 'dialogue'])).default(['grammar', 'style'])
});

export const POST = UnifiedAuthService.withAuth(async (request) => {
  const rateLimitResponse = await withRateLimit(request, {
    windowMs: 60 * 60 * TIME_MS.SECOND, // 1 hour
    maxRequests: 10, // 10 analysis requests per hour
    message: 'Too many analysis requests. Please wait before trying again.'
  });
  
  if (rateLimitResponse) {
    return rateLimitResponse;
  }
  
  try {
    const user = request.user!;
    const body = await request.json();
    const validatedData = contentAnalysisSchema.parse(body);
      
      logger.info('Content analysis requested', {
        projectId: validatedData.projectId,
        contentLength: validatedData.content.length,
        analysisTypes: validatedData.analysisTypes
      });
      
      // Verify user owns the project
      const project = await verifyProjectAccess(validatedData.projectId, user.id)
      if (!project) {
        return handleAPIError(new ValidationError(PROJECT_ACCESS_ERROR));
      }
      
      // Get project context if available (for better analysis)
      let projectContext = undefined;
          
      projectContext = {
        genre: project.genre || undefined,
        targetAudience: project.target_audience || undefined,
        writingStyle: project.writing_style || undefined
      };
      
      // Perform content analysis using the real AI service
      const analysisResult = await contentAnalysisService.analyzeContent(
        validatedData.content,
        validatedData.analysisTypes as AnalysisType[],
        projectContext
      );
      
      return NextResponse.json<StandardResponse>({
        success: true,
        data: {
          suggestions: analysisResult.suggestions,
          voiceProfile: analysisResult.voiceProfile,
          metadata: {
            contentLength: validatedData.content.length,
            wordCount: validatedData.content.split(/\s+/).filter(word => word.length > 0).length,
            analysisTypes: validatedData.analysisTypes,
            timestamp: new Date().toISOString()
          }
        }
      });
      
    } catch (error) {
      if (error instanceof z.ZodError) {
        return handleAPIError(new ValidationError('Invalid request data'));
      }
      
      logger.error('Content analysis error:', error);
      return NextResponse.json<StandardResponse>(
        { success: false, error: 'Failed to analyze content' },
        { status: 500 }
      );
    }
  }
);
