import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/api/middleware';
import { z } from 'zod';
import { logger } from '@/lib/services/logger';
import { contentAnalysisService, AnalysisType } from '@/lib/services/content-analysis-service';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/types/database';

// Request validation schema
const contentAnalysisSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  projectId: z.string().uuid('Invalid project ID'),
  chapterNumber: z.number().int().positive().optional(),
  analysisTypes: z.array(z.enum(['grammar', 'style', 'character', 'plot', 'pacing', 'dialogue'])).default(['grammar', 'style'])
});

export const POST = withMiddleware(
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      const validatedData = contentAnalysisSchema.parse(body);
      
      logger.info('Content analysis requested', {
        projectId: validatedData.projectId,
        contentLength: validatedData.content.length,
        analysisTypes: validatedData.analysisTypes
      });
      
      // Get project context if available (for better analysis)
      let projectContext = undefined;
      try {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
        
        if (!supabaseUrl || !supabaseServiceKey) {
          throw new Error('Missing required Supabase environment variables');
        }
        
        const supabase = createClient<Database>(
          supabaseUrl,
          supabaseServiceKey
        );
        
        const { data: project } = await supabase
          .from('projects')
          .select('genre, target_audience, writing_style')
          .eq('id', validatedData.projectId)
          .single();
          
        if (project) {
          projectContext = {
            genre: project.genre || undefined,
            targetAudience: project.target_audience || undefined,
            writingStyle: project.writing_style || undefined
          };
        }
      } catch (contextError) {
        logger.warn('Failed to fetch project context for analysis', contextError);
        // Continue without context
      }
      
      // Perform content analysis using the real AI service
      const analysisResult = await contentAnalysisService.analyzeContent(
        validatedData.content,
        validatedData.analysisTypes as AnalysisType[],
        projectContext
      );
      
      return NextResponse.json({
        success: true,
        suggestions: analysisResult.suggestions,
        voiceProfile: analysisResult.voiceProfile,
        metadata: {
          contentLength: validatedData.content.length,
          wordCount: validatedData.content.split(/\s+/).filter(word => word.length > 0).length,
          analysisTypes: validatedData.analysisTypes,
          timestamp: new Date().toISOString()
        }
      });
      
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }
      
      logger.error('Content analysis error:', error);
      return NextResponse.json(
        { error: 'Failed to analyze content' },
        { status: 500 }
      );
    }
  },
  {
    auth: true,
    rateLimit: {
      type: 'ai-analysis',
      requests: 10
    }
  }
);
