'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, CheckCircle, Shield, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface StepPaymentProps {
  formData: {
    title: string;
    wordCount: string;
    chapters: string;
    [key: string]: string | undefined;
  };
  mode?: 'live' | 'demo';
  isGenerating: boolean;
  onComplete: () => void;
}

export function StepPayment({ formData, mode, isGenerating, onComplete }: StepPaymentProps) {
  const features = [
    'Unlimited AI-powered writing assistance',
    'Complete story structure generation',
    'Character development tools',
    'Chapter-by-chapter guidance',
    'Export in multiple formats',
    'Lifetime access to your project'
  ];

  if (mode === 'demo') {
    return (
      <Card className="max-w-4xl mx-auto shadow-sm">
        <CardHeader>
          <div className="flex items-center gap-2 mb-4">
            <CheckCircle className="h-6 w-6 text-primary" />
            <CardTitle>Demo Project Ready!</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-primary/10 rounded-lg p-6 space-y-4">
            <h3 className="text-lg font-semibold">Your Demo Project: {formData.title}</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Target Length:</span>
                <span className="ml-2 font-medium">{parseInt(formData.wordCount).toLocaleString()} words</span>
              </div>
              <div>
                <span className="text-muted-foreground">Chapters:</span>
                <span className="ml-2 font-medium">{formData.chapters}</span>
              </div>
            </div>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              This is a demo project with limited features. To unlock the full power of BookScribe AI 
              and create your real novel, upgrade to a paid project.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <Button 
              className="w-full" 
              size="lg"
              onClick={onComplete}
              disabled={isGenerating}
            >
              {isGenerating ? 'Creating Demo Project...' : 'Create Demo Project'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-4xl mx-auto shadow-sm">
      <CardHeader>
        <div className="flex items-center gap-2 mb-4">
          <CreditCard className="h-6 w-6 text-primary" />
          <CardTitle>Complete Your Project Setup</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-primary/10 rounded-lg p-6 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Project: {formData.title}</h3>
            <Badge variant="default" className="text-lg px-3 py-1">$49</Badge>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Target Length:</span>
              <span className="ml-2 font-medium">{parseInt(formData.wordCount).toLocaleString()} words</span>
            </div>
            <div>
              <span className="text-muted-foreground">Chapters:</span>
              <span className="ml-2 font-medium">{formData.chapters}</span>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            What's Included:
          </h4>
          <ul className="space-y-2">
            {features.map((feature, index) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        <Alert className="border-green-200 bg-green-50">
          <Shield className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Secure Payment:</strong> Your payment information is encrypted and secure. 
            We use Stripe for payment processing and never store your card details.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <Button 
            className="w-full" 
            size="lg"
            onClick={onComplete}
            disabled={isGenerating}
          >
            {isGenerating ? 'Processing Payment...' : 'Complete Payment & Create Project'}
          </Button>
          
          <p className="text-center text-sm text-muted-foreground">
            By completing payment, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </CardContent>
    </Card>
  );
}