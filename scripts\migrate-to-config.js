#!/usr/bin/env node

/**
 * Migration script to update hardcoded values to use centralized configuration
 * Run with: node scripts/migrate-to-config.js
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration replacements
const replacements = {
  // Storage Keys
  localStorage: [
    {
      pattern: /localStorage\.(getItem|setItem|removeItem)\(['"]bookscribe_remembered_email['"]\)/g,
      replacement: (match, method) => `localStorage.${method}(STORAGE_KEYS.AUTH.REMEMBERED_EMAIL)`,
      imports: ["import { STORAGE_KEYS } from '@/lib/config/storage-keys'"]
    },
    {
      pattern: /localStorage\.(getItem|setItem|removeItem)\(['"]bookscribe_remember_me['"]\)/g,
      replacement: (match, method) => `localStorage.${method}(STORAGE_KEYS.AUTH.REMEMBER_ME)`,
      imports: ["import { STORAGE_KEYS } from '@/lib/config/storage-keys'"]
    },
    {
      pattern: /localStorage\.(getItem|setItem|removeItem)\(['"]bookscribe_theme['"]\)/g,
      replacement: (match, method) => `localStorage.${method}(STORAGE_KEYS.PREFERENCES.THEME)`,
      imports: ["import { STORAGE_KEYS } from '@/lib/config/storage-keys'"]
    }
  ],

  // API Endpoints
  apiEndpoints: [
    {
      pattern: /fetch\(['"`]\/api\/story-bible\?project_id=\$\{([^}]+)\}['"`]\)/g,
      replacement: (match, projectId) => `fetch(API_ENDPOINTS.STORY_BIBLE.GET(${projectId}))`,
      imports: ["import { API_ENDPOINTS } from '@/lib/config/api-endpoints'"]
    },
    {
      pattern: /fetch\(['"`]\/api\/story-bible\/\$\{([^}]+)\}['"`]/g,
      replacement: (match, id) => `fetch(\`\${API_ENDPOINTS.STORY_BIBLE.SECTIONS.GET(projectId, ${id})}\``,
      imports: ["import { API_ENDPOINTS } from '@/lib/config/api-endpoints'"]
    },
    {
      pattern: /fetch\(['"`]\/api\/analytics\/export['"`]/g,
      replacement: 'fetch(API_ENDPOINTS.ANALYTICS.EXPORT',
      imports: ["import { API_ENDPOINTS } from '@/lib/config/api-endpoints'"]
    },
    {
      pattern: /fetch\(['"`]\/api\/projects['"`]/g,
      replacement: 'fetch(API_ENDPOINTS.PROJECTS.LIST',
      imports: ["import { API_ENDPOINTS } from '@/lib/config/api-endpoints'"]
    }
  ],

  // Timing constants
  timing: [
    {
      pattern: /setTimeout\(([^,]+),\s*2000\)/g,
      replacement: (match, callback) => `setTimeout(${callback}, TIMING.TOAST.SUCCESS)`,
      imports: ["import { TIMING } from '@/lib/config/animation-timing'"]
    },
    {
      pattern: /setTimeout\(([^,]+),\s*1000\)/g,
      replacement: (match, callback) => `setTimeout(${callback}, TIMING.DEBOUNCE.SAVE)`,
      imports: ["import { TIMING } from '@/lib/config/animation-timing'"]
    },
    {
      pattern: /setTimeout\(([^,]+),\s*500\)/g,
      replacement: (match, callback) => `setTimeout(${callback}, TIMING.TRANSITION.TOOLTIP)`,
      imports: ["import { TIMING } from '@/lib/config/animation-timing'"]
    }
  ],

  // File size limits
  fileSizes: [
    {
      pattern: /5\s*\*\s*1024\s*\*\s*1024/g,
      replacement: 'FILE_LIMITS.MAX_SIZES.AVATAR',
      imports: ["import { FILE_LIMITS } from '@/lib/config/file-limits'"]
    },
    {
      pattern: /10\s*\*\s*1024\s*\*\s*1024/g,
      replacement: 'FILE_LIMITS.MAX_SIZES.COVER_IMAGE',
      imports: ["import { FILE_LIMITS } from '@/lib/config/file-limits'"]
    }
  ]
};

// Files to process
const filesToProcess = [
  'src/components/auth/auth-form.tsx',
  'src/contexts/auth-context.tsx',
  'src/components/editor/knowledge-base-editor.tsx',
  'src/components/analytics/analytics-dashboard.tsx',
  'src/lib/file-upload-security.ts'
];

// Add import if not present
function addImport(content, importStatement) {
  if (!content.includes(importStatement)) {
    // Find the last import statement
    const importRegex = /^import .* from .*;?$/gm;
    let lastImportIndex = 0;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      lastImportIndex = match.index + match[0].length;
    }
    
    if (lastImportIndex > 0) {
      // Add after last import
      return content.slice(0, lastImportIndex) + '\n' + importStatement + content.slice(lastImportIndex);
    } else {
      // Add at the beginning
      return importStatement + '\n\n' + content;
    }
  }
  return content;
}

// Process a single file
function processFile(filePath) {
  console.log(`Processing ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  const neededImports = new Set();
  
  // Apply all replacements
  Object.values(replacements).forEach(replacementGroup => {
    replacementGroup.forEach(({ pattern, replacement, imports }) => {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement);
        modified = true;
        imports.forEach(imp => neededImports.add(imp));
      }
    });
  });
  
  // Add necessary imports
  if (modified) {
    neededImports.forEach(importStatement => {
      content = addImport(content, importStatement);
    });
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✓ Updated ${filePath}`);
  } else {
    console.log(`- No changes needed for ${filePath}`);
  }
}

// Main execution
console.log('Starting configuration migration...\n');

filesToProcess.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    processFile(fullPath);
  } else {
    console.log(`⚠ File not found: ${file}`);
  }
});

console.log('\nMigration complete!');
console.log('\nNext steps:');
console.log('1. Review the changes with: git diff');
console.log('2. Run tests to ensure everything works');
console.log('3. Run ESLint to check for any issues: npm run lint');
console.log('4. Consider running a broader search for other hardcoded values');