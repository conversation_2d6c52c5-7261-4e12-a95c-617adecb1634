-- Create content embeddings table for storing vector embeddings
CREATE TABLE IF NOT EXISTS content_embeddings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id TEXT NOT NULL UNIQUE, -- References content_index.id
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    embedding VECTOR(1536) NOT NULL, -- OpenAI text-embedding-3-small dimension
    model TEXT NOT NULL DEFAULT 'text-embedding-3-small',
    dimensions INTEGER NOT NULL DEFAULT 1536,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient similarity search
CREATE INDEX IF NOT EXISTS idx_content_embeddings_project_id ON content_embeddings(project_id);
CREATE INDEX IF NOT EXISTS idx_content_embeddings_content_id ON content_embeddings(content_id);

-- Install pgvector extension if not already installed
CREATE EXTENSION IF NOT EXISTS vector;

-- Add embedding index for similarity search
CREATE INDEX IF NOT EXISTS idx_content_embeddings_embedding ON content_embeddings 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Function to search similar content using cosine similarity
CREATE OR REPLACE FUNCTION search_similar_content(
    p_project_id UUID,
    p_query_embedding VECTOR(1536),
    p_limit INTEGER DEFAULT 10,
    p_threshold FLOAT DEFAULT 0.7
)
RETURNS TABLE(
    content_id TEXT,
    similarity FLOAT,
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ce.content_id,
        1 - (ce.embedding <=> p_query_embedding) as similarity,
        ci.metadata
    FROM content_embeddings ce
    JOIN content_index ci ON ce.content_id = ci.id
    WHERE ce.project_id = p_project_id
    AND 1 - (ce.embedding <=> p_query_embedding) >= p_threshold
    ORDER BY ce.embedding <=> p_query_embedding
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Add column to track embedding status in content_index
ALTER TABLE content_index 
ADD COLUMN IF NOT EXISTS embedding_generated BOOLEAN DEFAULT FALSE;

-- Trigger to update timestamp
CREATE OR REPLACE FUNCTION update_content_embeddings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER content_embeddings_updated_at
    BEFORE UPDATE ON content_embeddings
    FOR EACH ROW
    EXECUTE FUNCTION update_content_embeddings_updated_at();

-- Enable RLS
ALTER TABLE content_embeddings ENABLE ROW LEVEL SECURITY;

-- RLS policies for content_embeddings
CREATE POLICY "Users can view embeddings for their projects" ON content_embeddings
    FOR SELECT USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
            UNION
            SELECT project_id FROM project_collaborators WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create embeddings for their projects" ON content_embeddings
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update embeddings for their projects" ON content_embeddings
    FOR UPDATE USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete embeddings for their projects" ON content_embeddings
    FOR DELETE USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
        )
    );

-- Grant permissions
GRANT SELECT ON content_embeddings TO authenticated;
GRANT INSERT, UPDATE, DELETE ON content_embeddings TO service_role;
GRANT EXECUTE ON FUNCTION search_similar_content(UUID, VECTOR(1536), INTEGER, FLOAT) TO authenticated;

-- Create a function to cleanup orphaned embeddings
CREATE OR REPLACE FUNCTION cleanup_orphaned_embeddings()
RETURNS void AS $$
BEGIN
    DELETE FROM content_embeddings ce
    WHERE NOT EXISTS (
        SELECT 1 FROM content_index ci
        WHERE ci.id = ce.content_id
    );
END;
$$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION cleanup_orphaned_embeddings() TO service_role;