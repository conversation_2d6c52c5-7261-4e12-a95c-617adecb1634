import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { characterQuerySchema, createCharacterSchema } from '@/lib/validation/schemas'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = {
      project_id: projectId, // Use the project ID from the URL
      role: searchParams.get('role'),
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const validatedQuery = characterQuerySchema.parse(queryParams)

    // Verify project ownership first
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Build query for characters
    let query = supabase
      .from('characters')
      .select('*', { count: 'exact' })
      .eq('project_id', projectId)

    // Apply filters
    if (validatedQuery.role) {
      query = query.eq('role', validatedQuery.role)
    }

    // Apply pagination
    if (validatedQuery.limit) {
      query = query.limit(validatedQuery.limit)
    }
    if (validatedQuery.offset) {
      query = query.range(validatedQuery.offset, (validatedQuery.offset + (validatedQuery.limit || 50)) - 1)
    }

    // Order by role priority then name
    query = query.order('role', { ascending: true }).order('name', { ascending: true })

    const { data: characters, error, count } = await query

    if (error) {
      logger.error('Error fetching characters:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Calculate summary statistics
    const stats = characters?.reduce((acc, character) => {
      switch (character.role) {
        case 'protagonist':
          acc.roleCounts.protagonist++
          break
        case 'antagonist':
          acc.roleCounts.antagonist++
          break
        case 'supporting':
          acc.roleCounts.supporting++
          break
        case 'minor':
          acc.roleCounts.minor++
          break
      }
      
      // Count characters with complete profiles
      if (character.description && character.backstory && character.personality_traits) {
        acc.completeProfiles++
      }
      
      return acc
    }, {
      roleCounts: {
        protagonist: 0,
        antagonist: 0,
        supporting: 0,
        minor: 0
      },
      completeProfiles: 0
    })

    // Group characters by role for easier consumption
    const charactersByRole = characters?.reduce((acc, character) => {
      if (!acc[character.role]) {
        acc[character.role] = []
      }
      acc[character.role].push(character)
      return acc
    }, {} as Record<string, typeof characters>)

    return NextResponse.json({ 
      characters: characters || [],
      charactersByRole: charactersByRole || {},
      stats: stats || {
        roleCounts: { protagonist: 0, antagonist: 0, supporting: 0, minor: 0 },
        completeProfiles: 0
      },
      pagination: {
        total: count,
        limit: validatedQuery.limit || 50,
        offset: validatedQuery.offset || 0
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 })
    }

    logger.error('Error in project characters GET:', error)
    return NextResponse.json(
      { error: 'Failed to fetch characters' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    
    // Add project_id from URL to the body
    const characterData = {
      ...body,
      project_id: projectId
    }
    
    // Validate input
    const validatedData = createCharacterSchema.parse(characterData)

    // Verify project ownership
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Check if character_id already exists in this project
    const { data: existingCharacter } = await supabase
      .from('characters')
      .select('id')
      .eq('project_id', projectId)
      .eq('character_id', validatedData.character_id)
      .single()

    if (existingCharacter) {
      return NextResponse.json({ 
        error: 'Character with this ID already exists in the project' 
      }, { status: 409 })
    }

    // Create character
    const { data: character, error } = await supabase
      .from('characters')
      .insert({
        ...validatedData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      logger.error('Error creating character:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ character }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid character data',
        details: error.errors
      }, { status: 400 })
    }

    logger.error('Error in project characters POST:', error)
    return NextResponse.json(
      { error: 'Failed to create character' },
      { status: 500 }
    )
  }
}