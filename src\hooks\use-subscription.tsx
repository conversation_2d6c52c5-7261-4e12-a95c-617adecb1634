'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { createClient } from '@/lib/supabase'
import { getUserTier, type UserSubscription, type SubscriptionTier } from '@/lib/subscription'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'

interface UseSubscriptionReturn {
  subscription: UserSubscription | null
  tier: SubscriptionTier
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
  canUseFeature: (feature: string) => boolean
  checkUsageLimit: (limitType: keyof SubscriptionTier['limits'], currentUsage: number) => {
    allowed: boolean
    limit: number
    remaining: number
  }
}

export function useSubscription(): UseSubscriptionReturn {
  const { user } = useAuth()
  const [subscription, setSubscription] = useState<UserSubscription | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const supabase = createClient()

  const fetchSubscription = async () => {
    if (!user) {
      setSubscription(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Fetch user data including subscription info
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('subscription_tier, subscription_expires_at')
        .eq('id', user.id)
        .single()

      if (userError) {
        throw userError
      }

      // If user has no subscription data, they're on the free tier
      if (!userData || !userData.subscription_tier) {
        setSubscription(null)
        setLoading(false)
        return
      }

      // Check if subscription is still valid
      const isExpired = userData.subscription_expires_at && 
        new Date(userData.subscription_expires_at) < new Date()

      // Map database subscription tier to UserSubscription format
      const userSubscription: UserSubscription = {
        id: `sub_${user.id}`,
        userId: user.id,
        tierId: userData.subscription_tier === 'free' ? 'starter' : 
               userData.subscription_tier === 'pro' ? 'author' : 
               userData.subscription_tier === 'enterprise' ? 'studio' : 
               'starter',
        status: isExpired ? 'canceled' : 'active',
        currentPeriodStart: new Date(),
        currentPeriodEnd: userData.subscription_expires_at ? 
          new Date(userData.subscription_expires_at) : 
          new Date(Date.now() + 30 * 24 * 60 * 60 * TIME_MS.SECOND), // 30 days from now
        cancelAtPeriodEnd: false,
        usage: {
          wordsUsed: 0, // This would need to be fetched from usage tracking
          projects: 0 // This would need to be fetched from projects count
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Optionally fetch usage data
      const { data: projectCount } = await supabase
        .from('projects')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', user.id)

      if (projectCount !== null) {
        userSubscription.usage.projects = projectCount
      }

      setSubscription(userSubscription)
    } catch (err) {
      logger.error('Error fetching subscription:', err)
      setError(err as Error)
      setSubscription(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSubscription()
  }, [user?.id])

  const tier = getUserTier(subscription)

  const canUseFeature = (feature: string): boolean => {
    // Import the canUseFeature function from subscription.ts
    const { canUseFeature: checkFeature } = require('@/lib/subscription')
    return checkFeature(subscription, feature)
  }

  const checkUsageLimit = (
    limitType: keyof SubscriptionTier['limits'], 
    currentUsage: number
  ) => {
    // Import the checkUsageLimit function from subscription.ts
    const { checkUsageLimit: checkLimit } = require('@/lib/subscription')
    return checkLimit(subscription, limitType, currentUsage)
  }

  return {
    subscription,
    tier,
    loading,
    error,
    refetch: fetchSubscription,
    canUseFeature,
    checkUsageLimit
  }
}