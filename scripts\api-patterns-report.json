{"timestamp": "2025-07-30T02:59:51.812Z", "summary": {"totalFiles": 152, "responseStyles": {"success-boolean": 75, "unknown": 63, "direct": 13, "helper": 1}, "errorHandling": {"withTryCatch": 151, "withErrorHandling": 147, "noErrorHandling": 5}, "statusCodes": {}, "patterns": {"success-true": 100, "error-object": 437, "handle-api-error": 447, "status-400": 65, "status-500": 346, "try-catch": 273, "status-201": 11, "handle-route-error": 38, "manual-pagination": 10, "direct-data": 30, "status-403": 4, "status-200": 3, "promise-catch": 5, "status-401": 3, "api-response-helper": 1, "api-error-helper": 6}}, "files": [{"file": "writing/sessions/route.ts", "patterns": {"success-true": 1, "error-object": 7, "handle-api-error": 6, "status-400": 1, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "writing/goals/route.ts", "patterns": {"error-object": 5, "handle-api-error": 2, "status-201": 1, "status-400": 1, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "writing/goals/progress/route.ts", "patterns": {"error-object": 3, "handle-api-error": 1, "status-500": 3, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "writing/goals/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 7, "handle-api-error": 5, "status-400": 1, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "webhooks/stripe/route.ts", "patterns": {"error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "voice-profiles/route.ts", "patterns": {"error-object": 3, "handle-api-error": 1, "status-500": 3, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "voice-profiles/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 4, "status-500": 3, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "voice-profiles/[id]/train-from-content/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 5, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "voice-profiles/[id]/train/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 4, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "version-history/route.ts", "patterns": {"error-object": 4, "handle-api-error": 3, "handle-route-error": 2, "status-201": 1, "status-400": 2, "status-500": 2, "manual-pagination": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "version-history/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 5, "handle-route-error": 2, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "universes/route.ts", "patterns": {"error-object": 4, "handle-api-error": 3, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "universes/timeline-events/route.ts", "patterns": {"success-true": 1, "error-object": 8, "handle-api-error": 15, "status-500": 8, "try-catch": 4}, "hasTryCatch": true, "hasErrorHandling": 15, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "universes/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 6, "handle-api-error": 4, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "timeline/validate/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "timeline/events/route.ts", "patterns": {"success-true": 2, "error-object": 2, "handle-api-error": 2, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "timeline/autofix/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "story-bible/route.ts", "patterns": {"error-object": 5, "handle-api-error": 4, "status-400": 1, "status-500": 4, "manual-pagination": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "story-bible/update/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 4, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "story-bible/bulk/route.ts", "patterns": {"error-object": 2, "handle-api-error": 1, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "story-bible/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 6, "handle-api-error": 6, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "services/orchestrator/route.ts", "patterns": {"direct-data": 6, "error-object": 2, "handle-api-error": 1, "handle-route-error": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "direct", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "services/health/route.ts", "patterns": {"success-true": 2, "direct-data": 1, "handle-api-error": 2, "handle-route-error": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "services/content/route.ts", "patterns": {"success-true": 1, "direct-data": 5, "error-object": 3, "handle-api-error": 1, "handle-route-error": 1, "status-500": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "services/analytics/route.ts", "patterns": {"direct-data": 6, "error-object": 4, "handle-api-error": 5, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "direct", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/route.ts", "patterns": {"error-object": 5, "handle-api-error": 2, "status-201": 1, "status-400": 1, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 6, "handle-api-error": 7, "status-400": 1, "status-500": 5, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 7, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/word-counts/route.ts", "patterns": {"error-object": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "series/[id]/universe-rules/route.ts", "patterns": {"error-object": 4, "handle-api-error": 3, "status-201": 1, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/universe/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 7, "status-500": 2, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 7, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/continuity-issues/route.ts", "patterns": {"error-object": 6, "handle-api-error": 5, "status-201": 1, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/continuity/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "series/[id]/characters/route.ts", "patterns": {"error-object": 5, "handle-api-error": 4, "status-500": 5, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/character-continuity/route.ts", "patterns": {"error-object": 4, "handle-api-error": 7, "status-500": 4, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 7, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/character-arcs/route.ts", "patterns": {"error-object": 6, "handle-api-error": 4, "status-201": 1, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/books/route.ts", "patterns": {"error-object": 4, "handle-api-error": 7, "status-201": 1, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 7, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/apply-voice-profile/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 8, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 8, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "series/[id]/analytics/route.ts", "patterns": {"error-object": 2, "handle-api-error": 2, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "search/theme/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "search/semantic/route.ts", "patterns": {"success-true": 3, "direct-data": 1, "error-object": 7, "status-400": 2, "status-500": 3, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 7, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "search/related/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "search/index/route.ts", "patterns": {"success-true": 2, "error-object": 7, "handle-api-error": 3, "status-400": 1, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "search/emotion/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "search/character-moments/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "sample-projects/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 2, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "relationships/graph/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "relationships/analyze/route.ts", "patterns": {"success-true": 2, "error-object": 2, "handle-api-error": 2, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "references/route.ts", "patterns": {"handle-api-error": 3, "handle-route-error": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "references/upload/route.ts", "patterns": {"error-object": 2, "handle-api-error": 2, "handle-route-error": 1, "status-400": 1, "status-500": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "references/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 4, "status-500": 3, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "references/[id]/summarize/route.ts", "patterns": {"error-object": 3, "handle-api-error": 3, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "projects/route.ts", "patterns": {"error-object": 3, "status-400": 1, "status-500": 2, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/route.refactored.ts", "patterns": {"manual-pagination": 1}, "hasTryCatch": false, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/invite/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 6, "status-400": 1, "status-403": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "projects/grouped/route.ts", "patterns": {"error-object": 3, "handle-api-error": 1, "status-400": 1, "status-500": 2, "manual-pagination": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "projects/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 6, "handle-api-error": 2, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/[id]/voice-profile/route.ts", "patterns": {"success-true": 1, "error-object": 4, "handle-api-error": 8, "status-500": 4, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 8, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/[id]/team/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 1, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/[id]/plot-threads/route.ts", "patterns": {"error-object": 4, "handle-api-error": 4, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/[id]/locations/route.ts", "patterns": {"error-object": 4, "handle-api-error": 4, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/[id]/export/route.ts", "patterns": {"error-object": 3, "handle-api-error": 3, "status-200": 1, "status-403": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "projects/[id]/collaborators/invite/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 8, "status-400": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 8, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "projects/[id]/characters/route.ts", "patterns": {"error-object": 7, "handle-api-error": 4, "status-201": 1, "status-400": 2, "status-500": 4, "manual-pagination": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "projects/[id]/chapters/route.ts", "patterns": {"error-object": 7, "handle-api-error": 4, "status-201": 1, "status-400": 2, "status-500": 4, "manual-pagination": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "project-collaborators/route.ts", "patterns": {"success-true": 1, "error-object": 8, "handle-api-error": 12, "status-400": 1, "status-500": 7, "try-catch": 3, "promise-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 12, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "project-collaborators/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 7, "handle-api-error": 10, "status-400": 1, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 10, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "profiles/route.ts", "patterns": {"handle-api-error": 1, "handle-route-error": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "profiles/user/route.ts", "patterns": {"handle-route-error": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "profiles/public/route.ts", "patterns": {"error-object": 1, "status-500": 1, "manual-pagination": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "profiles/[id]/favorite/route.ts", "patterns": {"success-true": 2, "error-object": 4, "handle-api-error": 1, "status-400": 1, "status-500": 3, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "profiles/[id]/clone/route.ts", "patterns": {"error-object": 2, "handle-api-error": 2, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "processing-tasks/route.ts", "patterns": {"success-true": 3, "error-object": 7, "handle-api-error": 7, "status-500": 7, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 7, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "processing-tasks/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 6, "handle-api-error": 9, "status-500": 6, "try-catch": 3, "promise-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 9, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "orchestration/start/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 3, "status-500": 1, "try-catch": 1, "promise-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "orchestration/progress/route.ts", "patterns": {"success-true": 4, "error-object": 2, "handle-api-error": 3, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "notifications/route.ts", "patterns": {"success-true": 3, "error-object": 7, "handle-api-error": 5, "status-500": 7, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "notifications/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 6, "handle-api-error": 5, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "memory/stats/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 2, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "memory/merge/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 2, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "memory/compress/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 2, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "invitations/[token]/accept/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 5, "status-500": 3, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "import/pdf/route.ts", "patterns": {"success-true": 1, "handle-api-error": 1, "handle-route-error": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "import/epub/route.ts", "patterns": {"success-true": 1, "handle-api-error": 6, "handle-route-error": 1, "try-catch": 1, "promise-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "import/docx/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 2, "handle-route-error": 1, "status-400": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "health/route.ts", "patterns": {"handle-api-error": 1, "handle-route-error": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "goals/route.ts", "patterns": {"success-true": 1, "error-object": 11, "handle-api-error": 6, "status-400": 2, "status-500": 9, "try-catch": 4}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "goals/recommendations/route.ts", "patterns": {"error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "goals/progress/route.ts", "patterns": {"error-object": 5, "handle-api-error": 3, "status-400": 1, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "email/send/route.ts", "patterns": {"error-object": 4, "handle-api-error": 2, "status-400": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "email/queue/process/route.ts", "patterns": {"error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "email/preferences/route.ts", "patterns": {"error-object": 3, "handle-api-error": 2, "status-400": 1, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "content-analysis/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 2, "status-500": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "consistency/check/route.ts", "patterns": {"success-true": 1, "handle-api-error": 3, "handle-route-error": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "collaboration/unlock/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "collaboration/sessions/route.ts", "patterns": {"success-true": 2, "error-object": 6, "handle-api-error": 2, "status-400": 2, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "collaboration/lock/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "collaboration/leave/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "collaboration/join/route.ts", "patterns": {"error-object": 3, "handle-api-error": 2, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "collaboration/cursor/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 2, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "collaboration/change/route.ts", "patterns": {"error-object": 3, "handle-api-error": 2, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "characters/route.ts", "patterns": {"error-object": 8, "handle-api-error": 3, "status-201": 1, "status-400": 2, "status-500": 5, "manual-pagination": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "characters/bulk/route.ts", "patterns": {"error-object": 2, "handle-api-error": 1, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "characters/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 8, "handle-api-error": 6, "status-400": 1, "status-500": 6, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "characters/[id]/share/route.ts", "patterns": {"error-object": 4, "handle-api-error": 6, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "chapters/restore-version/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 5, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 5, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "chapters/create-version/route.ts", "patterns": {"success-true": 1, "error-object": 4, "handle-api-error": 3, "status-400": 2, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "chapters/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 7, "handle-api-error": 6, "status-400": 1, "status-500": 6, "try-catch": 3, "promise-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "billing/webhooks/stripe/route.ts", "patterns": {"error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "billing/subscriptions/route.ts", "patterns": {"success-true": 2, "error-object": 3, "handle-api-error": 4, "status-400": 1, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "billing/subscriptions/portal/route.ts", "patterns": {"error-object": 1, "handle-api-error": 2, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "billing/subscriptions/checkout/route.ts", "patterns": {"error-object": 2, "handle-api-error": 2, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "billing/payments/create-payment-intent/route.ts", "patterns": {"error-object": 3, "handle-api-error": 1, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "billing/payments/charge/route.ts", "patterns": {"error-object": 2, "handle-api-error": 2, "status-400": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "billing/history/route.ts", "patterns": {"error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "analytics/sessions/route.ts", "patterns": {"direct-data": 1, "handle-api-error": 1, "handle-route-error": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "direct", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analytics/selections/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 3, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analytics/selections/success-patterns/route.ts", "patterns": {"error-object": 1, "status-500": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "analytics/recommendations/route.ts", "patterns": {"handle-route-error": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "analytics/quality/route.ts", "patterns": {"handle-api-error": 1, "handle-route-error": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analytics/profiles/performance/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "analytics/productivity/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "analytics/export/route.ts", "patterns": {"error-object": 1, "handle-api-error": 4, "status-200": 2, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "analytics/chapters/route.ts", "patterns": {"handle-api-error": 4, "handle-route-error": 2, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analytics/behavioral/route.ts", "patterns": {"direct-data": 1, "error-object": 4, "handle-api-error": 3, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "direct", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analytics/ai-usage/route.ts", "patterns": {"try-catch": 2}, "hasTryCatch": true, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analysis/voice-consistency/route.ts", "patterns": {"error-object": 4, "handle-api-error": 3, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analysis/voice/route.ts", "patterns": {"success-true": 1, "error-object": 2, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "analysis/progress/route.ts", "patterns": {"success-true": 2, "error-object": 5, "handle-api-error": 3, "status-400": 1, "status-500": 4, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "analysis/content/route.ts", "patterns": {"success-true": 1, "error-object": 2, "status-400": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "analysis/character-development-grid/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 4, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "analysis/character-arc-patterns/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 4, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "analysis/book-summary/route.ts", "patterns": {"success-true": 1, "handle-api-error": 3, "handle-route-error": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "analysis/auto-fix/route.ts", "patterns": {"success-true": 1, "error-object": 3, "handle-api-error": 1, "status-400": 1, "status-500": 1, "try-catch": 4}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "analysis/arc-suggestions/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 4, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "analysis/arc-predictions/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 4, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 4, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "ai/typed-stream/route.ts", "patterns": {"status-400": 1, "status-401": 1, "status-500": 1, "try-catch": 3}, "hasTryCatch": true, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "ai/suggestions/route.ts", "patterns": {"success-true": 1, "error-object": 7, "handle-api-error": 6, "handle-route-error": 4, "status-201": 1, "status-400": 3, "status-500": 4, "manual-pagination": 1, "try-catch": 4}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "ai/suggestions/[id]/route.ts", "patterns": {"success-true": 1, "error-object": 4, "handle-api-error": 3, "handle-route-error": 3, "status-400": 1, "status-500": 3, "try-catch": 3}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "ai/structured-content/route.ts", "patterns": {"handle-route-error": 2, "status-400": 2, "status-401": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "ai/stream-content/route.ts", "patterns": {"status-400": 1, "status-401": 1, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "ai/chat/route.ts", "patterns": {"status-400": 1, "status-500": 2, "try-catch": 2}, "hasTryCatch": true, "responseStyle": "unknown", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "agents/suggestions/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 2, "status-500": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "agents/suggestions/feedback/route.ts", "patterns": {"success-true": 1, "error-object": 1, "handle-api-error": 2, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 2, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "agents/initialize/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 3, "handle-route-error": 2, "status-400": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 3, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function", "?:const|async\\s+function"]}, {"file": "agents/generate/route.ts", "patterns": {"success-true": 9, "error-object": 9, "handle-api-error": 17, "status-400": 1, "status-403": 2, "status-500": 7, "try-catch": 5}, "hasTryCatch": true, "hasErrorHandling": 17, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "agents/edit/route.ts", "patterns": {"api-response-helper": 1, "api-error-helper": 6, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 6, "responseStyle": "helper", "methods": ["?:const|async\\s+function"]}, {"file": "agents/chat/route.ts", "patterns": {"success-true": 1, "error-object": 2, "handle-api-error": 1, "status-500": 2, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "agents/adjust-plan/route.ts", "patterns": {"success-true": 1, "handle-api-error": 1, "try-catch": 2}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "success-boolean", "methods": ["?:const|async\\s+function"]}, {"file": "admin/export-data/route.ts", "patterns": {"error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "achievements/route.ts", "patterns": {"error-object": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}, {"file": "achievements/stats/route.ts", "patterns": {"direct-data": 1, "error-object": 1, "handle-api-error": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "direct", "methods": ["?:const|async\\s+function"]}, {"file": "achievements/check/route.ts", "patterns": {"error-object": 1, "status-500": 1, "try-catch": 1}, "hasTryCatch": true, "hasErrorHandling": 1, "responseStyle": "unknown", "methods": ["?:const|async\\s+function"]}], "recommendations": ["Standardize on data-wrapper response style ({ data: ... })", "Use handleAPIError for all error responses", "Ensure all routes have try-catch blocks", "Use consistent status codes (201 for creation, 200 for success)", "Consider using unified middleware for common patterns"]}