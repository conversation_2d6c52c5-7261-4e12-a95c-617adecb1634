'use client'

import { logger } from '@/lib/services/logger'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useToast } from '@/hooks/use-toast'
import {
  Mic,
  BookO<PERSON>,
  User,
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  Upload,
  FileText,
  <PERSON>rkles,
  Save,
  X,
  Download,
  Co<PERSON>,
  Trash2
} from 'lucide-react'

interface VoiceProfile {
  id: string
  name: string
  description?: string
  type: 'author' | 'character' | 'narrator'
  confidence: number
  training_samples_count: number
  total_words_analyzed: number
  created_at: string
  updated_at: string
}

interface VoiceTrainerEnhancedProps {
  projectId?: string
  seriesId?: string
  characterId?: string
  onProfileCreated?: (profile: VoiceProfile) => void
  existingProfile?: VoiceProfile
}

export function VoiceTrainerEnhanced({
  projectId,
  seriesId,
  characterId,
  onProfileCreated,
  existingProfile
}: VoiceTrainerEnhancedProps) {
  const [isCreating, setIsCreating] = useState(false)
  const [isTraining, setIsTraining] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [profileName, setProfileName] = useState(existingProfile?.name || '')
  const [profileDescription, setProfileDescription] = useState(existingProfile?.description || '')
  const [profileType, setProfileType] = useState<'author' | 'character' | 'narrator'>(
    existingProfile?.type || 'author'
  )
  const [trainingText, setTrainingText] = useState('')
  const [trainingTexts, setTrainingTexts] = useState<{ id: string; text: string; source: string }[]>([])
  const [currentProfile, setCurrentProfile] = useState<VoiceProfile | null>(existingProfile || null)
  const [activeTab, setActiveTab] = useState('create')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const handleCreateProfile = async () => {
    if (!profileName.trim()) {
      toast({ title: 'Error', description: 'Please enter a profile name', variant: 'destructive' })
      return
    }

    setIsCreating(true)
    try {
      const response = await fetch('/api/voice-profiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: profileName,
          description: profileDescription,
          type: profileType,
          projectId,
          seriesId,
          characterId,
          isGlobal: !projectId && !seriesId && !characterId
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create profile')
      }

      const { profile } = await response.json()
      setCurrentProfile(profile)
      setActiveTab('train')
      toast({ title: 'Success', description: 'Voice profile created successfully' })
      
      if (onProfileCreated) {
        onProfileCreated(profile)
      }
    } catch (error) {
      logger.error('Error creating profile:', error)
      toast({ title: 'Error', description: error instanceof Error ? error.message : 'Failed to create voice profile', variant: 'destructive' })
    } finally {
      setIsCreating(false)
    }
  }

  const handleAddTrainingText = (text: string, source: string = 'manual_entry') => {
    if (text.trim().length < 100) {
      toast({ title: 'Error', description: 'Training text must be at least 100 characters', variant: 'destructive' })
      return
    }

    const id = `text_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    setTrainingTexts([...trainingTexts, { id, text: text.trim(), source }])
    setTrainingText('')
    toast({ title: 'Success', description: 'Training text added' })
  }

  const handleRemoveTrainingText = (id: string) => {
    setTrainingTexts(trainingTexts.filter(t => t.id !== id))
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setIsUploading(true)
    const allowedTypes = ['text/plain', 'text/markdown', 'application/x-markdown']
    
    try {
      for (const file of Array.from(files)) {
        if (!allowedTypes.includes(file.type) && !file.name.endsWith('.md')) {
          toast({ title: 'Error', description: `${file.name} is not a supported text file`, variant: 'destructive' })
          continue
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB limit
          toast({ title: 'Error', description: `${file.name} is too large (max 5MB)`, variant: 'destructive' })
          continue
        }

        const text = await file.text()
        
        // Split large texts into chunks if needed
        const chunkSize = 5000 // characters
        if (text.length > chunkSize) {
          const chunks = []
          for (let i = 0; i < text.length; i += chunkSize) {
            chunks.push(text.slice(i, i + chunkSize))
          }
          
          chunks.forEach((chunk, index) => {
            handleAddTrainingText(chunk, `file_upload:${file.name}:chunk_${index + 1}`)
          })
          
          toast({ title: 'Success', description: `${file.name} split into ${chunks.length} training samples` })
        } else {
          handleAddTrainingText(text, `file_upload:${file.name}`)
        }
      }
    } catch (error) {
      logger.error('Error uploading files:', error)
      toast({ title: 'Error', description: 'Failed to process uploaded files', variant: 'destructive' })
    } finally {
      setIsUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleTrainProfile = async () => {
    if (!currentProfile) {
      toast({ title: 'Error', description: 'Please create a profile first', variant: 'destructive' })
      return
    }

    if (trainingTexts.length === 0) {
      toast({ title: 'Error', description: 'Please add at least one training text', variant: 'destructive' })
      return
    }

    setIsTraining(true)
    try {
      const response = await fetch(`/api/voice-profiles/${currentProfile.id}/train`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          texts: trainingTexts.map(t => t.text),
          source: trainingTexts.some(t => t.source.startsWith('file_upload')) 
            ? 'file_upload' 
            : 'manual_entry'
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to train profile')
      }

      const { profile, trainedSamples } = await response.json()
      setCurrentProfile(profile)
      setTrainingTexts([])
      toast({ title: 'Success', description: `Successfully trained with ${trainedSamples} samples` })
    } catch (error) {
      logger.error('Error training profile:', error)
      toast({ title: 'Error', description: error instanceof Error ? error.message : 'Failed to train voice profile', variant: 'destructive' })
    } finally {
      setIsTraining(false)
    }
  }

  const handleExportProfile = async () => {
    if (!currentProfile) return

    try {
      const response = await fetch(`/api/voice-profiles/${currentProfile.id}`)
      if (!response.ok) throw new Error('Failed to fetch profile')
      
      const { profile } = await response.json()
      
      const exportData = {
        name: profile.name,
        description: profile.description,
        type: profile.type,
        patterns: profile.patterns,
        exported_at: new Date().toISOString(),
        bookscribe_version: '1.0.0'
      }
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `voice-profile-${profile.name.toLowerCase().replace(/\s+/g, '-')}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      toast({ title: 'Success', description: 'Voice profile exported successfully' })
    } catch (error) {
      logger.error('Error exporting profile:', error)
      toast({ title: 'Error', description: 'Failed to export voice profile', variant: 'destructive' })
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'author':
        return <BookOpen className="h-4 w-4" />
      case 'character':
        return <User className="h-4 w-4" />
      case 'narrator':
        return <Mic className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-success'
    if (confidence >= 0.6) return 'text-warning'
    return 'text-error'
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              Voice Profile Trainer
            </CardTitle>
            <CardDescription>
              {currentProfile 
                ? `Training "${currentProfile.name}" voice profile`
                : 'Create and train a custom voice profile for consistent writing style'
              }
            </CardDescription>
          </div>
          {currentProfile && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportProfile}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="create">
              {currentProfile ? 'Profile Details' : 'Create Profile'}
            </TabsTrigger>
            <TabsTrigger value="train" disabled={!currentProfile}>
              Train Profile
            </TabsTrigger>
            <TabsTrigger value="upload" disabled={!currentProfile}>
              Upload Files
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-4">
            {currentProfile ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(currentProfile.type)}
                    <h3 className="text-lg font-semibold">{currentProfile.name}</h3>
                    <Badge variant="outline">{currentProfile.type}</Badge>
                  </div>
                  <Badge className={getConfidenceColor(currentProfile.confidence)}>
                    {Math.round(currentProfile.confidence * 100)}% confidence
                  </Badge>
                </div>

                {currentProfile.description && (
                  <p className="text-sm text-muted-foreground">
                    {currentProfile.description}
                  </p>
                )}

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label className="text-muted-foreground">Training Samples</Label>
                    <p className="font-medium">{currentProfile.training_samples_count}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Words Analyzed</Label>
                    <p className="font-medium">{currentProfile.total_words_analyzed.toLocaleString()}</p>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Continue training this profile by adding more text samples in the "Train Profile" or "Upload Files" tabs.
                  </AlertDescription>
                </Alert>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="profileName">Profile Name</Label>
                  <Input
                    id="profileName"
                    placeholder="e.g., My Writing Style, Character Voice"
                    value={profileName}
                    onChange={(e) => setProfileName(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="profileDescription">Description (Optional)</Label>
                  <Textarea
                    id="profileDescription"
                    placeholder="Describe this voice profile..."
                    value={profileDescription}
                    onChange={(e) => setProfileDescription(e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="profileType">Profile Type</Label>
                  <Select value={profileType} onValueChange={(value: any) => setProfileType(value)}>
                    <SelectTrigger id="profileType">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="author">
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-4 w-4" />
                          Author Voice
                        </div>
                      </SelectItem>
                      <SelectItem value="character">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Character Voice
                        </div>
                      </SelectItem>
                      <SelectItem value="narrator">
                        <div className="flex items-center gap-2">
                          <Mic className="h-4 w-4" />
                          Narrator Voice
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button 
                  onClick={handleCreateProfile} 
                  disabled={isCreating || !profileName.trim()}
                  className="w-full"
                >
                  {isCreating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Profile...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Voice Profile
                    </>
                  )}
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="train" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="trainingText">Training Text</Label>
                <Textarea
                  id="trainingText"
                  placeholder="Paste or type a sample of the writing style you want to capture (minimum 100 characters)..."
                  value={trainingText}
                  onChange={(e) => setTrainingText(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <div className="flex items-center justify-between mt-2">
                  <span className="text-sm text-muted-foreground">
                    {trainingText.length} characters
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleAddTrainingText(trainingText, 'manual_entry')}
                    disabled={trainingText.trim().length < 100}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Sample
                  </Button>
                </div>
              </div>

              {trainingTexts.length > 0 && (
                <div className="space-y-2">
                  <Label>Training Samples ({trainingTexts.length})</Label>
                  <ScrollArea className="h-48 border rounded-lg p-2">
                    <div className="space-y-2">
                      {trainingTexts.map((item) => (
                        <div key={item.id} className="p-3 bg-muted rounded-lg relative group">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleRemoveTrainingText(item.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                          <p className="text-sm line-clamp-2 pr-8">{item.text}</p>
                          <div className="flex items-center justify-between mt-1">
                            <p className="text-xs text-muted-foreground">
                              {item.text.split(/\s+/).length} words
                            </p>
                            <Badge variant="outline" className="text-xs">
                              {item.source.replace(/_/g, ' ')}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              <Alert>
                <Sparkles className="h-4 w-4" />
                <AlertDescription>
                  Add multiple text samples for better accuracy. The more diverse samples you provide, 
                  the better the AI will understand and replicate the voice.
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleTrainProfile}
                disabled={isTraining || trainingTexts.length === 0}
                className="w-full"
              >
                {isTraining ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Training Profile...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Train Voice Profile ({trainingTexts.length} samples)
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="upload" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Upload Text Files</Label>
                <div className="mt-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".txt,.md"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="file-upload"
                  />
                  <label
                    htmlFor="file-upload"
                    className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:border-primary hover:bg-muted/50 transition-colors"
                  >
                    {isUploading ? (
                      <div className="flex flex-col items-center">
                        <Loader2 className="h-8 w-8 mb-2 animate-spin text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">Processing files...</span>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center">
                        <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          Click to upload text files (.txt, .md)
                        </span>
                        <span className="text-xs text-muted-foreground mt-1">
                          Multiple files supported, max 5MB each
                        </span>
                      </div>
                    )}
                  </label>
                </div>
              </div>

              <Alert>
                <FileText className="h-4 w-4" />
                <AlertDescription>
                  Upload text or markdown files containing writing samples. Large files will be 
                  automatically split into manageable chunks for training.
                </AlertDescription>
              </Alert>

              {trainingTexts.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Uploaded Samples ({trainingTexts.filter(t => t.source.startsWith('file_upload')).length})</Label>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setTrainingTexts(trainingTexts.filter(t => !t.source.startsWith('file_upload')))}
                      disabled={trainingTexts.filter(t => t.source.startsWith('file_upload')).length === 0}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Clear Uploads
                    </Button>
                  </div>
                  <ScrollArea className="h-48 border rounded-lg p-2">
                    <div className="space-y-2">
                      {trainingTexts
                        .filter(t => t.source.startsWith('file_upload'))
                        .map((item) => (
                          <div key={item.id} className="p-3 bg-muted rounded-lg">
                            <p className="text-sm line-clamp-2">{item.text}</p>
                            <div className="flex items-center justify-between mt-1">
                              <p className="text-xs text-muted-foreground">
                                {item.text.split(/\s+/).length} words
                              </p>
                              <Badge variant="outline" className="text-xs">
                                {item.source.split(':')[1]}
                              </Badge>
                            </div>
                          </div>
                        ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}