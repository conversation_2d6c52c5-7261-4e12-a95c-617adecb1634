import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export async function POST(request: NextRequest) {
  try {
    logger.info('Creating sample project via API...')
    
    const supabase = await createTypedServerClient()
    
    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      logger.error('Authentication error:', authError)
      return handleAPIError(new AuthenticationError())
    }
    
    logger.info('User authenticated:', user.id)
    
    // Get the sample project data from the request
    const { sampleProject } = await request.json()
    if (!sampleProject) {
      return handleAPIError(new ValidationError('Invalid request'))
    }
    
    logger.info('Creating sample project:', sampleProject.title)
    
    // Convert camelCase to snake_case for database compatibility
    function camelToSnake(str: string): string {
      return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
    }
    
    function convertKeysToSnakeCase(obj: Record<string, unknown>): Record<string, unknown> {
      const converted: Record<string, unknown> = {}
      for (const [key, value] of Object.entries(obj)) {
        converted[camelToSnake(key)] = value
      }
      return converted
    }
    
    // Create the project with sample data
    const convertedSelections = convertKeysToSnakeCase(sampleProject.selections)
    const projectData = {
      user_id: user.id,
      title: sampleProject.title,
      description: sampleProject.description,
      status: 'planning',
      ...convertedSelections
    }
    
    logger.info('Creating project with data:', projectData)
    
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert([projectData])
      .select()
      .single()
    
    if (projectError) {
      logger.error('Project creation error:', projectError)
      return NextResponse.json({ 
        error: 'Failed to create project', 
        details: projectError.message 
      }, { status: 500 })
    }
    
    logger.info('Project created successfully:', project.id)
    
    // Add sample story structure
    const sampleActs = [
      {
        project_id: project.id,
        act_number: 1,
        description: 'Setup and inciting incident',
        key_events: ['Character introduction', 'Normal world establishment', 'Inciting incident', 'First plot point']
      },
      {
        project_id: project.id,
        act_number: 2,
        description: 'Rising action and obstacles',
        key_events: ['Character development', 'Obstacles and conflicts', 'Midpoint reversal', 'Crisis moment']
      },
      {
        project_id: project.id,
        act_number: 3,
        description: 'Climax and resolution',
        key_events: ['Final confrontation', 'Climax', 'Resolution', 'New normal']
      }
    ]
    
    for (const act of sampleActs) {
      const { error: actError } = await supabase.from('story_arcs').insert(act)
      if (actError) {
        logger.warn('Error creating story arc:', actError)
      }
    }
    
    // Add sample characters
    const sampleCharacters = [
      {
        project_id: project.id,
        name: 'Protagonist',
        role: 'protagonist',
        description: 'The main character of the story',
        backstory: 'A compelling protagonist backstory',
        personality_traits: ['brave', 'curious', 'determined'],
        character_arc: { start: 'naive', middle: 'challenged', end: 'wise' }
      },
      {
        project_id: project.id,
        name: 'Antagonist',
        role: 'antagonist',
        description: 'The primary opposition',
        backstory: 'A compelling villain backstory',
        personality_traits: ['cunning', 'powerful', 'ruthless'],
        character_arc: { start: 'confident', middle: 'threatened', end: 'defeated' }
      }
    ]
    
    for (const character of sampleCharacters) {
      const { error: characterError } = await supabase.from('characters').insert(character)
      if (characterError) {
        logger.warn('Error creating character:', characterError)
      }
    }
    
    // Add sample chapters
    const chapterCount = Math.min(sampleProject.chapters || 5, 5) // Limit to 5 sample chapters
    for (let i = 1; i <= chapterCount; i++) {
      const chapter = {
        project_id: project.id,
        chapter_number: i,
        title: `Chapter ${i}`,
        target_word_count: Math.floor((sampleProject.wordCount || 80000) / (sampleProject.chapters || 20)),
        outline: `Sample outline for chapter ${i}`,
        status: 'planned'
      }
      
      const { error: chapterError } = await supabase.from('chapters').insert(chapter)
      if (chapterError) {
        logger.warn(`Error creating chapter ${i}:`, chapterError)
      }
    }
    
    // Add initial story bible entries
    const storyBibleEntries = [
      {
        project_id: project.id,
        entry_type: 'setting',
        entry_key: 'primary_setting',
        entry_data: {
          category: 'Setting',
          title: 'Primary Setting',
          content: `This ${sampleProject.genre} story takes place in a ${sampleProject.selections.geographicSetting || 'fictional'} setting.`,
          tags: [sampleProject.genre.toLowerCase(), 'setting']
        }
      },
      {
        project_id: project.id,
        entry_type: 'themes',
        entry_key: 'major_themes',
        entry_data: {
          category: 'Themes',
          title: 'Major Themes',
          content: `Key themes include: ${Array.isArray(sampleProject.selections.majorThemes) ? sampleProject.selections.majorThemes.join(', ') : 'Coming of age, Good vs Evil'}`,
          tags: ['themes', 'analysis']
        }
      }
    ]
    
    for (const entry of storyBibleEntries) {
      const { error: bibleError } = await supabase.from('story_bible').insert(entry)
      if (bibleError) {
        logger.warn('Error creating story bible entry:', bibleError)
      }
    }
    
    logger.info('Sample project created successfully:', project.id)
    
    return NextResponse.json({ 
      success: true, 
      projectId: project.id,
      message: 'Sample project created successfully'
    })
    
  } catch (error) {
    logger.error('Sample project creation error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500 })
  }
}
