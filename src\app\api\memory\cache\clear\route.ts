import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { UnifiedResponse } from '@/lib/utils/response'
import { getMemoryManager } from '@/lib/memory/memory-instances'
import { logger } from '@/lib/services/logger'

const clearCacheSchema = z.object({
  projectId: z.string().uuid(),
  cacheTypes: z.array(z.enum(['embeddings', 'contexts', 'analysis', 'all'])).optional().default(['all']),
  force: z.boolean().optional().default(false)
})

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const validation = clearCacheSchema.safeParse(body)
    
    if (!validation.success) {
      return UnifiedResponse.error('Invalid request data', 400, validation.error.errors)
    }

    const { projectId, cacheTypes, force } = validation.data
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Get memory manager
    const memoryManager = getMemoryManager(projectId)
    
    // Clear specified cache types
    const clearedItems = {
      embeddings: 0,
      contexts: 0,
      analysis: 0,
      total: 0
    }

    if (cacheTypes.includes('all') || cacheTypes.includes('embeddings')) {
      // Clear embedding cache
      const { error: embeddingError } = await supabase
        .from('content_embedding_cache')
        .delete()
        .eq('project_id', projectId)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Only clear cache older than 24h unless forced
        
      if (!embeddingError) {
        const { count } = await supabase
          .from('content_embedding_cache')
          .select('*', { count: 'exact', head: true })
          .eq('project_id', projectId)
        clearedItems.embeddings = count || 0
      }
    }

    if (cacheTypes.includes('all') || cacheTypes.includes('contexts')) {
      // Clear context cache from memory manager
      const contextsClearedCount = await memoryManager.clearContextCache({
        force,
        olderThan: force ? undefined : 24 * 60 * 60 * 1000 // 24 hours
      })
      clearedItems.contexts = contextsClearedCount || 0
    }

    if (cacheTypes.includes('all') || cacheTypes.includes('analysis')) {
      // Clear analysis cache
      const { error: analysisError } = await supabase
        .from('analysis_cache')
        .delete()
        .eq('project_id', projectId)
        
      if (!analysisError) {
        const { count } = await supabase
          .from('analysis_cache')
          .select('*', { count: 'exact', head: true })
          .eq('project_id', projectId)
        clearedItems.analysis = count || 0
      }
    }

    clearedItems.total = clearedItems.embeddings + clearedItems.contexts + clearedItems.analysis

    // Log cache clear event
    await supabase.from('system_events').insert({
      user_id: user.id,
      project_id: projectId,
      event_type: 'cache_cleared',
      metadata: {
        cacheTypes,
        clearedItems,
        force
      }
    })

    logger.info('Cache cleared', {
      projectId,
      userId: user.id,
      cacheTypes,
      clearedItems
    })

    return UnifiedResponse.success({
      message: 'Cache cleared successfully',
      clearedItems,
      cacheTypes: cacheTypes.includes('all') ? ['embeddings', 'contexts', 'analysis'] : cacheTypes
    })
  } catch (error) {
    logger.error('Cache clear error:', error)
    return UnifiedResponse.error('Failed to clear cache')
  }
})

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    
    if (!projectId) {
      return UnifiedResponse.error('Project ID is required', 400)
    }

    const validation = z.object({ projectId: z.string().uuid() }).safeParse({ projectId })
    if (!validation.success) {
      return UnifiedResponse.error('Invalid project ID format', 400)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Get cache statistics
    const [embeddingStats, analysisStats] = await Promise.all([
      supabase
        .from('content_embedding_cache')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId),
      supabase
        .from('analysis_cache')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId)
    ])

    const memoryManager = getMemoryManager(projectId)
    const contextCacheStats = memoryManager.getCacheStats()

    const cacheStats = {
      embeddings: {
        count: embeddingStats.count || 0,
        sizeEstimate: (embeddingStats.count || 0) * 6 * 1024 // ~6KB per embedding
      },
      contexts: {
        count: contextCacheStats.contextCount || 0,
        sizeEstimate: contextCacheStats.estimatedSize || 0
      },
      analysis: {
        count: analysisStats.count || 0,
        sizeEstimate: (analysisStats.count || 0) * 2 * 1024 // ~2KB per analysis
      },
      total: {
        count: (embeddingStats.count || 0) + (contextCacheStats.contextCount || 0) + (analysisStats.count || 0),
        sizeEstimate: ((embeddingStats.count || 0) * 6 * 1024) + 
                      (contextCacheStats.estimatedSize || 0) + 
                      ((analysisStats.count || 0) * 2 * 1024)
      }
    }

    return UnifiedResponse.success({
      projectId,
      cacheStats,
      lastCleared: contextCacheStats.lastCleared || null
    })
  } catch (error) {
    logger.error('Cache stats error:', error)
    return UnifiedResponse.error('Failed to fetch cache statistics')
  }
})