import { NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { type StandardResponse } from '@/lib/api/types';

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!;
    const supabase = await createTypedServerClient();
    
    const { data: profiles, error } = await supabase
      .from('selection_profiles')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });
    
    if (error) {
      logger.error('Error fetching user profiles:', error);
      return NextResponse.json<StandardResponse>(
        { success: false, error: 'Failed to fetch profiles' },
        { status: 500 }
      );
    }
    
    return NextResponse.json<StandardResponse>({
      success: true,
      data: { profiles }
    });
  } catch (error) {
    logger.error('Error in user profiles GET:', error);
    return NextResponse.json<StandardResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});