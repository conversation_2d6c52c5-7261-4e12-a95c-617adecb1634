"use client"

import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  BookOpen,
  LayoutDashboard,
  PlusCircle,
  FileText,
  Users,
  Settings,
  HelpCircle,
  ChevronLeft,
  Folder,
  BarChart3,
  BookOpenCheck,
  Globe,
  Trophy,
  Mic2,
  CreditCard,
  X,
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'
import type { ComponentType } from 'react'
import { useIsMobile } from '@/lib/utils/responsive-utils'
import { SPACING, TYPOGRAPHY, ANIMATIONS, ICON_SIZES, BACKDROPS, Z_INDEX } from '@/lib/config/ui-config'
import { ARIA_ROLES, SR_ONLY, FOCUS_CLASSES } from '@/lib/utils/accessibility-utils'

interface SidebarProps {
  className?: string
  isOpen?: boolean
  onClose?: () => void
}

const navigation = [
  {
    title: 'Overview',
    items: [
      {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutDashboard,
        description: 'Writing analytics and insights'
      },
      {
        title: 'All Projects', 
        href: '/projects',
        icon: Folder,
        description: 'Browse all your projects'
      },
      {
        title: 'Series Management',
        href: '/series',
        icon: BookOpenCheck,
        description: 'Organize multi-book series'
      },
      {
        title: 'Universe Management',
        href: '/universes',
        icon: Globe,
        description: 'Manage shared universes'
      },
      {
        title: 'Achievements',
        href: '/achievements',
        icon: Trophy,
        description: 'Track your writing accomplishments'
      },
      {
        title: 'Analytics',
        href: '/analytics',
        icon: BarChart3,
        description: 'Writing insights and productivity'
      }
    ]
  },
  {
    title: 'Create',
    items: [
      {
        title: 'New Project',
        href: '/projects/new',
        icon: PlusCircle,
        description: 'Start a new writing project'
      }
    ]
  },
  {
    title: 'Tools',
    items: [
      {
        title: 'Templates',
        href: '/templates',
        icon: FileText,
        description: 'Project templates and presets'
      },
      {
        title: 'Character Manager',
        href: '/characters',
        icon: Users,
        description: 'Manage your characters'
      },
      {
        title: 'Voice Profiles',
        href: '/voice-profiles',
        icon: Mic2,
        description: 'Writing style consistency'
      }
    ]
  },
  {
    title: 'Account',
    items: [
      {
        title: 'Billing',
        href: '/billing',
        icon: CreditCard,
        description: 'Subscription and payments'
      },
      {
        title: 'Settings',
        href: '/settings',
        icon: Settings,
        description: 'Account and preferences'
      },
      {
        title: 'Help & Support',
        href: '/help',
        icon: HelpCircle,
        description: 'Documentation and support'
      }
    ]
  }
]

interface NavItemProps {
  href: string
  icon: ComponentType<{ className?: string }>
  title: string
  description: string
  isActive: boolean
  isCollapsed: boolean
  onClick?: () => void
}

function NavItem({ href, icon: Icon, title, description, isActive, isCollapsed, onClick }: NavItemProps) {
  const isMobile = useIsMobile()
  
  return (
    <Button
      asChild
      variant={isActive ? 'secondary' : 'ghost'}
      className={cn(
        'w-full justify-start h-auto',
        // Touch-friendly sizing on mobile
        isMobile ? `${SPACING.PADDING.MD} min-h-[56px]` : SPACING.PADDING.SM,
        isActive && 'bg-secondary',
        isCollapsed && `justify-center ${SPACING.PADDING.XS}`
      )}
      onClick={onClick}
      aria-label={isCollapsed ? title : undefined}
      aria-current={isActive ? 'page' : undefined}
    >
      <Link href={href}>
        <Icon className={cn(ICON_SIZES.SM, 'flex-shrink-0', !isCollapsed && 'mr-3')} />
        {!isCollapsed && (
          <div className="flex flex-col items-start text-left">
            <span className={cn(TYPOGRAPHY.PRESETS.LABEL)}>{title}</span>
            <span className={cn(TYPOGRAPHY.PRESETS.CAPTION, "line-clamp-1")}>
              {description}
            </span>
          </div>
        )}
      </Link>
    </Button>
  )
}

export function Sidebar({ className, isOpen = true, onClose }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const isMobile = useIsMobile()
  
  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed)
  }
  
  // Close sidebar on route change for mobile
  useEffect(() => {
    if (isMobile && onClose) {
      onClose()
    }
  }, [pathname, isMobile, onClose])
  
  return (
    <>
      {/* Mobile backdrop */}
      {isMobile && isOpen && (
        <div
          className={cn('fixed inset-0', Z_INDEX.MODAL_BACKDROP, 'bg-background/80', BACKDROPS.BLUR.SM, 'md:hidden')}
          onClick={onClose}
          aria-hidden="true"
        />
      )}
      
      <aside 
        className={cn(
          'relative flex flex-col h-full bg-background border-r',
          ANIMATIONS.TRANSITION.ALL,
          // Mobile: full screen overlay
          isMobile && cn('fixed inset-y-0 left-0', Z_INDEX.MODAL, 'w-72'),
          // Desktop: responsive width
          !isMobile && (isCollapsed ? 'w-16' : 'w-64'),
          // Mobile: slide in/out
          isMobile && (isOpen ? 'translate-x-0' : '-translate-x-full'),
          className
        )}
        role={ARIA_ROLES.NAVIGATION}
        aria-label="Main navigation sidebar"
        aria-expanded={!isCollapsed}
      >
        {/* Header */}
        <div className={cn(
          "flex items-center justify-between border-b",
          SPACING.PADDING.MD
        )}>
          {(!isCollapsed || isMobile) && (
            <Link href="/dashboard" className={cn('flex items-center', SPACING.GAP.SM)} onClick={onClose}>
              <BookOpen className={ICON_SIZES.MD} />
              <span className={cn(TYPOGRAPHY.WEIGHT.SEMIBOLD)}>BookScribe AI</span>
            </Link>
          )}
          
          <div className={cn('flex items-center', SPACING.GAP.SM)}>
            {/* Mobile close button */}
            {isMobile && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-8 w-8 md:hidden"
                aria-label="Close sidebar"
              >
                <X className={ICON_SIZES.SM} />
              </Button>
            )}
            
            {/* Desktop collapse button */}
            {!isMobile && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleCollapsed}
                className={cn(
                  'h-8 w-8',
                  isCollapsed && 'mx-auto'
                )}
                aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
              >
                <ChevronLeft className={cn(
                  ICON_SIZES.SM,
                  ANIMATIONS.TRANSITION.TRANSFORM,
                  isCollapsed && 'rotate-180'
                )} />
              </Button>
            )}
          </div>
        </div>
      
      {/* Navigation */}
      <ScrollArea className={cn('flex-1', SPACING.PADDING.SM, 'py-4')}>
        <nav 
          className={SPACING.SPACE_Y.LG}
          role={ARIA_ROLES.NAVIGATION}
          aria-label="Sidebar navigation"
        >
          {navigation.map((section) => (
            <div key={section.title}>
              {!isCollapsed && (
                <h4 className={cn('mb-2', SPACING.PADDING.SM, TYPOGRAPHY.SIZE.XS, TYPOGRAPHY.WEIGHT.SEMIBOLD, 'text-muted-foreground uppercase tracking-wider')}>
                  {section.title}
                </h4>
              )}
              <div className={SPACING.SPACE_Y.XS}>
                {section.items.map((item) => (
                  <NavItem
                    key={item.href}
                    href={item.href}
                    icon={item.icon}
                    title={item.title}
                    description={item.description}
                    isActive={pathname === item.href || pathname.startsWith(item.href + '/')}
                    isCollapsed={isCollapsed}
                    onClick={onClose}
                  />
                ))}
              </div>
              {!isCollapsed && <Separator className="mt-4" />}
            </div>
          ))}
        </nav>
      </ScrollArea>
      
      {/* Footer */}
      <div className={cn(SPACING.PADDING.MD, 'border-t')}>
        {!isCollapsed ? (
          <div className={cn(TYPOGRAPHY.SIZE.XS, 'text-muted-foreground', SPACING.SPACE_Y.XS)}>
            <p>BookScribe AI v1.0</p>
            <p>AI-Powered Novel Writing</p>
          </div>
        ) : (
          <div className="flex justify-center">
            <BookOpen className={cn(ICON_SIZES.SM, 'text-muted-foreground')} />
          </div>
        )}
      </div>
    </aside>
    </>
  )
}