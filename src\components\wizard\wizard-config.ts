import { 
  Book<PERSON><PERSON>, 
  <PERSON>rk<PERSON>, 
  Target, 
  Users, 
  Globe,
  Settings,
  CreditCard
} from 'lucide-react';

export interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

export const wizardSteps: WizardStep[] = [
  { id: "basics", title: "Project Basics", description: "Set up your project foundation", icon: BookOpen },
  { id: "genre", title: "Genre & Style", description: "Define your story's genre and tone", icon: Sparkles },
  { id: "structure", title: "Structure & Pacing", description: "Plan your narrative flow", icon: Target },
  { id: "characters", title: "Characters & World", description: "Create your story universe", icon: Users },
  { id: "themes", title: "Themes & Content", description: "Establish themes and guidelines", icon: Globe },
  { id: "technical", title: "Technical Specs", description: "Set word counts and chapters", icon: Settings },
  { id: "payment", title: "Complete Setup", description: "Complete payment and create project", icon: CreditCard },
];

// Demo data for different themes/genres
export const demoDataSets = {
  fantasy: {
    title: "The Crystal Kingdoms",
    description: "A fantasy epic about a young mage discovering ancient crystal magic",
    genre: "fantasy",
    subgenre: "epic-fantasy",
    tone: "adventurous",
    targetAudience: "young-adult",
    protagonist: "Aria Stormwind",
    antagonist: "Lord Shadowmere",
    setting: "Mystical realm of Aethermoor",
    wordCount: "80000",
    chapters: "25",
    themes: "Power and corruption, Coming of age, Sacrifice and redemption",
    structure: "three-act",
    pacing: "medium",
    narrativeVoice: "third-limited",
    tense: "past",
    timePeriod: "Medieval fantasy era",
    protagonistName: "Aria Stormwind",
    universeId: "none",
    seriesId: "none",
    isStandalone: "true"
  },
  mystery: {
    title: "The Midnight Detective",
    description: "A noir mystery following a private investigator uncovering city corruption",
    genre: "mystery",
    subgenre: "noir",
    tone: "dark",
    targetAudience: "adult",
    protagonist: "Sam Carter",
    antagonist: "The Shadow Syndicate",
    setting: "1940s Chicago",
    wordCount: "75000",
    chapters: "22",
    themes: "Justice vs law, Moral ambiguity, Urban decay",
    structure: "three-act",
    pacing: "fast",
    narrativeVoice: "first",
    tense: "past",
    timePeriod: "1940s",
    protagonistName: "Sam Carter",
    universeId: "none",
    seriesId: "none",
    isStandalone: "true"
  },
  romance: {
    title: "Love in Tuscany",
    description: "A contemporary romance about finding love while renovating a villa in Italy",
    genre: "romance",
    subgenre: "contemporary",
    tone: "heartwarming",
    targetAudience: "adult",
    protagonist: "Emma Richardson",
    antagonist: "Internal conflicts and past trauma",
    setting: "Tuscan countryside",
    wordCount: "70000",
    chapters: "20",
    themes: "Second chances, Self-discovery, Healing through love",
    structure: "three-act",
    pacing: "medium",
    narrativeVoice: "third-limited",
    tense: "past",
    timePeriod: "Present day",
    protagonistName: "Emma Richardson",
    universeId: "none",
    seriesId: "none",
    isStandalone: "true"
  },
  scifi: {
    title: "Colony Ship Prometheus",
    description: "A hard sci-fi thriller about humanity's first interstellar colony ship",
    genre: "science-fiction",
    subgenre: "hard-scifi",
    tone: "suspenseful",
    targetAudience: "adult",
    protagonist: "Captain Maya Chen",
    antagonist: "AI system malfunction and saboteur",
    setting: "Generation ship in deep space",
    wordCount: "90000",
    chapters: "28",
    themes: "Survival, Human nature under pressure, Technology and ethics",
    structure: "three-act",
    pacing: "fast",
    narrativeVoice: "multiple",
    tense: "present",
    timePeriod: "2157",
    protagonistName: "Captain Maya Chen",
    universeId: "none",
    seriesId: "none",
    isStandalone: "true"
  }
};

// Guided tour content for each step
export const guidedTourContent = [
  {
    title: "Welcome to Your Writing Journey!",
    description: "Let's create your novel project step by step. This wizard will help you define every aspect of your story.",
    tips: [
      "Take your time with each step - these choices shape your entire novel",
      "You can always adjust settings later",
      "The AI will use these details to provide personalized writing assistance"
    ]
  },
  {
    title: "Choose Your Genre & Style",
    description: "Genre and tone are crucial - they determine the AI's writing style, pacing, and story elements.",
    tips: [
      "Your primary genre sets the overall framework",
      "Subgenre adds specific flavor and tropes",
      "Tone affects dialogue, descriptions, and narrative voice"
    ]
  },
  {
    title: "Structure Your Story",
    description: "Good structure is the backbone of a compelling narrative. Choose what works for your story.",
    tips: [
      "Three-act structure is classic and versatile",
      "Save the Cat provides detailed story beats",
      "Pacing affects reader engagement - match it to your genre"
    ]
  },
  {
    title: "Build Your World",
    description: "Characters and setting bring your story to life. Be specific - the AI uses these details throughout.",
    tips: [
      "Strong protagonists drive the story forward",
      "Compelling antagonists create meaningful conflict",
      "Rich settings immerse readers in your world"
    ]
  },
  {
    title: "Define Your Themes",
    description: "Themes give your story depth, while content ratings help target the right audience.",
    tips: [
      "Themes provide deeper meaning beyond the surface plot",
      "Content rating affects language, violence, and romantic content",
      "Consider what message or experience you want readers to take away"
    ]
  },
  {
    title: "Set Your Technical Specifications",
    description: "These details help the AI create appropriately sized chapters and structure the narrative.",
    tips: [
      "Word count affects story scope - more words allow for more subplots and development",
      "Chapter count determines pacing - more chapters create faster pacing",
      "POV characters affect narrative complexity and reader connection"
    ]
  },
  {
    title: "Complete Your Project Setup",
    description: "You're almost there! Complete the payment to unlock all of BookScribe's AI-powered writing features.",
    tips: [
      "One-time project fee includes unlimited AI assistance for this book",
      "Your payment information is secure and encrypted",
      "You'll have immediate access to all features after payment"
    ]
  }
];

export interface FormData {
  title: string;
  description: string;
  genre: string;
  subgenre: string;
  targetAudience: string;
  wordCount: string;
  chapters: string;
  tone: string;
  protagonist: string;
  antagonist: string;
  setting: string;
  themes: string;
  structure: string;
  pacing: string;
  narrativeVoice: string;
  tense: string;
  timePeriod: string;
  protagonistName: string;
  universeId: string;
  seriesId: string;
  isStandalone: string;
  contentRating?: string;
  triggerWarnings?: string;
  povCharacters?: string;
  [key: string]: string | undefined;
}

export const initialFormData: FormData = {
  title: "",
  description: "",
  genre: "",
  subgenre: "",
  tone: "",
  targetAudience: "adult",
  protagonist: "",
  antagonist: "",
  setting: "",
  wordCount: "80000",
  chapters: "25",
  themes: "",
  structure: "three-act",
  pacing: "medium",
  narrativeVoice: "third-limited",
  tense: "past",
  timePeriod: "",
  protagonistName: "",
  universeId: "none",
  seriesId: "none",
  isStandalone: "true"
};