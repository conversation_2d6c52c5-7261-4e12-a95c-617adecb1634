import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { getTierByPriceId } from '@/lib/billing/subscription-tiers'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { baseSchemas } from '@/lib/validation/common-schemas'

const checkoutSchema = z.object({
  priceId: z.string()
    .min(1, 'Price ID is required')
    .regex(/^price_[a-zA-Z0-9]+$/, 'Invalid price ID format'),
  successUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
  allowPromotionCodes: z.boolean().optional().default(true),
  billingAddressCollection: z.enum(['auto', 'required']).optional().default('auto'),
  customerEmail: z.string().email().optional(),
  metadata: z.record(z.string()).optional()
})

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: checkoutSchema,
    rateLimitKey: 'checkout-session',
    rateLimitCost: 5,
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const body = await req.json();
      const { priceId } = body;

      // Verify the price ID corresponds to a valid tier
      const tier = getTierByPriceId(priceId);
      if (!tier) {
        return { 
          valid: false, 
          error: 'Invalid subscription tier' 
        };
      }

      // Check if user already has an active subscription
      const supabase = await createTypedServerClient();
      const { data: existingSubscription } = await supabase
        .from('user_subscriptions')
        .select('id, tier_id, status')
        .eq('user_id', user.id)
        .in('status', ['active', 'trialing'])
        .single();

      if (existingSubscription) {
        return { 
          valid: false, 
          error: 'You already have an active subscription. Please manage it from your billing settings.' 
        };
      }

      // Check for recent checkout sessions to prevent abuse
      const recentSessions = await supabase
        .from('checkout_sessions')
        .select('id')
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 3600000).toISOString()) // Last hour
        .limit(3);

      if (recentSessions.data && recentSessions.data.length >= 3) {
        return { 
          valid: false, 
          error: 'Too many checkout attempts. Please try again later.' 
        };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { priceId, successUrl, cancelUrl, allowPromotionCodes, billingAddressCollection, customerEmail, metadata } = context.body;

  try {
    const supabase = await createTypedServerClient();

    // Get tier information (already validated)
    const tier = getTierByPriceId(priceId)!;

    // Check if user already has a Stripe customer ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    let customerId = profile?.stripe_customer_id

    // Create a new customer if needed
    if (!customerId) {
      try {
        const customer = await stripe.customers.create({
          email: customerEmail || user.email!,
          metadata: {
            userId: user.id,
            createdAt: new Date().toISOString(),
            source: 'checkout_session'
          },
        });
        customerId = customer.id;

        // Save the customer ID
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ stripe_customer_id: customerId })
          .eq('id', user.id);

        if (updateError) {
          logger.warn('Failed to save Stripe customer ID', {
            userId: user.id,
            customerId,
            error: updateError.message
          });
        }
      } catch (error) {
        logger.error('Failed to create Stripe customer', {
          userId: user.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          clientIP: context.clientIP
        });
        return UnifiedResponse.error('Failed to create billing account', 500);
      }
    }

    // Create checkout session
    let session: Stripe.Checkout.Session;
    try {
      const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_APP_URL;
      
      session = await stripe.checkout.sessions.create({
        customer: customerId,
        customer_email: !customerId ? (customerEmail || user.email!) : undefined,
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        success_url: successUrl || `${origin}/billing?checkout=success&session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: cancelUrl || `${origin}/billing?checkout=canceled`,
        metadata: {
          userId: user.id,
          tierId: tier.id,
          ...metadata
        },
        subscription_data: {
          metadata: {
            userId: user.id,
            tierId: tier.id,
            createdAt: new Date().toISOString()
          },
          trial_period_days: tier.trialDays || undefined,
        },
        allow_promotion_codes: allowPromotionCodes,
        billing_address_collection: billingAddressCollection,
        locale: 'auto',
        customer_update: customerId ? {
          address: 'auto',
          name: 'auto'
        } : undefined,
        phone_number_collection: {
          enabled: false
        },
        tax_id_collection: {
          enabled: true
        }
      });
    } catch (error) {
      logger.error('Stripe checkout session creation error', {
        userId: user.id,
        priceId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP: context.clientIP
      });
      
      if (error instanceof Error && error.message.includes('price')) {
        return UnifiedResponse.error('Invalid subscription plan selected', 400);
      }
      
      return UnifiedResponse.error('Failed to create checkout session', 500);
    }

    // Store checkout session for tracking
    await supabase
      .from('checkout_sessions')
      .insert({
        id: session.id,
        user_id: user.id,
        price_id: priceId,
        tier_id: tier.id,
        status: 'open',
        expires_at: new Date(session.expires_at * 1000).toISOString(),
        metadata: {
          ...metadata,
          clientIP: context.clientIP
        }
      });

    logger.info('Checkout session created', {
      userId: user.id,
      sessionId: session.id,
      tierId: tier.id,
      priceId,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      sessionId: session.id,
      url: session.url,
      expiresAt: session.expires_at,
      tier: {
        id: tier.id,
        name: tier.name,
        price: tier.price
      }
    });
  } catch (error) {
    logger.error('Checkout session error:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to process checkout request');
  }
});