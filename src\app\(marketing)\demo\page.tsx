"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SettingsButton } from "@/components/settings/settings-modal";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { 
  PenTool, 
  BookOpen, 
  Brain, 
  BarChart3, 
  Play,
  Star,
  Clock,
  Target,
  Sparkles,
  ArrowRight,
  Feather,
  Globe,
  CheckCircle
} from "lucide-react";
import { UnifiedProjectWizard } from "@/components/wizard/unified-project-wizard";
import { DemoEditorEnhanced } from "@/components/demo/demo-editor-enhanced";
import { DemoAgentsEnhanced } from "@/components/demo/demo-agents-enhanced";
import { DemoStoryBibleEnhanced } from "@/components/demo/demo-story-bible-enhanced";
import { DemoAnalyticsEnhanced } from "@/components/demo/demo-analytics-enhanced";
import { DemoUniverseEnhanced } from "@/components/demo/demo-universe-enhanced";
import { DemoProjectSetupEnhanced } from "@/components/demo/demo-project-setup-enhanced";

export default function DemoPage() {
  const [activeDemo, setActiveDemo] = useState<string>("overview");
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const demoItems = [
    { id: "wizard", title: "Project Setup", icon: Sparkles, desc: "AI-guided project creation" },
    { id: "editor", title: "Writing Interface", icon: PenTool, desc: "Smart editor with AI assistance" },
    { id: "agents", title: "AI Agents", icon: Brain, desc: "Multi-agent writing pipeline" },
    { id: "story-bible", title: "Story Bible", icon: BookOpen, desc: "Character & world management" },
    { id: "universes", title: "Universe Management", icon: Globe, desc: "Cross-series continuity & sharing" },
    { id: "analytics", title: "Analytics", icon: BarChart3, desc: "Progress tracking & insights" }
  ];

  // Handle mobile dropdown selection and scroll snapping
  const handleMobileSelection = (demoId: string) => {
    setActiveDemo(demoId);
    const demoIndex = demoItems.findIndex(item => item.id === demoId);
    if (scrollContainerRef.current && demoIndex !== -1) {
      const cardWidth = 280; // Approximate card width + gap
      scrollContainerRef.current.scrollTo({
        left: demoIndex * cardWidth,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Paper texture background */}
      <div className="fixed inset-0 paper-texture opacity-30" />

      {/* Custom styles for horizontal scroll and responsive grid */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .demo-grid {
          grid-template-columns: repeat(3, 1fr);
          grid-template-rows: repeat(2, 1fr);
        }
        .demo-grid > * {
          min-width: 0; /* Allow grid items to shrink */
          width: 100%; /* Force cards to fill grid cell width */
        }
        /* Switch to 1x6 at 1920px */
        @media (min-width: 1920px) {
          .demo-grid {
            grid-template-columns: repeat(6, 1fr);
            grid-template-rows: 1fr;
          }
        }
      `}</style>
      
      {/* Header */}
      <header className="relative z-50 border-b border-border bg-background/95 backdrop-blur-xl shadow-sm">
        <div className="container-wide flex h-20 items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 blur-sm opacity-20" />
              <div className="relative w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-6 h-6 text-primary-foreground" />
              </div>
            </div>
            <h1 className="text-3xl font-bold tracking-tight font-literary-display leading-none mt-4">
              BookScribe AI
            </h1>
          </div>
          
          <nav className="flex gap-2 sm:gap-4 sm:gap-5 lg:gap-6 items-center">
            <ThemeToggle />
            <SettingsButton />
            <Link href="/">
              <Button variant="ghost" className="text-foreground/80 hover:text-foreground hover:bg-accent font-medium">
                ← Back to Home
              </Button>
            </Link>
            <Link href="/signup">
              <Button variant="literary" className="font-medium px-4 sm:px-6 lg:px-8">
                Start Writing Free
                <Feather className="w-4 h-4 ml-1" />
              </Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="container-wide max-w-full mx-auto px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-primary/30 bg-primary/10 mb-8">
            <Play className="w-4 h-4 text-primary" />
            <span className="text-sm text-primary font-medium">Interactive Demo</span>
          </div>

          <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 font-literary-display">
            <span className="text-foreground">
              Experience BookScribe AI
            </span>
            <br />
            <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent">
              In Action
            </span>
          </h2>
          
          <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-12 font-mono">
            Explore the most advanced AI writing platform for novelists. See how our AI agents, 
            intelligent context engine, and collaborative tools work together to help you write your masterpiece.
          </p>

          {/* Demo Navigation */}

          {/* Mobile Dropdown (visible on small screens) */}
          <div className="block md:hidden mb-8">
            <Select value={activeDemo} onValueChange={handleMobileSelection}>
              <SelectTrigger className="w-full max-w-sm mx-auto">
                <SelectValue placeholder="Choose a demo to explore" />
              </SelectTrigger>
              <SelectContent>
                {demoItems.map((demo) => (
                  <SelectItem key={demo.id} value={demo.id}>
                    <div className="flex items-center gap-2">
                      <demo.icon className="w-4 h-4" />
                      <span>{demo.title}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Mobile Horizontal Scroll (visible on small screens) */}
          <div className="block md:hidden mb-12">
            <div
              ref={scrollContainerRef}
              className="flex gap-4 overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {demoItems.map((demo) => (
                <Card
                  key={demo.id}
                  className={`flex-shrink-0 w-64 cursor-pointer transition-all duration-300 border snap-center ${
                    activeDemo === demo.id
                      ? 'border-primary/50 bg-primary/10 scale-105'
                      : 'border-border bg-card hover:border-primary/30'
                  }`}
                  onClick={() => setActiveDemo(demo.id)}
                >
                  <CardContent className="p-6 text-center flex flex-col justify-between min-h-[160px]">
                    <div>
                      <demo.icon className={`w-8 h-8 mx-auto mb-3 ${
                        activeDemo === demo.id ? 'text-primary' : 'text-muted-foreground'
                      }`} />
                      <h3 className="font-semibold mb-2 font-literary">{demo.title}</h3>
                    </div>
                    <p className="text-sm text-muted-foreground font-mono mt-auto">{demo.desc}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Desktop Grid Layout */}
          {/* 2x3 for medium screens, 1x6 when text fits without wrapping */}
          <div className="hidden md:grid demo-grid gap-4 lg:gap-6 mb-12">
            {demoItems.map((demo) => (
              <Card
                key={demo.id}
                className={`cursor-pointer transition-all duration-300 hover:scale-105 border ${
                  activeDemo === demo.id
                    ? 'border-primary/50 bg-primary/10'
                    : 'border-border bg-card hover:border-primary/30'
                }`}
                onClick={() => setActiveDemo(demo.id)}
              >
                <CardContent className="p-6 text-center flex flex-col justify-between min-h-[160px]">
                  <div>
                    <demo.icon className={`w-8 h-8 mx-auto mb-3 ${
                      activeDemo === demo.id ? 'text-primary' : 'text-muted-foreground'
                    }`} />
                    <h3 className="font-semibold mb-2 font-literary">{demo.title}</h3>
                  </div>
                  <p className="text-sm text-muted-foreground font-mono mt-auto">{demo.desc}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Content */}
      <section className="relative z-10 py-12 px-4">
        <div className="container-wide max-w-full mx-auto px-8">
          <div className="min-h-[900px] w-full bg-background/50 backdrop-blur-sm rounded-lg border border-border overflow-hidden">
            {activeDemo === "overview" && <DemoOverview />}
            {activeDemo === "wizard" && <DemoProjectSetupEnhanced />}
            {activeDemo === "editor" && <DemoEditorEnhanced />}
            {activeDemo === "agents" && <DemoAgentsEnhanced />}
            {activeDemo === "story-bible" && <DemoStoryBibleEnhanced />}
            {activeDemo === "universes" && <DemoUniverseEnhanced />}
            {activeDemo === "analytics" && <DemoAnalyticsEnhanced />}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 px-4 border-t border-border">
        <div className="container-wide max-w-4xl mx-auto text-center">
          <h3 className="text-3xl sm:text-4xl font-bold mb-6 font-literary-display">
            Ready to Write Your
            <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent"> Masterpiece?</span>
          </h3>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto font-literary">
            Join thousands of authors who are already using BookScribe AI to create compelling narratives with unprecedented scale and consistency.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 justify-center">
            <Link href="/signup">
              <Button 
                size="lg" 
                variant="literary"
                className="font-medium px-8 py-6 text-lg"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Start Writing Free
              </Button>
            </Link>
            <Link href="/login">
              <Button 
                size="lg" 
                variant="outline" 
                className="font-medium px-8 py-6 text-lg"
              >
                Sign In
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 border-t border-border py-12 bg-background/95 backdrop-blur-xl">
        <div className="container-wide text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Feather className="w-4 h-4 text-primary-foreground" />
            </div>
            <span className="text-lg font-semibold font-literary text-foreground">
              BookScribe AI
            </span>
          </div>
          <p className="text-muted-foreground text-sm mb-4">
            © 2025 BookScribe AI. Empowering authors to write without limits.
          </p>
          <p className="text-xs text-primary">
            This demo showcases simulated features. Sign up to experience the real AI-powered writing platform.
          </p>
        </div>
      </footer>
    </div>
  );
}

// Demo Overview Component
function DemoOverview() {
  return (
    <div className="text-center">
      <h3 className="text-2xl font-bold mb-8 font-literary-display">Choose a demo above to explore BookScribe AI&apos;s features</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-border bg-card">
          <CardContent className="p-6">
            <Target className="w-12 h-12 text-primary mx-auto mb-4" />
            <h4 className="font-semibold mb-2">{"300,000+ Words"}</h4>
            <p className="text-sm text-muted-foreground">Write epic novels with perfect consistency across massive word counts</p>
          </CardContent>
        </Card>
        <Card className="border-border bg-card">
          <CardContent className="p-6">
            <Clock className="w-12 h-12 text-primary mx-auto mb-4" />
            <h4 className="font-semibold mb-2">Real-time AI</h4>
            <p className="text-sm text-muted-foreground">Get instant suggestions and improvements as you write</p>
          </CardContent>
        </Card>
        <Card className="border-border bg-card">
          <CardContent className="p-6">
            <Star className="w-12 h-12 text-primary mx-auto mb-4" />
            <h4 className="font-semibold mb-2">Your Voice</h4>
            <p className="text-sm text-muted-foreground">AI learns and adapts to your unique writing style</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Demo Universes Component
function DemoUniverses() {
  const [selectedUniverse, setSelectedUniverse] = useState<string | null>("crystal-realms");

  const universes = [
    {
      id: "crystal-realms",
      name: "The Crystal Realms",
      description: "A fantasy universe of magical kingdoms and ancient powers",
      series: ["The Crystal Saga", "Shattered Crown Chronicles"],
      characters: 34,
      events: 12,
      rules: 8
    },
    {
      id: "neo-future",
      name: "Neo-Future Earth",
      description: "A cyberpunk universe of AI consciousness and digital mysteries",
      series: ["Neural Web Trilogy"],
      characters: 18,
      events: 7,
      rules: 5
    }
  ];

  const selectedUniverseData = universes.find(u => u.id === selectedUniverse);

  return (
    <div className="max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px] xl:max-w-[1400px] 2xl:max-w-[1600px] mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="space-y-6">
            {/* Header */}
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2">
                <Globe className="w-6 h-6 text-primary" />
                Universe Management System
              </h3>
              <p className="text-muted-foreground">
                Create interconnected worlds where multiple series share characters, locations, and continuity.
              </p>
            </div>

            {/* Universe Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
              {universes.map((universe) => (
                <Card
                  key={universe.id}
                  className={`cursor-pointer transition-all border ${
                    selectedUniverse === universe.id
                      ? 'border-primary/50 bg-primary/10'
                      : 'border-border bg-card hover:border-primary/30'
                  }`}
                  onClick={() => setSelectedUniverse(universe.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold flex items-center gap-2">
                          <Globe className="w-4 h-4" />
                          {universe.name}
                        </h4>
                        <p className="text-sm text-muted-foreground mt-1">
                          {universe.description}
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 sm:gap-5 lg:gap-6 mt-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary">{universe.series.length}</div>
                        <div className="text-xs text-muted-foreground">Series</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary">{universe.characters}</div>
                        <div className="text-xs text-muted-foreground">Characters</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary">{universe.events}</div>
                        <div className="text-xs text-muted-foreground">Events</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Universe Details */}
            {selectedUniverseData && (
              <Card className="border-primary/20 bg-primary/5">
                <CardContent className="p-6">
                  <h4 className="text-xl font-semibold mb-4">{selectedUniverseData.name} Details</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h5 className="font-medium mb-2 flex items-center gap-2">
                        <BookOpen className="w-4 h-4" />
                        Connected Series
                      </h5>
                      <div className="space-y-2">
                        {selectedUniverseData.series.map((series, index) => (
                          <div key={index} className="flex items-center gap-2 p-2 bg-background rounded border">
                            <BookOpen className="w-3 h-3 text-muted-foreground" />
                            <span className="text-sm">{series}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h5 className="font-medium mb-2 flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        Shared Features
                      </h5>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-success" />
                          <span>Character sharing across series</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-success" />
                          <span>Timeline synchronization</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-success" />
                          <span>World-building consistency</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-success" />
                          <span>Voice profile continuity</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
              <Card className="border-info/20 bg-info/10">
                <CardContent className="p-4 text-center">
                  <Brain className="w-8 h-8 text-info mx-auto mb-2" />
                  <h4 className="font-medium text-info">Character Continuity</h4>
                  <p className="text-sm text-info/80 mt-1">
                    Track character development across multiple books and series
                  </p>
                </CardContent>
              </Card>

              <Card className="border-purple-500/20 bg-purple-500/10">
                <CardContent className="p-4 text-center">
                  <Clock className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                  <h4 className="font-medium text-purple-500">Timeline Events</h4>
                  <p className="text-sm text-purple-500/80 mt-1">
                    Manage universe-wide events and their impact across stories
                  </p>
                </CardContent>
              </Card>

              <Card className="border-success/20 bg-success/10">
                <CardContent className="p-4 text-center">
                  <Target className="w-8 h-8 text-success mx-auto mb-2" />
                  <h4 className="font-medium text-success">Voice Consistency</h4>
                  <p className="text-sm text-success/80 mt-1">
                    Maintain consistent voice and style across all universe content
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
