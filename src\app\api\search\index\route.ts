import { NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { createTypedServerClient } from '@/lib/supabase'
import { contentIndexingService } from '@/lib/services/content-indexing-service'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { applyRateLimit } from '@/lib/rate-limiter-unified'

// POST - Trigger indexing for a project
const indexRequestSchema = z.object({
  projectId: z.string().uuid(),
  fullReindex: z.boolean().optional().default(false)
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    // Apply rate limiting - indexing is expensive
    const rateLimitResponse = await applyRateLimit(request, { type: 'write', cost: 5 })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const body = await request.json()
    const validationResult = indexRequestSchema.safeParse(body)
    
    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid request data',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400)
    }
    
    const { projectId, fullReindex } = validationResult.data
    
    // Verify user has access to project
    const { data: project } = await supabase
      .from('projects')
      .select('id, user_id')
      .eq('id', projectId)
      .single()
    
    if (!project || project.user_id !== user.id) {
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('role')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single()
      
      if (!collaborator || collaborator.role === 'viewer') {
        return UnifiedResponse.error({
          message: 'You do not have permission to index this project',
          code: 'FORBIDDEN'
        }, undefined, 403)
      }
    }
    
    // Start indexing (async - don't wait for completion)
    contentIndexingService.indexProject(projectId).catch(error => {
      logger.error('Background indexing failed:', error)
    })
    
    // Update search vectors if requested
    if (fullReindex) {
      contentIndexingService.updateSearchVectors(projectId).catch(error => {
        logger.error('Search vector update failed:', error)
      })
    }
    
    return UnifiedResponse.success({
      message: 'Indexing started',
      projectId,
      fullReindex
    })
  } catch (error) {
    logger.error('Error starting indexing:', error)
    return UnifiedResponse.error({
      message: 'Failed to start indexing',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500)
  }
})

// GET - Get indexing status for a project
const statusQuerySchema = z.object({
  projectId: z.string().uuid()
})

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const searchParams = Object.fromEntries(request.nextUrl.searchParams)
    const validationResult = statusQuerySchema.safeParse(searchParams)
    
    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid query parameters',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400)
    }
    
    const { projectId } = validationResult.data
    
    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()
    
    if (!projectAccess) {
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single()
      
      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403)
      }
    }
    
    // Get indexing status
    const status = await contentIndexingService.getIndexingStatus(projectId)
    
    return UnifiedResponse.success(status)
  } catch (error) {
    logger.error('Error getting indexing status:', error)
    return UnifiedResponse.error({
      message: 'Failed to get indexing status',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500)
  }
})