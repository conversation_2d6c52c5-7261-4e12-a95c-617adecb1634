import { logger } from '@/lib/services/logger'
import { AI_CONFIG, PERFORMANCE_CONFIG } from '@/lib/config/environment-config'
import { TIME_MS } from '@/lib/constants'

// Retry configuration interface
export interface RetryConfig {
  maxAttempts?: number
  initialDelay?: number
  maxDelay?: number
  backoffMultiplier?: number
  shouldRetry?: (error: unknown, attempt: number) => boolean
  onRetry?: (error: unknown, attempt: number) => void
}

// Default retry configuration
const DEFAULT_RETRY_CONFIG: Required<RetryConfig> = {
  maxAttempts: AI_CONFIG.RETRY.MAX_ATTEMPTS,
  initialDelay: AI_CONFIG.RETRY.INITIAL_DELAY,
  maxDelay: AI_CONFIG.RETRY.MAX_DELAY,
  backoffMultiplier: AI_CONFIG.RETRY.BACKOFF_MULTIPLIER,
  shouldRetry: (error) => {
    // Don't retry on validation errors or client errors
    if (error instanceof Error) {
      const message = error.message.toLowerCase()
      if (
        message.includes('validation') ||
        message.includes('invalid') ||
        message.includes('unauthorized') ||
        message.includes('forbidden') ||
        message.includes('not found')
      ) {
        return false
      }
    }
    return true
  },
  onRetry: (error, attempt) => {
    logger.warn(`Retry attempt ${attempt}`, { error })
  }
}

// Exponential backoff with jitter
function calculateDelay(
  attempt: number,
  initialDelay: number,
  maxDelay: number,
  backoffMultiplier: number
): number {
  const exponentialDelay = initialDelay * Math.pow(backoffMultiplier, attempt - 1)
  const clampedDelay = Math.min(exponentialDelay, maxDelay)
  // Add jitter (±25% randomization)
  const jitter = clampedDelay * 0.25 * (Math.random() * 2 - 1)
  return Math.round(clampedDelay + jitter)
}

// Sleep utility
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// Main retry handler
export async function withRetry<T>(
  fn: () => Promise<T>,
  config?: RetryConfig
): Promise<T> {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config }
  let lastError: unknown

  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error

      // Check if we should retry
      if (!finalConfig.shouldRetry(error, attempt)) {
        throw error
      }

      // Don't retry if this was the last attempt
      if (attempt === finalConfig.maxAttempts) {
        logger.error('Max retry attempts reached', {
          attempts: finalConfig.maxAttempts,
          error
        })
        throw error
      }

      // Calculate delay and wait
      const delay = calculateDelay(
        attempt,
        finalConfig.initialDelay,
        finalConfig.maxDelay,
        finalConfig.backoffMultiplier
      )

      // Call onRetry callback
      finalConfig.onRetry(error, attempt)

      logger.info(`Retrying after ${delay}ms (attempt ${attempt}/${finalConfig.maxAttempts})`)
      await sleep(delay)
    }
  }

  // This should never be reached, but TypeScript needs it
  throw lastError
}

// Specialized retry handlers for different scenarios
export const RetryHandlers = {
  // For AI/LLM operations
  ai: <T>(fn: () => Promise<T>) => 
    withRetry(fn, {
      maxAttempts: AI_CONFIG.RETRY.MAX_ATTEMPTS,
      initialDelay: AI_CONFIG.RETRY.INITIAL_DELAY,
      maxDelay: AI_CONFIG.RETRY.MAX_DELAY,
      shouldRetry: (error) => {
        if (error instanceof Error) {
          const message = error.message.toLowerCase()
          // Retry on rate limits and service errors
          return (
            message.includes('rate limit') ||
            message.includes('timeout') ||
            message.includes('service') ||
            message.includes('500') ||
            message.includes('503')
          )
        }
        return true
      }
    }),

  // For database operations
  database: <T>(fn: () => Promise<T>) =>
    withRetry(fn, {
      maxAttempts: 3,
      initialDelay: 100,
      maxDelay: TIME_MS.TOAST_DURATION,
      shouldRetry: (error) => {
        if (error instanceof Error) {
          const message = error.message.toLowerCase()
          // Don't retry on constraint violations
          return !message.includes('duplicate') && !message.includes('constraint')
        }
        return true
      }
    }),

  // For external API calls
  external: <T>(fn: () => Promise<T>) =>
    withRetry(fn, {
      maxAttempts: 3,
      initialDelay: TIME_MS.SECOND,
      maxDelay: 10000,
      shouldRetry: (error) => {
        if (error instanceof Error) {
          const message = error.message.toLowerCase()
          // Retry on network and timeout errors
          return (
            message.includes('network') ||
            message.includes('timeout') ||
            message.includes('econnrefused') ||
            message.includes('enotfound')
          )
        }
        return true
      }
    }),

  // For critical operations (more aggressive retry)
  critical: <T>(fn: () => Promise<T>) =>
    withRetry(fn, {
      maxAttempts: 5,
      initialDelay: 500,
      maxDelay: 30000,
      backoffMultiplier: 1.5,
      onRetry: (error, attempt) => {
        logger.error(`Critical operation retry attempt ${attempt}`, { error })
      }
    }),

  // No retry (for operations that shouldn't be retried)
  none: <T>(fn: () => Promise<T>) => fn()
}

// Timeout wrapper
export async function withTimeout<T>(
  fn: () => Promise<T>,
  timeoutMs: number = PERFORMANCE_CONFIG.TIMEOUTS.API_DEFAULT
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Operation timed out after ${timeoutMs}ms`))
    }, timeoutMs)
  })

  return Promise.race([fn(), timeoutPromise])
}

// Combined retry with timeout
export async function withRetryAndTimeout<T>(
  fn: () => Promise<T>,
  retryConfig?: RetryConfig,
  timeoutMs?: number
): Promise<T> {
  return withRetry(
    () => withTimeout(fn, timeoutMs),
    retryConfig
  )
}

// Batch retry handler for multiple operations
export async function batchWithRetry<T>(
  operations: Array<() => Promise<T>>,
  config?: RetryConfig & { concurrency?: number }
): Promise<Array<{ success: boolean; data?: T; error?: unknown }>> {
  const concurrency = config?.concurrency || 3
  const results: Array<{ success: boolean; data?: T; error?: unknown }> = []
  
  // Process in batches
  for (let i = 0; i < operations.length; i += concurrency) {
    const batch = operations.slice(i, i + concurrency)
    const batchResults = await Promise.all(
      batch.map(async (operation) => {
        try {
          const data = await withRetry(operation, config)
          return { success: true, data }
        } catch (error) {
          return { success: false, error }
        }
      })
    )
    results.push(...batchResults)
  }
  
  return results
}