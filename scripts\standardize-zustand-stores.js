#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const minimist = require('minimist');

// simple option parsing
const argv = minimist(process.argv.slice(2), {
  string: ['name', 'state', 'action', 'async', 'out'],
  alias: { n: 'name', s: 'state', a: 'action', A: 'async', o: 'out' }
});

// allow repeated --state/--action values
const toArray = (val, defaultVal = []) => {
  if (!val) return defaultVal;
  return Array.isArray(val) ? val : [val];
};

const capitalize = (str = '') => str.charAt(0).toUpperCase() + str.slice(1);

const defaultState = ['count:number', 'loading:boolean'];
const stateDefinitions = toArray(argv.state, defaultState);
const actionDefinitions = toArray(argv.action, ['increment', 'decrement']);
const asyncAction = argv.async || 'fetchCount';
const storeNameArg = argv.name || 'example';

// Template for standardized store
const getStandardizedTemplate = (
  storeName,
  { states = [], actions = [], asyncAction: asyncName } = {}
) => {
  const interfaceName = capitalize(storeName) + 'Store';

  const parseState = (s) => {
    const [name, type] = s.split(':');
    return { name, type: type || 'string' };
  };

  const defaultForType = (type) => {
    if (type.endsWith('[]')) return '[]';
    switch (type) {
      case 'number':
        return '0';
      case 'boolean':
        return 'false';
      case 'string':
        return "''";
      case 'object':
      case 'Record<string, any>':
        return '{}';
      default:
        return 'null as any';
    }
  };

  const stateEntries = states.map(parseState);

  const interfaceStateLines = stateEntries.map(
    ({ name, type }) => `  ${name}: ${type}`
  );

  const interfaceActionLines = [];
  const initialStateLines = [];
  const implementationLines = [];

  stateEntries.forEach(({ name, type }) => {
    const setter = 'set' + capitalize(name);
    interfaceActionLines.push(`  ${setter}: (${name}: ${type}) => void`);
    initialStateLines.push(`        ${name}: ${defaultForType(type)},`);
    implementationLines.push(
      `        ${setter}: (${name}) => set({ ${name} }),`
    );
  });

  actions.forEach((action) => {
    interfaceActionLines.push(`  ${action}: () => void`);
    implementationLines.push(`        ${action}: () => {`);
    implementationLines.push(`          // Implement ${action}`);
    implementationLines.push('        },');
  });

  if (asyncName) {
    interfaceActionLines.push(`  ${asyncName}: () => Promise<void>`);
    implementationLines.push(`        ${asyncName}: async () => {`);
    implementationLines.push('          set({ loading: true });');
    implementationLines.push('          try {');
    implementationLines.push('            // Async logic here');
    implementationLines.push('          } catch (error) {');
    implementationLines.push("            logger.error(error);");
    implementationLines.push('          } finally {');
    implementationLines.push('            set({ loading: false });');
    implementationLines.push('          }');
    implementationLines.push('        },');
  }

  const imports = [
    "import { create } from 'zustand'",
    "import { devtools, persist } from 'zustand/middleware'",
    "import { immer } from 'zustand/middleware/immer'",
  ];
  if (asyncName) {
    imports.push("import { logger } from '@/lib/services/logger'");
  }

  return `${imports.join('\n')}

interface ${interfaceName} {
${interfaceStateLines.join('\n')}${interfaceActionLines.length ? '\n\n  // Actions\n' + interfaceActionLines.join('\n') : ''}
}

export const use${capitalize(storeName)}Store = create<${interfaceName}>()(
  devtools(
    persist(
      immer((set, get) => ({
${initialStateLines.join('\n')}

${implementationLines.join('\n')}
      })),
      {
        name: '${storeName}-storage',
      }
    ),
    {
      name: '${interfaceName}'
    }
  )
)

// Selectors for better performance
export const use${capitalize(storeName)}Selectors = {
  ${stateEntries[0] ? `get${capitalize(stateEntries[0].name)}: (state: ${interfaceName}) => state.${stateEntries[0].name}` : ''}
};`;
};

// If --out option is provided, generate a store file and exit
if (argv.out) {
  const template = getStandardizedTemplate(storeNameArg, {
    states: stateDefinitions,
    actions: actionDefinitions,
    asyncAction
  });
  const outPath = path.resolve(argv.out);
  fs.writeFileSync(outPath, template);
  console.log(`✅ Generated store at ${outPath}`);
  process.exit(0);
}

console.log('🔄 Standardizing Zustand stores...\n');

// Store files to update
const storeFiles = [
  'src/stores/editor-store.ts',
  'src/stores/project-store.ts',
  'src/lib/settings/settings-store.ts',
  'src/lib/themes/custom-themes-store.ts'
];

// Recommendations for each store
const storeRecommendations = {
  'editor-store.ts': {
    needsDevtools: true,
    needsPersist: false, // Editor state is usually transient
    needsImmer: true, // Complex nested state
    recommendations: [
      'Add devtools for debugging',
      'Add immer for easier state updates',
      'Create selectors for chapter lookups',
      'Type the chapter status properly'
    ]
  },
  'project-store.ts': {
    needsDevtools: true,
    needsPersist: true, // Projects should persist
    needsImmer: true,
    recommendations: [
      'Add devtools and persist middleware',
      'Create typed selectors for project queries',
      'Add error handling for async actions',
      'Implement optimistic updates'
    ]
  },
  'settings-store.ts': {
    needsDevtools: true,
    needsPersist: true, // Settings must persist
    needsImmer: false, // Settings are usually flat
    recommendations: [
      'Already has persist - add devtools',
      'Create typed setting groups',
      'Add validation for settings',
      'Export individual setting hooks'
    ]
  },
  'custom-themes-store.ts': {
    needsDevtools: true,
    needsPersist: true, // Themes should persist
    needsImmer: false,
    recommendations: [
      'Already has persist - add devtools',
      'Add theme validation',
      'Create theme preset management',
      'Add import/export functionality'
    ]
  }
};

// Analyze and create improvement plans
console.log('📊 Store Analysis:');
console.log('━'.repeat(60));

const improvementPlans = [];

storeFiles.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  const fileName = path.basename(file);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ Not found: ${file}`);
    return;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  const recommendations = storeRecommendations[fileName] || {};
  
  // Check current features
  const hasDevtools = content.includes('devtools');
  const hasPersist = content.includes('persist');
  const hasImmer = content.includes('immer');
  const hasTypedInterface = content.includes('interface');
  const hasSelectors = content.includes('Selector') || content.includes('get()');
  
  console.log(`\n${fileName}:`);
  console.log(`  ✓ TypeScript Interface: ${hasTypedInterface ? 'Yes' : 'No'}`);
  console.log(`  ✓ Devtools: ${hasDevtools ? 'Yes' : 'Missing'}`);
  console.log(`  ✓ Persist: ${hasPersist ? 'Yes' : recommendations.needsPersist ? 'Missing' : 'Not needed'}`);
  console.log(`  ✓ Immer: ${hasImmer ? 'Yes' : recommendations.needsImmer ? 'Missing' : 'Not needed'}`);
  console.log(`  ✓ Selectors: ${hasSelectors ? 'Yes' : 'No'}`);
  
  if (recommendations.recommendations) {
    console.log(`  📝 Recommendations:`);
    recommendations.recommendations.forEach(rec => {
      console.log(`     - ${rec}`);
    });
  }
  
  // Create improvement plan
  const needsUpdate = (!hasDevtools && recommendations.needsDevtools) ||
                     (!hasPersist && recommendations.needsPersist) ||
                     (!hasImmer && recommendations.needsImmer);
  
  if (needsUpdate) {
    improvementPlans.push({
      file,
      fileName,
      missing: {
        devtools: !hasDevtools && recommendations.needsDevtools,
        persist: !hasPersist && recommendations.needsPersist,
        immer: !hasImmer && recommendations.needsImmer
      },
      recommendations: recommendations.recommendations || []
    });
  }
});

// Create migration examples
console.log('\n\n📝 Migration Examples:');
console.log('━'.repeat(60));

// Example for editor-store
const editorStoreExample = `// Improved editor-store.ts
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface EditorStore {
  // ... existing state ...
  
  // Actions with Immer
  updateChapter: (chapterId: string, updates: Partial<Chapter>) => void
  reorderChapters: (fromIndex: number, toIndex: number) => void
}

export const useEditorStore = create<EditorStore>()(
  devtools(
    immer((set) => ({
      // ... existing state ...
      
      // Immer allows direct mutations
      updateChapter: (chapterId, updates) =>
        set((state) => {
          const chapter = state.chapters.find(ch => ch.id === chapterId)
          if (chapter) {
            Object.assign(chapter, updates)
          }
        }),
        
      reorderChapters: (fromIndex, toIndex) =>
        set((state) => {
          const [removed] = state.chapters.splice(fromIndex, 1)
          state.chapters.splice(toIndex, 0, removed)
        })
    })),
    {
      name: 'EditorStore'
    }
  )
)

// Selectors for performance
export const useCurrentChapter = () => 
  useEditorStore(state => state.chapters[state.currentChapter - 1])

export const useChapterById = (id: string) =>
  useEditorStore(state => state.chapters.find(ch => ch.id === id))`;

console.log('\nEditor Store Example:');
console.log(editorStoreExample);

// Create improvement guide
const exampleTemplate = getStandardizedTemplate('example', {
  states: ['items:string[]', 'loading:boolean'],
  actions: ['addItem', 'removeItem'],
  asyncAction: 'fetchItems'
});

const improvementGuide = `# Zustand Store Improvement Guide

## Standard Store Structure

\`\`\`typescript
${exampleTemplate}
\`\`\`

## Improvements Needed

${improvementPlans.map(plan => `
### ${plan.fileName}
${plan.missing.devtools ? '- Add devtools middleware for debugging' : ''}
${plan.missing.persist ? '- Add persist middleware for localStorage' : ''}
${plan.missing.immer ? '- Add immer middleware for easier updates' : ''}
${plan.recommendations.map(r => `- ${r}`).join('\n')}
`).join('\n')}

## Best Practices Applied

1. **TypeScript First**: Fully typed store interfaces
2. **Middleware Stack**: devtools → persist → immer
3. **Selectors**: Exported hooks for specific state slices
4. **Actions**: Clear naming, proper error handling
5. **Performance**: Use selectors to prevent unnecessary re-renders
`;

fs.writeFileSync(
  path.join(process.cwd(), 'docs/frontend/zustand-improvement-guide.md'),
  improvementGuide
);

// Summary
console.log('\n\n📊 Summary:');
console.log('━'.repeat(60));
console.log(`Total stores analyzed: ${storeFiles.length}`);
console.log(`Stores needing updates: ${improvementPlans.length}`);

console.log('\n📝 Next Steps:');
console.log('1. Review the improvement guide at docs/frontend/zustand-improvement-guide.md');
console.log('2. Update stores to use standard middleware stack');
console.log('3. Add TypeScript interfaces where missing');
console.log('4. Create selectors for frequently accessed data');
console.log('5. Test persistence and devtools integration');

console.log('\n✅ Zustand store analysis complete!');