#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// List of deprecated files to check
const deprecatedFiles = [
  // Old API middleware files
  'src/lib/api/middleware.ts',
  'src/lib/api/auth-helpers.ts',
  'src/lib/api/auth-middleware.ts',
  'src/lib/api/response-utils.ts',
  'src/lib/api/add-auth-to-routes.ts',
  
  // Old auth files (keeping unified-auth-service.ts)
  'src/lib/auth/index.ts',
  'src/lib/auth/server.ts',
  'src/lib/auth/admin.ts',
  'src/lib/auth/validation.ts',
  
  // Old collaboration service files
  'src/lib/services/collaboration-hub.ts',
  'src/lib/services/collaboration-hub-serverless.ts',
  'src/lib/services/collaboration-service.ts',
  'src/lib/services/collaboration-service-realtime.ts',
  
  // Old rate limiter files
  'src/lib/api/rate-limiter.ts',
  'src/lib/rate-limiter.ts',
  
  // Old environment config
  'src/lib/env/client.ts',
  'src/lib/env/server.ts',
  
  // Deprecated theme file
  'src/lib/themes/theme-registry.ts',
];

console.log('Checking for usage of deprecated files...\n');

const safeToRemove = [];
const stillInUse = [];

for (const file of deprecatedFiles) {
  const fullPath = path.join(process.cwd(), file);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚪ ${file} - Already removed`);
    continue;
  }
  
  // Extract just the filename without extension for import checking
  const fileName = path.basename(file, path.extname(file));
  const dirName = path.dirname(file).replace('src/', '');
  
  try {
    // Check for imports of this file
    const importPatterns = [
      `from ['"]@/${dirName}/${fileName}['"]`,
      `from ['"]@/${dirName.replace('lib/', '')}/${fileName}['"]`,
      `from ['"]./${fileName}['"]`,
      `require\\(['"].*${fileName}['"]\\)`,
    ];
    
    let isUsed = false;
    for (const pattern of importPatterns) {
      try {
        const result = execSync(
          `grep -r "${pattern}" src --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" 2>/dev/null || true`,
          { encoding: 'utf8' }
        );
        
        if (result.trim()) {
          // Filter out self-imports and imports from other deprecated files
          const lines = result.trim().split('\n').filter(line => {
            const importingFile = line.split(':')[0];
            return importingFile !== fullPath && !deprecatedFiles.some(df => importingFile.includes(df));
          });
          
          if (lines.length > 0) {
            isUsed = true;
            console.log(`❌ ${file} - Still in use by:`);
            lines.slice(0, 3).forEach(line => {
              const [importingFile] = line.split(':');
              console.log(`   - ${importingFile}`);
            });
            if (lines.length > 3) {
              console.log(`   ... and ${lines.length - 3} more files`);
            }
            stillInUse.push({ file, usedBy: lines });
            break;
          }
        }
      } catch (error) {
        // Grep failed, continue with next pattern
      }
    }
    
    if (!isUsed) {
      console.log(`✅ ${file} - Safe to remove`);
      safeToRemove.push(file);
    }
  } catch (error) {
    console.error(`Error checking ${file}:`, error.message);
  }
}

console.log('\n=== Summary ===');
console.log(`Safe to remove: ${safeToRemove.length} files`);
console.log(`Still in use: ${stillInUse.length} files`);

if (safeToRemove.length > 0) {
  console.log('\n=== Files safe to remove ===');
  safeToRemove.forEach(file => console.log(`- ${file}`));
  
  console.log('\nTo remove these files, run:');
  console.log('npm run cleanup:deprecated');
}

if (stillInUse.length > 0) {
  console.log('\n=== Files still in use ===');
  stillInUse.forEach(({ file, usedBy }) => {
    console.log(`\n${file}:`);
    usedBy.slice(0, 3).forEach(line => {
      const [importingFile] = line.split(':');
      console.log(`  - ${importingFile}`);
    });
  });
}