import { createServerClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import { notFound } from 'next/navigation'
import { ReferenceMaterials } from '@/components/references/reference-materials'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default async function ReferencesPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createServerClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')
  
  const { data: project, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', id)
    .eq('user_id', user.id)
    .single()
  
  if (error || !project) notFound()
  
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container-wide flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/projects/${id}`}>
              <Button variant="ghost">← Back to Project</Button>
            </Link>
            <h1 className="text-2xl font-bold">Reference Materials</h1>
          </div>
          <div className="text-sm text-muted-foreground">
            {project.title}
          </div>
        </div>
      </header>
      
      <main className="container-wide py-6">
        <ReferenceMaterials projectId={id} userId={user.id} />
      </main>
    </div>
  )
}