import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

// GET - Fetch all continuity issues for a series
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Get status filter from query params
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || undefined

    const service = getSeriesService(true)
    const { issues, error } = await service.getSeriesContinuityIssues(seriesId, status)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch continuity issues' }, { status: 500 })
    }

    return NextResponse.json({ issues })
  } catch (error) {
    logger.error('Error in continuity issues GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create a new continuity issue
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.issue_type || !body.issue_title || !body.issue_description) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const service = getSeriesService(true)
    const { issue, error } = await service.createContinuityIssue({
      series_id: seriesId,
      status: 'open',
      severity: body.severity || 'medium',
      ...body
    })

    if (error) {
      return NextResponse.json({ error: 'Failed to create continuity issue' }, { status: 500 })
    }

    return NextResponse.json({ issue }, { status: 201 })
  } catch (error) {
    logger.error('Error in continuity issues POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Resolve a continuity issue
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    const { issueId, resolution, bookNumber } = body
    
    if (!issueId || !resolution) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const service = getSeriesService(true)
    const { issue, error } = await service.resolveContinuityIssue(
      issueId, 
      resolution, 
      bookNumber
    )

    if (error) {
      return NextResponse.json({ error: 'Failed to resolve continuity issue' }, { status: 500 })
    }

    return NextResponse.json({ issue })
  } catch (error) {
    logger.error('Error in continuity issues PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}