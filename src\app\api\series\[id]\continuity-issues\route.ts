import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'

// GET - Fetch all continuity issues for a series
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get status filter from query params
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || undefined

    const service = getSeriesService(true)
    const { issues, error } = await service.getSeriesContinuityIssues(seriesId, status)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch continuity issues' }, { status: 500 })
    }

    return NextResponse.json({ issues })
  } catch (error) {
    logger.error('Error in continuity issues GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create a new continuity issue
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.issue_type || !body.issue_title || !body.issue_description) {
      return NextResponse.json({ 
        error: 'issue_type, issue_title, and issue_description are required' 
      }, { status: 400 })
    }

    const service = getSeriesService(true)
    const { issue, error } = await service.createContinuityIssue({
      series_id: seriesId,
      status: 'open',
      severity: body.severity || 'medium',
      ...body
    })

    if (error) {
      return NextResponse.json({ error: 'Failed to create continuity issue' }, { status: 500 })
    }

    return NextResponse.json({ issue }, { status: 201 })
  } catch (error) {
    logger.error('Error in continuity issues POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Resolve a continuity issue
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { issueId, resolution, bookNumber } = body
    
    if (!issueId || !resolution) {
      return NextResponse.json({ 
        error: 'issueId and resolution are required' 
      }, { status: 400 })
    }

    const service = getSeriesService(true)
    const { issue, error } = await service.resolveContinuityIssue(
      issueId, 
      resolution, 
      bookNumber
    )

    if (error) {
      return NextResponse.json({ error: 'Failed to resolve continuity issue' }, { status: 500 })
    }

    return NextResponse.json({ issue })
  } catch (error) {
    logger.error('Error in continuity issues PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}