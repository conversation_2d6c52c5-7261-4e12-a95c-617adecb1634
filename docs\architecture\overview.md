# BookScribe System Architecture Overview

## Table of Contents
- [System Overview](#system-overview)
- [Architecture Principles](#architecture-principles)
- [High-Level Architecture](#high-level-architecture)
- [Component Architecture](#component-architecture)
- [Data Flow](#data-flow)
- [Security Architecture](#security-architecture)
- [Deployment Architecture](#deployment-architecture)

## System Overview

BookScribe is a sophisticated AI-powered novel writing platform built with modern web technologies and a multi-agent AI system. The architecture is designed to handle long-form content creation (100k+ words) while maintaining narrative consistency and supporting real-time collaboration.

## Architecture Principles

### Core Principles
1. **Scalability**: Handle large documents and multiple concurrent users
2. **Modularity**: Loosely coupled services and components
3. **Context Preservation**: Maintain narrative consistency across long content
4. **Real-time Collaboration**: Support multiple writers simultaneously
5. **Security First**: Row-level security and encrypted communications
6. **Performance**: Optimized for large-scale content processing

### Design Patterns
- **Multi-Agent Architecture**: Specialized AI agents for different writing tasks
- **Event-Driven**: Real-time updates via WebSocket subscriptions
- **Service Layer**: Business logic separated from API routes
- **Repository Pattern**: Data access abstraction
- **Factory Pattern**: Dynamic agent and service instantiation
- **Observer Pattern**: Real-time collaboration and updates

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Browser[Web Browser]
        Mobile[Mobile PWA]
    end
    
    subgraph "Frontend - Next.js"
        NextApp[Next.js App Router]
        Pages[Pages/Routes]
        Components[React Components]
        StateManager[Zustand State]
    end
    
    subgraph "API Layer"
        APIRoutes[Next.js API Routes]
        Middleware[Auth Middleware]
        RateLimiter[Rate Limiter]
    end
    
    subgraph "AI Agent System"
        Orchestrator[Agent Orchestrator]
        StoryArch[Story Architect]
        CharDev[Character Developer]
        ChapterPlan[Chapter Planner]
        WritingAgent[Writing Agent]
        AdaptivePlan[Adaptive Planner]
    end
    
    subgraph "Service Layer"
        ContentService[Content Service]
        AnalyticsService[Analytics Service]
        CollabService[Collaboration Service]
        SearchService[Search Service]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Stripe[Stripe]
        Sentry[Sentry]
    end
    
    subgraph "Data Layer - Supabase"
        PostgreSQL[PostgreSQL DB]
        Realtime[Realtime Engine]
        Storage[File Storage]
        Auth[Supabase Auth]
    end
    
    Browser --> NextApp
    Mobile --> NextApp
    NextApp --> APIRoutes
    APIRoutes --> Middleware
    Middleware --> RateLimiter
    RateLimiter --> Service Layer
    
    Service Layer --> AI Agent System
    AI Agent System --> OpenAI
    
    Service Layer --> PostgreSQL
    Service Layer --> Realtime
    Service Layer --> Storage
    
    APIRoutes --> Stripe
    NextApp --> Sentry
```

## Component Architecture

### Frontend Architecture

```mermaid
graph TD
    subgraph "Next.js Frontend"
        AppRouter[App Router]
        
        subgraph "Route Groups"
            Marketing[(marketing)]
            Dashboard[(dashboard)]
            App[(app)]
        end
        
        subgraph "Core Components"
            Editor[Editor System]
            Analytics[Analytics Dashboard]
            AgentUI[Agent Pipeline UI]
            ThemeSystem[Theme System]
        end
        
        subgraph "State Management"
            Zustand[Zustand Stores]
            Context[React Context]
            LocalStorage[Local Storage]
        end
    end
    
    AppRouter --> Marketing
    AppRouter --> Dashboard
    AppRouter --> App
    
    Dashboard --> Editor
    Dashboard --> Analytics
    Dashboard --> AgentUI
    
    Editor --> Zustand
    Analytics --> Context
    AgentUI --> Zustand
```

### AI Agent Architecture

```mermaid
graph LR
    subgraph "Agent Orchestration"
        Queue[Task Queue]
        Orchestrator[Advanced Orchestrator]
        Monitor[Progress Monitor]
    end
    
    subgraph "Specialized Agents"
        BaseAgent[Base Agent Class]
        SA[Story Architect]
        CD[Character Developer]
        CP[Chapter Planner]
        WA[Writing Agent]
        AP[Adaptive Planner]
    end
    
    subgraph "Shared Resources"
        Context[Book Context]
        Memory[Context Memory]
        Quality[Quality Assessment]
    end
    
    Orchestrator --> Queue
    Queue --> SA
    Queue --> CD
    Queue --> CP
    Queue --> WA
    Queue --> AP
    
    BaseAgent --> SA
    BaseAgent --> CD
    BaseAgent --> CP
    BaseAgent --> WA
    BaseAgent --> AP
    
    SA --> Context
    CD --> Context
    CP --> Context
    WA --> Context
    AP --> Context
    
    Context --> Memory
    Memory --> Quality
```

### Database Architecture

```mermaid
erDiagram
    projects ||--o{ chapters : contains
    projects ||--o{ characters : has
    projects ||--o{ story_arcs : defines
    projects ||--o{ project_collaborators : shares
    
    series ||--o{ projects : includes
    series ||--o{ universes : belongs_to
    
    universes ||--o{ universe_characters : contains
    universes ||--o{ timeline_events : tracks
    
    chapters ||--o{ chapter_versions : versioned
    chapters ||--o{ chapter_content : stores
    
    characters ||--o{ character_relationships : relates
    characters ||--o{ character_arcs : develops
    
    users ||--o{ projects : owns
    users ||--o{ achievements : earns
    users ||--o{ analytics_sessions : tracks
    
    projects {
        uuid id PK
        string title
        jsonb settings
        jsonb selections
        int target_words
        timestamp created_at
    }
    
    chapters {
        uuid id PK
        uuid project_id FK
        int number
        string title
        text content
        jsonb metadata
    }
    
    characters {
        uuid id PK
        uuid project_id FK
        string name
        jsonb profile
        jsonb personality
        jsonb voice_profile
    }
```

## Data Flow

### Content Generation Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Orchestrator
    participant Agents
    participant OpenAI
    participant Database
    
    User->>Frontend: Request content generation
    Frontend->>API: POST /api/agents/generate
    API->>Orchestrator: Initialize pipeline
    
    Orchestrator->>Agents: Queue agent tasks
    
    loop For each agent
        Agents->>OpenAI: Generate content
        OpenAI-->>Agents: Return results
        Agents->>Database: Save progress
        Agents-->>Orchestrator: Report completion
    end
    
    Orchestrator-->>API: Pipeline complete
    API-->>Frontend: Stream results
    Frontend-->>User: Display content
```

### Real-time Collaboration Flow

```mermaid
sequenceDiagram
    participant User1
    participant User2
    participant Frontend1
    participant Frontend2
    participant API
    participant Realtime
    participant Database
    
    User1->>Frontend1: Edit content
    Frontend1->>API: Send change
    API->>Database: Apply operation
    API->>Realtime: Broadcast change
    
    Realtime-->>Frontend2: Push update
    Frontend2-->>User2: Show change
    
    User2->>Frontend2: Edit content
    Frontend2->>API: Send change
    API->>Database: Apply operation
    API->>Realtime: Broadcast change
    
    Realtime-->>Frontend1: Push update
    Frontend1-->>User1: Show change
```

## Security Architecture

### Authentication & Authorization

```mermaid
graph TD
    subgraph "Authentication Flow"
        Client[Client App]
        AuthAPI[Auth API]
        SupaAuth[Supabase Auth]
        JWT[JWT Token]
    end
    
    subgraph "Authorization"
        Middleware[Auth Middleware]
        RLS[Row Level Security]
        RBAC[Role-Based Access]
    end
    
    subgraph "Security Layers"
        CSP[Content Security Policy]
        RateLimit[Rate Limiting]
        Validation[Input Validation]
        Encryption[Data Encryption]
    end
    
    Client --> AuthAPI
    AuthAPI --> SupaAuth
    SupaAuth --> JWT
    JWT --> Middleware
    
    Middleware --> RLS
    Middleware --> RBAC
    
    Client --> CSP
    Client --> RateLimit
    Client --> Validation
    Database --> Encryption
```

### Security Measures

1. **Authentication**
   - JWT-based authentication via Supabase Auth
   - Secure session management with automatic refresh
   - OAuth integration support

2. **Authorization**
   - Row-level security (RLS) policies in PostgreSQL
   - Role-based access control (admin, user, guest)
   - API-level authorization checks

3. **Data Protection**
   - TLS encryption for all communications
   - Encrypted storage for sensitive data
   - Secure environment variable management

4. **API Security**
   - Rate limiting per subscription tier
   - CORS configuration
   - Input validation with Zod schemas
   - SQL injection prevention

## Deployment Architecture

### Production Infrastructure

```mermaid
graph TB
    subgraph "CDN Layer"
        Cloudflare[Cloudflare CDN]
    end
    
    subgraph "Hosting"
        Vercel[Vercel Platform]
        EdgeFunctions[Edge Functions]
    end
    
    subgraph "Backend Services"
        Supabase[Supabase Cloud]
        PostgreSQL[(PostgreSQL)]
        RealtimeEngine[Realtime Engine]
        Storage[Object Storage]
    end
    
    subgraph "External APIs"
        OpenAI[OpenAI API]
        Stripe[Stripe API]
        Sentry[Sentry]
    end
    
    Users[Users] --> Cloudflare
    Cloudflare --> Vercel
    Vercel --> EdgeFunctions
    
    EdgeFunctions --> Supabase
    Supabase --> PostgreSQL
    Supabase --> RealtimeEngine
    Supabase --> Storage
    
    EdgeFunctions --> OpenAI
    EdgeFunctions --> Stripe
    Vercel --> Sentry
```

### Scaling Strategy

1. **Horizontal Scaling**
   - Vercel automatic scaling for frontend
   - Supabase connection pooling
   - Distributed caching with Redis

2. **Performance Optimization**
   - Static generation for marketing pages
   - Incremental static regeneration
   - Edge caching for API responses
   - Lazy loading for heavy components

3. **Database Optimization**
   - Indexed queries for common operations
   - Materialized views for analytics
   - Partitioned tables for large datasets
   - Connection pooling

## Key Architecture Decisions

### Technology Choices

1. **Next.js 15 with App Router**
   - Server components for better performance
   - Built-in API routes
   - Excellent TypeScript support
   - SEO optimization

2. **Supabase**
   - Managed PostgreSQL with real-time
   - Built-in authentication
   - Row-level security
   - File storage

3. **OpenAI GPT-4.1**
   - Best-in-class language generation
   - Consistent quality
   - Structured output support
   - Function calling capabilities

4. **Vercel Deployment**
   - Automatic scaling
   - Edge network
   - GitHub integration
   - Preview deployments

### Architectural Trade-offs

1. **Monolithic Frontend vs Microservices**
   - Chose monolithic for simplicity and faster development
   - Can be split later if needed

2. **Real-time Collaboration**
   - Operational Transform for conflict resolution
   - Trade-off: complexity vs user experience

3. **AI Agent Orchestration**
   - Sequential with limited concurrency (3 agents)
   - Trade-off: consistency vs speed

## Future Architecture Considerations

1. **Microservices Migration**
   - Extract AI orchestration to separate service
   - Dedicated analytics service
   - Content processing workers

2. **Enhanced Caching**
   - Redis for session management
   - CDN for generated content
   - Embedding cache optimization

3. **Multi-Region Deployment**
   - Database replication
   - Edge computing for AI
   - Regional CDN presence

4. **Advanced AI Features**
   - Custom fine-tuned models
   - Multi-modal content support
   - Voice synthesis integration