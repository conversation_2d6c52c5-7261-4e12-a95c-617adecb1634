"use client";

import { useState } from "react";
import { logger } from '@/lib/services/logger';
import { useToast } from "@/hooks/use-toast";

import { But<PERSON> } from "@/components/ui/button";
import { Download, FileText, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { DateRange } from "react-day-picker";

interface ExportAnalyticsProps {
  userId: string;
  projectId?: string;
  dateRange?: DateRange;
  analyticsData?: Record<string, unknown>;
}

export function ExportAnalytics({
  userId,
  projectId,
  dateRange,
  analyticsData,
}: ExportAnalyticsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState<"pdf" | "csv">("pdf");
  const [exportScope, setExportScope] = useState<"current" | "all">("current");
  const { toast } = useToast();

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Call the export API endpoint to generate the report
      const response = await fetch("/api/analytics/export", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          projectId,
          dateRange,
          format: exportFormat,
          scope: exportScope,
          data: analyticsData,
        }),
      });

      if (!response.ok) {
        throw new Error("Export failed");
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = url;
      a.download = `analytics-report-${new Date().toISOString().split("T")[0]}.${exportFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      
      setIsOpen(false);
      toast({
        title: "Export Successful",
        description: `Your analytics report has been downloaded as a ${exportFormat.toUpperCase()} file.`,
      });
    } catch (error) {
      logger.error("Export error:", error);
      toast({
        title: "Export Failed",
        description: "Unable to export analytics report. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Export Analytics Report</DialogTitle>
          <DialogDescription>
            Download your analytics data as a PDF or CSV file.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="format">Export Format</Label>
            <Select
              value={exportFormat}
              onValueChange={(value) => setExportFormat(value as "pdf" | "csv")}
            >
              <SelectTrigger id="format">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    PDF Report
                  </div>
                </SelectItem>
                <SelectItem value="csv">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    CSV Data
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="scope">Data Scope</Label>
            <Select
              value={exportScope}
              onValueChange={(value) => setExportScope(value as "current" | "all")}
            >
              <SelectTrigger id="scope">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="current">Current View</SelectItem>
                <SelectItem value="all">All Available Data</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {exportFormat === "pdf" && (
            <p className="text-sm text-muted-foreground">
              PDF reports include charts, insights, and formatted data for easy sharing.
            </p>
          )}
          
          {exportFormat === "csv" && (
            <p className="text-sm text-muted-foreground">
              CSV exports contain raw data for further analysis in spreadsheet applications.
            </p>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}