import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'

const privacySettingsSchema = z.object({
  settings: z.object({
    analytics_enabled: z.boolean(),
    behavioral_tracking: z.boolean(),
    performance_monitoring: z.boolean(),
    error_reporting: z.boolean(),
    share_usage_data: z.boolean(),
    participate_in_research: z.boolean(),
    marketing_emails: z.boolean(),
    progress_emails: z.boolean(),
    achievement_emails: z.boolean(),
    collaboration_emails: z.boolean(),
    newsletter: z.boolean()
  })
})

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Get user's privacy settings
    const { data: settings, error } = await supabase
      .from('user_privacy_settings')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (error && error.code !== 'PGRST116') { // Not found is ok
      throw error
    }

    // Return default settings if none exist
    const defaultSettings = {
      analytics_enabled: true,
      behavioral_tracking: true,
      performance_monitoring: true,
      error_reporting: true,
      share_usage_data: false,
      participate_in_research: false,
      marketing_emails: true,
      progress_emails: true,
      achievement_emails: true,
      collaboration_emails: true,
      newsletter: true
    }

    return NextResponse.json({
      settings: settings || defaultSettings
    })
  } catch (error) {
    console.error('Error fetching privacy settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch privacy settings' },
      { status: 500 }
    )
  }
})

export const PUT = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const body = await request.json()
    
    // Validate input
    const validatedData = privacySettingsSchema.parse(body)
    const supabase = await createTypedServerClient()

    // Upsert privacy settings
    const { error } = await supabase
      .from('user_privacy_settings')
      .upsert({
        user_id: user.id,
        ...validatedData.settings,
        updated_at: new Date().toISOString()
      })

    if (error) {
      throw error
    }

    // Update user preferences for email settings
    await supabase
      .from('user_preferences')
      .upsert({
        user_id: user.id,
        email_notifications: {
          marketing: validatedData.settings.marketing_emails,
          progress: validatedData.settings.progress_emails,
          achievements: validatedData.settings.achievement_emails,
          collaboration: validatedData.settings.collaboration_emails,
          newsletter: validatedData.settings.newsletter
        },
        updated_at: new Date().toISOString()
      })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating privacy settings:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid privacy settings', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to update privacy settings' },
      { status: 500 }
    )
  }
})