/**
 * Operational Transform implementation for real-time collaborative editing
 * Handles concurrent text operations and conflict resolution
 */

export interface Operation {
  type: 'insert' | 'delete' | 'replace';
  position: number;
  content?: string;
  length?: number;
  userId: string;
  timestamp: number;
  version?: number;
}

export interface TransformResult {
  operation1: Operation;
  operation2: Operation;
}

export class OperationalTransform {
  /**
   * Transform operation1 against operation2
   * Returns the transformed version of operation1 that can be applied after operation2
   */
  transformOperation(op1: Operation, op2: Operation | Operation[]): Operation {
    if (Array.isArray(op2)) {
      // Transform against multiple operations
      let transformed = op1;
      for (const op of op2) {
        transformed = this.transformOperation(transformed, op);
      }
      return transformed;
    }

    // Single operation transform
    return this.transformPair(op1, op2).operation1;
  }

  /**
   * Transform two operations against each other
   * Returns both transformed operations
   */
  transformPair(op1: Operation, op2: Operation): TransformResult {
    const type1 = op1.type;
    const type2 = op2.type;

    if (type1 === 'insert' && type2 === 'insert') {
      return this.transformInsertInsert(op1, op2);
    } else if (type1 === 'insert' && type2 === 'delete') {
      return this.transformInsertDelete(op1, op2);
    } else if (type1 === 'delete' && type2 === 'insert') {
      return this.transformDeleteInsert(op1, op2);
    } else if (type1 === 'delete' && type2 === 'delete') {
      return this.transformDeleteDelete(op1, op2);
    } else if (type1 === 'replace' || type2 === 'replace') {
      return this.transformWithReplace(op1, op2);
    }

    // Default: no transformation needed
    return { operation1: op1, operation2: op2 };
  }

  /**
   * Apply an operation to a string
   */
  applyOperation(text: string, operation: Operation): string {
    switch (operation.type) {
      case 'insert':
        return this.applyInsert(text, operation);
      case 'delete':
        return this.applyDelete(text, operation);
      case 'replace':
        return this.applyReplace(text, operation);
      default:
        return text;
    }
  }

  /**
   * Compose multiple operations into a single operation
   */
  composeOperations(operations: Operation[]): Operation | null {
    if (operations.length === 0) return null;
    if (operations.length === 1) return operations[0];

    // For now, we don't compose - each operation is applied separately
    // This could be optimized in the future
    return null;
  }

  // Private transform methods
  private transformInsertInsert(op1: Operation, op2: Operation): TransformResult {
    const pos1 = op1.position;
    const pos2 = op2.position;
    const len1 = op1.content?.length || 0;
    const len2 = op2.content?.length || 0;

    if (pos1 < pos2 || (pos1 === pos2 && op1.userId < op2.userId)) {
      // op1 comes first
      return {
        operation1: op1,
        operation2: { ...op2, position: pos2 + len1 },
      };
    } else {
      // op2 comes first
      return {
        operation1: { ...op1, position: pos1 + len2 },
        operation2: op2,
      };
    }
  }

  private transformInsertDelete(op1: Operation, op2: Operation): TransformResult {
    const insertPos = op1.position;
    const deletePos = op2.position;
    const insertLen = op1.content?.length || 0;
    const deleteLen = op2.length || 0;

    if (insertPos <= deletePos) {
      // Insert before delete
      return {
        operation1: op1,
        operation2: { ...op2, position: deletePos + insertLen },
      };
    } else if (insertPos >= deletePos + deleteLen) {
      // Insert after delete
      return {
        operation1: { ...op1, position: insertPos - deleteLen },
        operation2: op2,
      };
    } else {
      // Insert within delete range - split the delete
      return {
        operation1: { ...op1, position: deletePos },
        operation2: op2,
      };
    }
  }

  private transformDeleteInsert(op1: Operation, op2: Operation): TransformResult {
    const result = this.transformInsertDelete(op2, op1);
    return {
      operation1: result.operation2,
      operation2: result.operation1,
    };
  }

  private transformDeleteDelete(op1: Operation, op2: Operation): TransformResult {
    const pos1 = op1.position;
    const pos2 = op2.position;
    const len1 = op1.length || 0;
    const len2 = op2.length || 0;

    if (pos1 + len1 <= pos2) {
      // op1 before op2
      return {
        operation1: op1,
        operation2: { ...op2, position: pos2 - len1 },
      };
    } else if (pos2 + len2 <= pos1) {
      // op2 before op1
      return {
        operation1: { ...op1, position: pos1 - len2 },
        operation2: op2,
      };
    } else {
      // Overlapping deletes
      const overlapStart = Math.max(pos1, pos2);
      const overlapEnd = Math.min(pos1 + len1, pos2 + len2);
      const overlapLen = overlapEnd - overlapStart;

      if (pos1 < pos2) {
        return {
          operation1: { ...op1, length: len1 - overlapLen },
          operation2: { ...op2, position: pos1, length: len2 - overlapLen },
        };
      } else {
        return {
          operation1: { ...op1, position: pos2, length: len1 - overlapLen },
          operation2: { ...op2, length: len2 - overlapLen },
        };
      }
    }
  }

  private transformWithReplace(op1: Operation, op2: Operation): TransformResult {
    // Treat replace as delete + insert
    if (op1.type === 'replace') {
      const delete1: Operation = {
        type: 'delete',
        position: op1.position,
        length: op1.length,
        userId: op1.userId,
        timestamp: op1.timestamp,
      };
      const insert1: Operation = {
        type: 'insert',
        position: op1.position,
        content: op1.content,
        userId: op1.userId,
        timestamp: op1.timestamp,
      };

      // Transform delete first
      const deleteResult = this.transformPair(delete1, op2);
      
      // Then transform insert
      const insertResult = this.transformPair(insert1, deleteResult.operation2);

      // Combine back into replace
      return {
        operation1: {
          ...op1,
          position: insertResult.operation1.position,
        },
        operation2: insertResult.operation2,
      };
    }

    // op2 is replace, op1 is not
    const delete2: Operation = {
      type: 'delete',
      position: op2.position,
      length: op2.length,
      userId: op2.userId,
      timestamp: op2.timestamp,
    };
    const insert2: Operation = {
      type: 'insert',
      position: op2.position,
      content: op2.content,
      userId: op2.userId,
      timestamp: op2.timestamp,
    };

    // Transform op1 against delete
    const deleteResult = this.transformPair(op1, delete2);
    
    // Then transform against insert
    const insertResult = this.transformPair(deleteResult.operation1, insert2);

    return {
      operation1: insertResult.operation1,
      operation2: op2,
    };
  }

  // Private apply methods
  private applyInsert(text: string, operation: Operation): string {
    const pos = Math.max(0, Math.min(operation.position, text.length));
    const content = operation.content || '';
    return text.slice(0, pos) + content + text.slice(pos);
  }

  private applyDelete(text: string, operation: Operation): string {
    const pos = Math.max(0, Math.min(operation.position, text.length));
    const len = Math.min(operation.length || 0, text.length - pos);
    return text.slice(0, pos) + text.slice(pos + len);
  }

  private applyReplace(text: string, operation: Operation): string {
    const pos = Math.max(0, Math.min(operation.position, text.length));
    const len = Math.min(operation.length || 0, text.length - pos);
    const content = operation.content || '';
    return text.slice(0, pos) + content + text.slice(pos + len);
  }

  /**
   * Validate an operation
   */
  validateOperation(operation: Operation, textLength: number): boolean {
    if (operation.position < 0 || operation.position > textLength) {
      return false;
    }

    switch (operation.type) {
      case 'insert':
        return operation.content !== undefined;
      case 'delete':
        return (
          operation.length !== undefined &&
          operation.length > 0 &&
          operation.position + operation.length <= textLength
        );
      case 'replace':
        return (
          operation.content !== undefined &&
          operation.length !== undefined &&
          operation.length >= 0 &&
          operation.position + operation.length <= textLength
        );
      default:
        return false;
    }
  }

  /**
   * Invert an operation (for undo functionality)
   */
  invertOperation(operation: Operation, originalText: string): Operation {
    switch (operation.type) {
      case 'insert':
        return {
          type: 'delete',
          position: operation.position,
          length: operation.content?.length || 0,
          userId: operation.userId,
          timestamp: Date.now(),
        };
      case 'delete':
        const deletedText = originalText.slice(
          operation.position,
          operation.position + (operation.length || 0)
        );
        return {
          type: 'insert',
          position: operation.position,
          content: deletedText,
          userId: operation.userId,
          timestamp: Date.now(),
        };
      case 'replace':
        const replacedText = originalText.slice(
          operation.position,
          operation.position + (operation.length || 0)
        );
        return {
          type: 'replace',
          position: operation.position,
          length: operation.content?.length || 0,
          content: replacedText,
          userId: operation.userId,
          timestamp: Date.now(),
        };
    }
  }
}