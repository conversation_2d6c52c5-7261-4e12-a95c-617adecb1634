import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { StoryArchitectAgent } from '@/lib/agents/story-architect';
import { CharacterDeveloperAgent } from '@/lib/agents/character-developer';
import { ChapterPlannerAgent } from '@/lib/agents/chapter-planner';
import { WritingAgent } from '@/lib/agents/writing-agent';
import { AdaptivePlanningAgent } from '@/lib/agents/adaptive-planning-agent';
import type { BookContext, StoryStructure, CharacterProfiles } from '@/lib/agents/types';
import type { ProjectSettings } from '@/lib/types/project-settings';

// Mock OpenAI
jest.mock('openai');

describe('Agent Handoff and Integration Tests', () => {
  let storyArchitect: StoryArchitectAgent;
  let characterDeveloper: CharacterDeveloperAgent;
  let chapterPlanner: ChapterPlannerAgent;
  let writingAgent: WritingAgent;
  let adaptivePlanner: AdaptivePlanningAgent;
  
  const mockProjectSettings: ProjectSettings = {
    primaryGenre: 'fantasy',
    secondaryGenres: ['adventure'],
    targetAudience: 'adult',
    writingStyle: 'descriptive',
    narrativeVoice: 'third-person',
    tense: 'past',
    pacing: 'medium',
    violenceLevel: 'moderate',
    romanceLevel: 'low',
    profanityLevel: 'mild',
    themeDepth: 'deep',
    worldBuildingDepth: 'extensive',
    characterComplexity: 'complex',
    plotComplexity: 'complex',
    tone: 'serious',
    dialogueStyle: 'natural',
    descriptionLevel: 'detailed',
    useDeepPOV: true,
    showDontTell: true,
    varyProse: true,
    useSymbolism: true,
    useCliffhangers: true,
    useForeshadowing: true,
    useFlashbacks: false,
    useUnreliableNarrator: false,
    protagonistTypes: ['hero'],
    antagonistTypes: ['villain'],
    supportingRoles: ['mentor', 'sidekick'],
    majorThemes: ['courage', 'friendship'],
    minorThemes: ['sacrifice'],
    culturalElements: [],
    magicSystemType: 'soft',
    technologyLevel: 'medieval',
    politicalSystem: 'monarchy',
    economicSystem: 'feudal',
    geographyType: 'earth-like',
    pacingPreference: 'medium'
  };

  const mockContext: BookContext = {
    projectId: 'test-project',
    settings: mockProjectSettings,
    projectSelections: mockProjectSettings,
    storyPrompt: 'A hero saves the world from darkness',
    targetWordCount: 80000,
    targetChapters: 20
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    storyArchitect = new StoryArchitectAgent();
    characterDeveloper = new CharacterDeveloperAgent();
    chapterPlanner = new ChapterPlannerAgent();
    writingAgent = new WritingAgent(mockContext);
    adaptivePlanner = new AdaptivePlanningAgent(mockContext);
  });

  describe('Story Architect to Character Developer Handoff', () => {
    it('should pass story structure data correctly to character developer', async () => {
      const mockStoryStructure: StoryStructure = {
        title: 'The Hero\'s Journey',
        premise: 'A young hero must save the world from an ancient evil',
        genre: 'fantasy',
        themes: ['courage', 'sacrifice', 'friendship'],
        acts: [
          {
            actNumber: 1,
            title: 'The Call to Adventure',
            description: 'Hero discovers their destiny',
            chapters: [1, 2, 3, 4, 5],
            keyEvents: ['Discovery of power', 'Meeting the mentor'],
            turningPoint: 'Accepting the quest'
          }
        ],
        conflicts: [
          {
            type: 'external',
            description: 'Hero vs Dark Lord',
            stakes: 'The fate of the world',
            resolution: 'Final battle'
          }
        ],
        timeline: [
          {
            event: 'Story begins',
            date: 'Day 1',
            significance: 'Introduction to the ordinary world'
          }
        ],
        worldBuilding: {
          setting: {
            timeForPeriod: 'medieval fantasy',
            locations: ['Kingdom of Light', 'Dark Forest', 'Ancient Temple'],
            culture: 'Medieval European-inspired',
            technology: 'Pre-industrial with magic'
          },
          rules: ['Magic requires sacrifice', 'Ancient prophecies guide fate'],
          history: [
            {
              period: 'The Age of Heroes',
              events: ['Founding of the kingdom', 'First defeat of darkness'],
              impact: 'Established current world order'
            }
          ]
        },
        plotPoints: [
          {
            chapter: 1,
            event: 'Hero discovers magical ability',
            importance: 'critical',
            setupPayoff: 'setup'
          }
        ]
      };

      // Mock story architect response
      jest.spyOn(storyArchitect, 'generateStoryStructure').mockResolvedValue({
        success: true,
        data: mockStoryStructure,
        executionTime: 1000,
        tokensUsed: 500
      });

      // Generate story structure
      const storyResult = await storyArchitect.generateStoryStructure({
        projectSelections: mockProjectSettings,
        storyPrompt: mockContext.storyPrompt!,
        targetWordCount: mockContext.targetWordCount!,
        targetChapters: mockContext.targetChapters!
      });

      expect(storyResult.success).toBe(true);
      expect(storyResult.data).toBeDefined();

      // Now pass to character developer
      const characterResult = await characterDeveloper.generateCharacters({
        storyStructure: storyResult.data!,
        projectSelections: mockProjectSettings,
        characterRequirements: {
          protagonistCount: 1,
          antagonistCount: 1,
          supportingCount: 3
        }
      });

      // Verify character developer received and processed story data
      const generateCharactersCall = jest.spyOn(characterDeveloper, 'generateCharacters');
      expect(generateCharactersCall).toHaveBeenCalledWith(
        expect.objectContaining({
          storyStructure: expect.objectContaining({
            title: 'The Hero\'s Journey',
            themes: expect.arrayContaining(['courage', 'sacrifice', 'friendship'])
          })
        })
      );
    });

    it('should ensure character arcs align with story structure', async () => {
      const mockStoryStructure: StoryStructure = {
        title: 'Test Story',
        premise: 'A test premise',
        genre: 'fantasy',
        themes: ['redemption', 'growth'],
        acts: [
          {
            actNumber: 1,
            title: 'Setup',
            description: 'Character introduction',
            chapters: [1, 2, 3],
            keyEvents: ['Character flaw revealed'],
            turningPoint: 'Inciting incident'
          },
          {
            actNumber: 3,
            title: 'Resolution',
            description: 'Character transformation',
            chapters: [18, 19, 20],
            keyEvents: ['Character overcomes flaw'],
            turningPoint: 'Final realization'
          }
        ],
        conflicts: [],
        timeline: [],
        worldBuilding: {
          setting: {
            timeForPeriod: 'medieval',
            locations: [],
            culture: 'fantasy',
            technology: 'pre-industrial'
          },
          rules: [],
          history: []
        },
        plotPoints: []
      };

      // Mock character developer to check arc alignment
      const mockCharacters: CharacterProfiles = {
        protagonists: [{
          id: '1',
          name: 'Hero',
          role: 'protagonist',
          appearance: 'Young and determined',
          personality: {
            traits: ['brave', 'impulsive'],
            strengths: ['courage'],
            weaknesses: ['pride'],
            fears: ['failure'],
            desires: ['recognition']
          },
          backstory: 'Humble beginnings',
          motivation: 'Prove themselves',
          arc: {
            type: 'positive_change',
            startingPoint: 'Arrogant and impulsive',
            endingPoint: 'Wise and humble',
            keyMoments: [
              'Failure due to pride in Act 1',
              'Learning humility in Act 2',
              'Selfless sacrifice in Act 3'
            ],
            internalConflict: 'Pride vs Humility',
            externalConflict: 'Proving worth to others'
          },
          voice: {
            speakingStyle: 'Direct',
            vocabulary: 'Simple',
            mannerisms: []
          },
          relationships: []
        }],
        antagonists: [],
        supporting: [],
        relationships: []
      };

      jest.spyOn(characterDeveloper, 'generateCharacters').mockResolvedValue({
        success: true,
        data: mockCharacters,
        executionTime: 1000,
        tokensUsed: 500
      });

      const result = await characterDeveloper.generateCharacters({
        storyStructure: mockStoryStructure,
        projectSelections: mockProjectSettings,
        characterRequirements: {
          protagonistCount: 1,
          antagonistCount: 1,
          supportingCount: 2
        }
      });

      // Verify character arc aligns with story themes
      expect(result.data?.protagonists[0].arc.type).toBe('positive_change');
      expect(result.data?.protagonists[0].arc.keyMoments).toHaveLength(3);
      
      // Arc should span across acts
      const arcMoments = result.data?.protagonists[0].arc.keyMoments || [];
      expect(arcMoments[0]).toContain('Act 1');
      expect(arcMoments[2]).toContain('Act 3');
    });
  });

  describe('Character Developer to Chapter Planner Handoff', () => {
    it('should integrate character arcs into chapter planning', async () => {
      const mockStoryStructure: StoryStructure = {
        title: 'Test Story',
        premise: 'A test premise',
        genre: 'fantasy',
        themes: ['courage'],
        acts: [{
          actNumber: 1,
          title: 'Act 1',
          description: 'Setup',
          chapters: [1, 2, 3, 4, 5],
          keyEvents: ['Meeting'],
          turningPoint: 'Decision'
        }],
        conflicts: [],
        timeline: [],
        worldBuilding: {
          setting: {
            timeForPeriod: 'medieval',
            locations: ['Castle', 'Forest'],
            culture: 'fantasy',
            technology: 'pre-industrial'
          },
          rules: [],
          history: []
        },
        plotPoints: []
      };

      const mockCharacters = [
        {
          id: '1',
          name: 'Hero',
          role: 'protagonist' as const,
          personality: {
            traits: ['brave'],
            strengths: ['leadership'],
            weaknesses: ['impulsive']
          },
          arc: {
            type: 'positive_change' as const,
            keyMoments: [
              'Chapter 2: First challenge',
              'Chapter 10: Major setback',
              'Chapter 18: Final transformation'
            ]
          }
        },
        {
          id: '2',
          name: 'Mentor',
          role: 'supporting' as const,
          personality: {
            traits: ['wise'],
            strengths: ['knowledge'],
            weaknesses: ['age']
          },
          arc: {
            type: 'flat' as const,
            keyMoments: ['Chapter 3: Shares wisdom']
          }
        }
      ];

      // Mock chapter planner to verify it receives character data
      const mockChapterOutlines = {
        chapters: [
          {
            chapterNumber: 1,
            title: 'The Beginning',
            outline: 'Introduction to the world',
            targetWordCount: 4000,
            scenes: [
              {
                sceneNumber: 1,
                description: 'Opening scene',
                characters: ['Hero'],
                location: 'Castle',
                purpose: 'Establish protagonist',
                conflict: 'Internal doubt',
                expectedWordCount: 2000
              }
            ],
            characterArcs: ['Hero introduction'],
            plotPoints: ['Setup ordinary world'],
            themes: ['courage']
          }
        ],
        totalWordCount: 80000,
        estimatedReadingTime: 240
      };

      jest.spyOn(chapterPlanner, 'generateChapterOutlines').mockResolvedValue({
        success: true,
        data: mockChapterOutlines,
        executionTime: 1000,
        tokensUsed: 500
      });

      const result = await chapterPlanner.generateChapterOutlines({
        storyStructure: mockStoryStructure,
        characters: mockCharacters,
        targetWordCount: 80000,
        targetChapters: 20,
        projectSelections: mockProjectSettings
      });

      // Verify chapter planner integrated character information
      expect(result.data?.chapters[0].scenes[0].characters).toContain('Hero');
      expect(result.data?.chapters[0].characterArcs).toContain('Hero introduction');
    });

    it('should ensure character appearances align with their arcs', async () => {
      const mockChapters = Array.from({ length: 20 }, (_, i) => ({
        chapterNumber: i + 1,
        title: `Chapter ${i + 1}`,
        outline: 'Chapter outline',
        targetWordCount: 4000,
        scenes: [],
        characterArcs: [],
        plotPoints: [],
        themes: []
      }));

      // Character with specific arc moments
      const characterWithArc = {
        id: '1',
        name: 'Hero',
        role: 'protagonist' as const,
        arc: {
          type: 'positive_change' as const,
          keyMoments: [
            'Chapter 2: Discovers power',
            'Chapter 8: First failure', 
            'Chapter 15: Learns lesson',
            'Chapter 19: Final test'
          ]
        }
      };

      // Verify character appears in chapters matching their arc
      const chaptersWithHero = [2, 8, 15, 19];
      chaptersWithHero.forEach(chapterNum => {
        const chapter = mockChapters[chapterNum - 1];
        expect(chapter.chapterNumber).toBe(chapterNum);
        // In real implementation, would verify character appears in scenes
      });
    });
  });

  describe('Chapter Planner to Writing Agent Handoff', () => {
    it('should maintain continuity between chapters', async () => {
      const mockChapterOutline = {
        chapterNumber: 5,
        title: 'The Journey Continues',
        outline: 'Heroes travel through the dark forest',
        targetWordCount: 4000,
        scenes: [
          {
            sceneNumber: 1,
            description: 'Entering the forest',
            characters: ['Hero', 'Mentor'],
            location: 'Forest Edge',
            purpose: 'Build tension',
            conflict: 'Fear of unknown',
            expectedWordCount: 2000
          },
          {
            sceneNumber: 2,
            description: 'Ambush',
            characters: ['Hero', 'Mentor', 'Bandits'],
            location: 'Forest Path',
            purpose: 'Action sequence',
            conflict: 'Physical combat',
            expectedWordCount: 2000
          }
        ],
        characterArcs: ['Hero learns combat'],
        plotPoints: ['First real danger'],
        themes: ['courage', 'growth']
      };

      const previousChapterEnding = 'The hero looked at the dark forest ahead, knowing there was no turning back.';
      
      // Mock writing agent to check continuity
      jest.spyOn(writingAgent, 'generateChapter').mockImplementation(async (params) => {
        // Verify writing agent receives previous chapter context
        expect(params.previousChapterEnding).toBe(previousChapterEnding);
        expect(params.chapterOutline).toEqual(mockChapterOutline);
        
        return {
          success: true,
          data: {
            chapterNumber: 5,
            title: 'The Journey Continues',
            content: 'The forest loomed before them, just as threatening as it had seemed from afar...',
            wordCount: 4000,
            scenes: [],
            metadata: {
              readingTime: 15,
              sentimentScore: 0.3,
              paceScore: 0.7
            }
          },
          executionTime: 2000,
          tokensUsed: 1000
        };
      });

      const result = await writingAgent.generateChapter({
        chapterOutline: mockChapterOutline,
        previousChapterEnding,
        characterStates: new Map(),
        storyContext: {
          storyStructure: {} as StoryStructure,
          completedChapters: []
        }
      });

      expect(result.success).toBe(true);
      expect(result.data?.content).toContain('forest');
    });

    it('should track character states across chapters', async () => {
      const characterStates = new Map([
        ['Hero', {
          emotionalState: 'determined but fearful',
          physicalState: 'tired from journey', 
          relationships: { Mentor: 'growing trust' },
          location: 'Forest Edge'
        }],
        ['Mentor', {
          emotionalState: 'cautious',
          physicalState: 'alert',
          relationships: { Hero: 'protective' },
          location: 'Forest Edge'
        }]
      ]);

      const mockChapterOutline = {
        chapterNumber: 6,
        title: 'Lessons in the Dark',
        outline: 'Mentor teaches hero while in hiding',
        targetWordCount: 3500,
        scenes: [
          {
            sceneNumber: 1,
            description: 'Finding shelter',
            characters: ['Hero', 'Mentor'],
            location: 'Hidden Cave',
            purpose: 'Character development',
            conflict: 'Internal - Hero doubts abilities',
            expectedWordCount: 3500
          }
        ],
        characterArcs: ['Hero gains confidence'],
        plotPoints: ['Mentor reveals past'],
        themes: ['wisdom', 'growth']
      };

      jest.spyOn(writingAgent, 'generateChapter').mockImplementation(async (params) => {
        // Verify character states are passed
        expect(params.characterStates).toBe(characterStates);
        
        // Check specific character states
        const heroState = params.characterStates.get('Hero');
        expect(heroState?.emotionalState).toBe('determined but fearful');
        expect(heroState?.location).toBe('Forest Edge');
        
        return {
          success: true,
          data: {
            chapterNumber: 6,
            title: 'Lessons in the Dark',
            content: 'The cave provided shelter, but not comfort...',
            wordCount: 3500,
            scenes: [],
            metadata: {
              readingTime: 13,
              sentimentScore: 0.4,
              paceScore: 0.5
            }
          },
          executionTime: 2000,
          tokensUsed: 800
        };
      });

      const result = await writingAgent.generateChapter({
        chapterOutline: mockChapterOutline,
        previousChapterEnding: 'They found the cave just as night fell.',
        characterStates,
        storyContext: {
          storyStructure: {} as StoryStructure,
          completedChapters: []
        }
      });

      expect(result.success).toBe(true);
    });
  });

  describe('Adaptive Planning Agent Integration', () => {
    it('should detect and suggest fixes for consistency issues', async () => {
      const mockStoryContext = {
        storyStructure: {
          title: 'Test Story',
          timeline: [
            { event: 'Hero born', date: 'Year 1', significance: 'Beginning' },
            { event: 'Hero trains', date: 'Year 18', significance: 'Preparation' }
          ],
          worldBuilding: {
            rules: ['Magic requires years of study', 'No instant mastery']
          }
        } as StoryStructure,
        characters: {
          protagonists: [{
            id: '1',
            name: 'Hero',
            age: 16, // Inconsistent with timeline
            backstory: 'Started training at 18' // Inconsistent with age
          }]
        } as unknown as CharacterProfiles,
        completedChapters: [
          {
            chapterNumber: 3,
            content: 'The hero, having mastered magic in just days...' // Violates world rules
          }
        ]
      };

      jest.spyOn(adaptivePlanner, 'analyzeConsistency').mockResolvedValue({
        success: true,
        data: {
          issues: [
            {
              type: 'timeline',
              severity: 'high',
              description: 'Character age inconsistent with timeline',
              suggestion: 'Update character age to 18 or adjust timeline'
            },
            {
              type: 'world_rules',
              severity: 'high', 
              description: 'Magic mastery violates established rules',
              suggestion: 'Revise to show gradual learning or justify exception'
            }
          ],
          recommendations: [
            'Consider aging up the character to match timeline',
            'Rewrite magic mastery scene to show struggle and time passage'
          ]
        },
        executionTime: 500,
        tokensUsed: 200
      });

      const result = await adaptivePlanner.analyzeConsistency(mockStoryContext);

      expect(result.success).toBe(true);
      expect(result.data?.issues).toHaveLength(2);
      expect(result.data?.issues[0].type).toBe('timeline');
      expect(result.data?.issues[1].type).toBe('world_rules');
    });

    it('should provide adaptive suggestions based on user changes', async () => {
      const userChange = {
        type: 'character_modification',
        characterId: '1',
        field: 'motivation',
        oldValue: 'Revenge',
        newValue: 'Redemption'
      };

      const currentContext = {
        storyStructure: {
          themes: ['revenge', 'justice'],
          conflicts: [{
            type: 'external',
            description: 'Hero seeks revenge against villain'
          }]
        } as unknown as StoryStructure,
        completedChapters: [
          {
            chapterNumber: 1,
            content: 'Driven by vengeance, the hero began their quest...'
          },
          {
            chapterNumber: 2, 
            content: 'The taste of revenge was all that mattered...'
          }
        ]
      };

      jest.spyOn(adaptivePlanner, 'suggestAdaptations').mockResolvedValue({
        success: true,
        data: {
          adaptations: [
            {
              type: 'theme_update',
              description: 'Update story themes to include redemption',
              changes: ['Add redemption theme', 'Shift from revenge to justice']
            },
            {
              type: 'chapter_revision',
              description: 'Revise early chapters to reflect new motivation',
              changes: [
                'Chapter 1: Change "vengeance" to "justice"',
                'Chapter 2: Add internal conflict about revenge vs redemption'
              ]
            },
            {
              type: 'conflict_adjustment',
              description: 'Modify main conflict to support redemption arc',
              changes: ['Add internal conflict about forgiveness']
            }
          ],
          impact: 'high',
          estimatedRevisions: 5
        },
        executionTime: 600,
        tokensUsed: 300
      });

      const result = await adaptivePlanner.suggestAdaptations(userChange, currentContext);

      expect(result.success).toBe(true);
      expect(result.data?.adaptations).toHaveLength(3);
      expect(result.data?.adaptations[0].type).toBe('theme_update');
      expect(result.data?.impact).toBe('high');
    });
  });

  describe('Quality Metrics and Assessment', () => {
    it('should track quality metrics across agent handoffs', async () => {
      const qualityMetrics = {
        consistency: {
          timeline: 0.95,
          characterization: 0.88,
          worldBuilding: 0.92
        },
        engagement: {
          pacing: 0.85,
          tension: 0.80,
          emotionalImpact: 0.75
        },
        technical: {
          grammarScore: 0.98,
          readabilityScore: 0.90,
          vocabularyDiversity: 0.85
        }
      };

      // Each agent should maintain or improve quality metrics
      const metricsHistory: any[] = [];

      // Story Architect establishes baseline
      metricsHistory.push({
        agent: 'StoryArchitect',
        metrics: {
          consistency: { worldBuilding: 0.92 },
          engagement: { tension: 0.80 }
        }
      });

      // Character Developer maintains consistency
      metricsHistory.push({
        agent: 'CharacterDeveloper',
        metrics: {
          consistency: { characterization: 0.88, worldBuilding: 0.92 },
          engagement: { emotionalImpact: 0.75 }
        }
      });

      // Writing Agent adds technical quality
      metricsHistory.push({
        agent: 'WritingAgent',
        metrics: {
          ...qualityMetrics
        }
      });

      // Verify quality doesn't degrade
      for (let i = 1; i < metricsHistory.length; i++) {
        const current = metricsHistory[i];
        const previous = metricsHistory[i - 1];
        
        // Check maintained metrics
        if (previous.metrics.consistency?.worldBuilding) {
          expect(current.metrics.consistency?.worldBuilding).toBeGreaterThanOrEqual(
            previous.metrics.consistency.worldBuilding
          );
        }
      }
    });
  });
});