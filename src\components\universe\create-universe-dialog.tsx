'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Globe, Shield, Users, Sparkles, BookOpen } from 'lucide-react'
import type { Universe } from '@/lib/db/types'

interface CreateUniverseDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreate: (universe: Partial<Universe>) => void
}

export function CreateUniverseDialog({
  open,
  onOpenChange,
  onCreate
}: CreateUniverseDialogProps) {
  const [universeData, setUniverseData] = useState<Partial<Universe>>({
    name: '',
    description: '',
    is_public: false,
    rules: {
      magic_system: '',
      technology_level: '',
      physics_rules: '',
      time_rules: '',
      species: [],
      languages: [],
      religions: [],
      political_systems: []
    },
    settings: {
      allow_crossovers: true,
      maintain_timeline: true,
      share_characters: true,
      share_locations: true
    }
  })

  const handleSubmit = () => {
    if (!universeData.name?.trim()) return
    onCreate(universeData)
    // Reset form
    setUniverseData({
      name: '',
      description: '',
      is_public: false,
      rules: {
        magic_system: '',
        technology_level: '',
        physics_rules: '',
        time_rules: '',
        species: [],
        languages: [],
        religions: [],
        political_systems: []
      },
      settings: {
        allow_crossovers: true,
        maintain_timeline: true,
        share_characters: true,
        share_locations: true
      }
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Universe</DialogTitle>
          <DialogDescription>
            Define a shared world that can span multiple series and stories
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Universe Name</Label>
              <Input
                id="name"
                value={universeData.name || ''}
                onChange={(e) => setUniverseData({ ...universeData, name: e.target.value })}
                placeholder="The Realms of Aetheria"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={universeData.description || ''}
                onChange={(e) => setUniverseData({ ...universeData, description: e.target.value })}
                placeholder="A vast magical world where ancient powers clash with emerging technology..."
                rows={3}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Public Universe</Label>
                <p className="text-sm text-muted-foreground">
                  Allow other authors to create stories in this universe
                </p>
              </div>
              <Switch
                checked={universeData.is_public || false}
                onCheckedChange={(checked) => 
                  setUniverseData({ ...universeData, is_public: checked })
                }
              />
            </div>
          </div>

          {/* Universe Rules */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Universe Rules
              </CardTitle>
              <CardDescription>Define the fundamental laws of your universe</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                <div className="space-y-2">
                  <Label htmlFor="magic">Magic System</Label>
                  <Input
                    id="magic"
                    value={universeData.rules?.magic_system || ''}
                    onChange={(e) => setUniverseData({
                      ...universeData,
                      rules: { ...universeData.rules, magic_system: e.target.value }
                    })}
                    placeholder="Elemental magic, runes..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tech">Technology Level</Label>
                  <Input
                    id="tech"
                    value={universeData.rules?.technology_level || ''}
                    onChange={(e) => setUniverseData({
                      ...universeData,
                      rules: { ...universeData.rules, technology_level: e.target.value }
                    })}
                    placeholder="Medieval, steampunk, futuristic..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="physics">Physics Rules</Label>
                  <Input
                    id="physics"
                    value={universeData.rules?.physics_rules || ''}
                    onChange={(e) => setUniverseData({
                      ...universeData,
                      rules: { ...universeData.rules, physics_rules: e.target.value }
                    })}
                    placeholder="Earth-like, low gravity..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="time">Time Rules</Label>
                  <Input
                    id="time"
                    value={universeData.rules?.time_rules || ''}
                    onChange={(e) => setUniverseData({
                      ...universeData,
                      rules: { ...universeData.rules, time_rules: e.target.value }
                    })}
                    placeholder="Linear, time travel possible..."
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sharing Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Users className="h-4 w-4" />
                Sharing Settings
              </CardTitle>
              <CardDescription>Configure what can be shared across series</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Allow Crossovers</Label>
                  <p className="text-sm text-muted-foreground">
                    Characters can appear in multiple series
                  </p>
                </div>
                <Switch
                  checked={universeData.settings?.allow_crossovers ?? true}
                  onCheckedChange={(checked) => 
                    setUniverseData({
                      ...universeData,
                      settings: { ...universeData.settings, allow_crossovers: checked }
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Maintain Timeline</Label>
                  <p className="text-sm text-muted-foreground">
                    Enforce chronological consistency
                  </p>
                </div>
                <Switch
                  checked={universeData.settings?.maintain_timeline ?? true}
                  onCheckedChange={(checked) => 
                    setUniverseData({
                      ...universeData,
                      settings: { ...universeData.settings, maintain_timeline: checked }
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Share Characters</Label>
                  <p className="text-sm text-muted-foreground">
                    Characters available to all series
                  </p>
                </div>
                <Switch
                  checked={universeData.settings?.share_characters ?? true}
                  onCheckedChange={(checked) => 
                    setUniverseData({
                      ...universeData,
                      settings: { ...universeData.settings, share_characters: checked }
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Share Locations</Label>
                  <p className="text-sm text-muted-foreground">
                    Locations available to all series
                  </p>
                </div>
                <Switch
                  checked={universeData.settings?.share_locations ?? true}
                  onCheckedChange={(checked) => 
                    setUniverseData({
                      ...universeData,
                      settings: { ...universeData.settings, share_locations: checked }
                    })
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Universe Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium mb-1">{universeData.name || 'Untitled Universe'}</h4>
                  <p className="text-sm text-muted-foreground">
                    {universeData.description || 'No description provided'}
                  </p>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {universeData.is_public && (
                    <Badge variant="secondary">
                      <Globe className="h-3 w-3 mr-1" />
                      Public Universe
                    </Badge>
                  )}
                  {universeData.settings?.allow_crossovers && (
                    <Badge variant="outline">Crossovers Allowed</Badge>
                  )}
                  {universeData.settings?.maintain_timeline && (
                    <Badge variant="outline">Timeline Enforced</Badge>
                  )}
                  {universeData.rules?.magic_system && (
                    <Badge variant="outline">{universeData.rules.magic_system}</Badge>
                  )}
                  {universeData.rules?.technology_level && (
                    <Badge variant="outline">{universeData.rules.technology_level}</Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!universeData.name?.trim()}>
            Create Universe
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}