import { logger } from '@/lib/services/logger';

// Simple test file to verify our schemas work correctly
import { 
  createCharacterSchema, 
  createStoryBibleEntrySchema, 
  createChapterSchema,
  bulkCharacterOperationSchema 
} from './schemas'
import { TIME_MS } from '@/lib/constants'

// Test character schema
const testCharacter = {
  project_id: "123e4567-e89b-12d3-a456-************",
  character_id: "protagonist_hero",
  name: "Aria Stormwind",
  role: "protagonist" as const,
  description: "A brave warrior seeking to restore peace to the realm",
  backstory: "Born in a small village that was destroyed by dark forces",
  personality_traits: {
    traits: ["brave", "determined", "compassionate"],
    strengths: ["swordsmanship", "leadership"],
    flaws: ["impulsive", "trusting"],
    motivations: ["justice", "protecting innocents"]
  },
  character_arc: {
    starting_point: "Reluctant hero driven by revenge",
    transformation: "Learning to forgive and lead with wisdom",
    ending_point: "Wise leader who brings peace to the realm",
    key_moments: [
      { chapter: 1, description: "Village destroyed" },
      { chapter: 10, description: "Learns to forgive enemies" }
    ]
  },
  relationships: {
    "mentor_sage": {
      relationship_type: "mentor",
      description: "Wise teacher who guides her journey",
      history: "Met after village destruction",
      current_status: "trusted advisor"
    }
  },
  voice_data: {
    speaking_style: "Direct but compassionate",
    vocabulary: ["honor", "justice", "hope"],
    mannerisms: ["touches sword hilt when nervous", "speaks softly to children"]
  }
}

// Test story bible entry schema
const testStoryBibleEntry = {
  project_id: "123e4567-e89b-12d3-a456-************",
  entry_type: "world_rule" as const,
  entry_key: "magic_system_basics",
  entry_data: {
    name: "Elemental Magic",
    description: "Magic is drawn from the four elements",
    rules: ["Cannot use opposing elements simultaneously", "Magic depletes physical energy"],
    limitations: ["Requires specific gestures", "Affected by emotional state"]
  },
  chapter_introduced: 2,
  is_active: true
}

// Test chapter schema
const testChapter = {
  project_id: "123e4567-e89b-12d3-a456-************",
  chapter_number: 1,
  title: "The Village Burns",
  target_word_count: TIME_MS.TYPING_TIMEOUT,
  actual_word_count: 2850,
  outline: "Aria's village is attacked. She escapes and vows revenge.",
  content: "The smoke rose black against the orange sunset...",
  scenes_data: {
    scenes: [
      {
        number: 1,
        setting: "Aria's village",
        characters: ["Aria", "Village Elder"],
        objectives: ["Establish normal life", "Build character sympathy"],
        conflicts: ["Sudden attack"],
        resolutions: ["Aria escapes"]
      }
    ]
  },
  character_states: {
    "protagonist_hero": {
      emotional_state: "shocked and angry",
      goals: ["survive", "find safety"],
      knowledge: ["village is destroyed", "family is dead"],
      relationships: {
        "village_elder": "grief - trusted mentor now dead"
      }
    }
  },
  status: "complete" as const,
  pov_character: "protagonist_hero",
  plot_advancement: {
    main_plot: {
      threads: ["hero's journey begins", "revenge quest initiated"],
      advancement: "Inciting incident - normal world destroyed",
      conflicts_introduced: ["mysterious attackers", "loss of home"],
      conflicts_resolved: []
    },
    subplots: [
      {
        name: "Mystery of the attackers",
        advancement: "Attackers appear without warning",
        status: "active" as const
      }
    ]
  }
}

// Test bulk operation schema
const testBulkOperation = {
  operation: "create" as const,
  characters: [testCharacter]
}

// Run validation tests
export function runSchemaTests() {
  try {
    logger.info('Testing character schema...');
    createCharacterSchema.parse(testCharacter)
    logger.info('✅ Character schema validation passed');

    logger.info('Testing story bible entry schema...');
    createStoryBibleEntrySchema.parse(testStoryBibleEntry)
    logger.info('✅ Story bible entry schema validation passed');

    logger.info('Testing chapter schema...');
    createChapterSchema.parse(testChapter)
    logger.info('✅ Chapter schema validation passed');

    logger.info('Testing bulk operation schema...');
    bulkCharacterOperationSchema.parse(testBulkOperation)
    logger.info('✅ Bulk operation schema validation passed');

    logger.info('🎉 All schema tests passed!');
    return true
  } catch (error) {
    logger.error('❌ Schema validation failed:', error);
    return false
  }
}

// Export test data for use in actual tests
export {
  testCharacter,
  testStoryBibleEntry,
  testChapter,
  testBulkOperation
}