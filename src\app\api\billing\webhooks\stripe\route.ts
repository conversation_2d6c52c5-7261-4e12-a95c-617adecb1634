import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { config } from '@/lib/config'
import type Stripe from 'stripe'
import { TIME_MS } from '@/lib/constants'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { logger } from '@/lib/services/logger'
import { baseSchemas } from '@/lib/validation/common-schemas'

const webhookSecret = config.stripe.webhookSecret

// Validation schemas for webhook payloads
const checkoutSessionMetadataSchema = z.object({
  userId: z.string().uuid(),
  tierId: z.string()
})

const subscriptionSchema = z.object({
  id: z.string(),
  customer: z.string(),
  status: z.enum(['active', 'canceled', 'past_due', 'unpaid', 'incomplete', 'trialing']),
  cancel_at_period_end: z.boolean()
})

// No custom interface needed - using standard Stripe.Invoice with proper property access

export async function POST(request: NextRequest) {
  // Enhanced request validation for webhooks
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'stripe-webhook',
    rateLimitCost: 2,
    maxBodySize: 64 * 1024, // 64KB - Stripe events can be large
    allowedContentTypes: ['application/json', 'text/plain'], // Stripe sends as text/plain
    validateCSRF: false, // Webhooks don't have CSRF
    customValidator: async (req) => {
      // Verify webhook signature is present
      const headersList = await headers();
      const signature = headersList.get('stripe-signature');
      
      if (!signature) {
        return { 
          valid: false, 
          error: 'Missing Stripe signature header' 
        };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature')!;

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      
      logger.info('Stripe webhook received', {
        eventType: event.type,
        eventId: event.id,
        livemode: event.livemode,
        clientIP: context.clientIP
      });
    } catch (error) {
      logger.error('Webhook signature verification failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Invalid webhook signature', 400);
    }

    const supabase = await createTypedServerClient()

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object
        
        // Validate metadata
        try {
          const metadata = checkoutSessionMetadataSchema.parse({
            userId: session.metadata?.userId,
            tierId: session.metadata?.tierId
          })
          
          const userId = metadata.userId
          const tierId = metadata.tierId

          // Create subscription record
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string, {
            expand: ['items']
          })
          
          // Validate subscription data
          const validatedSub = subscriptionSchema.parse(subscription)
          
          // Get period dates from the first subscription item
          const firstItem = subscription.items.data[0]
          if (!firstItem) {
            throw new Error('No subscription items found')
          }
          
          await supabase.from('user_subscriptions').insert({
            user_id: userId,
            tier_id: tierId,
            status: 'active',
            stripe_subscription_id: validatedSub.id,
            stripe_customer_id: validatedSub.customer,
            current_period_start: new Date(firstItem.current_period_start * TIME_MS.SECOND),
            current_period_end: new Date(firstItem.current_period_end * TIME_MS.SECOND),
            cancel_at_period_end: validatedSub.cancel_at_period_end
          })

          // Subscription created successfully
        } catch (error) {
          logger.error('Failed to process checkout.session.completed', {
            sessionId: session.id,
            error: error instanceof Error ? error.message : 'Unknown error',
            clientIP: context.clientIP
          });
        }
        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        
        try {
          // Validate subscription data
          const validatedSub = subscriptionSchema.parse(subscription)
          
          // Get period dates from the first subscription item
          const firstItem = subscription.items.data[0]
          if (!firstItem) {
            throw new Error('No subscription items found')
          }
          
          await supabase
            .from('user_subscriptions')
            .update({
              status: validatedSub.status,
              current_period_start: new Date(firstItem.current_period_start * TIME_MS.SECOND),
              current_period_end: new Date(firstItem.current_period_end * TIME_MS.SECOND),
              cancel_at_period_end: validatedSub.cancel_at_period_end
            })
            .eq('stripe_subscription_id', validatedSub.id)
        } catch (error) {
          logger.error('Failed to process customer.subscription.updated', {
            subscriptionId: subscription.id,
            error: error instanceof Error ? error.message : 'Unknown error',
            clientIP: context.clientIP
          });
        }

        // Subscription updated
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        
        const { error } = await supabase
          .from('user_subscriptions')
          .update({ 
            status: 'canceled',
            canceled_at: new Date().toISOString()
          })
          .eq('stripe_subscription_id', subscription.id);

        if (error) {
          logger.error('Failed to update canceled subscription', {
            subscriptionId: subscription.id,
            error: error.message,
            clientIP: context.clientIP
          });
        } else {
          logger.info('Subscription canceled', {
            subscriptionId: subscription.id,
            clientIP: context.clientIP
          });
        }
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        // Reset usage for new billing period  
        if (invoice.billing_reason === 'subscription_cycle' && invoice.parent?.subscription_details?.subscription) {
          const subscriptionRef = invoice.parent.subscription_details.subscription
          const subscriptionId = typeof subscriptionRef === 'string' ? subscriptionRef : subscriptionRef.id
          const subscription = await stripe.subscriptions.retrieve(subscriptionId)
          const { data: userSub } = await supabase
            .from('user_subscriptions')
            .select('user_id')
            .eq('stripe_subscription_id', subscription.id)
            .single()

          if (userSub) {
            const periodStart = new Date().toISOString().slice(0, 7)
            await supabase
              .from('usage_tracking')
              .upsert({
                user_id: userSub.user_id,
                period_start: periodStart,
                ai_generations: 0,
                projects: 0,
                exports: 0,
                storage_used: 0,
                updated_at: new Date().toISOString()
              })
          }
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        
        if (invoice.parent?.subscription_details?.subscription) {
          const subscriptionRef = invoice.parent.subscription_details.subscription
          const subscriptionId = typeof subscriptionRef === 'string' ? subscriptionRef : subscriptionRef.id
          
          const { error } = await supabase
            .from('user_subscriptions')
            .update({ 
              status: 'past_due',
              updated_at: new Date().toISOString()
            })
            .eq('stripe_subscription_id', subscriptionId);

          if (error) {
            logger.error('Failed to update past due subscription', {
              subscriptionId,
              error: error.message,
              clientIP: context.clientIP
            });
          } else {
            logger.warn('Subscription marked as past due', {
              subscriptionId,
              clientIP: context.clientIP
            });
          }
        }
        break
      }

      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        
        logger.info('Payment intent succeeded', {
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          customerId: paymentIntent.customer,
          clientIP: context.clientIP
        });
        
        // Update payment intent record if it exists
        await supabase
          .from('payment_intents')
          .update({ 
            status: 'succeeded',
            succeeded_at: new Date().toISOString()
          })
          .eq('id', paymentIntent.id);
        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        
        logger.warn('Payment intent failed', {
          paymentIntentId: paymentIntent.id,
          error: paymentIntent.last_payment_error?.message,
          clientIP: context.clientIP
        });
        
        // Update payment intent record
        await supabase
          .from('payment_intents')
          .update({ 
            status: 'failed',
            failed_at: new Date().toISOString(),
            failure_reason: paymentIntent.last_payment_error?.message
          })
          .eq('id', paymentIntent.id);
        break;
      }

      default:
        logger.debug('Unhandled webhook event type', {
          eventType: event.type,
          eventId: event.id,
          clientIP: context.clientIP
        });
    }

    return UnifiedResponse.success({ 
      received: true,
      eventId: event.id 
    });
  } catch (error) {
    logger.error('Webhook handler error:', error, {
      clientIP: context.clientIP
    });
    
    return UnifiedResponse.error(
      'Webhook processing failed',
      500
    );
  }
}