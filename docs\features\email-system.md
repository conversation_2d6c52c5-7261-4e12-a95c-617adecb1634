# BookScribe Email System

## Overview

The Email System handles all outbound email communications including transactional emails, notifications, marketing communications, and automated responses. It provides template management, queue processing, delivery tracking, and preference management.

## Architecture

### Email Infrastructure

#### Email Queue Table
Manages email delivery queue:

```sql
email_queue:
  - id: UUID (Primary Key)
  - to: TEXT - Recipient email
  - cc: TEXT[] - CC recipients
  - bcc: TEXT[] - BCC recipients
  - from: TEXT - Sender email
  - reply_to: TEXT - Reply-to address
  - subject: TEXT - Email subject
  - template_id: VARCHAR(100) - Template identifier
  - template_data: JSONB - Template variables
  - html_content: TEXT - Rendered HTML
  - text_content: TEXT - Plain text version
  - status: VARCHAR(20) - pending, sending, sent, failed, bounced
  - priority: INTEGER - Delivery priority (1-10)
  - attempts: INTEGER - Send attempts
  - sent_at: TIMESTAMPTZ - When sent
  - error_message: TEXT - Error details
  - metadata: JSONB - Additional data
  - scheduled_for: TIMESTAMPTZ - Scheduled send time
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Email Templates Table
Stores email templates:

```sql
email_templates:
  - id: VARCHAR(100) (Primary Key) - Template identifier
  - name: VARCHAR(255) - Template name
  - description: TEXT
  - subject_template: TEXT - Subject line template
  - html_template: TEXT - HTML template
  - text_template: TEXT - Plain text template
  - variables: JSONB - Required variables schema
  - category: VARCHAR(50) - transactional, notification, marketing
  - active: BOOLEAN
  - version: INTEGER
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Email Preferences Table
User email preferences:

```sql
email_preferences:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - email: VARCHAR(255) - User email
  - category: VARCHAR(50) - Email category
  - subscribed: BOOLEAN - Opt-in status
  - frequency: VARCHAR(20) - instant, daily, weekly, never
  - last_sent_at: TIMESTAMPTZ
  - unsubscribed_at: TIMESTAMPTZ
  - unsubscribe_token: VARCHAR(255) - Unique token
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
  - UNIQUE(user_id, category)
```

## Email Categories

### 1. Transactional Emails
Always sent, cannot be unsubscribed:
- Welcome email
- Password reset
- Email verification
- Payment receipts
- Security alerts
- Account changes

### 2. Notification Emails
Based on user activity:
- Collaboration invites
- Comments and mentions
- Achievement unlocks
- Goal reminders
- Project updates

### 3. Marketing Emails
Promotional content:
- Feature announcements
- Tips and tutorials
- Community highlights
- Special offers
- Newsletter

### 4. System Emails
Administrative communications:
- Maintenance notices
- Terms updates
- Data privacy updates
- Account warnings

## Email Templates

### Template Structure
```handlebars
<!DOCTYPE html>
<html>
<head>
  <style>
    /* Email-safe CSS */
    .container { max-width: 600px; margin: 0 auto; }
    .header { background: #6B46C1; color: white; padding: 20px; }
    .content { padding: 20px; }
    .footer { background: #f5f5f5; padding: 20px; font-size: 12px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>{{app_name}}</h1>
    </div>
    <div class="content">
      {{#if user_name}}
        <p>Hi {{user_name}},</p>
      {{/if}}
      
      {{{content}}}
      
      {{#if action_url}}
        <p>
          <a href="{{action_url}}" class="button">{{action_text}}</a>
        </p>
      {{/if}}
    </div>
    <div class="footer">
      <p>© {{year}} BookScribe. All rights reserved.</p>
      {{#unless is_transactional}}
        <p><a href="{{unsubscribe_url}}">Unsubscribe</a></p>
      {{/unless}}
    </div>
  </div>
</body>
</html>
```

### Common Templates

#### Welcome Email
```json
{
  "id": "welcome_email",
  "subject_template": "Welcome to BookScribe, {{user_name}}!",
  "variables": {
    "user_name": "string",
    "verification_url": "string",
    "getting_started_url": "string"
  }
}
```

#### Collaboration Invite
```json
{
  "id": "collaboration_invite",
  "subject_template": "{{inviter_name}} invited you to collaborate on {{project_title}}",
  "variables": {
    "inviter_name": "string",
    "project_title": "string",
    "role": "string",
    "accept_url": "string",
    "message": "string?"
  }
}
```

## API Endpoints

### Email Sending

#### POST /api/email/send
Send immediate email:

```typescript
// Request
{
  to: "<EMAIL>",
  template_id: "welcome_email",
  template_data: {
    user_name: "John Doe",
    verification_url: "https://bookscribe.ai/verify/token"
  },
  priority: 5
}

// Response
{
  id: "uuid",
  status: "queued",
  scheduled_for: "2024-01-15T10:30:00Z"
}
```

#### POST /api/email/send-batch
Send bulk emails:

```typescript
// Request
{
  template_id: "newsletter",
  recipients: [
    {
      email: "<EMAIL>",
      data: { name: "User 1" }
    },
    {
      email: "<EMAIL>",
      data: { name: "User 2" }
    }
  ],
  schedule_for: "2024-01-16T09:00:00Z"
}

// Response
{
  batch_id: "uuid",
  total: 2,
  queued: 2,
  failed: 0
}
```

### Email Preferences

#### GET /api/email/preferences
Get user email preferences:

```typescript
// Response
{
  preferences: [
    {
      category: "notifications",
      subscribed: true,
      frequency: "instant"
    },
    {
      category: "marketing",
      subscribed: false,
      unsubscribed_at: "2024-01-10T15:00:00Z"
    }
  ],
  global_unsubscribe: false
}
```

#### PUT /api/email/preferences
Update email preferences:

```typescript
// Request
{
  category: "notifications",
  subscribed: false
}
```

### Email Queue Management

#### GET /api/email/queue/status
Check queue status:

```typescript
// Response
{
  pending: 45,
  sending: 3,
  sent_today: 1250,
  failed_today: 5,
  average_send_time: 2.3 // seconds
}
```

#### POST /api/email/queue/process
Manually trigger queue processing:

```typescript
// Request
{
  limit: 100,
  priority_threshold: 5
}

// Response
{
  processed: 85,
  failed: 2,
  remaining: 15
}
```

## Email Delivery

### Queue Processing
```typescript
class EmailQueueProcessor {
  async processQueue() {
    const emails = await this.getQueuedEmails({
      status: 'pending',
      scheduled_for: { lte: new Date() },
      order_by: ['priority DESC', 'created_at ASC'],
      limit: this.batchSize
    });

    for (const email of emails) {
      await this.sendEmail(email);
    }
  }

  async sendEmail(email: QueuedEmail) {
    try {
      await this.updateStatus(email.id, 'sending');
      
      const result = await this.provider.send({
        to: email.to,
        subject: email.subject,
        html: email.html_content,
        text: email.text_content
      });

      await this.updateStatus(email.id, 'sent', {
        sent_at: new Date(),
        message_id: result.messageId
      });
    } catch (error) {
      await this.handleSendError(email, error);
    }
  }
}
```

### Email Providers
Support for multiple providers:

```typescript
interface EmailProvider {
  send(email: Email): Promise<SendResult>;
  verifyWebhook(payload: any): boolean;
  handleBounce(event: BounceEvent): void;
  handleComplaint(event: ComplaintEvent): void;
}

// Implementations
class SendGridProvider implements EmailProvider {}
class AWSProvider implements EmailProvider {}
class MailgunProvider implements EmailProvider {}
```

## Template Management

### Template Rendering
```typescript
class TemplateRenderer {
  render(template: EmailTemplate, data: Record<string, any>) {
    // Validate required variables
    this.validateVariables(template.variables, data);
    
    // Render with Handlebars
    const html = this.renderHandlebars(template.html_template, data);
    const text = this.renderHandlebars(template.text_template, data);
    const subject = this.renderHandlebars(template.subject_template, data);
    
    // Post-process
    return {
      subject: this.sanitizeSubject(subject),
      html: this.inlineCSS(html),
      text: this.formatPlainText(text)
    };
  }
}
```

### Template Variables
Common variables available:

```typescript
interface CommonTemplateData {
  app_name: string;
  app_url: string;
  user_name?: string;
  user_email: string;
  current_year: number;
  unsubscribe_url: string;
  preferences_url: string;
  support_email: string;
}
```

## Tracking & Analytics

### Email Events
Track delivery lifecycle:

```sql
email_events:
  - id: UUID
  - email_id: UUID - References email_queue
  - event_type: VARCHAR(50) - sent, delivered, opened, clicked, bounced, complained
  - timestamp: TIMESTAMPTZ
  - metadata: JSONB - Provider-specific data
  - ip_address: INET
  - user_agent: TEXT
```

### Metrics Tracking
```typescript
interface EmailMetrics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  complained: number;
  unsubscribed: number;
  
  // Rates
  delivery_rate: number;
  open_rate: number;
  click_rate: number;
  bounce_rate: number;
  complaint_rate: number;
}
```

## Unsubscribe Management

### One-Click Unsubscribe
```typescript
// GET /unsubscribe/{token}
async function handleUnsubscribe(token: string) {
  const preference = await getPreferenceByToken(token);
  
  if (!preference) {
    throw new Error('Invalid unsubscribe token');
  }
  
  await updatePreference(preference.id, {
    subscribed: false,
    unsubscribed_at: new Date()
  });
  
  return { success: true, category: preference.category };
}
```

### List-Unsubscribe Header
```typescript
const headers = {
  'List-Unsubscribe': `<${unsubscribeUrl}>, <mailto:<EMAIL>?subject=unsubscribe>`,
  'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click'
};
```

## Security & Compliance

### Email Security
- SPF records configured
- DKIM signing enabled
- DMARC policy set
- TLS encryption required

### GDPR Compliance
- Explicit consent for marketing
- Easy unsubscribe process
- Data retention policies
- Right to deletion

### CAN-SPAM Compliance
- Physical address in footer
- Clear sender identification
- Accurate subject lines
- Unsubscribe mechanism

## Performance Optimization

### Queue Optimization
```sql
CREATE INDEX idx_email_queue_status_scheduled 
  ON email_queue(status, scheduled_for) 
  WHERE status = 'pending';
  
CREATE INDEX idx_email_queue_priority 
  ON email_queue(priority DESC) 
  WHERE status = 'pending';
```

### Batching Strategy
- Group by template for efficiency
- Bulk API calls to provider
- Connection pooling
- Rate limit awareness

## Error Handling

### Retry Strategy
```typescript
const retryConfig = {
  max_attempts: 3,
  delays: [60, 300, 900], // seconds
  retryable_errors: [
    'RATE_LIMIT',
    'TEMPORARY_FAILURE',
    'CONNECTION_ERROR'
  ]
};
```

### Bounce Handling
```typescript
async function handleBounce(event: BounceEvent) {
  if (event.type === 'hard_bounce') {
    await markEmailInvalid(event.email);
    await notifyUser(event.user_id, 'email_invalid');
  } else if (event.type === 'soft_bounce') {
    await incrementBounceCount(event.email);
  }
}
```

## Future Enhancements

1. **Advanced Personalization**
   - AI-powered content generation
   - Dynamic content blocks
   - A/B testing framework

2. **Rich Media**
   - AMP email support
   - Interactive components
   - Embedded surveys

3. **Automation**
   - Drip campaigns
   - Behavioral triggers
   - Lifecycle emails

4. **Analytics Dashboard**
   - Real-time metrics
   - Campaign performance
   - Deliverability monitoring

## Related Systems
- Notification System (email delivery channel)
- User Management (preferences)
- Template Engine (content rendering)
- Analytics System (tracking metrics)