# BookScribe Writing Goals System

## Overview

The Writing Goals System helps authors set and track writing targets at multiple levels - daily, weekly, monthly, and project-specific. It integrates with writing sessions to automatically track progress and provides insights to help maintain consistent writing habits.

## Architecture

### Database Schema

#### Writing Goals Table
Stores goal definitions:

```sql
writing_goals:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects (optional)
  - goal_type: TEXT - daily, weekly, monthly, project
  - target_words: INTEGER - Word count target (> 0)
  - start_date: DATE - Goal start date
  - end_date: DATE - Goal end date (optional)
  - is_active: BOOLEAN - Currently active flag
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
  - CONSTRAINT unique_active_goal UNIQUE NULLS NOT DISTINCT (user_id, project_id, goal_type, is_active)
```

#### Writing Goal Progress Table
Tracks daily progress toward goals:

```sql
writing_goal_progress:
  - id: UUID (Primary Key)
  - goal_id: UUID - References writing_goals
  - date: DATE - Progress date
  - words_written: INTEGER - Words written on this date
  - sessions_count: INTEGER - Number of writing sessions
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
  - CONSTRAINT unique_goal_date UNIQUE (goal_id, date)
```

#### Writing Sessions Table
Records individual writing sessions:

```sql
writing_sessions:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects
  - chapter_id: UUID - References chapters (optional)
  - word_count: INTEGER - Words written in session
  - duration: INTEGER - Session duration in seconds
  - started_at: TIMESTAMPTZ
  - ended_at: TIMESTAMPTZ
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

## Goal Types

### 1. Daily Goals
- Reset every 24 hours
- Track consecutive days (streaks)
- Time zone aware
- Example: 500 words/day

### 2. Weekly Goals
- Monday to Sunday cycle
- Cumulative word count
- Allow catch-up days
- Example: 5,000 words/week

### 3. Monthly Goals
- Calendar month based
- Progress visualization
- Milestone tracking
- Example: 25,000 words/month

### 4. Project Goals
- Tied to specific project
- No time limit by default
- Track toward completion
- Example: 80,000 words total

## API Endpoints

### Goal Management

#### GET /api/writing/goals
Retrieve user's writing goals:

```typescript
// Request
GET /api/writing/goals?active=true&type=daily

// Response
{
  goals: [
    {
      id: "uuid",
      goal_type: "daily",
      target_words: 1000,
      start_date: "2024-01-01",
      end_date: null,
      is_active: true,
      current_progress: 750,
      percentage_complete: 75,
      days_active: 15,
      streak_count: 7
    }
  ]
}
```

#### POST /api/writing/goals
Create new writing goal:

```typescript
// Request
{
  goal_type: "daily",
  target_words: 1000,
  start_date: "2024-01-15",
  project_id: null // null for global goals
}

// Response
{
  id: "uuid",
  goal_type: "daily",
  target_words: 1000,
  start_date: "2024-01-15",
  is_active: true
}
```

#### PUT /api/writing/goals/{id}
Update existing goal:

```typescript
// Request
{
  target_words: 1500,
  is_active: false
}
```

#### DELETE /api/writing/goals/{id}
Delete a writing goal.

### Progress Tracking

#### GET /api/writing/goals/progress
Get goal progress data:

```typescript
// Request
GET /api/writing/goals/progress?goal_id=uuid&date_from=2024-01-01&date_to=2024-01-31

// Response
{
  goal: {
    id: "uuid",
    type: "monthly",
    target_words: 25000
  },
  progress: [
    {
      date: "2024-01-01",
      words_written: 1250,
      sessions_count: 2,
      cumulative_total: 1250
    },
    {
      date: "2024-01-02",
      words_written: 800,
      sessions_count: 1,
      cumulative_total: 2050
    }
  ],
  summary: {
    total_words: 15750,
    average_per_day: 1050,
    best_day: {
      date: "2024-01-05",
      words: 2500
    },
    current_streak: 7,
    longest_streak: 12,
    completion_rate: 63
  }
}
```

#### POST /api/writing/goals/progress
Manually record progress:

```typescript
// Request
{
  goal_id: "uuid",
  date: "2024-01-15",
  words_written: 1200
}
```

### Goal Recommendations

#### GET /api/goals/recommendations
Get AI-powered goal suggestions:

```typescript
// Response
{
  recommendations: [
    {
      type: "daily",
      suggested_target: 750,
      reasoning: "Based on your average of 850 words/day over the last month",
      confidence: 0.85
    },
    {
      type: "project",
      suggested_target: 80000,
      reasoning: "Standard novel length for your genre (thriller)",
      confidence: 0.90
    }
  ]
}
```

## Automatic Progress Tracking

### Writing Session Integration
Progress automatically updated when:
1. Chapter content saved
2. Writing session ends
3. Word count changes detected

### Trigger Function
```sql
CREATE OR REPLACE FUNCTION update_goal_progress()
RETURNS TRIGGER AS $$
DECLARE
  active_goals RECORD;
  session_date DATE;
BEGIN
  session_date := DATE(NEW.started_at);
  
  -- Find all active goals for this user and project
  FOR active_goals IN 
    SELECT id FROM writing_goals 
    WHERE user_id = NEW.user_id 
    AND is_active = true
    AND start_date <= session_date
    AND (end_date IS NULL OR end_date >= session_date)
    AND (project_id IS NULL OR project_id = NEW.project_id)
  LOOP
    -- Insert or update progress
    INSERT INTO writing_goal_progress (goal_id, date, words_written, sessions_count)
    VALUES (active_goals.id, session_date, NEW.word_count, 1)
    ON CONFLICT (goal_id, date) 
    DO UPDATE SET 
      words_written = writing_goal_progress.words_written + NEW.word_count,
      sessions_count = writing_goal_progress.sessions_count + 1,
      updated_at = NOW();
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## UI Components

### Goal Dashboard
Main interface for goal management:

```tsx
<WritingGoalsDashboard
  userId={userId}
  projectId={projectId}
  showStreaks={true}
  allowEdit={true}
/>
```

### Goal Progress Chart
Visualize progress over time:

```tsx
<GoalProgressChart
  goalId={goalId}
  dateRange="last30days"
  chartType="bar" | "line" | "calendar"
  showTrendline={true}
/>
```

### Goal Widget
Compact display for editor:

```tsx
<GoalProgressWidget
  goals={activeGoals}
  position="bottom-right"
  collapsible={true}
  showNotifications={true}
/>
```

### Streak Counter
Display writing streaks:

```tsx
<StreakCounter
  currentStreak={7}
  bestStreak={21}
  showMotivation={true}
/>
```

## Progress Calculations

### Daily Progress
```typescript
interface DailyProgress {
  date: Date;
  target: number;
  actual: number;
  percentage: number;
  met_goal: boolean;
}
```

### Streak Calculation
```typescript
function calculateStreak(progress: DailyProgress[]): {
  current: number;
  longest: number;
  dates: Date[];
} {
  // Logic to find consecutive days meeting goals
}
```

### Projections
```typescript
interface GoalProjection {
  current_pace: number; // words per day
  projected_completion: Date;
  required_pace: number; // to meet deadline
  ahead_behind: number; // days ahead/behind
}
```

## Notifications & Reminders

### Goal Notifications
- Daily reminder at user-set time
- Progress milestone alerts (25%, 50%, 75%, 100%)
- Streak maintenance reminders
- Goal achievement celebrations

### Email Notifications
```typescript
interface GoalNotification {
  type: 'reminder' | 'milestone' | 'achievement' | 'streak';
  user_id: string;
  goal_id: string;
  message: string;
  send_at: Date;
}
```

## Analytics Integration

### Goal Metrics
Track for insights:
- Goal completion rates
- Average daily word counts
- Streak patterns
- Time of day preferences
- Project vs. general goals

### Success Factors
Analyze what helps users meet goals:
- Optimal goal sizes
- Best days/times for writing
- Session duration patterns
- Environmental factors

## Performance Considerations

### Database Indexes
```sql
CREATE INDEX idx_writing_goals_user_active ON writing_goals(user_id, is_active);
CREATE INDEX idx_writing_goals_project ON writing_goals(project_id) WHERE project_id IS NOT NULL;
CREATE INDEX idx_writing_goals_dates ON writing_goals(start_date, end_date);
CREATE INDEX idx_writing_goal_progress_goal ON writing_goal_progress(goal_id);
CREATE INDEX idx_writing_goal_progress_date ON writing_goal_progress(date);
```

### Caching Strategy
- Active goals cached in session
- Progress calculations cached hourly
- Streak data cached daily

## Security

### Row Level Security
All tables have RLS enabled:
- Users can only access their own goals
- Progress inherits from parent goal
- No cross-user data access

### Validation
- Target words must be positive
- Dates must be logical (start <= end)
- Only one active goal per type/project

## Future Enhancements

1. **Social Features**
   - Goal sharing with accountability partners
   - Writing groups with collective goals
   - Public goal commitments

2. **Advanced Analytics**
   - ML-powered goal recommendations
   - Predictive completion dates
   - Personalized pacing suggestions

3. **Gamification**
   - Goal-based achievements
   - Milestone rewards
   - Leaderboards (opt-in)

4. **Integration**
   - Calendar sync
   - Mobile app notifications
   - Wearable device reminders

## Related Systems
- Achievement System (goal-based achievements)
- Analytics System (writing patterns)
- Notification System (reminders)
- Writing Sessions (automatic tracking)