const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all API route files
const apiRoutes = glob.sync('src/app/api/**/*.ts', {
  ignore: ['**/route.refactored.ts', '**/*.test.ts', '**/*.spec.ts']
});

console.log(`\nChecking ${apiRoutes.length} API route files for remaining auth patterns...\n`);

let updatedCount = 0;
let skippedCount = 0;
let publicRoutes = [];
let needsManualReview = [];

apiRoutes.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Skip if already using UnifiedAuthService
    if (content.includes('UnifiedAuthService')) {
      skippedCount++;
      return;
    }
    
    // Check if it's a public route
    if (filePath.includes('/webhooks/') || 
        filePath.includes('/health/') || 
        filePath.includes('/demo/') ||
        content.includes('// Public route') ||
        content.includes('// No auth required')) {
      publicRoutes.push(filePath);
      skippedCount++;
      return;
    }
    
    // Pattern 1: requireAuth pattern
    if (content.includes('requireAuth')) {
      console.log(`🔄 Migrating requireAuth pattern: ${filePath}`);
      
      // Add import if needed
      if (!content.includes("import { UnifiedAuthService")) {
        content = content.replace(
          /import.*requireAuth.*from.*auth-middleware.*\n/g,
          "import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'\n"
        );
      }
      
      // Replace function declarations
      content = content.replace(
        /export async function (GET|POST|PUT|DELETE|PATCH)\s*\([^)]*\)\s*{\s*try\s*{\s*(?:const\s+)?authResult\s*=\s*await\s+requireAuth\([^)]*\);?\s*if\s*\(authResult\s+instanceof\s+NextResponse\)\s*{\s*return\s+authResult;?\s*}\s*(?:const\s+)?user\s*=\s*authResult;?/g,
        (match, method) => {
          return `export const ${method} = UnifiedAuthService.withAuth(async (request) => {\n  try {\n    const user = request.user!;`;
        }
      );
      
      // Fix closing braces
      if (content !== originalContent) {
        // Count braces to find correct position
        const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        methods.forEach(method => {
          const pattern = new RegExp(`export const ${method} = UnifiedAuthService\\.withAuth`);
          if (pattern.test(content)) {
            // Find the matching closing brace
            const startMatch = content.match(pattern);
            if (startMatch) {
              const startIndex = startMatch.index;
              let braceCount = 0;
              let foundStart = false;
              
              for (let i = startIndex; i < content.length; i++) {
                if (content[i] === '{') {
                  if (!foundStart) foundStart = true;
                  braceCount++;
                } else if (content[i] === '}') {
                  braceCount--;
                  if (foundStart && braceCount === 0) {
                    // Check if there's already a closing paren
                    if (content[i + 1] !== ')') {
                      content = content.slice(0, i + 1) + ')' + content.slice(i + 1);
                    }
                    break;
                  }
                }
              }
            }
          }
        });
      }
    }
    
    // Pattern 2: Direct supabase.auth.getUser() without createServerClient
    else if (content.includes('supabase.auth.getUser()') && !content.includes('createServerClient')) {
      needsManualReview.push({
        file: filePath,
        reason: 'Uses supabase.auth.getUser() but pattern unclear'
      });
      return;
    }
    
    // Pattern 3: Any other auth-related patterns
    else if (content.match(/auth\.getUser|authenticat|requireAuth/i)) {
      needsManualReview.push({
        file: filePath,
        reason: 'Contains auth-related code but pattern not recognized'
      });
      return;
    }
    
    // Save if changes were made
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated: ${filePath}`);
      updatedCount++;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log('\n📊 Final Migration Summary:');
console.log(`✅ Updated: ${updatedCount} files`);
console.log(`⏭️  Skipped: ${skippedCount} files`);
console.log(`\n📌 Public Routes (${publicRoutes.length}):`);
publicRoutes.forEach(route => console.log(`  - ${route}`));
console.log(`\n⚠️  Needs Manual Review (${needsManualReview.length}):`);
needsManualReview.forEach(item => console.log(`  - ${item.file} (${item.reason})`));