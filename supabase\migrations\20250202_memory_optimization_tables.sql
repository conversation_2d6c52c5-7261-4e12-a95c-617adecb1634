-- Create memory settings table
CREATE TABLE IF NOT EXISTS memory_settings (
  project_id uuid PRIMARY KEY REFERENCES projects(id) ON DELETE CASCADE,
  auto_optimization jsonb DEFAULT '{
    "enabled": true,
    "threshold": 80,
    "strategy": "balanced",
    "checkInterval": 10,
    "preserveRecentHours": 24,
    "targetReduction": 30,
    "notifyUser": true
  }'::jsonb,
  retention_policy jsonb DEFAULT '{
    "keepDays": 30,
    "archiveAfterDays": 90,
    "deleteAfterDays": 365
  }'::jsonb,
  compression_settings jsonb DEFAULT '{
    "enableAutoCompression": true,
    "compressionThreshold": 5000,
    "compressionStrategy": "balanced"
  }'::jsonb,
  notifications jsonb DEFAULT '{
    "notifyOnHighUsage": true,
    "notifyOnOptimization": true,
    "highUsageThreshold": 80
  }'::jsonb,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create memory optimization logs table
CREATE TABLE IF NOT EXISTS memory_optimization_logs (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id uuid NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  optimization_type text NOT NULL CHECK (optimization_type IN ('auto', 'manual')),
  strategy text NOT NULL,
  tokens_before integer NOT NULL,
  tokens_after integer NOT NULL,
  tokens_saved integer NOT NULL,
  compression_ratio numeric(5,4) NOT NULL,
  success boolean NOT NULL DEFAULT true,
  error text,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes
CREATE INDEX idx_memory_optimization_logs_project_id ON memory_optimization_logs(project_id);
CREATE INDEX idx_memory_optimization_logs_created_at ON memory_optimization_logs(created_at DESC);

-- Add compression fields to chapters table
ALTER TABLE chapters
ADD COLUMN IF NOT EXISTS is_compressed boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS compressed_content text,
ADD COLUMN IF NOT EXISTS original_content text,
ADD COLUMN IF NOT EXISTS compressed_at timestamp with time zone;

-- Add index for compressed chapters
CREATE INDEX IF NOT EXISTS idx_chapters_compressed ON chapters(project_id, is_compressed);

-- Create notifications table if not exists
CREATE TABLE IF NOT EXISTS notifications (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type text NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  data jsonb,
  read boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add index for user notifications
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id, read, created_at DESC);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for memory_settings
CREATE TRIGGER update_memory_settings_updated_at BEFORE UPDATE
  ON memory_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();