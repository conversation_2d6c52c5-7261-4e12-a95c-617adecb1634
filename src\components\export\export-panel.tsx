'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Download, 
  FileText, 
  FileType, 
  Book, 
  File,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Sparkles
} from 'lucide-react'
import { QuickExportPresets } from './quick-export-presets'
import { AdvancedExportDialog } from './advanced-export-dialog'
import { UnifiedExport } from './unified-export'
import { useToast } from '@/components/ui/use-toast'

interface ExportPanelProps {
  projectId: string
  projectTitle: string
  projectName?: string
  chapters?: Array<{
    id: string
    chapter_number: number
    title?: string
    word_count?: number
    status?: 'draft' | 'completed'
  }>
  tier?: string
  lastExport?: {
    date: string
    format: string
    size: number
  }
}

interface ExportHistory {
  id: string
  date: string
  format: string
  size: number
  status: 'success' | 'failed'
}

const formatDetails = {
  txt: {
    name: 'Plain Text',
    icon: FileText,
    description: 'Simple text format, universal compatibility',
    pros: ['Works everywhere', 'Small file size', 'Easy to edit'],
    cons: ['No formatting', 'No images'],
    bestFor: 'Backups, simple sharing'
  },
  markdown: {
    name: 'Markdown',
    icon: FileType,
    description: 'Lightweight markup format',
    pros: ['Preserves basic formatting', 'Version control friendly', 'Convertible'],
    cons: ['Limited styling', 'Not for final output'],
    bestFor: 'Technical documentation, GitHub'
  },
  docx: {
    name: 'Word Document',
    icon: FileType,
    description: 'Microsoft Word format',
    pros: ['Industry standard', 'Full editing', 'Track changes'],
    cons: ['Requires Word/compatible app', 'Can lose formatting'],
    bestFor: 'Manuscript submissions, editing'
  },
  pdf: {
    name: 'PDF',
    icon: File,
    description: 'Portable Document Format',
    pros: ['Preserves formatting', 'Universal viewing', 'Print-ready'],
    cons: ['Not editable', 'Larger file size'],
    bestFor: 'Final drafts, sharing, printing'
  },
  epub: {
    name: 'EPUB',
    icon: Book,
    description: 'E-book format',
    pros: ['Reflowable text', 'E-reader compatible', 'Interactive'],
    cons: ['Complex formatting', 'Device variations'],
    bestFor: 'Digital publishing, e-readers'
  },
}

export function ExportPanel({
  projectId,
  projectTitle,
  projectName,
  chapters = [],
  tier = 'free',
  lastExport
}: ExportPanelProps) {
  const [exportHistory, setExportHistory] = useState<ExportHistory[]>([])
  const { toast } = useToast()

  const completedChapters = chapters.filter(ch => ch.status === 'completed')
  const totalWords = chapters.reduce((sum, ch) => sum + (ch.word_count || 0), 0)
  const avgWordsPerChapter = chapters.length > 0 ? Math.round(totalWords / chapters.length) : 0

  const handleQuickExport = async (format: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ format, includeMetadata: true, includeFrontMatter: true })
      })

      if (!response.ok) throw new Error('Export failed')

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${projectTitle}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      // Add to history
      setExportHistory(prev => [{
        id: Date.now().toString(),
        date: new Date().toISOString(),
        format,
        size: blob.size,
        status: 'success'
      }, ...prev].slice(0, 5))

      toast({
        title: 'Export successful',
        description: `Your project has been exported as ${format.toUpperCase()}.`,
      })
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Please try again later.',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Project Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalWords.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total words</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Chapters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedChapters.length}/{chapters.length}</div>
            <p className="text-xs text-muted-foreground">Completed</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Last Export</CardTitle>
          </CardHeader>
          <CardContent>
            {lastExport ? (
              <>
                <div className="text-2xl font-bold">{lastExport.format.toUpperCase()}</div>
                <p className="text-xs text-muted-foreground">
                  {new Date(lastExport.date).toLocaleDateString()}
                </p>
              </>
            ) : (
              <>
                <div className="text-2xl font-bold">—</div>
                <p className="text-xs text-muted-foreground">No exports yet</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="quick" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="quick">Quick Export</TabsTrigger>
          <TabsTrigger value="presets">Presets</TabsTrigger>
          <TabsTrigger value="formats">Format Guide</TabsTrigger>
        </TabsList>

        <TabsContent value="quick" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quick Export</CardTitle>
              <CardDescription>
                Export your project with one click using optimized settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {Object.entries(formatDetails).map(([format, details]) => {
                  const Icon = details.icon
                  const isRestricted = (format === 'pdf' || format === 'epub') && tier === 'free'
                  
                  return (
                    <Button
                      key={format}
                      variant="outline"
                      className="h-auto flex-col py-3"
                      onClick={() => handleQuickExport(format)}
                      disabled={isRestricted}
                    >
                      <Icon className="h-5 w-5 mb-1" />
                      <span className="text-xs">{details.name}</span>
                      {isRestricted && (
                        <Badge variant="secondary" className="mt-1 text-xs">
                          Pro
                        </Badge>
                      )}
                    </Button>
                  )
                })}
              </div>

              <div className="flex justify-between items-center pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Need more options?
                </div>
                <AdvancedExportDialog
                  projectId={projectId}
                  projectTitle={projectTitle}
                  tier={tier}
                  chapters={chapters}
                />
              </div>
            </CardContent>
          </Card>

          {/* Recent Exports */}
          {exportHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Recent Exports</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {exportHistory.map((item) => (
                    <div key={item.id} className="flex items-center justify-between py-2">
                      <div className="flex items-center gap-3">
                        {item.status === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-success" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-error" />
                        )}
                        <div>
                          <p className="text-sm font-medium">{item.format.toUpperCase()}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(item.date).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {(item.size / 1024).toFixed(1)} KB
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="presets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Export Presets</CardTitle>
              <CardDescription>
                Professional formatting presets for different purposes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <QuickExportPresets
                projectId={projectId}
                projectTitle={projectTitle}
                chapters={chapters}
                variant="cards"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="formats" className="space-y-4">
          <div className="space-y-4">
            {Object.entries(formatDetails).map(([format, details]) => {
              const Icon = details.icon
              return (
                <Card key={format}>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Icon className="h-5 w-5" />
                      <CardTitle className="text-base">{details.name}</CardTitle>
                      <Badge variant="outline">.{format}</Badge>
                    </div>
                    <CardDescription>{details.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                      <div>
                        <h4 className="text-sm font-medium mb-2 text-success dark:text-green-400">
                          Pros
                        </h4>
                        <ul className="text-sm space-y-1">
                          {details.pros.map((pro, i) => (
                            <li key={i} className="flex items-center gap-2">
                              <CheckCircle className="h-3 w-3 text-success" />
                              {pro}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium mb-2 text-orange-700 dark:text-orange-400">
                          Cons
                        </h4>
                        <ul className="text-sm space-y-1">
                          {details.cons.map((con, i) => (
                            <li key={i} className="flex items-center gap-2">
                              <AlertCircle className="h-3 w-3 text-warning" />
                              {con}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    <div className="mt-4 pt-4 border-t">
                      <p className="text-sm">
                        <span className="font-medium">Best for:</span> {details.bestFor}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}