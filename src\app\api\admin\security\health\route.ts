import { NextRequest, NextResponse } from 'next/server';
import { AdminSecurityMiddleware } from '@/lib/api/admin-security-middleware';
import { createAdminClient } from '@/lib/supabase/admin';
import { logger } from '@/lib/services/logger';

export async function GET(request: NextRequest) {
  // Enhanced admin security validation
  const validationResult = await AdminSecurityMiddleware.validateAdminAccess(request, {
    requiredPermission: 'canViewSecurityDashboard',
    maxSessionAgeMinutes: 60,
    rateLimitPerHour: 20
  });
  
  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { user, adminClient } = validationResult;

  // Start admin action context
  const contextId = AdminSecurityMiddleware.startAdminAction(
    user.id,
    'security_health_check',
    'security_metrics',
    undefined,
    { requestType: 'security_dashboard' },
    request
  );

  try {
    const healthData = {
      timestamp: new Date().toISOString(),
      auditedBy: user.email,
      securityScore: 0,
      metrics: {},
      alerts: [],
      recommendations: []
    };

    // 1. Admin Session Health
    const { data: adminSessions, error: sessionError } = await adminClient
      .from('admin_sessions')
      .select('*')
      .eq('revoked', false)
      .gt('expires_at', new Date().toISOString());

    if (!sessionError) {
      const activeSessions = adminSessions?.length || 0;
      const expiringSoon = adminSessions?.filter(s => 
        new Date(s.expires_at).getTime() - Date.now() < 30 * 60 * 1000 // 30 minutes
      ).length || 0;

      healthData.metrics.adminSessions = {
        active: activeSessions,
        expiringSoon,
        status: activeSessions < 10 ? 'healthy' : 'warning'
      };

      if (activeSessions > 15) {
        healthData.alerts.push({
          level: 'warning',
          category: 'session_management',
          message: `High number of active admin sessions: ${activeSessions}`,
          recommendation: 'Review active sessions and revoke unnecessary ones'
        });
      }
    }

    // 2. Recent Admin Activity Analysis
    const { data: recentActivity, error: activityError } = await adminClient
      .from('admin_audit_logs')
      .select('*')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
      .order('created_at', { ascending: false });

    if (!activityError) {
      const totalActions = recentActivity?.length || 0;
      const failedActions = recentActivity?.filter(a => !a.success).length || 0;
      const uniqueIPs = new Set(recentActivity?.map(a => a.ip_address).filter(Boolean)).size;
      const uniqueAdmins = new Set(recentActivity?.map(a => a.admin_user_id)).size;

      healthData.metrics.adminActivity = {
        totalActions24h: totalActions,
        failedActions24h: failedActions,
        uniqueIPs24h: uniqueIPs,
        uniqueAdmins24h: uniqueAdmins,
        failureRate: totalActions > 0 ? (failedActions / totalActions * 100).toFixed(2) : 0,
        status: failedActions > totalActions * 0.1 ? 'warning' : 'healthy'
      };

      if (failedActions > 10) {
        healthData.alerts.push({
          level: 'critical',
          category: 'admin_activity',
          message: `High number of failed admin actions: ${failedActions} in 24h`,
          recommendation: 'Investigate failed admin actions for security issues'
        });
      }

      if (uniqueIPs > 5) {
        healthData.alerts.push({
          level: 'warning',
          category: 'admin_activity',
          message: `Admin access from ${uniqueIPs} different IP addresses`,
          recommendation: 'Review IP access patterns for unusual activity'
        });
      }
    }

    // 3. Suspicious Activity Detection
    const suspiciousActivity = await AdminSecurityMiddleware.detectSuspiciousActivity();
    
    healthData.metrics.suspiciousActivity = {
      count: suspiciousActivity.length,
      patterns: suspiciousActivity.map(activity => ({
        adminUserId: activity.admin_user_id,
        activity: activity.suspicious_activity,
        count: activity.activity_count,
        lastOccurrence: activity.last_occurrence
      })),
      status: suspiciousActivity.length > 0 ? 'critical' : 'healthy'
    };

    if (suspiciousActivity.length > 0) {
      healthData.alerts.push({
        level: 'critical',
        category: 'suspicious_activity',
        message: `${suspiciousActivity.length} suspicious admin activity patterns detected`,
        recommendation: 'Immediately investigate flagged admin accounts'
      });
    }

    // 4. RLS Policy Health Check
    const { data: rlsTables, error: rlsError } = await adminClient
      .rpc('check_rls_status'); // Custom function to check RLS status

    if (!rlsError && rlsTables) {
      const tablesWithoutRLS = rlsTables.filter(t => !t.rls_enabled);
      
      healthData.metrics.dataProtection = {
        totalTables: rlsTables.length,
        rlsEnabled: rlsTables.filter(t => t.rls_enabled).length,
        missingRLS: tablesWithoutRLS.length,
        status: tablesWithoutRLS.length === 0 ? 'healthy' : 'critical'
      };

      if (tablesWithoutRLS.length > 0) {
        healthData.alerts.push({
          level: 'critical',
          category: 'data_protection',
          message: `${tablesWithoutRLS.length} tables without RLS protection`,
          recommendation: 'Enable RLS on all user data tables immediately'
        });
      }
    }

    // 5. Admin Account Security
    const { data: adminProfiles, error: profileError } = await adminClient
      .from('profiles')
      .select('role, mfa_enabled, require_mfa_for_admin, last_admin_action')
      .eq('role', 'admin');

    if (!profileError) {
      const totalAdmins = adminProfiles?.length || 0;
      const mfaEnabled = adminProfiles?.filter(p => p.mfa_enabled).length || 0;
      const recentlyActive = adminProfiles?.filter(p => 
        p.last_admin_action && 
        new Date(p.last_admin_action).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000
      ).length || 0;

      healthData.metrics.adminAccounts = {
        total: totalAdmins,
        mfaEnabled,
        mfaRate: totalAdmins > 0 ? (mfaEnabled / totalAdmins * 100).toFixed(2) : 0,
        recentlyActive,
        status: mfaEnabled === totalAdmins ? 'healthy' : 'warning'
      };

      if (mfaEnabled < totalAdmins) {
        healthData.alerts.push({
          level: 'warning',
          category: 'admin_security',
          message: `${totalAdmins - mfaEnabled} admin accounts without MFA`,
          recommendation: 'Require MFA for all admin accounts'
        });
      }
    }

    // 6. Calculate Overall Security Score
    let securityScore = 100;
    
    // Deduct points for alerts
    healthData.alerts.forEach(alert => {
      if (alert.level === 'critical') securityScore -= 15;
      else if (alert.level === 'warning') securityScore -= 5;
    });

    // Bonus points for good practices
    if (healthData.metrics.adminAccounts?.mfaRate === '100.00') securityScore += 5;
    if (healthData.metrics.dataProtection?.missingRLS === 0) securityScore += 5;
    if (healthData.metrics.suspiciousActivity?.count === 0) securityScore += 5;

    healthData.securityScore = Math.max(0, Math.min(100, securityScore));

    // 7. Generate Recommendations
    if (healthData.securityScore < 70) {
      healthData.recommendations.push('URGENT: Security score below 70% - immediate action required');
    }

    if (healthData.alerts.some(a => a.level === 'critical')) {
      healthData.recommendations.push('Address all critical security alerts immediately');
    }

    healthData.recommendations.push(
      'Regularly rotate admin credentials',
      'Monitor admin activity logs daily',
      'Conduct monthly security reviews',
      'Keep admin session timeouts short',
      'Use IP whitelisting for admin access where possible'
    );

    // Complete admin action successfully
    await AdminSecurityMiddleware.completeAdminAction(
      contextId,
      true,
      undefined,
      {
        securityScore: healthData.securityScore,
        alertCount: healthData.alerts.length,
        metricsCollected: Object.keys(healthData.metrics).length
      }
    );

    return NextResponse.json({
      success: true,
      data: healthData
    });

  } catch (error) {
    // Complete admin action with error
    await AdminSecurityMiddleware.completeAdminAction(
      contextId,
      false,
      error instanceof Error ? error.message : 'Security health check failed'
    );

    logger.error('Security health check failed', {
      error,
      adminId: user.id
    });

    return NextResponse.json(
      { error: 'Security health check failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Helper function to check RLS status (would be implemented as a Supabase function)
export async function createRLSStatusFunction() {
  return `
    CREATE OR REPLACE FUNCTION check_rls_status() 
    RETURNS TABLE(
      table_name TEXT,
      rls_enabled BOOLEAN,
      policy_count BIGINT
    ) AS $$
    BEGIN
      RETURN QUERY
      SELECT 
        t.tablename::TEXT,
        t.rowsecurity as rls_enabled,
        COALESCE(p.policy_count, 0) as policy_count
      FROM pg_tables t
      LEFT JOIN (
        SELECT tablename, COUNT(*) as policy_count
        FROM pg_policies 
        WHERE schemaname = 'public'
        GROUP BY tablename
      ) p ON t.tablename = p.tablename
      WHERE t.schemaname = 'public'
      ORDER BY t.tablename;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
  `;
}