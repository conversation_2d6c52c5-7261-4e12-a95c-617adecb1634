import { BaseService } from './base-service';
import { AnalyticsEvent, ServiceResponse } from './types';
import { SupabaseClient } from '@supabase/supabase-js';

interface WritingStats {
  totalWordsWritten: number;
  sessionsCount: number;
  avgSessionDuration: number;
  productiveDays: number;
  streakDays: number;
}

interface BehaviorPatterns {
  peakProductivityHours: number[];
  preferredWritingModes: string[];
  frequentActions: { action: string; count: number; frequency: number }[];
}

interface ProgressMetrics {
  chaptersCompleted: number;
  averageChapterLength: number;
  writingVelocity: number;
  goalCompletion: number;
}

interface ProjectOverview {
  totalWords: number;
  chaptersCount: number;
  timeSpent: number;
  lastActivity: number;
}

interface Recommendation {
  type: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
}

interface ProgressTrend {
  date: string;
  words: number;
  time: number;
}

interface CollaboratorActivity {
  userId: string;
  contribution: number;
  lastActive: number;
}

interface ContentQuality {
  readabilityScore: number;
  consistencyScore: number;
  engagementMetrics: number;
}

interface ProductivitySummary {
  totalWords: number;
  totalSessions: number;
  avgSessionLength: number;
  streak: number;
}

interface ProductivityTrends {
  wordsByDay: { date: string; words: number }[];
  sessionsByDay: { date: string; sessions: number }[];
  productivityScore: number;
}

interface ProductivityInsights {
  bestPerformingDays: string[];
  mostProductiveHours: number[];
  improvementAreas: string[];
}

export class AnalyticsEngine extends BaseService {
  private eventQueue: AnalyticsEvent[] = [];
  private sessionData: Map<string, { userId: string; startTime: number; events: AnalyticsEvent[] }> = new Map();
  private userBehaviorProfiles: Map<string, BehaviorPatterns> = new Map();
  private supabase: SupabaseClient;

  constructor(supabase: SupabaseClient) {
    super({
      name: 'analytics-engine',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/analytics/events', '/api/analytics/insights'],
      dependencies: [],
      healthCheck: '/api/analytics/health'
    });
    this.supabase = supabase;
  }

  async initialize(): Promise<void> {
    try {
      this.startEventProcessor();
      this.startBehaviorAnalysis();
      this.isInitialized = true;
      this.setStatus('active');
    } catch (error) {
      logger.error('Error initializing analytics engine:', error);
      this.setStatus('error');
      throw error;
    }
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    try {
      return this.createResponse(true, {
        status: `${this.eventQueue.length} events queued, ${this.sessionData.size} active sessions`,
        uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
      });
    } catch (error) {
      logger.error('Error performing analytics health check:', error);
      return this.createResponse(false, null, 'Health check failed');
    }
  }

  async shutdown(): Promise<void> {
    try {
      this.eventQueue = [];
      this.sessionData.clear();
      this.setStatus('inactive');
    } catch (error) {
      logger.error('Error shutting down analytics engine:', error);
      // Still try to set status to inactive
      this.setStatus('inactive');
    }
  }

  async trackEvent(event: Omit<AnalyticsEvent, 'id' | 'timestamp'>): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const fullEvent: AnalyticsEvent = {
        ...event,
        id: `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now()
      };

      this.eventQueue.push(fullEvent);

      // Add to session
      let session = this.sessionData.get(event.sessionId);
      if (!session) {
        session = {
          userId: event.userId,
          startTime: Date.now(),
          events: []
        };
        this.sessionData.set(event.sessionId, session);
      }
      session.events.push(fullEvent);

      return fullEvent.id;
    });
  }

  async getUserInsights(userId: string): Promise<ServiceResponse<{
    writingStats: {
      totalWordsWritten: number;
      sessionsCount: number;
      avgSessionDuration: number;
      productiveDays: number;
      streakDays: number;
    };
    behaviorPatterns: {
      peakProductivityHours: number[];
      preferredWritingModes: string[];
      frequentActions: { action: string; count: number }[];
    };
    progressMetrics: {
      chaptersCompleted: number;
      averageChapterLength: number;
      writingVelocity: number; // words per day
      goalCompletion: number; // percentage
    };
    recommendations: {
      type: string;
      message: string;
      priority: 'low' | 'medium' | 'high';
    }[];
  }>> {
    return this.withErrorHandling(async () => {
      const userEvents = this.getAllUserEvents(userId);
      const writingStats = this.calculateWritingStats(userEvents);
      const behaviorPatterns = this.analyzeBehaviorPatterns(userEvents);
      const progressMetrics = this.calculateProgressMetrics(userEvents);
      const recommendations = this.generateRecommendations(writingStats, behaviorPatterns, progressMetrics);

      return {
        writingStats,
        behaviorPatterns,
        progressMetrics,
        recommendations
      };
    });
  }

  async getProjectAnalytics(projectId: string): Promise<ServiceResponse<{
    overview: {
      totalWords: number;
      chaptersCount: number;
      timeSpent: number;
      lastActivity: number;
    };
    progressTrend: { date: string; words: number; time: number }[];
    collaboratorActivity: { userId: string; contribution: number; lastActive: number }[];
    contentQuality: {
      readabilityScore: number;
      consistencyScore: number;
      engagementMetrics: number;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const projectEvents = this.getProjectEvents(projectId);
      
      const overview = this.calculateProjectOverview(projectEvents);
      const progressTrend = this.calculateProgressTrend(projectEvents);
      const collaboratorActivity = this.analyzeCollaboratorActivity(projectEvents);
      const contentQuality = await this.assessContentQuality(projectEvents);

      return {
        overview,
        progressTrend,
        collaboratorActivity,
        contentQuality
      };
    });
  }

  async getWritingHeatmap(userId: string, days = 30): Promise<ServiceResponse<{
    heatmapData: { date: string; value: number; words: number }[];
    summary: {
      activeDays: number;
      totalWords: number;
      avgDaily: number;
      bestDay: { date: string; words: number };
    };
  }>> {
    return this.withErrorHandling(async () => {
      const cutoffDate = Date.now() - (days * 24 * 60 * 60 * 1000);
      const userEvents = this.getAllUserEvents(userId)
        .filter(event => event.timestamp >= cutoffDate && event.type === 'progress');

      const dailyData = new Map<string, { words: number; sessions: number }>();
      
      userEvents.forEach(event => {
        const dateStr = new Date(event.timestamp).toISOString().split('T')[0];
        if (!dateStr) return;
        
        const words = (event.data.wordsAdded as number) || 0;
        
        if (!dailyData.has(dateStr)) {
          dailyData.set(dateStr, { words: 0, sessions: 0 });
        }
        
        const dayData = dailyData.get(dateStr);
        if (dayData) {
          dayData.words += words;
          dayData.sessions += 1;
        }
      });

      const heatmapData = Array.from(dailyData.entries()).map(([date, data]) => ({
        date,
        value: Math.min(data.words / 100, 10), // Normalize to 0-10 scale
        words: data.words
      }));

      // Fill in missing days with 0 values
      for (let i = 0; i < days; i++) {
        const dateStr = new Date(Date.now() - (i * 24 * 60 * 60 * 1000))
          .toISOString().split('T')[0];
        if (dateStr && !heatmapData.find(d => d.date === dateStr)) {
          heatmapData.push({ date: dateStr, value: 0, words: 0 });
        }
      }

      heatmapData.sort((a, b) => a.date.localeCompare(b.date));

      const activeDays = heatmapData.filter(d => d.words > 0).length;
      const totalWords = heatmapData.reduce((sum, d) => sum + d.words, 0);
      const avgDaily = activeDays > 0 ? totalWords / activeDays : 0;
      const bestDay = heatmapData.reduce((best, current) => 
        current.words > best.words ? current : best, { date: '', words: 0 });

      return {
        heatmapData,
        summary: {
          activeDays,
          totalWords,
          avgDaily: Math.round(avgDaily),
          bestDay
        }
      };
    });
  }

  async trackUserBehavior(userId: string, sessionId: string, action: string, data: Record<string, unknown>): Promise<ServiceResponse<void>> {
    return this.withErrorHandling(async () => {
      await this.trackEvent({
        userId,
        sessionId,
        type: 'behavior',
        event: action,
        data,
        projectId: (data.projectId as string) || ''
      });
    });
  }

  async generateProductivityReport(userId: string, timeframe: 'week' | 'month' | 'quarter'): Promise<ServiceResponse<{
    summary: {
      totalWords: number;
      totalSessions: number;
      avgSessionLength: number;
      streak: number;
    };
    trends: {
      wordsByDay: { date: string; words: number }[];
      sessionsByDay: { date: string; sessions: number }[];
      productivityScore: number;
    };
    insights: {
      bestPerformingDays: string[];
      mostProductiveHours: number[];
      improvementAreas: string[];
    };
  }>> {
    return this.withErrorHandling(async () => {
      const days = timeframe === 'week' ? 7 : timeframe === 'month' ? 30 : 90;
      const cutoffDate = Date.now() - (days * 24 * 60 * 60 * 1000);
      
      const userEvents = this.getAllUserEvents(userId)
        .filter(event => event.timestamp >= cutoffDate);

      const summary = this.calculateProductivitySummary(userEvents);
      const trends = this.calculateProductivityTrends(userEvents, days);
      const insights = this.generateProductivityInsights(userEvents);

      return {
        summary,
        trends,
        insights
      };
    });
  }

  private getAllUserEvents(userId: string): AnalyticsEvent[] {
    const allEvents: AnalyticsEvent[] = [];
    
    // Get events from queue
    allEvents.push(...this.eventQueue.filter(event => event.userId === userId));
    
    // Get events from active sessions
    Array.from(this.sessionData.values()).forEach(session => {
      if (session.userId === userId) {
        allEvents.push(...session.events);
      }
    });

    return allEvents.sort((a, b) => a.timestamp - b.timestamp);
  }

  private getProjectEvents(projectId: string): AnalyticsEvent[] {
    const allEvents: AnalyticsEvent[] = [];
    
    allEvents.push(...this.eventQueue.filter(event => event.projectId === projectId));
    
    Array.from(this.sessionData.values()).forEach(session => {
      allEvents.push(...session.events.filter(event => event.projectId === projectId));
    });

    return allEvents.sort((a, b) => a.timestamp - b.timestamp);
  }

  private calculateWritingStats(events: AnalyticsEvent[]): WritingStats {
    const progressEvents = events.filter(e => e.type === 'progress');
    const sessions = new Set(events.map(e => e.sessionId));
    
    const totalWords = progressEvents.reduce((sum, event) => 
      sum + ((event.data.wordsAdded as number) || 0), 0);
    
    const sessionDurations = Array.from(sessions).map(sessionId => {
      const sessionEvents = events.filter(e => e.sessionId === sessionId);
      if (sessionEvents.length < 2) return 0;
      const firstEvent = sessionEvents[0];
      const lastEvent = sessionEvents[sessionEvents.length - 1];
      if (!firstEvent || !lastEvent) return 0;
      return lastEvent.timestamp - firstEvent.timestamp;
    });
    
    const avgSessionDuration = sessionDurations.length > 0 
      ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length 
      : 0;

    const writingDays = new Set(
      progressEvents.map(e => new Date(e.timestamp).toDateString())
    );

    return {
      totalWordsWritten: totalWords,
      sessionsCount: sessions.size,
      avgSessionDuration: Math.round(avgSessionDuration / 60000), // Convert to minutes
      productiveDays: writingDays.size,
      streakDays: this.calculateStreak(events)
    };
  }

  private analyzeBehaviorPatterns(events: AnalyticsEvent[]): BehaviorPatterns {
    const hourCounts = new Array(24).fill(0);
    const actionCounts = new Map<string, number>();
    const modeCounts = new Map<string, number>();

    events.forEach(event => {
      const hour = new Date(event.timestamp).getHours();
      hourCounts[hour]++;
      
      const action = event.event;
      actionCounts.set(action, (actionCounts.get(action) || 0) + 1);
      
      if (event.data.mode) {
        const mode = event.data.mode as string;
        modeCounts.set(mode, (modeCounts.get(mode) || 0) + 1);
      }
    });

    const peakHours = hourCounts
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour);

    const totalActions = Array.from(actionCounts.values()).reduce((sum, count) => sum + count, 0);
    const frequentActions = Array.from(actionCounts.entries())
      .map(([action, count]) => ({ action, count, frequency: count / Math.max(totalActions, 1) }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const preferredModes = Array.from(modeCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([mode]) => mode);

    return {
      peakProductivityHours: peakHours,
      preferredWritingModes: preferredModes,
      frequentActions
    };
  }

  private calculateProgressMetrics(events: AnalyticsEvent[]): ProgressMetrics {
    const progressEvents = events.filter(e => e.type === 'progress');
    const chapterEvents = events.filter(e => e.event === 'chapter_completed');
    
    const totalWords = progressEvents.reduce((sum, event) => 
      sum + ((event.data.wordsAdded as number) || 0), 0);
    
    const chaptersCompleted = chapterEvents.length;
    const averageChapterLength = chaptersCompleted > 0 ? totalWords / chaptersCompleted : 0;
    
    const firstEvent = events[0];
    const lastEvent = events[events.length - 1];
    const daysDiff = firstEvent && lastEvent 
      ? Math.max(1, (lastEvent.timestamp - firstEvent.timestamp) / (1000 * 60 * 60 * 24))
      : 1;
    
    const writingVelocity = totalWords / daysDiff;

    return {
      chaptersCompleted,
      averageChapterLength: Math.round(averageChapterLength),
      writingVelocity: Math.round(writingVelocity),
      goalCompletion: 75 // Would calculate based on actual goals
    };
  }

  private generateRecommendations(writingStats: WritingStats, behaviorPatterns: BehaviorPatterns, progressMetrics: ProgressMetrics): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (writingStats.avgSessionDuration < 30) {
      recommendations.push({
        type: 'session_length',
        message: 'Consider longer writing sessions to improve flow and productivity',
        priority: 'medium'
      });
    }

    if (progressMetrics.writingVelocity < 100) {
      recommendations.push({
        type: 'velocity',
        message: 'Set daily word count goals to increase writing consistency',
        priority: 'high'
      });
    }

    if (behaviorPatterns.peakProductivityHours.length > 0) {
      recommendations.push({
        type: 'timing',
        message: `Your peak hours are ${behaviorPatterns.peakProductivityHours.join(', ')}. Schedule writing during these times.`,
        priority: 'low'
      });
    }

    return recommendations;
  }

  private calculateStreak(events: AnalyticsEvent[]): number {
    const progressEvents = events.filter(e => e.type === 'progress');
    const writingDays = new Set(
      progressEvents.map(e => new Date(e.timestamp).toDateString())
    );
    
    const sortedDays = Array.from(writingDays).sort();
    let currentStreak = 0;
    
    for (let i = sortedDays.length - 1; i >= 0; i--) {
      const dayString = sortedDays[i];
      if (!dayString) continue;
      const currentDate = new Date(dayString);
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() - (sortedDays.length - 1 - i));
      
      if (currentDate.toDateString() === expectedDate.toDateString()) {
        currentStreak++;
      } else {
        break;
      }
    }
    
    return currentStreak;
  }

  private calculateProjectOverview(events: AnalyticsEvent[]): ProjectOverview {
    const progressEvents = events.filter(e => e.type === 'progress');
    const totalWords = progressEvents.reduce((sum, event) => 
      sum + ((event.data.wordsAdded as number) || 0), 0);
    
    const chapters = new Set(events.map(e => e.data.chapterNumber).filter(Boolean));
    const timeSpent = this.calculateTotalTimeSpent(events);
    const lastActivity = events.length > 0 ? events[events.length - 1]?.timestamp || 0 : 0;

    return {
      totalWords,
      chaptersCount: chapters.size,
      timeSpent,
      lastActivity
    };
  }

  private calculateProgressTrend(events: AnalyticsEvent[]): ProgressTrend[] {
    const dailyData = new Map<string, { words: number; time: number }>();
    
    events.forEach(event => {
      const date = new Date(event.timestamp).toISOString().split('T')[0];
      if (!date) return; // Skip if date parsing fails

      const words = (event.data.wordsAdded as number) || 0;
      const time = (event.data.timeSpent as number) || 0;

      if (!dailyData.has(date)) {
        dailyData.set(date, { words: 0, time: 0 });
      }

      const dayData = dailyData.get(date);
      if (dayData) {
        dayData.words += words;
        dayData.time += time;
      }
    });

    return Array.from(dailyData.entries())
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  private analyzeCollaboratorActivity(events: AnalyticsEvent[]): CollaboratorActivity[] {
    const userContributions = new Map<string, { contribution: number; lastActive: number }>();
    
    events.forEach(event => {
      const userId = event.userId;
      const contribution = (event.data.wordsAdded as number) || 1;
      
      if (!userContributions.has(userId)) {
        userContributions.set(userId, { contribution: 0, lastActive: 0 });
      }
      
      const userData = userContributions.get(userId);
      if (userData) {
        userData.contribution += contribution;
        userData.lastActive = Math.max(userData.lastActive, event.timestamp);
      }
    });

    return Array.from(userContributions.entries())
      .map(([userId, data]) => ({ userId, ...data }));
  }

  private async assessContentQuality(events: AnalyticsEvent[]): Promise<ContentQuality> {
    // Get project ID from events
    const projectIds = new Set(events.map(e => e.projectId).filter(Boolean));
    
    if (projectIds.size === 0) {
      return {
        readabilityScore: 0,
        consistencyScore: 0,
        engagementMetrics: 0
      };
    }
    
    // For simplicity, use the first project ID
    const projectId = Array.from(projectIds)[0];
    
    // Fetch quality metrics from database
    const { data: qualityData } = await this.supabase
      .from('project_quality_metrics')
      .select('avg_readability, avg_coherence, avg_emotional_impact')
      .eq('project_id', projectId)
      .single();
    
    if (!qualityData) {
      return {
        readabilityScore: 0,
        consistencyScore: 0,
        engagementMetrics: 0
      };
    }
    
    return {
      readabilityScore: Math.round(qualityData.avg_readability || 0),
      consistencyScore: Math.round(qualityData.avg_coherence || 0),
      engagementMetrics: Math.round(qualityData.avg_emotional_impact || 0)
    };
  }

  private calculateTotalTimeSpent(events: AnalyticsEvent[]): number {
    const sessions = new Map<string, { start: number; end: number }>();
    
    events.forEach(event => {
      const sessionId = event.sessionId;
      if (!sessions.has(sessionId)) {
        sessions.set(sessionId, { start: event.timestamp, end: event.timestamp });
      } else {
        const session = sessions.get(sessionId);
        if (session) {
          session.end = Math.max(session.end, event.timestamp);
        }
      }
    });

    return Array.from(sessions.values())
      .reduce((total, session) => total + (session.end - session.start), 0);
  }

  private calculateProductivitySummary(events: AnalyticsEvent[]): ProductivitySummary {
    const progressEvents = events.filter(e => e.type === 'progress');
    const sessions = new Set(events.map(e => e.sessionId));
    
    return {
      totalWords: progressEvents.reduce((sum, e) => sum + ((e.data.wordsAdded as number) || 0), 0),
      totalSessions: sessions.size,
      avgSessionLength: 45, // Would calculate properly
      streak: this.calculateStreak(events)
    };
  }

  private calculateProductivityTrends(_events: AnalyticsEvent[], _days: number): ProductivityTrends {
    // Implementation for trend calculation
    return {
      wordsByDay: [],
      sessionsByDay: [],
      productivityScore: 78
    };
  }

  private generateProductivityInsights(_events: AnalyticsEvent[]): ProductivityInsights {
    return {
      bestPerformingDays: ['Monday', 'Wednesday'],
      mostProductiveHours: [9, 14, 20],
      improvementAreas: ['Consistency', 'Session length']
    };
  }

  private startEventProcessor(): void {
    setInterval(() => {
      if (this.eventQueue.length > 1000) {
        // Process and archive old events
        this.eventQueue.splice(0, 500);
      }
    }, 60000);
  }

  private startBehaviorAnalysis(): void {
    setInterval(() => {
      // Analyze user behavior patterns periodically
      Array.from(this.userBehaviorProfiles.entries()).forEach(([userId]) => {
        const events = this.getAllUserEvents(userId);
        if (events.length > 0) {
          const patterns = this.analyzeBehaviorPatterns(events);
          this.userBehaviorProfiles.set(userId, patterns);
        }
      });
    }, 300000); // Every 5 minutes
  }

  // New methods for analytics dashboard
  async getOverviewMetrics(userId: string, startDate: Date, endDate: Date, projectId?: string): Promise<ServiceResponse<{
    totalWords: number;
    currentStreak: number;
    avgDailyWords: number;
    activeProjects: number;
  }>> {
    return this.withErrorHandling(async () => {
      const events = this.getAllUserEvents(userId).filter(e => {
        const eventDate = new Date(e.timestamp);
        return eventDate >= startDate && eventDate <= endDate && 
          (!projectId || e.projectId === projectId);
      });

      const stats = this.calculateWritingStats(events);
      const projectIds = new Set(events.map(e => e.projectId).filter(Boolean));

      const dailyWords = this.calculateDailyWordAverage(events, startDate, endDate);

      return {
        totalWords: stats.totalWordsWritten,
        currentStreak: stats.streakDays,
        avgDailyWords: Math.round(dailyWords),
        activeProjects: projectIds.size
      };
    });
  }

  async getActivityData(userId: string, startDate: Date, endDate: Date, projectId?: string): Promise<ServiceResponse<{
    dailyWordCount: Array<{ date: string; value: number }>;
    heatmapData: Array<{ date: string; count: number }>;
  }>> {
    return this.withErrorHandling(async () => {
      const events = this.getAllUserEvents(userId).filter(e => {
        const eventDate = new Date(e.timestamp);
        return eventDate >= startDate && eventDate <= endDate && 
          (!projectId || e.projectId === projectId) &&
          e.type === 'progress';
      });

      const dailyData = new Map<string, number>();
      
      events.forEach(event => {
        const dateStr = new Date(event.timestamp).toISOString().split('T')[0];
        if (!dateStr) return;
        
        const words = (event.data.wordsAdded as number) || 0;
        dailyData.set(dateStr, (dailyData.get(dateStr) || 0) + words);
      });

      // Fill in missing days
      const currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        if (dateStr && !dailyData.has(dateStr)) {
          dailyData.set(dateStr, 0);
        }
        currentDate.setDate(currentDate.getDate() + 1);
      }

      const dailyWordCount = Array.from(dailyData.entries())
        .map(([date, value]) => ({ date, value }))
        .sort((a, b) => a.date.localeCompare(b.date));

      const heatmapData = dailyWordCount.map(({ date, value }) => ({
        date,
        count: value
      }));

      return {
        dailyWordCount,
        heatmapData
      };
    });
  }

  async getQualityMetrics(userId: string, projectId?: string): Promise<ServiceResponse<{
    qualityDimensions: Array<{ metric: string; score: number; description: string }>;
  }>> {
    return this.withErrorHandling(async () => {
      // Fetch real quality metrics from database
      let query = this.supabase
        .from('project_quality_metrics')
        .select('*')
        .eq('user_id', userId);
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }
      
      const { data: qualityData, error } = await query.single();
      
      if (error || !qualityData) {
        // Return default values if no data found
        return {
          qualityDimensions: [
            { metric: "Readability", score: 0, description: "Clear and engaging prose" },
            { metric: "Character Depth", score: 0, description: "Well-developed characters" },
            { metric: "Plot Coherence", score: 0, description: "Logical story progression" },
            { metric: "Dialogue", score: 0, description: "Natural conversations" },
            { metric: "Pacing", score: 0, description: "Story rhythm and flow" },
            { metric: "Creativity", score: 0, description: "Originality and imagination" },
            { metric: "Emotional Impact", score: 0, description: "Reader engagement" },
            { metric: "Market Potential", score: 0, description: "Commercial viability" }
          ]
        };
      }
      
      // Map real data to quality dimensions
      return {
        qualityDimensions: [
          { 
            metric: "Readability", 
            score: Math.round(qualityData.avg_readability || 0), 
            description: "Clear and engaging prose" 
          },
          { 
            metric: "Character Depth", 
            score: Math.round(qualityData.avg_character_consistency || 0), 
            description: "Well-developed characters" 
          },
          { 
            metric: "Plot Coherence", 
            score: Math.round(qualityData.avg_plot_consistency || 0), 
            description: "Logical story progression" 
          },
          { 
            metric: "Dialogue", 
            score: Math.round(qualityData.avg_dialogue_authenticity || 0), 
            description: "Natural conversations" 
          },
          { 
            metric: "Pacing", 
            score: Math.round(qualityData.avg_pacing || 0), 
            description: "Story rhythm and flow" 
          },
          { 
            metric: "Creativity", 
            score: Math.round(qualityData.avg_creativity || 0), 
            description: "Originality and imagination" 
          },
          { 
            metric: "Emotional Impact", 
            score: Math.round(qualityData.avg_emotional_impact || 0), 
            description: "Reader engagement" 
          },
          { 
            metric: "Market Potential", 
            score: Math.round(qualityData.avg_market_potential || 0), 
            description: "Commercial viability" 
          }
        ]
      };
    });
  }

  async getGoalsData(userId: string, projectId?: string): Promise<ServiceResponse<{
    activeGoals: Array<{
      id: string;
      type: string;
      title: string;
      current: number;
      target: number;
      progress: number;
      unit: string;
      deadline: string;
    }>;
  }>> {
    return this.withErrorHandling(async () => {
      // Fetch actual goals from database
      let query = this.supabase
        .from('writing_goals')
        .select('*')
        .eq('user_id', userId);
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }
      
      const { data: goalsData, error } = await query;
      
      if (error || !goalsData) {
        return { activeGoals: [] };
      }
      
      // Process goals data
      const activeGoals = goalsData
        .filter(goal => goal.status === 'active')
        .map(goal => {
          const progress = goal.target_value > 0 
            ? Math.round((goal.current_value / goal.target_value) * 100)
            : 0;
          
          return {
            id: goal.id,
            type: goal.goal_type,
            title: goal.title,
            current: goal.current_value || 0,
            target: goal.target_value,
            progress,
            unit: goal.unit || 'words',
            deadline: goal.deadline || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          };
        });
      
      return { activeGoals };
    });
  }

  private calculateDailyWordAverage(events: AnalyticsEvent[], startDate: Date, endDate: Date): number {
    const dailyWords = new Map<string, number>();
    
    events.filter(e => e.type === 'progress').forEach(event => {
      const dateStr = new Date(event.timestamp).toISOString().split('T')[0];
      if (!dateStr) return;
      
      const words = (event.data.wordsAdded as number) || 0;
      dailyWords.set(dateStr, (dailyWords.get(dateStr) || 0) + words);
    });

    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const activeDays = dailyWords.size;
    const totalWords = Array.from(dailyWords.values()).reduce((sum, words) => sum + words, 0);

    return activeDays > 0 ? totalWords / activeDays : 0;
  }
}