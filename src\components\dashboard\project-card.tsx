"use client"

import { memo, useMemo } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Calendar, FileText, Users, Target, BookOpen, Edit } from 'lucide-react'
import Link from 'next/link'
import { 
  SPACING, 
  TYPOGRAPHY, 
  STATUS_COLORS, 
  ICON_SIZES, 
  COMPONENTS,
  HOVER,
  ANIMATIONS 
} from '@/lib/config/ui-config'

interface Project {
  id: string
  title: string
  description: string | null
  primary_genre: string | null
  target_word_count: number | null
  current_word_count: number
  status: string
  created_at: string
  updated_at: string
  chapters?: Array<{
    id: string
    chapter_number: number
    title: string | null
    actual_word_count: number
    status: string
  }>
  characters?: Array<{
    id: string
    name: string
    role: string | null
  }>
}

interface ProjectCardProps {
  project: Project
  onEdit?: () => void
  onDelete?: () => void
}

// Using centralized status colors from ui-config
const statusColors = {
  planning: STATUS_COLORS.PROJECT.DRAFT,
  writing: STATUS_COLORS.PROJECT.ACTIVE,
  editing: 'bg-amber-100 text-amber-800 border-amber-200',
  complete: STATUS_COLORS.PROJECT.COMPLETED,
  paused: STATUS_COLORS.PROJECT.ARCHIVED
}

const statusLabels = {
  planning: 'Planning',
  writing: 'Writing',
  editing: 'Editing', 
  complete: 'Complete',
  paused: 'Paused'
}

const ProjectCardComponent = function ProjectCard({ project, onEdit, onDelete }: ProjectCardProps) {
  // Memoize expensive calculations
  const progress = useMemo(() => {
    return project.target_word_count 
      ? Math.min((project.current_word_count / project.target_word_count) * 100, 100)
      : 0
  }, [project.current_word_count, project.target_word_count])

  const chapterStats = useMemo(() => {
    const completedChapters = project.chapters?.filter(ch => ch.status === 'complete').length || 0
    const totalChapters = project.chapters?.length || 0
    return { completedChapters, totalChapters }
  }, [project.chapters])
  
  const formatDate = useMemo(() => (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }, [])

  const getStatusColor = (status: string) => {
    return `${statusColors[status as keyof typeof statusColors] || statusColors.planning} border`
  }

  const getStatusLabel = (status: string) => {
    return statusLabels[status as keyof typeof statusLabels] || 'Planning'
  }

  return (
    <Card className={`group ${SHADOWS.HOVER.SOFT_TO_MEDIUM} ${ANIMATIONS.TRANSITION.ALL} ${HOVER.LIFT} border-warm-200 bg-white/90 ${BACKDROPS.BLUR.SM}`}>
      <CardHeader className={SPACING.PADDING.MD}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className={`${TYPOGRAPHY.PRESETS.CARD_TITLE} truncate text-warm-800`}>
              {project.title}
            </CardTitle>
            <div className={`flex items-center ${SPACING.GAP.SM} mt-2`}>
              <Badge className={getStatusColor(project.status)}>
                {getStatusLabel(project.status)}
              </Badge>
              {project.primary_genre && (
                <Badge variant="outline" className="text-xs border-warm-300 text-warm-700 bg-warm-50">
                  {project.primary_genre}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {project.description && (
          <p className={`${TYPOGRAPHY.PRESETS.BODY_SMALL} text-warm-600 line-clamp-2 mt-3 ${TYPOGRAPHY.FAMILY.LITERARY}`}>
            {project.description}
          </p>
        )}
      </CardHeader>

      <CardContent className={SPACING.SPACE_Y.MD}>
        {/* Progress Section */}
        <div className={SPACING.SPACE_Y.SM}>
          <div className={`flex items-center justify-between ${TYPOGRAPHY.SIZE.SM}`}>
            <span className={`flex items-center ${SPACING.GAP.XS}`}>
              <Target className={ICON_SIZES.SM} />
              Word Count
            </span>
            <span className="font-medium">
              {project.current_word_count.toLocaleString()}
              {project.target_word_count && (
                <span className="text-muted-foreground">
                  /{project.target_word_count.toLocaleString()}
                </span>
              )}
            </span>
          </div>
          
          {project.target_word_count && (
            <Progress value={progress} className="h-2" />
          )}
          
          {progress > 0 && (
            <div className={`${TYPOGRAPHY.SIZE.XS} text-muted-foreground text-right`}>
              {progress.toFixed(1)}% complete
            </div>
          )}
        </div>

        {/* Stats Grid */}
        <div className={`grid ${GRID.COLS[3]} ${SPACING.GAP.MD} ${TYPOGRAPHY.SIZE.SM}`}>
          <div className="text-center">
            <div className={`flex items-center justify-center ${SPACING.GAP.XS} text-muted-foreground mb-1`}>
              <FileText className={ICON_SIZES.SM} />
            </div>
            <div className={TYPOGRAPHY.WEIGHT.MEDIUM}>{chapterStats.completedChapters}/{chapterStats.totalChapters}</div>
            <div className={TYPOGRAPHY.PRESETS.CAPTION}>Chapters</div>
          </div>
          
          <div className="text-center">
            <div className={`flex items-center justify-center ${SPACING.GAP.XS} text-muted-foreground mb-1`}>
              <Users className={ICON_SIZES.SM} />
            </div>
            <div className="font-medium">{project.characters?.length || 0}</div>
            <div className={TYPOGRAPHY.PRESETS.CAPTION}>Characters</div>
          </div>
          
          <div className="text-center">
            <div className={`flex items-center justify-center ${SPACING.GAP.XS} text-muted-foreground mb-1`}>
              <Calendar className={ICON_SIZES.SM} />
            </div>
            <div className={`${TYPOGRAPHY.WEIGHT.MEDIUM} ${TYPOGRAPHY.SIZE.XS}`}>{formatDate(project.updated_at)}</div>
            <div className={TYPOGRAPHY.PRESETS.CAPTION}>Updated</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className={`flex ${SPACING.GAP.SM} pt-2`}>
          <Button asChild className="flex-1" size="sm">
            <Link href={`/projects/${project.id}`} className={`flex items-center ${SPACING.GAP.XS}`}>
              <BookOpen className={ICON_SIZES.BUTTON} />
              Open Project
            </Link>
          </Button>
          
          {project.status !== 'planning' && (
            <Button asChild variant="outline" size="sm">
              <Link href={`/projects/${project.id}/write`} className={`flex items-center ${SPACING.GAP.XS}`}>
                <Edit className={ICON_SIZES.BUTTON} />
                Write
              </Link>
            </Button>
          )}
        </div>
        
        {/* Quick Actions (visible on hover) */}
        {(onEdit || onDelete) && (
          <div className={`opacity-0 group-hover:opacity-100 ${ANIMATIONS.TRANSITION.COLORS} flex ${SPACING.GAP.SM}`}>
            {onEdit && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onEdit} 
                className="flex-1"
                aria-label={`Edit details for ${project.title}`}
              >
                Edit Details
              </Button>
            )}
            {onDelete && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onDelete} 
                className="text-destructive hover:text-destructive"
                aria-label={`Delete project ${project.title}`}
              >
                Delete
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Memoize the component to prevent unnecessary re-renders when project data hasn't changed
export const ProjectCard = memo(ProjectCardComponent)