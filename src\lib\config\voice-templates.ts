// Voice Profile Templates Configuration
// These templates are used as starting points for voice profiles
// In production, these could be stored in a database for easier management

export interface VoiceTemplate {
  id: string
  name: string
  description: string
  category: string
  characteristics: {
    formality: 'very_formal' | 'formal' | 'neutral' | 'casual' | 'very_casual'
    pacing: 'very_slow' | 'slow' | 'moderate' | 'fast' | 'very_fast'
    vocabulary: 'simple' | 'moderate' | 'advanced' | 'technical' | 'literary'
    sentenceStructure: 'simple' | 'moderate' | 'complex' | 'varied'
    tone: string[]
    perspective: string
  }
  examples: {
    sentencePatterns: string[]
    vocabularyExamples: string[]
    styleNotes: string[]
  }
  popularity: number
}

export const voiceTemplates: VoiceTemplate[] = [
  {
    id: 'literary-fiction',
    name: 'Literary Fiction',
    description: 'Rich, introspective prose with emphasis on character depth and thematic exploration',
    category: 'Fiction',
    characteristics: {
      formality: 'formal',
      pacing: 'slow',
      vocabulary: 'literary',
      sentenceStructure: 'complex',
      tone: ['introspective', 'philosophical', 'nuanced'],
      perspective: 'Deep third-person or first-person'
    },
    examples: {
      sentencePatterns: [
        'Complex, multi-clause sentences with embedded meanings',
        'Metaphorical language and symbolism',
        'Stream of consciousness passages'
      ],
      vocabularyExamples: ['ephemeral', 'zeitgeist', 'dichotomy', 'verisimilitude'],
      styleNotes: [
        'Focus on internal conflict over external action',
        'Rich sensory details and emotional landscapes',
        'Subtle character development through observation'
      ]
    },
    popularity: 85
  },
  {
    id: 'thriller-suspense',
    name: 'Thriller/Suspense',
    description: 'Fast-paced, tension-driven narrative with short sentences and cliffhangers',
    category: 'Genre Fiction',
    characteristics: {
      formality: 'neutral',
      pacing: 'fast',
      vocabulary: 'moderate',
      sentenceStructure: 'simple',
      tone: ['urgent', 'tense', 'dramatic'],
      perspective: 'Close third-person or first-person'
    },
    examples: {
      sentencePatterns: [
        'Short, punchy sentences.',
        'Fragments for emphasis.',
        'Cliffhanger chapter endings'
      ],
      vocabularyExamples: ['deadly', 'shadows', 'pulse', 'danger'],
      styleNotes: [
        'Action-oriented descriptions',
        'Minimal exposition during tense scenes',
        'Strong chapter hooks'
      ]
    },
    popularity: 92
  },
  {
    id: 'epic-fantasy',
    name: 'Epic Fantasy',
    description: 'Grand, sweeping narrative with detailed world-building and formal dialogue',
    category: 'Genre Fiction',
    characteristics: {
      formality: 'formal',
      pacing: 'moderate',
      vocabulary: 'advanced',
      sentenceStructure: 'varied',
      tone: ['epic', 'adventurous', 'mystical'],
      perspective: 'Multiple POV third-person'
    },
    examples: {
      sentencePatterns: [
        'Elaborate descriptions of settings and cultures',
        'Formal dialogue with period-appropriate speech',
        'Epic scope with multiple storylines'
      ],
      vocabularyExamples: ['realm', 'prophecy', 'arcane', 'sovereign'],
      styleNotes: [
        'Detailed magic system explanations',
        'Rich history and lore integration',
        'Character titles and formal address'
      ]
    },
    popularity: 88
  },
  {
    id: 'contemporary-romance',
    name: 'Contemporary Romance',
    description: 'Emotionally engaging narrative with focus on relationships and dialogue',
    category: 'Romance',
    characteristics: {
      formality: 'casual',
      pacing: 'moderate',
      vocabulary: 'moderate',
      sentenceStructure: 'moderate',
      tone: ['emotional', 'intimate', 'hopeful'],
      perspective: 'Dual POV or close third-person'
    },
    examples: {
      sentencePatterns: [
        'Dialogue-heavy scenes with emotional subtext',
        'Internal monologue revealing feelings',
        'Sensory details focusing on emotional responses'
      ],
      vocabularyExamples: ['butterflies', 'chemistry', 'vulnerable', 'connection'],
      styleNotes: [
        'Focus on emotional beats',
        'Natural, contemporary dialogue',
        'Building romantic tension'
      ]
    },
    popularity: 90
  },
  {
    id: 'hard-scifi',
    name: 'Hard Science Fiction',
    description: 'Technical, precise prose with scientific accuracy and detailed explanations',
    category: 'Science Fiction',
    characteristics: {
      formality: 'formal',
      pacing: 'moderate',
      vocabulary: 'technical',
      sentenceStructure: 'complex',
      tone: ['analytical', 'precise', 'speculative'],
      perspective: 'Third-person omniscient or limited'
    },
    examples: {
      sentencePatterns: [
        'Technical explanations integrated into narrative',
        'Precise, scientific descriptions',
        'Logical progression of ideas'
      ],
      vocabularyExamples: ['quantum', 'trajectory', 'anomaly', 'hypothesis'],
      styleNotes: [
        'Accurate scientific concepts',
        'Technical dialogue between experts',
        'World-building through technology'
      ]
    },
    popularity: 75
  },
  {
    id: 'cozy-mystery',
    name: 'Cozy Mystery',
    description: 'Light, engaging prose with focus on puzzle-solving and community',
    category: 'Mystery',
    characteristics: {
      formality: 'casual',
      pacing: 'moderate',
      vocabulary: 'simple',
      sentenceStructure: 'simple',
      tone: ['friendly', 'curious', 'lighthearted'],
      perspective: 'First-person or close third-person'
    },
    examples: {
      sentencePatterns: [
        'Conversational, approachable tone',
        'Clue revelation through dialogue',
        'Community-focused descriptions'
      ],
      vocabularyExamples: ['clue', 'suspect', 'alibi', 'motive'],
      styleNotes: [
        'Amateur sleuth perspective',
        'Small-town setting details',
        'Gentle humor and warmth'
      ]
    },
    popularity: 82
  },
  {
    id: 'historical-fiction',
    name: 'Historical Fiction',
    description: 'Period-appropriate language with rich historical detail and atmosphere',
    category: 'Historical',
    characteristics: {
      formality: 'formal',
      pacing: 'slow',
      vocabulary: 'advanced',
      sentenceStructure: 'complex',
      tone: ['atmospheric', 'authentic', 'immersive'],
      perspective: 'Third-person limited or omniscient'
    },
    examples: {
      sentencePatterns: [
        'Period-appropriate dialogue and narration',
        'Detailed historical settings',
        'Cultural and social context woven throughout'
      ],
      vocabularyExamples: ['period-specific terms', 'historical titles', 'archaic phrases'],
      styleNotes: [
        'Historical accuracy in details',
        'Period-appropriate social dynamics',
        'Atmospheric descriptions'
      ]
    },
    popularity: 80
  },
  {
    id: 'ya-contemporary',
    name: 'Young Adult Contemporary',
    description: 'Accessible, emotionally authentic prose with teen perspective',
    category: 'Young Adult',
    characteristics: {
      formality: 'very_casual',
      pacing: 'fast',
      vocabulary: 'simple',
      sentenceStructure: 'simple',
      tone: ['authentic', 'emotional', 'relatable'],
      perspective: 'First-person present or past'
    },
    examples: {
      sentencePatterns: [
        'Short, impactful sentences',
        'Contemporary slang and references',
        'Emotional immediacy'
      ],
      vocabularyExamples: ['literally', 'whatever', 'totally', 'crush'],
      styleNotes: [
        'Authentic teen voice',
        'Social media references',
        'Coming-of-age themes'
      ]
    },
    popularity: 87
  }
]

// Helper function to get templates by category
export function getTemplatesByCategory(category: string): VoiceTemplate[] {
  return voiceTemplates.filter(template => template.category === category)
}

// Helper function to get most popular templates
export function getPopularTemplates(limit: number = 5): VoiceTemplate[] {
  return [...voiceTemplates]
    .sort((a, b) => b.popularity - a.popularity)
    .slice(0, limit)
}

// Helper function to search templates
export function searchTemplates(query: string): VoiceTemplate[] {
  const lowercaseQuery = query.toLowerCase()
  return voiceTemplates.filter(template => 
    template.name.toLowerCase().includes(lowercaseQuery) ||
    template.description.toLowerCase().includes(lowercaseQuery) ||
    template.category.toLowerCase().includes(lowercaseQuery)
  )
}