import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import type { TaskDefinition, TaskResult } from '@/lib/agents/advanced-orchestrator';
import type { ProjectSettings } from '@/lib/types/project-settings';

// Performance benchmarking utilities
interface PerformanceMetrics {
  executionTime: number;
  memoryUsage: number;
  cpuUsage?: number;
  tasksPerSecond: number;
  averageTaskDuration: number;
  peakMemoryUsage: number;
}

class PerformanceBenchmark {
  private startTime: number = 0;
  private startMemory: number = 0;
  private peakMemory: number = 0;
  private taskCount: number = 0;
  private taskDurations: number[] = [];

  start(): void {
    this.startTime = Date.now();
    this.startMemory = process.memoryUsage().heapUsed;
    this.peakMemory = this.startMemory;
  }

  recordTask(duration: number): void {
    this.taskCount++;
    this.taskDurations.push(duration);
    this.updatePeakMemory();
  }

  private updatePeakMemory(): void {
    const currentMemory = process.memoryUsage().heapUsed;
    if (currentMemory > this.peakMemory) {
      this.peakMemory = currentMemory;
    }
  }

  getMetrics(): PerformanceMetrics {
    const executionTime = Date.now() - this.startTime;
    const currentMemory = process.memoryUsage().heapUsed;
    const memoryUsage = currentMemory - this.startMemory;
    
    return {
      executionTime,
      memoryUsage,
      tasksPerSecond: (this.taskCount / executionTime) * 1000,
      averageTaskDuration: this.taskDurations.reduce((a, b) => a + b, 0) / this.taskDurations.length || 0,
      peakMemoryUsage: this.peakMemory - this.startMemory
    };
  }
}

describe('Agent Orchestration Performance Benchmarks', () => {
  let orchestrator: AdvancedAgentOrchestrator;
  
  const mockProjectSettings: ProjectSettings = {
    primaryGenre: 'fantasy',
    secondaryGenres: ['adventure'],
    targetAudience: 'adult',
    writingStyle: 'descriptive',
    narrativeVoice: 'third-person',
    tense: 'past',
    pacing: 'medium',
    violenceLevel: 'moderate',
    romanceLevel: 'low',
    profanityLevel: 'mild',
    themeDepth: 'deep',
    worldBuildingDepth: 'extensive',
    characterComplexity: 'complex',
    plotComplexity: 'complex',
    tone: 'serious',
    dialogueStyle: 'natural',
    descriptionLevel: 'detailed',
    useDeepPOV: true,
    showDontTell: true,
    varyProse: true,
    useSymbolism: true,
    useCliffhangers: true,
    useForeshadowing: true,
    useFlashbacks: false,
    useUnreliableNarrator: false,
    protagonistTypes: ['hero'],
    antagonistTypes: ['villain'],
    supportingRoles: ['mentor', 'sidekick'],
    majorThemes: ['courage', 'friendship'],
    minorThemes: ['sacrifice'],
    culturalElements: [],
    magicSystemType: 'soft',
    technologyLevel: 'medieval',
    politicalSystem: 'monarchy',
    economicSystem: 'feudal',
    geographyType: 'earth-like',
    pacingPreference: 'medium'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Concurrency Performance', () => {
    it('should demonstrate performance improvement with increased concurrency', async () => {
      const taskCount = 50;
      const taskDuration = 100; // ms
      const concurrencyLevels = [1, 3, 5, 10];
      const results: Record<number, PerformanceMetrics> = {};

      for (const concurrency of concurrencyLevels) {
        const benchmark = new PerformanceBenchmark();
        const testOrchestrator = new AdvancedAgentOrchestrator(concurrency);
        
        // Create test tasks
        const tasks: TaskDefinition[] = Array.from({ length: taskCount }, (_, i) => ({
          id: `task-${i}`,
          type: 'content_generation',
          dependencies: [],
          priority: 'medium',
          estimatedDuration: taskDuration / 1000,
          agent: 'test',
          payload: { index: i }
        }));

        // Mock task execution
        testOrchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
          const taskStart = Date.now();
          await new Promise(resolve => setTimeout(resolve, taskDuration));
          const taskEnd = Date.now();
          
          benchmark.recordTask(taskEnd - taskStart);
          
          return {
            taskId: task.id,
            success: true,
            result: { completed: true },
            duration: taskEnd - taskStart,
            agentUsed: 'test'
          };
        });

        benchmark.start();
        await testOrchestrator['executeTaskPipeline'](tasks);
        results[concurrency] = benchmark.getMetrics();
      }

      // Analyze performance scaling
      const singleThreadTime = results[1].executionTime;
      
      // Higher concurrency should reduce execution time
      expect(results[3].executionTime).toBeLessThan(singleThreadTime * 0.4);
      expect(results[5].executionTime).toBeLessThan(singleThreadTime * 0.25);
      expect(results[10].executionTime).toBeLessThan(singleThreadTime * 0.15);
      
      // Tasks per second should increase with concurrency
      expect(results[3].tasksPerSecond).toBeGreaterThan(results[1].tasksPerSecond * 2.5);
      expect(results[5].tasksPerSecond).toBeGreaterThan(results[1].tasksPerSecond * 4);
      
      // Log performance comparison
      console.log('Concurrency Performance Results:');
      for (const [concurrency, metrics] of Object.entries(results)) {
        console.log(`  Concurrency ${concurrency}: ${metrics.executionTime}ms, ${metrics.tasksPerSecond.toFixed(2)} tasks/sec`);
      }
    });

    it('should handle resource contention efficiently', async () => {
      const concurrency = 10;
      const taskCount = 100;
      const resourcePool = new Set(['resource1', 'resource2', 'resource3']);
      const resourceUsage: Map<string, number> = new Map();
      const contentionEvents: Array<{ taskId: string; waitTime: number }> = [];

      orchestrator = new AdvancedAgentOrchestrator(concurrency);

      // Mock tasks that compete for limited resources
      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        const startWait = Date.now();
        let resource: string | undefined;
        
        // Wait for available resource
        while (!resource) {
          for (const res of resourcePool) {
            const usage = resourceUsage.get(res) || 0;
            if (usage < 2) { // Max 2 tasks per resource
              resource = res;
              resourceUsage.set(res, usage + 1);
              break;
            }
          }
          
          if (!resource) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
        
        const waitTime = Date.now() - startWait;
        if (waitTime > 0) {
          contentionEvents.push({ taskId: task.id, waitTime });
        }
        
        // Simulate work
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // Release resource
        const usage = resourceUsage.get(resource)! - 1;
        resourceUsage.set(resource, usage);
        
        return {
          taskId: task.id,
          success: true,
          result: { resource },
          duration: Date.now() - startWait,
          agentUsed: 'test'
        };
      });

      const tasks: TaskDefinition[] = Array.from({ length: taskCount }, (_, i) => ({
        id: `task-${i}`,
        type: 'content_generation',
        dependencies: [],
        priority: 'medium',
        estimatedDuration: 0.05,
        agent: 'test',
        payload: {}
      }));

      const startTime = Date.now();
      await orchestrator['executeTaskPipeline'](tasks);
      const totalTime = Date.now() - startTime;

      // Analyze contention
      const avgWaitTime = contentionEvents.reduce((sum, e) => sum + e.waitTime, 0) / contentionEvents.length || 0;
      const maxWaitTime = Math.max(...contentionEvents.map(e => e.waitTime), 0);
      
      // Performance expectations
      expect(totalTime).toBeLessThan(taskCount * 50 / resourcePool.size + 1000); // Allow overhead
      expect(avgWaitTime).toBeLessThan(100); // Average wait should be reasonable
      expect(maxWaitTime).toBeLessThan(500); // No excessive waiting
    });
  });

  describe('Memory Performance', () => {
    it('should maintain stable memory usage with large task counts', async () => {
      const taskCounts = [100, 500, 1000];
      const memorySnapshots: Array<{ taskCount: number; metrics: PerformanceMetrics }> = [];

      for (const count of taskCounts) {
        // Force garbage collection before test
        if (global.gc) {
          global.gc();
        }

        const benchmark = new PerformanceBenchmark();
        const testOrchestrator = new AdvancedAgentOrchestrator(5);

        // Create lightweight tasks
        const tasks: TaskDefinition[] = Array.from({ length: count }, (_, i) => ({
          id: `task-${i}`,
          type: 'content_generation',
          dependencies: i > 0 && i % 10 === 0 ? [`task-${i - 1}`] : [],
          priority: 'medium',
          estimatedDuration: 0.01,
          agent: 'test',
          payload: { index: i }
        }));

        testOrchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
          // Minimal processing
          await new Promise(resolve => setTimeout(resolve, 10));
          benchmark.recordTask(10);
          
          return {
            taskId: task.id,
            success: true,
            result: { index: task.payload.index },
            duration: 10,
            agentUsed: 'test'
          };
        });

        benchmark.start();
        await testOrchestrator['executeTaskPipeline'](tasks);
        
        memorySnapshots.push({
          taskCount: count,
          metrics: benchmark.getMetrics()
        });

        // Clean up
        testOrchestrator.cancelOrchestration();
      }

      // Analyze memory scaling
      const memoryGrowthRates = [];
      for (let i = 1; i < memorySnapshots.length; i++) {
        const prev = memorySnapshots[i - 1];
        const curr = memorySnapshots[i];
        const taskIncrease = curr.taskCount / prev.taskCount;
        const memoryIncrease = curr.metrics.peakMemoryUsage / prev.metrics.peakMemoryUsage;
        memoryGrowthRates.push(memoryIncrease / taskIncrease);
      }

      // Memory growth should be sub-linear
      for (const rate of memoryGrowthRates) {
        expect(rate).toBeLessThan(0.8); // Less than linear growth
      }

      // Log memory usage
      console.log('Memory Performance Results:');
      for (const snapshot of memorySnapshots) {
        console.log(`  ${snapshot.taskCount} tasks: ${(snapshot.metrics.peakMemoryUsage / 1024 / 1024).toFixed(2)}MB peak`);
      }
    });

    it('should efficiently handle memory-intensive tasks', async () => {
      const orchestrator = new AdvancedAgentOrchestrator(3);
      const largeDataSize = 10 * 1024 * 1024; // 10MB per task
      const taskCount = 20;
      const memoryCheckpoints: number[] = [];

      // Monitor memory during execution
      const memoryMonitor = setInterval(() => {
        memoryCheckpoints.push(process.memoryUsage().heapUsed);
      }, 100);

      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        // Allocate large data
        const largeData = Buffer.alloc(largeDataSize);
        
        // Process data
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // Return minimal result (not the large data)
        return {
          taskId: task.id,
          success: true,
          result: { 
            processed: true,
            dataSize: largeData.length,
            summary: 'Processed successfully'
          },
          duration: 50,
          agentUsed: 'test'
        };
      });

      const tasks: TaskDefinition[] = Array.from({ length: taskCount }, (_, i) => ({
        id: `memory-task-${i}`,
        type: 'content_generation',
        dependencies: [],
        priority: 'medium',
        estimatedDuration: 0.05,
        agent: 'test',
        payload: {}
      }));

      const startMemory = process.memoryUsage().heapUsed;
      await orchestrator['executeTaskPipeline'](tasks);
      const endMemory = process.memoryUsage().heapUsed;

      clearInterval(memoryMonitor);

      // Analyze memory pattern
      const peakMemory = Math.max(...memoryCheckpoints);
      const avgMemory = memoryCheckpoints.reduce((a, b) => a + b) / memoryCheckpoints.length;
      
      // Memory should not accumulate (efficient garbage collection)
      const expectedPeakMemory = startMemory + (largeDataSize * 3 * 2); // 3 concurrent * 2x overhead
      expect(peakMemory).toBeLessThan(expectedPeakMemory);
      
      // End memory should be close to start (no leaks)
      const memoryGrowth = endMemory - startMemory;
      expect(memoryGrowth).toBeLessThan(largeDataSize); // Less than one task's data
    });
  });

  describe('Task Pipeline Performance', () => {
    it('should optimize task execution order for maximum throughput', async () => {
      const orchestrator = new AdvancedAgentOrchestrator(5);
      const executionOrder: string[] = [];
      const taskCompletionTimes: Map<string, number> = new Map();

      // Create tasks with varying durations and dependencies
      const taskConfigs = [
        { id: 'fast-1', duration: 10, deps: [] },
        { id: 'fast-2', duration: 10, deps: [] },
        { id: 'slow-1', duration: 100, deps: [] },
        { id: 'slow-2', duration: 100, deps: [] },
        { id: 'dependent-1', duration: 50, deps: ['fast-1'] },
        { id: 'dependent-2', duration: 50, deps: ['fast-2'] },
        { id: 'final', duration: 20, deps: ['dependent-1', 'dependent-2'] }
      ];

      const tasks: TaskDefinition[] = taskConfigs.map(config => ({
        id: config.id,
        type: 'content_generation',
        dependencies: config.deps,
        priority: config.deps.length > 0 ? 'low' : 'high',
        estimatedDuration: config.duration / 1000,
        agent: 'test',
        payload: { duration: config.duration }
      }));

      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        const startTime = Date.now();
        executionOrder.push(task.id);
        
        await new Promise(resolve => setTimeout(resolve, task.payload.duration as number));
        
        taskCompletionTimes.set(task.id, Date.now());
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration: Date.now() - startTime,
          agentUsed: 'test'
        };
      });

      const pipelineStart = Date.now();
      await orchestrator['executeTaskPipeline'](tasks);
      const pipelineEnd = Date.now();
      const totalTime = pipelineEnd - pipelineStart;

      // Verify optimal execution patterns
      // Fast tasks should complete before slow tasks when possible
      const fastTasksCompletionTime = Math.max(
        taskCompletionTimes.get('fast-1')!,
        taskCompletionTimes.get('fast-2')!
      ) - pipelineStart;
      
      const slowTasksCompletionTime = Math.max(
        taskCompletionTimes.get('slow-1')!,
        taskCompletionTimes.get('slow-2')!
      ) - pipelineStart;

      expect(fastTasksCompletionTime).toBeLessThan(slowTasksCompletionTime);

      // Dependent tasks should start as soon as dependencies complete
      const dependent1StartTime = executionOrder.indexOf('dependent-1');
      const fast1CompleteTime = executionOrder.indexOf('fast-1');
      expect(dependent1StartTime).toBeGreaterThan(fast1CompleteTime);

      // Total time should be close to critical path
      const criticalPath = 10 + 50 + 20; // fast -> dependent -> final
      expect(totalTime).toBeLessThan(criticalPath + 100); // Allow some overhead
    });

    it('should handle complex dependency graphs efficiently', async () => {
      const orchestrator = new AdvancedAgentOrchestrator(10);
      
      // Create a complex DAG with multiple paths
      const layers = [
        // Layer 0: Entry points
        ['entry-1', 'entry-2', 'entry-3'],
        // Layer 1: First processing
        ['process-1-1', 'process-1-2', 'process-1-3', 'process-1-4'],
        // Layer 2: Aggregation
        ['aggregate-2-1', 'aggregate-2-2'],
        // Layer 3: Final processing
        ['final-3-1']
      ];

      const tasks: TaskDefinition[] = [];
      
      // Create tasks with dependencies on previous layer
      for (let layerIdx = 0; layerIdx < layers.length; layerIdx++) {
        const layer = layers[layerIdx];
        const prevLayer = layerIdx > 0 ? layers[layerIdx - 1] : [];
        
        for (const taskId of layer) {
          const dependencies = layerIdx === 0 ? [] : 
            layerIdx === layers.length - 1 ? layers[layerIdx - 1] : 
            prevLayer.slice(0, 2); // Each task depends on first 2 of previous layer
          
          tasks.push({
            id: taskId,
            type: 'content_generation',
            dependencies,
            priority: layerIdx === 0 ? 'critical' : 'medium',
            estimatedDuration: 0.05,
            agent: 'test',
            payload: { layer: layerIdx }
          });
        }
      }

      const layerCompletionTimes: Map<number, number> = new Map();
      const taskStartTimes: Map<string, number> = new Map();

      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        const startTime = Date.now();
        taskStartTimes.set(task.id, startTime);
        
        await new Promise(resolve => setTimeout(resolve, 50));
        
        const layer = task.payload.layer as number;
        const currentLayerTime = layerCompletionTimes.get(layer) || 0;
        layerCompletionTimes.set(layer, Math.max(currentLayerTime, Date.now()));
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration: 50,
          agentUsed: 'test'
        };
      });

      const startTime = Date.now();
      const results = await orchestrator['executeTaskPipeline'](tasks);
      const totalTime = Date.now() - startTime;

      // Verify all tasks completed
      expect(results).toHaveLength(tasks.length);
      expect(results.every(r => r.success)).toBe(true);

      // Verify parallelism within layers
      for (let layerIdx = 0; layerIdx < layers.length - 1; layerIdx++) {
        const layer = layers[layerIdx];
        if (layer.length > 1) {
          const startTimes = layer.map(taskId => taskStartTimes.get(taskId)!);
          const minStart = Math.min(...startTimes);
          const maxStart = Math.max(...startTimes);
          
          // Tasks in same layer should start close together (parallel execution)
          expect(maxStart - minStart).toBeLessThan(100);
        }
      }

      // Total time should be close to critical path length
      const criticalPathLength = layers.length * 50;
      expect(totalTime).toBeLessThan(criticalPathLength + 200);
    });
  });

  describe('Scalability Benchmarks', () => {
    it('should scale linearly with task count for independent tasks', async () => {
      const taskCounts = [10, 50, 100, 200];
      const concurrency = 10;
      const results: Array<{ count: number; time: number; rate: number }> = [];

      for (const count of taskCounts) {
        const orchestrator = new AdvancedAgentOrchestrator(concurrency);
        
        const tasks: TaskDefinition[] = Array.from({ length: count }, (_, i) => ({
          id: `task-${i}`,
          type: 'content_generation',
          dependencies: [],
          priority: 'medium',
          estimatedDuration: 0.01,
          agent: 'test',
          payload: {}
        }));

        orchestrator['executeTask'] = jest.fn().mockImplementation(async () => {
          await new Promise(resolve => setTimeout(resolve, 10));
          return {
            taskId: 'test',
            success: true,
            result: {},
            duration: 10,
            agentUsed: 'test'
          };
        });

        const startTime = Date.now();
        await orchestrator['executeTaskPipeline'](tasks);
        const totalTime = Date.now() - startTime;
        
        results.push({
          count,
          time: totalTime,
          rate: count / (totalTime / 1000)
        });
      }

      // Analyze scaling
      console.log('Scalability Results:');
      for (const result of results) {
        console.log(`  ${result.count} tasks: ${result.time}ms (${result.rate.toFixed(2)} tasks/sec)`);
      }

      // Throughput should remain relatively constant
      const rates = results.map(r => r.rate);
      const avgRate = rates.reduce((a, b) => a + b) / rates.length;
      
      for (const rate of rates) {
        const deviation = Math.abs(rate - avgRate) / avgRate;
        expect(deviation).toBeLessThan(0.2); // Within 20% of average
      }
    });

    it('should handle burst loads efficiently', async () => {
      const orchestrator = new AdvancedAgentOrchestrator(20);
      const normalLoad = 10;
      const burstLoad = 100;
      const metrics: Array<{ phase: string; metrics: PerformanceMetrics }> = [];

      // Phase 1: Normal load
      const normalBenchmark = new PerformanceBenchmark();
      normalBenchmark.start();

      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        const duration = Math.random() * 20 + 10; // 10-30ms
        await new Promise(resolve => setTimeout(resolve, duration));
        normalBenchmark.recordTask(duration);
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration,
          agentUsed: 'test'
        };
      });

      const normalTasks: TaskDefinition[] = Array.from({ length: normalLoad }, (_, i) => ({
        id: `normal-${i}`,
        type: 'content_generation',
        dependencies: [],
        priority: 'medium',
        estimatedDuration: 0.02,
        agent: 'test',
        payload: {}
      }));

      await orchestrator['executeTaskPipeline'](normalTasks);
      metrics.push({ phase: 'normal', metrics: normalBenchmark.getMetrics() });

      // Phase 2: Burst load
      const burstBenchmark = new PerformanceBenchmark();
      burstBenchmark.start();

      orchestrator['executeTask'] = jest.fn().mockImplementation(async (task) => {
        const duration = Math.random() * 20 + 10;
        await new Promise(resolve => setTimeout(resolve, duration));
        burstBenchmark.recordTask(duration);
        
        return {
          taskId: task.id,
          success: true,
          result: {},
          duration,
          agentUsed: 'test'
        };
      });

      const burstTasks: TaskDefinition[] = Array.from({ length: burstLoad }, (_, i) => ({
        id: `burst-${i}`,
        type: 'content_generation',
        dependencies: [],
        priority: i < 20 ? 'critical' : 'medium', // Some high priority
        estimatedDuration: 0.02,
        agent: 'test',
        payload: {}
      }));

      await orchestrator['executeTaskPipeline'](burstTasks);
      metrics.push({ phase: 'burst', metrics: burstBenchmark.getMetrics() });

      // Analyze burst handling
      const normalRate = metrics[0].metrics.tasksPerSecond;
      const burstRate = metrics[1].metrics.tasksPerSecond;
      
      // Burst rate should not degrade too much
      expect(burstRate).toBeGreaterThan(normalRate * 0.7);
      
      // Average task duration should remain stable
      const normalAvgDuration = metrics[0].metrics.averageTaskDuration;
      const burstAvgDuration = metrics[1].metrics.averageTaskDuration;
      expect(Math.abs(burstAvgDuration - normalAvgDuration)).toBeLessThan(10);

      console.log('Burst Load Performance:');
      console.log(`  Normal: ${normalRate.toFixed(2)} tasks/sec`);
      console.log(`  Burst: ${burstRate.toFixed(2)} tasks/sec`);
      console.log(`  Degradation: ${((1 - burstRate/normalRate) * 100).toFixed(2)}%`);
    });
  });
});