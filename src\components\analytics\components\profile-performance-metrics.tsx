'use client'

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Trophy,
  Target,
  Clock,
  TrendingUp,
  Award,
  AlertCircle,
  FileText
} from 'lucide-react'

interface ProfileMetric {
  profileName: string
  projectsCreated: number
  projectsCompleted: number
  averageWordCount: number
  completionRate: number
  averageTime: number // in days
  successScore: number
}

interface ProfilePerformanceMetricsProps {
  userId: string
}

export function ProfilePerformanceMetrics({ userId }: ProfilePerformanceMetricsProps) {
  const [metrics, setMetrics] = useState<ProfileMetric[]>([])
  const [topProfile, setTopProfile] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchProfileMetrics()
  }, [userId])

  const fetchProfileMetrics = async () => {
    try {
      const response = await fetch(`/api/analytics/profiles/performance?userId=${userId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch profile metrics')
      }
      
      const data = await response.json()
      const sortedMetrics = data.sort((a: ProfileMetric, b: ProfileMetric) => b.successScore - a.successScore)
      
      setMetrics(sortedMetrics)
      setTopProfile(sortedMetrics.length > 0 ? sortedMetrics[0].profileName : '')
    } catch (error) {
      logger.error('Failed to fetch profile metrics:', error)
      // Show empty state instead of mock data
      setMetrics([])
      setTopProfile('')
    } finally {
      setLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'text-success'
    if (score >= 70) return 'text-info'
    if (score >= 50) return 'text-warning'
    return 'text-error'
  }

  const formatDays = (days: number) => {
    if (days < 30) return `${days} days`
    if (days < 365) return `${Math.round(days / 30)} months`
    return `${Math.round(days / 365)} years`
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3, 4].map((i) => (
          <Skeleton key={i} className="h-32 w-full" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Top Performer */}
      {topProfile && (
        <Card className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border-orange-200">
          <div className="flex items-center gap-3">
            <Trophy className="h-6 w-6 text-warning" />
            <div>
              <h4 className="font-semibold">Top Performing Profile</h4>
              <p className="text-sm text-muted-foreground">
                "{topProfile}" yields your best results
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Profile Cards */}
      <div className="grid gap-4 sm:gap-5 lg:gap-6">
        {metrics.map((metric, index) => (
          <Card key={metric.profileName} className="p-4">
            <div className="space-y-3">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold">{metric.profileName}</h4>
                  {index === 0 && (
                    <Badge variant="default" className="bg-orange-500">
                      <Award className="h-3 w-3 mr-1" />
                      Top
                    </Badge>
                  )}
                </div>
                <div className={`text-2xl font-bold ${getScoreColor(metric.successScore)}`}>
                  {metric.successScore}
                </div>
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                <div className="space-y-1">
                  <p className="text-muted-foreground flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    Projects
                  </p>
                  <p className="font-medium">
                    {metric.projectsCompleted}/{metric.projectsCreated}
                  </p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-muted-foreground flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    Completion
                  </p>
                  <p className="font-medium">{metric.completionRate}%</p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-muted-foreground flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    Avg Words
                  </p>
                  <p className="font-medium">{metric.averageWordCount.toLocaleString()}</p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-muted-foreground flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Avg Time
                  </p>
                  <p className="font-medium">{formatDays(metric.averageTime)}</p>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Success Score</span>
                  <span>{metric.successScore}%</span>
                </div>
                <Progress value={metric.successScore} className="h-2" />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Tip */}
      <Card className="p-4 bg-info-light border-blue-200">
        <div className="flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-info mt-0.5" />
          <div className="text-sm">
            <p className="font-medium mb-1">Pro Tip</p>
            <p className="text-muted-foreground">
              Focus on your top-performing profiles or experiment with combining 
              successful elements from multiple profiles.
            </p>
          </div>
        </div>
      </Card>
    </div>
  )
}