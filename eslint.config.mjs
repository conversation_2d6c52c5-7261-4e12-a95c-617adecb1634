import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Re-enabled ESLint rules
      "@typescript-eslint/no-explicit-any": "error", // Ban any type usage
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          "argsIgnorePattern": "^_",
          "varsIgnorePattern": "^_",
          "caughtErrorsIgnorePattern": "^_"
        }
      ],
      
      // Configuration enforcement rules
      "no-restricted-syntax": [
        "error",
        {
          selector: "Literal[value=/^bookscribe_/]",
          message: "Use STORAGE_KEYS from '@/lib/config/storage-keys' instead of hardcoded localStorage keys"
        },
        {
          selector: "MemberExpression[object.name='process'][property.name='env']",
          message: "Use config from '@/lib/config' instead of accessing process.env directly"
        },
        {
          selector: "Literal[value=/^(profiles|users|projects|chapters|characters|story_arcs|story_bible|agent_logs|selection_profiles|reference_materials|processing_tasks|usage_tracking|usage_events|writing_sessions|editing_sessions|user_subscriptions|series|character_continuity|voice_profiles|collaborators|collaboration_invites|content_embeddings|export_history|subscription_tiers|achievements|user_achievements|writing_goals|writing_goal_progress|analytics_snapshots|notifications|reviews|feature_flags|templates|universe_profiles|cross_project_references|story_bibles|workflow_steps|knowledge_base_entries|selection_analytics|agent_executions|agent_performance_metrics|kb_search_queries|user_preferences)$/]",
          message: "Use DB_TABLES from '@/lib/config/database-tables' instead of hardcoded table names"
        },
        {
          selector: "Literal[value=/^\\/api\\//]",
          message: "Use API_ENDPOINTS from '@/lib/config/api-endpoints' instead of hardcoded API paths"
        },
        {
          selector: "CallExpression[callee.name='setTimeout'][arguments.0][arguments.1.type='Literal'][arguments.1.value>=300]",
          message: "Use TIMING constants from '@/lib/config/animation-timing' instead of hardcoded delays"
        },
        {
          selector: "BinaryExpression[left.value='1024'][operator='*'][right.left.value='1024']",
          message: "Use FILE_LIMITS from '@/lib/config/file-limits' instead of hardcoded file sizes"
        }
      ],
      
      // Magic number prevention
      "no-magic-numbers": [
        "warn",
        {
          ignore: [0, 1, -1, 2, 10, 100, 1000],
          ignoreArrayIndexes: true,
          enforceConst: true,
          detectObjects: false,
          ignoreDefaultValues: true
        }
      ],
      
      // Enforce consistent imports
      "no-restricted-imports": [
        "error",
        {
          patterns: [
            {
              group: ["../../lib/db/types", "../../../lib/db/types"],
              message: "Import from '@/lib/db/types' instead of relative paths"
            }
          ],
          paths: [
            {
              name: "lucide-react",
              importNames: ["Icon"],
              message: "Import specific icons instead of the generic Icon type"
            }
          ]
        }
      ],
      
      // Enforce API endpoint usage
      "no-restricted-properties": [
        "error",
        {
          object: "fetch",
          message: "Consider using API_ENDPOINTS from '@/lib/config/api-endpoints' for API calls"
        }
      ],
      
      // Custom pattern rules
      "@typescript-eslint/naming-convention": [
        "warn",
        {
          selector: "variable",
          filter: {
            regex: "^(STORAGE_KEY_|LOCAL_STORAGE_|SESSION_STORAGE_)",
            match: true
          },
          format: null,
          custom: {
            regex: ".",
            match: true
          },
          leadingUnderscore: "forbid",
          trailingUnderscore: "forbid",
          prefix: [],
          suffix: [],
          modifiers: [],
          types: [],
          message: "Storage keys should be defined in STORAGE_KEYS configuration"
        }
      ]
    },
  },
];

export default eslintConfig;
