// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

const SENTRY_DSN = process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN;
const isProduction = process.env.NODE_ENV === 'production';

Sentry.init({
  dsn: SENTRY_DSN,
  
  // Performance Monitoring
  tracesSampleRate: isProduction ? 0.1 : 1.0,
  
  // Disable in development unless debugging
  enabled: isProduction || process.env.SENTRY_DEBUG === 'true',
  
  environment: process.env.NODE_ENV,
  
  // Set release
  release: process.env.NEXT_PUBLIC_APP_VERSION,
  
  // Edge-specific configuration
  ignoreErrors: [
    'NEXT_NOT_FOUND',
    'NEXT_REDIRECT',
    'EdgeFunctionError',
  ],
  
  beforeSend(event) {
    // Don't send events in development unless explicitly enabled
    if (!isProduction && process.env.SENTRY_DEBUG !== 'true') {
      return null;
    }
    
    // Remove sensitive headers from edge requests
    if (event.request?.headers) {
      const sensitiveHeaders = [
        'authorization',
        'cookie',
        'x-api-key',
        'cf-ray',
        'x-vercel-id',
      ];
      
      sensitiveHeaders.forEach(header => {
        if (event.request?.headers?.[header]) {
          event.request.headers[header] = '[Filtered]';
        }
      });
    }
    
    return event;
  },
});
