/**
 * Streaming Content Generation API
 * Provides streaming AI content generation using Vercel AI SDK
 */

import { NextRequest } from 'next/server'
import { streamText } from 'ai'
import { openai } from '@ai-sdk/openai'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { withRateLimit } from '@/lib/rate-limiter-unified'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler'
import { AI_MODELS, AI_TEMPERATURE, AI_MAX_TOKENS } from '@/lib/config/ai-settings'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { createTypedServerClient } from '@/lib/supabase'
import { TIME_MS } from '@/lib/constants'

// Request validation schema
const streamContentSchema = z.object({
  prompt: z.string().min(1).max(10000),
  systemPrompt: z.string().optional(),
  model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(4000).optional(),
  estimatedTokens: z.number().min(1).max(4000).optional(),
  contentType: z.enum(['chapter', 'scene', 'dialogue', 'description', 'character', 'general']).optional()
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  const rateLimitResponse = await withRateLimit(request, {
    windowMs: 60 * 60 * TIME_MS.SECOND, // 1 hour
    maxRequests: 20, // 20 requests per hour
    message: 'Too many content generation requests. Please wait before trying again.'
  })
  
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Parse and validate request body
    const body = await request.json()
    const validatedData = streamContentSchema.parse(body)

    const {
      prompt,
      systemPrompt = 'You are an expert creative writing assistant. Generate high-quality, engaging content that maintains consistency with the established narrative.',
      model = AI_MODELS.PRIMARY,
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = AI_MAX_TOKENS.STANDARD,
      contentType = 'general'
    } = validatedData

    // Map our model names to provider models
    const modelMap: Record<string, string> = {
      'gpt-4.1-2025-04-14': 'gpt-4-turbo',
      'gpt-4o-mini': 'gpt-4o-mini',
      'text-embedding-3-small': 'text-embedding-3-small',
      'grok-beta': 'grok-beta',
      'grok-vision-beta': 'grok-vision-beta',
    }

    const mappedModel = modelMap[model] || 'gpt-4-turbo'

    // Enhanced system prompts based on content type
    const contentTypePrompts: Record<string, string> = {
      chapter: `${systemPrompt} Focus on creating a complete chapter with proper pacing, character development, and plot advancement. Include dialogue, action, and description in balanced proportions.`,
      scene: `${systemPrompt} Create a vivid, engaging scene that advances the story. Include sensory details, character interactions, and emotional depth.`,
      dialogue: `${systemPrompt} Write natural, character-appropriate dialogue that reveals personality, advances the plot, and maintains the story's tone.`,
      description: `${systemPrompt} Create immersive, sensory-rich descriptions that paint a clear picture without overwhelming the reader. Use varied sentence structure and vivid imagery.`,
      character: `${systemPrompt} Develop rich, complex characters with clear motivations, realistic flaws, and compelling backstories that serve the narrative.`,
      general: systemPrompt
    }

    const enhancedSystemPrompt = contentTypePrompts[contentType] || systemPrompt

    logger.info(`Streaming content generation started for user ${user.id}`, {
      contentType,
      model: mappedModel,
      promptLength: prompt.length
    })

    // Create streaming response
    const result = await streamText({
      model: openai(mappedModel),
      messages: [
        { role: 'system', content: enhancedSystemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature,
      maxTokens,
      onFinish: async ({ text, finishReason, usage }) => {
        logger.info(`Streaming content generation completed for user ${user.id}`, {
          contentType,
          finishReason,
          tokensUsed: usage?.totalTokens,
          contentLength: text.length
        })
      }
    })

    return result.toAIStreamResponse()

  } catch (error) {
    logger.error('Error in streaming content generation:', error)
    
    if (error instanceof z.ZodError) {
      return handleAPIError(new ValidationError('Invalid request data'))
    }

    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'models':
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              available: Object.keys(AI_MODELS),
              default: AI_MODELS.PRIMARY,
              fast: AI_MODELS.FAST
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      case 'content-types':
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              types: ['chapter', 'scene', 'dialogue', 'description', 'character', 'general'],
              descriptions: {
                chapter: 'Complete chapter with pacing and development',
                scene: 'Vivid scene with sensory details',
                dialogue: 'Natural character conversations',
                description: 'Immersive environmental descriptions',
                character: 'Rich character development',
                general: 'General creative content'
              }
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )

      default:
        return new Response(
          JSON.stringify({
            service: 'streaming-content-generator',
            version: '2.0.0',
            endpoints: [
              'POST /api/ai/stream-content - Stream content generation',
              'GET /api/ai/stream-content?action=models - Get available models',
              'GET /api/ai/stream-content?action=content-types - Get content types'
            ],
            usage: {
              rateLimit: '20 requests per hour',
              authentication: 'Required',
              streaming: true
            }
          }),
          { headers: { 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    logger.error('Error in streaming content GET:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})
