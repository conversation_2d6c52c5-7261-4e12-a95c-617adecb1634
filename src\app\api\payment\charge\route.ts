/**
 * Payment Charge API Route
 * Handles creating charges for one-time payments
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/api/auth-helpers'
import { stripe } from '@/lib/stripe'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'
import <PERSON><PERSON> from 'stripe'
import { z } from 'zod'

// Validation schema for charge request
const chargeRequestSchema = z.object({
  amount: z.number().int().min(50).max(999999), // Amount in cents, min $0.50, max $9,999.99
  currency: z.string().default('usd'),
  source: z.string().min(1), // Stripe token from frontend
  description: z.string().max(500).optional(),
  metadata: z.record(z.string()).optional()
})

export const POST = withAuth(async (request: NextRequest) => {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = chargeRequestSchema.parse(body)

    logger.info('Processing payment charge', {
      userId: user.id,
      amount: validatedData.amount,
      currency: validatedData.currency
    })

    // Get user's Stripe customer ID or create one
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id, email')
      .eq('id', user.id)
      .single()

    let customerId = profile?.stripe_customer_id

    if (!customerId) {
      // Create Stripe customer if doesn't exist
      const customer = await stripe.customers.create({
        email: user.email || profile?.email,
        metadata: {
          user_id: user.id
        }
      })
      
      customerId = customer.id

      // Save customer ID to profile
      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id)
    }

    // Create the charge
    const charge = await stripe.charges.create({
      amount: validatedData.amount,
      currency: validatedData.currency,
      source: validatedData.source,
      customer: customerId,
      description: validatedData.description || 'BookScribe AI Payment',
      metadata: {
        user_id: user.id,
        ...validatedData.metadata
      }
    })

    // Record the payment in our database
    await supabase
      .from('payments')
      .insert({
        user_id: user.id,
        stripe_charge_id: charge.id,
        amount: charge.amount,
        currency: charge.currency,
        status: charge.status,
        description: charge.description,
        metadata: {
          ...validatedData.metadata,
          source_type: charge.source?.object,
          payment_method: charge.payment_method
        }
      })

    logger.info('Payment charge successful', {
      userId: user.id,
      chargeId: charge.id,
      amount: charge.amount
    })

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        id: charge.id,
        amount: charge.amount,
        currency: charge.currency,
        status: charge.status,
        created: charge.created,
        description: charge.description
      }
    })

  } catch (error) {
    logger.error('Payment charge error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors 
        },
        { status: 400 }
      )
    }

    if (error instanceof Stripe.errors.StripeError) {
      // Handle Stripe-specific errors
      switch (error.type) {
        case 'StripeCardError':
          return NextResponse.json(
            { error: error.message },
            { status: 400 }
          )
        case 'StripeRateLimitError':
          return NextResponse.json(
            { error: 'Too many requests, please try again later' },
            { status: 429 }
          )
        case 'StripeInvalidRequestError':
          return NextResponse.json(
            { error: 'Invalid request to payment processor' },
            { status: 400 }
          )
        case 'StripeAPIConnectionError':
        case 'StripeConnectionError':
          return NextResponse.json(
            { error: 'Connection error, please try again' },
            { status: 502 }
          )
        case 'StripeAuthenticationError':
          logger.error('Stripe authentication error - check API keys')
          return NextResponse.json(
            { error: 'Payment configuration error' },
            { status: 500 }
          )
        default:
          return NextResponse.json(
            { error: 'Payment processing error' },
            { status: 500 }
          )
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})