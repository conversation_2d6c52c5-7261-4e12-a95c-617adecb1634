'use client';

import { useMemo, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Grid3X3,
  TrendingUp,
  TrendingDown,
  Download,
  Filter,
  Info
} from 'lucide-react';
import { DEVELOPMENT_DIMENSIONS } from '@/lib/types/character-development';
import type { DevelopmentGridData } from '@/lib/types/character-development';
import { DevelopmentDimensionSelector } from './development-dimension-selector';

interface CharacterDevelopmentGridProps {
  gridData: DevelopmentGridData;
  maxChapters?: number;
  onDimensionClick?: (dimensionId: string, chapter: number) => void;
  selectedDimensions?: string[];
  comparisonMode?: boolean;
}

export function CharacterDevelopmentGrid({ 
  gridData,
  maxChapters = 50,
  onDimensionClick,
  selectedDimensions: initialSelectedDimensions = DEVELOPMENT_DIMENSIONS.map(d => d.id),
  comparisonMode = false
}: CharacterDevelopmentGridProps) {
  const [hoveredCell, setHoveredCell] = useState<{ dimension: string; chapter: number } | null>(null);
  const [selectedDimensions, setSelectedDimensions] = useState<string[]>(initialSelectedDimensions);
  const [showDimensionSelector, setShowDimensionSelector] = useState(false);

  // Calculate color intensity based on development value
  const getColorForValue = (value: number, type: string) => {
    if (value === 0) return 'bg-gray-100 dark:bg-gray-800';
    
    const absValue = Math.abs(value);
    const intensity = Math.min(absValue / 100, 1);
    
    if (type === 'regression') {
      // Red shades for regression
      if (intensity < 0.25) return 'bg-error-light dark:bg-red-900/30';
      if (intensity < 0.5) return 'bg-red-200 dark:bg-red-800/40';
      if (intensity < 0.75) return 'bg-red-300 dark:bg-red-700/50';
      return 'bg-red-400 dark:bg-error/60';
    } else {
      // Green shades for growth
      if (intensity < 0.25) return 'bg-success-light dark:bg-green-900/30';
      if (intensity < 0.5) return 'bg-green-200 dark:bg-green-800/40';
      if (intensity < 0.75) return 'bg-green-300 dark:bg-green-700/50';
      return 'bg-green-400 dark:bg-success/60';
    }
  };

  // Get cell data for a specific dimension and chapter
  const getCellData = (dimensionId: string, chapterNumber: number) => {
    const chapter = gridData.chapters.find(c => c.chapter === chapterNumber);
    if (!chapter || !chapter.dimensions[dimensionId]) {
      return { value: 0, type: 'stagnant', events: [] };
    }
    return chapter.dimensions[dimensionId];
  };

  // Calculate summary statistics
  const stats = useMemo(() => {
    let totalGrowth = 0;
    let totalRegression = 0;
    let breakthroughs = 0;
    let stagnantChapters = 0;

    gridData.chapters.forEach(chapter => {
      let hasActivity = false;
      Object.entries(chapter.dimensions).forEach(([, data]) => {
        if (data.value > 0) {
          totalGrowth += data.value;
          hasActivity = true;
        } else if (data.value < 0) {
          totalRegression += Math.abs(data.value);
          hasActivity = true;
        }
        if (data.type === 'breakthrough') breakthroughs++;
      });
      if (!hasActivity) stagnantChapters++;
    });

    return {
      totalGrowth,
      totalRegression,
      breakthroughs,
      stagnantChapters,
      netProgress: totalGrowth - totalRegression
    };
  }, [gridData]);

  // Filter dimensions based on selection
  const visibleDimensions = DEVELOPMENT_DIMENSIONS.filter(
    dim => selectedDimensions.includes(dim.id)
  );

  // Limit chapters displayed
  const displayedChapters = gridData.chapters.slice(0, maxChapters);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Grid3X3 className="w-5 h-5 mr-2" />
              Character Development Grid
              {comparisonMode && (
                <Badge variant="secondary" className="ml-2">
                  Comparison Mode
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {gridData.characterName}&apos;s development across {displayedChapters.length} chapters
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="gap-2"
              onClick={() => setShowDimensionSelector(!showDimensionSelector)}
            >
              <Filter className="w-4 h-4" />
              Dimensions
            </Button>
            <Button variant="outline" size="sm" className="gap-2">
              <Download className="w-4 h-4" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-3 mb-6">
          <div className="text-center p-3 bg-success-light dark:bg-green-950/20 rounded-lg">
            <TrendingUp className="w-5 h-5 mx-auto mb-1 text-success" />
            <div className="text-lg font-bold text-success">+{Math.round(stats.totalGrowth)}</div>
            <div className="text-xs text-success">Total Growth</div>
          </div>
          <div className="text-center p-3 bg-error-light dark:bg-red-950/20 rounded-lg">
            <TrendingDown className="w-5 h-5 mx-auto mb-1 text-error" />
            <div className="text-lg font-bold text-error">-{Math.round(stats.totalRegression)}</div>
            <div className="text-xs text-error">Total Regression</div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
            <div className="text-lg font-bold text-purple-600">{stats.breakthroughs}</div>
            <div className="text-xs text-purple-600">Breakthroughs</div>
          </div>
          <div className="text-center p-3 bg-info-light dark:bg-blue-950/20 rounded-lg">
            <div className="text-lg font-bold text-info">
              {stats.netProgress > 0 ? '+' : ''}{Math.round(stats.netProgress)}
            </div>
            <div className="text-xs text-info">Net Progress</div>
          </div>
        </div>

        {/* Dimension Selector */}
        {showDimensionSelector && (
          <div className="mb-6">
            <DevelopmentDimensionSelector
              selectedDimensions={selectedDimensions}
              onSelectionChange={setSelectedDimensions}
              compact={true}
            />
          </div>
        )}

        {/* Grid Container */}
        <div className="overflow-x-auto">
          <div className="inline-block min-w-full">
            {/* Grid */}
            <div className="grid gap-1" style={{ gridTemplateColumns: `150px repeat(${displayedChapters.length}, 16px)` }}>
              {/* Header Row - Chapter Numbers */}
              <div></div>
              {displayedChapters.map((chapter, idx) => (
                <div 
                  key={chapter.chapter} 
                  className="text-xs text-center text-gray-500"
                  style={{ writingMode: idx % 5 === 0 ? 'horizontal-tb' : undefined }}
                >
                  {idx % 5 === 0 ? chapter.chapter : ''}
                </div>
              ))}

              {/* Dimension Rows */}
              {visibleDimensions.map(dimension => (
                <div key={dimension.id} className="contents">
                  {/* Dimension Label */}
                  <div className="flex items-center justify-between pr-2">
                    <span className="text-sm font-medium truncate">{dimension.name}</span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="w-3 h-3 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">{dimension.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>

                  {/* Chapter Cells */}
                  {displayedChapters.map(chapter => {
                    const cellData = getCellData(dimension.id, chapter.chapter);
                    const isHovered = hoveredCell?.dimension === dimension.id && 
                                     hoveredCell?.chapter === chapter.chapter;

                    return (
                      <TooltipProvider key={`${dimension.id}-${chapter.chapter}`}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`
                                h-4 rounded-sm cursor-pointer transition-all
                                ${getColorForValue(cellData.value, cellData.type)}
                                ${isHovered ? 'ring-2 ring-primary ring-offset-1' : ''}
                                ${cellData.type === 'breakthrough' ? 'ring-1 ring-yellow-400' : ''}
                              `}
                              onMouseEnter={() => setHoveredCell({ dimension: dimension.id, chapter: chapter.chapter })}
                              onMouseLeave={() => setHoveredCell(null)}
                              onClick={() => onDimensionClick?.(dimension.id, chapter.chapter)}
                            />
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="space-y-1">
                              <p className="font-medium">Chapter {chapter.chapter}</p>
                              <p className="text-sm">{dimension.name}</p>
                              <p className={`text-sm font-medium ${
                                cellData.value > 0 ? 'text-success' : 
                                cellData.value < 0 ? 'text-error' : 'text-gray-500'
                              }`}>
                                {cellData.value > 0 ? '+' : ''}{cellData.value}% {cellData.type}
                              </p>
                              {cellData.events.length > 0 && (
                                <div className="pt-1 border-t">
                                  {cellData.events.map((event, idx) => (
                                    <p key={idx} className="text-xs text-gray-600">• {event}</p>
                                  ))}
                                </div>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })}
                </div>
              ))}
            </div>

            {/* Legend */}
            <div className="mt-6 flex items-center justify-center space-x-4 text-xs">
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 bg-gray-100 dark:bg-gray-800 rounded-sm"></div>
                <span>No Change</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 bg-green-200 dark:bg-green-800/40 rounded-sm"></div>
                <span>Minor Growth</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 bg-green-400 dark:bg-success/60 rounded-sm"></div>
                <span>Major Growth</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 bg-red-200 dark:bg-red-800/40 rounded-sm"></div>
                <span>Minor Regression</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 bg-red-400 dark:bg-error/60 rounded-sm"></div>
                <span>Major Regression</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 bg-yellow-200 dark:bg-yellow-800/40 rounded-sm ring-1 ring-yellow-400"></div>
                <span>Breakthrough</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}