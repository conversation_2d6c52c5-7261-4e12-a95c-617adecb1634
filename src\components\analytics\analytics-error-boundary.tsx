"use client";

import React from "react";
import { logger } from '@/lib/services/logger';

import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class AnalyticsErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error("Analytics Dashboard Error:", error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center min-h-[400px] p-8">
          <Alert className="max-w-lg sm:max-w-xl lg:max-w-2xl lg:max-w-3xl xl:max-w-4xl">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Analytics Error</AlertTitle>
            <AlertDescription className="mt-2 space-y-2">
              <p>Something went wrong while loading the analytics dashboard.</p>
              {this.state.error && (
                <p className="text-sm text-muted-foreground font-mono">
                  {this.state.error.message}
                </p>
              )}
              <div className="mt-4">
                <Button onClick={this.handleReset} variant="outline" size="sm">
                  Try Again
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    return this.props.children;
  }
}