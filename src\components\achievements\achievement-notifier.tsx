'use client'

import { logger } from '@/lib/services/logger'

import { useEffect, useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import { Trophy, Star, Zap } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/auth-context'
import confetti from 'canvas-confetti'

interface NewAchievement {
  id: string
  name: string
  description: string
  points: number
  tier: string
}

export function AchievementNotifier() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [lastCheck, setLastCheck] = useState<Date>(new Date())
  const supabase = createClient()

  useEffect(() => {
    if (!user) return

    // Check for new achievements on mount
    checkAchievements()

    // Set up interval to check every 5 minutes
    const interval = setInterval(checkAchievements, 5 * 60 * 1000)

    // Listen for achievement events
    const channel = supabase
      .channel('achievement-updates')
      .on('broadcast', { event: 'new-achievement' }, (payload) => {
        if (payload.userId === user.id) {
          showAchievementNotification(payload.achievement)
        }
      })
      .subscribe()

    return () => {
      clearInterval(interval)
      channel.unsubscribe()
    }
  }, [user])

  const checkAchievements = async () => {
    if (!user) return

    try {
      const response = await fetch('/api/achievements/check', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          userId: user.id,
          lastCheck: lastCheck.toISOString()
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.newAchievements?.length > 0) {
          data.newAchievements.forEach((achievement: NewAchievement) => {
            showAchievementNotification(achievement)
          })
        }
      }
    } catch (error) {
      logger.error('Failed to check achievements:', error)
    }

    setLastCheck(new Date())
  }

  const showAchievementNotification = (achievement: NewAchievement) => {
    // Trigger confetti
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
      colors: ['#f59e0b', '#8b5cf6', '#3b82f6']
    })

    // Show toast
    toast({
      title: (
        <div className="flex items-center gap-2">
          <Trophy className="h-5 w-5 text-warning" />
          Achievement Unlocked!
        </div>
      ),
      description: (
        <div className="space-y-1">
          <p className="font-semibold">{achievement.name}</p>
          <p className="text-sm text-muted-foreground">{achievement.description}</p>
          <div className="flex items-center gap-2 mt-2">
            <Badge className={`text-xs ${getTierColor(achievement.tier)}`}>
              {achievement.tier}
            </Badge>
            <span className="text-xs text-muted-foreground">
              +{achievement.points} points
            </span>
          </div>
        </div>
      ),
      duration: 6000,
    })
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return 'bg-purple-500 text-white'
      case 'gold':
        return 'bg-warning text-white'
      case 'silver':
        return 'bg-gray-400 text-white'
      default:
        return 'bg-orange-600 text-white'
    }
  }

  return null // This is a notification-only component
}

// Badge component used in the toast
function Badge({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 font-semibold ${className}`}>
      {children}
    </span>
  )
}