import { NextResponse } from 'next/server'
import { handleAPIError } from '@/lib/api/error-handler'
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase'
import { AdaptivePlanningAgent } from '@/lib/agents/adaptive-planning-agent'
import { PlanAdjustmentService } from '@/lib/services/plan-adjustment'
import { aiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter-unified'
import { checkUsageBeforeAction, trackUsage } from '@/lib/usage-tracker'
import type { Database } from '@/lib/db/types'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { BookContext } from '@/lib/agents/types'
import type { ProjectSettings } from '@/lib/types/project-settings'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { RATE_LIMITS } from '@/lib/constants'

type StoryBibleEntry = Database['public']['Tables']['story_bible']['Row']

// Type definitions for story bible entry data structures
interface WorldRuleData {
  content?: string
  value?: string
}

interface PlotThreadData {
  description?: string
  status?: string
}

interface TimelineEventData {
  event?: string
  chapter?: number
}

// Validation schema for plan adjustment request
const adjustPlanSchema = z.object({
  projectId: z.string().uuid('Invalid project ID'),
  chapterId: z.string().uuid('Invalid chapter ID'),
  userChanges: z.object({
    changes: z.array(z.object({
      type: z.string(),
      description: z.string(),
      impact: z.enum(['minor', 'moderate', 'major']).optional(),
      affectedChapters: z.array(z.number()).optional(),
      metadata: z.record(z.unknown()).optional()
    })).min(1, 'At least one change is required')
  })
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check
    const clientIP = getClientIP(request)
    const rateLimitResult = aiLimiter.check(RATE_LIMITS.SERVICE_HEALTH_CHECK, clientIP) // 20 plan adjustments per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse()
    }
    
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return ErrorResponses.unauthorized()
    }

    // Check usage limits
    const usageCheck = await checkUsageBeforeAction(user.id, 'ai_generation')
    if (!usageCheck.allowed) {
      if (usageCheck.reason?.includes('credits')) {
        return ErrorResponses.insufficientCredits(
          1, // Required: 1 generation for this operation
          0  // Available: 0 (exceeded limit)
        )
      }
      return ErrorResponses.subscriptionRequired('AI plan adjustments')
    }

    const body = await request.json()
    
    // Validate request body
    const validationResult = adjustPlanSchema.safeParse(body)
    if (!validationResult.success) {
      return ErrorResponses.validationError(validationResult.error)
    }
    
    const { projectId, chapterId, userChanges } = validationResult.data

    // Validate project ownership
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return ErrorResponses.notFound('Project')
    }

    // Get comprehensive project context for analysis
    const projectContext = await buildProjectContext(supabase, projectId)

    if (!projectContext) {
      return ErrorResponses.internalError('Failed to load project context')
    }

    // Create BookContext for the agent
    const bookContext: BookContext = {
      projectId,
      settings: project as ProjectSettings,
      // Other properties can be added as needed
    }

    // Transform the input to match UserChanges interface
    const transformedUserChanges = {
      chapterId,
      changes: userChanges.changes.map(change => ({
        type: change.type as 'plot' | 'character' | 'pacing' | 'style' | 'dialogue' | 'setting' | 'other',
        description: change.description,
        impact: (change.impact || 'medium') as 'low' | 'medium' | 'high',
        affectedElements: change.affectedChapters?.map(ch => `Chapter ${ch}`) || [],
        textChange: {
          from: '',
          to: '',
          startIndex: 0,
          endIndex: 0
        }
      })),
      userNotes: userChanges.changes.map(c => c.description).join('; '),
      significantChanges: userChanges.changes
        .filter(c => c.impact === 'major' || c.impact === 'moderate')
        .map(c => c.description),
      requestsPlanUpdate: true
    }

    // Analyze user changes with adaptive planning agent
    const adaptivePlanningAgent = new AdaptivePlanningAgent(bookContext)
    const analysisResult = await adaptivePlanningAgent.analyzeUserChanges(transformedUserChanges, projectContext)

    // Apply plan adjustments if needed
    let adjustmentSummary = null
    if (analysisResult.needsAdjustment) {
      const planAdjustmentService = new PlanAdjustmentService()
      await planAdjustmentService.initialize()

      const adjustmentResult = await planAdjustmentService.applyPlanAdjustments({
        projectId,
        userId: user.id,
        chapterId,
        adjustmentData: analysisResult
      })

      if (adjustmentResult.success) {
        adjustmentSummary = adjustmentResult.data
      } else {
        logger.error('Plan adjustment failed:', adjustmentResult.error)
      }
    }

    // Track usage
    await trackUsage({
      userId: user.id,
      eventType: 'ai_generation',
      metadata: { 
        projectId, 
        chapterId, 
        changesCount: userChanges.changes.length,
        adjustmentsMade: analysisResult.needsAdjustment,
        impactLevel: adjustmentSummary?.impactLevel || 'none'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        analysis: analysisResult,
        adjustments: adjustmentSummary,
        message: analysisResult.needsAdjustment 
          ? `Plan adjusted successfully. ${adjustmentSummary?.totalChanges || 0} changes applied.`
          : 'No plan adjustments needed based on your changes.'
      }
    })

  } catch (error) {
    return handleAPIError(error, 'Plan adjustment')
  }
}

async function buildProjectContext(supabase: SupabaseClient<Database>, projectId: string) {
  try {
    // Get all required data in parallel
    const [
      { data: project },
      { data: completedChapters },
      { data: upcomingChapters },
      { data: characters },
      { data: storyBible }
    ] = await Promise.all([
      supabase.from('projects').select('*').eq('id', projectId).single(),
      supabase.from('chapters')
        .select('*')
        .eq('project_id', projectId)
        .eq('status', 'complete')
        .order('chapter_number'),
      supabase.from('chapters')
        .select('*')
        .eq('project_id', projectId)
        .in('status', ['planned', 'writing', 'generated'])
        .order('chapter_number'),
      supabase.from('characters')
        .select('*')
        .eq('project_id', projectId),
      supabase.from('story_bible')
        .select('*')
        .eq('project_id', projectId)
    ])

    if (!project) {
      return null
    }

    return {
      projectId,
      completedChapters: completedChapters?.map(ch => ({
        chapterNumber: ch.chapter_number,
        content: ch.content || '',
        wordCount: ch.actual_word_count || 0
      })) || [],
      upcomingChapters: upcomingChapters?.map(ch => {
        const outline = ch.outline ? JSON.parse(ch.outline) : {}
        return {
          chapterNumber: ch.chapter_number,
          title: ch.title || outline.title || `Chapter ${ch.chapter_number}`,
          summary: outline.summary || '',
          objectives: outline.objectives || [],
          conflicts: outline.conflicts || [],
          scenes: outline.scenes || [],
          characterStates: outline.characterStates || [],
          plotAdvancement: outline.plotAdvancement || []
        }
      }) || [],
      characters: characters?.map(char => ({
        id: char.id,
        name: char.name,
        role: char.role,
        arc: char.character_arc || {},
        currentState: {
          emotionalState: char.personality_traits?.currentEmotionalState,
          knowledge: char.character_arc?.currentKnowledge || [],
          relationships: char.relationships?.currentState
        }
      })) || [],
      storyBible: {
        worldRules: Object.fromEntries(
          storyBible?.filter((entry: StoryBibleEntry) => entry.entry_type === 'world_rule')
            .map((entry: StoryBibleEntry) => [
              entry.entry_key,
              (entry.entry_data as WorldRuleData)?.content || (entry.entry_data as WorldRuleData)?.value || entry.entry_key
            ]) || []
        ),
        plotThreads: storyBible?.filter((entry: StoryBibleEntry) => entry.entry_type === 'plot_thread')
          .map((entry: StoryBibleEntry) => ({
            id: entry.id,
            description: (entry.entry_data as PlotThreadData)?.description || entry.entry_key,
            status: 'active',
            nextMilestone: ''
          })) || [],
        timeline: storyBible?.filter((entry: StoryBibleEntry) => entry.entry_type === 'timeline_event')
          .map((entry: StoryBibleEntry) => ({
            event: (entry.entry_data as TimelineEventData)?.event || entry.entry_key,
            chapter: entry.chapter_introduced || 1
          })) || []
      },
      projectSettings: {
        primaryGenre: project.primary_genre,
        structureType: project.structure_type,
        pacingPreference: project.pacing_preference,
        targetWordCount: project.target_word_count,
        targetChapters: project.target_chapters
      }
    }
  } catch (error) {
    logger.error('Failed to build project context:', error)
    return null
  }
}