import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'
import { logger } from '@/lib/services/logger'
import { ExportQueueService } from '@/lib/services/export-queue-service'

const exportSchema = z.object({
  format: z.enum(['txt', 'markdown', 'docx', 'pdf', 'epub']),
  includeMetadata: z.boolean().optional().default(true),
  includeFrontMatter: z.boolean().optional().default(true),
  includeChapterNumbers: z.boolean().optional().default(true),
  includeTableOfContents: z.boolean().optional().default(true),
  chapterIds: z.array(baseSchemas.uuid).max(100).optional(),
  customStyles: z.object({
    fontFamily: z.string().max(50).optional(),
    fontSize: z.number().min(8).max(24).optional(),
    lineSpacing: z.number().min(1).max(3).optional()
  }).optional()
})

export const POST = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id: projectId } = await params;

  // Validate project ID format
  if (!baseSchemas.uuid.safeParse(projectId).success) {
    return UnifiedResponse.error('Invalid project ID format', 400);
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: exportSchema,
    rateLimitKey: 'export',
    rateLimitCost: 10, // High cost for exports
    maxBodySize: 5 * 1024, // 5KB for export options
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      // Verify user owns the project
      const supabase = await createTypedServerClient();
      const { data: project, error } = await supabase
        .from('projects')
        .select('id, status, chapters_count')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single();

      if (error || !project) {
        return { valid: false, error: 'Project not found or unauthorized' };
      }

      // Check if project has content to export
      if (!project.chapters_count || project.chapters_count === 0) {
        return { valid: false, error: 'Project has no chapters to export' };
      }

      // Check if project is in a valid state for export
      if (project.status === 'archived') {
        return { valid: false, error: 'Cannot export archived project' };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const validatedData = context.body;

  try {
    const { format, ...options } = validatedData;
    const supabase = await createTypedServerClient();

    // Get full project details for export
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id, word_count, chapters_count, metadata')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404);
    }

    // If specific chapters requested, validate they belong to the project
    if (options.chapterIds && options.chapterIds.length > 0) {
      const { data: chapters, error: chaptersError } = await supabase
        .from('chapters')
        .select('id')
        .eq('project_id', projectId)
        .in('id', options.chapterIds);

      if (chaptersError || !chapters || chapters.length !== options.chapterIds.length) {
        return UnifiedResponse.error('One or more requested chapters not found', 400);
      }
    }

    // Check subscription tier for export format restrictions
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single()
    
    const userTier = subscription?.tier || 'free'
    
    // Enforce export format restrictions by tier
    const restrictedFormats = {
      pdf: ['professional', 'studio'],
      epub: ['professional', 'studio'],
      docx: ['starter', 'professional', 'studio']
    }
    
    if (restrictedFormats[format as keyof typeof restrictedFormats]) {
      const allowedTiers = restrictedFormats[format as keyof typeof restrictedFormats]
      if (!allowedTiers.includes(userTier)) {
        return UnifiedResponse.error(
          `${format.toUpperCase()} export requires ${allowedTiers.join(' or ')} plan`,
          403,
          { requiredTier: allowedTiers[0] }
        )
      }
    }

    // Log export request
    logger.info('Export requested', {
      userId: user.id,
      projectId,
      format,
      includeChapters: options.chapterIds?.length || 'all',
      userTier,
      clientIP: context.clientIP
    });

    // Queue the export job
    const jobId = await ExportQueueService.addJob({
      projectId,
      projectTitle: project.title,
      userId: user.id,
      format,
      options: {
        ...options,
        userTierId: userTier,
        projectMetadata: project.metadata || {}
      }
    });

    // Track export achievement
    try {
      // Track first export achievement
      await supabase.rpc('track_achievement_progress', {
        p_user_id: user.id,
        p_achievement_code: 'first_export',
        p_progress_key: 'exports',
        p_increment: 1
      })
      
      // Track export format for export master achievement
      await supabase.rpc('track_achievement_progress', {
        p_user_id: user.id,
        p_achievement_code: 'export_master',
        p_progress_key: format,
        p_increment: 1
      })
      
      // Check for newly unlocked achievements
      await supabase.rpc('check_and_unlock_achievements', {
        p_user_id: user.id
      })
    } catch (achievementError) {
      logger.warn('Failed to track export achievement:', achievementError)
      // Don't fail the export if achievement tracking fails
    }

    logger.info('Export job queued', {
      userId: user.id,
      projectId,
      jobId,
      format,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      jobId,
      message: 'Export job queued successfully',
      status: 'pending',
      estimatedTime: getEstimatedExportTime(format, project.word_count || 0)
    });
  } catch (error) {
    logger.error('Export error:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to queue export');
  }
});

// Helper function to estimate export time
function getEstimatedExportTime(format: string, wordCount: number): string {
  const baseTime = 5; // 5 seconds base time
  const wordsPerSecond = {
    txt: 10000,
    markdown: 8000,
    docx: 5000,
    pdf: 3000,
    epub: 4000
  };
  
  const processingTime = Math.ceil(wordCount / (wordsPerSecond[format as keyof typeof wordsPerSecond] || 5000));
  const totalSeconds = baseTime + processingTime;
  
  if (totalSeconds < 60) {
    return `${totalSeconds} seconds`;
  } else {
    const minutes = Math.ceil(totalSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
}