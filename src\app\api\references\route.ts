import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

const getQuerySchema = z.object({
  projectId: z.string().uuid(),
  type: z.string().optional(),
  tags: z.string().optional()
})

const postSchema = z.object({
  projectId: z.string().uuid(),
  type: z.enum(['document', 'image', 'url', 'note', 'research']),
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  content: z.string().optional(),
  tags: z.array(z.string()).optional()
})

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const type = searchParams.get('type')
    const tags = searchParams.get('tags')

    const validation = getQuerySchema.safeParse({ projectId, type, tags })
    if (!validation.success) {
      return UnifiedResponse.error('Invalid query parameters', 400, validation.error.errors)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    let query = supabase
      .from('reference_materials')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    if (type && type !== 'all') {
      query = query.eq('type', type)
    }

    if (tags) {
      const tagList = tags.split(',').map(t => t.trim())
      query = query.contains('tags', tagList)
    }

    const { data: materials, error } = await query

    if (error) {
      logger.error('Failed to fetch reference materials:', error)
      return UnifiedResponse.error('Failed to fetch reference materials')
    }

    // Transform data to match frontend interface
    const formattedMaterials = materials?.map(material => ({
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    })) || []

    return UnifiedResponse.success({ materials: formattedMaterials })
  } catch (error) {
    logger.error('References GET error:', error)
    return UnifiedResponse.error('Failed to retrieve reference materials')
  }
})

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const validation = postSchema.safeParse(body)
    
    if (!validation.success) {
      return UnifiedResponse.error('Invalid request data', 400, validation.error.errors)
    }

    const { projectId, type, title, description, content, tags } = validation.data
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    const materialData = {
      project_id: projectId,
      user_id: user.id,
      type,
      title,
      description: description || null,
      content: content || null,
      tags: tags || [],
    }

    const { data: material, error } = await supabase
      .from('reference_materials')
      .insert(materialData)
      .select()
      .single()

    if (error) {
      logger.error('Failed to create reference material:', error)
      return UnifiedResponse.error('Failed to create reference material')
    }

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    }

    return UnifiedResponse.success({ material: formattedMaterial })
  } catch (error) {
    logger.error('References POST error:', error)
    return UnifiedResponse.error('Failed to create reference material')
  }
})