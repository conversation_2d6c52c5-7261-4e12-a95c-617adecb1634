# Export/Import System Documentation

## Overview

BookScribe's Export/Import system enables authors to work with their content across different formats and platforms. The system supports industry-standard formats (DOCX, EPUB, PDF) for export and intelligent parsing for imports, maintaining formatting, structure, and metadata throughout the process.

## Architecture

### System Components

```mermaid
graph TB
    subgraph "Export Pipeline"
        ExportManager[Export Manager]
        FormatConverters[Format Converters]
        StyleMapper[Style Mapper]
        MetadataHandler[Metadata Handler]
    end
    
    subgraph "Import Pipeline"
        ImportManager[Import Manager]
        FormatParsers[Format Parsers]
        ContentAnalyzer[Content Analyzer]
        StructureDetector[Structure Detector]
    end
    
    subgraph "Processing Layer"
        QueueManager[Queue Manager]
        FileProcessor[File Processor]
        ValidationEngine[Validation Engine]
        StorageHandler[Storage Handler]
    end
    
    subgraph "Format Handlers"
        DOCXHandler[DOCX Handler]
        EPUBHandler[EPUB Handler]
        PDFHandler[PDF Handler]
        MarkdownHandler[Markdown Handler]
    end
    
    ExportManager --> FormatConverters
    FormatConverters --> DOCXHandler
    FormatConverters --> EPUBHandler
    FormatConverters --> PDFHandler
    
    ImportManager --> FormatParsers
    FormatParsers --> ContentAnalyzer
    ContentAnalyzer --> StructureDetector
```

## Export System

### 1. Export Manager

```typescript
interface ExportOptions {
  format: 'docx' | 'epub' | 'pdf' | 'markdown' | 'html';
  includeMetadata: boolean;
  includeComments: boolean;
  includeRevisions: boolean;
  formatting: FormattingOptions;
  customization: CustomizationOptions;
}

class ExportManager {
  async exportProject(
    projectId: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    // Load project data
    const project = await this.loadProjectData(projectId);
    
    // Validate export permissions
    await this.validateExportPermissions(project);
    
    // Prepare content for export
    const preparedContent = await this.prepareContent(project, options);
    
    // Convert to target format
    const convertedFile = await this.convertToFormat(
      preparedContent,
      options.format
    );
    
    // Apply customizations
    const finalFile = await this.applyCustomizations(
      convertedFile,
      options.customization
    );
    
    // Store and return result
    return await this.storeExportResult(finalFile);
  }
  
  private async prepareContent(
    project: Project,
    options: ExportOptions
  ): Promise<PreparedContent> {
    const content: PreparedContent = {
      metadata: this.extractMetadata(project),
      frontMatter: this.generateFrontMatter(project),
      chapters: [],
      backMatter: this.generateBackMatter(project)
    };
    
    // Process chapters
    for (const chapter of project.chapters) {
      const processedChapter = await this.processChapter(chapter, options);
      content.chapters.push(processedChapter);
    }
    
    // Add comments if requested
    if (options.includeComments) {
      content.comments = await this.extractComments(project);
    }
    
    // Add revision history if requested
    if (options.includeRevisions) {
      content.revisions = await this.extractRevisions(project);
    }
    
    return content;
  }
}
```

### 2. Format Converters

#### DOCX Converter
```typescript
class DOCXConverter implements FormatConverter {
  async convert(content: PreparedContent): Promise<Buffer> {
    const doc = new Document({
      creator: content.metadata.author,
      title: content.metadata.title,
      description: content.metadata.description,
      styles: this.createStyles(),
      numbering: this.createNumbering(),
      sections: []
    });
    
    // Add front matter
    doc.addSection({
      properties: {},
      children: this.createFrontMatter(content.frontMatter)
    });
    
    // Add chapters
    for (const chapter of content.chapters) {
      doc.addSection({
        properties: {
          type: SectionType.NEXT_PAGE
        },
        headers: {
          default: new Header({
            children: [this.createHeaderContent(chapter)]
          })
        },
        children: this.createChapterContent(chapter)
      });
    }
    
    // Add back matter
    if (content.backMatter) {
      doc.addSection({
        properties: {},
        children: this.createBackMatter(content.backMatter)
      });
    }
    
    return await Packer.toBuffer(doc);
  }
  
  private createStyles(): IStylesOptions {
    return {
      default: {
        heading1: {
          run: {
            size: 32,
            bold: true,
            font: "Calibri"
          },
          paragraph: {
            spacing: { after: 120 }
          }
        },
        heading2: {
          run: {
            size: 26,
            bold: true,
            font: "Calibri"
          },
          paragraph: {
            spacing: { before: 240, after: 120 }
          }
        },
        normal: {
          run: {
            size: 24,
            font: "Times New Roman"
          },
          paragraph: {
            spacing: { line: 360 },
            indent: { firstLine: 720 }
          }
        }
      }
    };
  }
  
  private createChapterContent(chapter: Chapter): Paragraph[] {
    const paragraphs: Paragraph[] = [];
    
    // Chapter title
    paragraphs.push(
      new Paragraph({
        text: chapter.title,
        heading: HeadingLevel.HEADING_1,
        pageBreakBefore: true
      })
    );
    
    // Process chapter content
    const contentParagraphs = this.parseContent(chapter.content);
    paragraphs.push(...contentParagraphs);
    
    return paragraphs;
  }
}
```

#### EPUB Converter
```typescript
class EPUBConverter implements FormatConverter {
  async convert(content: PreparedContent): Promise<Buffer> {
    const epub = new EPub({
      id: content.metadata.id,
      title: content.metadata.title,
      author: content.metadata.author,
      publisher: "BookScribe AI",
      cover: content.metadata.coverImage,
      css: this.getDefaultCSS()
    });
    
    // Add metadata
    epub.addMetadata({
      description: content.metadata.description,
      subject: content.metadata.genres,
      language: content.metadata.language || 'en',
      date: new Date().toISOString()
    });
    
    // Add cover if available
    if (content.metadata.coverImage) {
      await epub.addCover(content.metadata.coverImage);
    }
    
    // Add title page
    epub.addSection({
      title: 'Title Page',
      content: this.createTitlePage(content),
      excludeFromToc: true
    });
    
    // Add table of contents
    epub.addNavigation(this.createTableOfContents(content));
    
    // Add chapters
    for (const [index, chapter] of content.chapters.entries()) {
      epub.addChapter({
        id: `chapter-${index + 1}`,
        title: chapter.title,
        content: this.formatChapterHTML(chapter)
      });
    }
    
    // Generate EPUB
    return await epub.generate();
  }
  
  private formatChapterHTML(chapter: Chapter): string {
    return `
      <!DOCTYPE html>
      <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <title>${chapter.title}</title>
        <link rel="stylesheet" type="text/css" href="styles.css"/>
      </head>
      <body>
        <h1>${chapter.title}</h1>
        ${this.convertContentToHTML(chapter.content)}
      </body>
      </html>
    `;
  }
  
  private getDefaultCSS(): string {
    return `
      body {
        font-family: Georgia, serif;
        line-height: 1.6;
        margin: 0 5%;
      }
      h1 {
        text-align: center;
        margin-top: 20%;
        page-break-before: always;
      }
      p {
        text-indent: 1.5em;
        margin: 0;
      }
      p:first-of-type {
        text-indent: 0;
      }
    `;
  }
}
```

#### PDF Converter
```typescript
class PDFConverter implements FormatConverter {
  async convert(content: PreparedContent): Promise<Buffer> {
    const doc = new PDFDocument({
      size: 'LETTER',
      margins: {
        top: 72,
        bottom: 72,
        left: 72,
        right: 72
      },
      info: {
        Title: content.metadata.title,
        Author: content.metadata.author,
        Subject: content.metadata.description,
        Creator: 'BookScribe AI'
      }
    });
    
    const chunks: Buffer[] = [];
    doc.on('data', chunks.push.bind(chunks));
    
    // Add cover page
    this.addCoverPage(doc, content.metadata);
    
    // Add table of contents
    this.addTableOfContents(doc, content.chapters);
    
    // Add chapters
    for (const chapter of content.chapters) {
      this.addChapter(doc, chapter);
    }
    
    // Add page numbers
    this.addPageNumbers(doc);
    
    doc.end();
    
    return new Promise((resolve) => {
      doc.on('end', () => {
        resolve(Buffer.concat(chunks));
      });
    });
  }
  
  private addChapter(doc: PDFDocument, chapter: Chapter) {
    // Start new page for chapter
    doc.addPage();
    
    // Chapter title
    doc.fontSize(24)
       .font('Helvetica-Bold')
       .text(chapter.title, {
         align: 'center',
         lineGap: 10
       });
    
    doc.moveDown(2);
    
    // Chapter content
    doc.fontSize(12)
       .font('Times-Roman')
       .text(chapter.content, {
         align: 'justify',
         indent: 30,
         lineGap: 5
       });
  }
}
```

### 3. Style Mapping

```typescript
interface StyleMapper {
  mapProjectStyles(project: Project): DocumentStyles {
    return {
      // Typography
      bodyFont: project.settings.typography?.bodyFont || 'Times New Roman',
      headingFont: project.settings.typography?.headingFont || 'Arial',
      fontSize: project.settings.typography?.fontSize || 12,
      lineHeight: project.settings.typography?.lineHeight || 1.5,
      
      // Formatting
      paragraphIndent: project.settings.formatting?.indent || 0.5,
      paragraphSpacing: project.settings.formatting?.spacing || 0,
      
      // Page setup
      pageSize: project.settings.page?.size || 'Letter',
      margins: project.settings.page?.margins || {
        top: 1,
        bottom: 1,
        left: 1,
        right: 1
      },
      
      // Headers/Footers
      headerContent: this.generateHeaderContent(project),
      footerContent: this.generateFooterContent(project)
    };
  }
}
```

## Import System

### 1. Import Manager

```typescript
interface ImportOptions {
  detectStructure: boolean;
  preserveFormatting: boolean;
  splitChapters: boolean;
  cleanupText: boolean;
  targetProject?: string;
}

class ImportManager {
  async importFile(
    file: File,
    options: ImportOptions
  ): Promise<ImportResult> {
    // Validate file
    const validation = await this.validateFile(file);
    if (!validation.isValid) {
      throw new ImportValidationError(validation.errors);
    }
    
    // Detect format
    const format = this.detectFormat(file);
    
    // Parse content
    const parsedContent = await this.parseContent(file, format);
    
    // Analyze structure
    const structure = await this.analyzeStructure(parsedContent, options);
    
    // Clean and process
    const processedContent = await this.processContent(
      parsedContent,
      structure,
      options
    );
    
    // Create or update project
    const result = await this.createProject(processedContent, options);
    
    return result;
  }
  
  private async analyzeStructure(
    content: ParsedContent,
    options: ImportOptions
  ): Promise<DocumentStructure> {
    const analyzer = new ContentAnalyzer();
    
    // Detect chapters
    const chapters = options.detectStructure
      ? await analyzer.detectChapters(content)
      : [{ title: 'Chapter 1', content: content.text }];
    
    // Extract metadata
    const metadata = await analyzer.extractMetadata(content);
    
    // Detect writing style
    const style = await analyzer.detectWritingStyle(content);
    
    // Find characters
    const characters = await analyzer.findCharacters(content);
    
    return {
      chapters,
      metadata,
      style,
      characters,
      scenes: await analyzer.detectScenes(chapters)
    };
  }
}
```

### 2. Format Parsers

#### DOCX Parser
```typescript
class DOCXParser implements FormatParser {
  async parse(file: Buffer): Promise<ParsedContent> {
    const result = await mammoth.convertToHtml({
      buffer: file,
      options: {
        preserveStyles: true,
        includeDefaultStyleMap: false,
        styleMap: this.getStyleMap()
      }
    });
    
    const $ = cheerio.load(result.value);
    
    return {
      html: result.value,
      text: $.text(),
      paragraphs: this.extractParagraphs($),
      headings: this.extractHeadings($),
      styles: this.extractStyles($),
      images: await this.extractImages(file),
      metadata: await this.extractMetadata(file)
    };
  }
  
  private extractParagraphs($: CheerioStatic): Paragraph[] {
    const paragraphs: Paragraph[] = [];
    
    $('p').each((index, element) => {
      const $el = $(element);
      const text = $el.text().trim();
      
      if (text) {
        paragraphs.push({
          text,
          style: this.detectParagraphStyle($el),
          formatting: this.extractFormatting($el),
          index
        });
      }
    });
    
    return paragraphs;
  }
  
  private detectParagraphStyle($el: Cheerio): ParagraphStyle {
    // Detect if it's dialogue
    if ($el.text().match(/^["'].*["']$/)) {
      return 'dialogue';
    }
    
    // Detect if it's a scene break
    if ($el.text().match(/^\*\s*\*\s*\*$|^#{3,}$/)) {
      return 'scene_break';
    }
    
    // Check for first paragraph after heading
    if ($el.prev().is('h1, h2, h3')) {
      return 'first_paragraph';
    }
    
    return 'normal';
  }
}
```

#### EPUB Parser
```typescript
class EPUBParser implements FormatParser {
  async parse(file: Buffer): Promise<ParsedContent> {
    const epub = await EPub.parse(file);
    const content: ParsedContent = {
      html: '',
      text: '',
      paragraphs: [],
      headings: [],
      metadata: this.extractEPUBMetadata(epub)
    };
    
    // Process each chapter
    for (const chapter of epub.flow) {
      const chapterContent = await epub.getChapter(chapter.id);
      const $ = cheerio.load(chapterContent);
      
      // Extract content
      content.html += chapterContent;
      content.text += $.text() + '\n\n';
      
      // Extract structure
      content.paragraphs.push(...this.extractParagraphs($));
      content.headings.push(...this.extractHeadings($));
    }
    
    return content;
  }
  
  private extractEPUBMetadata(epub: EPub): DocumentMetadata {
    return {
      title: epub.metadata.title,
      author: epub.metadata.creator,
      description: epub.metadata.description,
      language: epub.metadata.language,
      publisher: epub.metadata.publisher,
      date: epub.metadata.date,
      isbn: epub.metadata.identifier
    };
  }
}
```

### 3. Content Analysis

```typescript
class ContentAnalyzer {
  async detectChapters(content: ParsedContent): Promise<Chapter[]> {
    const chapters: Chapter[] = [];
    const chapterRegex = /^(Chapter|CHAPTER|Ch\.|CH\.)\s*(\d+|[IVXLCDM]+)/;
    
    let currentChapter: Chapter | null = null;
    let contentBuffer: string[] = [];
    
    for (const paragraph of content.paragraphs) {
      // Check if this is a chapter heading
      if (chapterRegex.test(paragraph.text)) {
        // Save previous chapter
        if (currentChapter) {
          currentChapter.content = contentBuffer.join('\n\n');
          chapters.push(currentChapter);
        }
        
        // Start new chapter
        currentChapter = {
          number: chapters.length + 1,
          title: paragraph.text,
          content: ''
        };
        contentBuffer = [];
      } else if (currentChapter) {
        contentBuffer.push(paragraph.text);
      }
    }
    
    // Save last chapter
    if (currentChapter) {
      currentChapter.content = contentBuffer.join('\n\n');
      chapters.push(currentChapter);
    }
    
    // If no chapters detected, treat as single chapter
    if (chapters.length === 0) {
      chapters.push({
        number: 1,
        title: 'Chapter 1',
        content: content.text
      });
    }
    
    return chapters;
  }
  
  async detectWritingStyle(content: ParsedContent): Promise<WritingStyle> {
    const sampleText = content.text.substring(0, 10000);
    
    return {
      pointOfView: this.detectPOV(sampleText),
      tense: this.detectTense(sampleText),
      narrativeVoice: this.detectNarrativeVoice(sampleText),
      sentenceComplexity: this.analyzeSentenceComplexity(sampleText),
      vocabularyLevel: this.analyzeVocabulary(sampleText),
      dialogueRatio: this.calculateDialogueRatio(content.paragraphs)
    };
  }
  
  private detectPOV(text: string): string {
    const firstPersonCount = (text.match(/\b(I|me|my|mine|myself)\b/gi) || []).length;
    const secondPersonCount = (text.match(/\b(you|your|yours|yourself)\b/gi) || []).length;
    const thirdPersonCount = (text.match(/\b(he|she|they|him|her|them|his|hers|theirs)\b/gi) || []).length;
    
    const total = firstPersonCount + secondPersonCount + thirdPersonCount;
    
    if (firstPersonCount / total > 0.6) return 'first';
    if (secondPersonCount / total > 0.3) return 'second';
    return 'third';
  }
  
  async findCharacters(content: ParsedContent): Promise<Character[]> {
    const nameExtractor = new NameExtractor();
    const potentialNames = await nameExtractor.extract(content.text);
    
    const characters: Character[] = [];
    
    for (const name of potentialNames) {
      // Verify it's actually a character
      if (this.isLikelyCharacter(name, content)) {
        characters.push({
          name: name.text,
          occurrences: name.count,
          firstAppearance: name.firstIndex,
          contexts: await this.extractCharacterContexts(name, content)
        });
      }
    }
    
    return characters.sort((a, b) => b.occurrences - a.occurrences);
  }
}
```

## Export/Import Queue System

### Queue Management
```typescript
interface ExportJob {
  id: string;
  userId: string;
  projectId: string;
  format: ExportFormat;
  options: ExportOptions;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: ExportResult;
  error?: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
}

class ExportQueue {
  private queue: Bull.Queue<ExportJob>;
  
  constructor() {
    this.queue = new Bull('export-queue', {
      redis: REDIS_CONFIG,
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      }
    });
    
    this.setupProcessors();
  }
  
  async addExportJob(job: ExportJob): Promise<string> {
    const bullJob = await this.queue.add(job, {
      priority: this.calculatePriority(job),
      delay: 0
    });
    
    return bullJob.id;
  }
  
  private setupProcessors() {
    this.queue.process('export', 3, async (job) => {
      const exportJob = job.data;
      
      try {
        // Update status
        await this.updateJobStatus(exportJob.id, 'processing');
        
        // Process export
        const result = await this.processExport(exportJob);
        
        // Update completion
        await this.updateJobStatus(exportJob.id, 'completed', result);
        
        // Notify user
        await this.notifyCompletion(exportJob.userId, result);
        
        return result;
      } catch (error) {
        await this.updateJobStatus(exportJob.id, 'failed', null, error);
        throw error;
      }
    });
  }
}
```

## File Storage

### Storage Strategy
```typescript
class ExportStorage {
  async storeExportedFile(
    file: Buffer,
    metadata: ExportMetadata
  ): Promise<StorageResult> {
    const filename = this.generateFilename(metadata);
    const path = `exports/${metadata.userId}/${metadata.projectId}/${filename}`;
    
    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('exports')
      .upload(path, file, {
        contentType: this.getContentType(metadata.format),
        cacheControl: '3600',
        upsert: false
      });
    
    if (error) throw error;
    
    // Generate signed URL (24 hour expiry)
    const { signedUrl } = await supabase.storage
      .from('exports')
      .createSignedUrl(path, 86400);
    
    // Log export
    await this.logExport({
      ...metadata,
      path,
      url: signedUrl,
      size: file.length,
      expiresAt: new Date(Date.now() + 86400000)
    });
    
    return {
      url: signedUrl,
      path,
      expiresAt: new Date(Date.now() + 86400000)
    };
  }
}
```

## Format Specifications

### Supported Formats

#### Export Formats
1. **DOCX**: Full formatting, track changes, comments
2. **EPUB**: E-reader optimized, metadata, TOC
3. **PDF**: Print-ready, custom layouts, embedded fonts
4. **Markdown**: Plain text, version control friendly
5. **HTML**: Web publishing, custom styling

#### Import Formats
1. **DOCX**: Preserves formatting and structure
2. **EPUB**: Extracts chapters and metadata
3. **PDF**: Text extraction (limited formatting)
4. **TXT**: Plain text with structure detection
5. **Markdown**: Preserves formatting markers

### Format Conversion Matrix
```typescript
const conversionSupport = {
  docx: {
    import: ['formatting', 'images', 'metadata', 'comments'],
    export: ['formatting', 'images', 'metadata', 'comments', 'revisions']
  },
  epub: {
    import: ['chapters', 'metadata', 'images', 'toc'],
    export: ['chapters', 'metadata', 'images', 'toc', 'css']
  },
  pdf: {
    import: ['text'],
    export: ['formatting', 'images', 'metadata', 'toc', 'headers']
  },
  markdown: {
    import: ['formatting', 'structure'],
    export: ['formatting', 'structure', 'metadata']
  }
};
```

## Best Practices

### Export Best Practices
1. **Batch Operations**: Queue multiple exports together
2. **Format Selection**: Choose appropriate format for use case
3. **Custom Styling**: Define reusable style templates
4. **Version Control**: Export markdown for Git tracking
5. **Backup Strategy**: Regular automated exports

### Import Best Practices
1. **Pre-validation**: Check file size and format before upload
2. **Structure Detection**: Enable automatic chapter detection
3. **Character Extraction**: Review and confirm detected characters
4. **Style Preservation**: Map imported styles to project settings
5. **Incremental Import**: Import to existing projects carefully

## Error Handling

### Common Export Errors
```typescript
class ExportErrorHandler {
  handle(error: ExportError): ErrorResponse {
    switch (error.code) {
      case 'PROJECT_TOO_LARGE':
        return {
          message: 'Project exceeds maximum export size',
          suggestion: 'Try exporting individual chapters'
        };
        
      case 'INVALID_FORMAT':
        return {
          message: 'Requested format not supported',
          suggestion: 'Choose from: DOCX, EPUB, PDF, Markdown'
        };
        
      case 'GENERATION_TIMEOUT':
        return {
          message: 'Export generation timed out',
          suggestion: 'Try again with fewer formatting options'
        };
        
      case 'STORAGE_FULL':
        return {
          message: 'Export storage limit reached',
          suggestion: 'Delete old exports or upgrade plan'
        };
    }
  }
}
```

### Common Import Errors
```typescript
class ImportErrorHandler {
  handle(error: ImportError): ErrorResponse {
    switch (error.code) {
      case 'UNSUPPORTED_FORMAT':
        return {
          message: 'File format not supported',
          suggestion: 'Convert to DOCX, EPUB, or TXT first'
        };
        
      case 'FILE_TOO_LARGE':
        return {
          message: 'File exceeds 50MB limit',
          suggestion: 'Split into smaller files or compress'
        };
        
      case 'PARSING_FAILED':
        return {
          message: 'Could not parse file content',
          suggestion: 'Ensure file is not corrupted or encrypted'
        };
        
      case 'NO_TEXT_FOUND':
        return {
          message: 'No text content found in file',
          suggestion: 'Check if file contains actual text'
        };
    }
  }
}
```

## Performance Optimization

### Export Optimization
1. **Streaming**: Stream large files instead of loading in memory
2. **Compression**: Compress exports before storage
3. **Caching**: Cache frequently exported content
4. **Parallel Processing**: Process chapters concurrently
5. **CDN Delivery**: Use CDN for download delivery

### Import Optimization
1. **Chunked Upload**: Upload large files in chunks
2. **Background Processing**: Process imports asynchronously
3. **Progressive Parsing**: Parse and display as available
4. **Memory Management**: Clear parsed data after processing
5. **Duplicate Detection**: Skip importing duplicate content

## Future Enhancements

### Planned Features
1. **Scrivener Integration**: Direct import from Scrivener
2. **Google Docs Sync**: Two-way sync with Google Docs
3. **LaTeX Export**: Academic formatting support
4. **Audio Export**: Text-to-speech audiobook generation
5. **Collaborative Export**: Multi-author attribution

### Format Roadmap
- RTF support for older word processors
- ODT support for OpenOffice/LibreOffice
- Final Draft support for screenwriters
- Fountain format for screenplays
- JSON export for data analysis