import { createServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import sgMail from '@sendgrid/mail'
import { z } from 'zod'
import { config } from '@/lib/config/unified-env'

// Email template types
export const EmailTemplates = {
  WELCOME: 'welcome',
  ACHIEVEMENT_UNLOCKED: 'achievement_unlocked',
  GOAL_REMINDER: 'goal_reminder',
  COLLABORATION_INVITE: 'collaboration_invite',
  EXPORT_READY: 'export_ready',
  SUBSCRIPTION_RENEWED: 'subscription_renewed',
  SUBSCRIPTION_CANCELLED: 'subscription_cancelled',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification',
  WEEKLY_PROGRESS: 'weekly_progress',
  PROJECT_SHARED: 'project_shared',
  COMMENT_NOTIFICATION: 'comment_notification'
} as const

export type EmailTemplate = typeof EmailTemplates[keyof typeof EmailTemplates]

// Email data schemas for each template
const emailDataSchemas = {
  welcome: z.object({
    userName: z.string(),
    loginUrl: z.string()
  }),
  achievement_unlocked: z.object({
    userName: z.string(),
    achievementName: z.string(),
    achievementDescription: z.string(),
    achievementTier: z.enum(['bronze', 'silver', 'gold', 'platinum'])
  }),
  goal_reminder: z.object({
    userName: z.string(),
    goalName: z.string(),
    currentProgress: z.number(),
    targetProgress: z.number(),
    dueDate: z.string()
  }),
  collaboration_invite: z.object({
    inviterName: z.string(),
    projectName: z.string(),
    inviteUrl: z.string(),
    role: z.enum(['viewer', 'editor', 'admin'])
  }),
  export_ready: z.object({
    userName: z.string(),
    projectName: z.string(),
    exportFormat: z.string(),
    downloadUrl: z.string(),
    expiresAt: z.string()
  }),
  subscription_renewed: z.object({
    userName: z.string(),
    planName: z.string(),
    nextBillingDate: z.string(),
    amount: z.number()
  }),
  subscription_cancelled: z.object({
    userName: z.string(),
    planName: z.string(),
    expiresAt: z.string()
  }),
  password_reset: z.object({
    resetUrl: z.string()
  }),
  email_verification: z.object({
    verificationUrl: z.string()
  }),
  weekly_progress: z.object({
    userName: z.string(),
    wordsWritten: z.number(),
    chaptersCompleted: z.number(),
    writingStreak: z.number(),
    projectsWorkedOn: z.array(z.string())
  }),
  account_deleted: z.object({
    userName: z.string()
  }),
  project_shared: z.object({
    sharedByName: z.string(),
    projectName: z.string(),
    projectUrl: z.string(),
    message: z.string().optional()
  }),
  comment_notification: z.object({
    commenterName: z.string(),
    projectName: z.string(),
    chapterTitle: z.string(),
    commentPreview: z.string(),
    commentUrl: z.string()
  })
}

export type EmailData<T extends EmailTemplate> = z.infer<typeof emailDataSchemas[T]>

interface EmailQueueItem {
  id: string
  to: string
  template: EmailTemplate
  data: any
  status: 'pending' | 'processing' | 'sent' | 'failed'
  attempts: number
  error?: string
  scheduled_for: Date
  sent_at?: Date
}

export class EmailService {
  private static instance: EmailService
  private isInitialized = false

  private constructor() {}

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService()
    }
    return EmailService.instance
  }

  async initialize() {
    if (this.isInitialized) return

    const sendGridApiKey = config.email?.apiKey
    if (!sendGridApiKey) {
      logger.error('Email API key not found')
      throw new Error('Email service not configured')
    }

    sgMail.setApiKey(sendGridApiKey)
    this.isInitialized = true
    logger.info('Email service initialized')
  }

  async sendEmail<T extends EmailTemplate>(
    to: string | string[],
    template: T,
    data: EmailData<T>,
    options?: {
      scheduledFor?: Date
      userId?: string
    }
  ): Promise<void> {
    try {
      // Validate data against schema
      const schema = emailDataSchemas[template]
      const validatedData = schema.parse(data)

      // Check user email preferences if userId provided
      if (options?.userId) {
        const canSend = await this.checkEmailPreferences(options.userId, template)
        if (!canSend) {
          logger.info(`Email blocked by user preferences: ${template} to ${to}`)
          return
        }
      }

      // Queue the email
      await this.queueEmail(to, template, validatedData, options?.scheduledFor)
    } catch (error) {
      logger.error('Error sending email:', error)
      throw error
    }
  }

  private async checkEmailPreferences(userId: string, template: EmailTemplate): Promise<boolean> {
    const supabase = await createServerClient()
    
    const { data: preferences } = await supabase
      .from('email_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (!preferences) return true // Default to sending if no preferences

    // Map templates to preference categories
    const preferenceMap: Record<EmailTemplate, keyof typeof preferences> = {
      welcome: 'marketing',
      achievement_unlocked: 'achievements',
      goal_reminder: 'progress',
      collaboration_invite: 'collaboration',
      export_ready: 'progress',
      subscription_renewed: 'marketing',
      subscription_cancelled: 'marketing',
      password_reset: 'marketing', // Always send
      email_verification: 'marketing', // Always send
      weekly_progress: 'progress',
      project_shared: 'collaboration',
      comment_notification: 'collaboration',
      account_deleted: 'marketing' // Always send
    }

    // Always send critical emails
    if (['password_reset', 'email_verification', 'account_deleted'].includes(template)) {
      return true
    }

    const category = preferenceMap[template]
    return preferences[category] === true
  }

  private async queueEmail(
    to: string | string[],
    template: EmailTemplate,
    data: any,
    scheduledFor?: Date
  ): Promise<void> {
    const supabase = await createServerClient()
    
    const { error } = await supabase
      .from('email_queue')
      .insert({
        to: Array.isArray(to) ? to.join(',') : to,
        template,
        data,
        scheduled_for: scheduledFor || new Date(),
        status: 'pending'
      })

    if (error) {
      logger.error('Error queueing email:', error)
      throw error
    }
  }

  async processEmailQueue(): Promise<void> {
    const supabase = await createServerClient()
    
    // Get pending emails scheduled for now or earlier
    const { data: emails, error } = await supabase
      .from('email_queue')
      .select('*')
      .eq('status', 'pending')
      .lte('scheduled_for', new Date().toISOString())
      .order('scheduled_for', { ascending: true })
      .limit(10)

    if (error) {
      logger.error('Error fetching email queue:', error)
      return
    }

    if (!emails || emails.length === 0) {
      return
    }

    // Process emails in parallel
    await Promise.all(
      emails.map(email => this.processEmailItem(email as EmailQueueItem))
    )
  }

  private async processEmailItem(item: EmailQueueItem): Promise<void> {
    const supabase = await createServerClient()
    
    try {
      // Update status to processing
      await supabase
        .from('email_queue')
        .update({ status: 'processing', attempts: item.attempts + 1 })
        .eq('id', item.id)

      // Get email content
      const emailContent = this.getEmailContent(item.template, item.data)
      
      // Send email
      const msg = {
        to: item.to.split(','),
        from: {
          email: config.email?.from,
          name: config.email?.fromName
        },
        subject: emailContent.subject,
        text: emailContent.text,
        html: emailContent.html
      }

      await sgMail.send(msg)

      // Update status to sent
      await supabase
        .from('email_queue')
        .update({ 
          status: 'sent', 
          sent_at: new Date().toISOString() 
        })
        .eq('id', item.id)

      // Log successful send
      await supabase
        .from('email_logs')
        .insert({
          to: item.to,
          template: item.template,
          provider: 'sendgrid',
          provider_id: msg.to.toString()
        })

      logger.info(`Email sent successfully: ${item.template} to ${item.to}`)
    } catch (error) {
      logger.error(`Error processing email ${item.id}:`, error)
      
      // Update with error
      await supabase
        .from('email_queue')
        .update({ 
          status: item.attempts >= 3 ? 'failed' : 'pending',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', item.id)
    }
  }

  private getEmailContent(template: EmailTemplate, data: any): {
    subject: string
    text: string
    html: string
  } {
    // In a real implementation, this would use a template engine
    // For now, we'll use simple string templates
    
    const templates = {
      welcome: {
        subject: 'Welcome to BookScribe AI!',
        text: `Hi ${data.userName},\n\nWelcome to BookScribe AI! We're excited to help you write your next masterpiece.\n\nGet started: ${data.loginUrl}\n\nHappy writing!\nThe BookScribe Team`,
        html: `
          <h2>Hi ${data.userName},</h2>
          <p>Welcome to BookScribe AI! We're excited to help you write your next masterpiece.</p>
          <p><a href="${data.loginUrl}" style="background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Get Started</a></p>
          <p>Happy writing!<br>The BookScribe Team</p>
        `
      },
      achievement_unlocked: {
        subject: `🏆 Achievement Unlocked: ${data.achievementName}!`,
        text: `Congratulations ${data.userName}!\n\nYou've unlocked the ${data.achievementTier} achievement: ${data.achievementName}\n\n${data.achievementDescription}\n\nKeep up the great work!`,
        html: `
          <h2>🏆 Congratulations ${data.userName}!</h2>
          <p>You've unlocked a new achievement:</p>
          <div style="background: #F3F4F6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: ${data.achievementTier === 'gold' ? '#F59E0B' : data.achievementTier === 'silver' ? '#6B7280' : '#92400E'};">
              ${data.achievementName}
            </h3>
            <p>${data.achievementDescription}</p>
            <p><strong>Tier:</strong> ${data.achievementTier.charAt(0).toUpperCase() + data.achievementTier.slice(1)}</p>
          </div>
          <p>Keep up the great work!</p>
        `
      },
      collaboration_invite: {
        subject: `${data.inviterName} invited you to collaborate on "${data.projectName}"`,
        text: `${data.inviterName} has invited you to collaborate on their project "${data.projectName}" as a ${data.role}.\n\nAccept invitation: ${data.inviteUrl}`,
        html: `
          <h2>You've been invited to collaborate!</h2>
          <p>${data.inviterName} has invited you to collaborate on their project:</p>
          <h3>"${data.projectName}"</h3>
          <p><strong>Your role:</strong> ${data.role.charAt(0).toUpperCase() + data.role.slice(1)}</p>
          <p><a href="${data.inviteUrl}" style="background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Accept Invitation</a></p>
        `
      }
      // Add more templates as needed
    }

    const content = templates[template] || {
      subject: 'BookScribe AI Notification',
      text: JSON.stringify(data, null, 2),
      html: `<pre>${JSON.stringify(data, null, 2)}</pre>`
    }

    return content
  }

  async testEmailConnection(): Promise<boolean> {
    try {
      await this.initialize()
      
      // Send a test email
      await sgMail.send({
        to: process.env.TEST_EMAIL_ADDRESS || '<EMAIL>',
        from: {
          email: config.email?.from,
          name: config.email?.fromName
        },
        subject: 'BookScribe Email Service Test',
        text: 'This is a test email from BookScribe AI.',
        html: '<p>This is a test email from BookScribe AI.</p>'
      })

      return true
    } catch (error) {
      logger.error('Email connection test failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance()