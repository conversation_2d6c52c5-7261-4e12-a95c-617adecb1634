import { conflictResolver } from '@/lib/services/collaboration-conflict-resolver'

// Mock logger to silence logs during tests
jest.mock('@/lib/services/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}))

describe('CollaborationConflictResolver', () => {
  const range1 = { startLine: 1, startColumn: 1, endLine: 1, endColumn: 1 }
  const range2 = { startLine: 1, startColumn: 2, endLine: 1, endColumn: 2 }

  beforeEach(() => {
    conflictResolver.clearConflicts('doc-success')
    conflictResolver.clearConflicts('doc-fail')
  })

  it('sequentially merges more than two insert changes', async () => {
    const changes = [
      { sessionId: 's1', userId: 'u1', type: 'insert', range: range1, text: 'A', timestamp: 1 },
      { sessionId: 's2', userId: 'u2', type: 'insert', range: range1, text: 'B', timestamp: 2 },
      { sessionId: 's3', userId: 'u3', type: 'insert', range: range1, text: 'C', timestamp: 3 }
    ]

    const merged = await (conflictResolver as any).operationalTransform(changes)
    expect(merged?.text).toBe('ABC')
  })

  it('does not persist conflicts when all changes merge successfully', async () => {
    const changes = [
      { sessionId: 's1', userId: 'u1', type: 'insert', range: range1, text: 'A', timestamp: 1 },
      { sessionId: 's2', userId: 'u2', type: 'insert', range: range1, text: 'B', timestamp: 2 },
      { sessionId: 's3', userId: 'u3', type: 'insert', range: range1, text: 'C', timestamp: 3 }
    ]

    const result = await conflictResolver.resolveConflicts('doc-success', changes)
    expect(result.success).toBe(true)
    expect(result.requiresManualResolution).toBe(false)
    expect(conflictResolver.getPendingConflicts('doc-success')).toHaveLength(0)
  })

  it('persists unresolved conflicts when merging fails', async () => {
    const changes = [
      { sessionId: 's1', userId: 'u1', type: 'insert', range: range1, text: 'A', timestamp: 1 },
      {
        sessionId: 's2',
        userId: 'u2',
        type: 'delete',
        range: { startLine: 1, startColumn: 1, endLine: 1, endColumn: 3 },
        text: '',
        timestamp: 2
      },
      { sessionId: 's3', userId: 'u3', type: 'insert', range: range2, text: 'B', timestamp: 3 }
    ]

    const result = await conflictResolver.resolveConflicts('doc-fail', changes)
    expect(result.success).toBe(false)
    expect(result.requiresManualResolution).toBe(true)
    expect(conflictResolver.getPendingConflicts('doc-fail').length).toBeGreaterThan(0)
  })
})

