#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Applying consistent rate limiting to API endpoints...\n');

// Map of route patterns to rate limiting configurations
const routeRateLimitMap = {
  // AI endpoints - most restrictive
  'ai/chat/route.ts': { type: 'ai-generation', cost: 2 },
  'ai/structured-content/route.ts': { type: 'ai-generation', cost: 2 },
  'ai/stream-content/route.ts': { type: 'ai-generation', cost: 2 },
  'ai/typed-stream/route.ts': { type: 'ai-generation', cost: 2 },
  'ai/suggestions/route.ts': { type: 'ai-analysis', cost: 1 },
  
  // Analysis endpoints - moderate limits
  'analysis/auto-fix/route.ts': { type: 'ai-analysis', cost: 2 },
  'analysis/book-summary/route.ts': { type: 'ai-analysis', cost: 3 },
  'analysis/content/route.ts': { type: 'ai-analysis', cost: 2 },
  'analysis/voice/route.ts': { type: 'ai-analysis', cost: 2 },
  'analysis/voice-consistency/route.ts': { type: 'ai-analysis', cost: 2 },
  
  // Content generation - high cost
  'agents/generate/route.ts': { type: 'ai-generation', cost: 3 },
  'agents/edit/route.ts': { type: 'ai-generation', cost: 2 },
  'agents/initialize/route.ts': { type: 'ai-generation', cost: 1 },
  
  // Webhooks - special handling
  'webhooks/stripe/route.ts': { type: 'webhook' },
  'billing/webhooks/stripe/route.ts': { type: 'webhook' },
  
  // Data export/import - expensive operations
  'projects/[id]/export/route.ts': { type: 'authenticated', cost: 5 },
  'import/docx/route.ts': { type: 'authenticated', cost: 3 },
  'import/epub/route.ts': { type: 'authenticated', cost: 3 },
  'import/pdf/route.ts': { type: 'authenticated', cost: 3 },
  'admin/export-data/route.ts': { type: 'authenticated', cost: 10 },
  
  // Search endpoints - moderate cost
  'search/semantic/route.ts': { type: 'authenticated', cost: 2 },
  'search/index/route.ts': { type: 'authenticated', cost: 3 },
  'search/character-moments/route.ts': { type: 'authenticated', cost: 2 },
  'search/emotion/route.ts': { type: 'authenticated', cost: 2 },
  'search/theme/route.ts': { type: 'authenticated', cost: 2 },
  
  // Default authenticated endpoints
  'projects/route.ts': { type: 'authenticated' },
  'characters/route.ts': { type: 'authenticated' },
  'chapters/route.ts': { type: 'authenticated' },
  'series/route.ts': { type: 'authenticated' },
  'profiles/route.ts': { type: 'authenticated' },
  
  // Public endpoints - less restrictive
  'health/route.ts': { type: 'default' },
  'demo/generate/route.ts': { type: 'default', cost: 2 }
};

// Template for adding rate limiting
const rateLimitTemplate = (config) => {
  const configStr = config.cost 
    ? `{ type: '${config.type}', cost: ${config.cost} }`
    : `{ type: '${config.type}' }`;
  
  return `
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, ${configStr})
    if (rateLimitResponse) {
      return rateLimitResponse
    }
`;
};

// Process files
let updatedCount = 0;
let skippedCount = 0;
let errorCount = 0;
const processedFiles = [];

for (const [routePath, config] of Object.entries(routeRateLimitMap)) {
  const fullPath = path.join(process.cwd(), 'src/app/api', routePath);
  
  try {
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ Not found: ${routePath}`);
      errorCount++;
      continue;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    const originalContent = content;
    
    // Skip if already has rate limiting
    if (content.includes('applyRateLimit') || content.includes('withRateLimit') || content.includes('checkRateLimit')) {
      console.log(`⏭️  Skipped: ${routePath} (already has rate limiting)`);
      skippedCount++;
      continue;
    }
    
    // Add imports if needed
    if (!content.includes("import { applyRateLimit")) {
      // Find the last import
      const lastImportMatch = content.match(/import.*from.*\n/g);
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1];
        const lastImportIndex = content.lastIndexOf(lastImport);
        content = content.substring(0, lastImportIndex + lastImport.length) +
          `import { applyRateLimit } from '@/lib/rate-limiter-unified'\n` +
          content.substring(lastImportIndex + lastImport.length);
      }
    }
    
    // Find export functions and add rate limiting
    let modified = false;
    const methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
    
    for (const method of methods) {
      // Look for UnifiedAuthService.withAuth pattern
      const authPattern = new RegExp(`export\\s+const\\s+${method}\\s*=\\s*UnifiedAuthService\\.withAuth\\s*\\(\\s*async\\s*\\([^)]+\\)\\s*=>\\s*{`, 'g');
      const authMatch = content.match(authPattern);
      
      if (authMatch) {
        // Add rate limiting after the opening brace
        const matchIndex = content.indexOf(authMatch[0]);
        const braceIndex = matchIndex + authMatch[0].length;
        
        // Check if there's already a try block
        const tryIndex = content.indexOf('try {', braceIndex);
        if (tryIndex !== -1 && tryIndex < braceIndex + 100) {
          // Insert after try {
          content = content.substring(0, tryIndex + 5) + 
            rateLimitTemplate(config) + 
            content.substring(tryIndex + 5);
        } else {
          // Insert at the beginning of the function
          content = content.substring(0, braceIndex) + 
            rateLimitTemplate(config) + 
            content.substring(braceIndex);
        }
        modified = true;
      } else {
        // Look for standard export pattern
        const standardPattern = new RegExp(`export\\s+(async\\s+)?function\\s+${method}\\s*\\([^)]+\\)\\s*{`, 'g');
        const standardMatch = content.match(standardPattern);
        
        if (standardMatch) {
          const matchIndex = content.indexOf(standardMatch[0]);
          const braceIndex = matchIndex + standardMatch[0].length;
          
          content = content.substring(0, braceIndex) + 
            rateLimitTemplate(config) + 
            content.substring(braceIndex);
          modified = true;
        }
      }
    }
    
    if (modified) {
      fs.writeFileSync(fullPath, content);
      console.log(`✅ Updated: ${routePath}`);
      updatedCount++;
      processedFiles.push({
        file: routePath,
        config: config
      });
    } else {
      console.log(`⏭️  Skipped: ${routePath} (no matching patterns)`);
      skippedCount++;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${routePath}:`, error.message);
    errorCount++;
  }
}

// Create rate limiting guide
const rateLimitingGuide = `# Rate Limiting Guide

## Overview
All API endpoints should implement rate limiting to prevent abuse and ensure fair usage.

## Configuration Types

### Default
- Window: 15 minutes
- Limit: 100 requests
- Used for: Public endpoints

### Authenticated
- Window: 15 minutes  
- Limit: 1000 requests
- Used for: Protected endpoints requiring auth

### AI Generation
- Window: 60 minutes
- Limit: 20 requests
- Used for: Content generation endpoints

### AI Analysis
- Window: 60 minutes
- Limit: 50 requests
- Used for: Analysis endpoints

### Webhook
- Window: 15 minutes
- Limit: 100 requests  
- Used for: External webhook endpoints

## Implementation Examples

### Basic Rate Limiting
\`\`\`typescript
import { applyRateLimit } from '@/lib/rate-limiter-unified'

export const GET = async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResponse = await applyRateLimit(request, { type: 'authenticated' })
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  
  // Continue with request processing
}
\`\`\`

### Rate Limiting with Cost
\`\`\`typescript
// For expensive operations
const rateLimitResponse = await applyRateLimit(request, { 
  type: 'ai-generation', 
  cost: 2 // Counts as 2 requests
})
\`\`\`

### Combined with Auth
\`\`\`typescript
export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Apply rate limiting after auth
  const rateLimitResponse = await applyRateLimit(request, { 
    type: 'authenticated' 
  })
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  
  // Process request
})
\`\`\`

## Cost Guidelines

- **Cost 1** (default): Simple operations
- **Cost 2**: Moderate AI operations, complex queries
- **Cost 3**: Heavy AI generation, bulk operations
- **Cost 5**: Export operations, data dumps
- **Cost 10**: Admin operations, full exports

## Headers

Rate limit information is included in response headers:
- \`X-RateLimit-Limit\`: Request limit
- \`X-RateLimit-Remaining\`: Remaining requests
- \`X-RateLimit-Reset\`: Reset timestamp
- \`Retry-After\`: Seconds until retry (on 429)

## Best Practices

1. Always apply rate limiting before expensive operations
2. Use appropriate cost multipliers for resource-intensive endpoints
3. Consider different limits for authenticated vs public endpoints
4. Provide clear error messages when limits are exceeded
5. Log rate limit violations for monitoring
`;

fs.writeFileSync(
  path.join(process.cwd(), 'docs/api/rate-limiting-guide.md'),
  rateLimitingGuide
);

// Save implementation log
const implementationLog = {
  timestamp: new Date().toISOString(),
  summary: {
    updated: updatedCount,
    skipped: skippedCount,
    errors: errorCount,
    total: Object.keys(routeRateLimitMap).length
  },
  processed: processedFiles,
  configurations: {
    aiGeneration: routeRateLimitMap.filter(([k, v]) => v.type === 'ai-generation').length,
    aiAnalysis: routeRateLimitMap.filter(([k, v]) => v.type === 'ai-analysis').length,
    authenticated: routeRateLimitMap.filter(([k, v]) => v.type === 'authenticated').length,
    webhook: routeRateLimitMap.filter(([k, v]) => v.type === 'webhook').length,
    default: routeRateLimitMap.filter(([k, v]) => v.type === 'default').length
  }
};

fs.writeFileSync(
  path.join(process.cwd(), 'scripts/rate-limiting-implementation.json'),
  JSON.stringify(implementationLog, null, 2)
);

// Summary
console.log('\n📊 Summary:');
console.log(`   Files updated: ${updatedCount}`);
console.log(`   Files skipped: ${skippedCount}`);
console.log(`   Errors: ${errorCount}`);
console.log(`   Total processed: ${Object.keys(routeRateLimitMap).length}`);

console.log('\n📝 Next Steps:');
console.log('1. Review the applied rate limiting configurations');
console.log('2. Test rate limiting on critical endpoints');
console.log('3. Monitor rate limit metrics in production');
console.log('4. Adjust limits based on usage patterns');
console.log('5. See docs/api/rate-limiting-guide.md for details');

console.log('\n✅ Rate limiting script completed!');