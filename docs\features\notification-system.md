# BookScribe Notification System

## Overview

The Notification System provides real-time and asynchronous notifications to keep users informed about important events, collaboration updates, system alerts, and achievement milestones. It supports multiple delivery channels and user preferences for notification management.

## Architecture

### Database Schema

#### Notifications Table
Core notification storage:

```sql
notifications:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - type: TEXT - notification category
  - title: TEXT - Notification title
  - message: TEXT - Notification content
  - data: JSONB - Additional context data
  - read: BOOLEAN - Read status (default: false)
  - read_at: TIMESTAMPTZ - When marked as read
  - priority: VARCHAR(20) - low, medium, high, critical
  - expires_at: TIMESTAMPTZ - Expiration time
  - action_url: TEXT - Link to relevant content
  - action_type: VARCHAR(50) - navigate, modal, external
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Notification Preferences Table
User notification settings:

```sql
notification_preferences:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - channel: VARCHAR(50) - in_app, email, push
  - category: VARCHAR(50) - Notification type
  - enabled: BOOLEAN - Channel enabled
  - frequency: VARCHAR(20) - instant, hourly, daily, weekly
  - quiet_hours: JSONB - Do not disturb settings
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
  - UNIQUE(user_id, channel, category)
```

#### Notification Queue Table
For async processing:

```sql
notification_queue:
  - id: UUID (Primary Key)
  - notification_id: UUID - References notifications
  - channel: VARCHAR(50) - Delivery channel
  - status: VARCHAR(20) - pending, processing, sent, failed
  - attempts: INTEGER - Delivery attempts
  - last_attempt_at: TIMESTAMPTZ
  - error_message: TEXT
  - scheduled_for: TIMESTAMPTZ
  - created_at: TIMESTAMPTZ
```

## Notification Types

### 1. Collaboration Notifications
- Project invitation received
- Collaborator joined project
- Comment on chapter
- Chapter updated by collaborator
- Real-time editing conflicts

### 2. Achievement Notifications
- Achievement unlocked
- Progress milestone reached
- New achievement available
- Leaderboard position change

### 3. Writing Goal Notifications
- Daily writing reminder
- Goal progress update
- Streak at risk warning
- Goal completed celebration

### 4. System Notifications
- Subscription renewal
- Payment issues
- System maintenance
- New feature announcements
- Security alerts

### 5. AI Agent Notifications
- Generation complete
- Agent task finished
- Error in processing
- Context limit warnings

### 6. Project Notifications
- Export completed
- Import successful
- Version conflict detected
- Backup created

## API Endpoints

### Notification Management

#### GET /api/notifications
Retrieve user notifications:

```typescript
// Request
GET /api/notifications?unread=true&limit=20&offset=0

// Response
{
  notifications: [
    {
      id: "uuid",
      type: "collaboration",
      title: "New comment on Chapter 5",
      message: "Sarah commented on your latest chapter",
      data: {
        project_id: "uuid",
        chapter_id: "uuid",
        comment_id: "uuid",
        commenter_name: "Sarah",
        comment_preview: "I love the character development here..."
      },
      read: false,
      priority: "medium",
      action_url: "/projects/uuid/chapters/5#comment-uuid",
      action_type: "navigate",
      created_at: "2024-01-15T10:30:00Z"
    }
  ],
  total: 45,
  unread_count: 12
}
```

#### POST /api/notifications/{id}/read
Mark notification as read:

```typescript
// Response
{
  id: "uuid",
  read: true,
  read_at: "2024-01-15T10:35:00Z"
}
```

#### POST /api/notifications/mark-all-read
Mark all notifications as read:

```typescript
// Response
{
  updated_count: 12,
  success: true
}
```

#### DELETE /api/notifications/{id}
Delete a notification:

```typescript
// Response
{
  success: true
}
```

### Notification Preferences

#### GET /api/notifications/preferences
Get user preferences:

```typescript
// Response
{
  preferences: [
    {
      channel: "email",
      category: "collaboration",
      enabled: true,
      frequency: "instant",
      quiet_hours: {
        enabled: true,
        start: "22:00",
        end: "08:00",
        timezone: "America/New_York"
      }
    }
  ]
}
```

#### PUT /api/notifications/preferences
Update preferences:

```typescript
// Request
{
  channel: "email",
  category: "collaboration",
  enabled: false
}
```

## Notification Delivery

### In-App Notifications
Real-time delivery via WebSocket:

```typescript
// WebSocket message
{
  type: "notification",
  payload: {
    id: "uuid",
    title: "New achievement unlocked!",
    message: "You've written 10,000 words",
    priority: "high",
    action: {
      type: "modal",
      component: "AchievementModal",
      props: { achievementId: "uuid" }
    }
  }
}
```

### Email Notifications
Template-based email delivery:

```typescript
interface EmailNotification {
  to: string;
  subject: string;
  template: 'collaboration' | 'achievement' | 'goal' | 'system';
  data: Record<string, any>;
  attachments?: Attachment[];
}
```

### Push Notifications
Browser/mobile push support:

```typescript
interface PushNotification {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: Record<string, any>;
  actions?: NotificationAction[];
}
```

## Notification Templates

### Email Templates
```html
<!-- collaboration-invite.html -->
<h2>You've been invited to collaborate!</h2>
<p>{{inviter_name}} has invited you to collaborate on "{{project_title}}"</p>
<p>Role: {{role}}</p>
<a href="{{accept_url}}">Accept Invitation</a>
```

### In-App Templates
```tsx
// Achievement notification component
<NotificationCard
  icon={<TrophyIcon />}
  title="Achievement Unlocked!"
  message={`You've earned "${achievement.name}"`}
  action={{
    label: "View Achievement",
    onClick: () => navigate(`/achievements/${achievement.id}`)
  }}
  dismissible={true}
/>
```

## UI Components

### Notification Bell
```tsx
<NotificationBell
  unreadCount={unreadCount}
  onClick={toggleNotificationPanel}
  showBadge={true}
  pulseOnNew={true}
/>
```

### Notification Panel
```tsx
<NotificationPanel
  isOpen={isPanelOpen}
  notifications={notifications}
  onClose={closePanel}
  onNotificationClick={handleNotificationClick}
  onMarkAllRead={markAllAsRead}
  groupByDate={true}
/>
```

### Notification Toast
```tsx
<NotificationToast
  notification={newNotification}
  position="top-right"
  duration={5000}
  onAction={handleAction}
  onDismiss={handleDismiss}
/>
```

### Preference Manager
```tsx
<NotificationPreferences
  preferences={userPreferences}
  onChange={updatePreferences}
  showAdvanced={true}
/>
```

## Real-time Implementation

### WebSocket Integration
```typescript
// Client-side listener
socket.on('notification', (notification: Notification) => {
  // Add to notification store
  notificationStore.add(notification);
  
  // Show toast if enabled
  if (shouldShowToast(notification)) {
    showNotificationToast(notification);
  }
  
  // Update unread count
  updateUnreadCount();
});
```

### Server-side Broadcasting
```typescript
// Broadcast notification
async function sendNotification(userId: string, notification: Notification) {
  // Save to database
  const saved = await saveNotification(notification);
  
  // Queue for channels
  await queueForDelivery(saved, getUserChannels(userId));
  
  // Broadcast via WebSocket
  io.to(`user:${userId}`).emit('notification', saved);
}
```

## Performance Optimization

### Database Indexes
```sql
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notification_queue_status ON notification_queue(status);
```

### Batching Strategy
- Group email notifications by frequency
- Batch database writes
- Aggregate similar notifications

### Cleanup Strategy
```sql
-- Archive old notifications
CREATE OR REPLACE FUNCTION archive_old_notifications()
RETURNS void AS $$
BEGIN
  INSERT INTO notifications_archive
  SELECT * FROM notifications
  WHERE created_at < NOW() - INTERVAL '30 days'
  AND read = true;
  
  DELETE FROM notifications
  WHERE created_at < NOW() - INTERVAL '30 days'
  AND read = true;
END;
$$ LANGUAGE plpgsql;
```

## Security

### Row Level Security
```sql
-- Users can only see their own notifications
CREATE POLICY "Users view own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own notifications
CREATE POLICY "Users update own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- System can create notifications
CREATE POLICY "System creates notifications" ON notifications
  FOR INSERT WITH CHECK (true);
```

### Rate Limiting
- Notification creation rate limited per user
- Email sending rate limited
- Push notification throttling

## Error Handling

### Delivery Failures
```typescript
interface DeliveryError {
  notification_id: string;
  channel: string;
  error: string;
  retry_count: number;
  next_retry?: Date;
}

// Retry strategy
const retryDelays = [
  60,    // 1 minute
  300,   // 5 minutes
  900,   // 15 minutes
  3600   // 1 hour
];
```

### Fallback Mechanisms
- If push fails, try in-app
- If email bounces, notify in-app
- Log all failures for analysis

## Analytics

### Notification Metrics
Track for optimization:
- Delivery success rates
- Read rates by type
- Time to read
- Action conversion rates
- Unsubscribe rates

### User Engagement
```typescript
interface NotificationAnalytics {
  user_id: string;
  notification_type: string;
  delivered_at: Date;
  read_at?: Date;
  action_taken?: string;
  action_at?: Date;
  channel: string;
}
```

## Future Enhancements

1. **Smart Notifications**
   - ML-based delivery timing
   - Personalized frequency
   - Content summarization

2. **Rich Media**
   - Image attachments
   - Video previews
   - Interactive cards

3. **Advanced Channels**
   - SMS notifications
   - Slack integration
   - Discord webhooks

4. **Notification Center**
   - Full notification history
   - Advanced filtering
   - Bulk actions

## Related Systems
- Email System (delivery channel)
- Achievement System (achievement notifications)
- Collaboration System (team notifications)
- Writing Goals (reminder notifications)