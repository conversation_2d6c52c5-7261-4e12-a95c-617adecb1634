'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { logger } from '@/lib/services/logger'

/**
 * Component that handles pending invitations after user login
 * Checks localStorage for pending invitation tokens and redirects user
 */
export function PendingInvitationHandler() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!user || loading) return

    // Check for pending invitation token in localStorage
    const pendingInvitation = localStorage.getItem('pendingInvitation')
    
    if (pendingInvitation) {
      // Clear the pending invitation
      localStorage.removeItem('pendingInvitation')
      
      // Redirect to invitation acceptance page
      logger.info('Redirecting user to pending invitation', { token: pendingInvitation })
      router.push(`/invite/${pendingInvitation}`)
    }
  }, [user, loading, router])

  // This component doesn't render anything
  return null
}