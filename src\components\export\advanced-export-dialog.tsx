'use client'

import { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import { 
  Download, 
  FileText, 
  FileType, 
  Book, 
  File,
  Settings,
  Loader2,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import type { ExportOptions, ExportPreset, EXPORT_PRESETS } from '@/lib/export/export-service'

interface AdvancedExportDialogProps {
  projectId: string
  projectTitle: string
  tier?: string
  chapters?: Array<{
    id: string
    chapter_number: number
    title?: string
    word_count?: number
  }>
  children?: React.ReactNode
}

const formatIcons = {
  txt: FileText,
  markdown: FileType,
  docx: FileType,
  pdf: File,
  epub: Book,
}

const exportPresets: Record<string, ExportPreset> = {
  manuscript: {
    id: 'manuscript',
    name: 'Manuscript (Standard Submission)',
    format: 'docx',
    settings: {
      includeMetadata: true,
      includeFrontMatter: true,
      includeChapterBreaks: true,
      includeTableOfContents: false,
      includePageNumbers: true,
      includeHeaders: true,
      chapterTitleFormat: 'uppercase',
      customStyling: {
        fontFamily: 'Times New Roman',
        fontSize: 12,
        lineSpacing: 2,
        margins: { top: 1, bottom: 1, left: 1, right: 1 }
      },
      pageFormat: 'letter'
    }
  },
  ebook: {
    id: 'ebook',
    name: 'E-book (Digital Publishing)',
    format: 'epub',
    settings: {
      includeMetadata: true,
      includeFrontMatter: true,
      includeChapterBreaks: true,
      includeTableOfContents: true,
      sceneBreakSymbol: '* * *',
      customStyling: {
        fontFamily: 'Georgia',
        fontSize: 14,
        lineSpacing: 1.5,
        margins: { top: 0.5, bottom: 0.5, left: 0.5, right: 0.5 }
      }
    }
  },
  printReady: {
    id: 'printReady',
    name: 'Print-Ready (POD)',
    format: 'pdf',
    settings: {
      includeMetadata: false,
      includeFrontMatter: true,
      includeChapterBreaks: true,
      includeTableOfContents: true,
      includePageNumbers: true,
      includeHeaders: false,
      includeFooters: true,
      chapterTitleFormat: 'capitalize',
      customStyling: {
        fontFamily: 'Garamond',
        fontSize: 11,
        lineSpacing: 1.4,
        margins: { top: 0.75, bottom: 0.75, left: 0.625, right: 0.625 }
      },
      pageFormat: 'trade'
    }
  }
}

export function AdvancedExportDialog({
  projectId,
  projectTitle,
  tier = 'free',
  chapters = [],
  children
}: AdvancedExportDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedPreset, setSelectedPreset] = useState<string>('manuscript')
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'docx',
    ...exportPresets.manuscript.settings
  })
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [exportMessage, setExportMessage] = useState('')
  const { toast } = useToast()

  const handlePresetChange = (presetId: string) => {
    setSelectedPreset(presetId)
    const preset = exportPresets[presetId]
    if (preset) {
      setExportOptions({
        format: preset.format,
        ...preset.settings,
        preset: preset
      })
    }
  }

  const handleExport = async () => {
    setIsExporting(true)
    setExportProgress(0)
    setExportMessage('Preparing export...')

    try {
      const response = await fetch(`/api/projects/${projectId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...exportOptions,
          progressCallback: (progress: number, message: string) => {
            setExportProgress(progress)
            setExportMessage(message)
          }
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Export failed')
      }

      setExportProgress(100)
      setExportMessage('Export complete!')

      // Get the filename from the response
      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition?.match(/filename="(.+)"/)?.[1] || 
        `${projectTitle}-export.${exportOptions.format}`

      // Create a blob from the response
      const blob = await response.blob()

      // Download the file
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: 'Export successful',
        description: `Your project has been exported as ${exportOptions.format.toUpperCase()}.`,
      })

      setTimeout(() => {
        setIsOpen(false)
        setExportProgress(0)
        setExportMessage('')
      }, 2000)
    } catch (error) {
      toast({
        title: 'Export failed',
        description: error instanceof Error ? error.message : 'Failed to export project',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  const updateOption = useCallback(<K extends keyof ExportOptions>(key: K, value: ExportOptions[K]) => {
    setExportOptions(prev => ({ ...prev, [key]: value }))
  }, [])

  const updateCustomStyling = useCallback((key: keyof NonNullable<ExportOptions['customStyling']>, value: string | number) => {
    setExportOptions(prev => ({
      ...prev,
      customStyling: {
        ...prev.customStyling,
        [key]: value
      }
    }))
  }, [])

  const updateMargins = useCallback((side: 'top' | 'bottom' | 'left' | 'right', value: number) => {
    setExportOptions(prev => ({
      ...prev,
      customStyling: {
        ...prev.customStyling,
        margins: {
          ...prev.customStyling?.margins,
          [side]: value
        }
      }
    }))
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Advanced Export
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Advanced Export Options</DialogTitle>
          <DialogDescription>
            Customize your export with professional formatting options
          </DialogDescription>
        </DialogHeader>

        {isExporting ? (
          <div className="py-6 sm:py-8 lg:py-10">
            <div className="space-y-4">
              <div className="flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
              <div className="space-y-2">
                <p className="text-center text-sm font-medium">{exportMessage}</p>
                <Progress value={exportProgress} className="w-full" />
                <p className="text-center text-xs text-muted-foreground">
                  {exportProgress.toFixed(0)}% complete
                </p>
              </div>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="preset" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="preset">Presets</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="formatting">Formatting</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="preset" className="space-y-4">
              <div className="space-y-4">
                <Label>Export Preset</Label>
                <div className="grid gap-3">
                  {Object.entries(exportPresets).map(([id, preset]) => (
                    <Card 
                      key={id}
                      className={`cursor-pointer transition-all ${
                        selectedPreset === id 
                          ? 'ring-2 ring-primary border-primary' 
                          : 'hover:border-primary/50'
                      }`}
                      onClick={() => handlePresetChange(id)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{preset.name}</CardTitle>
                          <Badge variant="outline">{preset.format.toUpperCase()}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">
                          {id === 'manuscript' && 'Standard manuscript format with double-spaced Times New Roman'}
                          {id === 'ebook' && 'Optimized for e-readers with proper navigation and formatting'}
                          {id === 'printReady' && 'Professional layout for print-on-demand services'}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="rounded-lg border bg-muted/50 p-4">
                  <div className="flex gap-2">
                    <Info className="h-5 w-5 text-muted-foreground shrink-0" />
                    <div className="text-sm">
                      <p className="font-medium">Custom Format</p>
                      <p className="text-muted-foreground mt-1">
                        You can also customize any preset using the other tabs
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="metadata">Include Metadata</Label>
                  <Switch
                    id="metadata"
                    checked={exportOptions.includeMetadata}
                    onCheckedChange={(checked) => updateOption('includeMetadata', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="frontmatter">Include Front Matter</Label>
                  <Switch
                    id="frontmatter"
                    checked={exportOptions.includeFrontMatter}
                    onCheckedChange={(checked) => updateOption('includeFrontMatter', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="toc">Table of Contents</Label>
                  <Switch
                    id="toc"
                    checked={exportOptions.includeTableOfContents}
                    onCheckedChange={(checked) => updateOption('includeTableOfContents', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="chapter-breaks">Chapter Page Breaks</Label>
                  <Switch
                    id="chapter-breaks"
                    checked={exportOptions.includeChapterBreaks}
                    onCheckedChange={(checked) => updateOption('includeChapterBreaks', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Scene Break Symbol</Label>
                  <Input
                    value={exportOptions.sceneBreakSymbol || '* * *'}
                    onChange={(e) => updateOption('sceneBreakSymbol', e.target.value)}
                    placeholder="* * *"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="formatting" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label>Font Family</Label>
                  <Select
                    value={exportOptions.customStyling?.fontFamily}
                    onValueChange={(value) => updateCustomStyling('fontFamily', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                      <SelectItem value="Arial">Arial</SelectItem>
                      <SelectItem value="Georgia">Georgia</SelectItem>
                      <SelectItem value="Garamond">Garamond</SelectItem>
                      <SelectItem value="Courier New">Courier New</SelectItem>
                      <SelectItem value="serif">Serif (Default)</SelectItem>
                      <SelectItem value="sans-serif">Sans Serif</SelectItem>
                      <SelectItem value="monospace">Monospace</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Font Size: {exportOptions.customStyling?.fontSize}pt</Label>
                  <Slider
                    value={[exportOptions.customStyling?.fontSize || 12]}
                    onValueChange={([value]) => updateCustomStyling('fontSize', value)}
                    min={8}
                    max={18}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Line Spacing: {exportOptions.customStyling?.lineSpacing}</Label>
                  <Slider
                    value={[exportOptions.customStyling?.lineSpacing || 1.5]}
                    onValueChange={([value]) => updateCustomStyling('lineSpacing', value)}
                    min={1}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Page Format</Label>
                  <Select
                    value={exportOptions.pageFormat}
                    onValueChange={(value) => updateOption('pageFormat', value as ExportOptions['pageFormat'])}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="letter">Letter (8.5" × 11")</SelectItem>
                      <SelectItem value="a4">A4 (210mm × 297mm)</SelectItem>
                      <SelectItem value="trade">Trade (6" × 9")</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Chapter Title Format</Label>
                  <Select
                    value={exportOptions.chapterTitleFormat || 'normal'}
                    onValueChange={(value) => updateOption('chapterTitleFormat', value as ExportOptions['chapterTitleFormat'])}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="uppercase">UPPERCASE</SelectItem>
                      <SelectItem value="capitalize">Title Case</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Margins (inches)</Label>
                  <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                    <div>
                      <Label className="text-xs">Top</Label>
                      <Slider
                        value={[exportOptions.customStyling?.margins?.top || 1]}
                        onValueChange={([value]) => updateMargins('top', value)}
                        min={0.5}
                        max={2}
                        step={0.125}
                        className="mt-1"
                      />
                      <span className="text-xs text-muted-foreground">
                        {exportOptions.customStyling?.margins?.top || 1}"
                      </span>
                    </div>
                    <div>
                      <Label className="text-xs">Bottom</Label>
                      <Slider
                        value={[exportOptions.customStyling?.margins?.bottom || 1]}
                        onValueChange={([value]) => updateMargins('bottom', value)}
                        min={0.5}
                        max={2}
                        step={0.125}
                        className="mt-1"
                      />
                      <span className="text-xs text-muted-foreground">
                        {exportOptions.customStyling?.margins?.bottom || 1}"
                      </span>
                    </div>
                    <div>
                      <Label className="text-xs">Left</Label>
                      <Slider
                        value={[exportOptions.customStyling?.margins?.left || 1]}
                        onValueChange={([value]) => updateMargins('left', value)}
                        min={0.5}
                        max={2}
                        step={0.125}
                        className="mt-1"
                      />
                      <span className="text-xs text-muted-foreground">
                        {exportOptions.customStyling?.margins?.left || 1}"
                      </span>
                    </div>
                    <div>
                      <Label className="text-xs">Right</Label>
                      <Slider
                        value={[exportOptions.customStyling?.margins?.right || 1]}
                        onValueChange={([value]) => updateMargins('right', value)}
                        min={0.5}
                        max={2}
                        step={0.125}
                        className="mt-1"
                      />
                      <span className="text-xs text-muted-foreground">
                        {exportOptions.customStyling?.margins?.right || 1}"
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="page-numbers">Include Page Numbers</Label>
                  <Switch
                    id="page-numbers"
                    checked={exportOptions.includePageNumbers}
                    onCheckedChange={(checked) => updateOption('includePageNumbers', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="headers">Include Headers</Label>
                  <Switch
                    id="headers"
                    checked={exportOptions.includeHeaders}
                    onCheckedChange={(checked) => updateOption('includeHeaders', checked)}
                  />
                </div>

                {exportOptions.includeHeaders && (
                  <div className="space-y-2 ml-6">
                    <Label>Header Text</Label>
                    <Input
                      value={exportOptions.headerText || projectTitle}
                      onChange={(e) => updateOption('headerText', e.target.value)}
                      placeholder="Header text"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <Label htmlFor="footers">Include Footers</Label>
                  <Switch
                    id="footers"
                    checked={exportOptions.includeFooters}
                    onCheckedChange={(checked) => updateOption('includeFooters', checked)}
                  />
                </div>

                {exportOptions.includeFooters && (
                  <div className="space-y-2 ml-6">
                    <Label>Footer Text</Label>
                    <Input
                      value={exportOptions.footerText || ''}
                      onChange={(e) => updateOption('footerText', e.target.value)}
                      placeholder="Footer text (optional)"
                    />
                  </div>
                )}

                <div className="rounded-lg border bg-muted/50 p-4">
                  <h4 className="font-medium mb-2">Export Summary</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>Format: <span className="font-medium text-foreground">{exportOptions.format.toUpperCase()}</span></p>
                    <p>Chapters: <span className="font-medium text-foreground">{chapters.length}</span></p>
                    <p>Total Words: <span className="font-medium text-foreground">
                      {chapters.reduce((sum, ch) => sum + (ch.word_count || 0), 0).toLocaleString()}
                    </span></p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}

        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}