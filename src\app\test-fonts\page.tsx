'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { SettingsButton } from '@/components/settings/settings-modal';
import { Card } from '@/components/ui/card';

export default function TestFontsPage() {
  return (
    <div className="container-wide mx-auto p-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-4xl font-literary-display">Font Test Page</h1>
        <SettingsButton />
      </div>

      <div className="space-y-6">
        <Card className="p-6">
          <h2 className="text-2xl font-literary mb-4">Typography Test</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Body Text (UI Font)</h3>
              <p className="font-ui">This paragraph uses the UI font. The quick brown fox jumps over the lazy dog. 1234567890</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">Editor Font</h3>
              <p className="font-editor">{`function writeCode() { return 'This uses the editor font'; } // 1234567890`}</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">Reading Font</h3>
              <p className="font-reading">This elegant paragraph uses the reading font, perfect for manuscripts and long-form content. The quick brown fox jumps over the lazy dog.</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-2xl font-literary mb-4">Font Sizes</h2>
          <div className="space-y-2">
            <p className="text-xs">Extra Small Text (12px)</p>
            <p className="text-sm">Small Text (14px)</p>
            <p className="text-base">Base Text (16px)</p>
            <p className="text-lg">Large Text (18px)</p>
            <p className="text-xl">Extra Large Text (20px)</p>
            <p className="text-2xl">2XL Text (24px)</p>
            <p className="text-3xl">3XL Text (30px)</p>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-2xl font-literary mb-4">Button Variants</h2>
          <div className="flex flex-wrap gap-4 sm:gap-5 lg:gap-6">
            <Button variant="default">Default Button</Button>
            <Button variant="literary">Literary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="secondary">Secondary Button</Button>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-2xl font-literary mb-4">Instructions</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>Click the settings button in the top right</li>
            <li>Navigate to the Typography tab</li>
            <li>Try changing the text size (small, medium, large, extra-large, or custom)</li>
            <li>Try changing the different font families</li>
            <li>Watch how the text on this page updates in real-time</li>
          </ol>
        </Card>
      </div>
    </div>
  );
}