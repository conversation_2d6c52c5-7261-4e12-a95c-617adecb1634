# BookScribe Timeline Events System

## Overview

The Timeline Events System provides comprehensive timeline management for both individual projects and shared universes. It helps authors maintain chronological consistency, track plot events, and visualize story progression across multiple books in a series.

## Architecture

### Database Schema

#### Universe Timeline Events Table
Manages timeline events at the universe level:

```sql
universe_timeline_events:
  - id: UUID (Primary Key)
  - universe_id: UUID - References universes
  - event_date: DATE - When event occurs
  - event_time: TIME - Optional specific time
  - title: VARCHAR(255) - Event title
  - description: TEXT - Detailed description
  - importance: VARCHAR(20) - minor, major, critical
  - event_type: VARCHAR(50) - plot, character, world, historical
  - affected_characters: UUID[] - Character IDs
  - affected_locations: UUID[] - Location IDs
  - visibility: VARCHAR(20) - public, series, private
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Plot Threads Table
Tracks interconnected story elements:

```sql
plot_threads:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - series_id: UUID - References series
  - title: VA<PERSON>HA<PERSON>(255) - Thread name
  - description: TEXT - Thread description
  - thread_type: VARCHAR(50) - main_plot, subplot, character_arc, mystery
  - status: VARCHAR(20) - active, resolved, abandoned, paused
  - start_chapter: INTEGER - Where thread begins
  - end_chapter: INTEGER - Where thread resolves
  - related_characters: UUID[] - Involved characters
  - related_locations: UUID[] - Relevant locations
  - resolution_notes: TEXT
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

## Timeline Event Types

### 1. Plot Events
Major story occurrences:
- Battle/conflict events
- Discovery/revelation moments
- Quest milestones
- Turning points

### 2. Character Events
Character-specific moments:
- Birth/death dates
- Relationship changes
- Personal achievements
- Character transformations

### 3. World Events
Universe-wide occurrences:
- Natural disasters
- Political changes
- Technological advances
- Cultural shifts

### 4. Historical Events
Background timeline:
- Past events referenced
- Historical context
- Lore establishment
- Prophecy markers

## API Endpoints

### Timeline Management

#### GET /api/timeline/events
Retrieve timeline events:

```typescript
// Request
GET /api/timeline/events?universe_id=uuid&date_from=2024-01-01&date_to=2024-12-31

// Response
{
  events: [
    {
      id: "uuid",
      event_date: "2024-06-15",
      event_time: "14:30:00",
      title: "The Great Council Meeting",
      description: "All kingdoms gather to discuss the growing threat",
      importance: "critical",
      event_type: "plot",
      affected_characters: ["char_id_1", "char_id_2"],
      affected_locations: ["loc_id_1"],
      book_reference: {
        series_id: "uuid",
        book_number: 2,
        chapter: 15
      }
    }
  ],
  total: 45
}
```

#### POST /api/timeline/events
Create new timeline event:

```typescript
// Request
{
  universe_id: "uuid",
  event_date: "2024-06-15",
  title: "The Great Council Meeting",
  description: "All kingdoms gather...",
  importance: "critical",
  event_type: "plot",
  affected_characters: ["uuid1", "uuid2"],
  affected_locations: ["uuid3"]
}
```

#### POST /api/timeline/validate
Validate timeline consistency:

```typescript
// Request
{
  universe_id: "uuid",
  check_type: "conflicts" | "continuity" | "complete"
}

// Response
{
  valid: false,
  issues: [
    {
      type: "character_conflict",
      severity: "error",
      description: "Character appears in two locations on same date",
      events: ["event_id_1", "event_id_2"],
      suggestion: "Adjust timing or location of events"
    }
  ],
  warnings: [
    {
      type: "pacing",
      description: "Large gap between major events (45 days)",
      suggestion: "Consider adding subplot events"
    }
  ]
}
```

#### POST /api/timeline/autofix
Automatically fix timeline issues:

```typescript
// Request
{
  universe_id: "uuid",
  issues: ["issue_id_1", "issue_id_2"],
  strategy: "minimal" | "aggressive"
}

// Response
{
  fixed: [
    {
      issue_id: "issue_id_1",
      action: "shifted_date",
      old_value: "2024-06-15",
      new_value: "2024-06-16"
    }
  ],
  failed: [],
  manual_review_required: ["issue_id_3"]
}
```

### Plot Thread Management

#### GET /api/projects/{id}/plot-threads
Get project plot threads:

```typescript
// Response
{
  threads: [
    {
      id: "uuid",
      title: "The Missing Artifact",
      thread_type: "main_plot",
      status: "active",
      start_chapter: 3,
      current_chapter: 15,
      completion_percentage: 65,
      related_events: ["event_1", "event_2"]
    }
  ]
}
```

#### POST /api/projects/{id}/plot-threads
Create new plot thread:

```typescript
// Request
{
  title: "The Missing Artifact",
  description: "Search for the ancient relic",
  thread_type: "main_plot",
  start_chapter: 3,
  related_characters: ["uuid1", "uuid2"]
}
```

## Timeline Visualization

### Calendar View
```typescript
interface CalendarEvent {
  date: Date;
  events: TimelineEvent[];
  density: 'sparse' | 'normal' | 'busy';
  conflicts: boolean;
}
```

### Timeline View
```typescript
interface TimelineView {
  scale: 'days' | 'weeks' | 'months' | 'years';
  range: DateRange;
  tracks: {
    characters: CharacterTrack[];
    locations: LocationTrack[];
    plotThreads: PlotTrack[];
  };
}
```

### Gantt Chart View
For plot thread progression:
```typescript
interface GanttData {
  threads: {
    id: string;
    name: string;
    start: Date;
    end?: Date;
    progress: number;
    dependencies: string[];
  }[];
}
```

## Consistency Validation

### Validation Rules

#### Character Consistency
- Character can't be in two places simultaneously
- Death dates prevent future appearances
- Age must progress logically
- Relationship timelines must align

#### Location Consistency
- Travel time between locations
- Location existence periods
- Environmental constraints
- Political boundary changes

#### Plot Consistency
- Cause and effect relationships
- Information availability
- Technology/magic limitations
- Cultural progression

### Validation Engine
```typescript
interface ValidationRule {
  id: string;
  type: 'character' | 'location' | 'plot' | 'world';
  severity: 'error' | 'warning' | 'info';
  check: (timeline: Timeline) => ValidationResult[];
}

interface ValidationResult {
  rule_id: string;
  affected_events: string[];
  message: string;
  suggestion?: string;
  auto_fixable: boolean;
}
```

## UI Components

### Timeline Editor
```tsx
<TimelineEditor
  universeId={universeId}
  viewMode="calendar" | "timeline" | "list"
  onEventCreate={handleCreate}
  onEventEdit={handleEdit}
  showValidation={true}
/>
```

### Plot Thread Manager
```tsx
<PlotThreadManager
  projectId={projectId}
  threads={plotThreads}
  onThreadUpdate={handleUpdate}
  visualMode="gantt" | "kanban"
/>
```

### Event Inspector
```tsx
<EventInspector
  event={selectedEvent}
  showRelationships={true}
  allowEdit={userCanEdit}
  onSave={handleSave}
/>
```

## Integration Features

### Character Integration
- Automatic character age calculation
- Relationship timeline tracking
- Character availability checking
- Death/birth event creation

### Location Integration
- Travel time calculations
- Location availability
- Environmental events
- Political changes

### Series Integration
- Cross-book timeline viewing
- Series-wide event search
- Continuity reports
- Reading order validation

## Performance Optimization

### Indexes
```sql
CREATE INDEX idx_universe_timeline_universe_date 
  ON universe_timeline_events(universe_id, event_date);
CREATE INDEX idx_plot_threads_project 
  ON plot_threads(project_id);
CREATE INDEX idx_plot_threads_series 
  ON plot_threads(series_id);
CREATE INDEX idx_timeline_events_importance 
  ON universe_timeline_events(importance) 
  WHERE importance = 'critical';
```

### Caching Strategy
- Timeline view cached per universe
- Validation results cached until changes
- Frequently accessed events in memory

## Security

### Row Level Security
- Universe members can view/edit events
- Project owners control plot threads
- Visibility settings respected
- Audit trail for changes

### Permissions
```sql
-- View timeline events for accessible universes
CREATE POLICY "View timeline events" ON universe_timeline_events
  FOR SELECT USING (
    universe_id IN (
      SELECT universe_id FROM universe_members 
      WHERE user_id = auth.uid()
    )
  );
```

## Future Enhancements

1. **Advanced Visualization**
   - 3D timeline views
   - Interactive timeline maps
   - AR timeline overlay

2. **AI Integration**
   - Automatic event extraction
   - Consistency predictions
   - Timeline suggestions

3. **Collaboration**
   - Multi-author timeline editing
   - Change proposals/approvals
   - Timeline versioning

4. **Export Features**
   - Timeline posters
   - Interactive web timelines
   - Reader timeline guides

## Related Systems
- Universe Management (parent system)
- Character Management (character tracking)
- Location System (place management)
- Series Management (cross-book continuity)