# BookScribe Version Control System Documentation

## Overview

BookScribe implements a comprehensive version control system for chapters and project snapshots, enabling authors to track changes, restore previous versions, and maintain a complete history of their writing journey. The system provides both automatic and manual versioning with intelligent comparison and rollback capabilities.

## Architecture

### System Components

```mermaid
graph TB
    subgraph "Version Control Core"
        VersionManager[Version Manager]
        SnapshotService[Snapshot Service]
        DiffEngine[Diff Engine]
        ComparisonService[Comparison Service]
    end
    
    subgraph "Storage Layer"
        ChapterVersions[Chapter Versions Table]
        ProjectSnapshots[Project Snapshots Table]
        VersionMetadata[Version Metadata]
    end
    
    subgraph "Features"
        AutoSave[Auto-Save Versions]
        ManualCheckpoint[Manual Checkpoints]
        Rollback[Version Rollback]
        Compare[Version Comparison]
    end
    
    VersionManager --> ChapterVersions
    SnapshotService --> ProjectSnapshots
    DiffEngine --> Compare
    AutoSave --> VersionManager
    ManualCheckpoint --> VersionManager
```

## Data Models

### Chapter Version

```typescript
interface ChapterVersion {
  id: string
  chapter_id: string
  user_id: string
  version_number: number
  title: string | null
  content: string | null
  word_count: number
  outline: string | null
  ai_notes: Record<string, unknown>
  change_summary: string | null
  is_auto_save: boolean
  created_by: 'user' | 'ai_writer' | 'ai_editor'
  quality_score?: QualityMetrics
  created_at: string
  metadata?: {
    device?: string
    session_id?: string
    trigger?: 'manual' | 'auto' | 'ai_generation'
  }
}
```

### Project Snapshot

```typescript
interface ProjectSnapshot {
  id: string
  project_id: string
  user_id: string
  name: string
  description: string | null
  snapshot_data: {
    chapters: ChapterSnapshot[]
    characters: CharacterSnapshot[]
    story_bible: StoryBibleSnapshot
    settings: ProjectSettings
    statistics: ProjectStatistics
  }
  created_at: string
  metadata?: {
    milestone?: string
    tags?: string[]
    export_ready?: boolean
  }
}
```

### Version Comparison

```typescript
interface VersionComparison {
  oldVersion: ChapterVersion
  newVersion: ChapterVersion
  changes: {
    titleChanged: boolean
    contentChanged: boolean
    wordCountDelta: number
    addedLines: string[]
    removedLines: string[]
    modifiedSections: TextDiff[]
    statistics: {
      charactersAdded: number
      charactersRemoved: number
      sentencesAdded: number
      sentencesRemoved: number
    }
  }
}
```

## Version Control Service

### Core Implementation

```typescript
// src/lib/version-history.ts
export class VersionHistoryService {
  private supabase: SupabaseClient
  private diffEngine: DiffEngine
  
  constructor() {
    this.supabase = createClient()
    this.diffEngine = new DiffEngine()
  }
  
  // Get version history for a chapter
  async getChapterVersions(
    chapterId: string, 
    userId: string,
    options?: {
      limit?: number
      includeAutoSaves?: boolean
      startDate?: Date
      endDate?: Date
    }
  ): Promise<ChapterVersion[]> {
    let query = this.supabase
      .from('chapter_versions')
      .select('*')
      .eq('chapter_id', chapterId)
      .eq('user_id', userId)
      .order('version_number', { ascending: false })
    
    if (!options?.includeAutoSaves) {
      query = query.eq('is_auto_save', false)
    }
    
    if (options?.limit) {
      query = query.limit(options.limit)
    }
    
    const { data, error } = await query
    if (error) throw error
    
    return data || []
  }
  
  // Create a manual checkpoint
  async createManualVersion(
    chapterId: string,
    userId: string,
    changeSummary: string,
    metadata?: Record<string, unknown>
  ): Promise<ChapterVersion> {
    // Get current chapter data
    const chapter = await this.getCurrentChapter(chapterId, userId)
    if (!chapter) throw new Error('Chapter not found')
    
    // Get next version number
    const nextVersion = await this.getNextVersionNumber(chapterId)
    
    // Create version with metadata
    const version = await this.createVersion({
      chapter_id: chapterId,
      user_id: userId,
      version_number: nextVersion,
      title: chapter.title,
      content: chapter.content,
      word_count: chapter.word_count,
      outline: chapter.outline,
      ai_notes: chapter.ai_notes,
      change_summary: changeSummary,
      is_auto_save: false,
      created_by: 'user',
      metadata
    })
    
    return version
  }
}
```

## Auto-Save System

### Auto-Save Configuration

```typescript
interface AutoSaveConfig {
  enabled: boolean
  interval: number // milliseconds
  minChanges: number // minimum character changes
  maxVersionsPerDay: number
  retentionDays: number
}

const DEFAULT_AUTOSAVE_CONFIG: AutoSaveConfig = {
  enabled: true,
  interval: 300000, // 5 minutes
  minChanges: 100, // 100 characters
  maxVersionsPerDay: 48, // Every 30 minutes max
  retentionDays: 30 // Keep auto-saves for 30 days
}
```

### Auto-Save Implementation

```typescript
export class AutoSaveManager {
  private config: AutoSaveConfig
  private pendingChanges: Map<string, PendingChange>
  private saveQueue: SaveQueue
  
  async scheduleAutoSave(
    chapterId: string,
    content: string,
    userId: string
  ): Promise<void> {
    const existing = this.pendingChanges.get(chapterId)
    
    // Check if enough changes to warrant save
    if (existing) {
      const changeSize = this.calculateChangeSize(
        existing.originalContent,
        content
      )
      
      if (changeSize < this.config.minChanges) {
        return // Skip save, not enough changes
      }
    }
    
    // Add to queue with debouncing
    this.saveQueue.add({
      chapterId,
      content,
      userId,
      scheduledAt: Date.now()
    }, this.config.interval)
  }
  
  async processAutoSave(save: QueuedSave): Promise<void> {
    try {
      // Check daily limit
      const todayCount = await this.getTodayAutoSaveCount(
        save.chapterId
      )
      
      if (todayCount >= this.config.maxVersionsPerDay) {
        logger.info('Daily auto-save limit reached', {
          chapterId: save.chapterId
        })
        return
      }
      
      // Create auto-save version
      await this.versionService.createAutoSaveVersion({
        chapter_id: save.chapterId,
        user_id: save.userId,
        content: save.content,
        change_summary: 'Auto-saved',
        is_auto_save: true
      })
      
      // Clean up old auto-saves
      await this.cleanupOldAutoSaves(
        save.chapterId,
        this.config.retentionDays
      )
    } catch (error) {
      logger.error('Auto-save failed', { error, save })
    }
  }
}
```

## Version Comparison

### Diff Engine

```typescript
export class DiffEngine {
  // Compare two versions and generate detailed diff
  compareVersions(
    oldVersion: ChapterVersion,
    newVersion: ChapterVersion
  ): VersionComparison {
    const oldLines = this.splitIntoLines(oldVersion.content || '')
    const newLines = this.splitIntoLines(newVersion.content || '')
    
    // Use diff algorithm (e.g., Myers diff)
    const diff = this.calculateDiff(oldLines, newLines)
    
    return {
      oldVersion,
      newVersion,
      changes: {
        titleChanged: oldVersion.title !== newVersion.title,
        contentChanged: oldVersion.content !== newVersion.content,
        wordCountDelta: newVersion.word_count - oldVersion.word_count,
        addedLines: diff.added,
        removedLines: diff.removed,
        modifiedSections: diff.modified,
        statistics: this.calculateStatistics(diff)
      }
    }
  }
  
  // Visual diff for UI display
  generateVisualDiff(
    comparison: VersionComparison
  ): VisualDiff {
    const segments: DiffSegment[] = []
    
    // Process each change
    for (const change of comparison.changes.modifiedSections) {
      segments.push({
        type: change.type,
        oldText: change.oldText,
        newText: change.newText,
        lineNumbers: {
          old: change.oldLineRange,
          new: change.newLineRange
        }
      })
    }
    
    return {
      segments,
      summary: this.generateDiffSummary(comparison),
      canMerge: this.checkMergeability(comparison)
    }
  }
}
```

## Version Restoration

### Rollback Service

```typescript
export class RollbackService {
  async restoreVersion(
    chapterId: string,
    versionId: string,
    userId: string,
    options?: {
      createBackup?: boolean
      preserveOutline?: boolean
      mergeAINotes?: boolean
    }
  ): Promise<RestoreResult> {
    // Validate permissions
    const version = await this.validateAccess(versionId, userId)
    if (!version) {
      throw new Error('Version not found or access denied')
    }
    
    // Create backup of current state if requested
    if (options?.createBackup) {
      await this.createBackupVersion(
        chapterId,
        userId,
        `Backup before restoring v${version.version_number}`
      )
    }
    
    // Prepare restoration data
    const updateData: Partial<Chapter> = {
      content: version.content,
      word_count: version.word_count,
      updated_at: new Date().toISOString()
    }
    
    // Optionally preserve current outline
    if (!options?.preserveOutline) {
      updateData.outline = version.outline
    }
    
    // Merge or replace AI notes
    if (options?.mergeAINotes) {
      const currentChapter = await this.getCurrentChapter(chapterId)
      updateData.ai_notes = {
        ...currentChapter.ai_notes,
        ...version.ai_notes,
        restored_from: {
          version_id: versionId,
          version_number: version.version_number,
          restored_at: new Date().toISOString()
        }
      }
    } else {
      updateData.ai_notes = version.ai_notes
    }
    
    // Perform restoration
    const { error } = await this.supabase
      .from('chapters')
      .update(updateData)
      .eq('id', chapterId)
      .eq('user_id', userId)
    
    if (error) throw error
    
    // Log restoration
    await this.logRestoration({
      chapter_id: chapterId,
      version_id: versionId,
      user_id: userId,
      restored_at: new Date().toISOString()
    })
    
    return {
      success: true,
      restoredVersion: version,
      backupCreated: options?.createBackup || false
    }
  }
}
```

## Project Snapshots

### Snapshot Creation

```typescript
export class SnapshotService {
  async createProjectSnapshot(
    projectId: string,
    userId: string,
    name: string,
    description?: string,
    options?: {
      includeVersionHistory?: boolean
      includeAnalytics?: boolean
      milestone?: string
    }
  ): Promise<ProjectSnapshot> {
    // Gather all project data
    const projectData = await this.gatherProjectData(projectId, userId)
    
    // Create comprehensive snapshot
    const snapshotData = {
      chapters: await this.snapshotChapters(projectId),
      characters: await this.snapshotCharacters(projectId),
      story_bible: await this.snapshotStoryBible(projectId),
      settings: projectData.settings,
      statistics: {
        total_words: projectData.totalWords,
        chapter_count: projectData.chapterCount,
        character_count: projectData.characterCount,
        completion_percentage: projectData.completionPercentage
      }
    }
    
    // Include optional data
    if (options?.includeVersionHistory) {
      snapshotData.version_history = await this.getAllVersions(projectId)
    }
    
    if (options?.includeAnalytics) {
      snapshotData.analytics = await this.getProjectAnalytics(projectId)
    }
    
    // Create snapshot record
    const snapshot = await this.createSnapshot({
      project_id: projectId,
      user_id: userId,
      name,
      description,
      snapshot_data: snapshotData,
      metadata: {
        milestone: options?.milestone,
        created_at: new Date().toISOString(),
        size_bytes: JSON.stringify(snapshotData).length
      }
    })
    
    return snapshot
  }
  
  // Restore entire project from snapshot
  async restoreProjectSnapshot(
    snapshotId: string,
    userId: string,
    options?: {
      createNewProject?: boolean
      projectName?: string
    }
  ): Promise<Project> {
    const snapshot = await this.getSnapshot(snapshotId, userId)
    if (!snapshot) throw new Error('Snapshot not found')
    
    let targetProjectId: string
    
    if (options?.createNewProject) {
      // Create new project from snapshot
      const newProject = await this.createProjectFromSnapshot(
        snapshot,
        options.projectName || `${snapshot.name} (Restored)`
      )
      targetProjectId = newProject.id
    } else {
      // Restore to existing project
      targetProjectId = snapshot.project_id
      await this.backupCurrentProject(targetProjectId)
    }
    
    // Restore all data
    await this.restoreChapters(
      targetProjectId,
      snapshot.snapshot_data.chapters
    )
    await this.restoreCharacters(
      targetProjectId,
      snapshot.snapshot_data.characters
    )
    await this.restoreStoryBible(
      targetProjectId,
      snapshot.snapshot_data.story_bible
    )
    await this.restoreSettings(
      targetProjectId,
      snapshot.snapshot_data.settings
    )
    
    return this.getProject(targetProjectId)
  }
}
```

## API Endpoints

### Version History Endpoints

#### Get Chapter Versions
```typescript
// GET /api/version-history?chapter_id=xxx
interface GetVersionsParams {
  chapter_id: string
  limit?: number
  offset?: number
  include_auto_saves?: boolean
}

interface GetVersionsResponse {
  versions: ChapterVersion[]
  total: number
  has_more: boolean
}
```

#### Create Manual Version
```typescript
// POST /api/version-history
interface CreateVersionBody {
  chapter_id: string
  change_summary: string
  metadata?: Record<string, unknown>
}

interface CreateVersionResponse {
  version: ChapterVersion
  version_number: number
}
```

#### Restore Version
```typescript
// POST /api/version-history/[id]/restore
interface RestoreVersionBody {
  create_backup?: boolean
  preserve_outline?: boolean
  merge_ai_notes?: boolean
}

interface RestoreVersionResponse {
  success: boolean
  restored_version: ChapterVersion
  backup_created: boolean
}
```

#### Compare Versions
```typescript
// GET /api/version-history/compare?old=xxx&new=yyy
interface CompareVersionsResponse {
  comparison: VersionComparison
  visual_diff: VisualDiff
  can_merge: boolean
}
```

### Snapshot Endpoints

#### Create Project Snapshot
```typescript
// POST /api/projects/[id]/snapshots
interface CreateSnapshotBody {
  name: string
  description?: string
  include_version_history?: boolean
  include_analytics?: boolean
  milestone?: string
}

interface CreateSnapshotResponse {
  snapshot: ProjectSnapshot
  size_mb: number
}
```

#### Restore Snapshot
```typescript
// POST /api/snapshots/[id]/restore
interface RestoreSnapshotBody {
  create_new_project?: boolean
  project_name?: string
}

interface RestoreSnapshotResponse {
  project: Project
  restored_items: {
    chapters: number
    characters: number
    settings: boolean
  }
}
```

## UI Components

### Version History Panel

```typescript
// src/components/version-history/version-history-panel.tsx
export function VersionHistoryPanel({ 
  chapterId,
  onRestore 
}: VersionHistoryPanelProps) {
  const [versions, setVersions] = useState<ChapterVersion[]>([])
  const [comparing, setComparing] = useState<{
    old?: string
    new?: string
  }>({})
  
  return (
    <div className="version-history-panel">
      <div className="version-list">
        {versions.map(version => (
          <VersionCard
            key={version.id}
            version={version}
            onSelect={() => handleVersionSelect(version)}
            onRestore={() => onRestore(version)}
            onCompare={() => setComparing({ old: version.id })}
          />
        ))}
      </div>
      
      {comparing.old && comparing.new && (
        <VersionComparisonView
          oldVersionId={comparing.old}
          newVersionId={comparing.new}
          onClose={() => setComparing({})}
        />
      )}
    </div>
  )
}
```

### Version Comparison View

```typescript
export function VersionComparisonView({
  oldVersionId,
  newVersionId,
  onClose
}: ComparisonViewProps) {
  const [comparison, setComparison] = useState<VersionComparison>()
  const [viewMode, setViewMode] = useState<'split' | 'unified'>('split')
  
  return (
    <div className="version-comparison">
      <ComparisonHeader
        oldVersion={comparison?.oldVersion}
        newVersion={comparison?.newVersion}
        stats={comparison?.changes.statistics}
      />
      
      <DiffViewer
        comparison={comparison}
        mode={viewMode}
        highlightChanges
      />
      
      <ComparisonActions
        onRestore={() => handleRestore(oldVersionId)}
        onMerge={() => handleMerge(comparison)}
        onClose={onClose}
      />
    </div>
  )
}
```

## Best Practices

### Version Management

1. **Checkpoint Strategy**
   - Create manual versions before major edits
   - Use descriptive change summaries
   - Tag milestones (completed draft, editor review)
   - Regular snapshots for complex projects

2. **Auto-Save Configuration**
   - Balance frequency with storage
   - Set appropriate retention periods
   - Monitor auto-save performance
   - Clean up old auto-saves regularly

3. **Restoration Safety**
   - Always create backups before restore
   - Review changes before confirming
   - Test restore in development
   - Log all restoration activities

### Performance Optimization

1. **Storage Efficiency**
   ```typescript
   // Use compression for large content
   const compressedContent = await compress(content)
   
   // Store diffs instead of full content
   const diff = calculateDiff(previousVersion, currentContent)
   ```

2. **Query Optimization**
   ```sql
   -- Index for efficient version queries
   CREATE INDEX idx_chapter_versions_lookup 
   ON chapter_versions(chapter_id, user_id, version_number DESC);
   
   -- Partial index for manual versions only
   CREATE INDEX idx_manual_versions 
   ON chapter_versions(chapter_id, created_at DESC) 
   WHERE is_auto_save = false;
   ```

3. **Caching Strategy**
   ```typescript
   // Cache recent versions in memory
   const versionCache = new LRUCache<string, ChapterVersion>({
     max: 100,
     ttl: 1000 * 60 * 5 // 5 minutes
   })
   ```

## Security Considerations

1. **Access Control**
   - Verify user ownership for all operations
   - Implement row-level security
   - Audit version access logs
   - Encrypt sensitive content

2. **Data Integrity**
   - Validate version consistency
   - Prevent version number conflicts
   - Maintain referential integrity
   - Regular integrity checks

3. **Privacy**
   - Separate user versions completely
   - No cross-user version access
   - Secure deletion of versions
   - GDPR compliance for data retention

## Future Enhancements

### Planned Features

1. **Branching System**
   - Create alternate storylines
   - Merge branches
   - A/B testing for content
   - Parallel universe management

2. **Collaborative Versioning**
   - Multi-author version tracking
   - Merge conflict resolution
   - Change attribution
   - Review and approval workflow

3. **Advanced Diff Features**
   - Semantic diff (plot changes)
   - Character arc tracking
   - Style consistency checks
   - AI-powered change summaries

4. **Version Analytics**
   - Writing velocity trends
   - Revision patterns
   - Quality score evolution
   - Productivity insights