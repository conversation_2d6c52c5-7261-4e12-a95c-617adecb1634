'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle2, 
  XCircle, 
  AlertCircle, 
  RefreshCw,
  Activity,
  Cpu,
  Database,
  Search,
  Users,
  Brain
} from 'lucide-react'

interface ServiceHealthMonitorProps {
  services: Record<string, boolean>
  onRefresh: () => void
}

const serviceDetails = {
  'ai-orchestrator': {
    name: 'AI Orchestrator',
    description: 'Manages agent coordination and task distribution',
    icon: Brain,
    criticalLevel: 'high'
  },
  'content-generator': {
    name: 'Content Generator',
    description: 'Generates chapter content and creative writing',
    icon: Activity,
    criticalLevel: 'high'
  },
  'context-manager': {
    name: 'Context Manager',
    description: 'Maintains story consistency and memory',
    icon: Database,
    criticalLevel: 'high'
  },
  'analytics-engine': {
    name: 'Analytics Engine',
    description: 'Tracks performance and quality metrics',
    icon: Cpu,
    criticalLevel: 'medium'
  },
  'semantic-search': {
    name: 'Semantic Search',
    description: 'Powers intelligent content search',
    icon: Search,
    criticalLevel: 'low'
  },
  'collaboration-hub': {
    name: 'Collaboration Hub',
    description: 'Manages multi-user features',
    icon: Users,
    criticalLevel: 'low'
  }
}

export function ServiceHealthMonitor({ services, onRefresh }: ServiceHealthMonitorProps) {
  const totalServices = Object.keys(serviceDetails).length
  const healthyServices = Object.values(services).filter(status => status).length
  const healthPercentage = (healthyServices / totalServices) * 100

  const getStatusIcon = (status: boolean | undefined) => {
    if (status === undefined) return <AlertCircle className="h-5 w-5 text-gray-400" />
    if (status) return <CheckCircle2 className="h-5 w-5 text-success" />
    return <XCircle className="h-5 w-5 text-error" />
  }

  const getStatusBadge = (status: boolean | undefined) => {
    if (status === undefined) return <Badge variant="outline">Unknown</Badge>
    if (status) return <Badge variant="default" className="bg-success">Operational</Badge>
    return <Badge variant="destructive">Offline</Badge>
  }

  const getCriticalityBadge = (level: string) => {
    const variants = {
      high: 'destructive',
      medium: 'secondary',
      low: 'outline'
    } as const
    
    return (
      <Badge variant={variants[level as keyof typeof variants] || 'outline'} className="text-xs">
        {level} priority
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Service Health Status</CardTitle>
            <CardDescription>
              Monitor the status of AI agent services
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Health */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Overall System Health</span>
            <span className="font-medium">{Math.round(healthPercentage)}%</span>
          </div>
          <Progress value={healthPercentage} className="h-2" />
          <p className="text-xs text-muted-foreground">
            {healthyServices} of {totalServices} services operational
          </p>
        </div>

        {/* Service Grid */}
        <div className="grid gap-4 sm:gap-5 lg:gap-6 md:grid-cols-2">
          {Object.entries(serviceDetails).map(([serviceId, details]) => {
            const ServiceIcon = details.icon
            const status = services[serviceId]
            
            return (
              <div
                key={serviceId}
                className="flex items-start gap-3 p-4 border rounded-lg"
              >
                <ServiceIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{details.name}</h4>
                    {getStatusIcon(status)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {details.description}
                  </p>
                  <div className="flex items-center gap-2 pt-1">
                    {getStatusBadge(status)}
                    {getCriticalityBadge(details.criticalLevel)}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Status Legend */}
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium mb-2">Service Priority Levels</h4>
          <div className="flex flex-wrap gap-4 sm:gap-5 lg:gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Badge variant="destructive" className="text-xs">High</Badge>
              <span>Critical for core functionality</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">Medium</Badge>
              <span>Important but not critical</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">Low</Badge>
              <span>Optional features</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}