'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { logger } from '@/lib/services/logger';

interface MapViewport {
  center: { lat: number; lng: number };
  zoom: number;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  rotation?: number;
  pitch?: number;
}

interface MapPreferences {
  id?: string;
  viewport: MapViewport;
  mapStyle?: string;
  showLabels?: boolean;
  showGrid?: boolean;
  showConnections?: boolean;
  clusterMarkers?: boolean;
  visibleLayers?: string[];
  locationTypeFilters?: string[];
  sidebarCollapsed?: boolean;
  minimapVisible?: boolean;
  toolbarPosition?: string;
  lastViewedLocationId?: string | null;
  lastInteraction?: string;
}

interface LocationPosition {
  id?: string;
  locationId: string;
  locationName?: string;
  locationType?: string;
  x: number;
  y: number;
  zIndex?: number;
  markerStyle?: {
    color?: string;
    icon?: string;
    size?: 'small' | 'medium' | 'large';
    shape?: 'circle' | 'square' | 'pin' | 'star';
  };
  isDefault?: boolean;
  isUserPosition?: boolean;
}

interface UseMapPreferencesOptions {
  projectId: string;
  autoLoad?: boolean;
  autoSave?: boolean;
  saveDebounceMs?: number;
}

interface UseMapPreferencesReturn {
  // Preferences
  preferences: MapPreferences | null;
  loadingPreferences: boolean;
  savingPreferences: boolean;
  preferencesError: string | null;
  loadPreferences: () => Promise<void>;
  savePreferences: (prefs: Partial<MapPreferences>) => Promise<void>;
  updateViewport: (viewport: Partial<MapViewport>) => void;
  resetPreferences: () => Promise<void>;
  
  // Positions
  positions: LocationPosition[];
  loadingPositions: boolean;
  savingPositions: boolean;
  positionsError: string | null;
  loadPositions: (includeDefaults?: boolean) => Promise<void>;
  savePosition: (position: LocationPosition) => Promise<void>;
  savePositions: (positions: LocationPosition[], clearExisting?: boolean) => Promise<void>;
  deletePosition: (locationId: string) => Promise<void>;
  getPositionForLocation: (locationId: string) => LocationPosition | undefined;
}

export function useMapPreferences(options: UseMapPreferencesOptions): UseMapPreferencesReturn {
  const { projectId, autoLoad = true, autoSave = true, saveDebounceMs = 1000 } = options;
  
  // Preferences state
  const [preferences, setPreferences] = useState<MapPreferences | null>(null);
  const [loadingPreferences, setLoadingPreferences] = useState(false);
  const [savingPreferences, setSavingPreferences] = useState(false);
  const [preferencesError, setPreferencesError] = useState<string | null>(null);
  
  // Positions state
  const [positions, setPositions] = useState<LocationPosition[]>([]);
  const [loadingPositions, setLoadingPositions] = useState(false);
  const [savingPositions, setSavingPositions] = useState(false);
  const [positionsError, setPositionsError] = useState<string | null>(null);
  
  // Debounce timer
  const saveTimerRef = useRef<NodeJS.Timeout>();
  const pendingPreferencesRef = useRef<Partial<MapPreferences> | null>(null);

  // Load preferences
  const loadPreferences = useCallback(async () => {
    if (!projectId) return;
    
    setLoadingPreferences(true);
    setPreferencesError(null);
    
    try {
      const response = await fetch(`/api/projects/${projectId}/map-preferences`);
      if (!response.ok) {
        throw new Error('Failed to load map preferences');
      }
      
      const data = await response.json();
      setPreferences(data.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load preferences';
      setPreferencesError(errorMessage);
      logger.error('Error loading map preferences:', err);
      
      // Set default preferences on error
      setPreferences({
        viewport: {
          center: { lat: 0, lng: 0 },
          zoom: 10
        },
        mapStyle: 'default',
        showLabels: true,
        showGrid: false,
        showConnections: true,
        clusterMarkers: true,
        visibleLayers: [],
        locationTypeFilters: [],
        sidebarCollapsed: false,
        minimapVisible: true,
        toolbarPosition: 'top'
      });
    } finally {
      setLoadingPreferences(false);
    }
  }, [projectId]);

  // Save preferences (immediate)
  const savePreferencesImmediate = useCallback(async (prefs: Partial<MapPreferences>) => {
    if (!projectId || !preferences) return;
    
    setSavingPreferences(true);
    setPreferencesError(null);
    
    try {
      const updatedPrefs = { ...preferences, ...prefs };
      
      const response = await fetch(`/api/projects/${projectId}/map-preferences`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedPrefs)
      });
      
      if (!response.ok) {
        throw new Error('Failed to save map preferences');
      }
      
      const data = await response.json();
      setPreferences(data.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save preferences';
      setPreferencesError(errorMessage);
      logger.error('Error saving map preferences:', err);
    } finally {
      setSavingPreferences(false);
    }
  }, [projectId, preferences]);

  // Save preferences (debounced)
  const savePreferences = useCallback(async (prefs: Partial<MapPreferences>) => {
    if (!autoSave) {
      return savePreferencesImmediate(prefs);
    }
    
    // Update local state immediately
    setPreferences(prev => prev ? { ...prev, ...prefs } : null);
    
    // Store pending changes
    pendingPreferencesRef.current = {
      ...pendingPreferencesRef.current,
      ...prefs
    };
    
    // Clear existing timer
    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current);
    }
    
    // Set new timer
    saveTimerRef.current = setTimeout(async () => {
      if (pendingPreferencesRef.current) {
        await savePreferencesImmediate(pendingPreferencesRef.current);
        pendingPreferencesRef.current = null;
      }
    }, saveDebounceMs);
  }, [autoSave, savePreferencesImmediate, saveDebounceMs]);

  // Update viewport specifically
  const updateViewport = useCallback((viewport: Partial<MapViewport>) => {
    if (!preferences) return;
    
    savePreferences({
      viewport: {
        ...preferences.viewport,
        ...viewport
      }
    });
  }, [preferences, savePreferences]);

  // Reset preferences
  const resetPreferences = useCallback(async () => {
    if (!projectId) return;
    
    setSavingPreferences(true);
    setPreferencesError(null);
    
    try {
      const response = await fetch(`/api/projects/${projectId}/map-preferences`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to reset map preferences');
      }
      
      // Reload to get defaults
      await loadPreferences();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset preferences';
      setPreferencesError(errorMessage);
      logger.error('Error resetting map preferences:', err);
    } finally {
      setSavingPreferences(false);
    }
  }, [projectId, loadPreferences]);

  // Load positions
  const loadPositions = useCallback(async (includeDefaults = true) => {
    if (!projectId) return;
    
    setLoadingPositions(true);
    setPositionsError(null);
    
    try {
      const params = new URLSearchParams();
      if (includeDefaults) params.append('includeDefaults', 'true');
      
      const response = await fetch(`/api/projects/${projectId}/location-positions?${params}`);
      if (!response.ok) {
        throw new Error('Failed to load location positions');
      }
      
      const data = await response.json();
      setPositions(data.data.positions || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load positions';
      setPositionsError(errorMessage);
      logger.error('Error loading location positions:', err);
    } finally {
      setLoadingPositions(false);
    }
  }, [projectId]);

  // Save single position
  const savePosition = useCallback(async (position: LocationPosition) => {
    if (!projectId) return;
    
    setSavingPositions(true);
    setPositionsError(null);
    
    try {
      const response = await fetch(`/api/projects/${projectId}/location-positions`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(position)
      });
      
      if (!response.ok) {
        throw new Error('Failed to save location position');
      }
      
      const data = await response.json();
      const savedPosition = data.data.position;
      
      // Update local state
      setPositions(prev => {
        const index = prev.findIndex(p => p.locationId === savedPosition.locationId);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = savedPosition;
          return updated;
        } else {
          return [...prev, savedPosition];
        }
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save position';
      setPositionsError(errorMessage);
      logger.error('Error saving location position:', err);
    } finally {
      setSavingPositions(false);
    }
  }, [projectId]);

  // Save multiple positions
  const savePositions = useCallback(async (newPositions: LocationPosition[], clearExisting = false) => {
    if (!projectId) return;
    
    setSavingPositions(true);
    setPositionsError(null);
    
    try {
      const response = await fetch(`/api/projects/${projectId}/location-positions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          positions: newPositions,
          clearExisting
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save location positions');
      }
      
      // Reload all positions
      await loadPositions();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save positions';
      setPositionsError(errorMessage);
      logger.error('Error saving location positions:', err);
    } finally {
      setSavingPositions(false);
    }
  }, [projectId, loadPositions]);

  // Delete position
  const deletePosition = useCallback(async (locationId: string) => {
    if (!projectId) return;
    
    setSavingPositions(true);
    setPositionsError(null);
    
    try {
      const params = new URLSearchParams({ locationId });
      const response = await fetch(`/api/projects/${projectId}/location-positions?${params}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete location position');
      }
      
      // Update local state
      setPositions(prev => prev.filter(p => p.locationId !== locationId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete position';
      setPositionsError(errorMessage);
      logger.error('Error deleting location position:', err);
    } finally {
      setSavingPositions(false);
    }
  }, [projectId]);

  // Get position for specific location
  const getPositionForLocation = useCallback((locationId: string): LocationPosition | undefined => {
    return positions.find(p => p.locationId === locationId);
  }, [positions]);

  // Auto-load on mount
  useEffect(() => {
    if (autoLoad && projectId) {
      loadPreferences();
      loadPositions();
    }
  }, [autoLoad, projectId]); // Don't include loadPreferences/loadPositions to avoid loops

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (saveTimerRef.current) {
        clearTimeout(saveTimerRef.current);
      }
    };
  }, []);

  return {
    // Preferences
    preferences,
    loadingPreferences,
    savingPreferences,
    preferencesError,
    loadPreferences,
    savePreferences,
    updateViewport,
    resetPreferences,
    
    // Positions
    positions,
    loadingPositions,
    savingPositions,
    positionsError,
    loadPositions,
    savePosition,
    savePositions,
    deletePosition,
    getPositionForLocation
  };
}