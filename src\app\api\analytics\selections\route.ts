import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthorizationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server';
import { createTypedServerClient } from '@/lib/supabase';
import { apiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter-unified';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service';
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting for analytics events (100 events per hour)
    const clientIP = getClientIP(request);
    const rateLimitResult = apiLimiter.check(100, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse();
    }

    // Check authentication
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) return handleAPIError(new AuthorizationError());
    const supabase = await createTypedServerClient();

    const body = await request.json();
    const { 
      projectId, 
      selectionProfileId, 
      eventType, 
      selectionData, 
      outcomeData 
    } = body;

    if (!eventType) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    // Validate event type
    const validEventTypes = [
      'profile_used',
      'project_created',
      'project_completed',
      'project_abandoned',
      'selection_modified',
      'writing_started',
      'chapter_completed',
      'export_generated'
    ];

    if (!validEventTypes.includes(eventType)) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    const analyticsData = {
      user_id: user.id,
      project_id: projectId || null,
      selection_profile_id: selectionProfileId || null,
      event_type: eventType,
      selection_data: selectionData || {},
      outcome_data: outcomeData || {},
    };

    const { data: analyticsEntry, error } = await supabase
      .from('selection_analytics')
      .insert(analyticsData)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json({ 
      success: true,
      analyticsEntry: {
        id: analyticsEntry.id,
        userId: analyticsEntry.user_id,
        projectId: analyticsEntry.project_id,
        selectionProfileId: analyticsEntry.selection_profile_id,
        eventType: analyticsEntry.event_type,
        selectionData: analyticsEntry.selection_data,
        outcomeData: analyticsEntry.outcome_data,
        createdAt: new Date(analyticsEntry.created_at),
      }
    });
  } catch (error) {
    logger.error('Selection analytics API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) return handleAPIError(new AuthorizationError());
    const supabase = await createTypedServerClient();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const projectId = searchParams.get('projectId');
    const eventType = searchParams.get('eventType');
    const profileId = searchParams.get('profileId');
    const days = parseInt(searchParams.get('days') || '30');

    // Only allow users to view their own analytics unless they're admin
    if (userId && userId !== user.id) {
      return handleAPIError(new AuthorizationError());
    }

    // If no userId specified, default to current user
    const targetUserId = userId || user.id;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    let query = supabase
      .from('selection_analytics')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    // Apply filters - always filter by user ID (either specified or current user)
    query = query.eq('user_id', targetUserId);
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    if (eventType) {
      query = query.eq('event_type', eventType);
    }
    if (profileId) {
      query = query.eq('selection_profile_id', profileId);
    }

    const { data: analytics, error } = await query;

    if (error) throw error;

    // Transform data
    const formattedAnalytics = analytics?.map(entry => ({
      id: entry.id,
      userId: entry.user_id,
      projectId: entry.project_id,
      selectionProfileId: entry.selection_profile_id,
      eventType: entry.event_type,
      selectionData: entry.selection_data,
      outcomeData: entry.outcome_data,
      createdAt: new Date(entry.created_at),
    })) || [];

    return NextResponse.json({ analytics: formattedAnalytics });
  } catch (error) {
    logger.error('Selection analytics API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}