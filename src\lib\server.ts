// Test helper module for server-side utilities
// This file provides test utilities and mock implementations for server-side testing

import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

// Test helper to create a mock NextRequest
export function createMockRequest(options: {
  url?: string
  method?: string
  headers?: Record<string, string>
  body?: any
  searchParams?: Record<string, string>
}): NextRequest {
  const {
    url = 'http://localhost:3000/',
    method = 'GET',
    headers = {},
    body,
    searchParams = {}
  } = options

  // Construct URL with search params
  const urlObject = new URL(url)
  Object.entries(searchParams).forEach(([key, value]) => {
    urlObject.searchParams.set(key, value)
  })

  // Create request options
  const requestOptions: RequestInit = {
    method,
    headers: new Headers(headers)
  }

  // Add body if provided
  if (body && method !== 'GET' && method !== 'HEAD') {
    requestOptions.body = JSON.stringify(body)
    requestOptions.headers = new Headers({
      ...headers,
      'Content-Type': 'application/json'
    })
  }

  return new NextRequest(urlObject.toString(), requestOptions)
}

// Test helper to create a mock response
export function createMockResponse(data: any, options?: ResponseInit): NextResponse {
  return NextResponse.json(data, options)
}

// Mock cookie store for testing
export class MockCookieStore {
  private cookies: Map<string, string> = new Map()

  get(name: string): { value: string } | undefined {
    const value = this.cookies.get(name)
    return value ? { value } : undefined
  }

  set(name: string, value: string, options?: any): void {
    this.cookies.set(name, value)
  }

  delete(name: string): void {
    this.cookies.delete(name)
  }

  getAll(): Array<{ name: string; value: string }> {
    return Array.from(this.cookies.entries()).map(([name, value]) => ({
      name,
      value
    }))
  }
}

// Mock headers for testing
export class MockHeaders {
  private headers: Map<string, string> = new Map()

  get(name: string): string | null {
    return this.headers.get(name.toLowerCase()) || null
  }

  set(name: string, value: string): void {
    this.headers.set(name.toLowerCase(), value)
  }

  delete(name: string): void {
    this.headers.delete(name.toLowerCase())
  }

  has(name: string): boolean {
    return this.headers.has(name.toLowerCase())
  }

  entries(): IterableIterator<[string, string]> {
    return this.headers.entries()
  }
}

// Test helper for server-side utilities
export const testHelpers = {
  createMockRequest,
  createMockResponse,
  MockCookieStore,
  MockHeaders,
  
  // Helper to extract JSON from response
  async extractJson(response: NextResponse): Promise<any> {
    const text = await response.text()
    return JSON.parse(text)
  },
  
  // Helper to create authenticated request
  createAuthenticatedRequest(token: string, options?: Parameters<typeof createMockRequest>[0]): NextRequest {
    return createMockRequest({
      ...options,
      headers: {
        ...options?.headers,
        'Authorization': `Bearer ${token}`
      }
    })
  },
  
  // Helper to mock Supabase client
  createMockSupabaseClient() {
    return {
      auth: {
        getUser: jest.fn(),
        signIn: jest.fn(),
        signOut: jest.fn()
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis()
      }))
    }
  }
}

// Re-export for convenience
export default testHelpers