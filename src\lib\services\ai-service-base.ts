import { vercelAIClient, VercelAIConfig, StreamingOptions } from '@/lib/ai/vercel-ai-client'
import { logger } from './logger'
import { BaseService } from './base-service'
import { ServiceResponse } from './types'
import { getAIConfig, AI_MODELS, AI_TEMPERATURE, AI_QUALITY_THRESHOLDS } from '../config/ai-settings'
import { ErrorContext } from './error-handler'
import { z } from 'zod'
import { TIME_MS } from '@/lib/constants'

export interface AIGenerationOptions {
  model?: string
  temperature?: number
  maxTokens?: number
  systemPrompt?: string
  responseFormat?: 'text' | 'json' | 'structured'
  structuredSchema?: z.ZodSchema
  retryConfig?: {
    maxRetries?: number
    onRetry?: (error: Error, attempt: number) => void
  }
  streaming?: boolean
  streamingOptions?: StreamingOptions
}

export abstract class AIServiceBase extends BaseService {
  protected aiClient = vercelAIClient

  constructor(config: {
    name: string
    version: string
    endpoints: string[]
    dependencies?: string[]
    healthCheck: string
    useSharedClient?: boolean
  }) {
    super({
      ...config,
      status: 'inactive'
    })
  }

  /**
   * Generate content using Vercel AI SDK with consistent error handling and retries
   */
  protected async generateWithAI<T = string>(
    prompt: string,
    options: AIGenerationOptions = {}
  ): Promise<ServiceResponse<T>> {
    return this.withErrorHandling(async () => {
      const {
        model = AI_MODELS.PRIMARY,
        temperature = AI_TEMPERATURE.BALANCED,
        maxTokens = 2000,
        systemPrompt = 'You are a helpful AI assistant.',
        responseFormat = 'text',
        structuredSchema,
        streaming = false,
        streamingOptions,
        retryConfig = {}
      } = options

      const errorContext: ErrorContext = {
        operation: `${this.name}.generateWithAI`,
        metadata: {
          model,
          responseFormat,
          promptLength: prompt.length,
          streaming
        }
      }

      const aiConfig: VercelAIConfig = {
        model,
        temperature,
        maxTokens,
        systemPrompt
      }

      // Handle different response formats and streaming
      if (responseFormat === 'structured' && structuredSchema) {
        if (streaming) {
          const result = await this.aiClient.streamObject(
            prompt,
            structuredSchema,
            aiConfig,
            streamingOptions,
            errorContext
          )
          return result as T
        } else {
          const result = await this.aiClient.generateObject(
            prompt,
            structuredSchema,
            aiConfig,
            errorContext
          )
          return result as T
        }
      }

      // Handle text response (streaming or non-streaming)
      if (streaming) {
        const result = await this.aiClient.streamText(
          prompt,
          aiConfig,
          streamingOptions,
          errorContext
        )
        return result as T
      } else {
        const result = await this.aiClient.generateText(
          prompt,
          aiConfig,
          errorContext
        )
        return result as T
      }

    })
  }

  /**
   * Assess content quality with consistent scoring
   */
  protected async assessQuality(
    content: string,
    type: string,
    criteria?: Record<string, number>
  ): Promise<number> {
    let score = AI_QUALITY_THRESHOLDS.NEEDS_IMPROVEMENT

    // Basic quality checks
    if (content.length > 200) score += 10
    if (content.length > TIME_MS.SECOND) score += 5

    const paragraphs = content.split('\n\n').filter(p => p.trim())
    if (paragraphs.length > 1) score += 10

    // Type-specific checks
    const typeChecks: Record<string, () => number> = {
      dialogue: () => {
        const hasQuotes = content.includes('"') || content.includes("'")
        const hasSpeakers = content.match(/said|asked|replied|whispered|shouted/gi)
        return (hasQuotes ? 10 : 0) + (hasSpeakers ? 5 : 0)
      },
      description: () => {
        const senses = content.match(/\b(saw|heard|felt|smelled|tasted|touched)\b/gi)
        const adjectives = content.match(/\b\w+ly\b/gi)
        return (senses ? senses.length * 2 : 0) + (adjectives ? Math.min(adjectives.length, 10) : 0)
      },
      scene: () => {
        const hasDialogue = content.includes('"')
        const hasAction = content.match(/\b(walked|ran|jumped|moved|turned)\b/gi)
        return (hasDialogue ? 10 : 0) + (hasAction ? 10 : 0)
      },
      character: () => {
        const hasTraits = content.match(/\b(personality|appearance|background|motivation)\b/gi)
        return hasTraits ? hasTraits.length * 5 : 0
      }
    }

    if (type in typeChecks) {
      score += typeChecks[type]()
    }

    // Apply custom criteria if provided
    if (criteria) {
      for (const [criterion, weight] of Object.entries(criteria)) {
        if (content.toLowerCase().includes(criterion.toLowerCase())) {
          score += weight
        }
      }
    }

    // Readability check
    const sentences = content.split(/[.!?]+/).filter(s => s.trim())
    const words = content.split(/\s+/)
    const avgSentenceLength = words.length / Math.max(sentences.length, 1)
    if (avgSentenceLength > 8 && avgSentenceLength < 30) score += 5

    // Ensure minimum score
    const minimumScore = AI_QUALITY_THRESHOLDS.MINIMUM_SCORES[
      type.toUpperCase() as keyof typeof AI_QUALITY_THRESHOLDS.MINIMUM_SCORES
    ] || AI_QUALITY_THRESHOLDS.ACCEPTABLE

    return Math.min(100, Math.max(minimumScore * 0.5, score))
  }

  /**
   * Common system prompts for different generation types
   */
  protected getSystemPrompt(type: string): string {
    const prompts: Record<string, string> = {
      scene: `You are a master scene writer with the cinematic vision of Cormac McCarthy, the tension-building of Stephen King, and the emotional depth of Elena Ferrante. Create scenes that readers will remember years later.

SCENE EXCELLENCE:
- Start in medias res—no warming up
- Every scene must change something fundamental
- Layer action with internal reaction
- Use specific, unexpected details that reveal character
- End with readers desperate to know what happens next`,

      dialogue: `You are a dialogue master who studied under Elmore Leonard, Aaron Sorkin, and Sally Rooney. Your conversations crackle with authenticity, subtext, and revelation.

DIALOGUE MASTERY:
- Each character's voice must be instantly recognizable
- Subtext is everything—what's not said matters most
- Use interruptions, silence, and action beats for rhythm
- Never use dialogue for exposition dumps
- Make every line earn its place through conflict or character revelation`,

      description: `You are a descriptive virtuoso with Annie Proulx's eye for detail, Gabriel García Márquez's magical touch, and Donna Tartt's atmospheric mastery. Paint worlds readers can taste, smell, and touch.

DESCRIPTIVE EXCELLENCE:
- Engage all five senses in unexpected ways
- Use specific details that do double duty (setting + emotion/character)
- Create atmosphere that enhances mood without slowing pace
- Make settings almost characters themselves
- Show the familiar through fresh eyes`,

      character: `You are a character creator with the psychological insight of Gillian Flynn, the complexity of George Eliot, and the relatability of Rainbow Rowell. Birth characters who feel more real than real people.

CHARACTER CREATION:
- Give each character a secret that changes everything
- Create contradictions that feel authentically human
- Build in specific details that readers will remember
- Design flaws that make characters more loveable
- Ensure each character could carry their own novel`,

      plot: `You are a plot architect with the structural genius of Christopher Nolan, the twists of Agatha Christie, and the emotional journeys of Khaled Hosseini. Design stories that haunt readers.

PLOT EXCELLENCE:
- Every plot point must feel surprising yet inevitable
- Layer multiple timeline tensions
- Create reversals that recontextualize everything
- Build to emotional climaxes, not just plot climaxes
- Ensure the ending satisfies while leaving readers wanting more`,

      general: 'You are a bestselling author with deep expertise across all aspects of commercial and literary fiction. Adapt your approach to create excellence in any writing task.'
    }

    return prompts[type] || prompts.general
  }

  /**
   * Batch process multiple AI requests efficiently
   */
  protected async batchGenerate<T>(
    requests: Array<{
      prompt: string
      options?: AIGenerationOptions
    }>,
    concurrency: number = 3
  ): Promise<Array<ServiceResponse<T>>> {
    const results: Array<ServiceResponse<T>> = []
    
    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency)
      const batchResults = await Promise.all(
        batch.map(req => this.generateWithAI<T>(req.prompt, req.options))
      )
      results.push(...batchResults)
    }
    
    return results
  }
}