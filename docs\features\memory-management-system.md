# BookScribe Memory Management System

## Overview

The Memory Management System optimizes AI context usage by intelligently compressing, merging, and managing the contextual information provided to AI agents. This system is critical for maintaining coherent long-form content generation while staying within token limits and managing costs.

## Architecture

### Core Components

#### Memory Store
Central storage for context data:

```typescript
interface MemoryStore {
  project_id: string;
  memories: {
    permanent: PermanentMemory;    // Core unchanging facts
    episodic: EpisodicMemory[];   // Chapter-specific events
    working: WorkingMemory;       // Current session context
    compressed: CompressedMemory; // Archived summaries
  };
  metadata: {
    total_tokens: number;
    compression_ratio: number;
    last_optimization: Date;
    memory_version: number;
  };
}
```

#### Memory Types

```typescript
interface PermanentMemory {
  // Core story elements that never change
  story_premise: string;
  main_characters: CharacterCore[];
  world_rules: WorldRule[];
  genre_conventions: string[];
  writing_style: StyleGuide;
  token_count: number;
}

interface EpisodicMemory {
  // Chapter or scene-specific memories
  chapter_id: string;
  events: StoryEvent[];
  character_states: CharacterState[];
  location_states: LocationState[];
  active_plot_threads: PlotThread[];
  token_count: number;
  importance_score: number;
}

interface WorkingMemory {
  // Active context for current generation
  current_chapter: number;
  recent_text: string;
  active_characters: string[];
  immediate_context: string;
  generation_constraints: Constraint[];
  token_count: number;
}

interface CompressedMemory {
  // Summarized historical context
  compressed_chapters: ChapterSummary[];
  character_arc_summaries: CharacterArcSummary[];
  plot_progression: PlotSummary[];
  compression_level: number;
  original_token_count: number;
  compressed_token_count: number;
}
```

## API Endpoints

### Memory Operations

#### GET /api/memory/stats
Get memory usage statistics:

```typescript
// Request
GET /api/memory/stats?project_id=uuid

// Response
{
  stats: {
    total_memory_tokens: 125000,
    breakdown: {
      permanent: 15000,
      episodic: 75000,
      working: 10000,
      compressed: 25000
    },
    token_limit: 128000,
    usage_percentage: 97.6,
    optimization_needed: true,
    cost_estimate: {
      current_monthly: 45.50,
      after_optimization: 28.30
    }
  }
}
```

#### POST /api/memory/compress
Compress memory for a project:

```typescript
// Request
{
  project_id: "uuid",
  compression_strategy: "intelligent" | "aggressive" | "minimal",
  preserve_recent: 5, // Keep last N chapters uncompressed
  target_reduction: 0.5 // Aim for 50% reduction
}

// Response
{
  compression_result: {
    original_tokens: 125000,
    compressed_tokens: 62000,
    reduction_percentage: 50.4,
    quality_score: 0.92,
    compressed_elements: {
      chapters: 15,
      character_arcs: 8,
      plot_threads: 12
    },
    warnings: [
      "Minor detail loss in chapters 3-5"
    ]
  }
}
```

#### POST /api/memory/merge
Merge related memories:

```typescript
// Request
{
  project_id: "uuid",
  merge_type: "characters" | "events" | "locations" | "auto",
  similarity_threshold: 0.8
}

// Response
{
  merge_result: {
    memories_analyzed: 450,
    memories_merged: 125,
    token_reduction: 15000,
    merge_operations: [
      {
        type: "character_states",
        merged_count: 45,
        description: "Consolidated Elena's emotional states"
      }
    ]
  }
}
```

#### POST /api/memory/optimize
Automatic memory optimization:

```typescript
// Request
{
  project_id: "uuid",
  optimization_goal: "quality" | "cost" | "balanced",
  constraints: {
    min_quality_score: 0.9,
    max_token_usage: 100000,
    preserve_elements: ["main_character_details", "recent_events"]
  }
}

// Response
{
  optimization: {
    strategies_applied: [
      "compression",
      "deduplication", 
      "summarization",
      "priority_filtering"
    ],
    token_reduction: 35000,
    quality_maintained: 0.93,
    cost_savings: "$18.50/month"
  }
}
```

## Memory Compression Strategies

### 1. Intelligent Summarization
AI-powered content summarization:

```typescript
class IntelligentSummarizer {
  async summarizeChapter(
    chapter: Chapter,
    importance_weights: ImportanceWeights
  ): Promise<ChapterSummary> {
    // Extract key events
    const keyEvents = await this.extractKeyEvents(chapter);
    
    // Identify character changes
    const characterDevelopments = await this.extractCharacterChanges(chapter);
    
    // Compress descriptions
    const compressedNarrative = await this.compressNarrative(
      chapter.content,
      importance_weights
    );
    
    return {
      chapter_id: chapter.id,
      key_events: keyEvents,
      character_changes: characterDevelopments,
      plot_advancement: this.extractPlotAdvancement(chapter),
      compressed_narrative: compressedNarrative,
      original_tokens: chapter.token_count,
      compressed_tokens: this.countTokens(compressedNarrative),
      importance_score: this.calculateImportance(chapter)
    };
  }
}
```

### 2. Hierarchical Compression
Layer-based compression approach:

```typescript
interface HierarchicalCompression {
  levels: [
    {
      level: 1,
      name: "Full Detail",
      retention: 1.0,
      scope: "current_chapter"
    },
    {
      level: 2, 
      name: "Recent Context",
      retention: 0.7,
      scope: "last_3_chapters"
    },
    {
      level: 3,
      name: "Medium Summary", 
      retention: 0.4,
      scope: "last_10_chapters"
    },
    {
      level: 4,
      name: "High Summary",
      retention: 0.2,
      scope: "remaining_chapters"
    }
  ];
}
```

### 3. Semantic Deduplication
Remove redundant information:

```typescript
class SemanticDeduplicator {
  async deduplicate(memories: Memory[]): Promise<Memory[]> {
    const embeddings = await this.generateEmbeddings(memories);
    const clusters = this.clusterSimilarMemories(embeddings);
    
    return clusters.map(cluster => {
      if (cluster.length === 1) return cluster[0];
      
      // Merge similar memories
      return this.mergeMemories(cluster);
    });
  }
  
  private mergeMemories(similar: Memory[]): Memory {
    // Keep most detailed version
    const primary = this.selectPrimaryMemory(similar);
    
    // Merge unique details from others
    const merged = this.mergeUniqueDetails(primary, similar);
    
    return {
      ...merged,
      source_memories: similar.map(m => m.id),
      merge_confidence: this.calculateMergeConfidence(similar)
    };
  }
}
```

### 4. Priority-based Filtering
Keep only essential information:

```typescript
interface PriorityFilter {
  criteria: {
    plot_critical: { weight: 1.0, required: true };
    character_development: { weight: 0.8, required: true };
    world_building: { weight: 0.6, required: false };
    atmosphere: { weight: 0.4, required: false };
    minor_details: { weight: 0.2, required: false };
  };
  
  filter(memories: Memory[], target_tokens: number): Memory[] {
    const scored = memories.map(m => ({
      memory: m,
      score: this.calculatePriority(m)
    }));
    
    scored.sort((a, b) => b.score - a.score);
    
    const selected = [];
    let totalTokens = 0;
    
    for (const item of scored) {
      if (totalTokens + item.memory.tokens <= target_tokens) {
        selected.push(item.memory);
        totalTokens += item.memory.tokens;
      }
    }
    
    return selected;
  }
}
```

## Memory Organization

### Context Windows
Sliding window approach:

```typescript
class ContextWindowManager {
  windows: {
    immediate: {
      size: 2000,    // tokens
      content: "last 500 words"
    };
    recent: {
      size: 8000,    // tokens
      content: "current + previous chapter"
    };
    extended: {
      size: 20000,   // tokens
      content: "last 5 chapters summarized"
    };
    global: {
      size: 50000,   // tokens
      content: "entire story compressed"
    };
  };
  
  async buildContext(
    request: ContextRequest
  ): Promise<GenerationContext> {
    const windows = this.selectWindows(request);
    const context = await this.assembleContext(windows);
    
    return this.optimizeForTokenLimit(context, request.token_limit);
  }
}
```

### Memory Indexing
Efficient retrieval system:

```typescript
interface MemoryIndex {
  by_chapter: Map<string, Memory[]>;
  by_character: Map<string, Memory[]>;
  by_event_type: Map<string, Memory[]>;
  by_importance: PriorityQueue<Memory>;
  by_recency: TimeOrderedList<Memory>;
  
  search(criteria: SearchCriteria): Memory[] {
    const results = [];
    
    if (criteria.chapter_ids) {
      results.push(...this.searchByChapter(criteria.chapter_ids));
    }
    
    if (criteria.character_ids) {
      results.push(...this.searchByCharacter(criteria.character_ids));
    }
    
    return this.rankResults(results, criteria);
  }
}
```

## Compression Algorithms

### 1. Extractive Summarization
```typescript
class ExtractiveSummarizer {
  summarize(text: string, target_length: number): string {
    const sentences = this.splitIntoSentences(text);
    const scores = this.scoreSentences(sentences);
    
    // Select top sentences
    const selected = this.selectTopSentences(
      sentences,
      scores,
      target_length
    );
    
    // Reorder for coherence
    return this.reorderForCoherence(selected);
  }
  
  private scoreSentences(sentences: string[]): number[] {
    // TF-IDF scoring
    const tfidf = this.calculateTFIDF(sentences);
    
    // Position scoring (beginning/end more important)
    const position = this.calculatePositionScores(sentences);
    
    // Cue phrase detection
    const cues = this.detectCuePhrases(sentences);
    
    // Combine scores
    return sentences.map((_, i) => 
      tfidf[i] * 0.4 + position[i] * 0.3 + cues[i] * 0.3
    );
  }
}
```

### 2. Abstractive Compression
```typescript
class AbstractiveCompressor {
  async compress(
    content: string,
    compression_ratio: number
  ): Promise<string> {
    const targetLength = Math.floor(
      content.length * (1 - compression_ratio)
    );
    
    return await this.ai.compress({
      content,
      target_length: targetLength,
      preserve: [
        "character names",
        "key events",
        "emotional beats",
        "plot points"
      ],
      style: "narrative_summary"
    });
  }
}
```

## Memory Lifecycle

### 1. Creation
```typescript
interface MemoryCreation {
  source: 'user_input' | 'ai_generation' | 'import';
  content: string;
  metadata: {
    chapter_id?: string;
    character_ids?: string[];
    importance?: number;
    timestamp: Date;
  };
  
  async create(): Promise<Memory> {
    const processed = await this.processContent(this.content);
    const embeddings = await this.generateEmbeddings(processed);
    const importance = this.calculateImportance(processed);
    
    return {
      id: generateId(),
      ...processed,
      embeddings,
      importance,
      created_at: new Date()
    };
  }
}
```

### 2. Evolution
```typescript
class MemoryEvolution {
  async evolve(memory: Memory, newContext: Context): Promise<Memory> {
    // Update importance based on usage
    memory.importance = this.recalculateImportance(
      memory,
      newContext
    );
    
    // Merge with related memories
    if (this.shouldMerge(memory, newContext)) {
      memory = await this.mergeWithRelated(memory, newContext);
    }
    
    // Compress if old and less important
    if (this.shouldCompress(memory)) {
      memory = await this.compress(memory);
    }
    
    return memory;
  }
}
```

### 3. Archival
```typescript
interface MemoryArchival {
  archive_threshold: {
    age_days: 30,
    importance_below: 0.3,
    access_frequency_below: 0.1
  };
  
  async archiveMemories(project_id: string): Promise<ArchivalResult> {
    const candidates = await this.findArchivalCandidates(project_id);
    
    const archived = await Promise.all(
      candidates.map(memory => this.archiveMemory(memory))
    );
    
    return {
      archived_count: archived.length,
      space_freed: this.calculateSpaceFreed(archived),
      quality_impact: this.assessQualityImpact(archived)
    };
  }
}
```

## Performance Optimization

### Caching Strategy
```typescript
interface MemoryCaching {
  layers: {
    hot: {
      storage: 'memory',
      capacity: '1GB',
      ttl: 3600, // 1 hour
      content: 'active working memory'
    };
    warm: {
      storage: 'redis',
      capacity: '10GB', 
      ttl: 86400, // 24 hours
      content: 'recent chapters'
    };
    cold: {
      storage: 'database',
      capacity: 'unlimited',
      ttl: null,
      content: 'all memories'
    };
  };
}
```

### Batch Processing
```typescript
class BatchMemoryProcessor {
  async processBatch(
    operations: MemoryOperation[]
  ): Promise<BatchResult> {
    // Group similar operations
    const grouped = this.groupOperations(operations);
    
    // Process in parallel where possible
    const results = await Promise.all([
      this.processCompressions(grouped.compressions),
      this.processMerges(grouped.merges),
      this.processArchival(grouped.archival)
    ]);
    
    return this.consolidateResults(results);
  }
}
```

## Memory Quality Metrics

### Quality Assessment
```typescript
interface QualityMetrics {
  coherence_score: number;      // 0-1, narrative coherence
  completeness_score: number;   // 0-1, information retention
  consistency_score: number;    // 0-1, fact consistency
  relevance_score: number;      // 0-1, context relevance
  
  calculate(
    original: Memory[],
    compressed: Memory[]
  ): QualityScore {
    return {
      coherence: this.assessCoherence(compressed),
      completeness: this.compareCompleteness(original, compressed),
      consistency: this.checkConsistency(compressed),
      relevance: this.measureRelevance(compressed),
      overall: this.calculateOverall()
    };
  }
}
```

## UI Components

### Memory Dashboard
```tsx
<MemoryDashboard
  projectId={projectId}
  showStats={true}
  showOptimizationSuggestions={true}
  allowManualOptimization={true}
  realTimeUpdates={true}
/>
```

### Compression Preview
```tsx
<CompressionPreview
  original={originalMemory}
  compressed={compressedMemory}
  showDiff={true}
  showQualityMetrics={true}
  allowAdjustment={true}
  onApprove={handleApprove}
/>
```

### Memory Timeline
```tsx
<MemoryTimeline
  memories={projectMemories}
  viewMode="compressed" | "full"
  showImportance={true}
  allowEditing={false}
  highlightRecent={true}
/>
```

## Security & Privacy

### Memory Isolation
- Project-level isolation
- No cross-project memory access
- Encrypted storage
- Secure memory operations

### Access Control
```sql
-- Memory access policies
CREATE POLICY "Users access own project memories" 
ON memory_store
FOR ALL USING (
  project_id IN (
    SELECT id FROM projects WHERE user_id = auth.uid()
  )
);
```

## Future Enhancements

1. **Adaptive Compression**
   - Learn optimal compression per user
   - Dynamic quality thresholds
   - Context-aware compression

2. **Distributed Memory**
   - Multi-model memory sharing
   - Cross-project memory pools
   - Federated learning

3. **Memory Visualization**
   - 3D memory maps
   - Relationship graphs
   - Importance heat maps

4. **Advanced Algorithms**
   - Neural compression
   - Quantum-inspired optimization
   - Biological memory models

## Related Systems
- AI Agent System (primary consumer)
- Content Generation System
- Analytics System (memory metrics)
- Cost Management System