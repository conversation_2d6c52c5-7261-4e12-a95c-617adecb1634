import { supabase } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';

// Database result types
interface ChapterResult {
  id: string;
  chapter_number: number;
  title: string | null;
  content: string;
  word_count: number;
  updated_at: string;
  project: {
    id: string;
    title: string;
    user_id: string;
  };
}

interface CharacterResult {
  id: string;
  name: string;
  description?: string;
  backstory?: string;
  updated_at: string;
  project: {
    id: string;
    title: string;
    user_id: string;
  };
}

interface LocationResult {
  id: string;
  content: string;
  updated_at: string;
  project: {
    id: string;
    title: string;
    user_id: string;
  };
}

export interface SearchResult {
  id: string;
  type: 'project' | 'character' | 'location' | 'chapter' | 'content';
  title: string;
  description: string;
  category: string;
  project?: string;
  projectId?: string;
  lastModified?: string;
  wordCount?: number;
  url: string;
  highlight?: string;
  score?: number;
}

export interface SearchOptions {
  query: string;
  types?: string[];
  projectId?: string;
  limit?: number;
  offset?: number;
  userId: string;
}

class SearchService {
  async search(options: SearchOptions): Promise<SearchResult[]> {
    const { query, types = ['all'], projectId, limit = 20, offset = 0, userId } = options;
    
    if (!query.trim()) {
      return [];
    }

    try {
      const results: SearchResult[] = [];
      const searchTerm = query.toLowerCase();
      
      // Search projects
      if (types.includes('all') || types.includes('project')) {
        const { data: projects, error } = await supabase
          .from('projects')
          .select('id, title, description, primary_genre, current_word_count, updated_at')
          .eq('user_id', userId)
          .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
          .order('updated_at', { ascending: false })
          .range(offset, offset + limit - 1);

        if (error) {
          logger.error('Error searching projects:', error);
        } else if (projects) {
          results.push(...projects.map(project => ({
            id: project.id,
            type: 'project' as const,
            title: project.title,
            description: project.description || '',
            category: project.primary_genre || 'Project',
            lastModified: project.updated_at,
            wordCount: project.current_word_count,
            url: `/projects/${project.id}`,
            score: this.calculateRelevanceScore(query, project.title, project.description)
          })));
        }
      }

      // Search chapters
      if (types.includes('all') || types.includes('chapter')) {
        let chaptersQuery = supabase
          .from('chapters')
          .select(`
            id, 
            chapter_number, 
            title, 
            content,
            word_count,
            updated_at,
            project:projects!inner(id, title, user_id)
          `)
          .eq('project.user_id', userId)
          .or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%`);

        if (projectId) {
          chaptersQuery = chaptersQuery.eq('project_id', projectId);
        }

        const { data: chapters, error } = await chaptersQuery
          .order('updated_at', { ascending: false })
          .range(offset, offset + limit - 1);

        if (error) {
          logger.error('Error searching chapters:', error);
        } else if (chapters) {
          results.push(...chapters.map((chapter: ChapterResult) => {
            const snippet = this.extractSnippet(chapter.content, searchTerm);
            return {
              id: chapter.id,
              type: 'chapter' as const,
              title: chapter.title || `Chapter ${chapter.chapter_number}`,
              description: snippet,
              category: 'Chapter',
              project: chapter.project.title,
              projectId: chapter.project.id,
              wordCount: chapter.word_count,
              lastModified: chapter.updated_at,
              url: `/projects/${chapter.project.id}/write?chapter=${chapter.chapter_number}`,
              highlight: snippet,
              score: this.calculateRelevanceScore(query, chapter.title, chapter.content)
            };
          }));
        }
      }

      // Search characters
      if (types.includes('all') || types.includes('character')) {
        let charactersQuery = supabase
          .from('characters')
          .select(`
            id,
            name,
            description,
            backstory,
            updated_at,
            project:projects!inner(id, title, user_id)
          `)
          .eq('project.user_id', userId)
          .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,backstory.ilike.%${searchTerm}%`);

        if (projectId) {
          charactersQuery = charactersQuery.eq('project_id', projectId);
        }

        const { data: characters, error } = await charactersQuery
          .order('updated_at', { ascending: false })
          .range(offset, offset + limit - 1);

        if (error) {
          logger.error('Error searching characters:', error);
        } else if (characters) {
          results.push(...characters.map((character: CharacterResult) => ({
            id: character.id,
            type: 'character' as const,
            title: character.name,
            description: character.description || character.backstory || 'No description available',
            category: 'Character',
            project: character.project.title,
            projectId: character.project.id,
            lastModified: character.updated_at,
            url: `/projects/${character.project.id}/story-bible?section=characters&id=${character.id}`,
            score: this.calculateRelevanceScore(query, character.name, character.description + ' ' + character.backstory)
          })));
        }
      }

      // Search locations
      if (types.includes('all') || types.includes('location')) {
        let locationsQuery = supabase
          .from('story_bible_sections')
          .select(`
            id,
            content,
            updated_at,
            project:projects!inner(id, title, user_id)
          `)
          .eq('project.user_id', userId)
          .eq('section_type', 'locations')
          .ilike('content', `%${searchTerm}%`);

        if (projectId) {
          locationsQuery = locationsQuery.eq('project_id', projectId);
        }

        const { data: locations, error } = await locationsQuery
          .order('updated_at', { ascending: false })
          .range(offset, offset + limit - 1);

        if (error) {
          logger.error('Error searching locations:', error);
        } else if (locations) {
          results.push(...locations.map((location: LocationResult) => {
            const locationData = this.parseLocationFromContent(location.content, searchTerm);
            return {
              id: location.id,
              type: 'location' as const,
              title: locationData.name,
              description: locationData.description,
              category: 'Location',
              project: location.project.title,
              projectId: location.project.id,
              lastModified: location.updated_at,
              url: `/projects/${location.project.id}/story-bible?section=locations&id=${location.id}`,
              score: this.calculateRelevanceScore(query, locationData.name, locationData.description)
            };
          }));
        }
      }

      // Sort by relevance score and limit results
      results.sort((a, b) => (b.score || 0) - (a.score || 0));
      
      return results.slice(0, limit);
    } catch (error) {
      logger.error('Search service error:', error);
      throw new Error('Failed to perform search');
    }
  }

  private calculateRelevanceScore(query: string, title: string, content?: string): number {
    const queryLower = query.toLowerCase();
    const titleLower = title?.toLowerCase() || '';
    const contentLower = content?.toLowerCase() || '';
    
    let score = 0;
    
    // Exact match in title
    if (titleLower === queryLower) score += 100;
    // Title starts with query
    else if (titleLower.startsWith(queryLower)) score += 50;
    // Title contains query
    else if (titleLower.includes(queryLower)) score += 25;
    
    // Word boundary matches in title
    const titleWords = titleLower.split(/\s+/);
    if (titleWords.some(word => word === queryLower)) score += 40;
    if (titleWords.some(word => word.startsWith(queryLower))) score += 20;
    
    // Content matches
    if (contentLower) {
      const contentWords = contentLower.split(/\s+/);
      const exactMatches = contentWords.filter(word => word === queryLower).length;
      const partialMatches = contentWords.filter(word => word.includes(queryLower)).length;
      
      score += Math.min(exactMatches * 10, 30);
      score += Math.min(partialMatches * 5, 15);
    }
    
    return score;
  }

  private extractSnippet(content: string, searchTerm: string, maxLength: number = 200): string {
    if (!content) return 'No content available';
    
    const lowerContent = content.toLowerCase();
    const lowerTerm = searchTerm.toLowerCase();
    const index = lowerContent.indexOf(lowerTerm);
    
    if (index === -1) {
      // If exact match not found, return beginning of content
      return content.substring(0, maxLength) + (content.length > maxLength ? '...' : '');
    }
    
    // Find sentence boundaries
    const start = Math.max(0, index - 50);
    const end = Math.min(content.length, index + searchTerm.length + 150);
    
    let snippet = content.substring(start, end);
    
    // Clean up snippet
    if (start > 0) snippet = '...' + snippet;
    if (end < content.length) snippet = snippet + '...';
    
    return snippet.trim();
  }

  private parseLocationFromContent(content: string, searchTerm: string): { name: string; description: string } {
    // Try to parse structured content
    try {
      const data = JSON.parse(content);
      if (data.name && data.description) {
        return { name: data.name, description: data.description };
      }
    } catch {
      // Not JSON, try to extract from text
    }
    
    // Extract name and description from text content
    const lines = content.split('\n').filter(line => line.trim());
    const name = lines[0] || 'Unknown Location';
    const description = lines.slice(1).join(' ').substring(0, 200) || 
                       this.extractSnippet(content, searchTerm);
    
    return { name, description };
  }

  async getSuggestions(query: string, userId: string, limit: number = 5): Promise<string[]> {
    if (!query || query.length < 2) return [];
    
    try {
      const suggestions = new Set<string>();
      
      // Get project title suggestions
      const { data: projects } = await supabase
        .from('projects')
        .select('title')
        .eq('user_id', userId)
        .ilike('title', `${query}%`)
        .limit(limit);
      
      if (projects) {
        projects.forEach(p => suggestions.add(p.title));
      }
      
      // Get character name suggestions
      const { data: characters } = await supabase
        .from('characters')
        .select('name, project:projects!inner(user_id)')
        .eq('project.user_id', userId)
        .ilike('name', `${query}%`)
        .limit(limit);
      
      if (characters) {
        characters.forEach(c => suggestions.add(c.name));
      }
      
      return Array.from(suggestions).slice(0, limit);
    } catch (error) {
      logger.error('Error getting search suggestions:', error);
      return [];
    }
  }
}

export const searchService = new SearchService();