/**
 * Enum types based on schema constraints and common values
 */

// Subscription and billing
export type SubscriptionStatus = 'active' | 'canceled' | 'past_due' | 'incomplete'

// Project statuses
export type ProjectStatus = 'planning' | 'writing' | 'editing' | 'completed' | 'paused' | 'archived'
export type ChapterStatus = 'planned' | 'writing' | 'review' | 'complete'
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed'
export type AgentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'

// Character and content
export type CharacterRole = 'protagonist' | 'antagonist' | 'supporting' | 'minor'
export type ContentRating = 'G' | 'PG' | 'PG-13' | 'R' | 'NC-17'
export type ContentType = 'chapter' | 'character' | 'story_bible' | 'reference'

// Project configuration
export type ProjectScope = 'standalone' | 'duology' | 'trilogy' | 'series' | 'epic_series' | 'anthology'
export type WritingStyle = 'literary' | 'commercial' | 'pulp' | 'experimental' | 'minimalist' | 'descriptive' | 'dialogue_heavy'
export type NarrativeVoice = 'first_person' | 'third_person_limited' | 'third_person_omniscient' | 'second_person' | 'multiple_pov'
export type Tense = 'present' | 'past' | 'mixed'
export type ChapterCountType = 'fixed' | 'flexible' | 'scene_based'

// Activity and session types
export type SessionType = 'writing' | 'editing' | 'planning'
export type ActionType = 'edit' | 'expand' | 'rewrite' | 'suggest'
export type EntryType = 'character_state' | 'plot_point' | 'world_rule' | 'timeline_event'
export type EventType = 'profile_used' | 'project_completed' | 'project_abandoned' | 'ai_generation' | 'export' | 'storage_use'

// File and resource types
export type FileType = 'document' | 'image' | 'map' | 'character_sheet' | 'research'

// System and metadata
export type CreatedBy = 'user' | 'ai_writer' | 'ai_editor'