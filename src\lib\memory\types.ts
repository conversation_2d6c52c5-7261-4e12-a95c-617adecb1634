export interface MemoryStats {
  totalTokensUsed: number
  tokenLimit: number
  breakdown: {
    chapters: number
    storyBible: number
    characters: number
    other: number
  }
  lastUpdated: string
}

export type CompressionStrategy = 'conservative' | 'balanced' | 'aggressive'

export interface CompressionOptions {
  strategy: CompressionStrategy
  preserveRecentHours?: number
  targetReduction?: number // 0-1 (percentage)
}

export interface MergeOptions {
  similarityThreshold: number // 0-1
  preserveRecentHours?: number
  mergeStrategy?: 'combine' | 'summarize'
}

export interface OptimizationResult {
  success: boolean
  tokensSaved: number
  compressionRatio: number
  message: string
  error?: string
  details?: {
    chaptersCompressed?: number
    contextsMerged?: number
    contextsRemoved?: number
    cacheCleared?: boolean
    metrics?: any
  }
}

export interface MemorySettings {
  project_id: string
  auto_optimization: {
    enabled: boolean
    threshold: number // Percentage (0-100)
    strategy: CompressionStrategy
    checkInterval: number // Minutes
    preserveRecentHours: number
    targetReduction: number // Percentage (0-100)
    notifyUser: boolean
  }
  retention_policy: {
    keepDays: number
    archiveAfterDays: number
    deleteAfterDays: number
  }
  compression_settings: {
    enableAutoCompression: boolean
    compressionThreshold: number // Word count
    compressionStrategy: CompressionStrategy
  }
  notifications: {
    notifyOnHighUsage: boolean
    notifyOnOptimization: boolean
    highUsageThreshold: number // Percentage
  }
  created_at?: string
  updated_at?: string
}

export interface MemoryOptimizationLog {
  id: string
  project_id: string
  optimization_type: 'auto' | 'manual'
  strategy: CompressionStrategy
  tokens_before: number
  tokens_after: number
  tokens_saved: number
  compression_ratio: number
  success: boolean
  error?: string
  metadata?: Record<string, any>
  created_at: string
}