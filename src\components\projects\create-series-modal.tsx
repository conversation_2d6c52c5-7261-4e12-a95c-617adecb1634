'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { Loader2 } from 'lucide-react'

interface CreateSeriesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CreateSeriesModal({ open, onOpenChange, onSuccess }: CreateSeriesModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    genre: '',
    plannedBookCount: '',
    publicationStatus: 'planning',
  })
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast({
        title: "Error",
        description: "Series title is required",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          genre: formData.genre || null,
          planned_book_count: formData.plannedBookCount ? parseInt(formData.plannedBookCount) : null,
          publication_status: formData.publicationStatus,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create series')
      }

      toast({
        title: "Success",
        description: "Series created successfully",
      })
      
      onOpenChange(false)
      onSuccess?.()
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        genre: '',
        plannedBookCount: '',
        publicationStatus: 'planning',
      })
    } catch (error) {
      logger.error('Error creating series:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create series",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Series</DialogTitle>
            <DialogDescription>
              Start a new book series. You can add books to it later.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 sm:gap-5 lg:gap-6 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Series Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Enter series title"
                disabled={loading}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Brief description of the series"
                rows={3}
                disabled={loading}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
              <div className="grid gap-2">
                <Label htmlFor="genre">Genre</Label>
                <Input
                  id="genre"
                  value={formData.genre}
                  onChange={(e) => setFormData({ ...formData, genre: e.target.value })}
                  placeholder="e.g., Fantasy, Mystery"
                  disabled={loading}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="plannedBookCount">Planned Books</Label>
                <Input
                  id="plannedBookCount"
                  type="number"
                  min="1"
                  value={formData.plannedBookCount}
                  onChange={(e) => setFormData({ ...formData, plannedBookCount: e.target.value })}
                  placeholder="Number of books"
                  disabled={loading}
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="publicationStatus">Publication Status</Label>
              <Select
                value={formData.publicationStatus}
                onValueChange={(value) => setFormData({ ...formData, publicationStatus: value })}
                disabled={loading}
              >
                <SelectTrigger id="publicationStatus">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="hiatus">Hiatus</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Series
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}