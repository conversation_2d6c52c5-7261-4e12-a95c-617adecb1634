import { useRef, useEffect, useCallback, useMemo } from 'react';
import { logger } from '@/lib/services/logger';

import { editor } from 'monaco-editor';
import { TIME_MS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

interface EditorPerformanceOptions {
  /** Maximum document size in characters before optimizations kick in */
  maxDocumentSize?: number;
  /** Debounce delay for change events in milliseconds */
  changeDebounceMs?: number;
  /** Whether to enable virtual scrolling for large documents */
  enableVirtualScrolling?: boolean;
  /** Whether to limit syntax highlighting for performance */
  limitSyntaxHighlighting?: boolean;
}

export function useEditorPerformance(
  editorRef: React.RefObject<editor.IStandaloneCodeEditor | null>,
  content: string,
  options: EditorPerformanceOptions = {}
) {
  const {
    maxDocumentSize = SIZE_LIMITS.MAX_DOCUMENT_CHARS, // 50k characters
    changeDebounceMs = 300,
    enableVirtualScrolling = true,
    limitSyntaxHighlighting = true,
  } = options;

  const debounceRef = useRef<NodeJS.Timeout | null>(null);
  const lastContentSizeRef = useRef(0);
  const isLargeDocumentRef = useRef(false);

  // Calculate if this is a large document
  const isLargeDocument = useMemo(() => {
    return content.length > maxDocumentSize;
  }, [content.length, maxDocumentSize]);

  // Performance metrics
  const performanceMetrics = useMemo(() => {
    const characterCount = content.length;
    const lineCount = content.split('\n').length;
    const wordCount = content.trim().split(/\s+/).filter(Boolean).length;
    
    return {
      characterCount,
      lineCount,
      wordCount,
      isLargeDocument,
      memoryUsageEstimate: Math.round(characterCount * SIZE_LIMITS.CHAR_TO_KB_RATIO / SIZE_LIMITS.KB_PER_MB), // Rough estimate in KB
    };
  }, [content, isLargeDocument]);

  // Apply performance optimizations based on document size
  const applyPerformanceOptimizations = useCallback(() => {
    const editor = editorRef.current;
    if (!editor) return;

    if (isLargeDocument) {
      // Optimizations for large documents
      editor.updateOptions({
        // Disable expensive features
        renderWhitespace: 'none',
        renderControlCharacters: false,
        renderLineHighlight: 'none',
        
        // Optimize scrolling
        scrollBeyondLastLine: false,
        smoothScrolling: false, // Disable for better performance
        
        // Disable minimap and overview ruler
        minimap: { enabled: false },
        overviewRulerLanes: 0,
        hideCursorInOverviewRuler: true,
        
        // Limit syntax highlighting if enabled
        ...(limitSyntaxHighlighting && {
          semanticHighlighting: { enabled: false },
          'bracketPairColorization.enabled': false,
        }),
        
        // Optimize suggestions
        quickSuggestions: false,
        suggestOnTriggerCharacters: false,
        acceptSuggestionOnCommitCharacter: false,
        tabCompletion: 'off',
        wordBasedSuggestions: 'off',
        
        // Disable hover and parameter hints
        hover: { enabled: false },
        parameterHints: { enabled: false },
        
        // Optimize find widget
        find: {
          seedSearchStringFromSelection: 'never',
          autoFindInSelection: 'never',
        },
      });

      // Enable virtual scrolling if supported and enabled
      if (enableVirtualScrolling) {
        // Monaco doesn't have native virtual scrolling, but we can optimize rendering
        editor.updateOptions({
          scrollbar: {
            vertical: 'visible',
            horizontal: 'hidden',
            verticalSliderSize: 8,
            alwaysConsumeMouseWheel: false,
          },
        });
      }
    } else {
      // Restore normal settings for smaller documents
      editor.updateOptions({
        renderWhitespace: 'none',
        renderControlCharacters: false,
        renderLineHighlight: 'none',
        scrollBeyondLastLine: false,
        smoothScrolling: true,
        minimap: { enabled: false },
        overviewRulerLanes: 0,
        hideCursorInOverviewRuler: true,
        quickSuggestions: false,
        suggestOnTriggerCharacters: false,
        acceptSuggestionOnCommitCharacter: false,
        tabCompletion: 'off',
        wordBasedSuggestions: 'off',
        hover: { enabled: false },
        parameterHints: { enabled: false },
      });
    }

    isLargeDocumentRef.current = isLargeDocument;
  }, [isLargeDocument, enableVirtualScrolling, limitSyntaxHighlighting, editorRef]);

  // Debounced change handler for performance
  const debouncedOptimization = useCallback(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    
    debounceRef.current = setTimeout(() => {
      applyPerformanceOptimizations();
    }, changeDebounceMs);
  }, [applyPerformanceOptimizations, changeDebounceMs]);

  // Monitor document size changes
  useEffect(() => {
    const currentSize = content.length;
    const sizeDifference = Math.abs(currentSize - lastContentSizeRef.current);
    
    // Only re-optimize if there's a significant size change (> 5k characters)
    if (sizeDifference > TIME_MS.TOAST_DURATION || isLargeDocumentRef.current !== isLargeDocument) {
      debouncedOptimization();
      lastContentSizeRef.current = currentSize;
    }
  }, [content.length, isLargeDocument, debouncedOptimization]);

  // Initial optimization on mount
  useEffect(() => {
    applyPerformanceOptimizations();
  }, [applyPerformanceOptimizations]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  // Force re-optimization when needed
  const forceOptimization = useCallback(() => {
    applyPerformanceOptimizations();
  }, [applyPerformanceOptimizations]);

  // Memory cleanup for large documents
  const cleanupMemory = useCallback(() => {
    const editor = editorRef.current;
    if (!editor) return;

    // Force garbage collection of editor internals if possible
    try {
      // Clear undo/redo stack if document is very large
      if (performanceMetrics.characterCount > SIZE_LIMITS.LARGE_DOCUMENT_THRESHOLD) {
        const model = editor.getModel();
        if (model) {
          // This clears the undo stack
          editor.pushUndoStop();
        }
      }
    } catch (error) {
      logger.warn('Memory cleanup failed:', error);
    }
  }, [editorRef, performanceMetrics.characterCount]);

  return {
    performanceMetrics,
    isLargeDocument,
    forceOptimization,
    cleanupMemory,
    applyPerformanceOptimizations,
  };
}