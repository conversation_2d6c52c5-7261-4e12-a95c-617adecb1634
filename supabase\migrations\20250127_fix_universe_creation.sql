-- Drop the existing universes table if it exists (since it's using BIGINT instead of UUID)
DROP TABLE IF EXISTS universes CASCADE;

-- Create universes table with correct data types
CREATE TABLE universes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    rules <PERSON><PERSON>N<PERSON> DEFAULT '{}',
    is_public BOOLEAN DEFAULT false,
    settings JSONB DEFAULT '{}',
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on created_by for performance
CREATE INDEX idx_universes_created_by ON universes(created_by);

-- First, add universe_id to series table if it doesn't exist
ALTER TABLE series 
ADD COLUMN IF NOT EXISTS universe_id UUID REFERENCES universes(id);

-- Create an index on the new foreign key for performance
CREATE INDEX IF NOT EXISTS idx_series_universe_id ON series(universe_id);

-- Now enable Row Level Security on universes
ALTER TABLE universes ENABLE ROW LEVEL SECURITY;

-- Create policies for universes (now that series.universe_id exists)
CREATE POLICY "Users can view universes they created or are part of" ON universes
    FOR SELECT 
    USING (
        auth.uid() = created_by OR
        is_public = true OR
        EXISTS (
            SELECT 1 FROM series s
            WHERE s.universe_id = universes.id
            AND s.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create universes" ON universes
    FOR INSERT 
    WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own universes" ON universes
    FOR UPDATE 
    USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own universes" ON universes
    FOR DELETE 
    USING (auth.uid() = created_by);

-- Create universe_timeline_events table if it doesn't exist
CREATE TABLE IF NOT EXISTS universe_timeline_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    universe_id UUID REFERENCES universes(id) ON DELETE CASCADE,
    event_date TIMESTAMPTZ,
    relative_date TEXT,
    event_name TEXT NOT NULL,
    description TEXT,
    affected_series UUID[] DEFAULT '{}',
    event_type TEXT CHECK (event_type IN ('historical', 'cataclysm', 'political', 'discovery', 'character', 'other')),
    importance TEXT CHECK (importance IN ('universe-changing', 'major', 'minor', 'personal')) DEFAULT 'major',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for universe_timeline_events
CREATE INDEX IF NOT EXISTS idx_universe_timeline_universe ON universe_timeline_events(universe_id);

-- Enable RLS on universe_timeline_events
ALTER TABLE universe_timeline_events ENABLE ROW LEVEL SECURITY;

-- Create policies for universe_timeline_events
CREATE POLICY "Users can view timeline events for universes they're part of" ON universe_timeline_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND (
                u.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM series s
                    WHERE s.universe_id = u.id
                    AND s.user_id = auth.uid()
                )
            )
        )
    );

CREATE POLICY "Users can create timeline events for their universes" ON universe_timeline_events
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND u.created_by = auth.uid()
        )
    );

CREATE POLICY "Users can update timeline events for their universes" ON universe_timeline_events
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND u.created_by = auth.uid()
        )
    );

CREATE POLICY "Users can delete timeline events for their universes" ON universe_timeline_events
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM universes u
            WHERE u.id = universe_id
            AND u.created_by = auth.uid()
        )
    );