import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()
    const body = await request.json()
    const { suggestionId, action } = body

    if (!suggestionId || !action) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Store feedback in database for learning
    // This could be used to improve future suggestions
    const { error } = await supabase
      .from('ai_suggestion_feedback')
      .insert({
        suggestion_id: suggestionId,
        user_id: user.id,
        action,
        created_at: new Date().toISOString()
      })

    if (error) {
      logger.info('Feedback storage failed (table may not exist):', error.message)
      // Don't fail the request if feedback storage fails
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    logger.error('Feedback error:', error)
    return NextResponse.json(
      { error: 'Failed to record feedback' },
      { status: 500 }
    )
  }
})