import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { createServerClient } from '@/lib/supabase';
import { apiSchemas } from '@/lib/validation/api-schemas';

describe('Projects API - Orchestration Integration', () => {
  let authToken: string;
  let userId: string;
  let createdProjectIds: string[] = [];

  beforeAll(async () => {
    // Setup test user and auth token
    const supabase = createServerClient();
    const { data: { user }, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'TestPassword123'
    });
    
    if (error || !user) {
      throw new Error('Failed to authenticate test user');
    }
    
    userId = user.id;
    authToken = user.access_token || '';
  });

  afterAll(async () => {
    // Cleanup created projects
    const supabase = createServerClient();
    for (const projectId of createdProjectIds) {
      await supabase.from('projects').delete().eq('id', projectId);
    }
  });

  describe('POST /api/projects - with orchestration', () => {
    it('should create project without orchestration when flag is false', async () => {
      const projectData = {
        title: 'Test Project Without Orchestration',
        description: 'A test project',
        genre: 'Fantasy',
        target_word_count: 80000,
        target_chapters: 20,
        startOrchestration: false
      };

      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(projectData)
      });

      expect(response.status).toBe(201);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.project).toBeDefined();
      expect(data.project.title).toBe(projectData.title);
      expect(data.orchestration).toBeNull();
      
      createdProjectIds.push(data.project.id);
    });

    it('should fail when startOrchestration is true but missing required fields', async () => {
      const projectData = {
        title: 'Test Project Missing Fields',
        description: 'A test project',
        genre: 'Fantasy',
        startOrchestration: true
        // Missing storyPrompt and projectSelections
      };

      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(projectData)
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('When startOrchestration is true');
    });

    it('should create project and start orchestration with valid data', async () => {
      const projectData = {
        title: 'Test Project With Orchestration',
        description: 'An AI-generated epic fantasy novel',
        genre: 'Fantasy',
        target_word_count: 100000,
        target_chapters: 25,
        startOrchestration: true,
        storyPrompt: 'A young wizard discovers an ancient prophecy that reveals they are the key to preventing a cosmic catastrophe. They must gather unlikely allies and master forbidden magic.',
        projectSelections: {
          genre: 'Epic Fantasy',
          tone: 'Dark and Mysterious',
          style: 'Literary Fiction',
          pov: 'Third Person Limited',
          tense: 'Past Tense',
          themes: ['Good vs Evil', 'Coming of Age', 'Sacrifice'],
          subgenres: ['Dark Fantasy', 'High Fantasy']
        }
      };

      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(projectData)
      });

      expect(response.status).toBe(201);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.project).toBeDefined();
      expect(data.project.title).toBe(projectData.title);
      
      // Verify orchestration was started
      expect(data.orchestration).toBeDefined();
      expect(data.orchestration.started).toBe(true);
      expect(data.orchestration.message).toBe('AI generation started');
      expect(data.orchestration.estimatedDuration).toBe('10-15 minutes');
      expect(data.orchestration.progressEndpoint).toContain(`/api/orchestration/progress?projectId=${data.project.id}`);
      
      createdProjectIds.push(data.project.id);
    });

    it('should validate orchestration fields properly', async () => {
      const projectData = {
        title: 'Test Validation',
        startOrchestration: true,
        storyPrompt: 'Too short', // Less than 10 characters
        projectSelections: {
          genre: 'Fantasy',
          tone: '', // Empty required field
          style: 'Literary',
          pov: 'Third Person',
          tense: 'Past'
        }
      };

      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(projectData)
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBeDefined();
    });
  });

  describe('Orchestration Progress Tracking', () => {
    it('should track orchestration progress after project creation', async () => {
      // First create a project with orchestration
      const projectData = {
        title: 'Progress Tracking Test',
        description: 'Testing progress tracking',
        startOrchestration: true,
        storyPrompt: 'A detective investigates supernatural crimes in a steampunk city where magic and technology collide.',
        projectSelections: {
          genre: 'Mystery',
          tone: 'Noir',
          style: 'Hardboiled',
          pov: 'First Person',
          tense: 'Past Tense'
        }
      };

      const createResponse = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(projectData)
      });

      const createData = await createResponse.json();
      createdProjectIds.push(createData.project.id);

      // Check progress endpoint
      const progressResponse = await fetch(`/api/orchestration/progress?projectId=${createData.project.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(progressResponse.status).toBe(200);
      const progressData = await progressResponse.json();
      
      expect(progressData.projectId).toBe(createData.project.id);
      expect(progressData.status).toBeDefined();
      expect(['not_started', 'in_progress', 'completed', 'failed']).toContain(progressData.status);
    });
  });
});