# BookScribe AI Agent System Overview

## Table of Contents
- [Introduction](#introduction)
- [Agent Architecture](#agent-architecture)
- [Base Agent Class](#base-agent-class)
- [Agent Types](#agent-types)
- [Agent Orchestration](#agent-orchestration)
- [Context Management](#context-management)
- [Model Selection Strategy](#model-selection-strategy)
- [Quality Assessment](#quality-assessment)
- [Agent Communication](#agent-communication)

## Introduction

BookScribe's AI Agent System is a sophisticated multi-agent architecture designed specifically for novel writing. The system maintains narrative consistency across hundreds of thousands of words through specialized agents that handle different aspects of the creative writing process.

### Key Features
- **7 Specialized Agents**: Each focused on specific writing tasks
- **Context-Aware Processing**: Maintains consistency across long-form content
- **Concurrent Execution**: Up to 3 agents can work simultaneously
- **Quality Assessment**: Built-in validation and scoring
- **Adaptive Planning**: Dynamic adjustment based on user changes
- **Model Selection**: Subscription-tier aware AI model routing

## Agent Architecture

### System Overview

```mermaid
graph TB
    subgraph "Agent System Core"
        BaseAgent[Base Agent Class]
        Context[Book Context]
        ModelSelector[AI Model Selector]
        WordCounter[Word Counter Service]
    end
    
    subgraph "Specialized Agents"
        StoryArch[Story Architect]
        CharDev[Character Developer]  
        ChapterPlan[Chapter Planner]
        Writing[Writing Agent]
        VoiceWriting[Voice-Aware Writing]
        Editor[Editor Agent]
        Adaptive[Adaptive Planning]
    end
    
    subgraph "Orchestration"
        Orchestrator[Advanced Orchestrator]
        Queue[Task Queue]
        Monitor[Progress Monitor]
    end
    
    BaseAgent --> StoryArch
    BaseAgent --> CharDev
    BaseAgent --> ChapterPlan
    BaseAgent --> Writing
    BaseAgent --> VoiceWriting
    BaseAgent --> Editor
    BaseAgent --> Adaptive
    
    Context --> BaseAgent
    ModelSelector --> BaseAgent
    WordCounter --> BaseAgent
    
    Orchestrator --> Queue
    Queue --> StoryArch
    Queue --> CharDev
    Queue --> ChapterPlan
    Queue --> Writing
```

## Base Agent Class

The `BaseAgent` class provides the foundation for all specialized agents:

### Core Features

```typescript
export abstract class BaseAgent {
  protected aiClient = vercelAIClient;
  protected context: BookContext;

  constructor(context: BookContext) {
    this.context = context;
  }

  // Standard text completion
  protected async createCompletion(
    prompt: string,
    systemPrompt?: string,
    model: string = AI_MODELS.PRIMARY,
    temperature?: number,
    maxTokens?: number,
    streaming?: boolean,
    streamingOptions?: StreamingOptions
  )

  // Structured JSON completion with schema validation
  protected async createStructuredCompletion<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    systemPrompt?: string,
    // ... other parameters
  ): Promise<T>

  // Word usage tracking
  protected async trackWordUsage(
    wordCount: number, 
    taskType: string
  ): Promise<void>

  // System prompt generation with project context
  protected createSystemPrompt(
    role: string, 
    instructions: string
  ): string

  // Abstract method for agent execution
  abstract execute(): Promise<unknown>;
}
```

### Key Responsibilities

1. **AI Model Integration**
   - Manages communication with OpenAI via Vercel AI SDK
   - Handles both streaming and non-streaming responses
   - Supports structured JSON output with Zod validation

2. **Context Management**
   - Maintains project settings and selections
   - Builds context-aware system prompts
   - Ensures consistency across all agent operations

3. **Model Selection**
   - Integrates with AI Model Selector service
   - Respects subscription tier limitations
   - Automatically selects appropriate models based on task type

4. **Usage Tracking**
   - Monitors word generation for billing
   - Tracks usage by agent type and task
   - Estimates words from token counts for streaming

## Agent Types

### 1. Story Architect Agent
**File**: `src/lib/agents/story-architect.ts`
**Purpose**: Creates comprehensive story structures and plot frameworks

#### Capabilities
- Generates detailed story outlines
- Creates plot structures (three-act, hero's journey, etc.)
- Defines major themes and conflicts
- Establishes story arcs and turning points
- Creates world-building elements

#### Output Structure
```typescript
interface StoryStructure {
  title: string;
  logline: string;
  themes: {
    primary: string[];
    philosophical: string[];
    emotional: string[];
  };
  structure: {
    act1: ActStructure;
    act2: ActStructure;
    act3: ActStructure;
  };
  worldBuilding: WorldDetails;
  conflicts: ConflictStructure[];
}
```

### 2. Character Developer Agent
**File**: `src/lib/agents/character-developer.ts`
**Purpose**: Creates detailed character profiles with psychological depth

#### Capabilities
- Develops character backstories
- Creates personality profiles (MBTI, Enneagram)
- Defines character arcs and growth
- Establishes character relationships
- Creates unique character voices

#### Output Structure
```typescript
interface CharacterProfile {
  name: string;
  role: 'protagonist' | 'antagonist' | 'supporting';
  personality: PersonalityProfile;
  backstory: BackstoryDetails;
  arc: CharacterArc;
  relationships: Relationship[];
  voice: VoiceProfile;
}
```

### 3. Chapter Planner Agent
**File**: `src/lib/agents/chapter-planner.ts`
**Purpose**: Plans detailed chapter outlines with scene breakdowns

#### Capabilities
- Creates chapter-by-chapter outlines
- Plans scene sequences
- Manages pacing and tension
- Tracks character states
- Ensures plot progression

#### Output Structure
```typescript
interface ChapterPlan {
  chapterNumber: number;
  title: string;
  purpose: string;
  scenes: Scene[];
  characterStates: CharacterState[];
  plotProgression: PlotPoint[];
  targetWordCount: number;
}
```

### 4. Writing Agent
**File**: `src/lib/agents/writing-agent.ts`
**Purpose**: Generates actual chapter content with narrative flow

#### Capabilities
- Writes complete chapter content
- Maintains consistent narrative voice
- Creates dialogue and descriptions
- Manages scene transitions
- Follows chapter outlines

### 5. Voice-Aware Writing Agent
**File**: `src/lib/agents/voice-aware-writing-agent.ts`
**Purpose**: Enhanced writing agent with character voice consistency

#### Capabilities
- Analyzes character voice patterns
- Maintains voice consistency in dialogue
- Adapts narrative style per character POV
- Tracks voice metrics
- Provides voice coaching

### 6. Editor Agent
**File**: `src/lib/agents/editor-agent.ts`
**Purpose**: Improves prose quality and fixes issues

#### Capabilities
- Line editing for clarity
- Style improvements
- Grammar and punctuation fixes
- Consistency checking
- Pacing adjustments

### 7. Adaptive Planning Agent
**File**: `src/lib/agents/adaptive-planning-agent.ts`
**Purpose**: Adjusts plans based on story evolution

#### Capabilities
- Analyzes user changes
- Recommends plot adjustments
- Maintains story consistency
- Suggests character development
- Adapts to new directions

## Agent Orchestration

The `AdvancedOrchestrator` manages the complex coordination of multiple agents:

### Orchestration Flow

```mermaid
sequenceDiagram
    participant User
    participant Orchestrator
    participant Queue
    participant Agent1
    participant Agent2
    participant Context
    
    User->>Orchestrator: Start generation
    Orchestrator->>Queue: Initialize tasks
    
    par Concurrent Execution
        Queue->>Agent1: Execute task
        Agent1->>Context: Read context
        Agent1->>Agent1: Generate content
        Agent1->>Context: Update context
    and
        Queue->>Agent2: Execute task
        Agent2->>Context: Read context
        Agent2->>Agent2: Generate content
        Agent2->>Context: Update context
    end
    
    Agent1-->>Queue: Complete
    Agent2-->>Queue: Complete
    Queue-->>Orchestrator: All tasks done
    Orchestrator-->>User: Generation complete
```

### Key Features

1. **Concurrent Processing**
   - Maximum 3 agents running simultaneously
   - Intelligent task scheduling
   - Dependency management

2. **Progress Tracking**
   - Real-time status updates
   - Completion percentage
   - Error handling and retry

3. **Context Synchronization**
   - Shared BookContext across agents
   - Atomic updates
   - Consistency guarantees

## Context Management

### BookContext Structure

```typescript
interface BookContext {
  // Core identifiers
  projectId: string;
  userId?: string;
  
  // Project configuration
  settings: ProjectSettings;
  projectSelections: ProjectSettings;
  
  // Generation parameters
  storyPrompt?: string;
  targetWordCount?: number;
  targetChapters?: number;
  
  // Subscription info
  subscription?: SubscriptionInfo;
  
  // Agent outputs
  storyStructure?: StoryStructure;
  characters?: CharacterProfile[];
  chapterPlans?: ChapterPlan[];
  
  // State tracking
  currentChapter?: number;
  generatedContent?: Map<number, string>;
}
```

### Context Flow

1. **Initialization**: Created with project settings
2. **Enrichment**: Each agent adds its output
3. **Propagation**: Passed to subsequent agents
4. **Persistence**: Saved to database

## Model Selection Strategy

### Subscription-Based Routing

```typescript
// Model selection based on tier and task
const modelSelection = aiModelSelector.selectModel(
  subscription,
  requestedModel,
  taskType
);

// Tiers and models
const TIER_MODELS = {
  free: ['gpt-4o-mini'],
  starter: ['gpt-4o-mini', 'gpt-4-turbo'],
  professional: ['gpt-4o-mini', 'gpt-4-turbo', 'gpt-4'],
  enterprise: ['gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'claude-3']
};
```

### Task Type Inference

```typescript
const TASK_TYPES = {
  STORY_STRUCTURE: 'story_structure',
  CHARACTER_DEVELOPMENT: 'character_development',
  CHAPTER_PLANNING: 'chapter_planning',
  CONTENT_GENERATION: 'content_generation',
  EDITING: 'editing',
  ANALYSIS: 'analysis'
};
```

## Quality Assessment

### Built-in Quality Metrics

1. **Content Quality**
   - Readability scores
   - Narrative consistency
   - Character voice accuracy
   - Plot coherence

2. **Technical Quality**
   - Grammar correctness
   - Spelling accuracy
   - Punctuation consistency
   - Format compliance

3. **Creative Quality**
   - Originality score
   - Emotional impact
   - Pacing effectiveness
   - Theme integration

### Quality Scoring

```typescript
interface QualityScore {
  overall: number; // 0-100
  dimensions: {
    technical: number;
    creative: number;
    consistency: number;
    engagement: number;
  };
  issues: QualityIssue[];
  suggestions: string[];
}
```

## Agent Communication

### Handoff Protocol

Agents communicate through structured handoffs:

```typescript
interface AgentHandoff {
  fromAgent: string;
  toAgent: string;
  timestamp: Date;
  data: {
    completed: string[];
    inProgress: string[];
    required: string[];
    context: any;
  };
  metadata: {
    wordCount: number;
    quality: QualityScore;
    issues: string[];
  };
}
```

### Communication Patterns

1. **Sequential Handoff**
   - Story Architect → Character Developer
   - Character Developer → Chapter Planner
   - Chapter Planner → Writing Agent

2. **Parallel Execution**
   - Multiple Chapter Planners
   - Character + World Building
   - Writing + Editing

3. **Feedback Loops**
   - Adaptive Planning → All Agents
   - Editor → Writing Agent
   - Quality Assessment → All

## Best Practices

### Agent Development

1. **Single Responsibility**
   - Each agent has one clear purpose
   - Avoid feature creep
   - Maintain focus

2. **Context Awareness**
   - Always use BookContext
   - Respect project settings
   - Maintain consistency

3. **Error Handling**
   - Graceful degradation
   - Meaningful error messages
   - Retry mechanisms

4. **Performance**
   - Stream when possible
   - Cache repeated operations
   - Optimize token usage

### Integration Guidelines

1. **Model Selection**
   - Respect tier limits
   - Choose appropriate models
   - Track usage

2. **Quality Control**
   - Validate all outputs
   - Score quality
   - Provide feedback

3. **Orchestration**
   - Define clear dependencies
   - Handle concurrency
   - Monitor progress