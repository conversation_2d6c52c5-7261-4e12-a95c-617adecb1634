-- Create readability_progress table for tracking readability metrics over time
CREATE TABLE IF NOT EXISTS readability_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  overall_score FLOAT NOT NULL CHECK (overall_score >= 0 AND overall_score <= 100),
  flesch_kincaid FLOAT NOT NULL,
  gunning_fog FLOAT NOT NULL,
  coleman_liau FLOAT NOT NULL,
  ari FLOAT NOT NULL,
  sentences INTEGER NOT NULL CHECK (sentences >= 0),
  words INTEGER NOT NULL CHECK (words >= 0),
  syllables INTEGER NOT NULL CHECK (syllables >= 0),
  complex_words INTEGER NOT NULL CHECK (complex_words >= 0),
  tracked_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create indexes for performance
CREATE INDEX idx_readability_progress_project_id ON readability_progress(project_id);
CREATE INDEX idx_readability_progress_user_id ON readability_progress(user_id);
CREATE INDEX idx_readability_progress_tracked_at ON readability_progress(tracked_at DESC);

-- Add RLS (Row Level Security) policies
ALTER TABLE readability_progress ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view progress for projects they have access to
CREATE POLICY "Users can view readability progress for their projects" 
  ON readability_progress
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = readability_progress.project_id
      AND (
        projects.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM project_collaborators
          WHERE project_collaborators.project_id = projects.id
          AND project_collaborators.user_id = auth.uid()
        )
      )
    )
  );

-- Policy: Users can insert progress for projects they can edit
CREATE POLICY "Users can track readability progress for projects they can edit"
  ON readability_progress
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id
    AND EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = readability_progress.project_id
      AND (
        projects.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM project_collaborators
          WHERE project_collaborators.project_id = projects.id
          AND project_collaborators.user_id = auth.uid()
          AND project_collaborators.role IN ('editor', 'admin')
        )
      )
    )
  );

-- Policy: Users can delete their own progress records
CREATE POLICY "Users can delete their own readability progress"
  ON readability_progress
  FOR DELETE
  USING (user_id = auth.uid());