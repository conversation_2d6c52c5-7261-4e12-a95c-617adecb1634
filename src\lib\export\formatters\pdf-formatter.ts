import { jsPDF } from 'jspdf'
import { BaseFormatter } from './base-formatter'
import { ExportOptions, ExportProject } from '../export-types'
import { Chapter } from '@/lib/db/types'

export class PDFFormatter extends BaseFormatter {
  private doc: jsPDF;
  private currentY: number = 20;
  private pageHeight: number = 297; // A4 height in mm
  private pageWidth: number = 210; // A4 width in mm
  private margin: { top: number; bottom: number; left: number; right: number };
  private lineHeight: number;
  private fontSize: number;

  constructor(options: ExportOptions) {
    super();
    
    this.margin = {
      top: options.customStyling?.margins?.top || 25,
      bottom: options.customStyling?.margins?.bottom || 25,
      left: options.customStyling?.margins?.left || 25,
      right: options.customStyling?.margins?.right || 25
    };

    this.fontSize = options.customStyling?.fontSize || 12;
    this.lineHeight = options.customStyling?.lineHeight || 1.5;

    this.doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    this.doc.setFont(options.customStyling?.fontFamily || 'helvetica');
    this.doc.setFontSize(this.fontSize);
  }

  async generatePDF(
    project: ExportProject,
    chapters: Chapter[],
    options: ExportOptions,
    watermarkText?: string
  ): Promise<Blob> {
    // Add metadata
    if (options.includeMetadata) {
      this.addMetadata(project);
    }

    // Add title page
    this.addTitlePage(project);

    // Add front matter
    if (options.includeFrontMatter) {
      this.addFrontMatter(project);
    }

    // Add table of contents
    if (options.includeTableOfContents) {
      this.addTableOfContents(chapters);
    }

    // Add chapters
    for (const chapter of chapters) {
      if (options.excludeEmptyChapters && !chapter.content?.trim()) {
        continue;
      }
      this.addChapter(chapter, options);
    }

    // Add watermark to all pages
    if (watermarkText) {
      this.addWatermarkToAllPages(watermarkText);
    }

    return this.doc.output('blob');
  }

  private addMetadata(project: ExportProject): void {
    this.doc.setProperties({
      title: project.title,
      author: project.author_name || 'Unknown Author',
      subject: project.description || '',
      keywords: project.genre || '',
      creator: 'BookScribe AI'
    });
  }

  private addTitlePage(project: ExportProject): void {
    // Center title
    this.doc.setFontSize(32);
    const titleWidth = this.doc.getTextWidth(project.title);
    this.doc.text(project.title, (this.pageWidth - titleWidth) / 2, 100);

    // Author
    this.doc.setFontSize(18);
    if (project.author_name) {
      const authorWidth = this.doc.getTextWidth(project.author_name);
      this.doc.text(project.author_name, (this.pageWidth - authorWidth) / 2, 120);
    }

    // Genre
    if (project.genre) {
      this.doc.setFontSize(14);
      const genreWidth = this.doc.getTextWidth(project.genre);
      this.doc.text(project.genre, (this.pageWidth - genreWidth) / 2, 140);
    }

    this.doc.addPage();
    this.currentY = this.margin.top;
  }

  private addFrontMatter(project: ExportProject): void {
    // Copyright page
    this.doc.setFontSize(10);
    this.doc.text('Copyright © ' + new Date().getFullYear() + ' ' + (project.author_name || 'Author'), this.margin.left, this.currentY);
    this.currentY += 10;
    this.doc.text('All rights reserved.', this.margin.left, this.currentY);
    
    if (project.isbn) {
      this.currentY += 10;
      this.doc.text('ISBN: ' + project.isbn, this.margin.left, this.currentY);
    }

    // Dedication
    if (project.dedication) {
      this.doc.addPage();
      this.currentY = this.margin.top;
      this.doc.setFontSize(14);
      this.doc.text('Dedication', this.margin.left, this.currentY);
      this.currentY += 10;
      this.doc.setFontSize(this.fontSize);
      this.addMultilineText(project.dedication);
    }

    // Acknowledgments
    if (project.acknowledgments) {
      this.doc.addPage();
      this.currentY = this.margin.top;
      this.doc.setFontSize(14);
      this.doc.text('Acknowledgments', this.margin.left, this.currentY);
      this.currentY += 10;
      this.doc.setFontSize(this.fontSize);
      this.addMultilineText(project.acknowledgments);
    }

    this.doc.addPage();
    this.currentY = this.margin.top;
  }

  private addTableOfContents(chapters: Chapter[]): void {
    this.doc.setFontSize(18);
    this.doc.text('Table of Contents', this.margin.left, this.currentY);
    this.currentY += 15;

    this.doc.setFontSize(this.fontSize);
    chapters.forEach((chapter, index) => {
      const chapterTitle = `Chapter ${index + 1}: ${chapter.title}`;
      this.doc.text(chapterTitle, this.margin.left, this.currentY);
      this.currentY += 8;

      if (this.currentY > this.pageHeight - this.margin.bottom) {
        this.doc.addPage();
        this.currentY = this.margin.top;
      }
    });

    this.doc.addPage();
    this.currentY = this.margin.top;
  }

  private addChapter(chapter: Chapter, options: ExportOptions): void {
    // Chapter title
    this.doc.setFontSize(18);
    const chapterNumber = this.formatChapterNumber(chapter.chapter_number, options.chapterNumbering);
    const chapterTitle = `Chapter ${chapterNumber}: ${chapter.title}`;
    this.doc.text(chapterTitle, this.margin.left, this.currentY);
    this.currentY += 15;

    // Chapter content
    this.doc.setFontSize(this.fontSize);
    const processedContent = this.processContent(chapter.content || '', options);
    this.addMultilineText(processedContent);

    // Add page break after chapter
    if (options.includeChapterBreaks) {
      this.doc.addPage();
      this.currentY = this.margin.top;
    }
  }

  private processContent(content: string, options: ExportOptions): string {
    let processed = content;

    // Apply formatting
    processed = this.processSceneBreaks(processed, options.sceneBreakSymbol);
    processed = this.cleanWhitespace(processed);
    processed = this.convertSmartQuotes(processed);
    processed = this.convertEmDashes(processed);

    return processed;
  }

  private addMultilineText(text: string): void {
    const lines = this.doc.splitTextToSize(text, this.pageWidth - this.margin.left - this.margin.right);
    
    lines.forEach((line: string) => {
      if (this.currentY > this.pageHeight - this.margin.bottom) {
        this.doc.addPage();
        this.currentY = this.margin.top;
      }
      
      this.doc.text(line, this.margin.left, this.currentY);
      this.currentY += this.fontSize * this.lineHeight * 0.3527; // Convert pt to mm
    });
  }

  private addWatermarkToAllPages(watermarkText: string): void {
    const totalPages = this.doc.getNumberOfPages();
    
    for (let i = 1; i <= totalPages; i++) {
      this.doc.setPage(i);
      this.doc.setFontSize(48);
      this.doc.setTextColor(200, 200, 200);
      this.doc.setFont('helvetica', 'bold');
      
      // Rotate and center watermark
      const textWidth = this.doc.getTextWidth(watermarkText);
      const x = (this.pageWidth - textWidth) / 2;
      const y = this.pageHeight / 2;
      
      this.doc.text(watermarkText, x, y, { angle: 45 });
      
      // Reset text color
      this.doc.setTextColor(0, 0, 0);
      this.doc.setFont('helvetica', 'normal');
    }
  }
}