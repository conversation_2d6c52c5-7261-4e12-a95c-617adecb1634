'use client';

import { useState, useEffect, useCallback } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  Database, 
  Zap, 
  Users, 
  MapPin, 
  Heart, 
  Calendar,
  TrendingUp,
  Compass,
  Merge,
  RefreshCw
} from 'lucide-react';

interface MemoryStats {
  totalChunks: number;
  totalTokens: number;
  chunksByType: Record<string, number>;
  averageImportance: number;
  compressionRatio: number;
  lastCompression: Date | null;
}

interface MemoryDashboardProps {
  projectId: string;
  onOptimize?: () => void;
}

export function MemoryDashboard({ projectId, onOptimize }: MemoryDashboardProps) {
  const [stats, setStats] = useState<MemoryStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const fetchMemoryStats = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/memory/stats?projectId=${projectId}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
        setLastRefresh(new Date());
      }
    } catch (error) {
      logger.error('Error fetching memory stats:', error);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchMemoryStats();
  }, [fetchMemoryStats]);

  const handleCompress = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/memory/compress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      });
      
      if (response.ok) {
        await fetchMemoryStats();
        onOptimize?.();
      }
    } catch (error) {
      logger.error('Error compressing memory:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMerge = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/memory/merge', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      });
      
      if (response.ok) {
        await fetchMemoryStats();
        onOptimize?.();
      }
    } catch (error) {
      logger.error('Error merging memory:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'character': return <Users className="w-4 h-4" />;
      case 'plot': return <TrendingUp className="w-4 h-4" />;
      case 'setting': return <MapPin className="w-4 h-4" />;
      case 'relationship': return <Heart className="w-4 h-4" />;
      case 'theme': return <Brain className="w-4 h-4" />;
      case 'event': return <Calendar className="w-4 h-4" />;
      default: return <Database className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    const colors = {
      character: 'bg-info-light text-blue-800',
      plot: 'bg-success-light text-green-800',
      setting: 'bg-warning-light text-yellow-800',
      relationship: 'bg-pink-100 text-pink-800',
      theme: 'bg-purple-100 text-purple-800',
      event: 'bg-orange-100 text-orange-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (date: Date | null) => {
    if (!date) return 'Never';
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.floor((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      'day'
    );
  };

  const getMemoryHealth = () => {
    if (!stats) return 'unknown';
    
    const tokenRatio = stats.totalTokens / 32000; // Assuming 32k max
    if (tokenRatio < 0.5) return 'excellent';
    if (tokenRatio < 0.7) return 'good';
    if (tokenRatio < 0.9) return 'warning';
    return 'critical';
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-success';
      case 'good': return 'text-info';
      case 'warning': return 'text-warning';
      case 'critical': return 'text-error';
      default: return 'text-gray-600';
    }
  };

  if (!stats) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <Brain className="w-8 h-8 mx-auto mb-2 animate-pulse" />
            <p>Loading memory analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const memoryHealth = getMemoryHealth();
  const tokenUsagePercent = Math.round((stats.totalTokens / 32000) * 100);

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Database className="w-5 h-5 text-info" />
              <div>
                <p className="text-sm text-gray-600">Total Chunks</p>
                <p className="text-2xl font-bold">{stats.totalChunks}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-warning" />
              <div>
                <p className="text-sm text-gray-600">Token Usage</p>
                <p className="text-2xl font-bold">{stats.totalTokens.toLocaleString()}</p>
                <Progress value={tokenUsagePercent} className="mt-1 h-1" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Brain className={`w-5 h-5 ${getHealthColor(memoryHealth)}`} />
              <div>
                <p className="text-sm text-gray-600">Memory Health</p>
                <p className={`text-lg font-semibold capitalize ${getHealthColor(memoryHealth)}`}>
                  {memoryHealth}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-success" />
              <div>
                <p className="text-sm text-gray-600">Avg. Importance</p>
                <p className="text-2xl font-bold">{(stats.averageImportance * 100).toFixed(0)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="distribution" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="distribution">Distribution</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="distribution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5" />
                <span>Memory Distribution by Type</span>
              </CardTitle>
              <CardDescription>
                How your story elements are distributed across memory chunks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(stats.chunksByType).map(([type, count]) => (
                  <div key={type} className="text-center p-4 border rounded-lg">
                    <div className="flex items-center justify-center mb-2">
                      {getTypeIcon(type)}
                    </div>
                    <Badge className={getTypeColor(type)}>{type}</Badge>
                    <p className="text-2xl font-bold mt-2">{count}</p>
                    <p className="text-sm text-gray-500">
                      {Math.round((count / stats.totalChunks) * 100)}%
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Compass className="w-5 h-5" />
                <span>Memory Optimization</span>
              </CardTitle>
              <CardDescription>
                Optimize memory usage and improve performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Compression Status</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Last compressed: {formatDate(stats.lastCompression)}
                  </p>
                  <Button 
                    onClick={handleCompress} 
                    disabled={loading}
                    className="w-full"
                  >
                    <Compass className="w-4 h-4 mr-2" />
                    Compress Memory
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Merge Similar Chunks</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Combine similar memory chunks to reduce redundancy
                  </p>
                  <Button 
                    onClick={handleMerge} 
                    disabled={loading}
                    variant="outline"
                    className="w-full"
                  >
                    <Merge className="w-4 h-4 mr-2" />
                    Merge Chunks
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">Compression Ratio</h4>
                <div className="flex items-center space-x-2">
                  <Progress value={stats.compressionRatio * 100} className="flex-1" />
                  <span className="text-sm font-medium">
                    {Math.round(stats.compressionRatio * 100)}%
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Target compression ratio for memory optimization
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5" />
                <span>Memory Insights</span>
              </CardTitle>
              <CardDescription>
                AI-powered insights about your story&apos;s memory usage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {tokenUsagePercent > 80 && (
                  <div className="p-3 bg-error-light border border-red-200 rounded-lg">
                    <p className="text-red-800 font-medium">High Memory Usage</p>
                    <p className="text-error text-sm">
                      Consider compressing memory to improve performance.
                    </p>
                  </div>
                )}

                {(stats.chunksByType.character || 0) > stats.totalChunks * 0.4 && (
                  <div className="p-3 bg-info-light border border-blue-200 rounded-lg">
                    <p className="text-blue-800 font-medium">Character-Heavy Story</p>
                    <p className="text-info text-sm">
                      Your story has many character details. Consider character arc visualization.
                    </p>
                  </div>
                )}

                {stats.averageImportance < 0.6 && (
                  <div className="p-3 bg-warning-light border border-yellow-200 rounded-lg">
                    <p className="text-yellow-800 font-medium">Low Average Importance</p>
                    <p className="text-warning text-sm">
                      Many memory chunks have low importance. Cleanup might help.
                    </p>
                  </div>
                )}

                <div className="p-3 bg-success-light border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">Memory Health Good</p>
                  <p className="text-success text-sm">
                    Your story memory is well-organized and efficiently managed.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Refresh Button */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-500">
          Last updated: {lastRefresh.toLocaleTimeString()}
        </p>
        <Button 
          onClick={fetchMemoryStats} 
          disabled={loading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
    </div>
  );
}