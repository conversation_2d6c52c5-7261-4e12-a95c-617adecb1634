'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  MessageSquare, 
  Eye,
  BarChart3,
  Target
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  Area, 
  AreaChart,
  Bar,
  ComposedChart,
  ReferenceLine
} from 'recharts';
import type { PacingData } from '@/lib/types/analysis';
import type { ChartTooltipProps } from '@/lib/types/charts';

interface PacingAnalyzerProps {
  pacingData: PacingData;
  projectId?: string;
  className?: string;
}

export function PacingAnalyzer({ pacingData, className }: PacingAnalyzerProps) {
  const [viewMode, setViewMode] = useState<'tension' | 'elements' | 'sentence' | 'comparison'>('tension');
  const [selectedChapter, setSelectedChapter] = useState<number | null>(null);

  const getTensionColor = (tension: number) => {
    if (tension >= 80) return 'hsl(var(--color-error))'; // Red - high tension
    if (tension >= 60) return 'hsl(38 92% 60%)'; // Orange - medium-high
    if (tension >= 40) return 'hsl(var(--color-warning))'; // Yellow - medium
    if (tension >= 20) return 'hsl(var(--color-success))'; // Green - low-medium
    return 'hsl(var(--color-info))'; // Blue - low tension
  };


  const getScoreBadge = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const highTensionChapters = pacingData.tensionCurve.filter(ch => ch.tension >= 70);
  const lowTensionChapters = pacingData.tensionCurve.filter(ch => ch.tension <= 30);

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Pacing Analysis
            </span>
            <Badge variant={getScoreBadge(pacingData.overallScore) as 'default' | 'secondary' | 'destructive'}>
              {pacingData.overallScore}% Pacing Score
            </Badge>
          </CardTitle>
          <CardDescription>
            Analyze story rhythm, tension curves, and narrative flow
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-error-light dark:bg-red-950/20 rounded-lg">
              <Activity className="w-5 h-5 mx-auto mb-1 text-error" />
              <div className="text-lg font-bold text-error">{highTensionChapters.length}</div>
              <div className="text-xs text-error">High Tension</div>
            </div>
            <div className="text-center p-3 bg-info-light dark:bg-blue-950/20 rounded-lg">
              <TrendingDown className="w-5 h-5 mx-auto mb-1 text-info" />
              <div className="text-lg font-bold text-info">{lowTensionChapters.length}</div>
              <div className="text-xs text-info">Low Tension</div>
            </div>
            <div className="text-center p-3 bg-success-light dark:bg-green-950/20 rounded-lg">
              <MessageSquare className="w-5 h-5 mx-auto mb-1 text-success" />
              <div className="text-lg font-bold text-success">
                {Math.round(pacingData.tensionCurve.reduce((acc, ch) => acc + ch.dialogue, 0) / pacingData.tensionCurve.length)}%
              </div>
              <div className="text-xs text-success">Avg Dialogue</div>
            </div>
            <div className="text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
              <Eye className="w-5 h-5 mx-auto mb-1 text-purple-600" />
              <div className="text-lg font-bold text-purple-600">
                {Math.round(pacingData.tensionCurve.reduce((acc, ch) => acc + ch.action, 0) / pacingData.tensionCurve.length)}%
              </div>
              <div className="text-xs text-purple-600">Avg Action</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Visualization Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Pacing Visualization</CardTitle>
            <Select value={viewMode} onValueChange={(value: 'tension' | 'elements' | 'sentence' | 'comparison') => setViewMode(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tension">Tension Curve</SelectItem>
                <SelectItem value="elements">Story Elements</SelectItem>
                <SelectItem value="sentence">Sentence Rhythm</SelectItem>
                <SelectItem value="comparison">Ideal vs Actual</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            {viewMode === 'tension' && (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={pacingData.tensionCurve}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="chapter" 
                    label={{ value: 'Chapter', position: 'insideBottom', offset: -10 }}
                  />
                  <YAxis 
                    label={{ value: 'Tension Level', angle: -90, position: 'insideLeft' }}
                    domain={[0, 100]}
                  />
                  <Tooltip
                    content={(props: ChartTooltipProps) => {
                      const { active, payload, label } = props;
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white dark:bg-slate-800 p-3 border rounded shadow">
                            <p className="font-medium">Chapter {label}</p>
                            <p className="text-error">Tension: {payload[0]?.value || 0}%</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="tension" 
                    stroke="#ef4444" 
                    fill="#ef4444" 
                    fillOpacity={0.3}
                  />
                  <ReferenceLine y={50} stroke="#94a3b8" strokeDasharray="5 5" />
                </AreaChart>
              </ResponsiveContainer>
            )}

            {viewMode === 'elements' && (
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart data={pacingData.tensionCurve}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="chapter" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Bar dataKey="action" stackId="a" fill="#8b5cf6" name="Action" />
                  <Bar dataKey="dialogue" stackId="a" fill="#10b981" name="Dialogue" />
                  <Bar dataKey="description" stackId="a" fill="#f59e0b" name="Description" />
                  <Line 
                    type="monotone" 
                    dataKey="tension" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    name="Tension"
                  />
                </ComposedChart>
              </ResponsiveContainer>
            )}

            {viewMode === 'sentence' && (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={pacingData.tensionCurve}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="chapter" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="avgSentenceLength" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    name="Avg Sentence Length"
                  />
                  <ReferenceLine y={15} stroke="#94a3b8" strokeDasharray="5 5" label="Ideal" />
                </LineChart>
              </ResponsiveContainer>
            )}

            {viewMode === 'comparison' && pacingData.idealPacing && (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={pacingData.tensionCurve}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="chapter" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="tension" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    name="Actual Tension"
                  />
                  <Line 
                    type="monotone" 
                    data={pacingData.idealPacing.tensionPoints}
                    dataKey="expectedTension" 
                    stroke="#10b981" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="Ideal Tension"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Pacing Issues */}
      {pacingData.pacingIssues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Pacing Issues ({pacingData.pacingIssues.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pacingData.pacingIssues.map((issue, index) => (
                <div 
                  key={index}
                  className="flex items-start justify-between p-3 border rounded-lg bg-slate-50 dark:bg-slate-800"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge 
                        variant={
                          issue.severity === 'high' ? 'destructive' :
                          issue.severity === 'medium' ? 'secondary' : 'outline'
                        }
                        className="text-xs"
                      >
                        Chapter {issue.chapter}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {issue.severity}
                      </Badge>
                    </div>
                    <h4 className="font-medium text-sm mb-1">{issue.issue}</h4>
                    <p className="text-xs text-slate-600 dark:text-slate-400">
                      {issue.suggestion}
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedChapter(issue.chapter)}
                  >
                    View
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {pacingData.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              Pacing Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pacingData.recommendations.map((recommendation, index) => (
                <div 
                  key={index}
                  className="p-3 bg-info-light dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg"
                >
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    {recommendation}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chapter Detail Modal */}
      {selectedChapter && (
        <Card>
          <CardHeader>
            <CardTitle>Chapter {selectedChapter} Analysis</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedChapter(null)}
              className="ml-auto"
            >
              Close
            </Button>
          </CardHeader>
          <CardContent>
            {(() => {
              const chapterData = pacingData.tensionCurve.find(ch => ch.chapter === selectedChapter);
              if (!chapterData) return <p>Chapter data not found.</p>;

              return (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold" style={{ color: getTensionColor(chapterData.tension) }}>
                      {chapterData.tension}%
                    </div>
                    <div className="text-sm text-slate-600">Tension</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{chapterData.action}%</div>
                    <div className="text-sm text-slate-600">Action</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-success">{chapterData.dialogue}%</div>
                    <div className="text-sm text-slate-600">Dialogue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-warning">{chapterData.description}%</div>
                    <div className="text-sm text-slate-600">Description</div>
                  </div>
                </div>
              );
            })()}
          </CardContent>
        </Card>
      )}
    </div>
  );
}