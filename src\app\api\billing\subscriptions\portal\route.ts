import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createTypedServerClient } from '@/lib/supabase'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/utils/response'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { z } from 'zod'

// Optional schema for return URL validation
const portalRequestSchema = z.object({
  returnUrl: z.string().url().optional()
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: portalRequestSchema,
    rateLimitKey: 'billing-portal',
    rateLimitCost: 3,
    maxBodySize: 1024, // 1KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      // Check if user has an active subscription
      const supabase = await createTypedServerClient();
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('status')
        .eq('user_id', user.id)
        .in('status', ['active', 'past_due', 'trialing'])
        .single();

      if (!subscription) {
        return { 
          valid: false, 
          error: 'No active subscription found. Please subscribe first.' 
        };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { returnUrl } = context.body || {};

  try {
    const supabase = await createTypedServerClient();

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id, email')
      .eq('id', user.id)
      .single();

    if (!profile?.stripe_customer_id) {
      // Try to find customer by email if ID is missing
      const customers = await stripe.customers.list({
        email: profile?.email || user.email!,
        limit: 1
      });

      if (customers.data.length === 0) {
        logger.error('No Stripe customer found', {
          userId: user.id,
          email: user.email,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error(
          'No billing account found. Please contact support.',
          404
        );
      }

      // Update profile with found customer ID
      const customerId = customers.data[0].id;
      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id);

      profile.stripe_customer_id = customerId;
    }

    // Create portal session with configuration
    const session = await stripe.billingPortal.sessions.create({
      customer: profile.stripe_customer_id,
      return_url: returnUrl || `${request.headers.get('origin')}/billing`,
      configuration: process.env.STRIPE_PORTAL_CONFIG_ID,
      flow_data: {
        type: 'subscription_cancel',
        subscription_cancel: {
          retention: {
            type: 'coupon_offer',
            coupon_offer: {
              coupon: process.env.STRIPE_RETENTION_COUPON_ID
            }
          }
        }
      }
    });

    logger.info('Billing portal session created', {
      userId: user.id,
      sessionId: session.id,
      customerId: profile.stripe_customer_id,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      url: session.url,
      expiresAt: session.expires_at
    });

  } catch (error) {
    logger.error('Portal session creation error:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    
    if (error instanceof Error && error.message.includes('customer')) {
      return UnifiedResponse.error(
        'Billing account configuration error. Please contact support.',
        400
      );
    }
    
    return UnifiedResponse.error('Failed to create billing portal session');
  }
});