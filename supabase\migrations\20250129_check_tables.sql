-- Diagnostic query to check what tables and columns exist

-- Check if tables exist and their columns
SELECT 
    t.table_name,
    array_agg(c.column_name ORDER BY c.ordinal_position) as columns
FROM 
    information_schema.tables t
    LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
WHERE 
    t.table_schema = 'public' 
    AND t.table_name IN ('writing_sessions', 'ai_usage_logs', 'achievements', 'user_achievements', 'achievement_progress')
GROUP BY 
    t.table_name
ORDER BY 
    t.table_name;

-- Check for any existing constraints on writing_sessions
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as definition
FROM 
    pg_constraint
WHERE 
    conrelid = 'writing_sessions'::regclass::oid
    AND contype IN ('u', 'p');  -- unique and primary key constraints