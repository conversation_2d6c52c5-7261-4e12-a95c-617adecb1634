import * as React from "react"
import { cn } from "@/lib/utils"
import { RADIUS, ANIMATIONS } from "@/lib/config/ui-config"

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "text" | "circular" | "rectangular" | "card"
  width?: string | number
  height?: string | number
  count?: number
}

export function Skeleton({
  className,
  variant = "text",
  width,
  height,
  count = 1,
  ...props
}: SkeletonProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case "circular":
        return "rounded-full"
      case "rectangular":
        return RADIUS.MD
      case "card":
        return RADIUS.CARD
      case "text":
      default:
        return RADIUS.SM
    }
  }

  const getVariantStyles = () => {
    const styles: React.CSSProperties = {}
    
    if (width) styles.width = typeof width === "number" ? `${width}px` : width
    if (height) styles.height = typeof height === "number" ? `${height}px` : height
    
    // Default dimensions based on variant
    if (!height) {
      switch (variant) {
        case "text":
          styles.height = "1em"
          break
        case "circular":
          styles.height = styles.width || "40px"
          styles.width = styles.width || "40px"
          break
        case "rectangular":
          styles.height = "120px"
          break
        case "card":
          styles.height = "200px"
          break
      }
    }
    
    return styles
  }

  const skeletons = Array.from({ length: count }, (_, i) => (
    <div
      key={i}
      className={cn(
        "animate-pulse bg-muted",
        getVariantClasses(),
        className
      )}
      style={getVariantStyles()}
      {...props}
    />
  ))

  return count > 1 ? (
    <div className="space-y-2">
      {skeletons}
    </div>
  ) : (
    skeletons[0]
  )
}

// Preset skeleton components for common use cases
export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-4", className)}>
      <Skeleton variant="rectangular" height={200} />
      <div className="space-y-2 p-4">
        <Skeleton variant="text" width="80%" />
        <Skeleton variant="text" width="60%" />
      </div>
    </div>
  )
}

export function SkeletonList({ count = 3, className }: { count?: number; className?: string }) {
  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: count }, (_, i) => (
        <div key={i} className="flex items-center space-x-4">
          <Skeleton variant="circular" width={40} height={40} />
          <div className="flex-1 space-y-2">
            <Skeleton variant="text" width="60%" />
            <Skeleton variant="text" width="40%" />
          </div>
        </div>
      ))}
    </div>
  )
}

export function SkeletonTable({ rows = 5, cols = 4, className }: { rows?: number; cols?: number; className?: string }) {
  return (
    <div className={cn("w-full", className)}>
      {/* Header */}
      <div className="flex space-x-4 pb-4 border-b">
        {Array.from({ length: cols }, (_, i) => (
          <Skeleton key={i} variant="text" width={`${100 / cols}%`} height={20} />
        ))}
      </div>
      
      {/* Rows */}
      <div className="space-y-4 pt-4">
        {Array.from({ length: rows }, (_, rowIndex) => (
          <div key={rowIndex} className="flex space-x-4">
            {Array.from({ length: cols }, (_, colIndex) => (
              <Skeleton key={colIndex} variant="text" width={`${100 / cols}%`} />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

export function SkeletonForm({ fields = 4, className }: { fields?: number; className?: string }) {
  return (
    <div className={cn("space-y-6", className)}>
      {Array.from({ length: fields }, (_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton variant="text" width={120} height={16} />
          <Skeleton variant="rectangular" height={40} />
        </div>
      ))}
      <div className="flex gap-4 pt-4">
        <Skeleton variant="rectangular" width={100} height={40} />
        <Skeleton variant="rectangular" width={100} height={40} />
      </div>
    </div>
  )
}