import { NextRequest } from 'next/server'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { TextExtractionService } from '@/lib/services/text-extraction-service'

export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return UnifiedResponse.error('Unauthorized', 401)
    }

    const supabase = await createTypedServerClient()
    
    // Get all projects with unprocessed materials
    const { data: projectsWithMaterials, error } = await supabase
      .from('reference_materials')
      .select('project_id')
      .eq('is_processed', false)
      .in('processing_status', ['pending', 'queued'])
      .limit(10) // Process up to 10 projects per run

    if (error) {
      logger.error('Failed to fetch projects with pending materials:', error)
      return UnifiedResponse.error('Failed to fetch pending materials')
    }

    if (!projectsWithMaterials || projectsWithMaterials.length === 0) {
      return UnifiedResponse.success({ 
        message: 'No pending materials to process',
        processed: 0 
      })
    }

    // Get unique project IDs
    const projectIds = [...new Set(projectsWithMaterials.map(m => m.project_id))]
    
    logger.info(`Processing materials for ${projectIds.length} projects`)

    // Process materials for each project
    let totalProcessed = 0
    for (const projectId of projectIds) {
      try {
        await TextExtractionService.processPendingMaterials(projectId)
        totalProcessed++
      } catch (error) {
        logger.error(`Failed to process materials for project ${projectId}:`, error)
      }
    }

    return UnifiedResponse.success({ 
      message: `Processed materials for ${totalProcessed} projects`,
      processed: totalProcessed,
      projectIds 
    })
  } catch (error) {
    logger.error('Cron job error:', error)
    return UnifiedResponse.error('Cron job failed')
  }
}

// Also export POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}