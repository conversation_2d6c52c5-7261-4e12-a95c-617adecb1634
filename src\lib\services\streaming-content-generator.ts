/**
 * Streaming Content Generator
 * Enhanced content generation with real-time streaming using Vercel AI SDK
 */

import { logger } from '@/lib/services/logger'
import { AIServiceBase, AIGenerationOptions } from './ai-service-base'
import { WritingTask, ServiceResponse } from './types'
import { getAIConfig, AI_QUALITY_THRESHOLDS, AI_TEMPERATURE, AI_MODELS } from '../config/ai-settings'
import { qualityAnalyzer } from './quality-analyzer'
import { StreamingOptions } from '@/lib/ai/vercel-ai-client'
import {
  sceneOutlineSchema,
  dialogueResponseSchema,
  characterProfileGenerationSchema,
  worldBuildingSchema,
  CharacterProfileGeneration,
  WorldBuilding
} from '../schemas/content-schemas'
import { z } from 'zod'
import { TIME_MS } from '@/lib/constants/time'

interface StreamingGenerationResult {
  content?: string
  metadata?: Record<string, unknown>
  quality?: number
  streamId?: string
}

export interface ContentStreamingOptions extends StreamingOptions {
  onQualityUpdate?: (quality: number) => void
  onMetadataUpdate?: (metadata: Record<string, unknown>) => void
}

export class StreamingContentGenerator extends AIServiceBase {
  private generationQueue: WritingTask[] = []
  private activeGenerations: Map<string, { task: WritingTask; startTime: number }> = new Map()
  private streamingSessions: Map<string, { active: boolean; content: string }> = new Map()

  constructor() {
    super({
      name: 'streaming-content-generator',
      version: '2.0.0',
      endpoints: ['/api/content/stream', '/api/content/generate', '/api/content/templates'],
      dependencies: ['ai-orchestrator'],
      healthCheck: '/api/content/health',
      useSharedClient: true
    })
  }

  async initialize(): Promise<void> {
    this.startGenerationProcessor()
    this.isInitialized = true
    this.setStatus('active')
  }

  /**
   * Generate content with streaming support
   */
  async generateContentStream(
    task: WritingTask,
    streamingOptions?: ContentStreamingOptions
  ): Promise<ServiceResponse<StreamingGenerationResult>> {
    const streamId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    try {
      this.streamingSessions.set(streamId, { active: true, content: '' })
      
      const aiConfig = getAIConfig({
        qualityVsSpeed: 'balanced',
        creativityLevel: 'medium'
      })

      const prompt = this.buildPrompt(task)
      const systemPrompt = this.getSystemPrompt(task.type)

      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt,
        streaming: true,
        streamingOptions: {
          onToken: (token: string) => {
            const session = this.streamingSessions.get(streamId)
            if (session) {
              session.content += token
              this.streamingSessions.set(streamId, session)
            }
            
            if (streamingOptions?.onToken) {
              streamingOptions.onToken(token)
            }
          },
          onProgress: (progress) => {
            if (streamingOptions?.onProgress) {
              streamingOptions.onProgress(progress)
            }
            
            // Real-time quality assessment
            if (progress.content.length > 100) {
              this.assessQuality(progress.content, task.type).then(quality => {
                if (streamingOptions?.onQualityUpdate) {
                  streamingOptions.onQualityUpdate(quality)
                }
              })
            }
          },
          onComplete: (content: string) => {
            const session = this.streamingSessions.get(streamId)
            if (session) {
              session.active = false
              session.content = content
              this.streamingSessions.set(streamId, session)
            }
            
            if (streamingOptions?.onComplete) {
              streamingOptions.onComplete(content)
            }
          },
          onError: (error: Error) => {
            this.streamingSessions.delete(streamId)
            
            if (streamingOptions?.onError) {
              streamingOptions.onError(error)
            }
          }
        }
      }

      const content = await this.generateWithAI<string>(prompt, options)

      // Final quality assessment
      const quality = await this.assessQuality(content, task.type)
      
      const metadata = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        taskType: task.type,
        processingTime: Date.now() - Date.now(),
        streamId,
        quality
      }

      if (streamingOptions?.onMetadataUpdate) {
        streamingOptions.onMetadataUpdate(metadata)
      }

      return {
        success: true,
        data: {
          content,
          metadata,
          quality,
          streamId
        }
      }
    } catch (error) {
      this.streamingSessions.delete(streamId)
      
      return {
        success: false,
        error: `Content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Generate structured content with streaming
   */
  async generateStructuredContentStream<T>(
    task: WritingTask,
    schema: z.ZodSchema<T>,
    streamingOptions?: ContentStreamingOptions
  ): Promise<ServiceResponse<T>> {
    try {
      const aiConfig = getAIConfig({
        qualityVsSpeed: 'quality',
        creativityLevel: 'medium'
      })

      const prompt = this.buildPrompt(task)
      const systemPrompt = this.getSystemPrompt(task.type)

      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt,
        responseFormat: 'structured',
        structuredSchema: schema,
        streaming: true,
        streamingOptions
      }

      const result = await this.generateWithAI<T>(prompt, options)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Structured content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Generate scene outline with streaming
   */
  async generateSceneOutlineStream(
    params: {
      chapterTitle: string
      chapterSummary: string
      previousScenes?: string[]
      tone?: string
      style?: string
    },
    streamingOptions?: ContentStreamingOptions
  ): Promise<ServiceResponse<any>> {
    const task: WritingTask = {
      id: `scene_outline_${Date.now()}`,
      type: 'scene_outline',
      priority: 'STANDARD',
      input: params,
      status: 'pending',
      createdAt: new Date(),
      estimatedDuration: 30000
    }

    return this.generateStructuredContentStream(task, sceneOutlineSchema, streamingOptions)
  }

  /**
   * Generate dialogue with streaming
   */
  async generateDialogueStream(
    params: {
      characters: string[]
      context: string
      tone?: string
      length?: 'short' | 'medium' | 'long'
    },
    streamingOptions?: ContentStreamingOptions
  ): Promise<ServiceResponse<any>> {
    const task: WritingTask = {
      id: `dialogue_${Date.now()}`,
      type: 'dialogue',
      priority: 'STANDARD',
      input: params,
      status: 'pending',
      createdAt: new Date(),
      estimatedDuration: 25000
    }

    return this.generateStructuredContentStream(task, dialogueResponseSchema, streamingOptions)
  }

  /**
   * Generate character profile with streaming
   */
  async generateCharacterProfileStream(
    params: {
      name: string
      role: string
      genre: string
      traits?: string[]
      background?: string
    },
    streamingOptions?: ContentStreamingOptions
  ): Promise<ServiceResponse<CharacterProfileGeneration>> {
    const task: WritingTask = {
      id: `character_${Date.now()}`,
      type: 'character_development',
      priority: 'STANDARD',
      input: params,
      status: 'pending',
      createdAt: new Date(),
      estimatedDuration: 40000
    }

    return this.generateStructuredContentStream(task, characterProfileGenerationSchema, streamingOptions)
  }

  /**
   * Generate world building content with streaming
   */
  async generateWorldBuildingStream(
    params: {
      genre: string
      setting: string
      scope: 'location' | 'culture' | 'history' | 'politics'
      details?: string
    },
    streamingOptions?: ContentStreamingOptions
  ): Promise<ServiceResponse<WorldBuilding>> {
    const task: WritingTask = {
      id: `worldbuilding_${Date.now()}`,
      type: 'world_building',
      priority: 'STANDARD',
      input: params,
      status: 'pending',
      createdAt: new Date(),
      estimatedDuration: 45000
    }

    return this.generateStructuredContentStream(task, worldBuildingSchema, streamingOptions)
  }

  /**
   * Get streaming session status
   */
  getStreamingSession(streamId: string): { active: boolean; content: string } | null {
    return this.streamingSessions.get(streamId) || null
  }

  /**
   * Cancel streaming session
   */
  cancelStreamingSession(streamId: string): boolean {
    const session = this.streamingSessions.get(streamId)
    if (session) {
      session.active = false
      this.streamingSessions.delete(streamId)
      return true
    }
    return false
  }

  /**
   * Build prompt for content generation
   */
  private buildPrompt(task: WritingTask): string {
    const { type, input } = task

    switch (type) {
      case 'scene_outline':
        return `Create a detailed scene outline for a chapter titled "${input.chapterTitle}". 
                Chapter summary: ${input.chapterSummary}
                ${input.previousScenes ? `Previous scenes: ${input.previousScenes.join(', ')}` : ''}
                Tone: ${input.tone || 'balanced'}
                Style: ${input.style || 'literary'}`

      case 'dialogue':
        return `Generate dialogue between characters: ${input.characters.join(', ')}
                Context: ${input.context}
                Tone: ${input.tone || 'natural'}
                Length: ${input.length || 'medium'}`

      case 'character_development':
        return `Create a detailed character profile for "${input.name}" who is a ${input.role} in a ${input.genre} story.
                ${input.traits ? `Key traits: ${input.traits.join(', ')}` : ''}
                ${input.background ? `Background: ${input.background}` : ''}`

      case 'world_building':
        return `Create detailed world building content for a ${input.genre} story.
                Setting: ${input.setting}
                Focus on: ${input.scope}
                ${input.details ? `Additional details: ${input.details}` : ''}`

      default:
        return `Generate content for ${type}: ${JSON.stringify(input)}`
    }
  }

  /**
   * Get system prompt based on content type
   */
  private getSystemPrompt(type: string): string {
    const basePrompt = 'You are an expert creative writing assistant specializing in high-quality content generation.'
    
    const typePrompts: Record<string, string> = {
      scene_outline: `${basePrompt} Create detailed, engaging scene outlines that advance the plot and develop characters.`,
      dialogue: `${basePrompt} Write natural, character-appropriate dialogue that reveals personality and advances the story.`,
      character_development: `${basePrompt} Develop rich, complex characters with clear motivations, flaws, and growth arcs.`,
      world_building: `${basePrompt} Create immersive, consistent world building that enhances the story's atmosphere and believability.`,
      chapter_writing: `${basePrompt} Write compelling chapters that maintain pacing, develop characters, and advance the plot.`,
      description: `${basePrompt} Create vivid, sensory descriptions that immerse readers in the scene without overwhelming them.`
    }

    return typePrompts[type] || basePrompt
  }

  /**
   * Start the generation processor for queued tasks
   */
  private startGenerationProcessor(): void {
    setInterval(async () => {
      if (this.generationQueue.length > 0 && this.activeGenerations.size < 3) {
        const task = this.generationQueue.shift()
        if (task) {
          this.processTask(task)
        }
      }
    }, TIME_MS.SECOND)
  }

  /**
   * Process a single task
   */
  private async processTask(task: WritingTask): Promise<void> {
    this.activeGenerations.set(task.id, { task, startTime: Date.now() })
    
    try {
      const result = await this.generateContentStream(task)
      logger.info(`Task ${task.id} completed:`, result.success ? 'success' : 'failed')
    } catch (error) {
      logger.error(`Task ${task.id} failed:`, error)
    } finally {
      this.activeGenerations.delete(task.id)
    }
  }
}

// Export singleton instance
export const streamingContentGenerator = new StreamingContentGenerator()
