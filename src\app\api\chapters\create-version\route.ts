import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { VersionHistoryService } from '@/lib/version-history'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

// Validation schema for version creation
const createVersionSchema = z.object({
  chapterId: z.string().uuid('Invalid chapter ID'),
  changeSummary: z.string().min(1, 'Change summary is required').max(500, 'Change summary must be 500 characters or less')
});

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()
    const body = await request.json()
    
    // Validate request body
    const validationResult = createVersionSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validationResult.error.errors 
      }, { status: 400 })
    }
    
    const { chapterId, changeSummary } = validationResult.data

    // Verify user owns the chapter
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select('project_id, user_id')
      .eq('id', chapterId)
      .single()

    if (chapterError || !chapter) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Verify user owns the project (chapters don't have user_id directly)
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', chapter.project_id)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return handleAPIError(new AuthorizationError())
    }

    // Create manual version
    const versionService = new VersionHistoryService()
    const success = await versionService.createManualVersion(chapterId, user.id, changeSummary)

    if (!success) {
      return NextResponse.json({ error: 'Failed to create version' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: error.errors 
      }, { status: 400 })
    }
    logger.error('Create version error:', error)
    return NextResponse.json(
      { error: 'Failed to create version' },
      { status: 500 }
    )
  }
})