'use client'

import { LazyAnalyticsDashboard } from '@/components/analytics/lazy-analytics'

interface AnalyticsPageClientProps {
  userId: string
  projects: any[]
}

export default function AnalyticsPageClient({ userId, projects }: AnalyticsPageClientProps) {
  return (
    <div className="container-wide py-6 sm:py-8 lg:py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Writing Analytics</h1>
        <p className="text-muted-foreground">
          Deep insights into your writing patterns and productivity
        </p>
      </div>
      
      <LazyAnalyticsDashboard 
        userId={userId} 
        projects={projects || []}
      />
    </div>
  )
}