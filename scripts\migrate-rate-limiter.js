#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Import mappings
const importMappings = {
  // Old imports to new unified import
  '@/lib/rate-limiter': '@/lib/rate-limiter-unified',
  '@/lib/rate-limiter-ai': '@/lib/rate-limiter-unified',
  '@/lib/api/rate-limiter': '@/lib/rate-limiter-unified'
};

// Function/variable mappings
const codeMappings = {
  // Rate limiter instances
  'aiGenerationLimiter': 'rateLimiters.aiGeneration',
  'aiChatLimiter': 'rateLimiters.aiGeneration',
  'aiAnalysisLimiter': 'rateLimiters.aiAnalysis',
  'AI_RATE_LIMITS': 'rateLimiters',
  'getAIRateLimiter': 'RateLimit.checkAI',
  'createAIRateLimitResponse': 'RateLimit.response',
  
  // Methods
  '.check(': '.check(',  // This stays the same
  'checkRateLimit(': 'checkRateLimit(',  // This stays the same
  'createRateLimitResponse': 'createRateLimitResponse',  // This stays the same
  
  // Backwards compatibility
  'authLimiter': 'authLimiter',  // Exported for compatibility
  'aiLimiter': 'aiLimiter',  // Exported for compatibility
  'apiLimiter': 'apiLimiter'  // Exported for compatibility
};

function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Skip the unified file itself
  if (filePath.includes('rate-limiter-unified')) {
    return false;
  }

  // Update import statements
  Object.entries(importMappings).forEach(([oldImport, newImport]) => {
    const importRegex = new RegExp(`from\\s+['"]${oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
    if (importRegex.test(content)) {
      content = content.replace(importRegex, `from '${newImport}'`);
      modified = true;
    }
  });

  // Update code usage
  Object.entries(codeMappings).forEach(([oldCode, newCode]) => {
    if (content.includes(oldCode) && oldCode !== newCode) {
      // Use word boundaries for more accurate replacements
      const codeRegex = new RegExp(`\\b${oldCode.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
      content = content.replace(codeRegex, newCode);
      modified = true;
    }
  });

  // Special case: Update rate limiter config objects
  content = content.replace(
    /AI_RATE_LIMITS\.(generation|chat|analysis)\.limiter/g,
    'rateLimiters.ai$1'
  );

  // Update getAIRateLimiter usage
  content = content.replace(
    /getAIRateLimiter\(['"](\w+)['"]\)/g,
    'rateLimiters.ai$1'
  );

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated: ${filePath}`);
    return true;
  }

  return false;
}

function main() {
  console.log('🔄 Migrating rate limiter imports to unified implementation...\n');

  const patterns = [
    'src/**/*.{ts,tsx}',
    '!src/lib/rate-limiter-unified.ts',  // Exclude the new file
    '!node_modules/**'
  ];

  let totalFiles = 0;
  let updatedFiles = 0;

  patterns.forEach(pattern => {
    const files = glob.sync(pattern, {
      cwd: path.join(__dirname, '..'),
      absolute: true
    });

    files.forEach(file => {
      totalFiles++;
      if (updateFile(file)) {
        updatedFiles++;
      }
    });
  });

  console.log(`\n✨ Migration complete!`);
  console.log(`📊 Updated ${updatedFiles} out of ${totalFiles} files scanned.`);
  
  // List old files that can be deleted
  console.log('\n🗑️  The following old rate limiter files can now be deleted:');
  console.log('   - src/lib/rate-limiter.ts');
  console.log('   - src/lib/rate-limiter-ai.ts');
  console.log('   - src/lib/api/rate-limiter.ts');
  console.log('\n⚠️  Review the changes and delete these files manually after testing.');
  
  // Additional instructions
  console.log('\n📝 Additional steps:');
  console.log('1. Run tests to ensure everything works correctly');
  console.log('2. Update any rate limit configurations in environment variables');
  console.log('3. Check for any custom rate limiters that may need updating');
}

main();