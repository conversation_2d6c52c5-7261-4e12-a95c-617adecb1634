// Performance monitoring utilities

import { logger } from '@/lib/services/logger'
import type { WebVitalsMetric, ComponentImport } from '@/types/performance'
import { TIME_MS } from '@/lib/constants'

export const measurePerformance = (name: string, fn: () => void) => {
  if (typeof window !== 'undefined' && window.performance) {
    const start = performance.now()
    fn()
    const end = performance.now()
    logger.debug(`[Performance] ${name}: ${(end - start).toFixed(2)}ms`)
  } else {
    fn()
  }
}

export const reportWebVitals = (metric: WebVitalsMetric) => {
  if (metric.label === 'web-vital') {
    logger.debug(`[Web Vital] ${metric.name}: ${metric.value}`)
    
    // Send to analytics if needed
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', metric.name, {
        value: Math.round(metric.name === 'CLS' ? metric.value * TIME_MS.SECOND : metric.value),
        metric_id: metric.id,
        metric_value: metric.value,
        metric_delta: metric.delta,
      })
    }
  }
}

// Lazy load with retry
export const lazyWithRetry = <T = React.ComponentType>(
  componentImport: ComponentImport<T>,
  retries = 3,
  interval = TIME_MS.SECOND
) => {
  return new Promise((resolve, reject) => {
    componentImport()
      .then(resolve)
      .catch((error) => {
        if (retries === 0) {
          reject(error)
          return
        }
        
        setTimeout(() => {
          logger.debug(`Retrying lazy import, ${retries} attempts left`)
          lazyWithRetry(componentImport, retries - 1, interval)
            .then(resolve)
            .catch(reject)
        }, interval)
      })
  })
}

// Intersection Observer for lazy loading
export const useLazyLoad = (ref: React.RefObject<HTMLElement>, onIntersect: () => void) => {
  if (typeof window === 'undefined') return
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          onIntersect()
          observer.disconnect()
        }
      })
    },
    { threshold: 0.1 }
  )
  
  if (ref.current) {
    observer.observe(ref.current)
  }
  
  return () => observer.disconnect()
}