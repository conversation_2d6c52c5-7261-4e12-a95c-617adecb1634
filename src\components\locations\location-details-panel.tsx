'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  MapPin,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
  Globe,
  Mountain,
  Trees,
  Building,
  Home,
  Share,
  Calendar,
  Tag
} from 'lucide-react'
import type { Location } from './location-manager'

interface LocationDetailsPanelProps {
  location: Location | null
  onUpdate: (locationId: string, updates: Partial<Location>) => Promise<void>
  onDelete: (locationId: string) => Promise<void>
  onAddChild: (parentLocation: Location) => void
}

const LOCATION_TYPE_ICONS = {
  world: Globe,
  continent: Mountain,
  country: Trees,
  region: Mountain,
  city: Building,
  building: Home,
  room: Home,
  other: MapPin
}

const LOCATION_TYPE_COLORS = {
  world: 'text-blue-600 bg-blue-50',
  continent: 'text-green-600 bg-green-50',
  country: 'text-purple-600 bg-purple-50',
  region: 'text-orange-600 bg-orange-50',
  city: 'text-red-600 bg-red-50',
  building: 'text-yellow-600 bg-yellow-50',
  room: 'text-gray-600 bg-gray-50',
  other: 'text-indigo-600 bg-indigo-50'
}

export function LocationDetailsPanel({ 
  location, 
  onUpdate, 
  onDelete, 
  onAddChild 
}: LocationDetailsPanelProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editData, setEditData] = useState<Partial<Location>>({})
  const [newFeature, setNewFeature] = useState('')

  if (!location) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center py-8">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Location Selected</h3>
            <p className="text-muted-foreground">
              Select a location from the tree to view its details
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const handleEdit = () => {
    setEditData({
      name: location.name,
      description: location.description || '',
      location_type: location.location_type,
      features: [...location.features],
      significance: location.significance || '',
      is_shareable: location.is_shareable
    })
    setIsEditing(true)
  }

  const handleSave = async () => {
    try {
      await onUpdate(location.id, editData)
      setIsEditing(false)
      setEditData({})
    } catch (error) {
      // Error handling is done in the parent component
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditData({})
  }

  const handleAddFeature = () => {
    if (newFeature.trim() && editData.features) {
      setEditData({
        ...editData,
        features: [...editData.features, newFeature.trim()]
      })
      setNewFeature('')
    }
  }

  const handleRemoveFeature = (index: number) => {
    if (editData.features) {
      setEditData({
        ...editData,
        features: editData.features.filter((_, i) => i !== index)
      })
    }
  }

  const getLocationIcon = (type: Location['location_type']) => {
    const IconComponent = LOCATION_TYPE_ICONS[type]
    return <IconComponent className="w-5 h-5" />
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <div className={`p-2 rounded-lg ${LOCATION_TYPE_COLORS[location.location_type]}`}>
              {getLocationIcon(location.location_type)}
            </div>
            <div>
              {isEditing ? (
                <Input
                  value={editData.name || ''}
                  onChange={(e) => setEditData({ ...editData, name: e.target.value })}
                  className="font-semibold text-lg"
                />
              ) : (
                <CardTitle className="text-lg">{location.name}</CardTitle>
              )}
              <div className="flex items-center gap-2 mt-1">
                {isEditing ? (
                  <Select
                    value={editData.location_type || location.location_type}
                    onValueChange={(value) => setEditData({ ...editData, location_type: value as Location['location_type'] })}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="world">World</SelectItem>
                      <SelectItem value="continent">Continent</SelectItem>
                      <SelectItem value="country">Country</SelectItem>
                      <SelectItem value="region">Region</SelectItem>
                      <SelectItem value="city">City</SelectItem>
                      <SelectItem value="building">Building</SelectItem>
                      <SelectItem value="room">Room</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <Badge variant="outline" className="capitalize">
                    {location.location_type}
                  </Badge>
                )}
                {location.is_shareable && (
                  <Badge variant="secondary" className="text-xs">
                    <Share className="w-3 h-3 mr-1" />
                    Shareable
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {isEditing ? (
              <>
                <Button size="sm" onClick={handleSave}>
                  <Save className="w-4 h-4 mr-1" />
                  Save
                </Button>
                <Button size="sm" variant="outline" onClick={handleCancel}>
                  <X className="w-4 h-4 mr-1" />
                  Cancel
                </Button>
              </>
            ) : (
              <>
                <Button size="sm" variant="outline" onClick={handleEdit}>
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
                <Button size="sm" variant="outline" onClick={() => onAddChild(location)}>
                  <Plus className="w-4 h-4 mr-1" />
                  Add Child
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Parent Location */}
        {location.parent_location && (
          <div>
            <Label className="text-sm font-medium text-muted-foreground">Parent Location</Label>
            <p className="text-sm mt-1">{location.parent_location.name}</p>
          </div>
        )}

        {/* Description */}
        <div>
          <Label className="text-sm font-medium text-muted-foreground">Description</Label>
          {isEditing ? (
            <Textarea
              value={editData.description || ''}
              onChange={(e) => setEditData({ ...editData, description: e.target.value })}
              placeholder="Describe this location..."
              rows={3}
              className="mt-1"
            />
          ) : (
            <p className="text-sm mt-1">
              {location.description || 'No description provided'}
            </p>
          )}
        </div>

        {/* Features */}
        <div>
          <Label className="text-sm font-medium text-muted-foreground">Features</Label>
          <div className="mt-2 space-y-2">
            {isEditing ? (
              <>
                <div className="flex flex-wrap gap-2">
                  {editData.features?.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                        onClick={() => handleRemoveFeature(index)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  )) || []}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    placeholder="Add a feature..."
                    className="text-sm"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddFeature()}
                  />
                  <Button size="sm" onClick={handleAddFeature}>
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </>
            ) : (
              <div className="flex flex-wrap gap-2">
                {location.features.length > 0 ? (
                  location.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      <Tag className="w-3 h-3 mr-1" />
                      {feature}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">No features defined</p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Significance */}
        <div>
          <Label className="text-sm font-medium text-muted-foreground">Significance</Label>
          {isEditing ? (
            <Textarea
              value={editData.significance || ''}
              onChange={(e) => setEditData({ ...editData, significance: e.target.value })}
              placeholder="Why is this location important to your story?"
              rows={2}
              className="mt-1"
            />
          ) : (
            <p className="text-sm mt-1">
              {location.significance || 'No significance notes'}
            </p>
          )}
        </div>

        {/* Shareable Toggle */}
        {isEditing && (
          <div className="flex items-center space-x-2">
            <Switch
              checked={editData.is_shareable ?? location.is_shareable}
              onCheckedChange={(checked) => setEditData({ ...editData, is_shareable: checked })}
            />
            <Label className="text-sm">Make this location shareable across series</Label>
          </div>
        )}

        <Separator />

        {/* Metadata */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Calendar className="w-3 h-3" />
            <span>Created {formatDate(location.created_at)}</span>
          </div>
          {location.updated_at !== location.created_at && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Calendar className="w-3 h-3" />
              <span>Updated {formatDate(location.updated_at)}</span>
            </div>
          )}
          {location.series && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>Series: {location.series.title}</span>
            </div>
          )}
          {location.universe && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>Universe: {location.universe.name}</span>
            </div>
          )}
        </div>

        {/* Danger Zone */}
        {!isEditing && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-sm font-medium text-destructive">Danger Zone</Label>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onDelete(location.id)}
                className="w-full"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Location
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}