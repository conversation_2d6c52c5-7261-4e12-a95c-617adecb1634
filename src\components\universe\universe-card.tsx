'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Globe, 
  MoreVertical, 
  Edit, 
  Trash, 
  Users, 
  BookOpen,
  Map,
  Shield,
  Sparkles,
  Layers,
  Share2
} from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { toast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import type { Universe } from '@/lib/db/types'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

interface UniverseCardProps {
  universe: Universe & {
    series?: Array<{
      id: string
      title: string
      projects?: Array<{
        project: {
          id: string
          title: string
          status: string
        }
      }>
    }>
  }
  onUpdate: () => void
}

export function UniverseCard({ universe, onUpdate }: UniverseCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      const supabase = createClient()
      const { error } = await supabase
        .from('universes')
        .delete()
        .eq('id', universe.id)

      if (error) throw error

      toast({
        title: 'Universe Deleted',
        description: `"${universe.name}" has been deleted`
      })
      
      onUpdate()
    } catch (error) {
      logger.error('Failed to delete universe', error)
      toast({
        title: 'Error',
        description: 'Failed to delete universe',
        variant: 'destructive'
      })
    } finally {
      setIsDeleting(false)
      setShowDeleteDialog(false)
    }
  }

  const totalBooks = universe.series?.reduce(
    (sum, series) => sum + (series.projects?.length || 0), 
    0
  ) || 0

  const totalCharacters = universe.character_count || 0
  const totalLocations = universe.location_count || 0

  return (
    <>
      <Card className="group hover:shadow-lg transition-all duration-200">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-xl mb-1 group-hover:text-primary transition-colors flex items-center gap-2">
                <Link href={`/universes/${universe.id}`} className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  {universe.name}
                </Link>
              </CardTitle>
              <CardDescription className="line-clamp-2">
                {universe.description || 'A vast interconnected world'}
              </CardDescription>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/universes/${universe.id}`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Universe
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/universes/${universe.id}/map`}>
                    <Map className="mr-2 h-4 w-4" />
                    View Map
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/universes/${universe.id}/timeline`}>
                    <Shield className="mr-2 h-4 w-4" />
                    Timeline
                  </Link>
                </DropdownMenuItem>
                {universe.is_public && (
                  <DropdownMenuItem>
                    <Share2 className="mr-2 h-4 w-4" />
                    Share Universe
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  Delete Universe
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Metadata Badges */}
          <div className="flex flex-wrap gap-2 mt-3">
            {universe.is_public && (
              <Badge variant="secondary" className="gap-1">
                <Sparkles className="h-3 w-3" />
                Public
              </Badge>
            )}
            {universe.rules?.magic_system && (
              <Badge variant="outline">{universe.rules.magic_system}</Badge>
            )}
            {universe.rules?.technology_level && (
              <Badge variant="outline">{universe.rules.technology_level}</Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Stats */}
          <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Layers className="h-4 w-4" />
                Series
              </div>
              <div className="text-2xl font-bold">{universe.series?.length || 0}</div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <BookOpen className="h-4 w-4" />
                Books
              </div>
              <div className="text-2xl font-bold">{totalBooks}</div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                Characters
              </div>
              <div className="text-2xl font-bold">{totalCharacters}</div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Map className="h-4 w-4" />
                Locations
              </div>
              <div className="text-2xl font-bold">{totalLocations}</div>
            </div>
          </div>

          {/* Series List */}
          {universe.series && universe.series.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Connected Series</h4>
              <div className="space-y-1">
                {universe.series.slice(0, 2).map((series) => (
                  <Link
                    key={series.id}
                    href={`/series/${series.id}`}
                    className="flex items-center justify-between p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    <span className="text-sm truncate">{series.title}</span>
                    <Badge variant="outline" className="text-xs">
                      {series.projects?.length || 0} books
                    </Badge>
                  </Link>
                ))}
                {universe.series.length > 2 && (
                  <p className="text-xs text-muted-foreground pl-2">
                    +{universe.series.length - 2} more series
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Universe Settings */}
          <div className="flex flex-wrap gap-2">
            {universe.settings?.allow_crossovers && (
              <Badge variant="secondary" className="text-xs">
                Crossovers
              </Badge>
            )}
            {universe.settings?.maintain_timeline && (
              <Badge variant="secondary" className="text-xs">
                Timeline Enforced
              </Badge>
            )}
            {universe.settings?.share_characters && (
              <Badge variant="secondary" className="text-xs">
                Shared Characters
              </Badge>
            )}
            {universe.settings?.share_locations && (
              <Badge variant="secondary" className="text-xs">
                Shared Locations
              </Badge>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button variant="outline" size="sm" className="flex-1" asChild>
              <Link href={`/universes/${universe.id}`}>
                <Globe className="mr-2 h-4 w-4" />
                Explore
              </Link>
            </Button>
            <Button variant="outline" size="sm" className="flex-1" asChild>
              <Link href={`/series/new?universeId=${universe.id}`}>
                <Layers className="mr-2 h-4 w-4" />
                Add Series
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Universe</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{universe.name}"? This action cannot be undone.
              The series and books in this universe will not be deleted, but they will no longer be connected to this universe.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Universe'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}