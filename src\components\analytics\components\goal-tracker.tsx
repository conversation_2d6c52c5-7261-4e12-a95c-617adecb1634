'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'
import { Target, Calendar, TrendingUp, Award, MoreVertical, Edit, Trash2, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/hooks/use-toast'
import { useCelebration } from '@/contexts/celebration-context'
import { TIME_MS } from '@/lib/constants'

interface Goal {
  id: string
  type: string
  title?: string
  target: number
  current: number
  progress: number
  deadline: string
  unit?: string
}

interface GoalTrackerProps {
  goals: Goal[]
  loading?: boolean
  className?: string
  onRefresh?: () => void
}

export function GoalTracker({ goals, loading = false, className, onRefresh }: GoalTrackerProps) {
  const [deletingGoalId, setDeletingGoalId] = useState<string | null>(null)
  const { toast } = useToast()
  const { celebrateGoal } = useCelebration()

  const handleDeleteGoal = async (goalId: string) => {
    setDeletingGoalId(goalId)
    try {
      const response = await fetch(`/api/goals?goalId=${goalId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete goal')
      }

      toast({
        title: 'Goal deleted',
        description: 'Your goal has been removed successfully.',
      })

      onRefresh?.()
    } catch (error) {
      toast({
        title: 'Error deleting goal',
        description: 'Please try again later.',
        variant: 'destructive',
      })
    } finally {
      setDeletingGoalId(null)
    }
  }

  const handleCompleteGoal = async (goalId: string, goalTitle?: string) => {
    try {
      const response = await fetch('/api/goals', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          goalId,
          status: 'completed',
          completed_at: new Date().toISOString(),
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to complete goal')
      }

      toast({
        title: 'Goal completed!',
        description: 'Congratulations on achieving your goal! 🎉',
      })

      // Trigger celebration
      celebrateGoal(goalTitle || 'Goal completed!')

      onRefresh?.()
    } catch (error) {
      toast({
        title: 'Error updating goal',
        description: 'Please try again later.',
        variant: 'destructive',
      })
    }
  }
  if (loading) {
    return (
      <div className={cn("grid gap-4", className)}>
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <Skeleton className="h-20 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const getGoalIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'daily':
        return Calendar
      case 'weekly':
        return TrendingUp
      case 'monthly':
        return Target
      default:
        return Award
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'bg-success'
    if (progress >= 75) return 'bg-info'
    if (progress >= 50) return 'bg-warning'
    return 'bg-error'
  }

  const getDeadlineStatus = (deadline: string) => {
    const daysLeft = Math.ceil((new Date(deadline).getTime() - new Date().getTime()) / (TIME_MS.SECOND * 60 * 60 * 24))
    
    if (daysLeft < 0) return { text: 'Overdue', color: 'destructive' }
    if (daysLeft === 0) return { text: 'Due today', color: 'warning' }
    if (daysLeft <= 3) return { text: `${daysLeft} days left`, color: 'warning' }
    return { text: `${daysLeft} days left`, color: 'secondary' }
  }

  if (goals.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Target className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground">No active goals</p>
          <p className="text-sm text-muted-foreground mt-1">
            Set writing goals to track your progress
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {goals.map((goal) => {
        const Icon = getGoalIcon(goal.type)
        const deadlineStatus = getDeadlineStatus(goal.deadline)
        const isCompleted = goal.progress >= 100

        return (
          <Card key={goal.id} className={cn(
            "transition-all",
            isCompleted && "border-success/50 bg-success/5"
          )}>
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "p-2 rounded",
                      isCompleted ? "bg-success/10" : "bg-primary/10"
                    )}>
                      <Icon className={cn(
                        "h-4 w-4",
                        isCompleted ? "text-success" : "text-primary"
                      )} />
                    </div>
                    <div>
                      <p className="font-medium">{goal.title || `${goal.type} Goal`}</p>
                      <p className="text-sm text-muted-foreground">
                        {goal.current.toLocaleString()} / {goal.target.toLocaleString()} {goal.unit || 'words'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={deadlineStatus.color as "destructive" | "warning" | "secondary"}>
                      {deadlineStatus.text}
                    </Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="h-8 w-8"
                          disabled={deletingGoalId === goal.id}
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {!isCompleted && (
                          <DropdownMenuItem onClick={() => handleCompleteGoal(goal.id, goal.title)}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Mark Complete
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleDeleteGoal(goal.id)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Progress */}
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className={cn(
                      "font-medium",
                      isCompleted && "text-success"
                    )}>
                      {goal.progress}%
                    </span>
                  </div>
                  <Progress 
                    value={goal.progress} 
                    className="h-2"
                    indicatorClassName={getProgressColor(goal.progress)}
                  />
                </div>

                {/* Achievement badge */}
                {isCompleted && (
                  <div className="flex items-center gap-2 pt-1">
                    <Award className="h-4 w-4 text-success" />
                    <span className="text-sm font-medium text-success">
                      Goal Achieved! 🎉
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}