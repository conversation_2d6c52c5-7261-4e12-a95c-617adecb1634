// Centralized mock data for demo components
// This file contains all mock data used in demo mode to ensure consistency
// and make it clear that this is demonstration data, not production data

export const DEMO_PROJECT = {
  id: 'demo-project-1',
  title: 'The Crystal Chronicles',
  description: 'An epic fantasy saga about a young mage discovering ancient powers',
  genre: 'Fantasy',
  targetWords: 80000,
  currentWords: 47832,
  chapters: 25,
  chaptersComplete: 12,
  status: 'In Progress'
};

export const DEMO_ANALYTICS = {
  stats: {
    totalWords: 47832,
    targetWords: 80000,
    chaptersComplete: 12,
    totalChapters: 25,
    dailyGoal: 2000,
    todayWords: 1847,
    streak: 23,
    avgWordsPerDay: 1654,
    timeSpent: 156, // hours
    sessionsToday: 3
  },
  
  progress: [
    { date: '2025-01-01', words: 1200, time: 2.5 },
    { date: '2025-01-02', words: 1800, time: 3.2 },
    { date: '2025-01-03', words: 2100, time: 3.8 },
    { date: '2025-01-04', words: 1650, time: 2.9 },
    { date: '2025-01-05', words: 1900, time: 3.1 },
    { date: '2025-01-06', words: 2200, time: 4.1 },
    { date: '2025-01-07', words: 1847, time: 2.7 }
  ],
  
  chapterStats: [
    { chapter: 1, title: "The Crystal's Call", words: 3542, target: 3500, status: 'Complete', quality: 92 },
    { chapter: 2, title: "Shadows Awakening", words: 3201, target: 3200, status: 'Complete', quality: 88 },
    { chapter: 3, title: "The First Trial", words: 3789, target: 3800, status: 'Complete', quality: 94 },
    { chapter: 4, title: "Ancient Secrets", words: 3456, target: 3400, status: 'Complete', quality: 90 },
    { chapter: 5, title: "The Gathering Storm", words: 2847, target: 3600, status: 'In Progress', quality: 85 },
    { chapter: 6, title: "Allies and Enemies", words: 0, target: 3500, status: 'Planned', quality: 0 }
  ],
  
  goals: [
    { type: 'Daily', target: 2000, current: 1847, unit: 'words', progress: 92 },
    { type: 'Weekly', target: 14000, current: 12847, unit: 'words', progress: 92 },
    { type: 'Monthly', target: 60000, current: 47832, unit: 'words', progress: 80 },
    { type: 'Chapter', target: 1, current: 0.8, unit: 'chapters', progress: 80 }
  ],
  
  recentSessions: [
    { time: '2 hours ago', words: 847, duration: '1.2 hours', chapter: 'Chapter 5' },
    { time: '6 hours ago', words: 623, duration: '0.8 hours', chapter: 'Chapter 5' },
    { time: '1 day ago', words: 1456, duration: '2.1 hours', chapter: 'Chapter 4' }
  ]
};

export const DEMO_STORY_BIBLE = {
  characters: [
    {
      id: 'char-1',
      name: 'Aria Moonweaver',
      role: 'Protagonist',
      description: 'A young mage with untapped potential, discovering her connection to ancient crystal magic.',
      traits: ['Curious', 'Determined', 'Compassionate', 'Impulsive'],
      arc: 'From naive apprentice to confident wielder of crystal magic',
      relationships: [
        { character: 'Kael Stormwind', type: 'Mentor', description: 'Teaches her control and wisdom' },
        { character: 'Zara Blackthorn', type: 'Rival', description: 'Competing for the same ancient knowledge' }
      ]
    },
    {
      id: 'char-2',
      name: 'Kael Stormwind',
      role: 'Mentor',
      description: 'Veteran mage and keeper of ancient secrets, guides Aria on her journey.',
      traits: ['Wise', 'Patient', 'Secretive', 'Protective'],
      arc: 'Learning to trust again and pass on his knowledge',
      relationships: [
        { character: 'Aria Moonweaver', type: 'Student', description: 'Sees potential in her abilities' }
      ]
    },
    {
      id: 'char-3',
      name: 'Zara Blackthorn',
      role: 'Antagonist',
      description: 'Ambitious dark mage seeking to harness crystal power for domination.',
      traits: ['Cunning', 'Ruthless', 'Charismatic', 'Strategic'],
      arc: 'Descent into darkness driven by past trauma',
      relationships: [
        { character: 'Aria Moonweaver', type: 'Rival', description: 'Sees her as threat to plans' }
      ]
    }
  ],
  
  worldBuilding: {
    locations: [
      {
        name: 'Crystal Spire Academy',
        description: 'Ancient magical institution where young mages train',
        significance: 'Main setting for early chapters, where Aria discovers her powers'
      },
      {
        name: 'The Shattered Realm',
        description: 'Dangerous dimension where crystal magic originates',
        significance: 'Key location for climactic battles and revelations'
      },
      {
        name: 'Elderwood Forest',
        description: 'Mystical forest hiding ancient crystal shrines',
        significance: 'Setting for trials and character development'
      }
    ],
    
    magicSystem: {
      name: 'Crystal Resonance',
      description: 'Magic drawn from ancient crystals that resonate with emotions',
      rules: [
        'Each crystal type corresponds to different emotions and powers',
        'Overuse leads to crystal corruption and loss of control',
        'Bonding with a crystal requires emotional harmony',
        'Multiple crystals can be combined for greater effects'
      ],
      limitations: [
        'Physical exhaustion from channeling',
        'Emotional instability affects power',
        'Crystals can shatter if overloaded'
      ]
    }
  },
  
  plotOutline: {
    acts: [
      {
        number: 1,
        title: 'Discovery',
        chapters: '1-8',
        description: 'Aria discovers her magical abilities and enters the academy'
      },
      {
        number: 2,
        title: 'Trials',
        chapters: '9-17',
        description: 'Training, challenges, and uncovering the crystal conspiracy'
      },
      {
        number: 3,
        title: 'Confrontation',
        chapters: '18-25',
        description: 'Final battle against Zara and the corrupted crystal forces'
      }
    ]
  }
};

export const DEMO_AGENTS = {
  available: [
    {
      id: 'story-architect',
      name: 'Story Architect',
      role: 'Creates comprehensive story structures',
      status: 'ready',
      capabilities: ['Plot structuring', 'Theme development', 'Conflict design']
    },
    {
      id: 'character-developer',
      name: 'Character Developer',
      role: 'Develops detailed character profiles',
      status: 'ready',
      capabilities: ['Personality design', 'Arc planning', 'Relationship mapping']
    },
    {
      id: 'writing-agent',
      name: 'Writing Agent',
      role: 'Generates chapter content',
      status: 'ready',
      capabilities: ['Scene writing', 'Dialogue creation', 'Description crafting']
    }
  ],
  
  recentActivity: [
    {
      agent: 'Story Architect',
      action: 'Generated Act 2 outline',
      time: '10 minutes ago',
      status: 'completed'
    },
    {
      agent: 'Character Developer',
      action: 'Created profile for Zara Blackthorn',
      time: '1 hour ago',
      status: 'completed'
    },
    {
      agent: 'Writing Agent',
      action: 'Wrote Chapter 5 opening scene',
      time: '2 hours ago',
      status: 'completed'
    }
  ]
};

export const DEMO_EDITOR_CONTENT = `Chapter 5: The Gathering Storm

The crystal hummed with an otherworldly resonance as Aria placed her trembling hand upon its surface. The moment her skin made contact, a surge of energy coursed through her veins, electric and ancient.

"Careful," Kael warned from the shadows, his weathered face etched with concern. "The Heartstone doesn't yield its secrets easily."

Aria's eyes fluttered closed as visions flooded her mind—fragments of a time when the crystals sang in harmony, before the great shattering that had torn the realms apart. She saw herself, or someone who looked like her, standing in this very chamber centuries ago.

"I can see it," she whispered, her voice barely audible over the crystal's song. "The past... the future... they're all connected."

Zara's laugh echoed from the doorway, cold and sharp as winter frost. "How touching. The little apprentice thinks she understands." She stepped into the light, dark energy crackling around her fingertips. "But understanding and controlling are two very different things."

The confrontation Aria had been dreading was finally here. As the two mages faced each other across the ancient chamber, the very air seemed to thicken with possibility and danger. The storm that had been gathering for so long was about to break.`;

// Helper function to generate demo data with variations
export function generateDemoData(type: 'analytics' | 'story' | 'agents' | 'editor') {
  switch (type) {
    case 'analytics':
      return DEMO_ANALYTICS;
    case 'story':
      return DEMO_STORY_BIBLE;
    case 'agents':
      return DEMO_AGENTS;
    case 'editor':
      return DEMO_EDITOR_CONTENT;
    default:
      return null;
  }
}

// Utility to check if we're in demo mode
export function isDemoMode(): boolean {
  return process.env.NEXT_PUBLIC_DEMO_MODE === 'true' || 
         typeof window !== 'undefined' && window.location.pathname.includes('/demo');
}