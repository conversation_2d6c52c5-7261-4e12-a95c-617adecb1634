import { ServiceRegistry } from './base-service';
import { logger } from '@/lib/services/logger';

import { AIOrchestrator } from './ai-orchestrator';
import { ContentGenerator } from './content-generator';
import { ContextManager } from './context-manager';
import { AnalyticsEngine } from './analytics-engine';
import { UnifiedCollaborationService } from './unified-collaboration-service';
import { SemanticSearchService } from './semantic-search';
import { config } from '@/lib/config';

export class ServiceManager {
  private static instance: ServiceManager;
  private registry: ServiceRegistry;
  private isInitialized: boolean = false;

  private constructor() {
    this.registry = ServiceRegistry.getInstance();
  }

  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Skip initialization during build time
    if (config.build.phase === 'phase-production-build' || config.build.phase === 'phase-export') {
      logger.info('[ServiceManager] Skipping initialization during build phase');
      return;
    }

    // Skip initialization in non-Node.js environments
    if (typeof window !== 'undefined') {
      logger.info('[ServiceManager] Skipping initialization in browser environment');
      return;
    }

    logger.info('[ServiceManager] Initializing microservices...');

    try {
      // Register all services
      this.registry.register(new AIOrchestrator());
      this.registry.register(new ContentGenerator());
      this.registry.register(new ContextManager());
      this.registry.register(new AnalyticsEngine());
      this.registry.register(new UnifiedCollaborationService()); // Serverless-compatible version using Supabase
      this.registry.register(new SemanticSearchService());

      // Initialize all services in dependency order
      await this.registry.initializeAll();

      this.isInitialized = true;
      logger.info('[ServiceManager] All microservices initialized successfully');

    } catch (error) {
      logger.error('[ServiceManager] Failed to initialize services:', error);
      // Don't throw - allow graceful degradation
      // Services will return null when accessed
    }
  }

  // Ensure services are initialized before accessing
  private async ensureInitialized(): Promise<boolean> {
    if (!this.isInitialized) {
      try {
        await this.initialize();
      } catch (error) {
        logger.error('[ServiceManager] Lazy initialization failed:', error);
        return false;
      }
    }
    return this.isInitialized;
  }

  async shutdown(): Promise<void> {
    if (!this.isInitialized) return;

    logger.info('[ServiceManager] Shutting down microservices...');
    await this.registry.shutdownAll();
    this.isInitialized = false;
    logger.info('[ServiceManager] All microservices shut down');
  }

  async healthCheck(): Promise<Record<string, Record<string, unknown>>> {
    if (!this.isInitialized) {
      return { error: { message: 'Service manager not initialized' } };
    }

    return await this.registry.healthCheckAll();
  }

  async getService<T>(serviceName: string): Promise<T | null> {
    const initialized = await this.ensureInitialized();
    if (!initialized) {
      logger.warn(`[ServiceManager] Service ${serviceName} not available - initialization failed`);
      return null;
    }

    return this.registry.get(serviceName) as T | null;
  }

  // Convenience methods for accessing specific services
  async getAIOrchestrator(): Promise<AIOrchestrator | null> {
    return this.getService<AIOrchestrator>('ai-orchestrator');
  }

  async getContentGenerator(): Promise<ContentGenerator | null> {
    return this.getService<ContentGenerator>('content-generator');
  }

  async getContextManager(): Promise<ContextManager | null> {
    return this.getService<ContextManager>('context-manager');
  }

  async getAnalyticsEngine(): Promise<AnalyticsEngine | null> {
    return this.getService<AnalyticsEngine>('analytics-engine');
  }

  async getCollaborationHub(): Promise<UnifiedCollaborationService | null> {
    return this.getService<UnifiedCollaborationService>('collaboration-hub');
  }

  async getSemanticSearch(): Promise<SemanticSearchService | null> {
    return this.getService<SemanticSearchService>('semantic-search');
  }

  async submitWritingTask(task: {
    type: 'generate' | 'edit' | 'expand' | 'rewrite';
    projectId: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
    input: {
      content?: string;
      prompt?: string;
      context?: Record<string, unknown>;
      requirements?: string[];
    };
  }): Promise<string | null> {
    const orchestrator = await this.getAIOrchestrator();
    if (!orchestrator) {
      logger.error('[ServiceManager] AI Orchestrator not available');
      return null;
    }

    const taskData = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...task,
      priority: task.priority || 'medium',
      status: 'pending' as const,
      createdAt: Date.now()
    };

    const response = await orchestrator.submitTask(taskData);
    return response.success ? response.data || null : null;
  }

  async generateContent(request: {
    type: 'scene' | 'dialogue' | 'description' | 'chapter' | 'character' | 'plot-outline';
    prompt: string;
    projectId: string;
    context?: Record<string, unknown>;
    style?: string;
    length?: 'short' | 'medium' | 'long';
    tone?: string;
  }): Promise<string | null> {
    const generator = await this.getContentGenerator();
    if (!generator) {
      logger.error('[ServiceManager] Content Generator not available');
      return null;
    }

    const response = await generator.generateContent(request);
    return response.success ? response.data || null : null;
  }

  async trackAnalytics(event: {
    userId: string;
    sessionId: string;
    type: 'action' | 'progress' | 'behavior' | 'performance';
    event: string;
    data: Record<string, unknown>;
    projectId?: string;
  }): Promise<boolean> {
    const analytics = await this.getAnalyticsEngine();
    if (!analytics) {
      logger.warn('[ServiceManager] Analytics Engine not available');
      return false;
    }

    const response = await analytics.trackEvent(event);
    return response.success;
  }

  async getProjectContext(projectId: string, refresh = false): Promise<Record<string, unknown> | null> {
    const contextManager = await this.getContextManager();
    if (!contextManager) {
      logger.error('[ServiceManager] Context Manager not available');
      return null;
    }

    const response = await contextManager.getProjectContext(projectId, refresh);
    return response.success ? (response.data as unknown as Record<string, unknown>) : null;
  }

  async createCollaborationSession(projectId: string, ownerId: string): Promise<string | null> {
    const collaboration = await this.getCollaborationHub();
    if (!collaboration) {
      logger.error('[ServiceManager] Collaboration Hub not available');
      return null;
    }

    const response = await collaboration.createSession(projectId, ownerId);
    return response.success ? response.data || null : null;
  }

  isServiceActive(serviceName: string): boolean {
    const service = this.registry.get(serviceName);
    return service ? service.isActive() : false;
  }

  getActiveServices(): string[] {
    return this.registry.getServicesByStatus('active')
      .map(service => service.getConfig().name);
  }

  getInactiveServices(): string[] {
    return this.registry.getServicesByStatus('inactive')
      .map(service => service.getConfig().name);
  }

  async restartService(serviceName: string): Promise<boolean> {
    const service = this.registry.get(serviceName);
    if (!service) {
      logger.error(`[ServiceManager] Service ${serviceName} not found`);
      return false;
    }

    try {
      await service.shutdown();
      await service.initialize();
      logger.info(`[ServiceManager] Service ${serviceName} restarted successfully`);
      return true;
    } catch (error) {
      logger.error(`[ServiceManager] Failed to restart service ${serviceName}:`, error);
      return false;
    }
  }

  getServiceStatus(): Record<string, {
    name: string;
    version: string;
    status: string;
    isActive: boolean;
    dependencies: string[];
  }> {
    const services = ['ai-orchestrator', 'content-generator', 'context-manager', 'analytics-engine', 'collaboration-hub', 'semantic-search'];
    const status: Record<string, {
      name: string;
      version: string;
      status: string;
      isActive: boolean;
      dependencies: string[];
    }> = {};

    services.forEach(serviceName => {
      const service = this.registry.get(serviceName);
      if (service) {
        const config = service.getConfig();
        status[serviceName] = {
          name: config.name,
          version: config.version,
          status: config.status,
          isActive: service.isActive(),
          dependencies: config.dependencies
        };
      }
    });

    return status;
  }
}

export default ServiceManager;