# State Management Standardization Guide

## Overview
BookScribe uses Zustand as the primary state management solution, with React Context reserved for specific use cases.

## When to Use What

### Use Zustand For:
- **Global application state** (user preferences, settings, theme)
- **Cross-component state** (editor state, project data, UI state)
- **Complex state logic** (with actions, computed values, middleware)
- **Performance-critical state** (frequent updates, large data)
- **Persistent state** (with localStorage/sessionStorage)

### Use React Context For:
- **Dependency injection** (providing services, configurations)
- **Theme providers** (when integrated with CSS-in-JS libraries)
- **Authentication wrappers** (for protected routes)
- **Component-tree specific state** (form contexts, modal contexts)

## Zustand Store Patterns

### Basic Store Pattern
```typescript
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface StoreState {
  // State
  items: Item[]
  isLoading: boolean
  error: string | null
  
  // Actions
  fetchItems: () => Promise<void>
  addItem: (item: Item) => void
  updateItem: (id: string, updates: Partial<Item>) => void
  deleteItem: (id: string) => void
  
  // Computed/Selectors
  getItemById: (id: string) => Item | undefined
}

export const useStore = create<StoreState>()(
  devtools(
    persist(
      immer((set, get) => ({
        // Initial state
        items: [],
        isLoading: false,
        error: null,
        
        // Actions
        fetchItems: async () => {
          set((state) => {
            state.isLoading = true
            state.error = null
          })
          
          try {
            const items = await api.fetchItems()
            set((state) => {
              state.items = items
              state.isLoading = false
            })
          } catch (error) {
            set((state) => {
              state.error = error.message
              state.isLoading = false
            })
          }
        },
        
        addItem: (item) =>
          set((state) => {
            state.items.push(item)
          }),
          
        updateItem: (id, updates) =>
          set((state) => {
            const index = state.items.findIndex(item => item.id === id)
            if (index !== -1) {
              state.items[index] = { ...state.items[index], ...updates }
            }
          }),
          
        deleteItem: (id) =>
          set((state) => {
            state.items = state.items.filter(item => item.id !== id)
          }),
          
        // Computed
        getItemById: (id) => get().items.find(item => item.id === id)
      })),
      {
        name: 'items-storage',
        partialize: (state) => ({ items: state.items }) // Only persist items
      }
    ),
    {
      name: 'ItemsStore'
    }
  )
)
```

### Sliced Store Pattern (for large stores)
```typescript
// stores/app-store.ts
import { create } from 'zustand'
import { createUserSlice, UserSlice } from './slices/user-slice'
import { createProjectSlice, ProjectSlice } from './slices/project-slice'
import { createUISlice, UISlice } from './slices/ui-slice'

export type AppStore = UserSlice & ProjectSlice & UISlice

export const useAppStore = create<AppStore>()((...args) => ({
  ...createUserSlice(...args),
  ...createProjectSlice(...args),
  ...createUISlice(...args)
}))

// stores/slices/user-slice.ts
export interface UserSlice {
  user: User | null
  setUser: (user: User | null) => void
  updateProfile: (updates: Partial<User>) => void
}

export const createUserSlice: StateCreator<
  AppStore,
  [],
  [],
  UserSlice
> = (set) => ({
  user: null,
  setUser: (user) => set({ user }),
  updateProfile: (updates) =>
    set((state) => ({
      user: state.user ? { ...state.user, ...updates } : null
    }))
})
```

## Migration Examples

### Context to Zustand Migration

**Before (React Context):**
```typescript
// contexts/editor-context.tsx
const EditorContext = createContext<EditorContextType | null>(null)

export function EditorProvider({ children }) {
  const [content, setContent] = useState('')
  const [wordCount, setWordCount] = useState(0)
  
  const updateContent = (newContent: string) => {
    setContent(newContent)
    setWordCount(countWords(newContent))
  }
  
  return (
    <EditorContext.Provider value={{ content, wordCount, updateContent }}>
      {children}
    </EditorContext.Provider>
  )
}

export const useEditor = () => {
  const context = useContext(EditorContext)
  if (!context) throw new Error('useEditor must be used within EditorProvider')
  return context
}
```

**After (Zustand):**
```typescript
// stores/editor-store.ts
interface EditorStore {
  content: string
  wordCount: number
  updateContent: (content: string) => void
}

export const useEditorStore = create<EditorStore>((set) => ({
  content: '',
  wordCount: 0,
  updateContent: (content) =>
    set({
      content,
      wordCount: countWords(content)
    })
}))

// Usage in component
const { content, wordCount, updateContent } = useEditorStore()
```

## Best Practices

### 1. Store Organization
```
src/stores/
├── index.ts              # Re-exports all stores
├── auth-store.ts         # Authentication state
├── project-store.ts      # Project management
├── editor-store.ts       # Editor state
├── ui-store.ts          # UI preferences
└── slices/              # For large stores
    ├── user-slice.ts
    └── settings-slice.ts
```

### 2. Selector Pattern
```typescript
// Avoid subscribing to entire store
const items = useStore(state => state.items) // ✅ Good

// Not this
const state = useStore() // ❌ Bad - rerenders on any change
```

### 3. Action Naming
```typescript
// Use clear, action-oriented names
interface Store {
  // Actions (verbs)
  fetchProjects: () => Promise<void>
  createProject: (data: ProjectData) => Promise<void>
  updateProject: (id: string, data: Partial<ProjectData>) => Promise<void>
  deleteProject: (id: string) => Promise<void>
  
  // State (nouns)
  projects: Project[]
  isLoading: boolean
  error: string | null
}
```

### 4. Async Actions
```typescript
const useProjectStore = create<ProjectStore>((set, get) => ({
  projects: [],
  isLoading: false,
  error: null,
  
  fetchProjects: async () => {
    // Guard against multiple fetches
    if (get().isLoading) return
    
    set({ isLoading: true, error: null })
    
    try {
      const { data } = await supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false })
      
      set({ projects: data || [], isLoading: false })
    } catch (error) {
      set({ error: error.message, isLoading: false })
    }
  }
}))
```

### 5. TypeScript Integration
```typescript
// Define store interface first
interface AuthStore {
  user: User | null
  isAuthenticated: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
}

// Create typed hooks
export const useUser = () => useAuthStore(state => state.user)
export const useIsAuthenticated = () => useAuthStore(state => state.isAuthenticated)
export const useAuthActions = () => useAuthStore(state => ({
  login: state.login,
  logout: state.logout
}))
```

## Common Patterns

### 1. Optimistic Updates
```typescript
updateItem: async (id, updates) => {
  // Optimistic update
  set((state) => {
    const item = state.items.find(i => i.id === id)
    if (item) Object.assign(item, updates)
  })
  
  try {
    await api.updateItem(id, updates)
  } catch (error) {
    // Revert on error
    get().fetchItems()
  }
}
```

### 2. Computed Values
```typescript
const useProjectStats = create((set, get) => ({
  projects: [],
  
  // Computed getters
  get totalWordCount() {
    return get().projects.reduce((sum, p) => sum + p.wordCount, 0)
  },
  
  get activeProjects() {
    return get().projects.filter(p => p.status === 'active')
  }
}))
```

### 3. Subscription Pattern
```typescript
// Subscribe to external changes (e.g., Supabase realtime)
useEffect(() => {
  const channel = supabase
    .channel('projects')
    .on('postgres_changes', { 
      event: '*', 
      schema: 'public', 
      table: 'projects' 
    }, (payload) => {
      if (payload.eventType === 'INSERT') {
        useProjectStore.getState().addProject(payload.new)
      }
      // Handle UPDATE, DELETE...
    })
    .subscribe()
    
  return () => {
    supabase.removeChannel(channel)
  }
}, [])
```

## Testing Zustand Stores

```typescript
// __tests__/stores/project-store.test.ts
import { renderHook, act } from '@testing-library/react'
import { useProjectStore } from '@/stores/project-store'

describe('ProjectStore', () => {
  beforeEach(() => {
    useProjectStore.setState({ projects: [], isLoading: false })
  })
  
  it('should add a project', () => {
    const { result } = renderHook(() => useProjectStore())
    
    act(() => {
      result.current.addProject({
        id: '1',
        name: 'Test Project'
      })
    })
    
    expect(result.current.projects).toHaveLength(1)
    expect(result.current.projects[0].name).toBe('Test Project')
  })
})
```

## Migration Checklist

- [ ] Identify all React Context providers
- [ ] Categorize state (global vs component-tree specific)
- [ ] Create Zustand stores for global state
- [ ] Migrate state logic to store actions
- [ ] Update components to use store hooks
- [ ] Remove unnecessary Context providers
- [ ] Add persistence where needed
- [ ] Add devtools for development
- [ ] Update tests
- [ ] Document store interfaces