'use client';

import { useState } from 'react';
import { CollaborativeEditorWrapper } from '@/components/editor/collaborative-editor-wrapper';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Info, Users, Zap, Shield } from 'lucide-react';

// Demo content
const DEMO_CONTENT = `# Chapter 1: The Beginning

The old mansion stood at the end of Willow Street, its weathered facade telling stories of decades past. Sarah approached the wrought iron gates with a mixture of excitement and trepidation.

"This is it," she whispered to herself, clutching the brass key that had arrived in yesterday's mail. The letter had been brief but intriguing:

*"Your inheritance awaits. Come alone. Trust no one."*

As she pushed open the gate, it creaked loudly in protest, as if warning her to turn back. But <PERSON> had come too far to give up now. The mystery of her great-aunt's disappearance had haunted her family for years, and this house might hold the answers.

The gravel path crunched beneath her feet as she made her way to the front door. Each step brought new questions:
- Why had Aunt <PERSON> left everything to her?
- What secrets did this house hold?
- And most importantly, why the warning to trust no one?

Sarah inserted the key into the lock, took a deep breath, and turned it. The door swung open, revealing a grand foyer shrouded in darkness...`;

export default function CollaborationDemoPage() {
  const [isCollaborating, setIsCollaborating] = useState(false);
  const [content, setContent] = useState(DEMO_CONTENT);

  return (
    <div className="container-wide mx-auto p-6 max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px]">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold">Real-time Collaboration Demo</h1>
          <Badge variant="secondary" className="text-sm">
            Beta Feature
          </Badge>
        </div>
        
        <p className="text-muted-foreground text-lg">
          Experience the power of real-time collaborative editing. Write together with your team, 
          see live cursor positions, and watch changes sync instantly.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Multi-User Editing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Multiple authors can edit the same document simultaneously. See their cursors, 
              selections, and changes in real-time.
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Instant Sync
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Changes are synchronized instantly using WebSocket technology. No more manual 
              saves or version conflicts.
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Conflict Resolution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Operational Transform algorithms ensure that all edits are preserved and 
              conflicts are automatically resolved.
            </CardDescription>
          </CardContent>
        </Card>
      </div>

      <Alert className="mb-6">
        <Info className="h-4 w-4" />
        <AlertDescription>
          This is a demo of the collaboration feature. In a real project, you would share 
          a collaboration link with your team members to edit together.
        </AlertDescription>
      </Alert>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Try Collaborative Editing</CardTitle>
          <CardDescription>
            Enable collaboration to see the real-time features in action. Open this page in 
            multiple browser windows to simulate multiple users.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CollaborativeEditorWrapper
            projectId="demo-project"
            documentId="demo-chapter"
            value={content}
            onChange={setContent}
            enableCollaboration={isCollaborating}
            showCollaborators={true}
            onCollaborationStateChange={(connected) => {
              // Collaboration state change handled by the component
            }}
            height="500px"
            showToolbar={true}
            showStats={true}
          />
        </CardContent>
      </Card>

      <div className="flex justify-center">
        <Button
          size="lg"
          onClick={() => setIsCollaborating(!isCollaborating)}
          variant={isCollaborating ? "secondary" : "default"}
        >
          {isCollaborating ? 'Disable Collaboration' : 'Enable Collaboration'}
        </Button>
      </div>

      {isCollaborating && (
        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground mb-2">
            Collaboration is enabled! Open this page in another browser window to see real-time sync.
          </p>
          <p className="text-xs text-muted-foreground">
            Note: This demo uses a local WebSocket server. In production, this would connect 
            to your collaboration server.
          </p>
        </div>
      )}
    </div>
  );
}