/**
 * Database Table Names Configuration
 * Centralized location for all database table names used across the application
 */

export const DB_TABLES = {
  // User and Authentication
  USERS: 'users',
  PROFILES: 'profiles',
  USER_ROLES: 'user_roles',
  
  // Projects and Content
  PROJECTS: 'projects',
  CHAPTERS: 'chapters',
  CHARACTERS: 'characters',
  SCENES: 'scenes',
  LOCATIONS: 'locations',
  STORY_BIBLES: 'story_bibles',
  PLOT_OUTLINES: 'plot_outlines',
  
  // Series and Universe
  SERIES: 'series',
  SERIES_BOOKS: 'series_books',
  UNIVERSES: 'universes',
  UNIVERSE_SERIES: 'universe_series',
  UNIVERSE_LOCATIONS: 'universe_locations',
  UNIVERSE_CHARACTERS: 'universe_characters',
  
  // Collaboration
  COLLABORATORS: 'collaborators',
  COLLABORATION_SESSIONS: 'collaboration_sessions',
  COLLABORATION_CURSORS: 'collaboration_cursors',
  COLLABORATION_EDITS: 'collaboration_edits',
  
  // Analytics and Tracking
  ANALYTICS_EVENTS: 'analytics_events',
  WRITING_SESSIONS: 'writing_sessions',
  WORD_COUNT_HISTORY: 'word_count_history',
  PRODUCTIVITY_METRICS: 'productivity_metrics',
  
  // AI and Generation
  AI_GENERATIONS: 'ai_generations',
  AI_USAGE: 'ai_usage',
  VOICE_PROFILES: 'voice_profiles',
  GENERATION_CACHE: 'generation_cache',
  
  // Export and Publishing
  EXPORTS: 'exports',
  EXPORT_HISTORY: 'export_history',
  PUBLISHING_METADATA: 'publishing_metadata',
  
  // Templates and Presets
  PROJECT_TEMPLATES: 'project_templates',
  CHARACTER_TEMPLATES: 'character_templates',
  SCENE_TEMPLATES: 'scene_templates',
  
  // Settings and Preferences
  USER_SETTINGS: 'user_settings',
  PROJECT_SETTINGS: 'project_settings',
  THEME_PREFERENCES: 'theme_preferences',
  
  // Achievements and Gamification
  ACHIEVEMENTS: 'achievements',
  USER_ACHIEVEMENTS: 'user_achievements',
  MILESTONES: 'milestones',
  BADGES: 'badges',
  
  // Subscriptions and Billing
  SUBSCRIPTIONS: 'subscriptions',
  SUBSCRIPTION_PLANS: 'subscription_plans',
  PAYMENT_HISTORY: 'payment_history',
  USAGE_LIMITS: 'usage_limits',
  
  // Backup and Version Control
  PROJECT_BACKUPS: 'project_backups',
  CHAPTER_VERSIONS: 'chapter_versions',
  CHANGE_HISTORY: 'change_history',
  
  // Knowledge Base
  KNOWLEDGE_BASE_ENTRIES: 'knowledge_base_entries',
  RESEARCH_NOTES: 'research_notes',
  WORLD_BUILDING_NOTES: 'world_building_notes',
  
  // Comments and Feedback
  COMMENTS: 'comments',
  FEEDBACK: 'feedback',
  REVIEWS: 'reviews',
  
  // Notifications
  NOTIFICATIONS: 'notifications',
  NOTIFICATION_PREFERENCES: 'notification_preferences',
  
  // Misc
  AUDIT_LOG: 'audit_log',
  SYSTEM_SETTINGS: 'system_settings',
  FEATURE_FLAGS: 'feature_flags',
} as const;

// Type for table names
export type TableName = typeof DB_TABLES[keyof typeof DB_TABLES];

// Helper function to get table name with optional schema
export function getTableName(table: keyof typeof DB_TABLES, schema?: string): string {
  const tableName = DB_TABLES[table];
  return schema ? `${schema}.${tableName}` : tableName;
}

// Validation helper to ensure table exists
export function isValidTable(tableName: string): tableName is TableName {
  return Object.values(DB_TABLES).includes(tableName as TableName);
}

// Export individual table names for backward compatibility
export const {
  USERS,
  PROFILES,
  PROJECTS,
  CHAPTERS,
  CHARACTERS,
  SCENES,
  LOCATIONS,
  STORY_BIBLES,
  SERIES,
  UNIVERSES,
  COLLABORATORS,
  AI_GENERATIONS,
  ACHIEVEMENTS,
  SUBSCRIPTIONS,
} = DB_TABLES;