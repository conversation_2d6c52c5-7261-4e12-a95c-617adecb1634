# Real-time Collaboration System Documentation

## Overview

BookScribe's real-time collaboration system enables multiple authors to work on the same project simultaneously, with live updates, conflict resolution, and presence awareness. Built on Supabase Realtime, it provides a seamless collaborative writing experience.

## Architecture

### System Components

```mermaid
graph TB
    subgraph "Client Layer"
        Editor[Monaco Editor]
        Presence[Presence Manager]
        Conflict[Conflict Resolver]
        Sync[Sync Engine]
    end
    
    subgraph "WebSocket Layer"
        Channel[Supabase Channel]
        Broadcast[Broadcast Events]
        PostgresChanges[Postgres Changes]
        PresenceSync[Presence Sync]
    end
    
    subgraph "Server Layer"
        API[Collaboration API]
        LockManager[Lock Manager]
        ChangeLog[Change Log]
        ConflictDetector[Conflict Detector]
    end
    
    subgraph "Database"
        Projects[(projects)]
        Chapters[(chapters)]
        Collaborators[(collaborators)]
        Locks[(content_locks)]
    end
    
    Editor --> Sync
    Sync --> Channel
    Channel --> Broadcast
    Channel --> PostgresChanges
    Presence --> PresenceSync
    
    API --> LockManager
    API --> ConflictDetector
    LockManager --> Locks
    ChangeLog --> Chapters
```

## Core Features

### 1. Real-time Text Synchronization

#### Implementation
```typescript
class CollaborationSync {
  private channel: RealtimeChannel;
  private pendingChanges: ChangeOperation[] = [];
  private localVersion: number = 0;
  private serverVersion: number = 0;
  
  async initializeSync(projectId: string, chapterId: string) {
    this.channel = supabase.channel(`chapter:${chapterId}`)
      .on('broadcast', { event: 'text-change' }, (payload) => {
        this.handleRemoteChange(payload);
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'chapters',
        filter: `id=eq.${chapterId}`
      }, (payload) => {
        this.handleDatabaseChange(payload);
      })
      .subscribe();
  }
  
  async sendChange(operation: ChangeOperation) {
    // Apply operational transformation
    const transformed = this.transformOperation(operation);
    
    // Broadcast to other clients
    await this.channel.send({
      type: 'broadcast',
      event: 'text-change',
      payload: {
        operation: transformed,
        version: ++this.localVersion,
        userId: this.userId,
        timestamp: Date.now()
      }
    });
    
    // Queue for server sync
    this.pendingChanges.push(transformed);
  }
}
```

### 2. Operational Transformation (OT)

#### Transform Operations
```typescript
interface Operation {
  type: 'insert' | 'delete' | 'format';
  position: number;
  content?: string;
  length?: number;
  attributes?: TextAttributes;
}

class OperationalTransform {
  transform(op1: Operation, op2: Operation): [Operation, Operation] {
    if (op1.type === 'insert' && op2.type === 'insert') {
      if (op1.position < op2.position) {
        return [op1, { ...op2, position: op2.position + op1.content!.length }];
      } else if (op1.position > op2.position) {
        return [{ ...op1, position: op1.position + op2.content!.length }, op2];
      } else {
        // Same position - use user ID for consistency
        if (op1.userId < op2.userId) {
          return [op1, { ...op2, position: op2.position + op1.content!.length }];
        } else {
          return [{ ...op1, position: op1.position + op2.content!.length }, op2];
        }
      }
    }
    
    // Handle other operation combinations...
    return this.transformOperations(op1, op2);
  }
  
  compose(ops: Operation[]): Operation[] {
    // Compose multiple operations into minimal set
    return this.composeOperations(ops);
  }
}
```

### 3. Presence & Awareness

#### Presence System
```typescript
interface UserPresence {
  userId: string;
  userName: string;
  userColor: string;
  cursor: {
    chapter: number;
    line: number;
    column: number;
  };
  selection?: {
    start: Position;
    end: Position;
  };
  status: 'active' | 'idle' | 'away';
  lastActivity: Date;
}

class PresenceManager {
  private presenceChannel: RealtimeChannel;
  private collaborators = new Map<string, UserPresence>();
  
  async trackPresence(projectId: string) {
    this.presenceChannel = supabase.channel(`presence:${projectId}`)
      .on('presence', { event: 'sync' }, () => {
        const state = this.presenceChannel.presenceState();
        this.updateCollaborators(state);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        this.handleCollaboratorJoin(key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        this.handleCollaboratorLeave(key, leftPresences);
      });
    
    await this.presenceChannel.subscribe();
    await this.broadcastPresence();
  }
  
  async updateCursor(position: CursorPosition) {
    await this.presenceChannel.track({
      cursor: position,
      lastActivity: new Date(),
      status: 'active'
    });
  }
}
```

### 4. Conflict Resolution

#### Conflict Detection
```typescript
interface ConflictDetector {
  detectConflicts(
    localChanges: Change[],
    remoteChanges: Change[]
  ): Conflict[] {
    const conflicts: Conflict[] = [];
    
    for (const local of localChanges) {
      for (const remote of remoteChanges) {
        if (this.isConflicting(local, remote)) {
          conflicts.push({
            type: this.getConflictType(local, remote),
            localChange: local,
            remoteChange: remote,
            severity: this.calculateSeverity(local, remote)
          });
        }
      }
    }
    
    return conflicts;
  }
  
  private isConflicting(change1: Change, change2: Change): boolean {
    // Check if changes overlap
    if (change1.type === 'edit' && change2.type === 'edit') {
      const overlap = this.calculateOverlap(
        change1.range,
        change2.range
      );
      return overlap > 0;
    }
    
    // Check structural conflicts
    if (change1.type === 'delete' && change2.type === 'edit') {
      return this.isWithinRange(change2.range, change1.range);
    }
    
    return false;
  }
}
```

#### Conflict Resolution Strategies
```typescript
enum ResolutionStrategy {
  LAST_WRITE_WINS = 'last_write_wins',
  MANUAL = 'manual',
  MERGE = 'merge',
  BRANCH = 'branch'
}

class ConflictResolver {
  async resolveConflict(
    conflict: Conflict,
    strategy: ResolutionStrategy
  ): Promise<Resolution> {
    switch (strategy) {
      case ResolutionStrategy.LAST_WRITE_WINS:
        return this.resolveByTimestamp(conflict);
        
      case ResolutionStrategy.MANUAL:
        return this.promptUserResolution(conflict);
        
      case ResolutionStrategy.MERGE:
        return this.autoMerge(conflict);
        
      case ResolutionStrategy.BRANCH:
        return this.createBranch(conflict);
    }
  }
  
  private async autoMerge(conflict: Conflict): Promise<Resolution> {
    // Intelligent merge based on content type
    if (conflict.type === 'formatting') {
      // Merge formatting changes
      return this.mergeFormatting(conflict);
    }
    
    if (conflict.type === 'adjacent_edit') {
      // Both changes can coexist
      return this.acceptBoth(conflict);
    }
    
    // Fall back to manual resolution
    return this.promptUserResolution(conflict);
  }
}
```

### 5. Content Locking

#### Lock Management
```typescript
interface ContentLock {
  id: string;
  userId: string;
  projectId: string;
  chapterId: string;
  range?: TextRange;
  type: 'chapter' | 'paragraph' | 'selection';
  acquiredAt: Date;
  expiresAt: Date;
}

class LockManager {
  async acquireLock(
    lockRequest: LockRequest
  ): Promise<ContentLock | null> {
    // Check for existing locks
    const existingLock = await this.checkExistingLocks(lockRequest);
    
    if (existingLock && !this.isExpired(existingLock)) {
      return null; // Lock denied
    }
    
    // Create new lock
    const lock = await supabase
      .from('content_locks')
      .insert({
        user_id: lockRequest.userId,
        project_id: lockRequest.projectId,
        chapter_id: lockRequest.chapterId,
        range: lockRequest.range,
        type: lockRequest.type,
        acquired_at: new Date(),
        expires_at: new Date(Date.now() + LOCK_DURATION)
      })
      .select()
      .single();
    
    // Broadcast lock acquisition
    await this.broadcastLockChange('acquired', lock);
    
    return lock;
  }
  
  async releaseLock(lockId: string): Promise<void> {
    await supabase
      .from('content_locks')
      .delete()
      .eq('id', lockId);
    
    await this.broadcastLockChange('released', { id: lockId });
  }
  
  async extendLock(lockId: string): Promise<boolean> {
    const result = await supabase
      .from('content_locks')
      .update({
        expires_at: new Date(Date.now() + LOCK_DURATION)
      })
      .eq('id', lockId)
      .select();
    
    return result.data?.length > 0;
  }
}
```

### 6. Collaborative Cursors

#### Cursor Visualization
```typescript
class CursorRenderer {
  private cursorElements = new Map<string, HTMLElement>();
  
  renderCollaboratorCursor(
    userId: string,
    position: CursorPosition,
    userInfo: UserInfo
  ) {
    let cursor = this.cursorElements.get(userId);
    
    if (!cursor) {
      cursor = this.createCursorElement(userInfo);
      this.cursorElements.set(userId, cursor);
    }
    
    // Update cursor position
    const coords = this.editor.positionToCoordinates(position);
    cursor.style.left = `${coords.x}px`;
    cursor.style.top = `${coords.y}px`;
    
    // Show user label on hover
    cursor.setAttribute('data-user', userInfo.name);
    cursor.style.backgroundColor = userInfo.color;
  }
  
  renderSelection(
    userId: string,
    selection: TextSelection,
    userInfo: UserInfo
  ) {
    const decorations = this.editor.createDecoration({
      range: selection,
      className: 'collaborator-selection',
      style: {
        backgroundColor: `${userInfo.color}20`,
        border: `2px solid ${userInfo.color}`
      }
    });
    
    this.selectionDecorations.set(userId, decorations);
  }
}
```

## Collaboration Modes

### 1. Live Collaboration
- Real-time cursor tracking
- Instant text synchronization
- Presence indicators
- Live chat/comments

### 2. Asynchronous Collaboration
- Change tracking
- Revision history
- Comments and suggestions
- Approval workflows

### 3. Hybrid Mode
- Real-time awareness
- Batched synchronization
- Offline capability
- Conflict queuing

## Performance Optimization

### 1. Change Batching
```typescript
class ChangeBatcher {
  private pendingChanges: Change[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  
  addChange(change: Change) {
    this.pendingChanges.push(change);
    
    if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.flushChanges();
      }, BATCH_DELAY);
    }
    
    // Force flush on significant changes
    if (this.shouldForceFlush(change)) {
      this.flushChanges();
    }
  }
  
  private flushChanges() {
    if (this.pendingChanges.length === 0) return;
    
    const batch = this.compressChanges(this.pendingChanges);
    this.sendBatch(batch);
    
    this.pendingChanges = [];
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
  }
}
```

### 2. Differential Sync
```typescript
class DifferentialSync {
  private shadow: string = '';
  private localVersion: number = 0;
  private remoteVersion: number = 0;
  
  createDiff(currentText: string): Diff {
    const diff = this.computeDiff(this.shadow, currentText);
    return {
      diff,
      baseVersion: this.localVersion,
      targetVersion: this.localVersion + 1
    };
  }
  
  applyDiff(diff: Diff): string {
    if (diff.baseVersion !== this.remoteVersion) {
      // Need to transform diff
      diff = this.transformDiff(diff, this.remoteVersion);
    }
    
    this.shadow = this.applyPatch(this.shadow, diff.diff);
    this.remoteVersion = diff.targetVersion;
    
    return this.shadow;
  }
}
```

## Security & Permissions

### 1. Access Control
```typescript
interface CollaborationPermissions {
  canRead: boolean;
  canWrite: boolean;
  canComment: boolean;
  canInvite: boolean;
  canDelete: boolean;
}

class PermissionManager {
  async checkPermissions(
    userId: string,
    projectId: string
  ): Promise<CollaborationPermissions> {
    const { data: collaborator } = await supabase
      .from('project_collaborators')
      .select('role, permissions')
      .eq('user_id', userId)
      .eq('project_id', projectId)
      .single();
    
    if (!collaborator) {
      // Check if owner
      const { data: project } = await supabase
        .from('projects')
        .select('user_id')
        .eq('id', projectId)
        .single();
      
      if (project?.user_id === userId) {
        return OWNER_PERMISSIONS;
      }
      
      return NO_PERMISSIONS;
    }
    
    return this.roleToPermissions(collaborator.role);
  }
}
```

### 2. Data Encryption
- End-to-end encryption for sensitive content
- Encrypted presence data
- Secure WebSocket connections
- Token-based authentication

## Offline Support

### 1. Local Changes Queue
```typescript
class OfflineQueue {
  private queue: LocalChange[] = [];
  private storage = new LocalStorage('collaboration_queue');
  
  async queueChange(change: Change) {
    this.queue.push({
      ...change,
      queuedAt: Date.now(),
      syncStatus: 'pending'
    });
    
    await this.storage.save(this.queue);
  }
  
  async syncWhenOnline() {
    if (!navigator.onLine) {
      // Wait for connection
      window.addEventListener('online', () => this.syncWhenOnline());
      return;
    }
    
    for (const change of this.queue) {
      try {
        await this.syncChange(change);
        change.syncStatus = 'completed';
      } catch (error) {
        change.syncStatus = 'failed';
        change.error = error.message;
      }
    }
    
    // Remove synced changes
    this.queue = this.queue.filter(c => c.syncStatus !== 'completed');
    await this.storage.save(this.queue);
  }
}
```

## Monitoring & Analytics

### Collaboration Metrics
```typescript
interface CollaborationMetrics {
  activeCollaborators: number;
  totalEdits: number;
  conflictRate: number;
  averageResolutionTime: number;
  peakConcurrentUsers: number;
  syncLatency: number;
}

class CollaborationAnalytics {
  async trackEvent(event: CollaborationEvent) {
    await supabase.from('collaboration_events').insert({
      type: event.type,
      user_id: event.userId,
      project_id: event.projectId,
      metadata: event.metadata,
      timestamp: new Date()
    });
  }
  
  async getMetrics(
    projectId: string,
    timeRange: TimeRange
  ): Promise<CollaborationMetrics> {
    // Aggregate collaboration data
    const metrics = await this.aggregateMetrics(projectId, timeRange);
    return metrics;
  }
}
```

## Best Practices

### For Developers
1. Implement proper error handling for network failures
2. Use operational transformation for all text changes
3. Batch small changes to reduce network overhead
4. Implement presence timeout handling
5. Test with simulated network conditions

### For Users
1. Save work frequently (auto-save enabled)
2. Communicate through built-in chat when needed
3. Use locking for major structural changes
4. Resolve conflicts promptly
5. Respect collaborator's work areas

## Troubleshooting

### Common Issues

1. **Cursor Jumping**
   - Cause: Improper transformation of remote operations
   - Solution: Ensure all operations go through OT

2. **Lost Changes**
   - Cause: Network disconnect during sync
   - Solution: Implement offline queue and retry

3. **Slow Synchronization**
   - Cause: Large documents or many collaborators
   - Solution: Use differential sync and compression

4. **Conflict Loops**
   - Cause: Simultaneous edits to same content
   - Solution: Implement proper conflict resolution

5. **Presence Not Updating**
   - Cause: WebSocket connection issues
   - Solution: Implement heartbeat and reconnection

## Future Enhancements

### Planned Features
1. **Voice Chat Integration**: Real-time voice during collaboration
2. **AI Conflict Resolution**: Smart merging using AI
3. **Version Branching**: Git-like branching for experiments
4. **Collaborative AI**: Shared AI assistant for teams
5. **Advanced Permissions**: Paragraph-level access control

### Research Areas
- CRDT implementation for better conflict resolution
- Peer-to-peer collaboration options
- Advanced compression algorithms
- Machine learning for merge predictions
- Blockchain for change verification