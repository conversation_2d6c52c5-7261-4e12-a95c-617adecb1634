import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { ContextManager } from '@/lib/services/context-manager';
import { MemoryOptimizer } from '@/lib/services/memory-optimizer';
import { BookContextLoader } from '@/lib/services/book-context-loader';
import type { BookContext, StoryStructure, CharacterProfiles } from '@/lib/agents/types';
import type { ProjectSettings } from '@/lib/types/project-settings';

// Mock dependencies
jest.mock('@/lib/services/context-manager');
jest.mock('@/lib/services/memory-optimizer');
jest.mock('@/lib/services/book-context-loader');

describe('Memory Management During Orchestration', () => {
  let orchestrator: AdvancedAgentOrchestrator;
  let mockContextManager: jest.Mocked<ContextManager>;
  let mockMemoryOptimizer: jest.Mocked<MemoryOptimizer>;
  let mockBookContextLoader: jest.Mocked<BookContextLoader>;
  
  const mockProjectSettings: ProjectSettings = {
    primaryGenre: 'fantasy',
    secondaryGenres: ['adventure'],
    targetAudience: 'adult',
    writingStyle: 'descriptive',
    narrativeVoice: 'third-person',
    tense: 'past',
    pacing: 'medium',
    violenceLevel: 'moderate',
    romanceLevel: 'low',
    profanityLevel: 'mild',
    themeDepth: 'deep',
    worldBuildingDepth: 'extensive',
    characterComplexity: 'complex',
    plotComplexity: 'complex',
    tone: 'serious',
    dialogueStyle: 'natural',
    descriptionLevel: 'detailed',
    useDeepPOV: true,
    showDontTell: true,
    varyProse: true,
    useSymbolism: true,
    useCliffhangers: true,
    useForeshadowing: true,
    useFlashbacks: false,
    useUnreliableNarrator: false,
    protagonistTypes: ['hero'],
    antagonistTypes: ['villain'],
    supportingRoles: ['mentor', 'sidekick'],
    majorThemes: ['courage', 'friendship'],
    minorThemes: ['sacrifice'],
    culturalElements: [],
    magicSystemType: 'soft',
    technologyLevel: 'medieval',
    politicalSystem: 'monarchy',
    economicSystem: 'feudal',
    geographyType: 'earth-like',
    pacingPreference: 'medium'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    orchestrator = new AdvancedAgentOrchestrator(3);
    
    mockContextManager = ContextManager.prototype as jest.Mocked<ContextManager>;
    mockMemoryOptimizer = MemoryOptimizer.prototype as jest.Mocked<MemoryOptimizer>;
    mockBookContextLoader = BookContextLoader.prototype as jest.Mocked<BookContextLoader>;
  });

  describe('Context Memory Optimization', () => {
    it('should compress context when memory threshold is reached', async () => {
      const memoryThresholdMB = 100;
      let currentMemoryUsage = 0;
      const contextSnapshots: any[] = [];

      // Mock memory monitoring
      mockMemoryOptimizer.getCurrentMemoryUsage = jest.fn().mockImplementation(() => {
        return currentMemoryUsage;
      });

      mockMemoryOptimizer.optimizeContext = jest.fn().mockImplementation((context) => {
        // Simulate compression
        const compressed = {
          ...context,
          compressed: true,
          originalSize: JSON.stringify(context).length,
          compressedSize: Math.floor(JSON.stringify(context).length * 0.6)
        };
        
        // Reduce memory usage after compression
        currentMemoryUsage *= 0.6;
        
        contextSnapshots.push({
          type: 'compression',
          beforeMemory: currentMemoryUsage / 0.6,
          afterMemory: currentMemoryUsage,
          compressionRatio: 0.6
        });
        
        return compressed;
      });

      // Simulate memory growth during orchestration
      const context: BookContext = {
        projectId: 'test-project',
        settings: mockProjectSettings,
        projectSelections: mockProjectSettings,
        completedChapters: []
      };

      // Generate chapters and monitor memory
      for (let i = 1; i <= 10; i++) {
        // Simulate memory growth with each chapter
        const chapterContent = new Array(1000).fill(`Chapter ${i} content`).join(' ');
        context.completedChapters?.push({
          chapterNumber: i,
          title: `Chapter ${i}`,
          content: chapterContent,
          wordCount: 4000,
          scenes: [],
          metadata: {
            readingTime: 15,
            sentimentScore: 0.7,
            paceScore: 0.6
          }
        });
        
        // Update memory usage (simulate growth)
        currentMemoryUsage = (i * 15); // 15MB per chapter
        
        // Check if optimization is needed
        if (currentMemoryUsage > memoryThresholdMB) {
          const optimizedContext = await mockMemoryOptimizer.optimizeContext(context);
          expect(optimizedContext.compressed).toBe(true);
        }
      }

      // Verify compression was triggered
      expect(mockMemoryOptimizer.optimizeContext).toHaveBeenCalled();
      expect(contextSnapshots.length).toBeGreaterThan(0);
      
      // Verify memory reduction
      const lastSnapshot = contextSnapshots[contextSnapshots.length - 1];
      expect(lastSnapshot.afterMemory).toBeLessThan(lastSnapshot.beforeMemory);
    });

    it('should maintain essential context during compression', async () => {
      const essentialFields = [
        'projectId',
        'storyStructure',
        'characters',
        'currentChapterOutline',
        'recentEvents'
      ];

      mockMemoryOptimizer.optimizeContext = jest.fn().mockImplementation((context) => {
        const optimized = { ...context };
        
        // Remove non-essential data
        if (optimized.completedChapters && optimized.completedChapters.length > 5) {
          // Keep only recent chapters and summaries of older ones
          const recentChapters = optimized.completedChapters.slice(-5);
          const olderChapterSummaries = optimized.completedChapters.slice(0, -5).map(ch => ({
            chapterNumber: ch.chapterNumber,
            title: ch.title,
            summary: ch.content.substring(0, 200) + '...',
            wordCount: ch.wordCount
          }));
          
          optimized.completedChapters = recentChapters;
          optimized.chapterSummaries = olderChapterSummaries;
        }
        
        // Verify essential fields are preserved
        for (const field of essentialFields) {
          if (context[field as keyof BookContext]) {
            optimized[field as keyof BookContext] = context[field as keyof BookContext];
          }
        }
        
        return optimized;
      });

      const context: BookContext = {
        projectId: 'test-project',
        settings: mockProjectSettings,
        projectSelections: mockProjectSettings,
        storyStructure: {
          title: 'Test Story',
          premise: 'A test premise',
          genre: 'fantasy',
          themes: ['courage'],
          acts: [],
          conflicts: [],
          timeline: [],
          worldBuilding: {
            setting: {
              timeForPeriod: 'medieval',
              locations: [],
              culture: 'fantasy',
              technology: 'pre-industrial'
            },
            rules: [],
            history: []
          },
          plotPoints: []
        },
        characters: {
          protagonists: [{ id: '1', name: 'Hero', role: 'protagonist' }],
          antagonists: [],
          supporting: [],
          relationships: []
        } as unknown as CharacterProfiles,
        completedChapters: Array.from({ length: 10 }, (_, i) => ({
          chapterNumber: i + 1,
          title: `Chapter ${i + 1}`,
          content: `Full content of chapter ${i + 1}`,
          wordCount: 4000,
          scenes: []
        }))
      };

      const optimized = await mockMemoryOptimizer.optimizeContext(context);

      // Verify essential fields are preserved
      expect(optimized.projectId).toBe(context.projectId);
      expect(optimized.storyStructure).toEqual(context.storyStructure);
      expect(optimized.characters).toEqual(context.characters);
      
      // Verify compression occurred
      expect(optimized.completedChapters?.length).toBe(5); // Only recent chapters
      expect(optimized.chapterSummaries).toBeDefined();
      expect(optimized.chapterSummaries?.length).toBe(5); // Summaries of older chapters
    });
  });

  describe('Selective Context Loading', () => {
    it('should load only relevant context for specific tasks', async () => {
      const taskContextRequirements = {
        'character_development': ['storyStructure', 'characters'],
        'chapter_planning': ['storyStructure', 'characters', 'completedChapters'],
        'scene_writing': ['currentChapter', 'recentEvents', 'characterStates'],
        'dialogue_generation': ['characters', 'characterVoices', 'currentScene']
      };

      const loadedContexts: any[] = [];

      mockContextManager.getRelevantContext = jest.fn().mockImplementation((taskType) => {
        const requirements = taskContextRequirements[taskType as keyof typeof taskContextRequirements] || [];
        const relevantContext: any = {};
        
        // Load only required fields
        for (const field of requirements) {
          relevantContext[field] = `${field}_data`;
        }
        
        loadedContexts.push({
          taskType,
          loadedFields: requirements,
          contextSize: JSON.stringify(relevantContext).length
        });
        
        return relevantContext;
      });

      // Simulate different task executions
      for (const taskType of Object.keys(taskContextRequirements)) {
        const context = await mockContextManager.getRelevantContext(taskType);
        
        // Verify only required fields are loaded
        const requirements = taskContextRequirements[taskType as keyof typeof taskContextRequirements];
        expect(Object.keys(context)).toHaveLength(requirements.length);
        
        for (const field of requirements) {
          expect(context[field]).toBeDefined();
        }
      }

      // Verify selective loading occurred
      expect(loadedContexts).toHaveLength(4);
      
      // Compare context sizes
      const fullContextSize = JSON.stringify(taskContextRequirements).length;
      for (const loaded of loadedContexts) {
        expect(loaded.contextSize).toBeLessThan(fullContextSize);
      }
    });

    it('should implement lazy loading for large data structures', async () => {
      const lazyLoadCalls: string[] = [];

      mockBookContextLoader.loadProjectContext = jest.fn().mockImplementation(async (projectId) => {
        // Return minimal context with lazy-loadable references
        return {
          projectId,
          settings: mockProjectSettings,
          projectSelections: mockProjectSettings,
          // Lazy-loadable references
          storyStructure: { _lazy: true, _loader: 'loadStoryStructure' },
          characters: { _lazy: true, _loader: 'loadCharacters' },
          completedChapters: { _lazy: true, _loader: 'loadChapters' }
        };
      });

      // Mock lazy loaders
      const lazyLoaders = {
        loadStoryStructure: jest.fn().mockImplementation(async () => {
          lazyLoadCalls.push('storyStructure');
          return {
            title: 'Test Story',
            premise: 'Loaded on demand',
            // ... full structure
          };
        }),
        loadCharacters: jest.fn().mockImplementation(async () => {
          lazyLoadCalls.push('characters');
          return {
            protagonists: [{ id: '1', name: 'Hero' }],
            antagonists: [],
            supporting: []
          };
        }),
        loadChapters: jest.fn().mockImplementation(async () => {
          lazyLoadCalls.push('chapters');
          return Array.from({ length: 20 }, (_, i) => ({
            chapterNumber: i + 1,
            title: `Chapter ${i + 1}`,
            content: 'Loaded on demand'
          }));
        })
      };

      // Load initial context
      const context = await mockBookContextLoader.loadProjectContext('test-project');
      
      // Verify lazy references
      expect(context.storyStructure._lazy).toBe(true);
      expect(context.characters._lazy).toBe(true);
      expect(context.completedChapters._lazy).toBe(true);
      
      // Simulate on-demand loading
      await lazyLoaders.loadStoryStructure();
      await lazyLoaders.loadCharacters();
      
      // Verify only requested data was loaded
      expect(lazyLoadCalls).toEqual(['storyStructure', 'characters']);
      expect(lazyLoadCalls).not.toContain('chapters'); // Not loaded yet
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should clean up unused references during orchestration', async () => {
      const referenceTracker = new Map<string, WeakRef<any>>();
      const cleanupLog: string[] = [];

      // Mock reference tracking
      const trackReference = (id: string, obj: any) => {
        referenceTracker.set(id, new WeakRef(obj));
      };

      const cleanupUnusedReferences = () => {
        for (const [id, ref] of referenceTracker.entries()) {
          if (ref.deref() === undefined) {
            referenceTracker.delete(id);
            cleanupLog.push(`Cleaned up: ${id}`);
          }
        }
      };

      // Create objects that will be garbage collected
      let tempContext: any = {
        id: 'temp-context-1',
        largeData: new Array(10000).fill('data')
      };
      trackReference('temp-context-1', tempContext);
      
      // Create persistent object
      const persistentContext = {
        id: 'persistent-context',
        data: 'important'
      };
      trackReference('persistent-context', persistentContext);
      
      // Clear temporary reference
      tempContext = null;
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      // Clean up unused references
      setTimeout(() => {
        cleanupUnusedReferences();
        
        // Verify cleanup
        expect(referenceTracker.has('temp-context-1')).toBe(false);
        expect(referenceTracker.has('persistent-context')).toBe(true);
        expect(cleanupLog).toContain('Cleaned up: temp-context-1');
      }, 100);
    });

    it('should implement memory pooling for frequently created objects', async () => {
      class ObjectPool<T> {
        private pool: T[] = [];
        private createFn: () => T;
        private resetFn: (obj: T) => void;
        private maxSize: number;
        
        constructor(createFn: () => T, resetFn: (obj: T) => void, maxSize = 10) {
          this.createFn = createFn;
          this.resetFn = resetFn;
          this.maxSize = maxSize;
        }
        
        acquire(): T {
          if (this.pool.length > 0) {
            return this.pool.pop()!;
          }
          return this.createFn();
        }
        
        release(obj: T): void {
          if (this.pool.length < this.maxSize) {
            this.resetFn(obj);
            this.pool.push(obj);
          }
        }
        
        getPoolSize(): number {
          return this.pool.length;
        }
      }

      // Create pool for chapter generation contexts
      const chapterContextPool = new ObjectPool(
        () => ({
          chapterNumber: 0,
          content: '',
          scenes: [],
          metadata: {}
        }),
        (obj) => {
          obj.chapterNumber = 0;
          obj.content = '';
          obj.scenes = [];
          obj.metadata = {};
        },
        5
      );

      const allocations: string[] = [];
      const reuses: string[] = [];

      // Simulate multiple chapter generations
      for (let i = 0; i < 10; i++) {
        const context = chapterContextPool.acquire();
        
        if (chapterContextPool.getPoolSize() < 5) {
          allocations.push(`Chapter ${i}: new allocation`);
        } else {
          reuses.push(`Chapter ${i}: reused from pool`);
        }
        
        // Use context
        context.chapterNumber = i;
        context.content = `Chapter ${i} content`;
        
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 10));
        
        // Return to pool
        chapterContextPool.release(context);
      }

      // Verify pooling efficiency
      expect(allocations.length).toBeLessThanOrEqual(5); // Max pool size
      expect(reuses.length).toBeGreaterThan(0); // Some objects were reused
      expect(chapterContextPool.getPoolSize()).toBeGreaterThan(0); // Pool has objects
    });
  });

  describe('Streaming and Chunked Processing', () => {
    it('should process large content in chunks to manage memory', async () => {
      const chunkSize = 1000; // words per chunk
      const processedChunks: any[] = [];

      const processChunk = async (chunk: string, chunkIndex: number) => {
        processedChunks.push({
          index: chunkIndex,
          size: chunk.length,
          wordCount: chunk.split(' ').length,
          processed: true
        });
        
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 10));
        
        return {
          chunkIndex,
          processedContent: `Processed: ${chunk.substring(0, 50)}...`
        };
      };

      // Generate large content
      const largeContent = Array.from({ length: 10000 }, (_, i) => `word${i}`).join(' ');
      const words = largeContent.split(' ');
      
      // Process in chunks
      const results = [];
      for (let i = 0; i < words.length; i += chunkSize) {
        const chunk = words.slice(i, i + chunkSize).join(' ');
        const result = await processChunk(chunk, Math.floor(i / chunkSize));
        results.push(result);
      }

      // Verify chunked processing
      expect(processedChunks.length).toBe(Math.ceil(words.length / chunkSize));
      expect(results.length).toBe(processedChunks.length);
      
      // Verify all chunks were processed
      let totalWords = 0;
      for (const chunk of processedChunks) {
        totalWords += chunk.wordCount;
      }
      expect(totalWords).toBe(words.length);
    });

    it('should implement streaming for real-time content generation', async () => {
      const streamEvents: any[] = [];
      
      // Mock streaming content generator
      const generateContentStream = async function* (prompt: string) {
        const sentences = [
          'The hero stood at the edge of the cliff.',
          'Below, the vast ocean stretched endlessly.',
          'A decision had to be made.',
          'The fate of the world hung in the balance.'
        ];
        
        for (const sentence of sentences) {
          // Simulate generation delay
          await new Promise(resolve => setTimeout(resolve, 50));
          
          yield {
            type: 'content',
            data: sentence,
            timestamp: Date.now()
          };
        }
        
        yield {
          type: 'complete',
          data: sentences.join(' '),
          timestamp: Date.now()
        };
      };

      // Consume stream
      const startTime = Date.now();
      let accumulatedContent = '';
      
      for await (const event of generateContentStream('Generate chapter opening')) {
        streamEvents.push(event);
        
        if (event.type === 'content') {
          accumulatedContent += event.data + ' ';
        }
      }
      
      const totalTime = Date.now() - startTime;

      // Verify streaming behavior
      expect(streamEvents.length).toBe(5); // 4 content + 1 complete
      expect(streamEvents[0].type).toBe('content');
      expect(streamEvents[streamEvents.length - 1].type).toBe('complete');
      
      // Verify content was streamed over time (not all at once)
      expect(totalTime).toBeGreaterThan(150); // At least 50ms * 3 gaps
      
      // Verify timestamps show progressive delivery
      for (let i = 1; i < streamEvents.length; i++) {
        expect(streamEvents[i].timestamp).toBeGreaterThan(streamEvents[i - 1].timestamp);
      }
    });
  });
});