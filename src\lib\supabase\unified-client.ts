import { createBrowserClient, createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/db/types'
import { logger } from '@/lib/services/logger'

// Centralized environment variable access
function getSupabaseConfig() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    // Check if we're in demo mode
    if (typeof window !== 'undefined' && (!supabaseUrl && !supabaseAnonKey)) {
      logger.warn('🚧 Demo Mode: Using mock Supabase client')
      return {
        url: 'https://demo.supabase.co',
        anon<PERSON>ey: 'demo_anon_key',
        isDemoMode: true
      }
    }
    throw new Error('Missing Supabase environment variables')
  }

  return {
    url: supabaseUrl,
    anonKey: supabase<PERSON><PERSON><PERSON>ey,
    isDemoMode: false
  }
}

// Browser client with typed database and optimal configuration
export function createTypedBrowserClient() {
  const config = getSupabaseConfig()
  
  return createBrowserClient<Database>(config.url, config.anonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10, // Rate limiting
      },
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'x-application-name': 'bookscribe',
      },
    },
  })
}

// Server client with typed database and cookie handling
export async function createTypedServerClient() {
  const config = getSupabaseConfig()
  const cookieStore = await cookies()

  return createServerClient<Database>(
    config.url,
    config.anonKey,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}

// Singleton browser client to avoid recreation
let browserClient: ReturnType<typeof createTypedBrowserClient> | null = null

export function getBrowserClient() {
  if (!browserClient && typeof window !== 'undefined') {
    browserClient = createTypedBrowserClient()
  }
  return browserClient
}

// Legacy compatibility exports
export const createClient = createTypedBrowserClient
export const createBrowserSupabaseClient = createTypedBrowserClient
export const createServerSupabaseClient = createTypedServerClient