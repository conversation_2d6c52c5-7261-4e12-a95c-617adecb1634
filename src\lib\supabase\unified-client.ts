import { createBrowserClient, createServerClient } from '@supabase/ssr'
import type { Database } from '@/lib/db/types'
import { config } from '@/lib/config'

// Browser client with typed database and optimal configuration
export function createTypedBrowserClient() {
  return createBrowserClient<Database>(config.supabase.url, config.supabase.anonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10, // Rate limiting
      },
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'x-application-name': 'bookscribe',
      },
    },
  })
}

// Server client with typed database and cookie handling
export async function createTypedServerClient(options?: {
  cookies?: {
    getAll(): { name: string; value: string }[]
    setAll(
      cookiesToSet: {
        name: string
        value: string
        options: any
      }[]
    ): void
  }
}) {
  let cookieOptions = options?.cookies

  if (!cookieOptions) {
    const { cookies } = await import('next/headers')
    const cookieStore = await cookies()

    cookieOptions = {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          )
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    }
  }

  return createServerClient<Database>(
    config.supabase.url,
    config.supabase.anonKey,
    {
      cookies: cookieOptions,
    }
  )
}

// Singleton browser client to avoid recreation
let browserClient: ReturnType<typeof createTypedBrowserClient> | null = null

export function getBrowserClient() {
  if (!browserClient && typeof window !== 'undefined') {
    browserClient = createTypedBrowserClient()
  }
  return browserClient
}

// Legacy compatibility exports
export const createClient = createTypedBrowserClient
export const createBrowserSupabaseClient = createTypedBrowserClient
export const createServerSupabaseClient = createTypedServerClient
