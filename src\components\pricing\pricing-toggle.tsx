'use client'

import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'

interface PricingToggleProps {
  isAnnual: boolean
  onToggle: (isAnnual: boolean) => void
}

export function PricingToggle({ isAnnual, onToggle }: PricingToggleProps) {
  return (
    <div className="flex items-center justify-center gap-4 sm:gap-5 lg:gap-6 mb-8">
      <Label 
        htmlFor="billing-toggle" 
        className={`text-sm font-medium transition-colors ${
          !isAnnual ? 'text-foreground' : 'text-muted-foreground'
        }`}
      >
        Monthly
      </Label>
      <Switch
        id="billing-toggle"
        checked={isAnnual}
        onCheckedChange={onToggle}
        className="data-[state=checked]:bg-primary"
      />
      <Label 
        htmlFor="billing-toggle" 
        className={`text-sm font-medium transition-colors ${
          isAnnual ? 'text-foreground' : 'text-muted-foreground'
        }`}
      >
        Annual
        <span className="ml-2 text-xs text-primary font-mono">Save 20%</span>
      </Label>
    </div>
  )
}