import { createClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { celebrations } from '@/components/celebrations/progress-celebration';
import { TIME_MS } from '@/lib/constants/time'
import { SIZE_LIMITS } from '@/lib/constants'

interface WritingSession {
  id?: string;
  user_id: string;
  project_id: string;
  chapter_id?: string;
  word_count: number;
  duration: number; // in seconds
  started_at: string;
  ended_at: string;
}

interface MilestoneEvent {
  type: 'word_count' | 'streak' | 'chapter' | 'quality' | 'goal' | 'achievement';
  trigger: ReturnType<typeof celebrations[keyof typeof celebrations]>;
}

interface ActiveSession {
  userId: string;
  projectId: string;
  chapterId?: string;
  startedAt: Date;
  lastUpdate: Date;
  initialWordCount: number;
  currentWordCount: number;
  sessionId?: string;
}

export class WritingSessionTracker {
  private static instance: WritingSessionTracker;
  private activeSessions: Map<string, ActiveSession> = new Map();
  private updateInterval: NodeJS.Timeout | null = null;
  private supabase = createClient();
  private isInitialized = false;
  private milestoneCallbacks: ((event: MilestoneEvent) => void)[] = [];

  private constructor() {
    // Don't start interval in constructor - use lazy initialization
  }

  static getInstance(): WritingSessionTracker {
    if (!WritingSessionTracker.instance) {
      WritingSessionTracker.instance = new WritingSessionTracker();
    }
    return WritingSessionTracker.instance;
  }

  /**
   * Initialize the tracker (starts the update interval)
   */
  private initialize(): void {
    if (this.isInitialized) return;
    
    // Update sessions every 30 seconds
    this.updateInterval = setInterval(() => {
      this.updateAllSessions();
    }, 30000);
    
    this.isInitialized = true;
    
    // Ensure cleanup on process exit
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => this.destroy());
    }
  }

  /**
   * Start a new writing session
   */
  async startSession(
    userId: string,
    projectId: string,
    chapterId?: string,
    initialWordCount: number = 0
  ): Promise<string> {
    // Ensure the tracker is initialized
    this.initialize();
    
    const sessionKey = this.getSessionKey(userId, projectId, chapterId);
    
    // End any existing session for this key
    if (this.activeSessions.has(sessionKey)) {
      await this.endSession(userId, projectId, chapterId);
    }

    const session: ActiveSession = {
      userId,
      projectId,
      chapterId,
      startedAt: new Date(),
      lastUpdate: new Date(),
      initialWordCount,
      currentWordCount: initialWordCount
    };

    this.activeSessions.set(sessionKey, session);
    logger.info('Writing session started', { userId, projectId, chapterId });
    
    return sessionKey;
  }

  /**
   * Update word count for an active session
   */
  updateWordCount(
    userId: string,
    projectId: string,
    currentWordCount: number,
    chapterId?: string
  ): void {
    const sessionKey = this.getSessionKey(userId, projectId, chapterId);
    const session = this.activeSessions.get(sessionKey);

    if (session) {
      session.currentWordCount = currentWordCount;
      session.lastUpdate = new Date();
    }
  }

  /**
   * End a writing session and save to database
   */
  async endSession(
    userId: string,
    projectId: string,
    chapterId?: string
  ): Promise<void> {
    const sessionKey = this.getSessionKey(userId, projectId, chapterId);
    const session = this.activeSessions.get(sessionKey);

    if (!session) {
      logger.warn('No active session found to end', { userId, projectId, chapterId });
      return;
    }

    await this.saveSession(session);
    this.activeSessions.delete(sessionKey);
    logger.info('Writing session ended', { userId, projectId, chapterId });
  }

  /**
   * Get statistics for the current session
   */
  getSessionStats(
    userId: string,
    projectId: string,
    chapterId?: string
  ): {
    duration: number;
    wordsWritten: number;
    wordsPerMinute: number;
  } | null {
    const sessionKey = this.getSessionKey(userId, projectId, chapterId);
    const session = this.activeSessions.get(sessionKey);

    if (!session) {
      return null;
    }

    const duration = Math.floor((session.lastUpdate.getTime() - session.startedAt.getTime()) / TIME_MS.SECOND);
    const wordsWritten = Math.max(0, session.currentWordCount - session.initialWordCount);
    const wordsPerMinute = duration > 0 ? Math.round((wordsWritten / duration) * 60) : 0;

    return {
      duration,
      wordsWritten,
      wordsPerMinute
    };
  }

  /**
   * Save session to database
   */
  private async saveSession(session: ActiveSession): Promise<void> {
    try {
      const duration = Math.floor((session.lastUpdate.getTime() - session.startedAt.getTime()) / TIME_MS.SECOND);
      const wordsWritten = Math.max(0, session.currentWordCount - session.initialWordCount);

      // Only save sessions with actual writing activity
      if (duration < 60 || wordsWritten === 0) {
        logger.info('Skipping session save - too short or no words written', { duration, wordsWritten });
        return;
      }

      const sessionData: Omit<WritingSession, 'id'> = {
        user_id: session.userId,
        project_id: session.projectId,
        chapter_id: session.chapterId,
        word_count: wordsWritten,
        duration,
        started_at: session.startedAt.toISOString(),
        ended_at: session.lastUpdate.toISOString()
      };

      const { data, error } = await this.supabase
        .from('writing_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        logger.error('Failed to save writing session', error);
        throw error;
      }

      // Update project word count
      if (wordsWritten > 0) {
        await this.updateProjectWordCount(session.projectId, wordsWritten);
      }

      // Update user analytics
      await this.updateUserAnalytics(session.userId, wordsWritten, duration);

      // Check for milestones
      await this.checkMilestones(session.userId, session.projectId, wordsWritten);

      logger.info('Writing session saved', { sessionId: data.id, wordsWritten, duration });
    } catch (error) {
      logger.error('Error saving writing session', error);
    }
  }

  /**
   * Update project total word count
   */
  private async updateProjectWordCount(projectId: string, wordsAdded: number): Promise<void> {
    try {
      const { data: project, error: fetchError } = await this.supabase
        .from('projects')
        .select('current_word_count')
        .eq('id', projectId)
        .single();

      if (fetchError) {
        logger.error('Failed to fetch project for word count update', fetchError);
        return;
      }

      const newWordCount = (project?.current_word_count || 0) + wordsAdded;

      const { error: updateError } = await this.supabase
        .from('projects')
        .update({ 
          current_word_count: newWordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);

      if (updateError) {
        logger.error('Failed to update project word count', updateError);
      }
    } catch (error) {
      logger.error('Error updating project word count', error);
    }
  }

  /**
   * Update user analytics data
   */
  private async updateUserAnalytics(userId: string, wordsWritten: number, duration: number): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Check if analytics record exists for today
      const { data: existing, error: fetchError } = await this.supabase
        .from('user_analytics')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows found
        logger.error('Failed to fetch user analytics', fetchError);
        return;
      }

      if (existing) {
        // Update existing record
        const { error: updateError } = await this.supabase
          .from('user_analytics')
          .update({
            words_written: existing.words_written + wordsWritten,
            time_spent: existing.time_spent + duration,
            sessions_count: existing.sessions_count + 1,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('date', today);

        if (updateError) {
          logger.error('Failed to update user analytics', updateError);
        }
      } else {
        // Create new record
        const { error: insertError } = await this.supabase
          .from('user_analytics')
          .insert({
            user_id: userId,
            date: today,
            words_written: wordsWritten,
            time_spent: duration,
            sessions_count: 1
          });

        if (insertError) {
          logger.error('Failed to insert user analytics', insertError);
        }
      }
    } catch (error) {
      logger.error('Error updating user analytics', error);
    }
  }

  /**
   * Update all active sessions (called periodically)
   */
  private async updateAllSessions(): Promise<void> {
    for (const [key, session] of this.activeSessions) {
      const timeSinceLastUpdate = Date.now() - session.lastUpdate.getTime();
      
      // If no update in 5 minutes, consider session inactive and save it
      if (timeSinceLastUpdate > 5 * 60 * TIME_MS.SECOND) {
        await this.saveSession(session);
        this.activeSessions.delete(key);
        logger.info('Auto-ended inactive session', { key });
      }
    }
  }

  /**
   * Check for milestones after a writing session
   */
  private async checkMilestones(userId: string, projectId: string, wordsWritten: number): Promise<void> {
    try {
      // Get total words for the project
      const { data: project } = await this.supabase
        .from('projects')
        .select('current_word_count')
        .eq('id', projectId)
        .single();

      const totalWords = project?.current_word_count || 0;

      // Check word count milestones
      if (totalWords >= 100 && totalWords - wordsWritten < 100) {
        this.emitMilestone({
          type: 'word_count',
          trigger: celebrations.firstWords()
        });
      }

      // Check for thousand-word milestones
      const milestones = [TIME_MS.SECOND, TIME_MS.TOAST_DURATION, 10000, 25000, SIZE_LIMITS.MAX_DOCUMENT_CHARS, SIZE_LIMITS.LARGE_DOCUMENT_THRESHOLD];
      for (const milestone of milestones) {
        if (totalWords >= milestone && totalWords - wordsWritten < milestone) {
          this.emitMilestone({
            type: 'word_count',
            trigger: celebrations.thousandWords(milestone)
          });
          break;
        }
      }

      // Check writing streak
      const { data: analytics } = await this.supabase
        .from('user_analytics')
        .select('date')
        .eq('user_id', userId)
        .order('date', { ascending: false })
        .limit(30);

      if (analytics && analytics.length > 0) {
        let streak = 1;
        const today = new Date();
        
        for (let i = 1; i < analytics.length; i++) {
          const currentDate = new Date(analytics[i].date);
          const prevDate = new Date(analytics[i - 1].date);
          const dayDiff = Math.floor((prevDate.getTime() - currentDate.getTime()) / (TIME_MS.SECOND * 60 * 60 * 24));
          
          if (dayDiff === 1) {
            streak++;
          } else {
            break;
          }
        }

        // Check streak milestones
        const streakMilestones = [3, 7, 14, 30];
        for (const milestone of streakMilestones) {
          if (streak === milestone) {
            this.emitMilestone({
              type: 'streak',
              trigger: celebrations.streakMilestone(milestone)
            });
            break;
          }
        }
      }

      // Check and unlock achievements
      const { data: newAchievements } = await this.supabase
        .rpc('check_and_unlock_achievements', { p_user_id: userId });

      if (newAchievements && newAchievements[0]?.newly_unlocked?.length > 0) {
        const { data: unlockedDetails } = await this.supabase
          .from('user_achievements')
          .select('*')
          .eq('user_id', userId)
          .in('achievement_id', newAchievements[0].newly_unlocked);

        if (unlockedDetails && unlockedDetails.length > 0) {
          for (const achievement of unlockedDetails) {
            this.emitMilestone({
              type: 'achievement',
              trigger: celebrations.achievementUnlocked(achievement.title, achievement.tier)
            });
          }
        }
      }
    } catch (error) {
      logger.error('Error checking milestones', error);
    }
  }

  /**
   * Register a callback for milestone events
   */
  onMilestone(callback: (event: MilestoneEvent) => void): () => void {
    this.milestoneCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.milestoneCallbacks.indexOf(callback);
      if (index > -1) {
        this.milestoneCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Emit a milestone event to all registered callbacks
   */
  private emitMilestone(event: MilestoneEvent): void {
    this.milestoneCallbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        logger.error('Error in milestone callback', error);
      }
    });
  }

  /**
   * Generate session key for map storage
   */
  private getSessionKey(userId: string, projectId: string, chapterId?: string): string {
    return `${userId}:${projectId}:${chapterId || 'no-chapter'}`;
  }

  /**
   * Cleanup on destroy
   */
  destroy(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.isInitialized = false;
    
    // Save all active sessions before destroying
    Promise.all(
      Array.from(this.activeSessions.values()).map(session => this.saveSession(session))
    ).then(() => {
      this.activeSessions.clear();
    }).catch(error => {
      logger.error('Error saving sessions during destroy', error);
    });
  }
  
  /**
   * Reset the singleton instance (mainly for testing)
   */
  static resetInstance(): void {
    if (WritingSessionTracker.instance) {
      WritingSessionTracker.instance.destroy();
      WritingSessionTracker.instance = null as unknown as WritingSessionTracker;
    }
  }
}

// Export singleton instance
export const writingSessionTracker = WritingSessionTracker.getInstance();