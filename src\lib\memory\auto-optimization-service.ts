import { logger } from '@/lib/services/logger'
import { getMemoryManager } from './memory-instances'
import { createServerClient } from '@/lib/supabase'
import type { CompressionStrategy, OptimizationResult } from './types'

interface AutoOptimizationConfig {
  enabled: boolean
  threshold: number // Percentage (0-100)
  strategy: CompressionStrategy
  checkInterval: number // Minutes
  preserveRecentHours: number
  targetReduction: number // Percentage (0-100)
  notifyUser: boolean
}

interface OptimizationMetrics {
  tokensBeforeOptimization: number
  tokensAfterOptimization: number
  compressionRatio: number
  itemsCompressed: number
  timeTaken: number
}

export class AutoOptimizationService {
  private static instance: AutoOptimizationService
  private optimizationInProgress = new Map<string, boolean>()
  private lastOptimizationTime = new Map<string, number>()
  private config: Map<string, AutoOptimizationConfig> = new Map()

  private constructor() {}

  static getInstance(): AutoOptimizationService {
    if (!AutoOptimizationService.instance) {
      AutoOptimizationService.instance = new AutoOptimizationService()
    }
    return AutoOptimizationService.instance
  }

  /**
   * Load optimization config for a project
   */
  async loadConfig(projectId: string): Promise<AutoOptimizationConfig> {
    try {
      const supabase = await createServerClient()
      
      const { data: settings } = await supabase
        .from('memory_settings')
        .select('auto_optimization')
        .eq('project_id', projectId)
        .single()

      const config: AutoOptimizationConfig = {
        enabled: settings?.auto_optimization?.enabled ?? true,
        threshold: settings?.auto_optimization?.threshold ?? 80,
        strategy: settings?.auto_optimization?.strategy ?? 'balanced',
        checkInterval: settings?.auto_optimization?.checkInterval ?? 10,
        preserveRecentHours: settings?.auto_optimization?.preserveRecentHours ?? 24,
        targetReduction: settings?.auto_optimization?.targetReduction ?? 30,
        notifyUser: settings?.auto_optimization?.notifyUser ?? true
      }

      this.config.set(projectId, config)
      return config
    } catch (error) {
      logger.error('Failed to load auto-optimization config:', error)
      // Return default config
      return {
        enabled: true,
        threshold: 80,
        strategy: 'balanced',
        checkInterval: 10,
        preserveRecentHours: 24,
        targetReduction: 30,
        notifyUser: true
      }
    }
  }

  /**
   * Check if optimization is needed and trigger if necessary
   */
  async checkAndOptimize(projectId: string): Promise<OptimizationResult | null> {
    try {
      // Check if optimization is already in progress
      if (this.optimizationInProgress.get(projectId)) {
        logger.info('Optimization already in progress for project', { projectId })
        return null
      }

      // Load config
      const config = await this.loadConfig(projectId)
      if (!config.enabled) {
        return null
      }

      // Check if enough time has passed since last optimization
      const lastTime = this.lastOptimizationTime.get(projectId) || 0
      const now = Date.now()
      if (now - lastTime < config.checkInterval * 60 * 1000) {
        return null
      }

      // Get memory stats
      const memoryManager = getMemoryManager(projectId)
      const stats = await memoryManager.getMemoryStats()
      
      const usagePercentage = (stats.totalTokensUsed / stats.tokenLimit) * 100

      // Check if optimization is needed
      if (usagePercentage < config.threshold) {
        return null
      }

      logger.info('Auto-optimization triggered', {
        projectId,
        usagePercentage,
        threshold: config.threshold
      })

      // Mark as in progress
      this.optimizationInProgress.set(projectId, true)

      // Perform optimization
      const result = await this.performOptimization(projectId, config)

      // Update last optimization time
      this.lastOptimizationTime.set(projectId, now)

      // Log optimization event
      await this.logOptimizationEvent(projectId, result, config)

      // Notify user if configured
      if (config.notifyUser && result.success) {
        await this.notifyUser(projectId, result)
      }

      return result
    } catch (error) {
      logger.error('Auto-optimization failed:', error)
      return null
    } finally {
      this.optimizationInProgress.set(projectId, false)
    }
  }

  /**
   * Perform the actual optimization
   */
  private async performOptimization(
    projectId: string, 
    config: AutoOptimizationConfig
  ): Promise<OptimizationResult> {
    const startTime = Date.now()
    const memoryManager = getMemoryManager(projectId)
    
    // Get initial stats
    const statsBefore = await memoryManager.getMemoryStats()

    try {
      // Compress old chapters
      const chapterResult = await memoryManager.compressChapters({
        strategy: config.strategy,
        preserveRecentHours: config.preserveRecentHours,
        targetReduction: config.targetReduction / 100
      })

      // Merge similar contexts
      const mergeResult = await memoryManager.mergeContexts({
        similarityThreshold: config.strategy === 'aggressive' ? 0.85 : 0.95,
        preserveRecentHours: config.preserveRecentHours
      })

      // Clear old cache entries
      await memoryManager.clearContextCache({
        olderThan: config.preserveRecentHours * 60 * 60 * 1000
      })

      // Get final stats
      const statsAfter = await memoryManager.getMemoryStats()

      const metrics: OptimizationMetrics = {
        tokensBeforeOptimization: statsBefore.totalTokensUsed,
        tokensAfterOptimization: statsAfter.totalTokensUsed,
        compressionRatio: 1 - (statsAfter.totalTokensUsed / statsBefore.totalTokensUsed),
        itemsCompressed: (chapterResult.chaptersCompressed || 0) + (mergeResult.contextsRemoved || 0),
        timeTaken: Date.now() - startTime
      }

      return {
        success: true,
        tokensSaved: statsBefore.totalTokensUsed - statsAfter.totalTokensUsed,
        compressionRatio: metrics.compressionRatio,
        message: `Optimized memory usage by ${Math.round(metrics.compressionRatio * 100)}%`,
        details: {
          chaptersCompressed: chapterResult.chaptersCompressed || 0,
          contextsM

: mergeResult.contextsMerged || 0,
          cacheCleared: true,
          metrics
        }
      }
    } catch (error) {
      logger.error('Optimization failed:', error)
      return {
        success: false,
        tokensSaved: 0,
        compressionRatio: 0,
        message: 'Optimization failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Log optimization event to database
   */
  private async logOptimizationEvent(
    projectId: string,
    result: OptimizationResult,
    config: AutoOptimizationConfig
  ): Promise<void> {
    try {
      const supabase = await createServerClient()
      
      await supabase.from('memory_optimization_logs').insert({
        project_id: projectId,
        optimization_type: 'auto',
        strategy: config.strategy,
        tokens_before: result.details?.metrics?.tokensBeforeOptimization || 0,
        tokens_after: result.details?.metrics?.tokensAfterOptimization || 0,
        tokens_saved: result.tokensSaved,
        compression_ratio: result.compressionRatio,
        success: result.success,
        error: result.error,
        metadata: {
          config,
          metrics: result.details?.metrics,
          trigger: 'auto_threshold'
        }
      })
    } catch (error) {
      logger.error('Failed to log optimization event:', error)
    }
  }

  /**
   * Notify user about optimization
   */
  private async notifyUser(projectId: string, result: OptimizationResult): Promise<void> {
    try {
      const supabase = await createServerClient()
      
      // Get project details
      const { data: project } = await supabase
        .from('projects')
        .select('title, user_id')
        .eq('id', projectId)
        .single()

      if (!project) return

      // Create notification
      await supabase.from('notifications').insert({
        user_id: project.user_id,
        type: 'memory_optimized',
        title: 'Memory Optimized',
        message: `Automatically optimized memory for "${project.title}". Saved ${Math.round(result.tokensSaved / 1000)}k tokens (${Math.round(result.compressionRatio * 100)}% reduction).`,
        data: {
          projectId,
          tokensSaved: result.tokensSaved,
          compressionRatio: result.compressionRatio
        }
      })
    } catch (error) {
      logger.error('Failed to notify user:', error)
    }
  }

  /**
   * Update optimization settings
   */
  async updateSettings(projectId: string, settings: Partial<AutoOptimizationConfig>): Promise<void> {
    try {
      const supabase = await createServerClient()
      
      // Get current settings
      const { data: current } = await supabase
        .from('memory_settings')
        .select('auto_optimization')
        .eq('project_id', projectId)
        .single()

      const updatedSettings = {
        ...current?.auto_optimization,
        ...settings
      }

      // Update in database
      await supabase
        .from('memory_settings')
        .upsert({
          project_id: projectId,
          auto_optimization: updatedSettings,
          updated_at: new Date().toISOString()
        })

      // Update in cache
      const config = this.config.get(projectId) || await this.loadConfig(projectId)
      this.config.set(projectId, { ...config, ...settings })

      logger.info('Auto-optimization settings updated', { projectId, settings })
    } catch (error) {
      logger.error('Failed to update auto-optimization settings:', error)
      throw error
    }
  }

  /**
   * Get optimization history for a project
   */
  async getOptimizationHistory(projectId: string, limit = 10): Promise<any[]> {
    try {
      const supabase = await createServerClient()
      
      const { data, error } = await supabase
        .from('memory_optimization_logs')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error

      return data || []
    } catch (error) {
      logger.error('Failed to get optimization history:', error)
      return []
    }
  }

  /**
   * Manually trigger optimization
   */
  async triggerManualOptimization(
    projectId: string, 
    options?: Partial<AutoOptimizationConfig>
  ): Promise<OptimizationResult> {
    try {
      const config = await this.loadConfig(projectId)
      const mergedConfig = { ...config, ...options }

      // Mark as in progress
      this.optimizationInProgress.set(projectId, true)

      // Perform optimization
      const result = await this.performOptimization(projectId, mergedConfig)

      // Log event
      await this.logOptimizationEvent(projectId, result, mergedConfig)

      return result
    } finally {
      this.optimizationInProgress.set(projectId, false)
    }
  }

  /**
   * Get optimization status
   */
  getOptimizationStatus(projectId: string): {
    inProgress: boolean
    lastOptimization: number | null
    config: AutoOptimizationConfig | null
  } {
    return {
      inProgress: this.optimizationInProgress.get(projectId) || false,
      lastOptimization: this.lastOptimizationTime.get(projectId) || null,
      config: this.config.get(projectId) || null
    }
  }
}

// Export singleton instance
export const autoOptimizationService = AutoOptimizationService.getInstance()