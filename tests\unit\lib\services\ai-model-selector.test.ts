import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { aiModelSelector, TASK_TYPES } from '@/lib/services/ai-model-selector';
import { getUserTier, getAIModelForTask } from '@/lib/subscription';
import { AI_MODELS } from '@/lib/config/ai-settings';

// Mock dependencies
jest.mock('@/lib/subscription');
jest.mock('@/lib/services/logger');

describe('AIModelSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('selectModel', () => {
    it('should select appropriate model for free tier users', async () => {
      (getUserTier as jest.Mock).mockResolvedValue('free');
      (getAIModelForTask as jest.Mock).mockReturnValue(AI_MODELS.gpt4oMini.id);

      const result = await aiModelSelector.selectModel('user-123', TASK_TYPES.SUGGESTIONS);

      expect(result).toEqual({
        model: AI_MODELS.gpt4oMini.id,
        isRestricted: true,
        tierName: 'free',
      });
    });

    it('should select premium model for premium users', async () => {
      (getUserTier as jest.Mock).mockResolvedValue('premium');
      (getAIModelForTask as jest.Mock).mockReturnValue(AI_MODELS.gpt41.id);

      const result = await aiModelSelector.selectModel('user-123', TASK_TYPES.STORY_STRUCTURE);

      expect(result).toEqual({
        model: AI_MODELS.gpt41.id,
        isRestricted: false,
        tierName: 'premium',
      });
    });

    it('should handle standard tier correctly', async () => {
      (getUserTier as jest.Mock).mockResolvedValue('standard');
      (getAIModelForTask as jest.Mock).mockReturnValue(AI_MODELS.gpt41Mini.id);

      const result = await aiModelSelector.selectModel('user-123', TASK_TYPES.CHAPTER_PLANNING);

      expect(result).toEqual({
        model: AI_MODELS.gpt41Mini.id,
        isRestricted: false,
        tierName: 'standard',
      });
    });

    it('should provide reason for restrictions', async () => {
      (getUserTier as jest.Mock).mockResolvedValue('free');
      (getAIModelForTask as jest.Mock).mockReturnValue(AI_MODELS.gpt4oMini.id);

      const result = await aiModelSelector.selectModel(
        'user-123',
        TASK_TYPES.CREATIVE_WRITING,
        { includeReason: true }
      );

      expect(result.reason).toBeDefined();
      expect(result.reason).toContain('Free tier');
    });

    it('should handle errors gracefully', async () => {
      (getUserTier as jest.Mock).mockRejectedValue(new Error('Database error'));

      const result = await aiModelSelector.selectModel('user-123', TASK_TYPES.ANALYSIS);

      expect(result.model).toBe(AI_MODELS.gpt4oMini.id);
      expect(result.isRestricted).toBe(true);
      expect(result.tierName).toBe('free');
    });
  });

  describe('getModelForTaskType', () => {
    it('should return correct model for each task type', () => {
      const complexTasks = [
        TASK_TYPES.STORY_STRUCTURE,
        TASK_TYPES.CHARACTER_DEVELOPMENT,
        TASK_TYPES.CHAPTER_WRITING,
        TASK_TYPES.CREATIVE_WRITING,
        TASK_TYPES.DIALOGUE_GENERATION,
      ];

      complexTasks.forEach(task => {
        const model = aiModelSelector.getModelForTaskType(task, 'premium');
        expect(model).toBe(AI_MODELS.gpt41.id);
      });

      const mediumTasks = [
        TASK_TYPES.CHAPTER_PLANNING,
        TASK_TYPES.EDITING,
        TASK_TYPES.CONTENT_GENERATION,
        TASK_TYPES.ANALYSIS,
      ];

      mediumTasks.forEach(task => {
        const model = aiModelSelector.getModelForTaskType(task, 'standard');
        expect(model).toBe(AI_MODELS.gpt41Mini.id);
      });

      const simpleTasks = [
        TASK_TYPES.ADAPTIVE_PLANNING,
        TASK_TYPES.SUGGESTIONS,
        TASK_TYPES.SUMMARIZATION,
        TASK_TYPES.METADATA,
        TASK_TYPES.SIMPLE_QUERY,
      ];

      simpleTasks.forEach(task => {
        const model = aiModelSelector.getModelForTaskType(task, 'free');
        expect(model).toBe(AI_MODELS.gpt4oMini.id);
      });
    });

    it('should respect tier restrictions', () => {
      // Free tier should only get basic model
      const model = aiModelSelector.getModelForTaskType(TASK_TYPES.STORY_STRUCTURE, 'free');
      expect(model).toBe(AI_MODELS.gpt4oMini.id);

      // Standard tier should get mid-tier model for complex tasks
      const standardModel = aiModelSelector.getModelForTaskType(TASK_TYPES.STORY_STRUCTURE, 'standard');
      expect(standardModel).toBe(AI_MODELS.gpt41Mini.id);

      // Premium tier should get best model
      const premiumModel = aiModelSelector.getModelForTaskType(TASK_TYPES.STORY_STRUCTURE, 'premium');
      expect(premiumModel).toBe(AI_MODELS.gpt41.id);
    });
  });

  describe('getTokenLimit', () => {
    it('should return correct token limits for each model', () => {
      expect(aiModelSelector.getTokenLimit(AI_MODELS.gpt41.id)).toBe(128000);
      expect(aiModelSelector.getTokenLimit(AI_MODELS.gpt41Mini.id)).toBe(128000);
      expect(aiModelSelector.getTokenLimit(AI_MODELS.gpt4oMini.id)).toBe(128000);
      expect(aiModelSelector.getTokenLimit('unknown-model')).toBe(8000); // Default
    });
  });

  describe('estimateTokenUsage', () => {
    it('should estimate tokens for different task types', () => {
      const largePrompt = 'a'.repeat(10000);
      
      // Complex tasks should have higher estimates
      const complexEstimate = aiModelSelector.estimateTokenUsage(
        largePrompt,
        TASK_TYPES.STORY_STRUCTURE
      );
      expect(complexEstimate).toBeGreaterThan(2500);

      // Simple tasks should have lower estimates
      const simpleEstimate = aiModelSelector.estimateTokenUsage(
        'Short prompt',
        TASK_TYPES.SIMPLE_QUERY
      );
      expect(simpleEstimate).toBeLessThan(1000);
    });

    it('should account for response multipliers', () => {
      const prompt = 'Test prompt';
      
      const creativeEstimate = aiModelSelector.estimateTokenUsage(
        prompt,
        TASK_TYPES.CREATIVE_WRITING
      );
      
      const summaryEstimate = aiModelSelector.estimateTokenUsage(
        prompt,
        TASK_TYPES.SUMMARIZATION
      );
      
      expect(creativeEstimate).toBeGreaterThan(summaryEstimate);
    });
  });

  describe('isTaskAllowedForTier', () => {
    it('should check if tasks are allowed for tiers', () => {
      // Free tier restrictions
      expect(aiModelSelector.isTaskAllowedForTier(TASK_TYPES.STORY_STRUCTURE, 'free')).toBe(false);
      expect(aiModelSelector.isTaskAllowedForTier(TASK_TYPES.SIMPLE_QUERY, 'free')).toBe(true);

      // Standard tier allowances
      expect(aiModelSelector.isTaskAllowedForTier(TASK_TYPES.CHAPTER_PLANNING, 'standard')).toBe(true);
      expect(aiModelSelector.isTaskAllowedForTier(TASK_TYPES.CREATIVE_WRITING, 'standard')).toBe(true);

      // Premium tier should allow everything
      Object.values(TASK_TYPES).forEach(task => {
        expect(aiModelSelector.isTaskAllowedForTier(task, 'premium')).toBe(true);
      });
    });
  });

  describe('getRecommendedTasksForTier', () => {
    it('should return appropriate tasks for each tier', () => {
      const freeTasks = aiModelSelector.getRecommendedTasksForTier('free');
      expect(freeTasks).toContain(TASK_TYPES.SIMPLE_QUERY);
      expect(freeTasks).toContain(TASK_TYPES.SUGGESTIONS);
      expect(freeTasks).not.toContain(TASK_TYPES.STORY_STRUCTURE);

      const standardTasks = aiModelSelector.getRecommendedTasksForTier('standard');
      expect(standardTasks).toContain(TASK_TYPES.CHAPTER_PLANNING);
      expect(standardTasks).toContain(TASK_TYPES.EDITING);

      const premiumTasks = aiModelSelector.getRecommendedTasksForTier('premium');
      expect(premiumTasks.length).toBeGreaterThan(standardTasks.length);
      expect(premiumTasks).toContain(TASK_TYPES.STORY_STRUCTURE);
    });
  });
});