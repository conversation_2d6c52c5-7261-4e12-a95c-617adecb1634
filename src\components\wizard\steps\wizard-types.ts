import { ComponentType } from 'react'

export type WizardMode = 'live' | 'demo'
export type WizardDisplay = 'page' | 'modal' | 'guided'

export interface WizardStep {
  id: string
  title: string
  description: string
  icon: ComponentType<{ className?: string }>
}

export interface FormData {
  // Basic information
  title: string
  description: string
  
  // Genre and style
  genre: string
  subgenre: string
  tone: string
  targetAudience: string
  
  // Story structure
  structure: string
  pacing: string
  
  // Characters and world
  protagonist: string
  antagonist: string
  setting: string
  worldBuildingDepth: string
  timePeriod: string
  
  // Themes and content
  themes: string
  contentWarnings: string
  
  // Technical details
  narrativeVoice: string
  tense: string
  wordCount: string
  chapters: string
  
  // Series/Universe connections
  isStandalone: string
  seriesId: string
  universeId: string
  
  // Name for protagonist
  protagonistName: string
}

export interface StepComponentProps {
  formData: FormData
  updateFormData: (key: string, value: string | string[]) => void
  mode: WizardMode
}