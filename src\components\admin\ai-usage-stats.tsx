'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { Activity, Brain, DollarSign, Zap, BarChart } from 'lucide-react'
import { LoadingSkeleton } from '@/components/ui/loading-skeleton'
import { EmptyState } from '@/components/ui/empty-state'
import { TIME_MS } from '@/lib/constants'

interface ModelUsage {
  model: string
  requests: number
  wordsGenerated: number
  estimatedCost: number
  averageWordsPerRequest: number
}

interface AIUsageMetrics {
  totalRequests: number
  totalWordsGenerated: number
  totalEstimatedCost: number
  modelBreakdown: ModelUsage[]
  topUsers: Array<{
    userId: string
    email: string
    requests: number
    wordsGenerated: number
  }>
}

export function AIUsageStats() {
  const [metrics, setMetrics] = useState<AIUsageMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('month')
  const { toast } = useToast()
  const supabase = createClient()

  useEffect(() => {
    loadAIUsageMetrics()
  }, [timeRange])

  const loadAIUsageMetrics = async () => {
    try {
      // Calculate date range
      const startDate = new Date()
      switch (timeRange) {
        case 'day':
          startDate.setDate(startDate.getDate() - 1)
          break
        case 'week':
          startDate.setDate(startDate.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1)
          break
      }

      // Load AI usage logs
      const { data: usageLogs, error } = await supabase
        .from('ai_usage_logs')
        .select(`
          model_used,
          words_generated,
          cost_estimate,
          user_id,
          users!inner(email)
        `)
        .gte('timestamp', startDate.toISOString())

      if (error) throw error

      // Process model breakdown
      const modelStats: Record<string, ModelUsage> = {}
      const userStats: Record<string, { email: string; requests: number; words: number }> = {}

      let totalRequests = 0
      let totalWords = 0
      let totalCost = 0

      usageLogs?.forEach(log => {
        // Model stats
        if (!modelStats[log.model_used]) {
          modelStats[log.model_used] = {
            model: log.model_used,
            requests: 0,
            wordsGenerated: 0,
            estimatedCost: 0,
            averageWordsPerRequest: 0
          }
        }
        
        modelStats[log.model_used].requests++
        modelStats[log.model_used].wordsGenerated += log.words_generated || 0
        modelStats[log.model_used].estimatedCost += log.cost_estimate || 0

        // User stats
        if (!userStats[log.user_id]) {
          userStats[log.user_id] = {
            email: log.users?.email || 'Unknown',
            requests: 0,
            words: 0
          }
        }
        
        userStats[log.user_id].requests++
        userStats[log.user_id].words += log.words_generated || 0

        // Totals
        totalRequests++
        totalWords += log.words_generated || 0
        totalCost += log.cost_estimate || 0
      })

      // Calculate averages
      Object.values(modelStats).forEach(model => {
        model.averageWordsPerRequest = model.requests > 0 
          ? Math.round(model.wordsGenerated / model.requests)
          : 0
      })

      // Get top users
      const topUsers = Object.entries(userStats)
        .map(([userId, stats]) => ({
          userId,
          email: stats.email,
          requests: stats.requests,
          wordsGenerated: stats.words
        }))
        .sort((a, b) => b.wordsGenerated - a.wordsGenerated)
        .slice(0, 10)

      setMetrics({
        totalRequests,
        totalWordsGenerated: totalWords,
        totalEstimatedCost: totalCost,
        modelBreakdown: Object.values(modelStats),
        topUsers
      })
    } catch (error) {
      logger.error('Failed to load AI usage metrics:', error)
      toast({
        title: 'Error',
        description: 'Failed to load AI usage metrics',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <LoadingSkeleton type="stats-grid" count={4} />
  }

  if (!metrics) {
    return (
      <EmptyState
        icon={BarChart}
        title="No AI usage data available"
        description="AI usage metrics will appear here once AI features are used."
      />
    )
  }

  const formatCost = (cost: number) => {
    return cost < 0.01 ? '<$0.01' : `$${cost.toFixed(2)}`
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex gap-2">
        {(['day', 'week', 'month'] as const).map((range) => (
          <Badge
            key={range}
            variant={timeRange === range ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setTimeRange(range)}
          >
            Last {range}
          </Badge>
        ))}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">AI API calls</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Words Generated</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(metrics.totalWordsGenerated / TIME_MS.SECOND).toFixed(1)}k</div>
            <p className="text-xs text-muted-foreground">Total AI output</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCost(metrics.totalEstimatedCost)}</div>
            <p className="text-xs text-muted-foreground">API usage cost</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Words/Request</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.totalRequests > 0 
                ? Math.round(metrics.totalWordsGenerated / metrics.totalRequests)
                : 0}
            </div>
            <p className="text-xs text-muted-foreground">Generation size</p>
          </CardContent>
        </Card>
      </div>

      {/* Model Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>AI Model Usage</CardTitle>
          <CardDescription>Breakdown by model type</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Model</TableHead>
                <TableHead className="text-right">Requests</TableHead>
                <TableHead className="text-right">Words Generated</TableHead>
                <TableHead className="text-right">Avg Words/Request</TableHead>
                <TableHead className="text-right">Est. Cost</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {metrics.modelBreakdown.map((model) => (
                <TableRow key={model.model}>
                  <TableCell>
                    <Badge variant="outline">{model.model}</Badge>
                  </TableCell>
                  <TableCell className="text-right">{model.requests.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{(model.wordsGenerated / TIME_MS.SECOND).toFixed(1)}k</TableCell>
                  <TableCell className="text-right">{model.averageWordsPerRequest}</TableCell>
                  <TableCell className="text-right">{formatCost(model.estimatedCost)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Top Users */}
      <Card>
        <CardHeader>
          <CardTitle>Top AI Users</CardTitle>
          <CardDescription>Highest usage by word count</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.topUsers.map((user, index) => {
              const usagePercentage = (user.wordsGenerated / metrics.totalWordsGenerated) * 100
              return (
                <div key={user.userId} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">#{index + 1}</span>
                      <span className="text-sm">{user.email}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(user.wordsGenerated / TIME_MS.SECOND).toFixed(1)}k words ({user.requests} requests)
                    </div>
                  </div>
                  <Progress value={usagePercentage} className="h-2" />
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}