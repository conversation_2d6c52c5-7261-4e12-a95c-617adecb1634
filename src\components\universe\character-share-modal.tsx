'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import {
  Users,
  ArrowRight,
  Info,
  BookOpen,
  Link2,
  Copy,
  UserPlus,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface Series {
  id: string
  title: string
  description?: string
  universe_id?: string
}

interface CharacterShareModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  character: {
    id: string
    name: string
    role: string
    description?: string
  }
  currentSeriesId: string
  currentSeriesTitle: string
  userId: string
  onShareComplete?: () => void
}

export function CharacterShareModal({
  open,
  onOpenChange,
  character,
  currentSeriesId,
  currentSeriesTitle,
  userId,
  onShareComplete
}: CharacterShareModalProps) {
  const [availableSeries, setAvailableSeries] = useState<Series[]>([])
  const [targetSeriesId, setTargetSeriesId] = useState('')
  const [shareType, setShareType] = useState<'reference' | 'guest' | 'permanent'>('reference')
  const [versionNotes, setVersionNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (open) {
      loadAvailableSeries()
    }
  }, [open])

  const loadAvailableSeries = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/series')
      if (!response.ok) throw new Error('Failed to load series')
      
      const data = await response.json()
      // Filter out the current series
      const filtered = (data.series || []).filter(
        (s: Series) => s.id !== currentSeriesId
      )
      setAvailableSeries(filtered)
    } catch (error) {
      logger.error('Error loading series:', error)
      toast({
        title: "Error",
        description: "Failed to load available series",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const shareCharacter = async () => {
    if (!targetSeriesId) {
      toast({
        title: "Error",
        description: "Please select a target series",
        variant: "destructive"
      })
      return
    }

    setIsSharing(true)
    try {
      const response = await fetch(`/api/characters/${character.id}/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sourceSeriesId: currentSeriesId,
          targetSeriesId,
          shareType,
          versionNotes: versionNotes.trim() || undefined
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to share character')
      }
      
      const data = await response.json()
      
      toast({
        title: "Success",
        description: `${character.name} has been shared to the selected series`,
      })
      
      onShareComplete?.()
      onOpenChange(false)
      
      // Reset form
      setTargetSeriesId('')
      setShareType('reference')
      setVersionNotes('')
    } catch (error) {
      logger.error('Error sharing character:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to share character",
        variant: "destructive"
      })
    } finally {
      setIsSharing(false)
    }
  }

  const targetSeries = availableSeries.find(s => s.id === targetSeriesId)
  const isSameUniverse = targetSeries?.universe_id && 
    availableSeries.find(s => s.id === currentSeriesId)?.universe_id === targetSeries.universe_id

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl lg:max-w-3xl xl:max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Share Character: {character.name}
          </DialogTitle>
          <DialogDescription>
            Share this character to another series for crossover appearances
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 mt-4">
          {/* Character Info */}
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-start gap-4 sm:gap-5 lg:gap-6">
              <div className="flex-1">
                <h4 className="font-semibold">{character.name}</h4>
                <p className="text-sm text-muted-foreground">{character.role}</p>
                {character.description && (
                  <p className="text-sm mt-1">{character.description}</p>
                )}
              </div>
              <Badge variant="outline">
                <BookOpen className="h-3 w-3 mr-1" />
                {currentSeriesTitle}
              </Badge>
            </div>
          </div>

          {/* Target Series Selection */}
          <div className="space-y-2">
            <Label htmlFor="target-series">Target Series</Label>
            <Select
              value={targetSeriesId}
              onValueChange={setTargetSeriesId}
              disabled={isLoading}
            >
              <SelectTrigger id="target-series">
                <SelectValue placeholder={
                  isLoading ? "Loading series..." : "Select a series to share to"
                } />
              </SelectTrigger>
              <SelectContent>
                {availableSeries.map((series) => (
                  <SelectItem key={series.id} value={series.id}>
                    <div className="flex items-center gap-2">
                      {series.title}
                      {series.universe_id && (
                        <Badge variant="outline" className="text-xs">
                          Shared Universe
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Share Type */}
          <div className="space-y-3">
            <Label>Share Type</Label>
            <RadioGroup value={shareType} onValueChange={(value) => setShareType(value as typeof shareType)}>
              <div className="space-y-2">
                <div className="flex items-start gap-3 p-3 rounded border hover:bg-muted/50">
                  <RadioGroupItem value="reference" id="reference" />
                  <div className="flex-1">
                    <Label htmlFor="reference" className="cursor-pointer">
                      <div className="font-medium flex items-center gap-2">
                        <Link2 className="h-4 w-4" />
                        Reference
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        Character can be mentioned or referenced but doesn't actively appear
                      </p>
                    </Label>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded border hover:bg-muted/50">
                  <RadioGroupItem value="guest" id="guest" />
                  <div className="flex-1">
                    <Label htmlFor="guest" className="cursor-pointer">
                      <div className="font-medium flex items-center gap-2">
                        <UserPlus className="h-4 w-4" />
                        Guest Appearance
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        Character makes limited appearances while maintaining their original series as home
                      </p>
                    </Label>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded border hover:bg-muted/50">
                  <RadioGroupItem value="permanent" id="permanent" />
                  <div className="flex-1">
                    <Label htmlFor="permanent" className="cursor-pointer">
                      <div className="font-medium flex items-center gap-2">
                        <Copy className="h-4 w-4" />
                        Permanent Transfer
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        Character becomes a regular in the target series (can still appear in original)
                      </p>
                    </Label>
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Version Notes */}
          <div className="space-y-2">
            <Label htmlFor="version-notes">Version Notes (Optional)</Label>
            <Textarea
              id="version-notes"
              value={versionNotes}
              onChange={(e) => setVersionNotes(e.target.value)}
              placeholder="Any notes about this character's state or changes when appearing in the target series..."
              rows={3}
            />
          </div>

          {/* Universe Info */}
          {targetSeriesId && isSameUniverse && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Both series are in the same universe, making character sharing seamless!
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={shareCharacter} 
              disabled={!targetSeriesId || isSharing}
            >
              {isSharing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sharing...
                </>
              ) : (
                <>
                  <ArrowRight className="h-4 w-4 mr-2" />
                  Share Character
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}