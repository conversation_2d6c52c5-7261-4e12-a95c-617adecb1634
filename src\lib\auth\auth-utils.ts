// Test compatibility module - redirects to UnifiedAuthService
// This file exists for backward compatibility with tests that import from @/lib/auth/auth-utils

export { UnifiedAuthService } from './unified-auth-service'
export type { AuthenticatedRequest, AuthUser } from './unified-auth-service'

// Re-export the authenticateUser function for test compatibility
export async function authenticateUser(request: Request): Promise<AuthUser | null> {
  try {
    const authService = await UnifiedAuthService.initialize()
    const result = await authService.validateRequest(request)
    return result.success ? result.user : null
  } catch {
    return null
  }
}

// Re-export helper functions that tests might expect
export function getAuthToken(request: Request): string | null {
  const authHeader = request.headers.get('authorization')
  if (!authHeader) return null
  
  const match = authHeader.match(/^Bearer (.+)$/)
  return match ? match[1] : null
}

export function createAuthResponse(error: string, status: number = 401) {
  return new Response(JSON.stringify({ error }), {
    status,
    headers: { 'Content-Type': 'application/json' }
  })
}