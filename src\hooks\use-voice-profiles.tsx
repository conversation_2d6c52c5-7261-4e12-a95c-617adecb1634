'use client'

import { useState, useEffect, useCallback } from 'react'
import { logger } from '@/lib/services/logger'

interface VoiceProfile {
  id: string
  name: string
  description?: string
  type: 'author' | 'character' | 'narrator'
  confidence: number
  training_samples_count: number
  total_words_analyzed: number
  is_global: boolean
  project_id?: string
  series_id?: string
  character_id?: string
  created_at: string
  updated_at: string
}

interface UseVoiceProfilesOptions {
  projectId?: string
  seriesId?: string
  type?: 'author' | 'character' | 'narrator'
  isGlobal?: boolean
  autoLoad?: boolean
}

interface UseVoiceProfilesReturn {
  profiles: VoiceProfile[]
  loading: boolean
  error: string | null
  loadProfiles: () => Promise<void>
  createProfile: (data: CreateProfileData) => Promise<VoiceProfile | null>
  updateProfile: (id: string, data: Partial<CreateProfileData>) => Promise<VoiceProfile | null>
  deleteProfile: (id: string) => Promise<boolean>
  trainProfile: (id: string, texts: string[], source?: string) => Promise<boolean>
}

interface CreateProfileData {
  name: string
  description?: string
  type: 'author' | 'character' | 'narrator'
  projectId?: string
  seriesId?: string
  characterId?: string
  isGlobal?: boolean
}

export function useVoiceProfiles(options: UseVoiceProfilesOptions = {}): UseVoiceProfilesReturn {
  const [profiles, setProfiles] = useState<VoiceProfile[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadProfiles = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams()
      
      if (options.projectId) params.append('projectId', options.projectId)
      if (options.seriesId) params.append('seriesId', options.seriesId)
      if (options.type) params.append('type', options.type)
      if (options.isGlobal !== undefined) params.append('isGlobal', options.isGlobal.toString())
      
      const response = await fetch(`/api/voice-profiles?${params}`)
      if (!response.ok) {
        throw new Error('Failed to load voice profiles')
      }
      
      const data = await response.json()
      setProfiles(data.profiles || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load voice profiles'
      setError(errorMessage)
      logger.error('Error loading voice profiles:', err)
    } finally {
      setLoading(false)
    }
  }, [options.projectId, options.seriesId, options.type, options.isGlobal])

  const createProfile = useCallback(async (data: CreateProfileData): Promise<VoiceProfile | null> => {
    try {
      const response = await fetch('/api/voice-profiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        throw new Error('Failed to create voice profile')
      }
      
      const result = await response.json()
      const newProfile = result.profile
      
      // Update local state
      setProfiles(prev => [newProfile, ...prev])
      
      return newProfile
    } catch (err) {
      logger.error('Error creating voice profile:', err)
      setError(err instanceof Error ? err.message : 'Failed to create voice profile')
      return null
    }
  }, [])

  const updateProfile = useCallback(async (id: string, data: Partial<CreateProfileData>): Promise<VoiceProfile | null> => {
    try {
      const response = await fetch(`/api/voice-profiles/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        throw new Error('Failed to update voice profile')
      }
      
      const result = await response.json()
      const updatedProfile = result.profile
      
      // Update local state
      setProfiles(prev => prev.map(p => p.id === id ? updatedProfile : p))
      
      return updatedProfile
    } catch (err) {
      logger.error('Error updating voice profile:', err)
      setError(err instanceof Error ? err.message : 'Failed to update voice profile')
      return null
    }
  }, [])

  const deleteProfile = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/voice-profiles/${id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete voice profile')
      }
      
      // Update local state
      setProfiles(prev => prev.filter(p => p.id !== id))
      
      return true
    } catch (err) {
      logger.error('Error deleting voice profile:', err)
      setError(err instanceof Error ? err.message : 'Failed to delete voice profile')
      return false
    }
  }, [])

  const trainProfile = useCallback(async (id: string, texts: string[], source = 'manual_entry'): Promise<boolean> => {
    try {
      const response = await fetch(`/api/voice-profiles/${id}/train`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ texts, source })
      })
      
      if (!response.ok) {
        throw new Error('Failed to train voice profile')
      }
      
      const result = await response.json()
      const updatedProfile = result.profile
      
      // Update local state
      setProfiles(prev => prev.map(p => p.id === id ? updatedProfile : p))
      
      return true
    } catch (err) {
      logger.error('Error training voice profile:', err)
      setError(err instanceof Error ? err.message : 'Failed to train voice profile')
      return false
    }
  }, [])

  // Auto-load profiles on mount if enabled
  useEffect(() => {
    if (options.autoLoad !== false) {
      loadProfiles()
    }
  }, [loadProfiles, options.autoLoad])

  return {
    profiles,
    loading,
    error,
    loadProfiles,
    createProfile,
    updateProfile,
    deleteProfile,
    trainProfile
  }
}

// Helper hook for voice consistency checking
export function useVoiceConsistency() {
  const [checking, setChecking] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const checkConsistency = useCallback(async (
    content: string,
    profileId: string,
    projectId?: string,
    chapterId?: string
  ) => {
    setChecking(true)
    setError(null)
    
    try {
      const response = await fetch('/api/analysis/voice-consistency', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          profileId,
          projectId,
          chapterId
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to check voice consistency')
      }
      
      return await response.json()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check voice consistency'
      setError(errorMessage)
      logger.error('Error checking voice consistency:', err)
      return null
    } finally {
      setChecking(false)
    }
  }, [])

  return {
    checking,
    error,
    checkConsistency
  }
}