'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  <PERSON>Chart, 
  Bar, 
  LineChart,
  Line,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { 
  Zap, 
  Clock, 
  Target, 
  TrendingUp,
  Brain,
  Activity
} from 'lucide-react'
import { TIME_MS } from '@/lib/constants'

interface AgentMetric {
  agent: string
  tasksCompleted: number
  averageTime: number
  successRate: number
  qualityScore: number
}

interface AgentPerformanceMetricsProps {
  projectId: string
}

export function AgentPerformanceMetrics({ projectId }: AgentPerformanceMetricsProps) {
  const [metrics, setMetrics] = useState<AgentMetric[]>([])
  const [timeSeriesData, setTimeSeriesData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate fetching performance data
    setTimeout(() => {
      setMetrics([
        {
          agent: 'Story Architect',
          tasksCompleted: 45,
          averageTime: 125, // seconds
          successRate: 92,
          qualityScore: 88
        },
        {
          agent: 'Character Developer',
          tasksCompleted: 38,
          averageTime: 210,
          successRate: 95,
          qualityScore: 91
        },
        {
          agent: 'Chapter Planner',
          tasksCompleted: 52,
          averageTime: 180,
          successRate: 89,
          qualityScore: 85
        },
        {
          agent: 'Writing Agent',
          tasksCompleted: 67,
          averageTime: 300,
          successRate: 87,
          qualityScore: 82
        },
        {
          agent: 'Editor Agent',
          tasksCompleted: 41,
          averageTime: 90,
          successRate: 94,
          qualityScore: 90
        }
      ])

      setTimeSeriesData([
        { time: '00:00', tasks: 5, quality: 85 },
        { time: '04:00', tasks: 3, quality: 82 },
        { time: '08:00', tasks: 12, quality: 88 },
        { time: '12:00', tasks: 15, quality: 90 },
        { time: '16:00', tasks: 18, quality: 87 },
        { time: '20:00', tasks: 10, quality: 86 }
      ])

      setLoading(false)
    }, TIME_MS.SECOND)
  }, [projectId])

  const pieData = metrics.map(m => ({
    name: m.agent,
    value: m.tasksCompleted
  }))

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  if (loading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardContent className="py-12">
            <div className="text-center text-muted-foreground">
              Loading performance metrics...
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const totalTasks = metrics.reduce((sum, m) => sum + m.tasksCompleted, 0)
  const avgSuccessRate = Math.round(
    metrics.reduce((sum, m) => sum + m.successRate, 0) / metrics.length
  )
  const avgQuality = Math.round(
    metrics.reduce((sum, m) => sum + m.qualityScore, 0) / metrics.length
  )

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Total Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTasks}</div>
            <p className="text-xs text-muted-foreground">Completed today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Target className="h-4 w-4" />
              Success Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgSuccessRate}%</div>
            <Progress value={avgSuccessRate} className="h-2 mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Quality Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgQuality}%</div>
            <Progress value={avgQuality} className="h-2 mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Efficiency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">High</div>
            <p className="text-xs text-muted-foreground">Performance optimal</p>
          </CardContent>
        </Card>
      </div>

      {/* Agent Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Individual Agent Performance</CardTitle>
          <CardDescription>
            Detailed metrics for each AI agent
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.map((metric) => (
              <div key={metric.agent} className="space-y-2 p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Brain className="h-5 w-5 text-primary" />
                    <h4 className="font-semibold">{metric.agent}</h4>
                  </div>
                  <Badge variant="outline">
                    {metric.tasksCompleted} tasks
                  </Badge>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Avg Time</p>
                    <p className="font-medium flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {Math.round(metric.averageTime / 60)}m {metric.averageTime % 60}s
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Success Rate</p>
                    <p className="font-medium">{metric.successRate}%</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Quality Score</p>
                    <p className="font-medium">{metric.qualityScore}%</p>
                  </div>
                </div>
                
                <Progress value={metric.qualityScore} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Task Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Task Distribution</CardTitle>
            <CardDescription>
              Tasks completed by each agent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Timeline</CardTitle>
            <CardDescription>
              Task completion and quality over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="tasks" 
                  stroke="#8884d8" 
                  name="Tasks"
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="quality" 
                  stroke="#82ca9d" 
                  name="Quality %"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}