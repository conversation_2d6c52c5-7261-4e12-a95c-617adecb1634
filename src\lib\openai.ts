import OpenAI from 'openai'
import { logger } from '@/lib/services/logger';
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'

import { config } from '@/lib/config'
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '@/lib/services/circuit-breaker'
import { AI_MODELS } from '@/lib/config/ai-settings'

// Check if OpenAI API key is properly configured
const apiKey = config.openai.apiKey
const isValidApiKey = apiKey && !apiKey.includes('your_openai_api_key') && apiKey.startsWith('sk-')

if (!isValidApiKey) {
  const errorMsg = 'OpenAI API key not configured or invalid. AI features will not work without a valid API key. Get your API key from: https://platform.openai.com/api-keys'
  logger.error('⚠️  ' + errorMsg);
  
  // In production, we should fail fast rather than use a dummy key
  if (config.isProduction) {
    throw new Error(errorMsg);
  }
}

// Only create OpenAI client if we have a valid key (for backward compatibility)
export const openai = isValidApiKey ? new OpenAI({
  apiKey: apiKey,
}) : null as unknown as OpenAI

// Export the enhanced Vercel AI client as the primary client
export const aiClient = vercelAIClient

export const DEFAULT_MODEL = AI_MODELS.PRIMARY
export const FAST_MODEL = AI_MODELS.FAST
export const EMBEDDING_MODEL = AI_MODELS.EMBEDDING
export const EMBEDDING_DIMENSIONS = 1536

export interface OpenAIConfig {
  model: string
  temperature: number
  maxTokens: number
}

export const defaultConfig: OpenAIConfig = {
  model: DEFAULT_MODEL,
  temperature: 0.7,
  maxTokens: 4000
}

// Embedding utility functions
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    if (!openai) {
      throw new Error('OpenAI client not initialized. Please configure a valid API key.')
    }
    
    if (!text || text.trim().length === 0) {
      throw new Error('Text content cannot be empty')
    }

    // Clean and truncate text if necessary (OpenAI has token limits)
    const cleanText = text.trim().substring(0, 8000) // Conservative limit

    const response = await withCircuitBreaker(
      'openai-embeddings',
      () => openai.embeddings.create({
        model: EMBEDDING_MODEL,
        input: cleanText,
        dimensions: EMBEDDING_DIMENSIONS,
      }),
      CIRCUIT_BREAKER_PRESETS.OPENAI
    )

    if (!response.data || response.data.length === 0) {
      throw new Error('No embedding data received from OpenAI')
    }

    const embedding = response.data[0]?.embedding
    if (!embedding) {
      throw new Error('No embedding data in response')
    }
    return embedding
  } catch (error) {
    logger.error('Error generating embedding:', error);
    throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export async function generateEmbeddings(texts: string[]): Promise<number[][]> {
  try {
    if (!openai) {
      throw new Error('OpenAI client not initialized. Please configure a valid API key.')
    }
    
    if (!texts || texts.length === 0) {
      throw new Error('Texts array cannot be empty')
    }

    // Filter out empty texts and clean them
    const cleanTexts = texts
      .filter(text => text && text.trim().length > 0)
      .map(text => text.trim().substring(0, 8000))

    if (cleanTexts.length === 0) {
      throw new Error('No valid texts provided after cleaning')
    }

    const response = await openai.embeddings.create({
      model: EMBEDDING_MODEL,
      input: cleanTexts,
      dimensions: EMBEDDING_DIMENSIONS,
    })

    if (!response.data || response.data.length === 0) {
      throw new Error('No embedding data received from OpenAI')
    }

    return response.data.map(item => item.embedding)
  } catch (error) {
    logger.error('Error generating embeddings:', error);
    throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}