'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { EmptyState } from '@/components/ui/empty-state'
import { useCachedData, projectListCache, cache<PERSON>eys, Cache } from '@/lib/cache/client'
import { createClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { RefreshCw } from 'lucide-react'
import { LoadingSkeleton } from '@/components/ui/loading-skeleton'

interface Project {
  id: string
  name: string
  description: string
  genre: string
  status: string
  progress: {
    wordsWritten: number
    targetWords: number
    chaptersComplete: number
    targetChapters: number
    percentComplete: number
  }
  metadata: {
    createdAt: Date
    updatedAt: Date
    targetAudience: string
    contentRating: string
    estimatedReadTime: number
  }
  [key: string]: unknown
}

interface ProjectsListProps {
  userId: string
}

export function ProjectsList({ userId }: ProjectsListProps) {
  const supabase = createClient()
  
  const fetcher = async () => {
    const response = await fetch('/api/projects')
    if (!response.ok) {
      throw new Error('Failed to fetch projects')
    }
    const data = await response.json()
    return data.projects as Project[]
  }
  
  const { data: projects, loading, error, refresh } = useCachedData<Project[]>({
    cacheKey: cacheKeys.projectList(userId),
    cache: projectListCache as Cache<Project[]>,
    fetcher,
    staleTime: 60000, // Consider data stale after 1 minute
  })

  // Subscribe to real-time updates
  useEffect(() => {
    const subscription = supabase
      .channel('projects-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'projects',
          filter: `user_id=eq.${userId}`,
        },
        () => {
          // Refresh the cache when changes occur
          refresh()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [userId, refresh, supabase])

  if (loading && !projects) {
    return <LoadingSkeleton variant="card" count={6} />
  }

  if (error) {
    return (
      <EmptyState
        icon={RefreshCw}
        title="Failed to load projects"
        description="There was an error loading your projects. Please try again."
        actions={[
          { label: 'Try Again', onClick: refresh, variant: 'outline' }
        ]}
        className="border-destructive/20 bg-destructive/5"
      />
    )
  }

  if (!projects || projects.length === 0) {
    return (
      <EmptyState
        icon={BookOpen}
        title="No projects yet"
        description="Start your writing journey by creating your first project."
        actions={[
          { label: 'Browse Templates', href: '/templates', variant: 'outline' },
          { label: 'Try a Sample', href: '/samples', variant: 'outline' },
          { label: 'Create Your First Project', href: '/projects/new' }
        ]}
      />
    )
  }

  return (
    <div className="grid gap-6 md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
      {projects.map((project) => (
        <Link key={project.id} href={`/projects/${project.id}`}>
          <Card className="h-full transition-shadow hover:shadow-lg">
            <CardHeader>
              <CardTitle>{project.name}</CardTitle>
              <CardDescription>
                {project.genre} • {project.progress.targetWords.toLocaleString()} words
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground line-clamp-3">
                {project.description || 'No description yet'}
              </p>
              <div className="mt-4 flex items-center justify-between text-sm">
                <span className="capitalize text-muted-foreground">{project.status}</span>
                <span className="text-muted-foreground">
                  {project.progress.wordsWritten.toLocaleString()} / {project.progress.targetWords.toLocaleString()}
                </span>
              </div>
              <div className="mt-2">
                <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary transition-all"
                    style={{ width: `${Math.min(project.progress.percentComplete, 100)}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}