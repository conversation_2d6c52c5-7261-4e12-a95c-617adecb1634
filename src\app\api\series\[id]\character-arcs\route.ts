import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'

// GET - Fetch all character arcs for a series
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const service = getSeriesService(true)
    const { arcs, error } = await service.getSeriesCharacterArcs(seriesId)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch character arcs' }, { status: 500 })
    }

    return NextResponse.json({ arcs })
  } catch (error) {
    logger.error('Error in character arcs GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create a new character arc
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const service = getSeriesService(true)
    
    const { arc, error } = await service.createCharacterArc({
      series_id: seriesId,
      ...body
    })

    if (error) {
      return NextResponse.json({ error: 'Failed to create character arc' }, { status: 500 })
    }

    return NextResponse.json({ arc }, { status: 201 })
  } catch (error) {
    logger.error('Error in character arcs POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Update a character arc
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { arcId, ...updates } = body
    
    if (!arcId) {
      return NextResponse.json({ error: 'Arc ID is required' }, { status: 400 })
    }

    const service = getSeriesService(true)
    const { arc, error } = await service.updateCharacterArc(arcId, updates)

    if (error) {
      return NextResponse.json({ error: 'Failed to update character arc' }, { status: 500 })
    }

    return NextResponse.json({ arc })
  } catch (error) {
    logger.error('Error in character arcs PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}