import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

// GET - Fetch all character arcs for a series
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const service = getSeriesService(true)
    const { arcs, error } = await service.getSeriesCharacterArcs(seriesId)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch character arcs' }, { status: 500 })
    }

    return NextResponse.json({ arcs })
  } catch (error) {
    logger.error('Error in character arcs GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create a new character arc
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    const service = getSeriesService(true)
    
    const { arc, error } = await service.createCharacterArc({
      series_id: seriesId,
      ...body
    })

    if (error) {
      return NextResponse.json({ error: 'Failed to create character arc' }, { status: 500 })
    }

    return NextResponse.json({ arc }, { status: 201 })
  } catch (error) {
    logger.error('Error in character arcs POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Update a character arc
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    const { arcId, ...updates } = body
    
    if (!arcId) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const service = getSeriesService(true)
    const { arc, error } = await service.updateCharacterArc(arcId, updates)

    if (error) {
      return NextResponse.json({ error: 'Failed to update character arc' }, { status: 500 })
    }

    return NextResponse.json({ arc })
  } catch (error) {
    logger.error('Error in character arcs PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}