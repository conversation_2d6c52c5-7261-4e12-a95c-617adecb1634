'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  BookOpen, 
  PlusCircle, 
  Sparkles,
  FileText,
  Layers,
  ArrowRight
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export function EmptyProjects() {
  const router = useRouter()

  return (
    <div className="max-w-4xl mx-auto py-12">
      <Card className="border-2 border-dashed border-muted-foreground/20">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/10 flex items-center justify-center">
            <BookOpen className="w-8 h-8 text-primary" />
          </div>
          
          <h2 className="text-2xl font-literary-display text-foreground mb-4">
            No Projects Yet
          </h2>
          
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto">
            Start your writing journey by creating your first project. Use AI to generate complete story structures or begin with a blank canvas.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">AI Story Generation</h3>
              <p className="text-sm text-muted-foreground">
                Let AI create your story structure, characters, and plot
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <FileText className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Chapter Planning</h3>
              <p className="text-sm text-muted-foreground">
                Organize your story with detailed chapter outlines
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Layers className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Series Support</h3>
              <p className="text-sm text-muted-foreground">
                Build connected stories in shared universes
              </p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 justify-center">
            <Button 
              size="lg" 
              onClick={() => router.push('/projects/new')}
              className="group"
            >
              <PlusCircle className="w-4 h-4 mr-2" />
              Create New Project
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => router.push('/templates')}
            >
              <FileText className="w-4 h-4 mr-2" />
              Browse Templates
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}