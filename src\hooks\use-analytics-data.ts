import { useState, useEffect } from "react";
import { logger } from '@/lib/services/logger';

import { DateRange } from "react-day-picker";
import { createClient } from '@/lib/supabase';
import type { SupabaseClient } from '@supabase/supabase-js';
import { AnalyticsEngine } from "@/lib/services/analytics-engine";
import { format, startOfDay, endOfDay } from "date-fns";
import { ACHIEVEMENT_MULTIPLIERS } from '@/lib/constants'

interface UseAnalyticsDataProps {
  userId: string;
  projectId?: string;
  dateRange?: DateRange;
}

interface AnalyticsData {
  // Overview metrics
  totalWords: number;
  wordsTrend: { value: number; direction: 'up' | 'down' | 'neutral' };
  currentStreak: number;
  streakTrend: { value: number; direction: 'up' | 'down' | 'neutral' };
  avgDailyWords: number;
  avgWordsTrend: { value: number; direction: 'up' | 'down' | 'neutral' };
  activeProjects: number;
  projectsTrend: { value: number; direction: 'up' | 'down' | 'neutral' };
  
  // Charts data
  dailyWordCount: Array<{ date: string; value: number }>;
  heatmapData: Array<{ date: string; value: number; words: number }>;
  hourlyPattern: Array<{ date: string; value: number }>;
  weeklyPattern: Array<{ date: string; value: number }>;
  
  // Session data
  avgSessionDuration: number;
  avgWordsPerSession: number;
  totalSessions: number;
  
  // Project data
  projectProgress: Array<{ date: string; value: number }>;
  projectLines?: Array<{ dataKey: string; name: string; color: string }>;
  wordsByProject: Array<{ date: string; value: number }>;
  projectQuality: Array<{ metric: string; score: number }>;
  
  // Quality data
  qualityDimensions: Array<{ metric: string; score: number; description: string }>;
  qualityTrends: Array<{ date: string; value: number }>;
  qualityLines?: Array<{ dataKey: string; name: string; color: string }>;
  improvementAreas: Array<{ title: string; description: string }>;
  
  // Goals data
  activeGoals: Array<{
    id: string;
    type: string;
    title: string;
    current: number;
    target: number;
    progress: number;
    unit: string;
    deadline: string;
  }>;
  achievements: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
  goalProgress: Array<{ date: string; value: number }>;
  goals?: {
    active: Array<{
      id: string;
      type: string;
      title: string;
      current: number;
      target: number;
      progress: number;
      unit: string;
      deadline: string;
    }>;
    completed: number;
    completionRate: number;
    completedThisMonth: number;
  };
  
  // Insights
  insights: Array<{
    title: string;
    content: string;
    recommendation?: string;
  }>;
  tips: Array<{
    title: string;
    content: string;
  }>;
}

export function useAnalyticsData({
  userId,
  projectId,
  dateRange,
}: UseAnalyticsDataProps) {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);

        const supabase = createClient();
        const engine = new AnalyticsEngine(supabase);

        // Get date range
        const startDate = dateRange?.from || startOfDay(new Date(Date.now() - 30 * 24 * 60 * 60 * TIME_MS.SECOND));
        const endDate = dateRange?.to || endOfDay(new Date());

        // Fetch analytics data
        const [
          overviewMetrics,
          activityData,
          qualityData,
          goalsData,
        ] = await Promise.all([
          engine.getOverviewMetrics(userId, startDate, endDate, projectId),
          engine.getActivityData(userId, startDate, endDate, projectId),
          engine.getQualityMetrics(userId, projectId),
          engine.getGoalsData(userId, projectId),
        ]);

        // Fetch real data from database
        const { data: writingSessions } = await supabase
          .from('writing_sessions')
          .select('*')
          .eq('user_id', userId)
          .gte('created_at', startDate.toISOString())
          .lte('created_at', endDate.toISOString())
          .order('created_at', { ascending: true });

        const { data: userAnalytics } = await supabase
          .from('user_analytics')
          .select('*')
          .eq('user_id', userId)
          .gte('date', startDate.toISOString())
          .lte('date', endDate.toISOString());

        const { data: projectsData } = await supabase
          .from('projects')
          .select('id, title, current_word_count, target_word_count, status')
          .eq('user_id', userId)
          .eq('status', 'active');

        // Fetch quality metrics for projects
        const { data: qualityMetricsData } = await supabase
          .from('project_quality_metrics')
          .select('*')
          .eq('user_id', userId)
          .in('project_id', projectsData?.map(p => p.id) || []);

        // Fetch all goals
        const { data: goalsDataRaw } = await supabase
          .from('writing_goals')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        // Process real data
        const processedDailyData = processDailyWordCount(writingSessions || [], startDate, endDate);
        const processedHeatmapData = generateHeatmapFromSessions(writingSessions || []);
        const realStreak = calculateWritingStreak(writingSessions || []);
        const realActiveProjects = projectsData?.length || 0;

        // Use real data when available, fallback to mock
        const totalWordsWritten = writingSessions?.reduce((sum, s) => sum + s.word_count, 0) || 0;
        const avgDaily = processedDailyData.length > 0 
          ? Math.round(totalWordsWritten / Math.max(1, processedDailyData.filter(d => d.value > 0).length))
          : 0;

        // Calculate real session statistics
        const totalSessions = writingSessions?.length || 0;
        const avgSessionDuration = totalSessions > 0 
          ? Math.round(writingSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / totalSessions / 60) // Convert to minutes
          : 0;
        const avgWordsPerSession = totalSessions > 0
          ? Math.round(totalWordsWritten / totalSessions)
          : 0;

        // Calculate real hourly and weekly patterns
        const hourlyPattern = calculateHourlyPattern(writingSessions || []);
        const weeklyPattern = calculateWeeklyPattern(writingSessions || []);
        const realProjectProgress = await calculateProjectProgress(supabase, projectsData || [], startDate, endDate);

        // Process project data
        const wordsByProject = projectsData?.map(p => ({
          date: p.title,
          value: p.current_word_count || 0
        })) || [];

        const projectQuality = qualityMetricsData?.[0] ? [
          { metric: "Plot Structure", score: Math.round(qualityMetricsData[0].avg_plot_consistency || 0) },
          { metric: "Character Development", score: Math.round(qualityMetricsData[0].avg_character_consistency || 0) },
          { metric: "Dialogue Quality", score: Math.round(qualityMetricsData[0].avg_dialogue_authenticity || 0) },
          { metric: "Pacing", score: Math.round(qualityMetricsData[0].avg_pacing || 0) },
          { metric: "World Building", score: Math.round(qualityMetricsData[0].avg_creativity || 0) },
        ] : [];

        // Process goals data
        const activeGoals = goalsDataRaw?.filter(g => g.status === 'active').map(goal => {
          const currentValue = goal.current_value || 0;
          const progress = goal.target_value > 0 ? (currentValue / goal.target_value) * 100 : 0;
          
          return {
            id: goal.id,
            type: goal.goal_type,
            title: goal.title,
            current: currentValue,
            target: goal.target_value,
            progress: Math.round(progress),
            unit: goal.unit || 'words',
            deadline: goal.deadline
          };
        }) || [];

        // Generate insights based on real data
        const insights = generateInsights(
          writingSessions || [],
          hourlyPattern,
          weeklyPattern,
          realStreak,
          avgWordsPerSession
        );

        const analyticsData: AnalyticsData = {
          // Overview metrics - use real data when available
          totalWords: totalWordsWritten || overviewMetrics.totalWords || 0,
          wordsTrend: calculateTrend(writingSessions || [], 'words'), 
          currentStreak: realStreak || overviewMetrics.currentStreak || 0,
          streakTrend: calculateStreakTrend(writingSessions || []),
          avgDailyWords: avgDaily || overviewMetrics.avgDailyWords || 0,
          avgWordsTrend: calculateTrend(writingSessions || [], 'avgDaily'),
          activeProjects: realActiveProjects || overviewMetrics.activeProjects || 0,
          projectsTrend: calculateProjectsTrend(projectsData || []),

          // Activity data - use real processed data
          dailyWordCount: processedDailyData,
          heatmapData: processedHeatmapData,
          hourlyPattern: hourlyPattern,
          weeklyPattern: weeklyPattern,

          // Session data - using real calculated values
          avgSessionDuration: avgSessionDuration || 0,
          avgWordsPerSession: avgWordsPerSession || 0,
          totalSessions: totalSessions || 0,

          // Project data - using real data
          projectProgress: realProjectProgress,
          projectLines: projectsData?.slice(0, 3).map((p, i) => ({
            dataKey: `project${i}`,
            name: p.title,
            color: `hsl(var(--${i === 0 ? 'primary' : i === 1 ? 'secondary' : 'accent'}))`
          })) || [],
          wordsByProject: wordsByProject,
          projectQuality: projectQuality,

          // Quality data - using real data when available
          qualityDimensions: qualityMetricsData?.[0] ? [
            { metric: "Readability", score: Math.round(qualityMetricsData[0].avg_readability || 0), description: "Text clarity and ease of reading" },
            { metric: "Consistency", score: Math.round(qualityMetricsData[0].avg_consistency || 0), description: "Character and plot consistency" },
            { metric: "Pacing", score: Math.round(qualityMetricsData[0].avg_pacing || 0), description: "Story rhythm and flow" },
            { metric: "Engagement", score: Math.round(qualityMetricsData[0].avg_engagement || 0), description: "Reader interest and hook strength" },
            { metric: "Dialogue", score: Math.round(qualityMetricsData[0].avg_dialogue_authenticity || 0), description: "Character voice authenticity" },
            { metric: "Description", score: Math.round(qualityMetricsData[0].avg_creativity || 0), description: "Vivid and immersive descriptions" },
          ] : [],
          qualityTrends: await calculateQualityTrends(supabase, userId, startDate, endDate),
          qualityLines: [
            { dataKey: "readability", name: "Readability", color: "hsl(var(--primary))" },
            { dataKey: "consistency", name: "Consistency", color: "hsl(var(--secondary))" },
            { dataKey: "pacing", name: "Pacing", color: "hsl(var(--accent))" },
          ],
          improvementAreas: generateImprovementAreas(qualityMetricsData?.[0]),

          // Goals data - using real data
          activeGoals: activeGoals.length > 0 ? activeGoals : [],
          achievements: generateAchievements(
            totalWordsWritten,
            realStreak,
            qualityMetricsData?.[0]?.overall_score || 0,
            totalSessions
          ),
          goalProgress: await calculateGoalProgress(supabase, activeGoals, startDate, endDate),
          
          // Calculate goals section data
          goals: {
            active: activeGoals,
            completed: goalsDataRaw?.filter(g => g.status === 'completed').length || 0,
            completionRate: goalsDataRaw && goalsDataRaw.length > 0 
              ? Math.round((goalsDataRaw.filter(g => g.status === 'completed').length / goalsDataRaw.length) * 100)
              : 0,
            completedThisMonth: goalsDataRaw?.filter(g => {
              if (g.status !== 'completed' || !g.completed_at) return false
              const completedDate = new Date(g.completed_at)
              const now = new Date()
              return completedDate.getMonth() === now.getMonth() && 
                     completedDate.getFullYear() === now.getFullYear()
            }).length || 0
          },

          // Insights - using real generated insights
          insights: insights.length > 0 ? insights : [
            {
              title: "Start Writing",
              content: "No writing data available yet.",
              recommendation: "Begin your first writing session to see personalized insights.",
            }
          ],
          tips: [
            {
              title: "Try the Pomodoro Technique",
              content: "Based on your session data, 25-minute focused sprints might boost your productivity.",
            },
            {
              title: "Character Voice Exercises",
              content: "Write diary entries from each character's perspective to develop unique voices.",
            },
          ],
        };

        setData(analyticsData);
      } catch (err) {
        logger.error("Error fetching analytics:", err);
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [userId, projectId, dateRange]);

  return { data, loading, error };
}

// Helper functions to process real data
function processDailyWordCount(
  sessions: Array<{ created_at: string; word_count: number }>,
  startDate: Date,
  endDate: Date
): Array<{ date: string; value: number }> {
  const dailyMap = new Map<string, number>();
  
  // Initialize all days with 0
  const current = new Date(startDate);
  while (current <= endDate) {
    dailyMap.set(format(current, "MMM dd"), 0);
    current.setDate(current.getDate() + 1);
  }
  
  // Add actual session data
  sessions.forEach(session => {
    const date = format(new Date(session.created_at), "MMM dd");
    dailyMap.set(date, (dailyMap.get(date) || 0) + session.word_count);
  });
  
  return Array.from(dailyMap.entries())
    .map(([date, value]) => ({ date, value }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}

function generateHeatmapFromSessions(
  sessions: Array<{ created_at: string; word_count: number }>
): Array<{ date: string; value: number; words: number }> {
  const heatmapMap = new Map<string, number>();
  
  sessions.forEach(session => {
    const date = format(new Date(session.created_at), "yyyy-MM-dd");
    heatmapMap.set(date, (heatmapMap.get(date) || 0) + session.word_count);
  });
  
  return Array.from(heatmapMap.entries()).map(([date, words]) => ({
    date,
    value: Math.min(10, Math.floor(words / 200)), // Normalize to 0-10
    words,
  }));
}

function calculateWritingStreak(
  sessions: Array<{ created_at: string }>
): number {
  if (sessions.length === 0) return 0;
  
  const uniqueDays = new Set(
    sessions.map(s => format(new Date(s.created_at), "yyyy-MM-dd"))
  );
  
  const sortedDays = Array.from(uniqueDays).sort().reverse();
  let streak = 0;
  const today = format(new Date(), "yyyy-MM-dd");
  
  for (let i = 0; i < sortedDays.length; i++) {
    const expectedDate = format(
      new Date(Date.now() - i * 24 * 60 * 60 * TIME_MS.SECOND),
      "yyyy-MM-dd"
    );
    
    if (sortedDays[i] === expectedDate || (i === 0 && sortedDays[i] === today)) {
      streak++;
    } else {
      break;
    }
  }
  
  return streak;
}

function calculateHourlyPattern(
  sessions: Array<{ created_at: string; word_count: number }>
): Array<{ date: string; value: number }> {
  const hourlyMap = new Map<number, number>();
  
  // Initialize all hours with 0
  for (let i = 0; i < 24; i++) {
    hourlyMap.set(i, 0);
  }
  
  // Add actual session data
  sessions.forEach(session => {
    const hour = new Date(session.created_at).getHours();
    hourlyMap.set(hour, (hourlyMap.get(hour) || 0) + session.word_count);
  });
  
  return Array.from(hourlyMap.entries())
    .map(([hour, value]) => ({ date: `${hour}:00`, value }))
    .sort((a, b) => parseInt(a.date) - parseInt(b.date));
}

function calculateWeeklyPattern(
  sessions: Array<{ created_at: string; word_count: number }>
): Array<{ date: string; value: number }> {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const weeklyMap = new Map<number, number>();
  
  // Initialize all days with 0
  for (let i = 0; i < 7; i++) {
    weeklyMap.set(i, 0);
  }
  
  // Add actual session data
  sessions.forEach(session => {
    const dayOfWeek = new Date(session.created_at).getDay();
    weeklyMap.set(dayOfWeek, (weeklyMap.get(dayOfWeek) || 0) + session.word_count);
  });
  
  return Array.from(weeklyMap.entries())
    .map(([day, value]) => ({ date: days[day], value }));
}

// These functions were replaced with real database queries above

// Real data calculation functions
async function calculateProjectProgress(
  supabase: SupabaseClient,
  projects: Array<{ id: string; title: string; created_at?: string }>,
  startDate: Date,
  endDate: Date
): Promise<Array<{ date: string; value: number }>> {
  if (projects.length === 0) return [];

  const { data: sessions } = await supabase
    .from('writing_sessions')
    .select('created_at, word_count, project_id')
    .in('project_id', projects.map(p => p.id))
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString())
    .order('created_at');

  const dailyMap = new Map<string, number>();
  const current = new Date(startDate);
  
  // Initialize all days with 0
  while (current <= endDate) {
    dailyMap.set(format(current, "MMM dd"), 0);
    current.setDate(current.getDate() + 1);
  }
  
  // Add session data
  sessions?.forEach(session => {
    const date = format(new Date(session.created_at), "MMM dd");
    dailyMap.set(date, (dailyMap.get(date) || 0) + session.word_count);
  });
  
  return Array.from(dailyMap.entries()).map(([date, value]) => ({ date, value }));
}

async function calculateQualityTrends(
  supabase: SupabaseClient,
  userId: string,
  startDate: Date,
  endDate: Date
): Promise<Array<{ date: string; readability: number; consistency: number; pacing: number }>> {
  const { data: qualityData } = await supabase
    .from('quality_metrics')
    .select('created_at, avg_readability, avg_consistency, avg_pacing')
    .eq('user_id', userId)
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString())
    .order('created_at');

  return qualityData?.map(q => ({
    date: format(new Date(q.created_at), "MMM dd"),
    readability: Math.round(q.avg_readability || 0),
    consistency: Math.round(q.avg_consistency || 0),
    pacing: Math.round(q.avg_pacing || 0),
  })) || [];
}

async function calculateGoalProgress(
  supabase: SupabaseClient,
  goals: Array<{ id: string; current: number; target: number }>,
  startDate: Date,
  endDate: Date
): Promise<Array<{ date: string; value: number }>> {
  if (goals.length === 0) return [];

  const { data: progressData } = await supabase
    .from('writing_goal_progress')
    .select('created_at, current_value')
    .in('goal_id', goals.map(g => g.id))
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString())
    .order('created_at');

  return progressData?.map(p => ({
    date: format(new Date(p.created_at), "MMM dd"),
    value: p.current_value || 0,
  })) || [];
}

interface QualityMetricsData {
  avg_dialogue_authenticity?: number;
  avg_pacing?: number;
  avg_creativity?: number;
  avg_readability?: number;
  avg_consistency?: number;
  avg_engagement?: number;
  overall_score?: number;
}

function generateImprovementAreas(qualityData?: QualityMetricsData): Array<{ title: string; description: string }> {
  if (!qualityData) return [];
  
  const areas = [];
  
  if ((qualityData.avg_dialogue_authenticity || 0) < 70) {
    areas.push({
      title: "Dialogue Enhancement",
      description: "Consider varying speech patterns between characters for better distinction"
    });
  }
  
  if ((qualityData.avg_pacing || 0) < 75) {
    areas.push({
      title: "Pacing Optimization", 
      description: "Focus on balancing action scenes with character development"
    });
  }
  
  if ((qualityData.avg_creativity || 0) < 80) {
    areas.push({
      title: "Show vs Tell Balance",
      description: "Increase scenes that show character emotions through actions"
    });
  }
  
  return areas;
}

function generateAchievements(
  totalWords: number,
  streak: number,
  qualityScore: number,
  totalSessions: number
): Array<{ icon: string; title: string; description: string }> {
  const achievements = [];
  
  // Word count achievements
  if (totalWords >= SIZE_LIMITS.LARGE_DOCUMENT_THRESHOLD) {
    achievements.push({
      icon: "📚",
      title: "Prolific Writer",
      description: `Written over ${(totalWords / TIME_MS.SECOND).toFixed(0)}k words total`
    });
  } else if (totalWords >= SIZE_LIMITS.MAX_DOCUMENT_CHARS) {
    achievements.push({
      icon: "✍️",
      title: "Dedicated Writer",
      description: `Written ${(totalWords / TIME_MS.SECOND).toFixed(0)}k words total`
    });
  } else if (totalWords >= 10000) {
    achievements.push({
      icon: "📝",
      title: "Getting Started",
      description: `Written ${(totalWords / TIME_MS.SECOND).toFixed(0)}k words total`
    });
  }
  
  // Streak achievements
  if (streak >= 30) {
    achievements.push({
      icon: "🔥",
      title: "Writing Streak Master",
      description: `Maintained a ${streak}-day writing streak`
    });
  } else if (streak >= 14) {
    achievements.push({
      icon: "🔥",
      title: "Consistent Writer",
      description: `Maintained a ${streak}-day writing streak`
    });
  } else if (streak >= 7) {
    achievements.push({
      icon: "📅",
      title: "Week Warrior",
      description: `Maintained a ${streak}-day writing streak`
    });
  }
  
  // Quality achievements
  if (qualityScore >= 90) {
    achievements.push({
      icon: "⭐",
      title: "Excellence Award",
      description: `Achieved ${qualityScore}% quality score`
    });
  } else if (qualityScore >= 80) {
    achievements.push({
      icon: "🏆",
      title: "Quality Champion",
      description: `Achieved ${qualityScore}% quality score`
    });
  }
  
  // Session achievements
  if (totalSessions >= 100) {
    achievements.push({
      icon: "🎯",
      title: "Session Master",
      description: `Completed ${totalSessions} writing sessions`
    });
  } else if (totalSessions >= 50) {
    achievements.push({
      icon: "💪",
      title: "Persistent Writer",
      description: `Completed ${totalSessions} writing sessions`
    });
  }
  
  return achievements;
}

// Helper functions for calculating trends
function calculateTrend(
  sessions: Array<{ created_at: string; word_count: number }>,
  type: 'words' | 'avgDaily'
): { value: number; direction: 'up' | 'down' | 'neutral' } {
  if (sessions.length === 0) return { value: 0, direction: 'neutral' };
  
  const now = new Date();
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * TIME_MS.SECOND);
  const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * TIME_MS.SECOND);
  
  const thisWeek = sessions.filter(s => new Date(s.created_at) >= weekAgo);
  const lastWeek = sessions.filter(s => {
    const date = new Date(s.created_at);
    return date >= twoWeeksAgo && date < weekAgo;
  });
  
  const thisWeekTotal = thisWeek.reduce((sum, s) => sum + s.word_count, 0);
  const lastWeekTotal = lastWeek.reduce((sum, s) => sum + s.word_count, 0);
  
  if (type === 'avgDaily') {
    const thisWeekAvg = thisWeek.length > 0 ? thisWeekTotal / 7 : 0;
    const lastWeekAvg = lastWeek.length > 0 ? lastWeekTotal / 7 : 0;
    
    if (lastWeekAvg === 0) return { value: 0, direction: 'neutral' };
    
    const percentChange = ((thisWeekAvg - lastWeekAvg) / lastWeekAvg) * 100;
    return {
      value: Math.abs(Math.round(percentChange)),
      direction: percentChange > 5 ? 'up' : percentChange < -5 ? 'down' : 'neutral'
    };
  }
  
  if (lastWeekTotal === 0) return { value: 0, direction: 'neutral' };
  
  const percentChange = ((thisWeekTotal - lastWeekTotal) / lastWeekTotal) * 100;
  return {
    value: Math.abs(Math.round(percentChange)),
    direction: percentChange > 5 ? 'up' : percentChange < -5 ? 'down' : 'neutral'
  };
}

function calculateStreakTrend(
  sessions: Array<{ created_at: string }>
): { value: number; direction: 'up' | 'down' | 'neutral' } {
  const currentStreak = calculateWritingStreak(sessions);
  const weekAgoSessions = sessions.filter(
    s => new Date(s.created_at) <= new Date(Date.now() - 7 * 24 * 60 * 60 * TIME_MS.SECOND)
  );
  const weekAgoStreak = calculateWritingStreak(weekAgoSessions);
  
  const difference = currentStreak - weekAgoStreak;
  return {
    value: Math.abs(difference),
    direction: difference > 0 ? 'up' : difference < 0 ? 'down' : 'neutral'
  };
}

function calculateProjectsTrend(
  projects: Array<{ status: string; created_at?: string }>
): { value: number; direction: 'up' | 'down' | 'neutral' } {
  const activeProjects = projects.filter(p => p.status === 'active').length;
  const recentProjects = projects.filter(p => {
    if (!p.created_at) return false;
    const createdDate = new Date(p.created_at);
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * TIME_MS.SECOND);
    return createdDate >= weekAgo;
  }).length;
  
  return {
    value: recentProjects,
    direction: recentProjects > 0 ? 'up' : 'neutral'
  };
}

function generateInsights(
  sessions: Array<{ created_at: string; word_count: number; duration?: number }>,
  hourlyPattern: Array<{ date: string; value: number }>,
  weeklyPattern: Array<{ date: string; value: number }>,
  streak: number,
  avgWordsPerSession: number
): Array<{ title: string; content: string; recommendation?: string }> {
  const insights = [];
  
  // Peak productivity hours insight
  if (hourlyPattern.length > 0) {
    const sortedHours = [...hourlyPattern].sort((a, b) => b.value - a.value);
    const topHours = sortedHours.slice(0, 3).filter(h => h.value > 0);
    
    if (topHours.length > 0) {
      insights.push({
        title: "Peak Productivity Hours",
        content: `Your most productive writing hours are ${topHours.map(h => h.date).join(', ')}, with an average of ${Math.round(topHours[0].value / Math.max(sessions.length, 1))} words per session.`,
        recommendation: "Schedule your most challenging writing tasks during these peak hours."
      });
    }
  }
  
  // Weekly pattern insight
  if (weeklyPattern.length > 0) {
    const sortedDays = [...weeklyPattern].sort((a, b) => b.value - a.value);
    const bestDay = sortedDays[0];
    
    if (bestDay && bestDay.value > 0) {
      insights.push({
        title: "Best Writing Day",
        content: `${bestDay.date} is your most productive day with ${bestDay.value.toLocaleString()} total words written.`,
        recommendation: `Reserve ${bestDay.date}s for important writing milestones.`
      });
    }
  }
  
  // Streak insight
  if (streak > 0) {
    if (streak >= 7) {
      insights.push({
        title: "Writing Momentum",
        content: `You're on a ${streak}-day writing streak! Consistent writers produce ${Math.round(streak * ACHIEVEMENT_MULTIPLIERS.STREAK_CONTENT_BOOST)}% more content.`,
        recommendation: "Keep the momentum going - even 100 words maintains your streak."
      });
    } else if (streak < 3) {
      insights.push({
        title: "Building Consistency",
        content: "Your writing streak is just beginning. Writers with 7+ day streaks report higher satisfaction.",
        recommendation: "Set a small daily goal (even 250 words) to build a sustainable habit."
      });
    }
  }
  
  // Session efficiency insight
  if (sessions.length > 5 && avgWordsPerSession > 0) {
    if (avgWordsPerSession > 500) {
      insights.push({
        title: "High Session Productivity",
        content: `You average ${avgWordsPerSession} words per session, which is above the typical 300-400 range.`,
        recommendation: "Your current approach is working well - maintain these productive sessions."
      });
    } else if (avgWordsPerSession < 200) {
      insights.push({
        title: "Session Optimization",
        content: `Your sessions average ${avgWordsPerSession} words. Consider longer, focused sessions.`,
        recommendation: "Try the Pomodoro technique or eliminate distractions for deeper focus."
      });
    }
  }
  
  return insights;
}