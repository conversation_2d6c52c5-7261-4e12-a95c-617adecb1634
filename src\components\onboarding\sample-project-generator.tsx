'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { BookO<PERSON>, Sparkles, Clock, Target, AlertCircle } from 'lucide-react'

interface SampleProject {
  id: string
  title: string
  description: string
  genre: string
  wordCount: number
  estimatedTime: string
  features: string[]
  selections: Record<string, unknown>
}

const sampleProjects: SampleProject[] = [
  {
    id: 'fantasy-adventure',
    title: 'The Crystal of Aethermoor',
    description: 'A young apprentice discovers an ancient crystal that holds the power to save their dying kingdom from an eternal winter.',
    genre: 'Epic Fantasy',
    wordCount: 80000,
    estimatedTime: '5 minutes',
    features: ['Magic System', 'Character Arcs', 'World Building', 'Quest Structure'],
    selections: {
      primaryGenre: 'Fantasy',
      subgenre: 'Epic Fantasy',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Adventurous', 'Heroic'],
      writingStyle: 'Descriptive',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Moderate',
      chapterStructure: 'Traditional Chapters',
      timelineComplexity: 'Linear',
      protagonistTypes: ['Reluctant Hero'],
      antagonistTypes: ['Dark Lord'],
      characterComplexity: 'Moderate',
      characterArcTypes: ['Hero\'s Journey'],
      timePeriod: 'Medieval Fantasy',
      geographicSetting: 'Fictional World',
      worldType: 'Secondary World',
      magicTechLevel: 'High Magic',
      majorThemes: ['Good vs Evil', 'Coming of Age'],
      targetAudience: 'Young Adult',
      contentRating: 'PG-13',
      projectScope: 'Standalone Novel',
      targetWordCount: 80000,
      targetChapters: 20,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    }
  },
  {
    id: 'sci-fi-thriller',
    title: 'Neural Echo',
    description: 'In 2087, a cybersecurity expert discovers their memories have been hacked, leading them down a dangerous path to uncover a conspiracy that reaches the highest levels of government.',
    genre: 'Cyberpunk Thriller',
    wordCount: 75000,
    estimatedTime: '4 minutes',
    features: ['Tech Noir', 'Mind-bending Plot', 'Dystopian Setting', 'Fast Pacing'],
    selections: {
      primaryGenre: 'Science Fiction',
      subgenre: 'Cyberpunk',
      narrativeVoice: 'First Person',
      tense: 'Present',
      toneOptions: ['Dark', 'Suspenseful'],
      writingStyle: 'Concise',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Fast',
      chapterStructure: 'Short Chapters',
      timelineComplexity: 'Non-linear',
      protagonistTypes: ['Anti-hero'],
      antagonistTypes: ['Corporation'],
      characterComplexity: 'Complex',
      characterArcTypes: ['Redemption'],
      timePeriod: 'Near Future',
      geographicSetting: 'Urban',
      worldType: 'Alternate Earth',
      magicTechLevel: 'High Tech',
      majorThemes: ['Identity', 'Technology vs Humanity'],
      philosophicalThemes: ['Free Will', 'Reality vs Simulation'],
      targetAudience: 'Adult',
      contentRating: 'R',
      projectScope: 'Standalone Novel',
      targetWordCount: 75000,
      targetChapters: 18,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    }
  },
  {
    id: 'mystery-cozy',
    title: 'Secrets of Millbrook Manor',
    description: 'When librarian Emma inherits her great-aunt\'s manor, she discovers a decades-old mystery involving missing heirloom recipes and a secret that could change everything.',
    genre: 'Cozy Mystery',
    wordCount: 60000,
    estimatedTime: '3 minutes',
    features: ['Small Town Setting', 'Amateur Sleuth', 'Light Romance', 'Food Theme'],
    selections: {
      primaryGenre: 'Mystery',
      subgenre: 'Cozy Mystery',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Light-hearted', 'Cozy'],
      writingStyle: 'Conversational',
      structureType: 'Mystery Structure',
      pacingPreference: 'Gentle',
      chapterStructure: 'Traditional Chapters',
      timelineComplexity: 'Linear with Flashbacks',
      protagonistTypes: ['Amateur Sleuth'],
      antagonistTypes: ['Hidden Culprit'],
      characterComplexity: 'Moderate',
      characterArcTypes: ['Personal Growth'],
      timePeriod: 'Contemporary',
      geographicSetting: 'Small Town',
      worldType: 'Real World',
      magicTechLevel: 'Low Tech',
      majorThemes: ['Community', 'Family Secrets'],
      targetAudience: 'General Adult',
      contentRating: 'PG',
      projectScope: 'Standalone Novel',
      targetWordCount: 60000,
      targetChapters: 15,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    }
  }
]

interface SampleProjectGeneratorProps {
  onProjectCreated?: (projectId: string) => void
}

// Utility function to convert camelCase to snake_case
function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

// Utility function to convert camelCase object keys to snake_case
function convertKeysToSnakeCase(obj: Record<string, unknown>): Record<string, unknown> {
  const converted: Record<string, unknown> = {}
  for (const [key, value] of Object.entries(obj)) {
    converted[camelToSnake(key)] = value
  }
  return converted
}

// Utility function to extract meaningful error messages from Supabase errors
function extractErrorMessage(error: unknown): string {
  if (!error) return 'Unknown error occurred'

  if (typeof error === 'string') {
    return error
  }

  if (error instanceof Error) {
    return error.message
  }

  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>

    // Check for Supabase error properties
    if (errorObj.message && typeof errorObj.message === 'string') {
      return errorObj.message
    }

    if (errorObj.details && typeof errorObj.details === 'string') {
      return errorObj.details
    }

    if (errorObj.hint && typeof errorObj.hint === 'string') {
      return errorObj.hint
    }

    if (errorObj.code && typeof errorObj.code === 'string') {
      return `Database error (${errorObj.code}): ${errorObj.message || 'Unknown database error'}`
    }

    // Try to extract any meaningful string property
    const stringProps = Object.entries(errorObj)
      .filter(([_, value]) => typeof value === 'string' && value.length > 0)
      .map(([key, value]) => `${key}: ${value}`)

    if (stringProps.length > 0) {
      return stringProps.join(', ')
    }

    // Last resort: stringify the object
    try {
      return JSON.stringify(error, null, 2)
    } catch {
      return 'Error object could not be serialized'
    }
  }

  return 'Unknown error type'
}

export function SampleProjectGenerator({ onProjectCreated }: SampleProjectGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [progress, setProgress] = useState<string>('')
  const router = useRouter()
  const supabase = createClient()
  const { user, loading: authLoading } = useAuth()

  // Show loading state while auth is initializing
  if (authLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Sample Projects</h2>
          <p className="text-muted-foreground">Initializing authentication...</p>
        </div>
      </div>
    )
  }

  // Show error if not authenticated
  if (!user) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Sample Projects</h2>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You must be logged in to access sample projects. Please refresh the page or log in again.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  const handleGenerateSample = async (sampleProject: SampleProject) => {
    setIsGenerating(true)
    setSelectedProject(sampleProject.id)
    setError(null)
    setProgress('Initializing...')
    
    try {
      logger.info('Starting sample project generation...');
      logger.info('Selected sample:', sampleProject);
      logger.info('Auth state:', { user: !!user, authLoading });

      if (authLoading) {
        throw new Error('Authentication is still loading. Please wait a moment and try again.')
      }

      if (!user) {
        throw new Error('You must be logged in to create a sample project')
      }

      logger.info('User authenticated:', user.id);
      setProgress('Creating project...')

      // Call the API endpoint to create the sample project
      const response = await fetch('/api/sample-projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sampleProject }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.details || errorData.error || 'Failed to create sample project')
      }

      const result = await response.json()
      logger.info('Sample project created successfully:', result);

      setProgress('Complete!')

      onProjectCreated?.(result.projectId)
      router.push(`/projects/${result.projectId}?sample=true`)
    } catch (error) {
      logger.error('Error creating sample project:', error);
      logger.error('Error type:', typeof error);
      logger.error('Error constructor:', error?.constructor?.name);

      // Log the error in a way that shows its structure
      if (error && typeof error === 'object') {
        logger.error('Error keys:', Object.keys(error))
        logger.error('Error values:', Object.values(error))
      }

      const errorMessage = extractErrorMessage(error)
      logger.error('Extracted error message:', errorMessage);

      setError(`Failed to create sample project: ${errorMessage}`)
    } finally {
      setIsGenerating(false)
      setSelectedProject(null)
      setProgress('')
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Try a Sample Project</h2>
        <p className="text-muted-foreground">
          See BookScribe AI in action with a pre-built project structure. Choose a genre that interests you!
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isGenerating && progress && (
        <Alert>
          <Sparkles className="h-4 w-4" />
          <AlertDescription>{progress}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
        {sampleProjects.map((project) => (
          <Card key={project.id} className="relative overflow-hidden">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{project.title}</CardTitle>
                  <Badge variant="secondary">{project.genre}</Badge>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {project.estimatedTime}
                </div>
              </div>
              <CardDescription className="text-sm">
                {project.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4 sm:gap-5 lg:gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3" />
                  {project.wordCount.toLocaleString()} words
                </div>
                <div className="flex items-center gap-1">
                  <BookOpen className="h-3 w-3" />
                  {typeof project.selections.targetChapters === 'number' ? project.selections.targetChapters : 'N/A'} chapters
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Includes:</p>
                <div className="flex flex-wrap gap-1">
                  {project.features.map((feature, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              <Button 
                onClick={() => {
                  setError(null)
                  handleGenerateSample(project)
                }}
                disabled={isGenerating}
                className="w-full"
                variant={selectedProject === project.id ? "secondary" : "default"}
              >
                {isGenerating && selectedProject === project.id ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Try This Sample
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Sample projects help you understand how the AI structures stories, creates characters, and plans chapters.
        </p>
      </div>
    </div>
  )
}