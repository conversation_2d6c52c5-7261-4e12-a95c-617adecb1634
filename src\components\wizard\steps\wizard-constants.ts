import { FormData } from './wizard-types'
import { SIZE_LIMITS } from '@/lib/constants'

// Template presets for different genres
export const templatePresets: Record<string, FormData> = {
  fantasy: {
    title: "The Realm of Shadows",
    description: "An epic fantasy adventure about a young hero's journey to save their kingdom",
    genre: "fantasy",
    subgenre: "epic-fantasy",
    tone: "adventurous",
    targetAudience: "young-adult",
    protagonist: "A young farmhand with hidden magical abilities",
    antagonist: "The Shadow Lord",
    setting: "The Kingdom of Avaloria",
    worldBuildingDepth: "extensive",
    wordCount: "SIZE_LIMITS.LARGE_DOCUMENT_THRESHOLD",
    chapters: "25",
    themes: "courage, friendship, self-discovery",
    structure: "three-act",
    pacing: "medium",
    narrativeVoice: "third-limited",
    tense: "past",
    timePeriod: "medieval-fantasy",
    protagonistName: "Aria",
    universeId: "demo-universe-1",
    seriesId: "demo-series-1",
    isStandalone: "false",
    contentWarnings: ""
  },
  scifi: {
    title: "Neural Networks",
    description: "A cyberpunk thriller about AI consciousness and human identity",
    genre: "sci-fi",
    subgenre: "cyberpunk",
    tone: "dark",
    targetAudience: "adult",
    protagonist: "<PERSON>",
    antagonist: "The Collective",
    setting: "Neo-Tokyo 2087",
    worldBuildingDepth: "moderate",
    wordCount: "90000",
    chapters: "30",
    themes: "identity, technology, consciousness",
    structure: "seven-point",
    pacing: "fast",
    narrativeVoice: "first",
    tense: "present",
    timePeriod: "near-future",
    protagonistName: "Alex",
    universeId: "demo-universe-2",
    seriesId: "none",
    isStandalone: "true",
    contentWarnings: ""
  },
  mystery: {
    title: "The Lighthouse Keeper's Secret",
    description: "A cozy mystery about secrets hidden in a coastal lighthouse",
    genre: "mystery",
    subgenre: "cozy-mystery",
    tone: "mysterious",
    targetAudience: "adult",
    protagonist: "Detective Sarah Mills",
    antagonist: "The Anonymous Caller",
    setting: "Coastal Maine village",
    worldBuildingDepth: "minimal",
    wordCount: "70000",
    chapters: "20",
    themes: "justice, community, truth",
    structure: "three-act",
    pacing: "medium",
    narrativeVoice: "third-limited",
    tense: "past",
    timePeriod: "contemporary",
    protagonistName: "Sarah",
    universeId: "none",
    seriesId: "demo-series-2",
    isStandalone: "false",
    contentWarnings: ""
  }
}

// Guided mode step guidance
export const stepGuidance = [
  {
    title: "Let's Start with the Basics",
    description: "Give your story a title and brief description. Don't worry about making it perfect - you can always change it later!",
    tips: [
      "Choose a working title that captures the essence of your story",
      "The description helps AI understand your vision - be descriptive but concise",
      "Think about what makes your story unique or interesting"
    ]
  },
  {
    title: "Choose Your Story's Genre and Style",
    description: "These choices will guide how the AI develops your story structure, characters, and writing style.",
    tips: [
      "Genre affects plot structure and character archetypes",
      "Narrative voice determines who tells the story (first person, third person, etc.)",
      "Tense choice impacts the story's immediacy and perspective"
    ]
  },
  {
    title: "Define Your Story Structure",
    description: "How your story unfolds affects reader engagement and pacing throughout your novel.",
    tips: [
      "Three-act structure is classic and reliable for most genres",
      "Pacing preference affects how quickly events unfold",
      "Consider your target audience when choosing structure complexity"
    ]
  },
  {
    title: "Build Your World and Characters",
    description: "Rich characters and immersive settings are the heart of compelling fiction.",
    tips: [
      "Character complexity affects how much development each character gets",
      "Setting influences mood, conflict, and plot possibilities",
      "Time period impacts available technology, social norms, and conflicts"
    ]
  },
  {
    title: "Establish Themes and Content Guidelines",
    description: "Themes give your story depth and meaning beyond the surface plot.",
    tips: [
      "Themes guide character arcs and plot development",
      "Content warnings help AI avoid topics you're uncomfortable with",
      "Consider what message or feeling you want readers to take away"
    ]
  },
  {
    title: "Configure Technical Details",
    description: "These final settings ensure AI generates content that matches your vision.",
    tips: [
      "Word count affects story complexity and subplot development",
      "Chapter count influences pacing and story beats",
      "Series/universe connections affect world-building scope"
    ]
  }
]