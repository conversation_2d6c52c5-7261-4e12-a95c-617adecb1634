'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, 
  Clock, 
  Target,
  ArrowRight,
  BookOpen,
  Edit3
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { SearchResult } from '../content-search-interface'

interface ChapterPreviewProps {
  result: SearchResult
  onNavigate: () => void
  onEdit?: () => void
  className?: string
}

export function ChapterPreview({ 
  result, 
  onNavigate, 
  onEdit,
  className 
}: ChapterPreviewProps) {
  const chapterNumber = result.metadata.chapterNumber || 0
  const wordCount = result.metadata.wordCount || 0
  const targetWordCount = 2500 // Default target, could come from project settings
  const completionPercentage = Math.min((wordCount / targetWordCount) * 100, 100)

  return (
    <Card className={cn("hover:shadow-lg transition-shadow", className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <FileText className="w-5 h-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg">
                Chapter {chapterNumber}: {result.title}
              </CardTitle>
              <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                <Clock className="w-3 h-3" />
                <span>{new Date(result.metadata.lastModified).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          <Badge variant="secondary">Chapter</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Excerpt */}
        <div className="prose prose-sm dark:prose-invert max-w-none">
          <p className="line-clamp-3 text-muted-foreground">
            {result.excerpt}
          </p>
        </div>

        {/* Word Count Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">
              {wordCount.toLocaleString()} / {targetWordCount.toLocaleString()} words
            </span>
          </div>
          <Progress value={completionPercentage} className="h-2" />
        </div>

        {/* Highlights */}
        {result.highlights.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Matching excerpts:</p>
            <div className="space-y-1">
              {result.highlights.slice(0, 2).map((highlight, idx) => (
                <div
                  key={idx}
                  className="text-sm p-2 bg-muted/50 rounded"
                  dangerouslySetInnerHTML={{ __html: highlight }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button 
            variant="default" 
            size="sm" 
            onClick={onNavigate}
            className="flex-1"
          >
            <BookOpen className="w-4 h-4 mr-2" />
            Read Chapter
          </Button>
          {onEdit && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onEdit}
              className="flex-1"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}