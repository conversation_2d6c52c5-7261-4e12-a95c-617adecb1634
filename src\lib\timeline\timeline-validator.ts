import { vercelAIClient } from '@/lib/ai/vercel-ai-client';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';
import { AI_MODELS } from '@/lib/config/ai-settings';
import { TIME_MS } from '@/lib/constants'

export interface TimelineEvent {
  id: string;
  type: 'plot' | 'character' | 'world' | 'reference';
  title: string;
  description: string;
  timestamp: TimeStamp;
  chapter: number;
  scene?: number;
  characters: string[];
  location?: string;
  duration?: Duration;
  dependencies: string[]; // Events that must happen before this
  consequences: string[]; // Events that happen because of this
  importance: number; // 0-1 scale
  verified: boolean;
  conflicts: TimelineConflict[];
}

export interface TimeStamp {
  type: 'absolute' | 'relative' | 'seasonal' | 'vague';
  value: string; // e.g., "March 15, 2024", "3 days later", "winter", "sometime after the war"
  parsedDate?: Date;
  relativeToEvent?: string; // Event ID for relative timestamps
  uncertainty: number; // 0-1 scale, how certain we are about this time
}

export interface Duration {
  amount: number;
  unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months' | 'years';
  approximate: boolean;
}

export interface TimelineConflict {
  id: string;
  type: 'chronological' | 'logical' | 'duration' | 'character_age' | 'seasonal' | 'reference';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedEvents: string[];
  suggestions: string[];
  autoFixable: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  conflicts: TimelineConflict[];
  warnings: string[];
  suggestions: string[];
  confidence: number;
}

export interface TimelineAnalysis {
  totalEvents: number;
  timeSpan: Duration;
  conflicts: TimelineConflict[];
  characterAges: Record<string, { start: number; end: number; conflicts: string[] }>;
  seasonalConsistency: { valid: boolean; issues: string[] };
  plotContinuity: { valid: boolean; gaps: string[] };
}

export class TimelineValidator {
  private events = new Map<string, TimelineEvent>();
  private referenceDate?: Date; // Story's starting point
  private timePeriod?: string; // Historical period for validation

  constructor(_projectId: string, referenceDate?: Date, timePeriod?: string) {
    this.referenceDate = referenceDate;
    this.timePeriod = timePeriod;
  }

  async addEvent(event: Omit<TimelineEvent, 'id' | 'verified' | 'conflicts'>): Promise<string> {
    const id = `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const timelineEvent: TimelineEvent = {
      ...event,
      id,
      verified: false,
      conflicts: []
    };

    // Parse and validate timestamp
    timelineEvent.timestamp = await this.parseTimestamp(event.timestamp);
    
    this.events.set(id, timelineEvent);
    
    // Validate this event against existing timeline
    await this.validateEvent(id);
    
    return id;
  }

  async validateTimeline(): Promise<ValidationResult> {
    const conflicts: TimelineConflict[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check chronological consistency
    const chronologicalConflicts = await this.validateChronology();
    conflicts.push(...chronologicalConflicts);

    // Check character age consistency
    const ageConflicts = await this.validateCharacterAges();
    conflicts.push(...ageConflicts);

    // Check seasonal consistency
    const seasonalConflicts = await this.validateSeasonalConsistency();
    conflicts.push(...seasonalConflicts);

    // Check duration consistency
    const durationConflicts = await this.validateDurations();
    conflicts.push(...durationConflicts);

    // Check plot continuity
    const plotConflicts = await this.validatePlotContinuity();
    conflicts.push(...plotConflicts);

    // Check historical accuracy (if applicable)
    if (this.timePeriod && this.timePeriod !== 'contemporary') {
      const historicalConflicts = await this.validateHistoricalAccuracy();
      conflicts.push(...historicalConflicts);
    }

    // Generate suggestions
    if (conflicts.length > 0) {
      suggestions.push(...await this.generateSuggestions(conflicts));
    }

    // Calculate confidence score
    const confidence = this.calculateConfidence(conflicts);

    return {
      isValid: conflicts.filter(c => c.severity === 'high' || c.severity === 'critical').length === 0,
      conflicts,
      warnings,
      suggestions,
      confidence
    };
  }

  async extractTimelineFromChapter(chapterContent: string, chapterNumber: number): Promise<TimelineEvent[]> {
    const events: TimelineEvent[] = [];

    try {
      const timelineEventSchema = z.array(z.object({
        type: z.enum(['plot', 'character', 'world', 'reference']).optional(),
        title: z.string(),
        description: z.string(),
        timestamp: z.object({
          type: z.enum(['absolute', 'relative', 'seasonal', 'vague']),
          value: z.string(),
          uncertainty: z.number().min(0).max(1)
        }),
        characters: z.array(z.string()).optional(),
        location: z.string().optional(),
        duration: z.object({
          amount: z.number(),
          unit: z.enum(['minutes', 'hours', 'days', 'weeks', 'months', 'years']),
          approximate: z.boolean()
        }).optional(),
        importance: z.number().min(0).max(1).optional()
      }));
      
      const systemPrompt = `Extract timeline events from this chapter content. Identify:
1. Specific times mentioned (dates, times, durations)
2. Sequence of events
3. Character actions and their timing
4. References to past or future events
5. Seasonal or time period indicators

Return a JSON array of events with the following structure:
{
  "type": "plot|character|world|reference",
  "title": "Brief title",
  "description": "Description of what happens",
  "timestamp": {
    "type": "absolute|relative|seasonal|vague",
    "value": "time reference as stated in text",
    "uncertainty": 0.0-1.0
  },
  "characters": ["character names involved"],
  "location": "where it happens",
  "duration": {
    "amount": number,
    "unit": "minutes|hours|days|weeks|months|years",
    "approximate": boolean
  },
  "importance": 0.0-1.0
}`;
      
      const userPrompt = `Chapter ${chapterNumber} content:\n\n${chapterContent}`;
      
      const extractedEvents = await vercelAIClient.generateObjectWithFallback(
        userPrompt,
        timelineEventSchema,
        {
          model: AI_MODELS.TASKS.TIMELINE_EXTRACTION || 'gpt-4',
          temperature: 0.1,
          systemPrompt
        }
      );
      
      for (const eventData of extractedEvents) {
        const event: TimelineEvent = {
          id: `extracted_${chapterNumber}_${events.length}`,
          type: eventData.type || 'plot',
          title: eventData.title,
          description: eventData.description,
          timestamp: eventData.timestamp,
          chapter: chapterNumber,
          characters: eventData.characters || [],
          location: eventData.location,
          duration: eventData.duration,
          dependencies: [],
          consequences: [],
          importance: eventData.importance || 0.5,
          verified: false,
          conflicts: []
        };

        events.push(event);
      }
    } catch (error) {
      logger.error('Error extracting timeline from chapter:', error);
    }

    return events;
  }

  async validateEvent(eventId: string): Promise<TimelineConflict[]> {
    const event = this.events.get(eventId);
    if (!event) return [];

    const conflicts: TimelineConflict[] = [];

    // Check against all other events
    for (const [otherId, otherEvent] of Array.from(this.events.entries())) {
      if (otherId === eventId) continue;

      // Chronological conflicts
      const chronoConflict = await this.checkChronologicalConflict(event, otherEvent);
      if (chronoConflict) conflicts.push(chronoConflict);

      // Character conflicts (same character in two places at once)
      const characterConflict = await this.checkCharacterConflict(event, otherEvent);
      if (characterConflict) conflicts.push(characterConflict);

      // Logical conflicts (contradictory events)
      const logicalConflict = await this.checkLogicalConflict(event, otherEvent);
      if (logicalConflict) conflicts.push(logicalConflict);
    }

    // Update event with conflicts
    event.conflicts = conflicts;
    this.events.set(eventId, event);

    return conflicts;
  }

  async autoFixConflicts(conflictIds: string[]): Promise<{ fixed: string[]; failed: string[] }> {
    const fixed: string[] = [];
    const failed: string[] = [];

    for (const conflictId of conflictIds) {
      const conflict = this.findConflictById(conflictId);
      if (!conflict || !conflict.autoFixable) {
        failed.push(conflictId);
        continue;
      }

      try {
        const success = await this.applyAutoFix(conflict);
        if (success) {
          fixed.push(conflictId);
        } else {
          failed.push(conflictId);
        }
      } catch (error) {
        logger.error(`Error auto-fixing conflict ${conflictId}:`, error);
        failed.push(conflictId);
      }
    }

    return { fixed, failed };
  }

  getTimelineAnalysis(): TimelineAnalysis {
    const events = Array.from(this.events.values());
    const conflicts = events.flatMap(e => e.conflicts);

    // Calculate time span
    const dates = events
      .map(e => e.timestamp.parsedDate)
      .filter(d => d !== undefined)
      .sort((a, b) => a!.getTime() - b!.getTime());

    const timeSpan: Duration = dates.length > 1 
      ? this.calculateDuration(dates[0]!, dates[dates.length - 1]!)
      : { amount: 0, unit: 'days', approximate: true };

    // Analyze character ages
    const characterAges = this.analyzeCharacterAges(events);

    // Check seasonal consistency
    const seasonalConsistency = this.checkSeasonalConsistency(events);

    // Check plot continuity
    const plotContinuity = this.checkPlotContinuity(events);

    return {
      totalEvents: events.length,
      timeSpan,
      conflicts,
      characterAges,
      seasonalConsistency,
      plotContinuity
    };
  }

  getEventsInRange(startDate: Date, endDate: Date): TimelineEvent[] {
    return Array.from(this.events.values()).filter(event => {
      if (!event.timestamp.parsedDate) return false;
      return event.timestamp.parsedDate >= startDate && event.timestamp.parsedDate <= endDate;
    });
  }

  getEventsByCharacter(characterName: string): TimelineEvent[] {
    return Array.from(this.events.values()).filter(event =>
      event.characters.includes(characterName)
    );
  }

  private async parseTimestamp(timestamp: TimeStamp): Promise<TimeStamp> {
    if (timestamp.parsedDate) return timestamp;

    const parsed = { ...timestamp };

    try {
      switch (timestamp.type) {
        case 'absolute':
          parsed.parsedDate = new Date(timestamp.value);
          break;
        case 'relative':
          if (timestamp.relativeToEvent && this.referenceDate) {
            const refEvent = this.events.get(timestamp.relativeToEvent);
            if (refEvent?.timestamp.parsedDate) {
              parsed.parsedDate = this.parseRelativeTime(timestamp.value, refEvent.timestamp.parsedDate);
            }
          }
          break;
        case 'seasonal':
          if (this.referenceDate) {
            parsed.parsedDate = this.parseSeasonalTime(timestamp.value, this.referenceDate);
          }
          break;
        case 'vague':
          // Can't parse vague timestamps precisely
          parsed.uncertainty = Math.max(parsed.uncertainty || 0.5, 0.8);
          break;
      }
    } catch (error) {
      logger.error('Error parsing timestamp:', error);
      parsed.uncertainty = 1.0;
    }

    return parsed;
  }

  private async validateChronology(): Promise<TimelineConflict[]> {
    const conflicts: TimelineConflict[] = [];
    const events = Array.from(this.events.values()).sort((a, b) => a.chapter - b.chapter);

    for (let i = 0; i < events.length - 1; i++) {
      const current = events[i];
      const next = events[i + 1];

      if (current && next && current.timestamp.parsedDate && next.timestamp.parsedDate) {
        if (current.timestamp.parsedDate > next.timestamp.parsedDate && current.chapter < next.chapter) {
          conflicts.push({
            id: `chrono_${current.id}_${next.id}`,
            type: 'chronological',
            severity: 'high',
            description: `Event in Chapter ${current.chapter} happens after event in Chapter ${next.chapter}`,
            affectedEvents: [current.id, next.id],
            suggestions: [
              'Adjust the timeline to maintain chronological order',
              'Add transitional text explaining the time jump',
              'Consider using flashbacks or non-linear narrative structure'
            ],
            autoFixable: false
          });
        }
      }
    }

    return conflicts;
  }

  private async validateCharacterAges(): Promise<TimelineConflict[]> {
    const conflicts: TimelineConflict[] = [];
    const characterEvents = new Map<string, TimelineEvent[]>();

    // Group events by character
    for (const event of Array.from(this.events.values())) {
      for (const character of event.characters) {
        if (!characterEvents.has(character)) {
          characterEvents.set(character, []);
        }
        characterEvents.get(character)!.push(event);
      }
    }

    // Check each character's timeline for age consistency
    for (const [character, events] of Array.from(characterEvents.entries())) {
      const sortedEvents = events
        .filter(e => e.timestamp.parsedDate)
        .sort((a, b) => a.timestamp.parsedDate!.getTime() - b.timestamp.parsedDate!.getTime());

      if (sortedEvents.length > 1) {
        const firstEvent = sortedEvents[0];
        const lastEvent = sortedEvents[sortedEvents.length - 1];
        if (firstEvent?.timestamp.parsedDate && lastEvent?.timestamp.parsedDate) {
          const timeSpan = this.calculateDuration(
            firstEvent.timestamp.parsedDate,
            lastEvent.timestamp.parsedDate
          );

        // Check for unrealistic aging (e.g., character ages 20 years in 1 week)
        if (timeSpan.unit === 'days' && timeSpan.amount < 365 && this.hasAgingReferences(events)) {
          conflicts.push({
            id: `age_${character}`,
            type: 'character_age',
            severity: 'medium',
            description: `Character ${character} appears to age unrealistically quickly`,
            affectedEvents: events.map(e => e.id),
            suggestions: [
              'Adjust the timeline to allow for realistic aging',
              'Remove or modify aging references',
              'Add time skips between chapters'
            ],
            autoFixable: false
          });
        }
        }
      }
    }

    return conflicts;
  }

  private async validateSeasonalConsistency(): Promise<TimelineConflict[]> {
    const conflicts: TimelineConflict[] = [];
    const events = Array.from(this.events.values())
      .filter(e => e.timestamp.type === 'seasonal' || e.description.match(/spring|summer|fall|autumn|winter/i))
      .sort((a, b) => a.chapter - b.chapter);

    for (let i = 0; i < events.length - 1; i++) {
      const current = events[i];
      const next = events[i + 1];

      if (!current || !next) continue;
      
      const currentSeason = this.extractSeason(current);
      const nextSeason = this.extractSeason(next);

      if (currentSeason && nextSeason && !this.isValidSeasonTransition(currentSeason, nextSeason, current.chapter, next.chapter)) {
        conflicts.push({
          id: `seasonal_${current.id}_${next.id}`,
          type: 'seasonal',
          severity: 'medium',
          description: `Illogical season transition from ${currentSeason} to ${nextSeason}`,
          affectedEvents: [current.id, next.id],
          suggestions: [
            'Adjust seasonal references to maintain logical progression',
            'Add time passages to explain season changes',
            'Verify the timeline spans the correct duration'
          ],
          autoFixable: false
        });
      }
    }

    return conflicts;
  }

  private async validateDurations(): Promise<TimelineConflict[]> {
    const conflicts: TimelineConflict[] = [];

    for (const event of Array.from(this.events.values())) {
      if (event.duration && event.timestamp.parsedDate) {
        // Check for impossible durations (e.g., 25-hour day)
        if (event.duration.unit === 'hours' && event.duration.amount > 24) {
          conflicts.push({
            id: `duration_${event.id}`,
            type: 'duration',
            severity: 'high',
            description: `Event duration of ${event.duration.amount} hours exceeds 24 hours`,
            affectedEvents: [event.id],
            suggestions: [
              'Split the event across multiple days',
              'Reduce the duration to a realistic timeframe',
              'Clarify if this is cumulative time across multiple days'
            ],
            autoFixable: true
          });
        }
      }
    }

    return conflicts;
  }

  private async validatePlotContinuity(): Promise<TimelineConflict[]> {
    const conflicts: TimelineConflict[] = [];

    for (const event of Array.from(this.events.values())) {
      // Check dependencies
      for (const depId of event.dependencies) {
        const dependency = this.events.get(depId);
        if (dependency && dependency.timestamp.parsedDate && event.timestamp.parsedDate) {
          if (dependency.timestamp.parsedDate > event.timestamp.parsedDate) {
            conflicts.push({
              id: `dependency_${event.id}_${depId}`,
              type: 'logical',
              severity: 'high',
              description: `Event depends on something that happens later`,
              affectedEvents: [event.id, depId],
              suggestions: [
                'Reorder events to maintain logical sequence',
                'Remove the dependency relationship',
                'Adjust timestamps to fix the order'
              ],
              autoFixable: false
            });
          }
        }
      }
    }

    return conflicts;
  }

  private async validateHistoricalAccuracy(): Promise<TimelineConflict[]> {
    const conflicts: TimelineConflict[] = [];

    // This would check against historical databases/APIs
    // For now, implementing basic checks
    
    for (const event of Array.from(this.events.values())) {
      if (this.timePeriod === 'medieval' && event.description.includes('gunpowder')) {
        conflicts.push({
          id: `historical_${event.id}`,
          type: 'reference',
          severity: 'medium',
          description: `Gunpowder mentioned in medieval setting`,
          affectedEvents: [event.id],
          suggestions: [
            'Replace with period-appropriate weapons',
            'Adjust the time period setting',
            'Add fantasy elements to justify anachronisms'
          ],
          autoFixable: false
        });
      }
    }

    return conflicts;
  }

  private calculateDuration(start: Date, end: Date): Duration {
    const diffMs = end.getTime() - start.getTime();
    const diffDays = Math.floor(diffMs / (TIME_MS.SECOND * 60 * 60 * 24));
    
    if (diffDays < 1) {
      const diffHours = Math.floor(diffMs / (TIME_MS.SECOND * 60 * 60));
      return { amount: diffHours, unit: 'hours', approximate: false };
    } else if (diffDays < 30) {
      return { amount: diffDays, unit: 'days', approximate: false };
    } else if (diffDays < 365) {
      const diffWeeks = Math.floor(diffDays / 7);
      return { amount: diffWeeks, unit: 'weeks', approximate: true };
    } else {
      const diffYears = Math.floor(diffDays / 365);
      return { amount: diffYears, unit: 'years', approximate: true };
    }
  }

  private parseRelativeTime(value: string, referenceDate: Date): Date {
    const result = new Date(referenceDate);
    
    const patterns = [
      { regex: /(\d+)\s*days?\s*later/i, unit: 'days' },
      { regex: /(\d+)\s*weeks?\s*later/i, unit: 'weeks' },
      { regex: /(\d+)\s*months?\s*later/i, unit: 'months' },
      { regex: /(\d+)\s*years?\s*later/i, unit: 'years' },
      { regex: /(\d+)\s*hours?\s*later/i, unit: 'hours' }
    ];

    for (const pattern of patterns) {
      const match = value.match(pattern.regex);
      if (match && match[1]) {
        const amount = parseInt(match[1]);
        switch (pattern.unit) {
          case 'hours':
            result.setHours(result.getHours() + amount);
            break;
          case 'days':
            result.setDate(result.getDate() + amount);
            break;
          case 'weeks':
            result.setDate(result.getDate() + (amount * 7));
            break;
          case 'months':
            result.setMonth(result.getMonth() + amount);
            break;
          case 'years':
            result.setFullYear(result.getFullYear() + amount);
            break;
        }
        break;
      }
    }

    return result;
  }

  private parseSeasonalTime(value: string, referenceDate: Date): Date {
    const seasons = {
      spring: 3, // March
      summer: 6, // June
      fall: 9, autumn: 9, // September
      winter: 12 // December
    };

    const season = value.toLowerCase();
    for (const [seasonName, month] of Object.entries(seasons)) {
      if (season.includes(seasonName)) {
        const result = new Date(referenceDate);
        result.setMonth(month - 1, 21); // 21st day of the season month
        return result;
      }
    }

    return referenceDate;
  }

  private hasAgingReferences(events: TimelineEvent[]): boolean {
    const agingKeywords = ['older', 'aged', 'grew up', 'matured', 'birthday', 'years old'];
    return events.some(event => 
      agingKeywords.some(keyword => 
        event.description.toLowerCase().includes(keyword)
      )
    );
  }

  private extractSeason(event: TimelineEvent): string | null {
    const seasonPatterns = {
      spring: /spring|bloom|blossom|green/i,
      summer: /summer|hot|sunny|vacation/i,
      fall: /fall|autumn|leaves|harvest/i,
      winter: /winter|cold|snow|frost/i
    };

    const text = `${event.description} ${event.timestamp.value}`;
    
    for (const [season, pattern] of Object.entries(seasonPatterns)) {
      if (pattern.test(text)) return season;
    }

    return null;
  }

  private isValidSeasonTransition(from: string, to: string, fromChapter: number, toChapter: number): boolean {
    const seasonOrder = ['winter', 'spring', 'summer', 'fall'];
    const fromIndex = seasonOrder.indexOf(from);
    const toIndex = seasonOrder.indexOf(to);
    
    if (fromIndex === -1 || toIndex === -1) return true; // Unknown seasons
    
    const chapterDiff = toChapter - fromChapter;
    
    // Allow same season for nearby chapters
    if (chapterDiff <= 2) return true;
    
    // Allow next season transition
    const expectedNext = (fromIndex + 1) % 4;
    return toIndex === expectedNext || toIndex === fromIndex;
  }

  private analyzeCharacterAges(_events: TimelineEvent[]): Record<string, { start: number; end: number; conflicts: string[] }> {
    // Simplified implementation - would need character birth dates for full analysis
    return {};
  }

  private checkSeasonalConsistency(_events: TimelineEvent[]): { valid: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Check for seasonal inconsistencies
    // Implementation would filter and check seasonal events
    
    return {
      valid: issues.length === 0,
      issues
    };
  }

  private checkPlotContinuity(_events: TimelineEvent[]): { valid: boolean; gaps: string[] } {
    const gaps: string[] = [];
    
    // Check for plot gaps or missing events
    // Implementation would filter and check plot events
    
    return {
      valid: gaps.length === 0,
      gaps
    };
  }

  private async checkChronologicalConflict(event1: TimelineEvent, event2: TimelineEvent): Promise<TimelineConflict | null> {
    if (!event1.timestamp.parsedDate || !event2.timestamp.parsedDate) return null;
    
    const date1 = event1.timestamp.parsedDate;
    const date2 = event2.timestamp.parsedDate;
    
    // If earlier chapter has later date, that's a conflict
    if (event1.chapter < event2.chapter && date1 > date2) {
      return {
        id: `chrono_${event1.id}_${event2.id}`,
        type: 'chronological',
        severity: 'high',
        description: `Chapter ${event1.chapter} event occurs after Chapter ${event2.chapter} event`,
        affectedEvents: [event1.id, event2.id],
        suggestions: ['Adjust timestamps to maintain chronological order'],
        autoFixable: false
      };
    }
    
    return null;
  }

  private async checkCharacterConflict(event1: TimelineEvent, event2: TimelineEvent): Promise<TimelineConflict | null> {
    const sharedCharacters = event1.characters.filter(c => event2.characters.includes(c));
    
    if (sharedCharacters.length > 0 && 
        event1.timestamp.parsedDate && 
        event2.timestamp.parsedDate &&
        Math.abs(event1.timestamp.parsedDate.getTime() - event2.timestamp.parsedDate.getTime()) < 60000 && // Within 1 minute
        event1.location !== event2.location) {
      
      return {
        id: `character_${event1.id}_${event2.id}`,
        type: 'logical',
        severity: 'high',
        description: `Character(s) ${sharedCharacters.join(', ')} in two different locations simultaneously`,
        affectedEvents: [event1.id, event2.id],
        suggestions: ['Adjust timing or location to prevent character conflicts'],
        autoFixable: false
      };
    }
    
    return null;
  }

  private async checkLogicalConflict(event1: TimelineEvent, event2: TimelineEvent): Promise<TimelineConflict | null> {
    // Check for contradictory events using AI
    try {
      const conflictResultSchema = z.object({
        result: z.enum(['CONFLICT', 'CONSISTENT', 'UNCLEAR']),
        reason: z.string().optional()
      });
      
      const systemPrompt = 'Analyze if these two story events contradict each other logically. Return result as "CONFLICT" if they contradict, "CONSISTENT" if they are compatible, or "UNCLEAR" if uncertain, with optional reason.';
      
      const userPrompt = `Event 1: ${event1.description}\nEvent 2: ${event2.description}`;
      
      const analysis = await vercelAIClient.generateObjectWithFallback(
        userPrompt,
        conflictResultSchema,
        {
          model: AI_MODELS.TASKS.LOGIC_CHECK || 'gpt-4',
          temperature: 0.1,
          maxTokens: 150,
          systemPrompt
        }
      );

      const result = analysis.result;
      
      if (result === 'CONFLICT') {
        return {
          id: `logical_${event1.id}_${event2.id}`,
          type: 'logical',
          severity: 'medium',
          description: 'Events contain contradictory information',
          affectedEvents: [event1.id, event2.id],
          suggestions: ['Review events for logical consistency'],
          autoFixable: false
        };
      }
    } catch (error) {
      logger.error('Error checking logical conflict:', error);
    }
    
    return null;
  }

  private async generateSuggestions(conflicts: TimelineConflict[]): Promise<string[]> {
    const suggestions = new Set<string>();
    
    conflicts.forEach(conflict => {
      conflict.suggestions.forEach(suggestion => suggestions.add(suggestion));
    });
    
    return Array.from(suggestions);
  }

  private calculateConfidence(conflicts: TimelineConflict[]): number {
    if (conflicts.length === 0) return 1.0;
    
    const severityWeights = { low: 0.1, medium: 0.3, high: 0.6, critical: 1.0 };
    const totalWeight = conflicts.reduce((sum, c) => sum + severityWeights[c.severity], 0);
    
    return Math.max(0, 1 - (totalWeight / conflicts.length));
  }

  private findConflictById(conflictId: string): TimelineConflict | null {
    for (const event of Array.from(this.events.values())) {
      const conflict = event.conflicts.find(c => c.id === conflictId);
      if (conflict) return conflict;
    }
    return null;
  }

  private async applyAutoFix(conflict: TimelineConflict): Promise<boolean> {
    // Implement auto-fix logic for fixable conflicts
    if (conflict.type === 'duration' && conflict.affectedEvents.length === 1) {
      const eventId = conflict.affectedEvents[0];
      if (!eventId) return false;
      const event = this.events.get(eventId);
      if (event?.duration && event.duration.unit === 'hours' && event.duration.amount > 24) {
        event.duration.amount = 24;
        event.duration.approximate = true;
        return true;
      }
    }
    
    return false;
  }
}