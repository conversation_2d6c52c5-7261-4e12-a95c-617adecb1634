-- Migration: User presence tracking for collaboration
-- Dependencies: auth.users, profiles, projects
-- Rollback: DROP TABLE IF EXISTS user_presence CASCADE;

BEGIN;

-- Create user presence table
CREATE TABLE IF NOT EXISTS user_presence (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL,
  
  -- Presence status
  status TEXT NOT NULL DEFAULT 'offline' CHECK (status IN ('online', 'away', 'busy', 'offline')),
  last_seen TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- Current activity
  current_page TEXT, -- Page/route user is on
  current_section TEXT, -- What section they're working on
  is_writing BOOLEAN DEFAULT false,
  is_idle BOOLEAN DEFAULT false,
  
  -- Session info
  session_id TEXT, -- Browser session ID
  ip_address INET,
  user_agent TEXT,
  
  -- Location data (for collaborative editing)
  cursor_position JSONB, -- { line: number, column: number }
  selection_range JSONB, -- { start: {line, col}, end: {line, col} }
  viewport JSONB, -- Current editor viewport
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure one presence record per user per project
  UNIQUE(user_id, project_id)
);

-- Create indexes for efficient queries
CREATE INDEX idx_user_presence_user_id ON user_presence(user_id);
CREATE INDEX idx_user_presence_project_id ON user_presence(project_id);
CREATE INDEX idx_user_presence_status ON user_presence(status);
CREATE INDEX idx_user_presence_last_seen ON user_presence(last_seen);
CREATE INDEX idx_user_presence_session_id ON user_presence(session_id);
CREATE INDEX idx_user_presence_is_writing ON user_presence(is_writing) WHERE is_writing = true;

-- Enable Row Level Security
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users can view presence of collaborators on projects they have access to
CREATE POLICY "Users can view collaborator presence" ON user_presence
  FOR SELECT USING (
    -- User can see their own presence
    user_id = auth.uid()
    OR
    -- User can see presence in projects they collaborate on
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
      UNION
      SELECT project_id FROM project_collaborators 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Users can update their own presence
CREATE POLICY "Users can update own presence" ON user_presence
  FOR ALL USING (user_id = auth.uid());

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_presence_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at
CREATE TRIGGER update_user_presence_updated_at_trigger
  BEFORE UPDATE ON user_presence
  FOR EACH ROW
  EXECUTE FUNCTION update_user_presence_updated_at();

-- Function to clean up stale presence records
CREATE OR REPLACE FUNCTION cleanup_stale_presence()
RETURNS void AS $$
BEGIN
  -- Mark users as offline if they haven't been seen in 5 minutes
  UPDATE user_presence
  SET 
    status = 'offline',
    is_writing = false,
    is_idle = true,
    updated_at = NOW()
  WHERE 
    status != 'offline' 
    AND last_seen < NOW() - INTERVAL '5 minutes';
    
  -- Delete very old presence records (older than 24 hours)
  DELETE FROM user_presence
  WHERE last_seen < NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(
  p_user_id UUID,
  p_project_id UUID DEFAULT NULL,
  p_chapter_id UUID DEFAULT NULL,
  p_status TEXT DEFAULT 'online',
  p_current_page TEXT DEFAULT NULL,
  p_current_section TEXT DEFAULT NULL,
  p_is_writing BOOLEAN DEFAULT false,
  p_cursor_position JSONB DEFAULT NULL,
  p_selection_range JSONB DEFAULT NULL,
  p_viewport JSONB DEFAULT NULL,
  p_session_id TEXT DEFAULT NULL
)
RETURNS user_presence AS $$
DECLARE
  result user_presence;
BEGIN
  INSERT INTO user_presence (
    user_id,
    project_id,
    chapter_id,
    status,
    last_seen,
    current_page,
    current_section,
    is_writing,
    is_idle,
    cursor_position,
    selection_range,
    viewport,
    session_id
  ) VALUES (
    p_user_id,
    p_project_id,
    p_chapter_id,
    p_status,
    NOW(),
    p_current_page,
    p_current_section,
    p_is_writing,
    false, -- Reset idle status on update
    p_cursor_position,
    p_selection_range,
    p_viewport,
    p_session_id
  )
  ON CONFLICT (user_id, project_id) 
  DO UPDATE SET
    chapter_id = EXCLUDED.chapter_id,
    status = EXCLUDED.status,
    last_seen = EXCLUDED.last_seen,
    current_page = EXCLUDED.current_page,
    current_section = EXCLUDED.current_section,
    is_writing = EXCLUDED.is_writing,
    is_idle = EXCLUDED.is_idle,
    cursor_position = EXCLUDED.cursor_position,
    selection_range = EXCLUDED.selection_range,
    viewport = EXCLUDED.viewport,
    session_id = EXCLUDED.session_id
  RETURNING * INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get project collaborators with presence
CREATE OR REPLACE FUNCTION get_project_presence(p_project_id UUID)
RETURNS TABLE (
  user_id UUID,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT,
  status TEXT,
  last_seen TIMESTAMPTZ,
  current_page TEXT,
  current_section TEXT,
  is_writing BOOLEAN,
  is_idle BOOLEAN,
  cursor_position JSONB,
  selection_range JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.user_id,
    u.email,
    prof.full_name,
    prof.avatar_url,
    pc.role,
    COALESCE(up.status, 'offline') as status,
    up.last_seen,
    up.current_page,
    up.current_section,
    COALESCE(up.is_writing, false) as is_writing,
    COALESCE(up.is_idle, true) as is_idle,
    up.cursor_position,
    up.selection_range
  FROM project_collaborators pc
  JOIN auth.users u ON pc.user_id = u.id
  LEFT JOIN profiles prof ON pc.user_id = prof.id
  LEFT JOIN user_presence up ON pc.user_id = up.user_id AND up.project_id = p_project_id
  WHERE pc.project_id = p_project_id 
    AND pc.status = 'active'
  ORDER BY 
    CASE WHEN up.status = 'online' THEN 1 
         WHEN up.status = 'away' THEN 2 
         WHEN up.status = 'busy' THEN 3 
         ELSE 4 END,
    up.last_seen DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable Realtime for user_presence
ALTER PUBLICATION supabase_realtime ADD TABLE user_presence;

COMMIT;