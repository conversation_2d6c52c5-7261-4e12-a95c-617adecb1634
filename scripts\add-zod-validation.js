#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Adding Zod validation to API endpoints...\n');

// Map of API routes to their expected validation schemas
const routeValidationMap = {
  // Projects
  'projects/route.ts': {
    POST: {
      body: `z.object({
        name: schemas.project.name,
        description: schemas.strings.description,
        genre: schemas.project.genre,
        writingStyle: schemas.project.writingStyle,
        pov: schemas.project.pov,
        tense: schemas.project.tense,
        targetWordCount: schemas.project.targetWordCount,
        targetChapters: schemas.project.targetChapters
      })`,
      query: null
    },
    GET: {
      body: null,
      query: `schemas.composite.pagination`
    }
  },
  
  // Characters
  'characters/route.ts': {
    POST: {
      body: `z.object({
        name: schemas.character.name,
        role: schemas.character.role,
        bio: schemas.character.bio,
        traits: schemas.character.traits,
        projectId: schemas.ids.project
      })`,
      query: null
    },
    GET: {
      body: null,
      query: `z.object({
        projectId: schemas.ids.project.optional(),
        page: schemas.numbers.page,
        limit: schemas.numbers.limit
      })`
    }
  },
  
  // User profile
  'profiles/user/route.ts': {
    PATCH: {
      body: `z.object({
        displayName: schemas.user.displayName.optional(),
        bio: schemas.user.bio,
        avatarUrl: schemas.strings.url.optional(),
        settings: schemas.composite.metadata
      })`,
      query: null
    }
  },
  
  // AI generation
  'ai/chat/route.ts': {
    POST: {
      body: `z.object({
        messages: z.array(z.object({
          role: z.enum(['user', 'assistant', 'system']),
          content: z.string()
        })),
        options: schemas.composite.aiOptions.optional()
      })`,
      query: null
    }
  },
  
  // Search
  'search/semantic/route.ts': {
    POST: {
      body: `schemas.composite.search`,
      query: null
    }
  },
  
  // Analytics
  'analytics/productivity/route.ts': {
    GET: {
      body: null,
      query: `z.object({
        startDate: schemas.dates.date,
        endDate: schemas.dates.date,
        granularity: z.enum(['daily', 'weekly', 'monthly']).optional()
      })`
    }
  },
  
  // Achievements
  'achievements/check/route.ts': {
    POST: {
      body: `z.object({
        userId: schemas.ids.user,
        action: z.string(),
        metadata: schemas.composite.metadata
      })`,
      query: null
    }
  },
  
  // Email preferences
  'email/preferences/route.ts': {
    PATCH: {
      body: `z.object({
        achievement_unlocked: z.boolean().optional(),
        writing_streak: z.boolean().optional(),
        weekly_summary: z.boolean().optional(),
        collaboration_invite: z.boolean().optional(),
        system_updates: z.boolean().optional()
      })`,
      query: null
    }
  }
};

// Template for adding validation
const validationTemplate = (method, bodySchema, querySchema) => {
  let validation = '';
  
  if (bodySchema) {
    validation += `
    // Parse request body
    const body = await request.json()
    const validatedBody = ${bodySchema}.parse(body)
`;
  }
  
  if (querySchema) {
    validation += `
    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const queryData = Object.fromEntries(searchParams.entries())
    const validatedQuery = ${querySchema}.parse(queryData)
`;
  }
  
  return validation;
};

// Process files
let updatedCount = 0;
let skippedCount = 0;
let errorCount = 0;

for (const [routePath, methods] of Object.entries(routeValidationMap)) {
  const fullPath = path.join(process.cwd(), 'src/app/api', routePath);
  
  try {
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ Not found: ${routePath}`);
      errorCount++;
      continue;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    const originalContent = content;
    
    // Skip if already has Zod validation
    if (content.includes('.parse(') || content.includes('z.object')) {
      console.log(`⏭️  Skipped: ${routePath} (already has validation)`);
      skippedCount++;
      continue;
    }
    
    // Add imports if needed
    if (!content.includes('import { z }') && !content.includes('import * as z')) {
      // Find the last import
      const lastImportMatch = content.match(/import.*from.*\n/g);
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1];
        const lastImportIndex = content.lastIndexOf(lastImport);
        content = content.substring(0, lastImportIndex + lastImport.length) +
          `import { z } from 'zod'\n` +
          `import { schemas } from '@/lib/validation/unified-schemas'\n` +
          content.substring(lastImportIndex + lastImport.length);
      }
    }
    
    // Add validation to each method
    let modified = false;
    for (const [method, schemas] of Object.entries(methods)) {
      const methodRegex = new RegExp(`export\\s+(const|async\\s+function)\\s+${method}[\\s\\S]*?{`, 'g');
      const methodMatch = content.match(methodRegex);
      
      if (methodMatch) {
        const validation = validationTemplate(method, schemas.body, schemas.query);
        if (validation) {
          // Find the opening brace of the method
          const methodIndex = content.indexOf(methodMatch[0]);
          const braceIndex = methodIndex + methodMatch[0].length;
          
          // Insert validation after try {
          const tryIndex = content.indexOf('try {', braceIndex);
          if (tryIndex !== -1) {
            content = content.substring(0, tryIndex + 5) + validation + content.substring(tryIndex + 5);
            modified = true;
            
            // Update variable names in the rest of the method
            if (schemas.body) {
              content = content.replace(/const\s+{\s*([^}]+)\s*}\s*=\s+await\s+request\.json\(\)/g, 
                'const { $1 } = validatedBody');
            }
            if (schemas.query) {
              content = content.replace(/searchParams\.get\(/g, 'validatedQuery.');
            }
          }
        }
      }
    }
    
    if (modified) {
      fs.writeFileSync(fullPath, content);
      console.log(`✅ Updated: ${routePath}`);
      updatedCount++;
    } else {
      console.log(`⏭️  Skipped: ${routePath} (no matching patterns)`);
      skippedCount++;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${routePath}:`, error.message);
    errorCount++;
  }
}

// Create validation guide
const validationGuide = `# API Validation Guide

## Overview
All API endpoints should validate incoming data using Zod schemas from \`@/lib/validation/unified-schemas\`.

## Example Implementation

\`\`\`typescript
import { NextRequest } from 'next/server'
import { z } from 'zod'
import { schemas } from '@/lib/validation/unified-schemas'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

// Define request schema
const createProjectSchema = z.object({
  name: schemas.project.name,
  description: schemas.strings.description,
  genre: schemas.project.genre,
  settings: schemas.composite.metadata
})

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    // Parse and validate request body
    const body = await request.json()
    const validatedData = createProjectSchema.parse(body)
    
    // Use validated data
    const project = await createProject(validatedData)
    
    return UnifiedResponse.created(project)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return UnifiedResponse.validationError(error)
    }
    return UnifiedResponse.error({
      message: error.message,
      code: 'CREATE_PROJECT_ERROR'
    })
  }
})
\`\`\`

## Common Patterns

### Query Parameters
\`\`\`typescript
const querySchema = z.object({
  page: schemas.numbers.page,
  limit: schemas.numbers.limit,
  sortBy: z.string().optional(),
  sortOrder: schemas.enums.sortOrder
})

const { searchParams } = new URL(request.url)
const queryData = Object.fromEntries(searchParams.entries())
const validatedQuery = querySchema.parse(queryData)
\`\`\`

### Optional Fields
\`\`\`typescript
const updateSchema = z.object({
  name: schemas.strings.name.optional(),
  description: schemas.strings.description,
  settings: schemas.composite.metadata
}).partial() // Makes all fields optional
\`\`\`

### File Uploads
\`\`\`typescript
const uploadSchema = z.object({
  file: schemas.file.image,
  projectId: schemas.ids.project
})
\`\`\`

## Best Practices
1. Always validate input data before processing
2. Use predefined schemas from unified-schemas.ts
3. Return proper validation errors using UnifiedResponse.validationError()
4. Type your validated data for better TypeScript support
5. Use .partial() for update endpoints where all fields are optional
`;

fs.writeFileSync(
  path.join(process.cwd(), 'docs/api/validation-guide.md'),
  validationGuide
);

// Summary
console.log('\n📊 Summary:');
console.log(`   Files updated: ${updatedCount}`);
console.log(`   Files skipped: ${skippedCount}`);
console.log(`   Errors: ${errorCount}`);
console.log(`   Total processed: ${Object.keys(routeValidationMap).length}`);

console.log('\n📝 Next Steps:');
console.log('1. Review the changes made to API routes');
console.log('2. Test the validation on each endpoint');
console.log('3. Add validation to remaining endpoints manually');
console.log('4. See docs/api/validation-guide.md for implementation guide');

console.log('\n✅ Zod validation script completed!');