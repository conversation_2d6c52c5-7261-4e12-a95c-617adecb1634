/**
 * Centralized Error Messages Configuration
 * Provides consistent, user-friendly error messages throughout the application
 */

export const ERROR_MESSAGES = {
  // Authentication Errors
  AUTH: {
    UNAUTHORIZED: 'Please sign in to continue',
    SESSION_EXPIRED: 'Your session has expired. Please sign in again',
    INVALID_CREDENTIALS: 'Invalid email or password',
    EMAIL_NOT_VERIFIED: 'Please verify your email address before signing in',
    ACCOUNT_DISABLED: 'Your account has been disabled. Please contact support',
    SIGNUP_FAILED: 'Unable to create account. Please try again',
    PASSWORD_RESET_FAILED: 'Unable to reset password. Please try again',
    SOCIAL_LOGIN_FAILED: 'Unable to sign in with social provider',
    INSUFFICIENT_PERMISSIONS: 'You do not have permission to perform this action',
  },

  // Database Errors
  DATABASE: {
    CONNECTION_FAILED: 'Unable to connect to database. Please try again later',
    QUERY_FAILED: 'Database operation failed. Please try again',
    TRANSACTION_FAILED: 'Unable to complete operation. Please try again',
    RECORD_NOT_FOUND: 'The requested item could not be found',
    DUPLICATE_ENTRY: 'This item already exists',
    CONSTRAINT_VIOLATION: 'Unable to complete operation due to data constraints',
    SAVE_FAILED: 'Unable to save changes. Please try again',
    DELETE_FAILED: 'Unable to delete item. Please try again',
  },

  // API Errors
  API: {
    NETWORK_ERROR: 'Network error. Please check your connection',
    TIMEOUT: 'Request timed out. Please try again',
    SERVER_ERROR: 'Server error. Our team has been notified',
    BAD_REQUEST: 'Invalid request. Please check your input',
    NOT_FOUND: 'The requested resource was not found',
    RATE_LIMIT: 'Too many requests. Please slow down',
    MAINTENANCE: 'Service is under maintenance. Please try again later',
    INVALID_RESPONSE: 'Received invalid response from server',
  },

  // AI Service Errors
  AI: {
    GENERATION_FAILED: 'AI generation failed. Please try again',
    TOKEN_LIMIT_EXCEEDED: 'Content is too long. Please reduce the input size',
    RATE_LIMIT_EXCEEDED: 'AI request limit reached. Please wait a moment',
    SERVICE_UNAVAILABLE: 'AI service is temporarily unavailable',
    INVALID_PROMPT: 'Invalid prompt. Please check your input',
    QUALITY_CHECK_FAILED: 'Generated content did not meet quality standards',
    CONTEXT_TOO_LARGE: 'Context is too large. Some information will be summarized',
    MODEL_NOT_AVAILABLE: 'Selected AI model is not available',
  },

  // Content Errors
  CONTENT: {
    INVALID_FORMAT: 'Invalid content format',
    TOO_SHORT: 'Content is too short. Please add more details',
    TOO_LONG: 'Content exceeds maximum length',
    PROFANITY_DETECTED: 'Content contains inappropriate language',
    SPAM_DETECTED: 'Content appears to be spam',
    COPYRIGHT_VIOLATION: 'Content may violate copyright',
    SAVE_FAILED: 'Unable to save content. Please try again',
    LOAD_FAILED: 'Unable to load content. Please refresh the page',
  },

  // File/Export Errors
  FILE: {
    UPLOAD_FAILED: 'File upload failed. Please try again',
    INVALID_TYPE: 'Invalid file type. Please check supported formats',
    SIZE_EXCEEDED: 'File size exceeds maximum limit',
    EXPORT_FAILED: 'Export failed. Please try again',
    IMPORT_FAILED: 'Import failed. Please check file format',
    PROCESSING_FAILED: 'File processing failed',
    STORAGE_FULL: 'Storage quota exceeded. Please upgrade your plan',
    CORRUPTED: 'File appears to be corrupted',
  },

  // Validation Errors
  VALIDATION: {
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Please enter a valid email address',
    INVALID_URL: 'Please enter a valid URL',
    INVALID_PHONE: 'Please enter a valid phone number',
    PASSWORD_TOO_SHORT: 'Password must be at least 8 characters',
    PASSWORD_TOO_WEAK: 'Password must include uppercase, lowercase, number, and special character',
    PASSWORDS_DONT_MATCH: 'Passwords do not match',
    INVALID_DATE: 'Please enter a valid date',
    INVALID_NUMBER: 'Please enter a valid number',
    OUT_OF_RANGE: 'Value is out of allowed range',
    INVALID_CHARACTERS: 'Contains invalid characters',
  },

  // Project/Chapter Errors
  PROJECT: {
    CREATE_FAILED: 'Unable to create project. Please try again',
    UPDATE_FAILED: 'Unable to update project. Please try again',
    DELETE_FAILED: 'Unable to delete project. Please ensure all chapters are removed first',
    NOT_FOUND: 'Project not found',
    ACCESS_DENIED: 'You do not have access to this project',
    LIMIT_REACHED: 'Project limit reached. Please upgrade your plan',
    INVALID_SETTINGS: 'Invalid project settings',
    SYNC_FAILED: 'Project sync failed. Changes may not be saved',
  },

  CHAPTER: {
    CREATE_FAILED: 'Unable to create chapter. Please try again',
    UPDATE_FAILED: 'Unable to update chapter. Please try again',
    DELETE_FAILED: 'Unable to delete chapter. Please try again',
    NOT_FOUND: 'Chapter not found',
    ORDER_UPDATE_FAILED: 'Unable to reorder chapters',
    GENERATION_IN_PROGRESS: 'Chapter generation already in progress',
    WORD_LIMIT_EXCEEDED: 'Chapter exceeds maximum word count',
  },

  // Collaboration Errors
  COLLABORATION: {
    INVITE_FAILED: 'Unable to send invitation. Please try again',
    JOIN_FAILED: 'Unable to join project. Invalid or expired invitation',
    SYNC_CONFLICT: 'Sync conflict detected. Please resolve conflicts',
    OFFLINE_CHANGES: 'You have offline changes. Please sync when connected',
    PERMISSION_DENIED: 'You do not have permission for this action',
    USER_LIMIT_REACHED: 'Collaborator limit reached for this project',
    CONNECTION_LOST: 'Connection to collaboration server lost',
  },

  // Payment/Subscription Errors
  PAYMENT: {
    CARD_DECLINED: 'Card was declined. Please use a different payment method',
    INSUFFICIENT_FUNDS: 'Insufficient funds. Please use a different payment method',
    INVALID_CARD: 'Invalid card information. Please check and try again',
    SUBSCRIPTION_FAILED: 'Unable to process subscription. Please try again',
    CANCELLATION_FAILED: 'Unable to cancel subscription. Please contact support',
    INVOICE_FAILED: 'Unable to generate invoice. Please contact support',
    QUOTA_EXCEEDED: 'Usage quota exceeded. Please upgrade your plan',
    PAYMENT_REQUIRED: 'Payment required to access this feature',
  },

  // Generic Errors
  GENERIC: {
    UNKNOWN: 'An unexpected error occurred. Please try again',
    TRY_AGAIN: 'Something went wrong. Please try again',
    CONTACT_SUPPORT: 'An error occurred. Please contact support if this persists',
    REFRESH_PAGE: 'An error occurred. Please refresh the page',
    CHECK_CONNECTION: 'Please check your internet connection',
    FEATURE_UNAVAILABLE: 'This feature is currently unavailable',
    COMING_SOON: 'This feature is coming soon',
  },
} as const;

// Helper function to get nested error message
export function getErrorMessage(category: keyof typeof ERROR_MESSAGES, key: string): string {
  const categoryMessages = ERROR_MESSAGES[category];
  if (categoryMessages && key in categoryMessages) {
    return categoryMessages[key as keyof typeof categoryMessages];
  }
  return ERROR_MESSAGES.GENERIC.UNKNOWN;
}

// Error code to message mapping for common HTTP/database errors
export const ERROR_CODE_MESSAGES: Record<string, string> = {
  // HTTP Status Codes
  '400': ERROR_MESSAGES.API.BAD_REQUEST,
  '401': ERROR_MESSAGES.AUTH.UNAUTHORIZED,
  '403': ERROR_MESSAGES.AUTH.INSUFFICIENT_PERMISSIONS,
  '404': ERROR_MESSAGES.API.NOT_FOUND,
  '408': ERROR_MESSAGES.API.TIMEOUT,
  '429': ERROR_MESSAGES.API.RATE_LIMIT,
  '500': ERROR_MESSAGES.API.SERVER_ERROR,
  '503': ERROR_MESSAGES.API.MAINTENANCE,

  // Database Error Codes
  '23505': ERROR_MESSAGES.DATABASE.DUPLICATE_ENTRY,
  '23503': ERROR_MESSAGES.DATABASE.CONSTRAINT_VIOLATION,
  '42P01': ERROR_MESSAGES.DATABASE.RECORD_NOT_FOUND,
  'P0001': ERROR_MESSAGES.DATABASE.QUERY_FAILED,

  // Supabase Auth Error Codes
  'invalid_credentials': ERROR_MESSAGES.AUTH.INVALID_CREDENTIALS,
  'email_not_confirmed': ERROR_MESSAGES.AUTH.EMAIL_NOT_VERIFIED,
  'user_banned': ERROR_MESSAGES.AUTH.ACCOUNT_DISABLED,
  'session_not_found': ERROR_MESSAGES.AUTH.SESSION_EXPIRED,

  // Stripe Error Codes
  'card_declined': ERROR_MESSAGES.PAYMENT.CARD_DECLINED,
  'insufficient_funds': ERROR_MESSAGES.PAYMENT.INSUFFICIENT_FUNDS,
  'invalid_card_type': ERROR_MESSAGES.PAYMENT.INVALID_CARD,
  'payment_intent_authentication_failure': ERROR_MESSAGES.PAYMENT.CARD_DECLINED,
} as const;

// Function to get user-friendly error message from error object
export function getUserFriendlyError(error: unknown): string {
  if (!error) return ERROR_MESSAGES.GENERIC.UNKNOWN;

  // Handle string errors
  if (typeof error === 'string') {
    return ERROR_CODE_MESSAGES[error] || error;
  }

  // Handle error objects
  if (error instanceof Error) {
    // Check for specific error codes
    const errorWithCode = error as Error & { code?: string; status?: number };
    
    if (errorWithCode.code) {
      const codeMessage = ERROR_CODE_MESSAGES[errorWithCode.code];
      if (codeMessage) return codeMessage;
    }

    if (errorWithCode.status) {
      const statusMessage = ERROR_CODE_MESSAGES[errorWithCode.status.toString()];
      if (statusMessage) return statusMessage;
    }

    // Check for network errors
    if (error.message.toLowerCase().includes('network')) {
      return ERROR_MESSAGES.API.NETWORK_ERROR;
    }

    if (error.message.toLowerCase().includes('timeout')) {
      return ERROR_MESSAGES.API.TIMEOUT;
    }

    // Return the error message if it seems user-friendly
    if (error.message.length < 100 && !error.message.includes('Error:')) {
      return error.message;
    }
  }

  // Default to generic error
  return ERROR_MESSAGES.GENERIC.UNKNOWN;
}

// Function to format error for logging (includes technical details)
export function formatErrorForLogging(error: unknown): Record<string, unknown> {
  if (!error) return { message: 'Unknown error', type: 'unknown' };

  if (typeof error === 'string') {
    return { message: error, type: 'string' };
  }

  if (error instanceof Error) {
    const errorObj: Record<string, unknown> = {
      message: error.message,
      type: error.constructor.name,
      stack: error.stack,
    };

    // Include any additional properties
    Object.keys(error).forEach(key => {
      if (key !== 'message' && key !== 'stack') {
        errorObj[key] = (error as any)[key];
      }
    });

    return errorObj;
  }

  // Handle non-Error objects
  if (typeof error === 'object' && error !== null) {
    return { ...error, type: 'object' };
  }

  return { message: String(error), type: typeof error };
}