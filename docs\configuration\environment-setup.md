# BookScribe Configuration & Environment Setup Guide

## Overview

BookScribe uses a comprehensive configuration system built on environment variables, TypeScript validation, and modular configuration files. The system supports multiple environments (development, staging, production) and includes a demo mode for quick setup without external services.

## Quick Start

### 1. Minimal Setup (Demo Mode)

For a quick start without external services:

```bash
# Create .env.local file
cp .env.example.clean .env.local

# Add to .env.local
NEXT_PUBLIC_DEMO_MODE=true
NEXT_PUBLIC_SUPABASE_URL=https://demo.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=demo_key
SUPABASE_SERVICE_ROLE_KEY=demo_key

# Start the application
npm run dev
```

### 2. Full Setup

For production-ready setup with all features:

```bash
# Copy example file
cp .env.example.clean .env.local

# Fill in your actual credentials in .env.local
# See "Environment Variables" section below for details
```

## Environment Variables

### Required Variables

#### Supabase (Database & Auth)
```bash
# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co

# Public anonymous key (safe for client-side)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Service role key (server-side only - keep secret!)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

#### OpenAI (AI Features)
```bash
# Your OpenAI API key
OPENAI_API_KEY=sk-your_openai_api_key
```

### Optional Services

#### Stripe (Payments)
```bash
# Stripe API keys
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Stripe Price IDs for subscription tiers
STRIPE_PRICE_ID_BASIC=price_basic_monthly
STRIPE_PRICE_ID_PRO=price_pro_monthly
STRIPE_PRICE_ID_ENTERPRISE=price_enterprise_monthly
```

#### Alternative AI Providers
```bash
# Google Gemini (fallback AI)
GEMINI_API_KEY=your_google_gemini_key
GENKIT_API_KEY=your_genkit_key  # Alternative env var

# xAI (additional fallback)
XAI_API_KEY=your_xai_api_key
NEXT_PUBLIC_AI_FALLBACK_ENABLED=true
```

#### Email Service
```bash
# Choose one email provider:

# Resend (recommended)
EMAIL_API_KEY=re_your_resend_key
EMAIL_FROM=<EMAIL>

# OR SendGrid
SENDGRID_API_KEY=SG.your_sendgrid_key
EMAIL_FROM=<EMAIL>

# OR Maileroo
MAILEROO_API_KEY=your_maileroo_key
EMAIL_FROM=<EMAIL>
```

#### Error Monitoring
```bash
# Sentry configuration
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development  # or staging, production
SENTRY_AUTH_TOKEN=your_sentry_auth_token  # For source maps
```

### Development Options

```bash
# Enable demo mode (uses mock data)
NEXT_PUBLIC_DEMO_MODE=false

# Bypass authentication (development only!)
NEXT_PUBLIC_DEV_BYPASS_AUTH=false
DEV_USER_EMAIL=<EMAIL>
DEV_USER_ID=dev-user-123

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Admin configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### Advanced Configuration

#### Rate Limiting
```bash
# API rate limits (requests per hour)
API_RATE_LIMIT_DEFAULT=100
API_RATE_LIMIT_AUTH=1000
API_RATE_LIMIT_AI=30
API_RATE_LIMIT_ANALYSIS=10

# Rate limit windows (milliseconds)
RATE_LIMIT_WINDOW=3600000  # 1 hour
RATE_LIMIT_WINDOW_AI=3600000

# Burst limits
BURST_LIMIT_DEFAULT=20
BURST_LIMIT_AI=5
BURST_WINDOW=60000  # 1 minute
```

#### AI Configuration
```bash
# Token limits by content type
AI_TOKEN_LIMIT_CHAPTER=10000
AI_TOKEN_LIMIT_SCENE=5000
AI_TOKEN_LIMIT_CHARACTER=6000
AI_TOKEN_LIMIT_DIALOGUE=4000

# Quality thresholds (0-100)
AI_QUALITY_EXCELLENT=95
AI_QUALITY_GOOD=90
AI_QUALITY_ACCEPTABLE=85

# Model selection
AI_MODEL_PRIMARY=gpt-4.1-2025-04-14
AI_MODEL_SECONDARY=gpt-4.1-2025-04-14
AI_MODEL_FAST=gpt-4o-mini
AI_MODEL_EMBEDDING=text-embedding-3-small

# Temperature settings
AI_TEMP_CREATIVE=0.9
AI_TEMP_BALANCED=0.7
AI_TEMP_FOCUSED=0.5

# Retry configuration
AI_RETRY_MAX=3
AI_RETRY_DELAY=1000
AI_RETRY_MAX_DELAY=30000
```

## Configuration System Architecture

### 1. Environment Validation

BookScribe uses Zod for runtime environment validation:

```typescript
// src/lib/config/index.ts
const envSchema = z.object({
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  OPENAI_API_KEY: z.string().min(1),
  // ... other validations
})
```

### 2. Configuration Files

The configuration is organized into modular files:

```
src/lib/config/
├── index.ts                 # Main configuration entry
├── environment-config.ts    # Environment-specific settings
├── ai-settings.ts          # AI model configurations
├── client-config.ts        # Client-side safe config
├── demo-config.ts          # Demo mode defaults
├── validate-env.ts         # Environment validation
└── unified-env.ts          # Unified environment access
```

### 3. Configuration Access

Access configuration throughout the application:

```typescript
import { config } from '@/lib/config'

// Access configuration values
const supabaseUrl = config.get('NEXT_PUBLIC_SUPABASE_URL')
const isDemoMode = config.isDemoMode
const isProduction = config.isProduction
```

### 4. Client-Safe Configuration

Only expose safe values to the client:

```typescript
// src/lib/config/client-config.ts
export const clientConfig = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  },
  stripe: {
    publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  },
  // Never expose sensitive keys here!
}
```

## Environment-Specific Settings

### Development Environment

```bash
# .env.local (development)
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_DEV_BYPASS_AUTH=true
NEXT_PUBLIC_DEMO_MODE=false
```

### Staging Environment

```bash
# .env.staging
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://staging.bookscribe.ai
SENTRY_ENVIRONMENT=staging
```

### Production Environment

```bash
# .env.production
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://bookscribe.ai
SENTRY_ENVIRONMENT=production
```

## Feature Flags

Control feature availability:

```typescript
// Feature flag configuration
export const FEATURES = {
  AI_FALLBACK: process.env.NEXT_PUBLIC_AI_FALLBACK_ENABLED === 'true',
  DEMO_MODE: process.env.NEXT_PUBLIC_DEMO_MODE === 'true',
  DEV_TOOLS: process.env.NODE_ENV === 'development',
  EXPERIMENTAL_FEATURES: process.env.NEXT_PUBLIC_ENABLE_EXPERIMENTAL === 'true',
}
```

## Security Best Practices

### 1. Secret Management

- **Never commit** `.env.local` or any file with real credentials
- Use `.env.example.clean` as a template only
- Store production secrets in secure vaults (Vercel, AWS Secrets Manager)
- Rotate API keys regularly

### 2. Environment Variable Prefixes

- `NEXT_PUBLIC_*` - Safe for client-side exposure
- No prefix - Server-side only (keep secret!)
- `DEV_*` - Development-only variables

### 3. Service Role Key Security

```typescript
// ❌ NEVER do this in client components
const supabase = createClient(url, SERVICE_ROLE_KEY)

// ✅ Only use in API routes or server components
import { createServerClient } from '@/lib/supabase'
const supabase = await createServerClient()
```

## Deployment Configuration

### Vercel Deployment

1. Add environment variables in Vercel dashboard:
   - Project Settings → Environment Variables
   - Add all required variables for each environment

2. Environment-specific variables:
   - Set different values for Preview/Production
   - Use Vercel's environment dropdown

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine

# Build arguments for public variables
ARG NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY

# Set as environment variables
ENV NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY

# Copy and build application
COPY . .
RUN npm ci && npm run build
```

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```
   Error: Missing or invalid environment variables: OPENAI_API_KEY
   ```
   **Solution**: Ensure all required variables are set in `.env.local`

2. **Demo Mode Not Working**
   ```
   Error: API features not available in demo mode
   ```
   **Solution**: Set `NEXT_PUBLIC_DEMO_MODE=true` and restart

3. **Build Failures**
   ```
   Error during build: Environment validation failed
   ```
   **Solution**: Build-time variables must be set or use demo mode

### Debug Configuration

Enable configuration debugging:

```typescript
// In your application startup
if (process.env.DEBUG_CONFIG === 'true') {
  console.log('Configuration loaded:', {
    isDemoMode: config.isDemoMode,
    hasOpenAI: !!config.get('OPENAI_API_KEY'),
    hasStripe: !!config.get('STRIPE_SECRET_KEY'),
  })
}
```

## Configuration Checklist

### Initial Setup
- [ ] Copy `.env.example.clean` to `.env.local`
- [ ] Set up Supabase project and add credentials
- [ ] Add OpenAI API key
- [ ] Configure optional services as needed
- [ ] Test configuration with `npm run dev`

### Production Deployment
- [ ] Set all environment variables in hosting platform
- [ ] Verify no secrets in code repository
- [ ] Test all integrations in staging
- [ ] Set up monitoring (Sentry)
- [ ] Configure rate limiting appropriately

### Security Audit
- [ ] No sensitive keys in client-side code
- [ ] Service role key only in server-side code
- [ ] All API keys are valid and scoped appropriately
- [ ] Environment variables are properly typed
- [ ] Demo mode is disabled in production

## Best Practices

1. **Use TypeScript Types**: Always type your configuration
2. **Validate Early**: Validate environment on startup
3. **Fail Fast**: Stop the application if critical config is missing
4. **Provide Defaults**: Use sensible defaults where appropriate
5. **Document Variables**: Keep `.env.example.clean` updated
6. **Separate Concerns**: Keep client and server config separate
7. **Use Feature Flags**: Control feature rollout with environment variables
8. **Monitor Configuration**: Log configuration issues in production