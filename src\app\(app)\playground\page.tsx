"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  BookOpen, 
  Brain, 
  PenTool, 
  Sparkles,
  Settings,
  FileText,
  Users,
  Globe,
  BarChart3,
  Save,
  Download,
  Share2,
  Lightbulb,
  Target,
  CheckCircle,
  ArrowRight
} from "lucide-react";
import { UnifiedProjectWizard } from "@/components/wizard/unified-project-wizard";
import { EditorInterface } from "@/components/editor/editor-interface";
import { AgentOrchestrator } from "@/components/agents/agent-orchestrator";
import { StoryBible } from "@/components/story-bible/story-bible";
import { UniverseManager } from "@/components/universe/universe-manager";
import { AnalyticsDashboard } from "@/components/analytics/analytics-dashboard";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/auth-context";
import { createClient } from "@/lib/supabase";

export default function PlaygroundPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [activeView, setActiveView] = useState<string>("welcome");
  const [playgroundProject, setPlaygroundProject] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const supabase = createClient();

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      router.push("/login?redirect=/playground");
    }
  }, [user, router]);

  // Create or load playground project
  useEffect(() => {
    if (user) {
      loadOrCreatePlaygroundProject();
    }
  }, [user]);

  const loadOrCreatePlaygroundProject = async () => {
    try {
      // Check if user has a playground project
      const { data: existingProject, error: fetchError } = await supabase
        .from('projects')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_playground', true)
        .single();

      if (existingProject) {
        setPlaygroundProject(existingProject);
      } else {
        // Create a new playground project
        const { data: newProject, error: createError } = await supabase
          .from('projects')
          .insert({
            user_id: user.id,
            name: 'My Playground Story',
            description: 'A sandbox environment to explore BookScribe AI features',
            is_playground: true,
            settings: {
              genre: 'fantasy',
              target_audience: 'adult',
              writing_style: 'descriptive',
              pov: 'third_person',
              tense: 'past'
            }
          })
          .select()
          .single();

        if (createError) throw createError;
        setPlaygroundProject(newProject);
      }
    } catch (error) {
      console.error('Error loading playground project:', error);
      toast({
        title: "Error",
        description: "Failed to load playground. Please try again.",
        variant: "destructive"
      });
    }
  };

  const saveAsRealProject = async () => {
    if (!playgroundProject) return;

    setIsSaving(true);
    try {
      // Create a copy as a real project
      const { data: realProject, error } = await supabase
        .from('projects')
        .insert({
          user_id: user.id,
          name: playgroundProject.name + ' (From Playground)',
          description: playgroundProject.description,
          is_playground: false,
          settings: playgroundProject.settings,
          content: playgroundProject.content
        })
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Success!",
        description: "Your playground story has been saved as a real project.",
      });

      // Redirect to the new project
      router.push(`/projects/${realProject.id}`);
    } catch (error) {
      console.error('Error saving project:', error);
      toast({
        title: "Error",
        description: "Failed to save project. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const features = [
    { id: "wizard", title: "Story Setup", icon: Sparkles, desc: "Configure your story with AI guidance" },
    { id: "editor", title: "Smart Editor", icon: PenTool, desc: "Write with intelligent AI assistance" },
    { id: "agents", title: "AI Agents", icon: Brain, desc: "Collaborate with specialized writing agents" },
    { id: "bible", title: "Story Bible", icon: BookOpen, desc: "Manage characters, locations, and lore" },
    { id: "universe", title: "Universe Builder", icon: Globe, desc: "Create interconnected story worlds" },
    { id: "analytics", title: "Writing Analytics", icon: BarChart3, desc: "Track your progress and insights" }
  ];

  if (!user || !playgroundProject) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Sparkles className="h-12 w-12 text-primary mx-auto mb-4 animate-pulse" />
          <p className="text-mono-lg font-mono">Setting up your playground...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card/50">
        <div className="container-wide px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-display-sm font-literary-display font-bold">
                BookScribe Playground
              </h1>
              <Badge variant="secondary" className="font-mono">
                <Sparkles className="w-3 h-3 mr-1" />
                Sandbox Mode
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard')}
              >
                Exit Playground
              </Button>
              <Button
                variant="literary"
                onClick={saveAsRealProject}
                disabled={isSaving}
              >
                <Save className="w-4 h-4 mr-2" />
                Save as Real Project
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Info Banner */}
      <div className="bg-primary/10 border-b border-primary/20">
        <div className="container-wide px-4 py-3">
          <div className="flex items-center gap-3">
            <Lightbulb className="w-5 h-5 text-primary flex-shrink-0" />
            <p className="text-mono-sm font-mono">
              <strong className="font-bold">Welcome to your playground!</strong> This is a fully functional environment where you can explore all BookScribe features. 
              Your work here is automatically saved and can be converted to a real project anytime.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container-wide px-4 py-8">
        {activeView === "welcome" ? (
          <div className="max-w-4xl mx-auto">
            {/* Welcome Section */}
            <div className="text-center mb-12">
              <h2 className="text-display-md font-literary-display mb-4">
                Explore BookScribe AI Features
              </h2>
              <p className="text-mono-lg font-mono text-muted-foreground">
                Choose any feature below to start experimenting. Everything works just like in a real project!
              </p>
            </div>

            {/* Feature Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {features.map((feature) => (
                <Card
                  key={feature.id}
                  className="cursor-pointer hover:border-primary/50 transition-all hover:shadow-lg"
                  onClick={() => setActiveView(feature.id)}
                >
                  <CardHeader>
                    <feature.icon className="w-8 h-8 text-primary mb-2" />
                    <CardTitle className="font-literary-display">{feature.title}</CardTitle>
                    <CardDescription className="font-mono text-mono-sm">
                      {feature.desc}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full" variant="outline">
                      Try it out
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Tips Section */}
            <Card className="bg-muted/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 font-literary-display">
                  <Target className="w-5 h-5 text-primary" />
                  Playground Tips
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 font-mono text-mono-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    <span>All features work exactly like in real projects - no limitations!</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    <span>Your playground content is automatically saved to the cloud</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    <span>Convert to a real project anytime to continue your story</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    <span>Perfect for testing ideas before committing to a full project</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="max-w-7xl mx-auto">
            {/* Feature Navigation */}
            <div className="flex items-center justify-between mb-6">
              <Button
                variant="ghost"
                onClick={() => setActiveView("welcome")}
                className="font-mono"
              >
                ← Back to Features
              </Button>
              <h2 className="text-display-sm font-literary-display">
                {features.find(f => f.id === activeView)?.title}
              </h2>
              <div className="w-32" /> {/* Spacer for centering */}
            </div>

            {/* Feature Content */}
            <div className="bg-card rounded-lg border p-6">
              {activeView === "wizard" && (
                <UnifiedProjectWizard
                  mode="playground"
                  initialData={playgroundProject}
                  onComplete={(data) => {
                    // Update playground project
                    setPlaygroundProject({ ...playgroundProject, ...data });
                    toast({
                      title: "Story setup updated!",
                      description: "Your playground configuration has been saved."
                    });
                  }}
                />
              )}

              {activeView === "editor" && (
                <EditorInterface
                  projectId={playgroundProject.id}
                  isPlayground={true}
                />
              )}

              {activeView === "agents" && (
                <AgentOrchestrator
                  projectId={playgroundProject.id}
                  projectSettings={playgroundProject.settings}
                  isPlayground={true}
                />
              )}

              {activeView === "bible" && (
                <StoryBible
                  projectId={playgroundProject.id}
                  isPlayground={true}
                />
              )}

              {activeView === "universe" && (
                <UniverseManager
                  projectId={playgroundProject.id}
                  isPlayground={true}
                />
              )}

              {activeView === "analytics" && (
                <AnalyticsDashboard
                  projectId={playgroundProject.id}
                  isPlayground={true}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}