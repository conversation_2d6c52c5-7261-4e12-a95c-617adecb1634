import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Get story bible entry with project ownership check
    const { data: entry, error } = await supabase
      .from('story_bible')
      .select(`
        *,
        projects!inner (
          id,
          title,
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'))
      }
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Clean up the response to remove nested project data
    const { projects: _, ...cleanEntry } = entry
    // Suppress unused variable warning for intentionally unused destructured property
    void _

    return NextResponse.json({ entry: cleanEntry })

  } catch (error) {
    logger.error('Error fetching story bible entry:', error)
    return NextResponse.json(
      { error: 'Failed to fetch story bible entry' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    const { entry_type, entry_key, entry_data, chapter_introduced, is_active } = body

    // First verify ownership through project
    const { data: existingEntry } = await supabase
      .from('story_bible')
      .select(`
        id,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (!existingEntry) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Update entry
    const updateData: {
      updated_at: string
      entry_type?: string
      entry_key?: string
      entry_data?: Record<string, unknown>
      chapter_introduced?: number | null
      is_active?: boolean
    } = { updated_at: new Date().toISOString() }
    if (entry_type !== undefined) updateData.entry_type = entry_type
    if (entry_key !== undefined) updateData.entry_key = entry_key
    if (entry_data !== undefined) updateData.entry_data = entry_data
    if (chapter_introduced !== undefined) updateData.chapter_introduced = chapter_introduced
    if (is_active !== undefined) updateData.is_active = is_active

    const { data: entry, error } = await supabase
      .from('story_bible')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      logger.error('Error updating story bible entry:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ entry })

  } catch (error) {
    logger.error('Error updating story bible entry:', error)
    return NextResponse.json(
      { error: 'Failed to update story bible entry' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // First verify ownership through project
    const { data: entry } = await supabase
      .from('story_bible')
      .select(`
        id,
        entry_key,
        entry_type,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (!entry) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Delete entry
    const { error } = await supabase
      .from('story_bible')
      .delete()
      .eq('id', id)

    if (error) {
      logger.error('Error deleting story bible entry:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: `Story bible entry "${entry.entry_key}" (${entry.entry_type}) deleted successfully`
    })

  } catch (error) {
    logger.error('Error deleting story bible entry:', error)
    return NextResponse.json(
      { error: 'Failed to delete story bible entry' },
      { status: 500 }
    )
  }
}