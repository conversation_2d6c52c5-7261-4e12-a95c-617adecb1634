import { Page } from '@playwright/test';

/**
 * Wait for real-time update to appear on the page
 */
export async function waitForRealtimeUpdate(
  page: Page,
  expectedContent: string,
  timeout: number = 5000
): Promise<void> {
  await page.waitForFunction(
    (content) => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      return editor?.textContent?.includes(content);
    },
    expectedContent,
    { timeout }
  );
}

/**
 * Measure sync latency between two pages
 */
export async function measureSyncLatency(
  sourcePage: Page,
  targetPage: Page,
  testContent: string
): Promise<number> {
  const startTime = Date.now();

  // Add unique timestamp to content to ensure uniqueness
  const uniqueContent = `${testContent}_${startTime}`;

  // Type in source page
  await sourcePage.click('[data-testid="editor-content"]');
  await sourcePage.keyboard.press('End');
  await sourcePage.type('[data-testid="editor-content"]', uniqueContent);

  // Wait for content to appear in target page
  await targetPage.waitForFunction(
    (content) => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      return editor?.textContent?.includes(content);
    },
    uniqueContent,
    { timeout: 10000 }
  );

  const endTime = Date.now();
  return endTime - startTime;
}

/**
 * Wait for presence indicator to appear
 */
export async function waitForPresenceIndicator(
  page: Page,
  userId: string,
  timeout: number = 5000
): Promise<void> {
  await page.waitForSelector(`[data-testid="presence-${userId}"]`, { timeout });
}

/**
 * Wait for cursor position to update
 */
export async function waitForCursorUpdate(
  page: Page,
  userId: string,
  expectedPosition: { x: number; y: number },
  tolerance: number = 10
): Promise<void> {
  await page.waitForFunction(
    ({ userId, expectedPos, tol }) => {
      const cursor = document.querySelector(`[data-testid="cursor-${userId}"]`);
      if (!cursor) return false;
      
      const rect = cursor.getBoundingClientRect();
      const xMatch = Math.abs(rect.left - expectedPos.x) <= tol;
      const yMatch = Math.abs(rect.top - expectedPos.y) <= tol;
      
      return xMatch && yMatch;
    },
    { userId, expectedPos: expectedPosition, tol: tolerance },
    { timeout: 5000 }
  );
}

/**
 * Simulate typing with realistic delays
 */
export async function simulateTyping(
  page: Page,
  text: string,
  minDelay: number = 50,
  maxDelay: number = 150
): Promise<void> {
  await page.click('[data-testid="editor-content"]');
  
  for (const char of text) {
    const delay = Math.random() * (maxDelay - minDelay) + minDelay;
    await page.keyboard.type(char);
    await page.waitForTimeout(delay);
  }
}

/**
 * Wait for all collaborators to sync
 */
export async function waitForAllCollaboratorsSync(
  pages: Page[],
  expectedContent: string,
  timeout: number = 10000
): Promise<void> {
  await Promise.all(
    pages.map(page => 
      page.waitForFunction(
        (content) => {
          const editor = document.querySelector('[data-testid="editor-content"]');
          return editor?.textContent === content;
        },
        expectedContent,
        { timeout }
      )
    )
  );
}

/**
 * Get current collaborator count
 */
export async function getCollaboratorCount(page: Page): Promise<number> {
  return await page.evaluate(() => {
    const indicators = document.querySelectorAll('[data-testid^="presence-"]');
    return indicators.length;
  });
}

/**
 * Wait for specific number of collaborators
 */
export async function waitForCollaboratorCount(
  page: Page,
  expectedCount: number,
  timeout: number = 5000
): Promise<void> {
  await page.waitForFunction(
    (count) => {
      const indicators = document.querySelectorAll('[data-testid^="presence-"]');
      return indicators.length === count;
    },
    expectedCount,
    { timeout }
  );
}

/**
 * Get conflict dialog if present
 */
export async function getConflictDialog(page: Page): Promise<boolean> {
  try {
    await page.waitForSelector('[data-testid="conflict-dialog"]', { timeout: 1000 });
    return true;
  } catch {
    return false;
  }
}

/**
 * Resolve conflict with specified strategy
 */
export async function resolveConflict(
  page: Page,
  strategy: 'yours' | 'theirs' | 'merge'
): Promise<void> {
  const conflictDialog = page.locator('[data-testid="conflict-dialog"]');
  
  switch (strategy) {
    case 'yours':
      await conflictDialog.locator('[data-testid="keep-yours-btn"]').click();
      break;
    case 'theirs':
      await conflictDialog.locator('[data-testid="keep-theirs-btn"]').click();
      break;
    case 'merge':
      await conflictDialog.locator('[data-testid="merge-both-btn"]').click();
      break;
  }
  
  // Wait for dialog to disappear
  await page.waitForSelector('[data-testid="conflict-dialog"]', { 
    state: 'detached',
    timeout: 5000 
  });
}

/**
 * Get activity log entries
 */
export async function getActivityLog(page: Page, count?: number): Promise<string[]> {
  await page.waitForSelector('[data-testid="activity-list"]');
  
  const activities = await page.evaluate((limit) => {
    const items = document.querySelectorAll('[data-testid="activity-item"]');
    const texts: string[] = [];
    
    const itemCount = limit ? Math.min(items.length, limit) : items.length;
    for (let i = 0; i < itemCount; i++) {
      texts.push(items[i].textContent || '');
    }
    
    return texts;
  }, count);
  
  return activities;
}

/**
 * Wait for save operation to complete
 */
export async function waitForSave(page: Page): Promise<void> {
  // Click save
  await page.click('[data-testid="save-btn"]');
  
  // Wait for success indicator
  await page.waitForSelector('[data-testid="save-success"]', { timeout: 5000 });
  
  // Wait for indicator to disappear
  await page.waitForSelector('[data-testid="save-success"]', { 
    state: 'detached',
    timeout: 3000 
  });
}