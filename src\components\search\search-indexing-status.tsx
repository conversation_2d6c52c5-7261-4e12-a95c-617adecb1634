'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Database,
  Zap
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'

interface SearchIndexingStatusProps {
  projectId: string
  className?: string
  onIndexComplete?: () => void
}

interface IndexingStatus {
  totalContent: number
  indexedContent: number
  lastIndexed: Date | null
  isComplete: boolean
}

export function SearchIndexingStatus({ 
  projectId, 
  className,
  onIndexComplete 
}: SearchIndexingStatusProps) {
  const { toast } = useToast()
  const [status, setStatus] = useState<IndexingStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isIndexing, setIsIndexing] = useState(false)
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null)

  // Fetch indexing status
  const fetchStatus = async () => {
    try {
      const response = await fetch(`/api/search/index?projectId=${projectId}`)
      if (!response.ok) throw new Error('Failed to fetch status')
      
      const data = await response.json()
      const newStatus = data.data
      setStatus(newStatus)
      
      // If indexing completed, notify
      if (status && !status.isComplete && newStatus.isComplete && onIndexComplete) {
        onIndexComplete()
      }
      
      return newStatus
    } catch (error) {
      console.error('Error fetching indexing status:', error)
      toast({
        title: 'Error',
        description: 'Failed to get indexing status',
        variant: 'destructive'
      })
      return null
    } finally {
      setIsLoading(false)
    }
  }

  // Initial load
  useEffect(() => {
    fetchStatus()
  }, [projectId])

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }
    }
  }, [pollingInterval])

  // Start indexing
  const startIndexing = async (fullReindex = false) => {
    setIsIndexing(true)
    
    try {
      const response = await fetch('/api/search/index', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId, fullReindex })
      })
      
      if (!response.ok) throw new Error('Failed to start indexing')
      
      toast({
        title: 'Indexing Started',
        description: fullReindex 
          ? 'Full reindexing in progress. This may take a few minutes.'
          : 'Incremental indexing started.'
      })
      
      // Start polling for status updates
      const interval = setInterval(() => {
        fetchStatus().then(newStatus => {
          if (newStatus?.isComplete) {
            clearInterval(interval)
            setPollingInterval(null)
            setIsIndexing(false)
          }
        })
      }, 2000) // Poll every 2 seconds
      
      setPollingInterval(interval)
    } catch (error) {
      console.error('Error starting indexing:', error)
      toast({
        title: 'Error',
        description: 'Failed to start indexing',
        variant: 'destructive'
      })
      setIsIndexing(false)
    }
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="h-4 bg-muted animate-pulse rounded" />
            <div className="h-2 bg-muted animate-pulse rounded" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!status) {
    return null
  }

  const progressPercentage = status.totalContent > 0 
    ? Math.round((status.indexedContent / status.totalContent) * 100)
    : 0

  const needsIndexing = !status.isComplete || status.indexedContent === 0

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Search Index</CardTitle>
            <CardDescription>
              Keep your content searchable and up-to-date
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {isIndexing ? (
              <Badge variant="secondary" className="gap-1">
                <RefreshCw className="w-3 h-3 animate-spin" />
                Indexing
              </Badge>
            ) : status.isComplete ? (
              <Badge variant="default" className="gap-1">
                <CheckCircle className="w-3 h-3" />
                Up to date
              </Badge>
            ) : (
              <Badge variant="outline" className="gap-1">
                <AlertCircle className="w-3 h-3" />
                Needs update
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Indexed content</span>
            <span className="font-medium">
              {status.indexedContent} / {status.totalContent} items
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Status Info */}
        <div className="grid grid-cols-2 gap-4 p-3 bg-muted/30 rounded-lg">
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Database className="w-3 h-3" />
              <span>Total content</span>
            </div>
            <p className="text-sm font-medium">{status.totalContent} items</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>Last indexed</span>
            </div>
            <p className="text-sm font-medium">
              {status.lastIndexed 
                ? new Date(status.lastIndexed).toLocaleDateString()
                : 'Never'
              }
            </p>
          </div>
        </div>

        {/* Alert for outdated index */}
        {needsIndexing && !isIndexing && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Your search index is out of date. Update it to ensure all content is searchable.
            </AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            variant={needsIndexing ? "default" : "outline"}
            size="sm"
            onClick={() => startIndexing(false)}
            disabled={isIndexing}
            className="flex-1"
          >
            {isIndexing ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Indexing...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4 mr-2" />
                Quick Update
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => startIndexing(true)}
            disabled={isIndexing}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Full Reindex
          </Button>
        </div>

        {/* Help text */}
        <p className="text-xs text-muted-foreground">
          {isIndexing 
            ? "Indexing in progress. You can continue working while this completes."
            : "Quick update indexes new content. Full reindex rebuilds the entire search index."
          }
        </p>
      </CardContent>
    </Card>
  )
}