# BookScribe Writing Sessions Tracking System

## Overview

The Writing Sessions Tracking System monitors and analyzes individual writing sessions to provide insights into productivity, writing habits, and progress. It captures detailed metrics about when, how long, and how productively users write, enabling personalized recommendations and goal tracking.

## Architecture

### Database Schema

#### Writing Sessions Table
Core session tracking:

```sql
writing_sessions:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects
  - chapter_id: UUID - References chapters (optional)
  - session_type: VARCHAR(50) - writing, editing, planning, revision
  - word_count: INTEGER - Words written in session
  - character_count: INTEGER - Characters typed
  - words_deleted: INTEGER - Words removed
  - net_word_change: INTEGER - Final word count change
  - duration: INTEGER - Session duration in seconds
  - active_duration: INTEGER - Active writing time
  - idle_duration: INTEGER - Idle time
  - started_at: TIMESTAMPTZ - Session start time
  - ended_at: TIMESTAMPTZ - Session end time
  - timezone: VARCHAR(50) - User timezone
  - device_type: VARCHAR(50) - desktop, tablet, mobile
  - location_hash: VARCHAR(64) - Anonymized location
  - mood_rating: INTEGER - 1-5 user mood (optional)
  - productivity_rating: INTEGER - 1-5 self-assessment
  - distractions_count: INTEGER - Times left editor
  - ai_usage_count: INTEGER - AI features used
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Session Segments Table
Detailed session breakdown:

```sql
session_segments:
  - id: UUID (Primary Key)
  - session_id: UUID - References writing_sessions
  - segment_type: VARCHAR(50) - writing_burst, thinking, editing, idle
  - start_time: TIMESTAMPTZ
  - end_time: TIMESTAMPTZ
  - duration_seconds: INTEGER
  - words_written: INTEGER
  - wpm_rate: FLOAT - Words per minute
  - keystrokes: INTEGER
  - deletions: INTEGER
  - ai_interactions: INTEGER
  - created_at: TIMESTAMPTZ
```

#### Writing Streaks Table
Track consecutive writing days:

```sql
writing_streaks:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects (optional)
  - streak_type: VARCHAR(50) - daily, weekly, project
  - current_streak: INTEGER - Current streak count
  - longest_streak: INTEGER - Best streak achieved
  - streak_start_date: DATE
  - last_writing_date: DATE
  - total_streak_days: INTEGER - Lifetime total
  - is_active: BOOLEAN
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Session Goals Table
Session-specific targets:

```sql
session_goals:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - session_id: UUID - References writing_sessions
  - goal_type: VARCHAR(50) - word_count, duration, chapter_completion
  - target_value: INTEGER
  - achieved_value: INTEGER
  - is_achieved: BOOLEAN
  - achievement_time: TIMESTAMPTZ
  - created_at: TIMESTAMPTZ
```

## Session Tracking Features

### 1. Real-time Session Monitoring
Live tracking during writing:

```typescript
interface LiveSession {
  session_id: string;
  start_time: Date;
  current_duration: number;
  words_written: number;
  current_wpm: number;
  activity_status: 'active' | 'idle' | 'thinking';
  last_keystroke: Date;
  
  segments: SessionSegment[];
  milestones: {
    hundred_words: Date[];
    thousand_words: Date[];
    hour_marks: Date[];
  };
  
  real_time_stats: {
    avg_wpm: number;
    peak_wpm: number;
    productivity_score: number;
    focus_score: number;
  };
}
```

### 2. Productivity Metrics
Detailed productivity analysis:

```typescript
interface ProductivityMetrics {
  words_per_minute: {
    current: number;
    session_avg: number;
    historical_avg: number;
    percentile: number; // Compared to other users
  };
  
  flow_state: {
    flow_periods: FlowPeriod[];
    total_flow_time: number;
    flow_percentage: number;
    avg_flow_duration: number;
  };
  
  efficiency: {
    gross_wpm: number; // Including deleted words
    net_wpm: number; // Final words only
    revision_ratio: number; // Deletions / total typed
    quality_score: number; // Based on revision patterns
  };
  
  distraction_metrics: {
    focus_periods: number;
    avg_focus_duration: number;
    distraction_events: number;
    recovery_time: number; // Time to resume after distraction
  };
}
```

### 3. Writing Patterns
Behavioral pattern analysis:

```typescript
interface WritingPatterns {
  temporal_patterns: {
    best_time_of_day: string;
    best_day_of_week: string;
    seasonal_trends: SeasonalTrend[];
    consistency_score: number;
  };
  
  session_patterns: {
    avg_session_length: number;
    preferred_session_length: number;
    warm_up_time: number; // Time to reach peak productivity
    fatigue_point: number; // When productivity drops
  };
  
  burst_patterns: {
    burst_writer: boolean;
    avg_burst_length: number;
    avg_burst_words: number;
    burst_frequency: number;
    recovery_time: number;
  };
  
  environmental_patterns: {
    location_productivity: { [location: string]: number };
    device_preferences: { [device: string]: number };
    mood_correlation: number; // Mood vs productivity
  };
}
```

## API Endpoints

### Session Management

#### POST /api/writing/sessions
Start a new writing session:

```typescript
// Request
{
  project_id: "uuid",
  chapter_id: "uuid",
  session_type: "writing",
  initial_word_count: 5420,
  goals: [
    { type: "word_count", target: 1000 },
    { type: "duration", target: 3600 }
  ]
}

// Response
{
  session: {
    id: "uuid",
    started_at: "2024-01-15T10:30:00Z",
    tracking_enabled: true,
    goals: [
      {
        id: "uuid",
        type: "word_count",
        target: 1000,
        current: 0,
        percentage: 0
      }
    ]
  }
}
```

#### PUT /api/writing/sessions/{id}
Update session progress:

```typescript
// Request
{
  current_word_count: 5920,
  words_deleted: 50,
  ai_usage_count: 2,
  activity_status: "active"
}

// Response
{
  session: {
    id: "uuid",
    duration: 1800,
    words_written: 500,
    net_change: 450,
    current_wpm: 15,
    goals_progress: [
      {
        type: "word_count",
        current: 500,
        target: 1000,
        percentage: 50
      }
    ]
  }
}
```

#### POST /api/writing/sessions/{id}/end
End writing session:

```typescript
// Request
{
  final_word_count: 6420,
  mood_rating: 4,
  productivity_rating: 5,
  notes: "Great flow state today"
}

// Response
{
  session_summary: {
    id: "uuid",
    duration: 3650,
    words_written: 1000,
    avg_wpm: 16.4,
    goals_achieved: ["word_count"],
    productivity_score: 0.85,
    achievements: ["1000_words_session", "maintained_flow"],
    insights: [
      "You write 25% faster in morning sessions",
      "Your productivity increased after using the outline feature"
    ]
  }
}
```

### Analytics Endpoints

#### GET /api/writing/sessions/analytics
Get session analytics:

```typescript
// Request
GET /api/writing/sessions/analytics?user_id=uuid&date_range=last_30_days

// Response
{
  overview: {
    total_sessions: 25,
    total_writing_time: 2850, // minutes
    total_words: 38500,
    avg_session_length: 114, // minutes
    avg_words_per_session: 1540,
    writing_days: 22
  },
  productivity: {
    avg_wpm: 13.5,
    peak_wpm: 28,
    flow_state_percentage: 0.35,
    efficiency_score: 0.78
  },
  patterns: {
    best_time: "9:00-11:00 AM",
    best_day: "Saturday",
    optimal_session_length: 120, // minutes
    consistency_score: 0.88
  },
  streaks: {
    current: 7,
    longest: 15,
    total_streak_days: 125
  }
}
```

#### GET /api/writing/sessions/insights
Get personalized insights:

```typescript
// Response
{
  insights: [
    {
      type: "productivity_tip",
      title: "Morning Magic",
      description: "You write 40% more words in morning sessions",
      data: {
        morning_avg: 1850,
        afternoon_avg: 1320,
        evening_avg: 980
      },
      recommendation: "Schedule important writing for mornings"
    },
    {
      type: "pattern_discovery",
      title: "Outline Advantage",
      description: "Sessions with outlines are 30% more productive",
      data: {
        with_outline_wpm: 18,
        without_outline_wpm: 14
      },
      recommendation: "Create chapter outlines before writing"
    }
  ]
}
```

## Session Tracking Implementation

### Client-side Tracker
```typescript
class SessionTracker {
  private session: WritingSession;
  private activityMonitor: ActivityMonitor;
  private metricsCollector: MetricsCollector;
  
  startSession(config: SessionConfig): void {
    this.session = new WritingSession(config);
    
    // Start monitoring
    this.activityMonitor.start({
      idleThreshold: 30000, // 30 seconds
      onActive: () => this.recordActivity('active'),
      onIdle: () => this.recordActivity('idle')
    });
    
    // Track metrics
    this.metricsCollector.start({
      wordCountInterval: 5000, // Check every 5 seconds
      onWordCountChange: (count) => this.updateWordCount(count),
      onKeystroke: () => this.recordKeystroke()
    });
    
    // Auto-save progress
    this.startAutoSave();
  }
  
  private recordSegment(): void {
    if (this.currentSegment.duration > 60) {
      const segment = {
        type: this.determineSegmentType(),
        duration: this.currentSegment.duration,
        words_written: this.currentSegment.words,
        wpm_rate: this.calculateWPM()
      };
      
      this.session.addSegment(segment);
      this.startNewSegment();
    }
  }
}
```

### Flow State Detection
```typescript
class FlowStateDetector {
  detectFlowState(metrics: SessionMetrics): FlowState {
    const indicators = {
      consistent_wpm: this.checkWPMConsistency(metrics),
      minimal_deletions: metrics.deletion_ratio < 0.1,
      sustained_activity: metrics.idle_percentage < 0.05,
      high_productivity: metrics.wpm > metrics.historical_avg * 1.2
    };
    
    const flowScore = this.calculateFlowScore(indicators);
    
    return {
      in_flow: flowScore > 0.7,
      flow_score: flowScore,
      duration: this.calculateFlowDuration(metrics),
      quality: this.assessFlowQuality(indicators)
    };
  }
}
```

## Visualization Components

### Session Timeline
```tsx
<SessionTimeline
  session={currentSession}
  showSegments={true}
  showMilestones={true}
  showProductivity={true}
  realTime={true}
  onSegmentClick={handleSegmentClick}
/>
```

### Productivity Dashboard
```tsx
<ProductivityDashboard
  sessions={recentSessions}
  timeRange="last_30_days"
  metrics={['wpm', 'word_count', 'flow_time', 'efficiency']}
  showComparisons={true}
  showGoals={true}
/>
```

### Writing Calendar
```tsx
<WritingCalendar
  userId={userId}
  showStreaks={true}
  showWordCounts={true}
  showMood={true}
  onDayClick={handleDayClick}
  highlightAchievements={true}
/>
```

### Session Heatmap
```tsx
<SessionHeatmap
  data={sessionData}
  metric="productivity" | "word_count" | "duration"
  groupBy="hour" | "day" | "week"
  showTrends={true}
  interactive={true}
/>
```

## Gamification & Achievements

### Session-based Achievements
```typescript
interface SessionAchievements {
  milestones: {
    first_hundred_words: Date;
    first_thousand_words: Date;
    first_hour: Date;
    flow_state_achieved: Date;
  };
  
  records: {
    longest_session: number;
    most_productive_session: number;
    highest_wpm: number;
    longest_flow_state: number;
  };
  
  badges: {
    early_bird: boolean; // Writing before 6 AM
    night_owl: boolean; // Writing after midnight
    marathon_writer: boolean; // 4+ hour session
    speed_demon: boolean; // 30+ WPM sustained
    consistency_champion: boolean; // 30-day streak
  };
}
```

### Progress Tracking
```typescript
interface ProgressTracking {
  daily_progress: {
    target: number;
    achieved: number;
    percentage: number;
    time_remaining: number;
  };
  
  weekly_progress: {
    days_written: number;
    total_words: number;
    avg_per_day: number;
    on_track: boolean;
  };
  
  project_progress: {
    sessions_count: number;
    total_time: number;
    words_written: number;
    completion_estimate: Date;
  };
}
```

## Integration Features

### AI Writing Coach
```typescript
interface WritingCoach {
  session_feedback: {
    productivity_analysis: string;
    improvement_suggestions: string[];
    motivational_message: string;
  };
  
  real_time_tips: {
    trigger: 'low_productivity' | 'distraction' | 'fatigue';
    tip: string;
    action_suggestion: string;
  };
  
  session_recommendations: {
    optimal_time: string;
    suggested_duration: number;
    warm_up_exercise: string;
    goal_suggestion: SessionGoal;
  };
}
```

### Goal Integration
```typescript
interface SessionGoalTracking {
  connect_to_daily_goals: boolean;
  connect_to_project_goals: boolean;
  
  goal_progress: {
    session_contribution: number;
    daily_remaining: number;
    weekly_pace: 'ahead' | 'on_track' | 'behind';
  };
  
  adaptive_goals: {
    adjust_based_on_patterns: boolean;
    suggest_realistic_targets: boolean;
    celebrate_overachievement: boolean;
  };
}
```

## Performance Optimization

### Data Aggregation
```typescript
interface SessionAggregation {
  real_time: {
    update_interval: 5000, // 5 seconds
    metrics: ['word_count', 'duration', 'activity']
  };
  
  segment_creation: {
    min_duration: 60, // seconds
    merge_threshold: 30 // seconds between segments
  };
  
  batch_analytics: {
    calculation_interval: 300000, // 5 minutes
    background_processing: true
  };
}
```

### Storage Optimization
```sql
-- Partition sessions by month for better performance
CREATE TABLE writing_sessions_2024_01 PARTITION OF writing_sessions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Index for common queries
CREATE INDEX idx_sessions_user_project_date 
ON writing_sessions(user_id, project_id, started_at DESC);

CREATE INDEX idx_sessions_productivity 
ON writing_sessions(user_id, word_count, duration)
WHERE word_count > 0;
```

## Privacy & Settings

### Session Privacy
```typescript
interface SessionPrivacy {
  tracking_enabled: boolean;
  share_analytics: boolean;
  location_tracking: boolean;
  
  data_retention: {
    detailed_sessions: 90, // days
    aggregated_data: 365, // days
    anonymous_analytics: 'indefinite'
  };
  
  export_options: {
    format: 'csv' | 'json';
    include_segments: boolean;
    include_analytics: boolean;
  };
}
```

## Future Enhancements

1. **Biometric Integration**
   - Heart rate variability for flow state
   - Eye tracking for focus analysis
   - Stress level monitoring

2. **Environmental Factors**
   - Weather correlation
   - Music/ambient sound tracking
   - Calendar integration

3. **Collaborative Sessions**
   - Co-writing tracking
   - Productivity comparison
   - Shared goals

4. **Advanced Analytics**
   - Predictive productivity
   - Burnout prevention
   - Optimal scheduling AI

## Related Systems
- Writing Goals System
- Analytics System
- Achievement System
- Behavioral Analytics System