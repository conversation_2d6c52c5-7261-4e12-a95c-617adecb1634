import { test, expect, Page } from '@playwright/test';
import { createTestUser, deleteTestUser, TestUser } from '../helpers/test-users';
import { createTestProject, deleteTestProject, TestProject } from '../helpers/test-projects';
import { waitForRealtimeUpdate } from '../helpers/realtime-helpers';

describe('Project Sharing E2E Tests', () => {
  let ownerPage: Page;
  let collaboratorPage: Page;
  let owner: TestUser;
  let collaborator: TestUser;
  let project: TestProject;

  test.beforeAll(async ({ browser }) => {
    // Create test users
    owner = await createTestUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Project Owner'
    });

    collaborator = await createTestUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Collaborator User'
    });

    // Create pages for each user
    const ownerContext = await browser.newContext();
    const collaboratorContext = await browser.newContext();
    
    ownerPage = await ownerContext.newPage();
    collaboratorPage = await collaboratorContext.newPage();

    // Login both users
    await loginUser(ownerPage, owner);
    await loginUser(collaboratorPage, collaborator);

    // Create test project
    project = await createTestProject({
      title: 'Collaborative Story',
      description: 'A story for collaboration testing',
      ownerId: owner.id
    });
  });

  test.afterAll(async () => {
    // Cleanup
    await deleteTestProject(project.id);
    await deleteTestUser(owner.id);
    await deleteTestUser(collaborator.id);
    await ownerPage.close();
    await collaboratorPage.close();
  });

  test('should send and accept project invitation', async () => {
    // Owner navigates to project settings
    await ownerPage.goto(`/projects/${project.id}/settings`);
    await ownerPage.waitForSelector('[data-testid="collaborators-section"]');

    // Click add collaborator button
    await ownerPage.click('[data-testid="add-collaborator-btn"]');
    
    // Enter collaborator email
    await ownerPage.fill('[data-testid="collaborator-email-input"]', collaborator.email);
    
    // Select role
    await ownerPage.selectOption('[data-testid="collaborator-role-select"]', 'editor');
    
    // Send invitation
    await ownerPage.click('[data-testid="send-invitation-btn"]');
    
    // Wait for success message
    await expect(ownerPage.locator('[data-testid="invitation-sent-success"]')).toBeVisible();

    // Collaborator checks invitations
    await collaboratorPage.goto('/invitations');
    await collaboratorPage.waitForSelector('[data-testid="invitation-list"]');

    // Find invitation from owner
    const invitation = collaboratorPage.locator(`[data-testid="invitation-from-${owner.email}"]`);
    await expect(invitation).toBeVisible();

    // Accept invitation
    await invitation.locator('[data-testid="accept-invitation-btn"]').click();
    
    // Wait for redirect to project
    await collaboratorPage.waitForURL(`/projects/${project.id}`);
    
    // Verify project is accessible
    await expect(collaboratorPage.locator('[data-testid="project-title"]')).toContainText('Collaborative Story');
  });

  test('should show real-time presence indicators', async () => {
    // Both users navigate to the same project
    await ownerPage.goto(`/projects/${project.id}`);
    await collaboratorPage.goto(`/projects/${project.id}`);

    // Wait for presence indicators to appear
    await ownerPage.waitForSelector('[data-testid="presence-indicators"]');
    await collaboratorPage.waitForSelector('[data-testid="presence-indicators"]');

    // Owner should see collaborator's presence
    const collaboratorPresence = ownerPage.locator(`[data-testid="presence-${collaborator.id}"]`);
    await expect(collaboratorPresence).toBeVisible();
    await expect(collaboratorPresence).toContainText('Collaborator User');

    // Collaborator should see owner's presence
    const ownerPresence = collaboratorPage.locator(`[data-testid="presence-${owner.id}"]`);
    await expect(ownerPresence).toBeVisible();
    await expect(ownerPresence).toContainText('Project Owner');

    // Test cursor tracking
    await collaboratorPage.click('[data-testid="editor-content"]');
    
    // Owner should see collaborator's cursor
    await expect(ownerPage.locator(`[data-testid="cursor-${collaborator.id}"]`)).toBeVisible();
  });

  test('should handle concurrent editing with conflict resolution', async () => {
    // Both users navigate to editor
    await ownerPage.goto(`/projects/${project.id}/editor`);
    await collaboratorPage.goto(`/projects/${project.id}/editor`);

    // Wait for editor to load
    await ownerPage.waitForSelector('[data-testid="editor-content"]');
    await collaboratorPage.waitForSelector('[data-testid="editor-content"]');

    // Owner starts typing
    await ownerPage.click('[data-testid="editor-content"]');
    await ownerPage.type('[data-testid="editor-content"]', 'Chapter 1: The Beginning\n\nIt was a dark and stormy night.');

    // Collaborator types in same area (creating potential conflict)
    await collaboratorPage.click('[data-testid="editor-content"]');
    await collaboratorPage.keyboard.press('End');
    await collaboratorPage.type('[data-testid="editor-content"]', '\n\nThe wind howled through the trees.');

    // Wait for sync
    await waitForRealtimeUpdate(ownerPage);
    await waitForRealtimeUpdate(collaboratorPage);

    // Both should see merged content
    const ownerContent = await ownerPage.textContent('[data-testid="editor-content"]');
    const collaboratorContent = await collaboratorPage.textContent('[data-testid="editor-content"]');
    
    expect(ownerContent).toContain('Chapter 1: The Beginning');
    expect(ownerContent).toContain('It was a dark and stormy night.');
    expect(ownerContent).toContain('The wind howled through the trees.');
    
    expect(collaboratorContent).toBe(ownerContent);
  });

  test('should track and display activity history', async () => {
    // Owner makes an edit
    await ownerPage.goto(`/projects/${project.id}/editor`);
    await ownerPage.click('[data-testid="editor-content"]');
    await ownerPage.type('[data-testid="editor-content"]', '\n\nChapter 2: The Journey Begins');

    // Save the change
    await ownerPage.click('[data-testid="save-btn"]');
    await ownerPage.waitForSelector('[data-testid="save-success"]');

    // Navigate to activity history
    await collaboratorPage.goto(`/projects/${project.id}/activity`);
    await collaboratorPage.waitForSelector('[data-testid="activity-list"]');

    // Should see owner's recent activity
    const latestActivity = collaboratorPage.locator('[data-testid="activity-item"]:first-child');
    await expect(latestActivity).toContainText('Project Owner');
    await expect(latestActivity).toContainText('edited');
    await expect(latestActivity).toContainText('Chapter 2');
  });

  test('should enforce role-based permissions', async () => {
    // Create a viewer user
    const viewer = await createTestUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Viewer User'
    });

    const viewerContext = await test.browser!.newContext();
    const viewerPage = await viewerContext.newPage();
    await loginUser(viewerPage, viewer);

    // Owner adds viewer with read-only permissions
    await ownerPage.goto(`/projects/${project.id}/settings`);
    await ownerPage.click('[data-testid="add-collaborator-btn"]');
    await ownerPage.fill('[data-testid="collaborator-email-input"]', viewer.email);
    await ownerPage.selectOption('[data-testid="collaborator-role-select"]', 'viewer');
    await ownerPage.click('[data-testid="send-invitation-btn"]');

    // Viewer accepts invitation
    await viewerPage.goto('/invitations');
    await viewerPage.click('[data-testid="accept-invitation-btn"]');
    await viewerPage.waitForURL(`/projects/${project.id}`);

    // Viewer navigates to editor
    await viewerPage.goto(`/projects/${project.id}/editor`);

    // Editor should be in read-only mode
    const editor = viewerPage.locator('[data-testid="editor-content"]');
    await expect(editor).toHaveAttribute('contenteditable', 'false');

    // Save button should be disabled
    await expect(viewerPage.locator('[data-testid="save-btn"]')).toBeDisabled();

    // Cleanup
    await viewerPage.close();
    await deleteTestUser(viewer.id);
  });

  test('should handle offline editing and sync', async () => {
    // Collaborator goes to editor
    await collaboratorPage.goto(`/projects/${project.id}/editor`);
    await collaboratorPage.waitForSelector('[data-testid="editor-content"]');

    // Simulate offline mode
    await collaboratorPage.context().setOffline(true);

    // Make changes while offline
    await collaboratorPage.click('[data-testid="editor-content"]');
    await collaboratorPage.type('[data-testid="editor-content"]', '\n\nOffline edit: This was written without connection');

    // Should show offline indicator
    await expect(collaboratorPage.locator('[data-testid="offline-indicator"]')).toBeVisible();

    // Go back online
    await collaboratorPage.context().setOffline(false);

    // Wait for sync
    await collaboratorPage.waitForSelector('[data-testid="sync-complete"]', { timeout: 10000 });

    // Owner should see the offline changes
    await ownerPage.reload();
    const content = await ownerPage.textContent('[data-testid="editor-content"]');
    expect(content).toContain('Offline edit: This was written without connection');
  });

  test('should manage collaboration lifecycle', async () => {
    // Owner removes collaborator access
    await ownerPage.goto(`/projects/${project.id}/settings`);
    await ownerPage.waitForSelector('[data-testid="collaborators-list"]');

    // Find collaborator in list
    const collaboratorItem = ownerPage.locator(`[data-testid="collaborator-${collaborator.id}"]`);
    await expect(collaboratorItem).toBeVisible();

    // Remove access
    await collaboratorItem.locator('[data-testid="remove-collaborator-btn"]').click();
    
    // Confirm removal
    await ownerPage.click('[data-testid="confirm-remove-btn"]');
    
    // Wait for removal to complete
    await expect(collaboratorItem).not.toBeVisible();

    // Collaborator should no longer have access
    await collaboratorPage.goto(`/projects/${project.id}`);
    await expect(collaboratorPage.locator('[data-testid="access-denied"]')).toBeVisible();
  });
});

// Helper function to login a user
async function loginUser(page: Page, user: TestUser) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('[data-testid="login-btn"]');
  await page.waitForURL('/dashboard');
}