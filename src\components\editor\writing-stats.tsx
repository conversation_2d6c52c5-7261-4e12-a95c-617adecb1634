'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Clock,
  FileText,
  Zap,
  BarChart3,
  Award,
  Target,
  TrendingUp
} from 'lucide-react'
import { SIZE_LIMITS } from '@/lib/constants'

interface WritingStatsProps {
  content: string
  dailyGoal?: number
  projectGoal?: number
  className?: string
}

interface Stats {
  wordCount: number
  characterCount: number
  paragraphCount: number
  sentenceCount: number
  readingTime: number
  averageWordsPerSentence: number
  averageSentencesPerParagraph: number
  dailyProgress: number
  projectProgress: number
}

function WritingStatsComponent({ 
  content, 
  dailyGoal = 500, 
  projectGoal = SIZE_LIMITS.MAX_DOCUMENT_CHARS,
  className = '' 
}: WritingStatsProps) {
  const [stats, setStats] = useState<Stats>({
    wordCount: 0,
    characterCount: 0,
    paragraphCount: 0,
    sentenceCount: 0,
    readingTime: 0,
    averageWordsPerSentence: 0,
    averageSentencesPerParagraph: 0,
    dailyProgress: 0,
    projectProgress: 0
  })


  const calculateStats = useCallback((text: string) => {
    const cleanText = text.trim()
    
    // Basic counts
    const wordCount = cleanText ? cleanText.split(/\s+/).filter(word => word.length > 0).length : 0
    const characterCount = cleanText.length
    const paragraphs = cleanText.split(/\n\s*\n/).filter(p => p.trim().length > 0)
    const paragraphCount = paragraphs.length
    
    // Sentence count (basic estimation)
    const sentences = cleanText.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const sentenceCount = sentences.length
    
    // Reading time (average 200 words per minute)
    const readingTime = Math.ceil(wordCount / 200)
    
    // Averages
    const averageWordsPerSentence = sentenceCount > 0 ? Math.round(wordCount / sentenceCount) : 0
    const averageSentencesPerParagraph = paragraphCount > 0 ? Math.round(sentenceCount / paragraphCount) : 0
    
    // Progress calculations
    const dailyProgress = Math.min((wordCount / dailyGoal) * 100, 100)
    const projectProgress = Math.min((wordCount / projectGoal) * 100, 100)

    setStats({
      wordCount,
      characterCount,
      paragraphCount,
      sentenceCount,
      readingTime,
      averageWordsPerSentence,
      averageSentencesPerParagraph,
      dailyProgress,
      projectProgress
    })
  }, [dailyGoal, projectGoal])

  useEffect(() => {
    calculateStats(content)
  }, [content, calculateStats])


  const getAchievementBadge = () => {
    if (stats.dailyProgress >= 100) {
      return (
        <Badge className="bg-success text-white">
          <Award className="h-3 w-3 mr-1" />
          Daily Goal Achieved!
        </Badge>
      )
    }
    if (stats.dailyProgress >= 75) {
      return (
        <Badge className="bg-info text-white">
          <Zap className="h-3 w-3 mr-1" />
          Almost There!
        </Badge>
      )
    }
    return null
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="h-4 w-4 text-info" />
            <span className="text-sm font-medium">Words</span>
          </div>
          <div className="text-2xl font-bold">{stats.wordCount.toLocaleString()}</div>
          <div className="text-xs text-muted-foreground">
            {stats.characterCount.toLocaleString()} characters
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <Target className="h-4 w-4 text-success" />
            <span className="text-sm font-medium">Daily Goal</span>
          </div>
          <div className="text-2xl font-bold">{Math.round(stats.dailyProgress)}%</div>
          <div className="text-xs text-muted-foreground">
            {stats.wordCount} / {dailyGoal} words
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="h-4 w-4 text-purple-500" />
            <span className="text-sm font-medium">Reading Time</span>
          </div>
          <div className="text-2xl font-bold">{stats.readingTime}</div>
          <div className="text-xs text-muted-foreground">
            minutes
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <BarChart3 className="h-4 w-4 text-warning" />
            <span className="text-sm font-medium">Structure</span>
          </div>
          <div className="text-lg font-bold">{stats.paragraphCount}</div>
          <div className="text-xs text-muted-foreground">
            {stats.sentenceCount} sentences
          </div>
        </Card>
      </div>

      {/* Progress Bars */}
      <div className="space-y-3">
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Daily Progress</span>
            {getAchievementBadge()}
          </div>
          <Progress 
            value={stats.dailyProgress} 
            className="h-3"
          />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{stats.wordCount} words</span>
            <span>{dailyGoal} goal</span>
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Project Progress</span>
            <Badge variant="outline">{Math.round(stats.projectProgress)}%</Badge>
          </div>
          <Progress 
            value={stats.projectProgress} 
            className="h-3"
          />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{stats.wordCount} words</span>
            <span>{projectGoal.toLocaleString()} target</span>
          </div>
        </div>
      </div>

      {/* Detailed Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Writing Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
            <div>
              <div className="text-sm text-muted-foreground">Avg. Words/Sentence</div>
              <div className="text-lg font-semibold">{stats.averageWordsPerSentence}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Avg. Sentences/Paragraph</div>
              <div className="text-lg font-semibold">{stats.averageSentencesPerParagraph}</div>
            </div>
          </div>

          <div className="pt-3 border-t">
            <div className="text-xs text-muted-foreground space-y-1">
              <div>• {stats.paragraphCount} paragraphs</div>
              <div>• {stats.sentenceCount} sentences</div>
              <div>• ~{stats.readingTime} minute read</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const WritingStats = React.memo(WritingStatsComponent)