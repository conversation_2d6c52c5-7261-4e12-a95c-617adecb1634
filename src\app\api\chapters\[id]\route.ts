import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase'
import { z } from 'zod'
import { cacheKeys, getServerCache, setServerCache, invalidateChapterCache } from '@/lib/cache/server'
import { chapterQualityAnalyzer } from '@/lib/services/chapter-quality-analyzer'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'

// Validation schema for chapter updates
const updateChapterSchema = z.object({
  content: z.string().optional(),
  title: z.string().optional(),
  status: z.enum(['draft', 'revised', 'final']).optional(),
  summary: z.string().optional(),
  notes: z.string().optional(),
  scene_count: z.number().int().positive().optional(),
  target_word_count: z.number().int().positive().optional()
})

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Check cache first
    const cacheKey = cacheKeys.chapter(id);
    const cached = getServerCache(cacheKey);
    if (cached) {
      return NextResponse.json({ chapter: cached });
    }

    // Get chapter with project ownership check
    const { data: chapter, error } = await supabase
      .from('chapters')
      .select(`
        *,
        projects!inner (
          id,
          title,
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'))
      }
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Cache the result for 5 minutes
    setServerCache(cacheKey, chapter, 5 * 60 * TIME_MS.SECOND);

    return NextResponse.json({ chapter })
  } catch (error) {
    logger.error('Error fetching chapter:', error)
    return NextResponse.json(
      { error: 'Failed to fetch chapter' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    
    // Validate request body
    const validationResult = updateChapterSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: validationResult.error.errors 
      }, { status: 400 })
    }
    
    const { content, title, status, ...updateData } = validationResult.data

    // Calculate word count if content is provided
    let wordCount
    if (content !== undefined) {
      wordCount = content.trim().split(/\s+/).filter((word: string) => word.length > 0).length
    }

    // First verify ownership through project
    const { data: ownershipCheck } = await supabase
      .from('chapters')
      .select(`
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (!ownershipCheck) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Update chapter
    const updateFields: Record<string, unknown> = {
      ...updateData,
      updated_at: new Date().toISOString()
    }

    if (content !== undefined) {
      updateFields.content = content
      updateFields.actual_word_count = wordCount
    }
    if (title !== undefined) {
      updateFields.title = title.trim()
    }
    if (status !== undefined) {
      updateFields.status = status
    }

    const { data: chapter, error } = await supabase
      .from('chapters')
      .update(updateFields)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Update project total word count
    if (content !== undefined) {
      const { data: allChapters } = await supabase
        .from('chapters')
        .select('actual_word_count')
        .eq('project_id', chapter.project_id)

      const totalWordCount = allChapters?.reduce(
        (sum, ch) => sum + (ch.actual_word_count || 0), 
        0
      ) || 0

      await supabase
        .from('projects')
        .update({ 
          current_word_count: totalWordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', chapter.project_id)
    }

    // Invalidate caches
    invalidateChapterCache(id, chapter.project_id);

    // Queue quality analysis if content was updated
    if (content !== undefined && content.trim().length > 0) {
      chapterQualityAnalyzer.queueAnalysis({
        chapterId: id,
        projectId: chapter.project_id,
        userId: user.id,
        content: content,
        title: chapter.title,
        chapterNumber: chapter.chapter_number
      }).catch(error => {
        logger.error('Failed to queue quality analysis:', error);
      });
    }

    return NextResponse.json({ chapter })
  } catch (error) {
    logger.error('Error updating chapter:', error)
    return NextResponse.json(
      { error: 'Failed to update chapter' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // First verify ownership through project
    const { data: chapter } = await supabase
      .from('chapters')
      .select(`
        project_id,
        projects!inner (
          user_id
        )
      `)
      .eq('id', id)
      .eq('projects.user_id', user.id)
      .single()

    if (!chapter) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Delete chapter
    const { error } = await supabase
      .from('chapters')
      .delete()
      .eq('id', id)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Update project word count
    const { data: remainingChapters } = await supabase
      .from('chapters')
      .select('actual_word_count')
      .eq('project_id', chapter.project_id)

    const totalWordCount = remainingChapters?.reduce(
      (sum, ch) => sum + (ch.actual_word_count || 0), 
      0
    ) || 0

    await supabase
      .from('projects')
      .update({ 
        current_word_count: totalWordCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', chapter.project_id)

    // Invalidate caches
    invalidateChapterCache(id, chapter.project_id);

    return NextResponse.json({ success: true })
  } catch (error) {
    logger.error('Error deleting chapter:', error)
    return NextResponse.json(
      { error: 'Failed to delete chapter' },
      { status: 500 }
    )
  }
}