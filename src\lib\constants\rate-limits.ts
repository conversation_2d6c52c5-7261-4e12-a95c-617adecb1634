/**
 * Rate limiting thresholds (requests per hour)
 */
export const CONSTANTS_RATE_LIMITS = {
  // AI operations
  AI_GENERATION: 5,
  AI_CONTENT: 15,
  
  // API operations
  API_GENERAL: 60,
  API_AUTH: 20,
  API_SEARCH: 100,
  
  // Service operations
  SERVICE_ORCHESTRATOR_WRITE: 10,
  SERVICE_ORCHESTRATOR_READ: 30,
  SERVICE_HEALTH_CHECK: 20,
  SERVICE_MANAGEMENT: 5,
  
  // Bulk operations
  BULK_OPERATIONS: 10,
  
  // Export operations
  EXPORT_OPERATIONS: 20,
} as const

/**
 * Rate limiting time windows (in milliseconds)
 */
export const RATE_LIMIT_WINDOWS = {
  ONE_MINUTE: 60 * 1000,
  FIVE_MINUTES: 5 * 60 * 1000,
  ONE_HOUR: 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
} as const

/**
 * Rate limit cost multipliers for expensive operations
 */
export const RATE_LIMIT_COSTS = {
  STANDARD: 1,
  EXPENSIVE: 5,
  VERY_EXPENSIVE: 10,
} as const