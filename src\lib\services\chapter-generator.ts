import { ContentGenerator } from './content-generator';
import { BaseService } from './base-service';
import { ServiceResponse } from './types';
import { TIME_MS } from '@/lib/constants'

export interface ChapterContext {
  projectId: string;
  chapterNumber: number;
  chapterOutline: {
    title: string;
    summary: string;
    wordCountTarget: number;
    povCharacter: string;
    objectives: string[];
    conflicts: string[];
    scenes: Array<{
      id: string;
      title: string;
      description: string;
      wordCountTarget: number;
      setting: string;
      characters: string[];
      purpose: string;
      conflict: string;
      outcome: string;
    }>;
    characterStates: Array<{
      characterId: string;
      emotionalState: string;
      physicalState: string;
      knowledge: string[];
      relationships: string;
    }>;
    plotAdvancement: string[];
  };
  projectSettings: {
    primaryGenre: string;
    narrativeVoice: string;
    tense: string;
    writingStyle: string;
    tone: string[];
    targetAudience: string;
    contentRating: string;
  };
  characters: Array<{
    id: string;
    name: string;
    role: string;
    description: string;
    personality: Record<string, unknown>;
    voiceCharacteristics: string[];
  }>;
  storyBible: {
    worldRules: Record<string, string>;
    timeline: Array<{ event: string; chapter: number }>;
    plotThreads: Array<{ id: string; description: string; status: string }>;
  };
  previousChapters: Array<{
    chapterNumber: number;
    content: string;
    wordCount: number;
    characterVoices: Record<string, string>;
    summary: string;
  }>;
}

export interface GeneratedChapter {
  content: string;
  wordCount: number;
  scenes: Array<{
    id: string;
    content: string;
    wordCount: number;
    characters: string[];
    setting: string;
    purpose: string;
  }>;
  characterVoices: Record<string, string>;
  plotProgression: string[];
  continuityNotes: string[];
  qualityScore: number;
  generationMetadata: {
    model: string;
    tokensUsed: number;
    generationTime: number;
    revisionsCount: number;
  };
}

export class ChapterGenerator extends BaseService {
  private contentGenerator: ContentGenerator;

  constructor() {
    super({
      name: 'chapter-generator',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/chapters/generate'],
      dependencies: ['content-generator'],
      healthCheck: '/api/chapters/health'
    });

    this.contentGenerator = new ContentGenerator();
  }

  async initialize(): Promise<void> {
    await this.contentGenerator.initialize();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    return this.createResponse(true, {
      status: 'Chapter generator ready',
      uptime: Date.now() - (this.isInitialized ? Date.now() - TIME_MS.SECOND : Date.now())
    });
  }

  async shutdown(): Promise<void> {
    await this.contentGenerator.shutdown();
    this.setStatus('inactive');
  }

  async generateChapter(context: ChapterContext): Promise<ServiceResponse<GeneratedChapter>> {
    return this.withErrorHandling(async () => {
      const startTime = Date.now();
      let revisionsCount = 0;

      // Build comprehensive context for generation
      const generationContext = this.buildGenerationContext(context);
      
      // Generate chapter content scene by scene
      const scenes = [];
      let totalWordCount = 0;
      const characterVoices: Record<string, string> = {};
      const plotProgression: string[] = [];

      for (const sceneOutline of context.chapterOutline.scenes) {
        const sceneResult = await this.generateScene(sceneOutline, generationContext, context);
        
        if (sceneResult.success && sceneResult.data) {
          const sceneData = sceneResult.data;
          scenes.push(sceneData);
          totalWordCount += sceneData.wordCount;
          
          // Track character voices
          sceneData.characters.forEach(charName => {
            if (!characterVoices[charName]) {
              characterVoices[charName] = this.extractCharacterVoice(sceneData.content, charName);
            }
          });
        }
      }

      // Combine scenes into full chapter
      const fullContent = this.combineScenes(scenes, context);
      
      // Validate and potentially revise
      const qualityCheck = await this.validateChapterQuality(fullContent, context);
      
      if (qualityCheck.needsRevision && revisionsCount < 2) {
        revisionsCount++;
        // Implement revision logic here if needed
      }

      // Generate plot progression summary
      const plotSummary = await this.generatePlotProgression(fullContent, context);
      if (plotSummary.success) {
        plotProgression.push(...(plotSummary.data || []));
      }

      // Generate continuity notes
      const continuityNotes = await this.generateContinuityNotes(fullContent, context);

      const generatedChapter: GeneratedChapter = {
        content: fullContent,
        wordCount: totalWordCount,
        scenes,
        characterVoices,
        plotProgression,
        continuityNotes: continuityNotes.success ? continuityNotes.data || [] : [],
        qualityScore: qualityCheck.score,
        generationMetadata: {
          model: 'gpt-4',
          tokensUsed: this.estimateTokens(fullContent),
          generationTime: Date.now() - startTime,
          revisionsCount
        }
      };

      return generatedChapter;
    });
  }

  private buildGenerationContext(context: ChapterContext): string {
    const { projectSettings, characters, storyBible, previousChapters, chapterOutline } = context;

    const characterProfiles = characters.map(char => 
      `${char.name} (${char.role}): ${char.description}\nVoice: ${char.voiceCharacteristics.join(', ')}`
    ).join('\n\n');

    const worldContext = Object.entries(storyBible.worldRules)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');

    const timelineContext = storyBible.timeline
      .filter(event => event.chapter <= context.chapterNumber)
      .map(event => `Chapter ${event.chapter}: ${event.event}`)
      .join('\n');

    const previousContext = previousChapters.length > 0 
      ? `\nPREVIOUS CHAPTERS SUMMARY:\n${previousChapters.map(ch => 
          `Chapter ${ch.chapterNumber} (${ch.wordCount} words): ${ch.summary || 'No summary'}`
        ).join('\n')}`
      : '';

    return `
STORY CONTEXT:
Genre: ${projectSettings.primaryGenre}
Narrative Voice: ${projectSettings.narrativeVoice}
Tense: ${projectSettings.tense}
Writing Style: ${projectSettings.writingStyle}
Tone: ${projectSettings.tone.join(', ')}
Target Audience: ${projectSettings.targetAudience}
Content Rating: ${projectSettings.contentRating}

CHARACTERS:
${characterProfiles}

WORLD RULES:
${worldContext}

TIMELINE SO FAR:
${timelineContext}

CURRENT CHAPTER OUTLINE:
Title: ${chapterOutline.title}
Summary: ${chapterOutline.summary}
POV Character: ${chapterOutline.povCharacter}
Target Word Count: ${chapterOutline.wordCountTarget}
Objectives: ${chapterOutline.objectives.join(', ')}
Conflicts: ${chapterOutline.conflicts.join(', ')}
Plot Advancement: ${chapterOutline.plotAdvancement.join(', ')}

${previousContext}

ACTIVE PLOT THREADS:
${storyBible.plotThreads.filter(thread => thread.status === 'active')
  .map(thread => `${thread.id}: ${thread.description}`)
  .join('\n')}
    `;
  }

  private async generateScene(
    sceneOutline: ChapterContext['chapterOutline']['scenes'][0],
    generationContext: string,
    context: ChapterContext
  ): Promise<ServiceResponse<GeneratedChapter['scenes'][0]>> {
    return this.withErrorHandling(async () => {
      const scenePrompt = `
${generationContext}

SCENE TO WRITE:
Title: ${sceneOutline.title}
Description: ${sceneOutline.description}
Setting: ${sceneOutline.setting}
Characters Present: ${sceneOutline.characters.join(', ')}
Scene Purpose: ${sceneOutline.purpose}
Central Conflict: ${sceneOutline.conflict}
Expected Outcome: ${sceneOutline.outcome}
Target Word Count: ${sceneOutline.wordCountTarget}

Write this scene in full, maintaining:
1. Consistent character voices and personalities
2. The established writing style and tone
3. Proper narrative voice (${context.projectSettings.narrativeVoice}) and tense (${context.projectSettings.tense})
4. The scene's specific purpose and conflict
5. Natural progression toward the expected outcome
6. Appropriate pacing and emotional beats
7. Rich sensory details and immersive description

The scene should feel natural and engaging while advancing the plot and developing characters.
      `;

      const sceneResult = await this.contentGenerator.generateContent({
        type: 'scene',
        prompt: scenePrompt,
        context: {
          setting: sceneOutline.setting,
          characters: sceneOutline.characters,
          purpose: sceneOutline.purpose,
          wordCountTarget: sceneOutline.wordCountTarget
        },
        style: context.projectSettings.writingStyle,
        tone: context.projectSettings.tone.join(', '),
        length: sceneOutline.wordCountTarget > 800 ? 'long' : sceneOutline.wordCountTarget > 400 ? 'medium' : 'short',
        projectId: context.projectId
      });

      if (!sceneResult.success || !sceneResult.data) {
        throw new Error(`Failed to generate scene: ${sceneOutline.title}`);
      }

      const content = sceneResult.data;
      const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

      return {
        id: sceneOutline.id,
        content,
        wordCount,
        characters: sceneOutline.characters,
        setting: sceneOutline.setting,
        purpose: sceneOutline.purpose
      };
    });
  }

  private combineScenes(scenes: GeneratedChapter['scenes'], context: ChapterContext): string {
    const chapterHeader = `# ${context.chapterOutline.title}\n\n`;
    
    const sceneContents = scenes.map(scene => {
      // Add scene breaks between scenes
      return scene.content.trim();
    }).join('\n\n---\n\n');

    return chapterHeader + sceneContents;
  }

  private extractCharacterVoice(content: string, characterName: string): string {
    // Extract dialogue and internal thoughts for this character
    const lines = content.split('\n');
    const characterLines = lines.filter(line => 
      line.includes(`"`) && line.toLowerCase().includes(characterName.toLowerCase())
    );

    if (characterLines.length === 0) return 'Standard voice';

    // Analyze speech patterns, word choices, etc.
    const voiceCharacteristics = [];
    
    // Simple analysis - in a real implementation, this would be more sophisticated
    if (characterLines.some(line => line.includes('...'))) {
      voiceCharacteristics.push('hesitant');
    }
    if (characterLines.some(line => line.includes('!'))) {
      voiceCharacteristics.push('energetic');
    }
    if (characterLines.some(line => line.length > 100)) {
      voiceCharacteristics.push('verbose');
    }

    return voiceCharacteristics.join(', ') || 'Standard voice';
  }

  private async validateChapterQuality(content: string, context: ChapterContext): Promise<{
    score: number;
    needsRevision: boolean;
    issues: string[];
  }> {
    let score = 70; // Base score
    const issues: string[] = [];

    // Word count check
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
    const targetWordCount = context.chapterOutline.wordCountTarget;
    const wordCountRatio = wordCount / targetWordCount;

    if (wordCountRatio < 0.8 || wordCountRatio > 1.2) {
      score -= 10;
      issues.push(`Word count variance: ${wordCount} vs target ${targetWordCount}`);
    } else {
      score += 5;
    }

    // Character consistency check
    const mentionedCharacters = context.characters.filter(char => 
      content.toLowerCase().includes(char.name.toLowerCase())
    );

    if (mentionedCharacters.length < context.chapterOutline.scenes.reduce((acc, scene) => 
      acc + scene.characters.length, 0) / 2) {
      score -= 5;
      issues.push('Some outlined characters not properly featured');
    }

    // Structure check
    const paragraphs = content.split('\n\n').filter(p => p.trim());
    if (paragraphs.length < 5) {
      score -= 10;
      issues.push('Chapter too brief, lacks proper structure');
    }

    // Dialogue check
    const dialogueCount = (content.match(/"/g) || []).length;
    if (dialogueCount < 10) {
      score -= 5;
      issues.push('Insufficient dialogue for character development');
    }

    return {
      score: Math.max(30, Math.min(100, score)),
      needsRevision: score < 60,
      issues
    };
  }

  private async generatePlotProgression(content: string, context: ChapterContext): Promise<ServiceResponse<string[]>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Analyze this chapter content and identify the key plot progression points:

CHAPTER CONTENT:
${content.substring(0, 2000)}...

EXPECTED PLOT ADVANCEMENT:
${context.chapterOutline.plotAdvancement.join(', ')}

List the specific plot points that were advanced in this chapter as bullet points.
Focus on concrete story developments, character revelations, and conflict progression.
      `;

      const result = await this.contentGenerator.generateContent({
        type: 'plot-outline',
        prompt,
        length: 'short',
        projectId: context.projectId
      });

      if (!result.success || !result.data) {
        return [];
      }

      // Parse the result into array of plot points
      const plotPoints = result.data
        .split('\n')
        .filter(line => line.trim().startsWith('•') || line.trim().startsWith('-'))
        .map(line => line.replace(/^[•\-]\s*/, '').trim())
        .filter(point => point.length > 0);

      return plotPoints;
    });
  }

  private async generateContinuityNotes(content: string, context: ChapterContext): Promise<ServiceResponse<string[]>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Review this chapter for continuity and consistency:

CHAPTER CONTENT:
${content.substring(0, 1500)}...

WORLD RULES:
${Object.entries(context.storyBible.worldRules).map(([key, value]) => `${key}: ${value}`).join('\n')}

TIMELINE EVENTS:
${context.storyBible.timeline.map(event => `Chapter ${event.chapter}: ${event.event}`).join('\n')}

Generate continuity notes highlighting:
1. Any potential inconsistencies with established world rules
2. Timeline or character state conflicts
3. Important details that future chapters should remember
4. Character development moments that impact their arcs

Format as bullet points.
      `;

      const result = await this.contentGenerator.generateContent({
        type: 'chapter',
        prompt,
        length: 'medium',
        projectId: context.projectId
      });

      if (!result.success || !result.data) {
        return [];
      }

      const notes = result.data
        .split('\n')
        .filter(line => line.trim().startsWith('•') || line.trim().startsWith('-'))
        .map(line => line.replace(/^[•\-]\s*/, '').trim())
        .filter(note => note.length > 0);

      return notes;
    });
  }

  private estimateTokens(content: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(content.length / 4);
  }
}