'use client'

import { useState, useEffect, useCallback } from 'react'
import { useDebounce } from '@/hooks/use-debounce'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { PaginationControls } from '@/components/ui/pagination'
import { Plus, Search, Filter, Users, User, BookOpen, Edit2, Trash2, Link2, Sparkles, Share2, Save, X, Mic, VolumeIcon } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import Link from 'next/link'

interface Project {
  id: string
  title: string
}

interface Character {
  id: string
  project_id: string
  character_id: string
  name: string
  role: 'protagonist' | 'antagonist' | 'supporting' | 'minor'
  description: string
  backstory: string
  personality_traits: {
    traits?: string[]
    [key: string]: unknown
  }
  character_arc: {
    beginning?: string
    middle?: string
    end?: string
    [key: string]: unknown
  }
  relationships: Array<{
    character_id: string
    character_name: string
    relationship_type: string
    description: string
  }>
  voice_data: {
    speaking_style?: string
    vocabulary?: string[]
    mannerisms?: string[]
  }
  created_at: string
  updated_at: string
  project?: {
    id: string
    title: string
  }
}

interface CharacterShare {
  id: string
  character_id: string
  source_series_id: string
  target_series_id: string
  share_type: 'reference' | 'guest' | 'permanent'
  version_notes?: string
  created_at: string
}

interface CharacterManagerProps {
  projects: Project[]
  userId: string
}

interface PaginationData {
  total: number;
  limit: number;
  offset: number;
  page: number;
  totalPages: number;
}

export function CharacterManager({ projects, userId }: CharacterManagerProps) {
  const [selectedProject, setSelectedProject] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterRole, setFilterRole] = useState<string>('all')
  const [characters, setCharacters] = useState<Character[]>([])
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    limit: 20,
    offset: 0,
    page: 1,
    totalPages: 1
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null)
  const [showShareDialog, setShowShareDialog] = useState<Character | null>(null)
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)
  const { toast } = useToast()
  const debouncedSearchQuery = useDebounce(searchQuery, 300)
  
  // Character form state
  const [characterForm, setCharacterForm] = useState({
    name: '',
    role: 'supporting' as const,
    description: '',
    backstory: '',
    project_id: '',
    personality_traits: '',
    speaking_style: '',
    vocabulary: '',
    mannerisms: '',
    voice_profile_id: ''
  })
  
  useEffect(() => {
    loadCharacters()
  }, [currentPage, debouncedSearchQuery, selectedProject, filterRole])

  const loadCharacters = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: ((currentPage - 1) * pagination.limit).toString(),
      })

      if (debouncedSearchQuery) {
        params.append('search', debouncedSearchQuery)
      }

      if (selectedProject !== 'all') {
        params.append('project_id', selectedProject)
      }

      if (filterRole !== 'all') {
        params.append('role', filterRole)
      }

      const response = await fetch(`/api/characters?${params}`)
      if (!response.ok) throw new Error('Failed to load characters')

      const data = await response.json()
      setCharacters(data.characters || [])

      if (data.pagination) {
        const totalPages = Math.ceil(data.pagination.total / pagination.limit)
        setPagination({
          total: data.pagination.total,
          limit: data.pagination.limit,
          offset: data.pagination.offset,
          page: currentPage,
          totalPages
        })
      }
    } catch (error) {
      logger.error('Error loading characters:', error)
      toast({
        title: "Error",
        description: "Failed to load characters",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [currentPage, debouncedSearchQuery, selectedProject, filterRole, pagination.limit])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    setCurrentPage(1) // Reset to first page when searching
  }

  const handleProjectChange = (value: string) => {
    setSelectedProject(value)
    setCurrentPage(1) // Reset to first page when changing project filter
  }

  const handleRoleChange = (value: string) => {
    setFilterRole(value)
    setCurrentPage(1) // Reset to first page when changing role filter
  }

  const handleCreateCharacter = async () => {
    if (!characterForm.name || !characterForm.project_id) {
      toast({
        title: "Error",
        description: "Character name and project are required",
        variant: "destructive"
      })
      return
    }

    try {
      const personality = characterForm.personality_traits.split(',').map(t => t.trim()).filter(Boolean)

      const response = await fetch('/api/characters', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...characterForm,
          character_id: `char_${Date.now()}`,
          personality_traits: { traits: personality },
          voice_data: {
            speaking_style: characterForm.speaking_style,
            vocabulary: characterForm.vocabulary.split(',').map(v => v.trim()).filter(Boolean),
            mannerisms: characterForm.mannerisms.split(',').map(m => m.trim()).filter(Boolean)
          },
          voice_profile_id: characterForm.voice_profile_id || null
        })
      })

      if (!response.ok) throw new Error('Failed to create character')

      const data = await response.json()
      // Refresh character list instead of manually adding to avoid pagination issues
      loadCharacters()
      setShowCreateDialog(false)
      resetForm()

      toast({
        title: "Success",
        description: "Character created successfully"
      })
    } catch (error) {
      logger.error('Error creating character:', error)
      toast({
        title: "Error",
        description: "Failed to create character",
        variant: "destructive"
      })
    }
  }
  
  const handleUpdateCharacter = async () => {
    if (!selectedCharacter) return
    
    try {
      const personality = characterForm.personality_traits.split(',').map(t => t.trim()).filter(Boolean)
      
      const response = await fetch(`/api/characters/${selectedCharacter.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: characterForm.name,
          role: characterForm.role,
          description: characterForm.description,
          backstory: characterForm.backstory,
          personality_traits: { traits: personality },
          voice_data: {
            speaking_style: characterForm.speaking_style,
            vocabulary: characterForm.vocabulary.split(',').map(v => v.trim()).filter(Boolean),
            mannerisms: characterForm.mannerisms.split(',').map(m => m.trim()).filter(Boolean)
          },
          voice_profile_id: characterForm.voice_profile_id || null
        })
      })
      
      if (!response.ok) throw new Error('Failed to update character')
      
      const data = await response.json()
      setCharacters(characters.map(c => c.id === selectedCharacter.id ? data.character : c))
      setShowEditDialog(false)
      setSelectedCharacter(null)
      resetForm()
      
      toast({
        title: "Success",
        description: "Character updated successfully"
      })
    } catch (error) {
      logger.error('Error updating character:', error)
      toast({
        title: "Error",
        description: "Failed to update character",
        variant: "destructive"
      })
    }
  }
  
  const handleDeleteCharacter = async (characterId: string) => {
    try {
      const response = await fetch(`/api/characters/${characterId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) throw new Error('Failed to delete character')
      
      setCharacters(characters.filter(c => c.id !== characterId))
      setShowDeleteDialog(null)
      
      toast({
        title: "Success",
        description: "Character deleted successfully"
      })
    } catch (error) {
      logger.error('Error deleting character:', error)
      toast({
        title: "Error",
        description: "Failed to delete character",
        variant: "destructive"
      })
    }
  }
  
  const resetForm = () => {
    setCharacterForm({
      name: '',
      role: 'supporting',
      description: '',
      backstory: '',
      project_id: '',
      personality_traits: '',
      speaking_style: '',
      vocabulary: '',
      mannerisms: '',
      voice_profile_id: ''
    })
  }
  
  const openEditDialog = (character: Character) => {
    setSelectedCharacter(character)
    setCharacterForm({
      name: character.name,
      role: character.role,
      description: character.description || '',
      backstory: character.backstory || '',
      project_id: character.project_id,
      personality_traits: character.personality_traits?.traits?.join(', ') || '',
      speaking_style: character.voice_data?.speaking_style || '',
      vocabulary: character.voice_data?.vocabulary?.join(', ') || '',
      mannerisms: character.voice_data?.mannerisms?.join(', ') || '',
      voice_profile_id: ''
    })
    setShowEditDialog(true)
  }
  
  // Filter characters
  const filteredCharacters = characters.filter(char => {
    const matchesProject = selectedProject === 'all' || char.project_id === selectedProject
    const matchesSearch = !searchQuery || 
      char.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      char.description?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesRole = filterRole === 'all' || char.role === filterRole
    
    return matchesProject && matchesSearch && matchesRole
  })
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }
  
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'protagonist': return 'default'
      case 'antagonist': return 'destructive'
      case 'supporting': return 'secondary'
      case 'minor': return 'outline'
      default: return 'secondary'
    }
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="flex-1 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search characters..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={selectedProject} onValueChange={handleProjectChange}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All projects" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All projects</SelectItem>
              {projects.map(project => (
                <SelectItem key={project.id} value={project.id}>
                  {project.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={filterRole} onValueChange={handleRoleChange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="All roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All roles</SelectItem>
              <SelectItem value="protagonist">Protagonist</SelectItem>
              <SelectItem value="antagonist">Antagonist</SelectItem>
              <SelectItem value="supporting">Supporting</SelectItem>
              <SelectItem value="minor">Minor</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Character
        </Button>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="grid" className="space-y-4">
        <TabsList>
          <TabsTrigger value="grid">Grid View</TabsTrigger>
          <TabsTrigger value="list">List View</TabsTrigger>
          <TabsTrigger value="relationships">Relationships</TabsTrigger>
        </TabsList>
        
        <TabsContent value="grid" className="space-y-4">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-pulse">
                <div className="h-12 w-12 bg-muted rounded-full mx-auto mb-4" />
                <div className="h-4 w-32 bg-muted rounded mx-auto" />
              </div>
            </div>
          ) : filteredCharacters.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Characters Found</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {characters.length === 0 
                  ? "Create your first character to get started"
                  : "No characters match your current filters"}
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Character
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredCharacters.map((character) => (
                <Card key={character.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <Avatar className="h-12 w-12">
                        <AvatarFallback>{getInitials(character.name)}</AvatarFallback>
                      </Avatar>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(character)}
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowDeleteDialog(character.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-3">
                      <CardTitle className="text-lg">{character.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={getRoleBadgeVariant(character.role)}>
                          {character.role}
                        </Badge>
                        {character.project && (
                          <Link href={`/projects/${character.project.id}/write`}>
                            <Badge variant="outline" className="cursor-pointer hover:bg-muted">
                              <BookOpen className="h-3 w-3 mr-1" />
                              {character.project.title}
                            </Badge>
                          </Link>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {character.description || 'No description available'}
                    </p>
                    {character.personality_traits?.traits && character.personality_traits.traits.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-1">
                          {character.personality_traits.traits.slice(0, 3).map((trait: string, idx: number) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {trait}
                            </Badge>
                          ))}
                          {character.personality_traits.traits.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{character.personality_traits.traits.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Character List</CardTitle>
              <CardDescription>
                Detailed view of all your characters
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredCharacters.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No characters to display
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredCharacters.map((character) => (
                    <div key={character.id} className="flex items-start gap-4 p-4 border rounded-lg">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>{getInitials(character.name)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold">{character.name}</h4>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant={getRoleBadgeVariant(character.role)}>
                                {character.role}
                              </Badge>
                              {character.project && (
                                <span className="text-sm text-muted-foreground">
                                  in {character.project.title}
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(character)}
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setShowDeleteDialog(character.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {character.description || 'No description available'}
                        </p>
                        {character.backstory && (
                          <div className="pt-2">
                            <h5 className="text-sm font-medium">Backstory</h5>
                            <p className="text-sm text-muted-foreground mt-1">
                              {character.backstory}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="relationships" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Character Relationships</CardTitle>
              <CardDescription>
                Visualize and manage relationships between your characters
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredCharacters.length < 2 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Create at least two characters to map their relationships
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Relationship mapping will be available in the next update. 
                    For now, you can define relationships in each character's details.
                  </p>
                  <div className="grid gap-4">
                    {filteredCharacters.filter(c => c.relationships && c.relationships.length > 0).map((character) => (
                      <div key={character.id} className="p-4 border rounded-lg">
                        <h4 className="font-semibold mb-2">{character.name}'s Relationships</h4>
                        <div className="space-y-2">
                          {character.relationships.map((rel, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-sm">
                              <Link2 className="h-4 w-4 text-muted-foreground" />
                              <span>{rel.relationship_type} with {rel.character_name}</span>
                              {rel.description && (
                                <span className="text-muted-foreground">- {rel.description}</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Pagination Controls */}
      {pagination.totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={pagination.totalPages}
          onPageChange={handlePageChange}
          showInfo={true}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          className="mt-6"
        />
      )}

      {/* Create Character Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Character</DialogTitle>
            <DialogDescription>
              Add a new character to your story universe
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Character Name</Label>
                <Input
                  id="name"
                  value={characterForm.name}
                  onChange={(e) => setCharacterForm({ ...characterForm, name: e.target.value })}
                  placeholder="e.g., Jane Doe"
                />
              </div>
              <div>
                <Label htmlFor="project">Project</Label>
                <Select
                  value={characterForm.project_id}
                  onValueChange={(value) => setCharacterForm({ ...characterForm, project_id: value })}
                >
                  <SelectTrigger id="project">
                    <SelectValue placeholder="Select a project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects.map(project => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="role">Character Role</Label>
              <Select
                value={characterForm.role}
                onValueChange={(value: 'protagonist' | 'antagonist' | 'supporting' | 'minor') => setCharacterForm({ ...characterForm, role: value })}
              >
                <SelectTrigger id="role">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="protagonist">Protagonist</SelectItem>
                  <SelectItem value="antagonist">Antagonist</SelectItem>
                  <SelectItem value="supporting">Supporting</SelectItem>
                  <SelectItem value="minor">Minor</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={characterForm.description}
                onChange={(e) => setCharacterForm({ ...characterForm, description: e.target.value })}
                placeholder="Brief description of the character..."
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="backstory">Backstory</Label>
              <Textarea
                id="backstory"
                value={characterForm.backstory}
                onChange={(e) => setCharacterForm({ ...characterForm, backstory: e.target.value })}
                placeholder="Character's background and history..."
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="traits">Personality Traits</Label>
              <Input
                id="traits"
                value={characterForm.personality_traits}
                onChange={(e) => setCharacterForm({ ...characterForm, personality_traits: e.target.value })}
                placeholder="e.g., brave, cunning, loyal (comma-separated)"
              />
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-2">
                <Mic className="h-4 w-4" />
                <h4 className="font-medium">Voice Profile</h4>
              </div>
              
              <div>
                <Label htmlFor="speaking-style">Speaking Style</Label>
                <Textarea
                  id="speaking-style"
                  value={characterForm.speaking_style}
                  onChange={(e) => setCharacterForm({ ...characterForm, speaking_style: e.target.value })}
                  placeholder="How does this character speak? Formal, casual, poetic, blunt..."
                  rows={2}
                />
                <p className="text-xs text-muted-foreground mt-1">Describe the character's overall speaking style and tone</p>
              </div>
              
              <div>
                <Label htmlFor="vocabulary">Unique Vocabulary</Label>
                <Input
                  id="vocabulary"
                  value={characterForm.vocabulary}
                  onChange={(e) => setCharacterForm({ ...characterForm, vocabulary: e.target.value })}
                  placeholder="e.g., 'forsooth', 'ain't', technical jargon (comma-separated)"
                />
                <p className="text-xs text-muted-foreground mt-1">Words or phrases this character commonly uses</p>
              </div>
              
              <div>
                <Label htmlFor="mannerisms">Speech Mannerisms</Label>
                <Input
                  id="mannerisms"
                  value={characterForm.mannerisms}
                  onChange={(e) => setCharacterForm({ ...characterForm, mannerisms: e.target.value })}
                  placeholder="e.g., 'stutters when nervous', 'ends sentences with questions' (comma-separated)"
                />
                <p className="text-xs text-muted-foreground mt-1">Speech patterns, quirks, or verbal tics</p>
              </div>
              
              <div className="pt-2">
                <Link href="/voice-profiles" className="text-sm text-primary hover:underline flex items-center gap-1">
                  <VolumeIcon className="h-3 w-3" />
                  Manage Voice Profiles
                </Link>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setShowCreateDialog(false)
                resetForm()
              }}>
                Cancel
              </Button>
              <Button onClick={handleCreateCharacter}>
                <Plus className="h-4 w-4 mr-2" />
                Create Character
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Edit Character Dialog */}
      <Dialog open={showEditDialog} onOpenChange={(open) => {
        setShowEditDialog(open)
        if (!open) {
          setSelectedCharacter(null)
          resetForm()
        }
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Character</DialogTitle>
            <DialogDescription>
              Update character details
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div>
              <Label htmlFor="edit-name">Character Name</Label>
              <Input
                id="edit-name"
                value={characterForm.name}
                onChange={(e) => setCharacterForm({ ...characterForm, name: e.target.value })}
                placeholder="e.g., Jane Doe"
              />
            </div>
            
            <div>
              <Label htmlFor="edit-role">Character Role</Label>
              <Select
                value={characterForm.role}
                onValueChange={(value: 'protagonist' | 'antagonist' | 'supporting' | 'minor') => setCharacterForm({ ...characterForm, role: value })}
              >
                <SelectTrigger id="edit-role">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="protagonist">Protagonist</SelectItem>
                  <SelectItem value="antagonist">Antagonist</SelectItem>
                  <SelectItem value="supporting">Supporting</SelectItem>
                  <SelectItem value="minor">Minor</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={characterForm.description}
                onChange={(e) => setCharacterForm({ ...characterForm, description: e.target.value })}
                placeholder="Brief description of the character..."
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="edit-backstory">Backstory</Label>
              <Textarea
                id="edit-backstory"
                value={characterForm.backstory}
                onChange={(e) => setCharacterForm({ ...characterForm, backstory: e.target.value })}
                placeholder="Character's background and history..."
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="edit-traits">Personality Traits</Label>
              <Input
                id="edit-traits"
                value={characterForm.personality_traits}
                onChange={(e) => setCharacterForm({ ...characterForm, personality_traits: e.target.value })}
                placeholder="e.g., brave, cunning, loyal (comma-separated)"
              />
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-2">
                <Mic className="h-4 w-4" />
                <h4 className="font-medium">Voice Profile</h4>
              </div>
              
              <div>
                <Label htmlFor="edit-speaking-style">Speaking Style</Label>
                <Textarea
                  id="edit-speaking-style"
                  value={characterForm.speaking_style}
                  onChange={(e) => setCharacterForm({ ...characterForm, speaking_style: e.target.value })}
                  placeholder="How does this character speak? Formal, casual, poetic, blunt..."
                  rows={2}
                />
                <p className="text-xs text-muted-foreground mt-1">Describe the character's overall speaking style and tone</p>
              </div>
              
              <div>
                <Label htmlFor="edit-vocabulary">Unique Vocabulary</Label>
                <Input
                  id="edit-vocabulary"
                  value={characterForm.vocabulary}
                  onChange={(e) => setCharacterForm({ ...characterForm, vocabulary: e.target.value })}
                  placeholder="e.g., 'forsooth', 'ain't', technical jargon (comma-separated)"
                />
                <p className="text-xs text-muted-foreground mt-1">Words or phrases this character commonly uses</p>
              </div>
              
              <div>
                <Label htmlFor="edit-mannerisms">Speech Mannerisms</Label>
                <Input
                  id="edit-mannerisms"
                  value={characterForm.mannerisms}
                  onChange={(e) => setCharacterForm({ ...characterForm, mannerisms: e.target.value })}
                  placeholder="e.g., 'stutters when nervous', 'ends sentences with questions' (comma-separated)"
                />
                <p className="text-xs text-muted-foreground mt-1">Speech patterns, quirks, or verbal tics</p>
              </div>
              
              <div className="pt-2">
                <Link href="/voice-profiles" className="text-sm text-primary hover:underline flex items-center gap-1">
                  <VolumeIcon className="h-3 w-3" />
                  Manage Voice Profiles
                </Link>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateCharacter}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!showDeleteDialog} onOpenChange={() => setShowDeleteDialog(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this character
              from your project.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => showDeleteDialog && handleDeleteCharacter(showDeleteDialog)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Character
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}