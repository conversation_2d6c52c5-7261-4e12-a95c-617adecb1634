/**
 * Monitoring and telemetry constants
 */
export const MONITORING = {
  // Sentry sampling rates
  TRACES_SAMPLE_RATE_PROD: 0.1,  // 10% in production
  TRACES_SAMPLE_RATE_DEV: 1.0,   // 100% in development
  REPLAY_SESSION_SAMPLE_RATE: 0.1, // 10% of sessions
  
  // Error tracking
  MAX_ERROR_MESSAGE_LENGTH: 1000,
  MAX_STACK_TRACE_FRAMES: 50,
  
  // Performance thresholds (in milliseconds)
  SLOW_REQUEST_THRESHOLD: 3000,
  VERY_SLOW_REQUEST_THRESHOLD: 10000,
} as const

/**
 * AI model parameters
 */
export const AI_MODEL_PARAMS = {
  DEFAULT_TEMPERATURE: 0.7,
  MIN_TEMPERATURE: 0.0,
  MAX_TEMPERATURE: 2.0,
  DEFAULT_MAX_TOKENS: 4000,
  DEFAULT_TOP_P: 1.0,
} as const

/**
 * Timeout values (in milliseconds)
 */
export const MONITORING_TIMEOUTS = {
  API_REQUEST: 30000,      // 30 seconds
  AI_GENERATION: 120000,   // 2 minutes
  BULK_OPERATION: 300000,  // 5 minutes
  HEALTH_CHECK: 5000,      // 5 seconds
} as const