'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Slider } from '@/components/ui/slider';

interface StepTechnicalSpecsProps {
  formData: {
    wordCount: string;
    chapters: string;
    narrativeVoice?: string;
    tense?: string;
    povCharacters?: string;
    [key: string]: string | undefined;
  };
  updateFormData: (field: string, value: string) => void;
  mode?: 'live' | 'demo';
}

export function StepTechnicalSpecs({ formData, updateFormData, mode }: StepTechnicalSpecsProps) {
  const wordCountValue = parseInt(formData.wordCount) || 80000;
  const chaptersValue = parseInt(formData.chapters) || 25;

  return (
    <Card className="max-w-4xl mx-auto shadow-sm">
      <CardHeader>
        <div className="flex items-center gap-2 mb-4">
          <Settings className="h-6 w-6 text-primary" />
          <CardTitle>Technical Specifications</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="wordCount">Target Word Count: {wordCountValue.toLocaleString()} words</Label>
          <Slider
            id="wordCount"
            min={20000}
            max={200000}
            step={5000}
            value={[wordCountValue]}
            onValueChange={([value]) => updateFormData('wordCount', value.toString())}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>20k (Novella)</span>
            <span>80k (Standard Novel)</span>
            <span>200k (Epic)</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            This affects story scope, subplot complexity, and character development depth
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="chapters">Number of Chapters: {chaptersValue}</Label>
          <Slider
            id="chapters"
            min={10}
            max={50}
            step={1}
            value={[chaptersValue]}
            onValueChange={([value]) => updateFormData('chapters', value.toString())}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>10 chapters</span>
            <span>30 chapters</span>
            <span>50 chapters</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Average chapter length: {Math.round(wordCountValue / chaptersValue).toLocaleString()} words
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="narrativeVoice">Narrative Voice</Label>
            <Select 
              value={formData.narrativeVoice || 'third-limited'}
              onValueChange={(value) => updateFormData('narrativeVoice', value)}
            >
              <SelectTrigger id="narrativeVoice">
                <SelectValue placeholder="Select narrative voice" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="first">First Person</SelectItem>
                <SelectItem value="third-limited">Third Person Limited</SelectItem>
                <SelectItem value="third-omniscient">Third Person Omniscient</SelectItem>
                <SelectItem value="second">Second Person</SelectItem>
                <SelectItem value="multiple">Multiple POV</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tense">Tense</Label>
            <Select 
              value={formData.tense || 'past'}
              onValueChange={(value) => updateFormData('tense', value)}
            >
              <SelectTrigger id="tense">
                <SelectValue placeholder="Select tense" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="past">Past Tense</SelectItem>
                <SelectItem value="present">Present Tense</SelectItem>
                <SelectItem value="mixed">Mixed (flashbacks, etc.)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {formData.narrativeVoice === 'multiple' && (
          <div className="space-y-2">
            <Label htmlFor="povCharacters">POV Characters</Label>
            <Input
              id="povCharacters"
              placeholder="List main POV characters (e.g., Sarah, Marcus, Elena)"
              value={formData.povCharacters || ''}
              onChange={(e) => updateFormData('povCharacters', e.target.value)}
            />
            <p className="text-sm text-muted-foreground">
              Each POV character will have distinct chapters and narrative style
            </p>
          </div>
        )}

        {mode === 'demo' && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Demo projects use standard specifications. In a real project, these settings 
              directly influence how the AI structures and writes your novel.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}