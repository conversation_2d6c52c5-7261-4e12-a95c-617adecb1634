import { ExportOptions } from '../export-types'

export abstract class BaseFormatter {
  protected escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  protected processSceneBreaks(content: string, symbol: string = '* * *'): string {
    return content.replace(/^(\*\*\*|\* \* \*|---)$/gm, symbol);
  }

  protected cleanWhitespace(content: string): string {
    return content.replace(/\n{3,}/g, '\n\n');
  }

  protected convertSmartQuotes(content: string): string {
    return content
      .replace(/"([^"]*)"/g, '"$1"')
      .replace(/'([^']*)'/g, "'$1'");
  }

  protected convertEmDashes(content: string): string {
    return content.replace(/--/g, '—');
  }

  protected formatChapterNumber(chapterNumber: number, style: ExportOptions['chapterNumbering'] = 'numeric'): string {
    switch (style) {
      case 'word':
        return this.numberToWords(chapterNumber);
      case 'roman':
        return this.toRoman(chapterNumber);
      default:
        return chapterNumber.toString();
    }
  }

  private numberToWords(num: number): string {
    const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

    if (num < 10) return ones[num];
    if (num < 20) return teens[num - 10];
    if (num < 100) {
      const ten = Math.floor(num / 10);
      const one = num % 10;
      return tens[ten] + (one ? '-' + ones[one] : '');
    }
    return num.toString(); // Fallback for larger numbers
  }

  private toRoman(num: number): string {
    const romanNumerals: [number, string][] = [
      [1000, 'M'], [900, 'CM'], [500, 'D'], [400, 'CD'],
      [100, 'C'], [90, 'XC'], [50, 'L'], [40, 'XL'],
      [10, 'X'], [9, 'IX'], [5, 'V'], [4, 'IV'], [1, 'I']
    ];

    let result = '';
    for (const [value, numeral] of romanNumerals) {
      while (num >= value) {
        result += numeral;
        num -= value;
      }
    }
    return result;
  }
}