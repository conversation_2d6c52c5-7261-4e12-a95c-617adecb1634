-- Check achievement system status

-- 1. Check if tables exist
SELECT 
    table_name,
    CASE 
        WHEN table_name IS NOT NULL THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status
FROM (
    VALUES 
        ('achievements'),
        ('user_achievements'),
        ('achievement_progress')
) AS required_tables(name)
LEFT JOIN information_schema.tables ON table_name = name
WHERE table_schema = 'public';

-- 2. Check achievements table structure (if exists)
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns
WHERE table_name = 'achievements'
ORDER BY ordinal_position;

-- 3. Count existing achievements
SELECT COUNT(*) as achievement_count FROM achievements;

-- 4. Check if function exists
SELECT EXISTS (
    SELECT 1 
    FROM pg_proc 
    WHERE proname = 'check_and_unlock_achievements'
) as function_exists;

-- 5. Check for any user achievements
SELECT 
    COUNT(*) as total_user_achievements,
    COUNT(DISTINCT user_id) as users_with_achievements
FROM user_achievements;

-- 6. List first 5 achievements (if any)
SELECT code, title, category, tier, points 
FROM achievements 
LIMIT 5;