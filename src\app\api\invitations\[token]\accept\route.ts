import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'

// Token validation schema
const tokenSchema = z.string().uuid();

export const POST = UnifiedAuthService.withAuth(async (
  request: AuthenticatedRequest,
  { params }: { params: { token: string } }
) => {
  const inviteToken = params.token;

  // Validate token format
  if (!tokenSchema.safeParse(inviteToken).success) {
    return UnifiedResponse.error('Invalid invitation token format', 400);
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'invitation-accept',
    rateLimitCost: 3,
    maxRequestSize: 1024,
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      // Check if user already has too many active collaborations
      const supabase = await createTypedServerClient();
      const { count } = await supabase
        .from('project_collaborators')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('status', 'active');

      // Limit users to 50 active collaborations
      if (count && count >= 50) {
        return { valid: false, error: 'You have reached the maximum number of active project collaborations' };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const supabase = await createTypedServerClient();

    // Find the invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('project_invitations')
      .select(`
        *,
        project:projects(
          id,
          title,
          user_id
        ),
        inviter:users!inviter_id(
          id,
          email,
          profiles(full_name)
        )
      `)
      .eq('token', inviteToken)
      .eq('status', 'pending')
      .single()

    if (inviteError || !invitation) {
      logger.warn('Invalid invitation token', {
        token: inviteToken,
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Invitation not found or already used', 404);
    }

    // Check if invitation has expired
    if (new Date(invitation.expires_at) < new Date()) {
      logger.warn('Expired invitation attempt', {
        invitationId: invitation.id,
        expiredAt: invitation.expires_at,
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('This invitation has expired', 410);
    }

    // Check if user's email matches the invitation (if not already registered)
    const { data: userData } = await supabase
      .from('users')
      .select('email')
      .eq('id', user.id)
      .single();

    if (invitation.email && userData?.email !== invitation.email) {
      logger.warn('Email mismatch for invitation', {
        invitationEmail: invitation.email,
        userEmail: userData?.email,
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('This invitation was sent to a different email address', 403);
    }

    // Check if user is already a collaborator
    const { data: existingCollab } = await supabase
      .from('project_collaborators')
      .select('id, status')
      .eq('project_id', invitation.project_id)
      .eq('user_id', user.id)
      .single();

    if (existingCollab) {
      if (existingCollab.status === 'active') {
        return UnifiedResponse.error('You are already a collaborator on this project', 409);
      } else if (existingCollab.status === 'removed') {
        // Reactivate the collaboration
        const { error: reactivateError } = await supabase
          .from('project_collaborators')
          .update({
            status: 'active',
            role: invitation.role,
            rejoined_at: new Date().toISOString()
          })
          .eq('id', existingCollab.id);

        if (reactivateError) {
          throw reactivateError;
        }

        // Mark invitation as accepted
        await supabase
          .from('project_invitations')
          .update({
            status: 'accepted',
            accepted_at: new Date().toISOString(),
            user_id: user.id
          })
          .eq('id', invitation.id);

        logger.info('Collaboration reactivated via invitation', {
          projectId: invitation.project_id,
          userId: user.id,
          invitationId: invitation.id,
          clientIP: context.clientIP
        });

        return UnifiedResponse.success({
          project: {
            id: invitation.project.id,
            title: invitation.project.title,
            role: invitation.role
          },
          message: 'You have been re-added to the project'
        });
      }
    }

    // Mark invitation as accepted
    const { error: updateError } = await supabase
      .from('project_invitations')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
        user_id: user.id
      })
      .eq('id', invitation.id)

    if (updateError) {
      logger.error('Failed to accept invitation', updateError, {
        invitationId: invitation.id,
        userId: user.id
      });
      throw updateError;
    }

    // Add user as project collaborator
    const { error: collabError } = await supabase
      .from('project_collaborators')
      .insert({
        project_id: invitation.project_id,
        user_id: user.id,
        role: invitation.role,
        status: 'active',
        invited_by: invitation.inviter_id,
        joined_at: new Date().toISOString()
      })

    if (collabError) {
      logger.error('Failed to add collaborator', collabError, {
        projectId: invitation.project_id,
        userId: user.id
      });
      
      // Revert invitation status
      await supabase
        .from('project_invitations')
        .update({ status: 'pending' })
        .eq('id', invitation.id);
      
      throw collabError;
    }

    // Create initial collaboration session for this user
    const sessionId = `project:${invitation.project.id}:welcome`
    await supabase
      .from('collaboration_sessions')
      .upsert({
        id: sessionId,
        project_id: invitation.project.id,
        owner_id: invitation.project.user_id,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      })

    logger.info('Invitation accepted', {
      projectId: invitation.project_id,
      userId: user.id,
      invitationId: invitation.id,
      role: invitation.role,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      project: {
        id: invitation.project.id,
        title: invitation.project.title,
        role: invitation.role
      },
      message: 'Successfully joined the project'
    });

  } catch (error) {
    logger.error('Error accepting invitation', error, {
      userId: user.id,
      token: inviteToken,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to accept invitation');
  }
});

export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  const inviteToken = params.token;

  // Validate token format
  if (!tokenSchema.safeParse(inviteToken).success) {
    return UnifiedResponse.error('Invalid invitation token format', 400);
  }

  // Basic rate limiting for public endpoint
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'invitation-view',
    rateLimitCost: 1,
    maxRequestSize: 1024
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const supabase = await createTypedServerClient();

    // Find the invitation
    const { data: invitation, error } = await supabase
      .from('project_invitations')
      .select(`
        *,
        project:projects(
          id,
          title
        ),
        inviter:users!inviter_id(
          id,
          email,
          profiles(full_name)
        )
      `)
      .eq('token', inviteToken)
      .eq('status', 'pending')
      .single()

    if (error || !invitation) {
      logger.warn('Invalid invitation token lookup', {
        token: inviteToken,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Invitation not found', 404);
    }

    // Check if invitation has expired
    if (new Date(invitation.expires_at) < new Date()) {
      return UnifiedResponse.error('This invitation has expired', 410);
    }

    return UnifiedResponse.success({
      invitation: {
        id: invitation.id,
        project: {
          id: invitation.project.id,
          title: invitation.project.title
        },
        inviter: {
          name: invitation.inviter.profiles?.full_name || invitation.inviter.email,
          email: invitation.inviter.email
        },
        role: invitation.role,
        expires_at: invitation.expires_at
      }
    });

  } catch (error) {
    logger.error('Error fetching invitation', error, {
      token: inviteToken,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to fetch invitation details');
  }
}