#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Migrating loading states to unified components...\n');

// Load the report from previous analysis
const reportPath = path.join(process.cwd(), 'scripts/loading-states-report.json');
const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));

// Filter files that need updates
const filesToUpdate = report.files.filter(f => f.status === 'needs-update');

console.log(`Found ${filesToUpdate.length} files to update\n`);

// Migration templates based on file paths
const getMigrationTemplate = (filePath) => {
  const fileName = path.basename(path.dirname(filePath));
  
  // Specific templates for different sections
  if (fileName === 'achievements') {
    return `import { PageLoading, SkeletonCard } from '@/components/ui/unified-loading'

export default function AchievementsLoading() {
  return (
    <div className="container py-6 space-y-6">
      <PageLoading text="Loading achievements..." />
    </div>
  )
}`;
  }
  
  if (fileName === 'analytics') {
    return `import { SkeletonCard, SkeletonTable } from '@/components/ui/unified-loading'
import { Skeleton } from '@/components/ui/skeleton'

export default function AnalyticsLoading() {
  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-96" />
      </div>
      
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <SkeletonCard key={i} />
        ))}
      </div>
      
      {/* Chart Area */}
      <SkeletonCard className="h-96" />
      
      {/* Table */}
      <SkeletonTable rows={5} cols={4} />
    </div>
  )
}`;
  }
  
  if (fileName === 'billing') {
    return `import { SkeletonCard } from '@/components/ui/unified-loading'
import { Skeleton } from '@/components/ui/skeleton'

export default function BillingLoading() {
  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-4 w-64" />
      </div>
      
      {/* Subscription Cards */}
      <div className="grid gap-6 md:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <SkeletonCard key={i} className="h-64" />
        ))}
      </div>
    </div>
  )
}`;
  }
  
  if (fileName === 'goals') {
    return `import { SkeletonCard, SkeletonList } from '@/components/ui/unified-loading'
import { Skeleton } from '@/components/ui/skeleton'

export default function GoalsLoading() {
  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-96" />
      </div>
      
      {/* Goal Cards */}
      <div className="grid gap-4 md:grid-cols-2">
        {[...Array(4)].map((_, i) => (
          <SkeletonCard key={i} />
        ))}
      </div>
      
      {/* Progress List */}
      <SkeletonList items={5} />
    </div>
  )
}`;
  }
  
  if (fileName === 'projects') {
    return `import { ProjectLoading, SkeletonCard } from '@/components/ui/unified-loading'

export default function ProjectsLoading() {
  return (
    <div className="container py-6 space-y-6">
      <ProjectLoading text="Loading your projects..." />
    </div>
  )
}`;
  }
  
  if (fileName === 'write') {
    return `import { WritingLoading } from '@/components/ui/unified-loading'

export default function WriteLoading() {
  return <WritingLoading text="Loading editor..." />
}`;
  }
  
  if (fileName === 'series') {
    return `import { SkeletonCard } from '@/components/ui/unified-loading'
import { Skeleton } from '@/components/ui/skeleton'

export default function SeriesLoading() {
  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      {/* Series Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <SkeletonCard key={i} className="h-48" />
        ))}
      </div>
    </div>
  )
}`;
  }
  
  if (fileName === 'settings') {
    return `import { SkeletonForm } from '@/components/ui/unified-loading'
import { Skeleton } from '@/components/ui/skeleton'

export default function SettingsLoading() {
  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-4 w-64" />
      </div>
      
      {/* Settings Form */}
      <SkeletonForm fields={6} />
    </div>
  )
}`;
  }
  
  if (fileName === 'universes') {
    return `import { SkeletonCard } from '@/components/ui/unified-loading'
import { Skeleton } from '@/components/ui/skeleton'

export default function UniversesLoading() {
  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-40" />
      </div>
      
      {/* Universe Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <SkeletonCard key={i} className="h-64" />
        ))}
      </div>
    </div>
  )
}`;
  }
  
  // Default templates
  if (filePath.includes('(marketing)')) {
    return `import { PageLoading } from '@/components/ui/unified-loading'

export default function MarketingLoading() {
  return <PageLoading text="Loading..." />
}`;
  }
  
  if (filePath.includes('(dashboard)\\loading.tsx')) {
    return `import { PageLoading } from '@/components/ui/unified-loading'

export default function DashboardLayoutLoading() {
  return <PageLoading text="Loading dashboard..." />
}`;
  }
  
  // Root loading
  return `import { PageLoading } from '@/components/ui/unified-loading'

export default function Loading() {
  return <PageLoading />
}`;
};

// Update files
let successCount = 0;
let errorCount = 0;

for (const fileInfo of filesToUpdate) {
  const filePath = path.join(process.cwd(), fileInfo.file);
  
  try {
    const newContent = getMigrationTemplate(fileInfo.file);
    fs.writeFileSync(filePath, newContent);
    console.log(`✅ Updated: ${fileInfo.file}`);
    successCount++;
  } catch (error) {
    console.error(`❌ Error updating ${fileInfo.file}:`, error.message);
    errorCount++;
  }
}

// Summary
console.log('\n📊 Migration Summary:');
console.log(`   Files updated: ${successCount}`);
console.log(`   Errors: ${errorCount}`);
console.log(`   Total processed: ${filesToUpdate.length}`);

console.log('\n📝 Next Steps:');
console.log('1. Review the updated loading files');
console.log('2. Test loading states in the browser');
console.log('3. Adjust templates if needed');
console.log('4. Ensure consistent loading experience');

console.log('\n✅ Loading states migration complete!');