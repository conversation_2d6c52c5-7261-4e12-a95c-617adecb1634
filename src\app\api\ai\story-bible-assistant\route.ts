import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import OpenAI from 'openai'

export const runtime = 'nodejs'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

interface StoryBibleData {
  characters: Array<{ id: string; name: string; role: string; description: string }>
  worldRules: Record<string, string>
  timeline: Array<{ id?: string; event: string; chapter: number }>
  plotThreads: Array<{ id: string; description: string; status: string }>
}

interface AISuggestion {
  id: string
  type: 'character' | 'world' | 'plot' | 'timeline'
  title: string
  description: string
  confidence: number
  impact: 'low' | 'medium' | 'high'
  actionable: boolean
  data?: any
}

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  try {
    const body = await request.json()
    const { action, projectId, storyBible, message, suggestion, context } = body

    if (!action || !projectId) {
      return handleAPIError(new ValidationError('Missing required fields'))
    }

    // Verify user has project access using UnifiedAuthService
    const user = await UnifiedAuthService.authenticateProjectAccess(request, projectId)
    if (!user) {
      return UnifiedResponse.forbidden('Project access denied')
    }

    // Get project details
    const supabase = await createTypedServerClient()
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title')
      .eq('id', projectId)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.notFound('Project')
    }

    switch (action) {
      case 'initialize':
        return await handleInitialize(projectId, storyBible, project.title)
      
      case 'chat':
        return await handleChat(projectId, message, storyBible, context)
      
      case 'generate_suggestions':
        return await handleGenerateSuggestions(projectId, storyBible, body.excludeExisting)
      
      case 'apply_suggestion':
        return await handleApplySuggestion(projectId, suggestion, supabase)
      
      case 'deep_analysis':
        return await handleDeepAnalysis(projectId, storyBible)
      
      default:
        return UnifiedResponse.error({
          message: 'Invalid action',
          code: 'VALIDATION_ERROR',
          userMessage: 'Please specify a valid action'
        }, undefined, 400)
    }
  } catch (error) {
    logger.error('Error in story bible assistant API:', error)
    return UnifiedResponse.error({
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      details: error
    }, undefined, 500)
  }
})

async function handleInitialize(projectId: string, storyBible: StoryBibleData, projectTitle: string) {
  try {
    const analysis = analyzeStoryBible(storyBible)
    const suggestions = await generateInitialSuggestions(storyBible, analysis)
    
    const greeting = `Hello! I'm your AI Story Bible Assistant for "${projectTitle}". I've analyzed your story bible and found ${storyBible.characters.length} characters, ${Object.keys(storyBible.worldRules).length} world rules, ${storyBible.timeline.length} timeline events, and ${storyBible.plotThreads.length} plot threads. How can I help you improve your story today?`

    return UnifiedResponse.success({
      greeting,
      suggestions: suggestions.slice(0, 5), // Limit initial suggestions
      analysis
    })
  } catch (error) {
    logger.error('Error initializing assistant:', error)
    return UnifiedResponse.error({
      message: 'Failed to initialize assistant',
      code: 'AI_SERVICE_ERROR',
      details: error
    }, undefined, 500)
  }
}

async function handleChat(projectId: string, message: string, storyBible: StoryBibleData, context: any) {
  try {
    const systemPrompt = buildSystemPrompt(storyBible)
    const conversationHistory = context?.recentMessages || []
    
    const messages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory.slice(-10).map((msg: any) => ({
        role: msg.role,
        content: msg.content
      })),
      { role: 'user', content: message }
    ]

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: messages as any,
      max_tokens: 500,
      temperature: 0.7,
    })

    const response = completion.choices[0]?.message?.content || 'I apologize, but I couldn\'t generate a response.'

    // Analyze the response to categorize it and extract metadata
    const responseType = categorizeResponse(message, response)
    const metadata = extractMetadata(message, response, storyBible)

    // Check if we should generate new suggestions based on the conversation
    let newSuggestions: AISuggestion[] = []
    if (shouldGenerateSuggestions(message, response)) {
      newSuggestions = await generateContextualSuggestions(message, response, storyBible)
    }

    return UnifiedResponse.success({
      response,
      type: responseType,
      metadata,
      suggestions: newSuggestions
    })
  } catch (error) {
    logger.error('Error in chat handler:', error)
    return UnifiedResponse.error({
      message: 'Failed to get AI response',
      code: 'AI_SERVICE_ERROR',
      details: error
    }, undefined, 500)
  }
}

async function handleGenerateSuggestions(projectId: string, storyBible: StoryBibleData, excludeExisting: string[] = []) {
  try {
    const analysis = analyzeStoryBible(storyBible)
    const suggestions = await generateComprehensiveSuggestions(storyBible, analysis)
    
    // Filter out existing suggestions
    const newSuggestions = suggestions.filter(s => !excludeExisting.includes(s.id))
    
    return UnifiedResponse.success({
      suggestions: newSuggestions.slice(0, 10) // Limit to 10 new suggestions
    })
  } catch (error) {
    logger.error('Error generating suggestions:', error)
    return UnifiedResponse.error({
      message: 'Failed to generate suggestions',
      code: 'AI_SERVICE_ERROR',
      details: error
    }, undefined, 500)
  }
}

async function handleApplySuggestion(projectId: string, suggestion: AISuggestion, supabase: any) {
  try {
    let updatedBible = null

    switch (suggestion.type) {
      case 'character':
        if (suggestion.data?.character) {
          const { error } = await supabase
            .from('characters')
            .insert([{
              project_id: projectId,
              ...suggestion.data.character
            }])
          
          if (error) throw error
        }
        break

      case 'world':
        if (suggestion.data?.worldRule) {
          const { error } = await supabase
            .from('story_bible')
            .upsert([{
              project_id: projectId,
              entry_type: 'world_rule',
              entry_key: suggestion.data.worldRule.key,
              entry_data: { value: suggestion.data.worldRule.value }
            }])
          
          if (error) throw error
        }
        break

      case 'timeline':
        if (suggestion.data?.event) {
          const { error } = await supabase
            .from('story_bible')
            .upsert([{
              project_id: projectId,
              entry_type: 'timeline_event',
              entry_key: Date.now().toString(),
              entry_data: suggestion.data.event
            }])
          
          if (error) throw error
        }
        break

      case 'plot':
        if (suggestion.data?.plotThread) {
          const { error } = await supabase
            .from('story_bible')
            .upsert([{
              project_id: projectId,
              entry_type: 'plot_thread',
              entry_key: Date.now().toString(),
              entry_data: suggestion.data.plotThread
            }])
          
          if (error) throw error
        }
        break
    }

    return UnifiedResponse.success({
      applied: true,
      updatedBible
    })
  } catch (error) {
    logger.error('Error applying suggestion:', error)
    return UnifiedResponse.error({
      message: 'Failed to apply suggestion',
      code: 'DATABASE_ERROR',
      details: error
    }, undefined, 500)
  }
}

async function handleDeepAnalysis(projectId: string, storyBible: StoryBibleData) {
  try {
    const analysis = await performDeepAnalysis(storyBible)
    const suggestions = await generateAnalysisBasedSuggestions(storyBible, analysis)

    return UnifiedResponse.success({
      analysis,
      suggestions
    })
  } catch (error) {
    logger.error('Error in deep analysis:', error)
    return UnifiedResponse.error({
      message: 'Failed to perform deep analysis',
      code: 'AI_SERVICE_ERROR',
      details: error
    }, undefined, 500)
  }
}

function buildSystemPrompt(storyBible: StoryBibleData): string {
  return `You are an AI Story Bible Assistant helping authors maintain consistency and develop their stories. 

Current Story Bible Summary:
- Characters: ${storyBible.characters.length} (${storyBible.characters.map(c => `${c.name} (${c.role})`).join(', ')})
- World Rules: ${Object.keys(storyBible.worldRules).length} defined
- Timeline Events: ${storyBible.timeline.length}
- Plot Threads: ${storyBible.plotThreads.length} (${storyBible.plotThreads.filter(p => p.status === 'active').length} active)

Your role is to:
1. Help maintain story consistency
2. Suggest improvements and additions
3. Answer questions about story elements
4. Identify potential plot holes or inconsistencies
5. Provide creative suggestions while respecting established canon

Be helpful, insightful, and constructive. Focus on storytelling craft and narrative coherence.
Keep responses concise but informative.`
}

function analyzeStoryBible(storyBible: StoryBibleData) {
  const charactersByRole = storyBible.characters.reduce((acc, char) => {
    acc[char.role] = (acc[char.role] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const plotByStatus = storyBible.plotThreads.reduce((acc, plot) => {
    acc[plot.status] = (acc[plot.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const timelineSpan = storyBible.timeline.length > 0
    ? Math.max(...storyBible.timeline.map(e => e.chapter)) - Math.min(...storyBible.timeline.map(e => e.chapter)) + 1
    : 0

  return {
    characterCount: storyBible.characters.length,
    charactersByRole,
    worldRuleCount: Object.keys(storyBible.worldRules).length,
    timelineEvents: storyBible.timeline.length,
    timelineSpan,
    plotThreads: storyBible.plotThreads.length,
    plotByStatus,
    completenessScore: calculateCompletenessScore(storyBible),
    consistencyScore: 85, // Would be calculated more thoroughly
    overallHealth: 82
  }
}

function calculateCompletenessScore(storyBible: StoryBibleData): number {
  let score = 0
  
  // Characters (25 points max)
  if (storyBible.characters.length > 0) score += 10
  if (storyBible.characters.length >= 3) score += 5
  if (storyBible.characters.some(c => c.role === 'protagonist')) score += 5
  if (storyBible.characters.some(c => c.role === 'antagonist')) score += 5

  // World rules (25 points max)
  const worldRuleCount = Object.keys(storyBible.worldRules).length
  if (worldRuleCount > 0) score += 10
  if (worldRuleCount >= 5) score += 10
  if (worldRuleCount >= 10) score += 5

  // Timeline (25 points max)
  if (storyBible.timeline.length > 0) score += 10
  if (storyBible.timeline.length >= 5) score += 10
  if (storyBible.timeline.length >= 10) score += 5

  // Plot threads (25 points max)
  if (storyBible.plotThreads.length > 0) score += 10
  if (storyBible.plotThreads.length >= 3) score += 10
  if (storyBible.plotThreads.some(p => p.status === 'active')) score += 5

  return Math.min(100, score)
}

async function generateInitialSuggestions(storyBible: StoryBibleData, analysis: any): Promise<AISuggestion[]> {
  const suggestions: AISuggestion[] = []

  // Character suggestions
  if (analysis.characterCount === 0) {
    suggestions.push({
      id: 'char-001',
      type: 'character',
      title: 'Add Main Characters',
      description: 'Start by defining your protagonist and key supporting characters.',
      confidence: 0.95,
      impact: 'high',
      actionable: true,
      data: null
    })
  } else if (!analysis.charactersByRole.antagonist) {
    suggestions.push({
      id: 'char-002',
      type: 'character',
      title: 'Define Antagonist',
      description: 'Every compelling story needs opposition. Consider adding an antagonist.',
      confidence: 0.8,
      impact: 'medium',
      actionable: true,
      data: null
    })
  }

  // World building suggestions
  if (analysis.worldRuleCount === 0) {
    suggestions.push({
      id: 'world-001',
      type: 'world',
      title: 'Establish World Rules',
      description: 'Define the fundamental rules and systems that govern your story world.',
      confidence: 0.9,
      impact: 'high',
      actionable: true,
      data: null
    })
  }

  // Plot suggestions
  if (analysis.plotThreads === 0) {
    suggestions.push({
      id: 'plot-001',
      type: 'plot',
      title: 'Create Plot Threads',
      description: 'Outline the main storylines and subplots that will drive your narrative.',
      confidence: 0.95,
      impact: 'high',
      actionable: true,
      data: null
    })
  } else if (!analysis.plotByStatus.active) {
    suggestions.push({
      id: 'plot-002',
      type: 'plot',
      title: 'Activate Plot Threads',
      description: 'Mark which plot threads are currently driving your story forward.',
      confidence: 0.7,
      impact: 'medium',
      actionable: true,
      data: null
    })
  }

  // Timeline suggestions
  if (analysis.timelineEvents === 0) {
    suggestions.push({
      id: 'timeline-001',
      type: 'timeline',
      title: 'Build Story Timeline',
      description: 'Create a chronological sequence of key events in your story.',
      confidence: 0.8,
      impact: 'medium',
      actionable: true,
      data: null
    })
  }

  return suggestions
}

async function generateComprehensiveSuggestions(storyBible: StoryBibleData, analysis: any): Promise<AISuggestion[]> {
  const suggestions = await generateInitialSuggestions(storyBible, analysis)
  
  // Add more advanced suggestions based on existing content
  if (storyBible.characters.length >= 2) {
    suggestions.push({
      id: 'char-relations',
      type: 'character',
      title: 'Develop Character Relationships',
      description: 'Explore the connections and conflicts between your existing characters.',
      confidence: 0.75,
      impact: 'medium',
      actionable: false,
      data: null
    })
  }

  if (Object.keys(storyBible.worldRules).length >= 3) {
    suggestions.push({
      id: 'world-consistency',
      type: 'world',
      title: 'World Rule Consistency Check',
      description: 'Review your world rules for potential contradictions or gaps.',
      confidence: 0.6,
      impact: 'low',
      actionable: false,
      data: null
    })
  }

  return suggestions
}

async function generateContextualSuggestions(message: string, response: string, storyBible: StoryBibleData): Promise<AISuggestion[]> {
  // Generate suggestions based on the conversation context
  return []
}

async function performDeepAnalysis(storyBible: StoryBibleData) {
  const analysis = analyzeStoryBible(storyBible)
  
  const insights = []
  
  // Character analysis
  if (analysis.charactersByRole.protagonist > 1) {
    insights.push({
      type: 'observation',
      title: 'Multiple Protagonists',
      description: 'You have multiple protagonists. Consider how their storylines intersect and whether each has a distinct arc.'
    })
  }

  // Plot analysis
  if (analysis.plotByStatus.active > 5) {
    insights.push({
      type: 'weakness',
      title: 'Many Active Plots',
      description: 'You have many active plot threads. Consider whether all are necessary or if some should be resolved first.'
    })
  }

  // Completeness analysis
  if (analysis.completenessScore >= 80) {
    insights.push({
      type: 'strength',
      title: 'Well-Developed Story Bible',
      description: 'Your story bible is comprehensive and well-structured. Great foundation for consistent storytelling.'
    })
  }

  return {
    overallScore: analysis.overallHealth,
    consistencyScore: analysis.consistencyScore,
    completenessScore: analysis.completenessScore,
    summary: `Your story bible shows ${insights.length} key insights across characters, world-building, and plot structure.`,
    insights
  }
}

async function generateAnalysisBasedSuggestions(storyBible: StoryBibleData, analysis: any): Promise<AISuggestion[]> {
  const suggestions: AISuggestion[] = []
  
  analysis.insights.forEach((insight: any, index: number) => {
    if (insight.type === 'weakness') {
      suggestions.push({
        id: `analysis-${index}`,
        type: 'plot', // This would be determined by the insight content
        title: `Address: ${insight.title}`,
        description: `Based on analysis: ${insight.description}`,
        confidence: 0.7,
        impact: 'medium',
        actionable: false,
        data: null
      })
    }
  })
  
  return suggestions
}

function categorizeResponse(message: string, response: string): string {
  if (message.toLowerCase().includes('suggest') || message.toLowerCase().includes('recommend')) {
    return 'suggestion'
  }
  if (message.toLowerCase().includes('analyze') || message.toLowerCase().includes('check')) {
    return 'analysis'
  }
  if (message.toLowerCase().includes('how') || message.toLowerCase().includes('what')) {
    return 'question'
  }
  return 'question'
}

function extractMetadata(message: string, response: string, storyBible: StoryBibleData) {
  let category: string | undefined
  
  if (message.toLowerCase().includes('character')) category = 'character'
  else if (message.toLowerCase().includes('world') || message.toLowerCase().includes('setting')) category = 'world'
  else if (message.toLowerCase().includes('plot') || message.toLowerCase().includes('story')) category = 'plot'
  else if (message.toLowerCase().includes('timeline') || message.toLowerCase().includes('event')) category = 'timeline'
  
  return {
    category,
    confidence: 0.8,
    source: 'ai-assistant'
  }
}

function shouldGenerateSuggestions(message: string, response: string): boolean {
  const suggestionTriggers = ['suggest', 'recommend', 'improve', 'add', 'enhance', 'develop']
  return suggestionTriggers.some(trigger => 
    message.toLowerCase().includes(trigger) || response.toLowerCase().includes(trigger)
  )
}