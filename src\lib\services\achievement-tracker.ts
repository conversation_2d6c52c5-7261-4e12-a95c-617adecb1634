/**
 * Achievement Tracking Service
 * Monitors user activities and unlocks achievements
 */

import { createClient } from '@/lib/supabase/client'
import { logger } from '@/lib/services/logger'
import { EventEmitter } from 'events'

// Achievement definitions
export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'writing' | 'ai' | 'collaboration' | 'project' | 'special'
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  points: number
  requirement: {
    type: string
    value: number
    comparison: 'gte' | 'lte' | 'eq' | 'custom'
  }
  hidden?: boolean
  dependsOn?: string[] // Achievement IDs that must be unlocked first
}

// Predefined achievements
export const ACHIEVEMENTS: Record<string, Achievement> = {
  // Writing Achievements
  first_words: {
    id: 'first_words',
    name: 'First Words',
    description: 'Write your first 100 words',
    icon: '✍️',
    category: 'writing',
    rarity: 'common',
    points: 10,
    requirement: { type: 'words_written', value: 100, comparison: 'gte' }
  },
  chapter_complete: {
    id: 'chapter_complete',
    name: 'Chapter Complete',
    description: 'Complete your first chapter',
    icon: '📖',
    category: 'writing',
    rarity: 'common',
    points: 20,
    requirement: { type: 'chapters_completed', value: 1, comparison: 'gte' }
  },
  prolific_writer: {
    id: 'prolific_writer',
    name: 'Prolific Writer',
    description: 'Write 10,000 words',
    icon: '🏆',
    category: 'writing',
    rarity: 'rare',
    points: 50,
    requirement: { type: 'words_written', value: 10000, comparison: 'gte' }
  },
  novelist: {
    id: 'novelist',
    name: 'Novelist',
    description: 'Write 50,000 words',
    icon: '📚',
    category: 'writing',
    rarity: 'epic',
    points: 100,
    requirement: { type: 'words_written', value: 50000, comparison: 'gte' }
  },
  epic_saga: {
    id: 'epic_saga',
    name: 'Epic Saga',
    description: 'Write 100,000 words',
    icon: '⚔️',
    category: 'writing',
    rarity: 'legendary',
    points: 200,
    requirement: { type: 'words_written', value: 100000, comparison: 'gte' }
  },
  
  // AI Achievements
  ai_assistant: {
    id: 'ai_assistant',
    name: 'AI Assistant',
    description: 'Use AI generation for the first time',
    icon: '🤖',
    category: 'ai',
    rarity: 'common',
    points: 10,
    requirement: { type: 'ai_generations', value: 1, comparison: 'gte' }
  },
  ai_collaborator: {
    id: 'ai_collaborator',
    name: 'AI Collaborator',
    description: 'Generate 50 AI suggestions',
    icon: '🧠',
    category: 'ai',
    rarity: 'rare',
    points: 30,
    requirement: { type: 'ai_generations', value: 50, comparison: 'gte' }
  },
  voice_master: {
    id: 'voice_master',
    name: 'Voice Master',
    description: 'Maintain 90% voice consistency across 5 chapters',
    icon: '🎭',
    category: 'ai',
    rarity: 'epic',
    points: 75,
    requirement: { type: 'voice_consistency_streak', value: 5, comparison: 'gte' }
  },
  
  // Collaboration Achievements
  team_player: {
    id: 'team_player',
    name: 'Team Player',
    description: 'Invite a collaborator to your project',
    icon: '👥',
    category: 'collaboration',
    rarity: 'common',
    points: 15,
    requirement: { type: 'collaborators_invited', value: 1, comparison: 'gte' }
  },
  
  // Project Achievements
  world_builder: {
    id: 'world_builder',
    name: 'World Builder',
    description: 'Create 10 characters',
    icon: '🌍',
    category: 'project',
    rarity: 'rare',
    points: 40,
    requirement: { type: 'characters_created', value: 10, comparison: 'gte' }
  },
  series_author: {
    id: 'series_author',
    name: 'Series Author',
    description: 'Create a book series',
    icon: '📚',
    category: 'project',
    rarity: 'rare',
    points: 50,
    requirement: { type: 'series_created', value: 1, comparison: 'gte' }
  },
  
  // Special Achievements
  night_owl: {
    id: 'night_owl',
    name: 'Night Owl',
    description: 'Write for 3 hours between midnight and 5 AM',
    icon: '🦉',
    category: 'special',
    rarity: 'rare',
    points: 25,
    requirement: { type: 'night_writing_hours', value: 3, comparison: 'gte' }
  },
  consistency_king: {
    id: 'consistency_king',
    name: 'Consistency King',
    description: 'Write every day for 7 days straight',
    icon: '👑',
    category: 'special',
    rarity: 'epic',
    points: 60,
    requirement: { type: 'writing_streak', value: 7, comparison: 'gte' }
  },
  speed_demon: {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Write 1000 words in 30 minutes',
    icon: '⚡',
    category: 'special',
    rarity: 'epic',
    points: 50,
    requirement: { type: 'words_per_30min', value: 1000, comparison: 'gte' },
    hidden: true
  }
}

interface UserStats {
  words_written: number
  chapters_completed: number
  ai_generations: number
  characters_created: number
  collaborators_invited: number
  series_created: number
  writing_sessions: number
  voice_consistency_scores: number[]
}

interface ActivityDataMap {
  'words_written': { count: number; sessionDuration?: number }
  'chapter_completed': Record<string, never>
  'ai_generation': Record<string, never>
  'character_created': Record<string, never>
  'collaborator_invited': Record<string, never>
  'series_created': Record<string, never>
  'voice_consistency_check': { score: number }
}

type ActivityType = keyof ActivityDataMap

export class AchievementTracker extends EventEmitter {
  private userId: string | null = null
  private userStats: UserStats = {
    words_written: 0,
    chapters_completed: 0,
    ai_generations: 0,
    characters_created: 0,
    collaborators_invited: 0,
    series_created: 0,
    writing_sessions: 0,
    voice_consistency_scores: []
  }
  private unlockedAchievements: Set<string> = new Set()
  private sessionStats = {
    sessionStartTime: 0,
    sessionWordsWritten: 0,
    nightWritingMinutes: 0,
    dailyWords: new Map<string, number>()
  }

  constructor() {
    super()
    this.initialize()
  }

  private async initialize() {
    try {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        this.userId = user.id
        await this.loadUserStats()
        await this.loadUnlockedAchievements()
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        try {
          if (session?.user) {
            this.userId = session.user.id
            await this.loadUserStats()
            await this.loadUnlockedAchievements()
          } else {
            this.userId = null
            this.reset()
          }
        } catch (error) {
          logger.error('Error handling auth state change:', error)
        }
      })
    } catch (error) {
      logger.error('Error initializing achievement tracker:', error)
      // Don't throw - the app should work even if achievements fail to initialize
    }
  }

  private reset() {
    this.userStats = {
      words_written: 0,
      chapters_completed: 0,
      ai_generations: 0,
      characters_created: 0,
      collaborators_invited: 0,
      series_created: 0,
      writing_sessions: 0,
      voice_consistency_scores: []
    }
    this.unlockedAchievements.clear()
  }

  private async loadUserStats() {
    if (!this.userId) return

    try {
      const supabase = createClient()
      
      // Load usage stats
      const { data: usage } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', this.userId)
        .single()

      if (usage) {
        this.userStats.words_written = usage.total_words_written || 0
        this.userStats.ai_generations = usage.ai_generations || 0
        this.userStats.characters_created = usage.characters_created || 0
      }

      // Load project stats
      const { data: projects } = await supabase
        .from('projects')
        .select('id')
        .eq('user_id', this.userId)

      const { data: chapters } = await supabase
        .from('chapters')
        .select('id, status')
        .in('project_id', projects?.map(p => p.id) || [])
        .eq('status', 'completed')

      this.userStats.chapters_completed = chapters?.length || 0

      // Load collaboration stats
      const { data: collaborators } = await supabase
        .from('project_collaborators')
        .select('id')
        .eq('invited_by', this.userId)

      this.userStats.collaborators_invited = collaborators?.length || 0

      // Load series stats
      const { data: series } = await supabase
        .from('series')
        .select('id')
        .eq('user_id', this.userId)

      this.userStats.series_created = series?.length || 0

    } catch (error) {
      logger.error('Failed to load user stats for achievements:', error)
    }
  }

  private async loadUnlockedAchievements() {
    if (!this.userId) return

    try {
      const supabase = createClient()
      const { data: achievements } = await supabase
        .from('user_achievements')
        .select('achievement_id')
        .eq('user_id', this.userId)

      if (achievements) {
        this.unlockedAchievements = new Set(achievements.map(a => a.achievement_id))
      }
    } catch (error) {
      logger.error('Failed to load unlocked achievements:', error)
    }
  }

  // Track various user activities
  async trackActivity<T extends ActivityType>(activity: T, data: ActivityDataMap[T]) {
    if (!this.userId) return

    try {
      switch (activity) {
        case 'words_written':
          const wordsData = data as ActivityDataMap['words_written']
          await this.trackWordsWritten(wordsData.count, wordsData.sessionDuration)
          break
        
        case 'chapter_completed':
          this.userStats.chapters_completed++
          await this.checkAchievements(['chapter_complete'])
          break
        
        case 'ai_generation':
          this.userStats.ai_generations++
          await this.checkAchievements(['ai_assistant', 'ai_collaborator'])
          break
        
        case 'character_created':
          this.userStats.characters_created++
          await this.checkAchievements(['world_builder'])
          break
        
        case 'collaborator_invited':
          this.userStats.collaborators_invited++
          await this.checkAchievements(['team_player'])
          break
        
        case 'series_created':
          this.userStats.series_created++
          await this.checkAchievements(['series_author'])
          break
        
        case 'voice_consistency_check':
          const voiceData = data as ActivityDataMap['voice_consistency_check']
          if (voiceData.score >= 0.9) {
            this.userStats.voice_consistency_scores.push(voiceData.score)
            await this.checkVoiceConsistencyStreak()
          }
          break
      }
    } catch (error) {
      logger.error(`Error tracking activity ${activity}:`, error)
      // Don't throw - we don't want to break the user's flow due to achievement tracking errors
    }
  }

  private async trackWordsWritten(count: number, sessionDuration?: number) {
    try {
      this.userStats.words_written += count
      this.sessionStats.sessionWordsWritten += count

    // Track daily words for streak
    const today = new Date().toDateString()
    const dailyWords = this.sessionStats.dailyWords.get(today) || 0
    this.sessionStats.dailyWords.set(today, dailyWords + count)

    // Check writing speed achievement
    if (sessionDuration && sessionDuration <= 30 * 60 * 1000) { // 30 minutes
      if (this.sessionStats.sessionWordsWritten >= 1000) {
        await this.checkAchievements(['speed_demon'])
      }
    }

    // Check night owl achievement
    const hour = new Date().getHours()
    if (hour >= 0 && hour < 5) {
      this.sessionStats.nightWritingMinutes += (sessionDuration || 0) / 60000
      if (this.sessionStats.nightWritingMinutes >= 180) { // 3 hours
        await this.checkAchievements(['night_owl'])
      }
    }

    // Check writing achievements
    await this.checkAchievements([
      'first_words',
      'prolific_writer',
      'novelist',
      'epic_saga'
    ])

    // Check writing streak
    await this.checkWritingStreak()
    } catch (error) {
      logger.error('Error tracking words written:', error)
      // Don't throw - continue gracefully
    }
  }

  private async checkVoiceConsistencyStreak() {
    try {
      const recentScores = this.userStats.voice_consistency_scores.slice(-5)
      if (recentScores.length >= 5 && recentScores.every(score => score >= 0.9)) {
        await this.checkAchievements(['voice_master'])
      }
    } catch (error) {
      logger.error('Error checking voice consistency streak:', error)
    }
  }

  private async checkWritingStreak() {
    try {
      const days = Array.from(this.sessionStats.dailyWords.keys())
        .map(dateStr => new Date(dateStr))
        .sort((a, b) => b.getTime() - a.getTime())

      let streak = 0
    const today = new Date()
    
    for (let i = 0; i < 7; i++) {
      const checkDate = new Date(today)
      checkDate.setDate(checkDate.getDate() - i)
      const dateStr = checkDate.toDateString()
      
      if (this.sessionStats.dailyWords.has(dateStr)) {
        streak++
      } else if (i > 0) { // Allow today to be empty
        break
      }
    }

    if (streak >= 7) {
      await this.checkAchievements(['consistency_king'])
    }
    } catch (error) {
      logger.error('Error checking writing streak:', error)
    }
  }

  private async checkAchievements(achievementIds?: string[]) {
    try {
      const toCheck = achievementIds || Object.keys(ACHIEVEMENTS)
    
    for (const achievementId of toCheck) {
      if (this.unlockedAchievements.has(achievementId)) continue
      
      const achievement = ACHIEVEMENTS[achievementId]
      if (!achievement) continue

      // Check dependencies
      if (achievement.dependsOn) {
        const hasAllDependencies = achievement.dependsOn.every(
          depId => this.unlockedAchievements.has(depId)
        )
        if (!hasAllDependencies) continue
      }

      // Check requirement
      if (this.meetsRequirement(achievement)) {
        await this.unlockAchievement(achievement)
      }
    }
    } catch (error) {
      logger.error('Error checking achievements:', error)
    }
  }

  private meetsRequirement(achievement: Achievement): boolean {
    const { type, value, comparison } = achievement.requirement
    const userValue = this.userStats[type] || 0

    switch (comparison) {
      case 'gte':
        return userValue >= value
      case 'lte':
        return userValue <= value
      case 'eq':
        return userValue === value
      case 'custom':
        // Handle custom requirements
        return this.checkCustomRequirement(achievement)
      default:
        return false
    }
  }

  private checkCustomRequirement(achievement: Achievement): boolean {
    // Implement custom logic for special achievements
    switch (achievement.id) {
      case 'voice_consistency_streak':
        return this.userStats.voice_consistency_scores
          .slice(-5)
          .every(score => score >= 0.9)
      default:
        return false
    }
  }

  private async unlockAchievement(achievement: Achievement) {
    if (!this.userId) return

    try {
      const supabase = createClient()
      
      // Record achievement unlock
      const { error } = await supabase
        .from('user_achievements')
        .insert({
          user_id: this.userId,
          achievement_id: achievement.id,
          achievement_name: achievement.name,
          achievement_data: {
            description: achievement.description,
            icon: achievement.icon,
            category: achievement.category,
            rarity: achievement.rarity,
            points: achievement.points
          }
        })

      if (error) throw error

      this.unlockedAchievements.add(achievement.id)
      
      // Emit achievement unlocked event
      this.emit('achievementUnlocked', achievement)
      
      logger.info('Achievement unlocked:', {
        userId: this.userId,
        achievementId: achievement.id,
        achievementName: achievement.name
      })
    } catch (error) {
      logger.error('Failed to unlock achievement:', error)
    }
  }

  // Get user's achievement progress
  getProgress() {
    const progress = Object.values(ACHIEVEMENTS).map(achievement => {
      const unlocked = this.unlockedAchievements.has(achievement.id)
      const currentValue = this.userStats[achievement.requirement.type] || 0
      const targetValue = achievement.requirement.value
      const progressPercent = Math.min(100, (currentValue / targetValue) * 100)

      return {
        ...achievement,
        unlocked,
        currentValue,
        targetValue,
        progressPercent
      }
    })

    return {
      totalPoints: progress
        .filter(a => a.unlocked)
        .reduce((sum, a) => sum + a.points, 0),
      unlockedCount: this.unlockedAchievements.size,
      totalCount: Object.keys(ACHIEVEMENTS).length,
      achievements: progress
    }
  }

  // Start a new writing session
  startSession() {
    this.sessionStats.sessionStartTime = Date.now()
    this.sessionStats.sessionWordsWritten = 0
  }

  // End writing session
  endSession() {
    const duration = Date.now() - this.sessionStats.sessionStartTime
    return {
      duration,
      wordsWritten: this.sessionStats.sessionWordsWritten
    }
  }
}

// Singleton instance
export const achievementTracker = new AchievementTracker()