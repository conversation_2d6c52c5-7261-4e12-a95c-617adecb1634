import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { StepComponentProps } from './wizard-types'
import { SPACING } from '@/lib/config/ui-config'

export function StepStructurePacing({ formData, updateFormData, mode }: StepComponentProps) {
  return (
    <div className={SPACING.SPACE_Y.MD}>
      <div>
        <Label htmlFor="structure" className="text-base font-semibold">
          Story Structure
        </Label>
        <Select
          value={formData.structure}
          onValueChange={(value) => updateFormData('structure', value)}
          disabled={mode === 'demo'}
        >
          <SelectTrigger id="structure" className="mt-2">
            <SelectValue placeholder="Select structure" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="three-act">Three-Act Structure</SelectItem>
            <SelectItem value="heros-journey">Hero's Journey</SelectItem>
            <SelectItem value="seven-point">Seven-Point Story Structure</SelectItem>
            <SelectItem value="save-cat">Save the Cat</SelectItem>
            <SelectItem value="fichtean-curve">Fichtean Curve</SelectItem>
            <SelectItem value="in-medias-res">In Medias Res</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-muted-foreground mt-1">
          The framework for how your story unfolds
        </p>
      </div>
      
      <div>
        <Label className="text-base font-semibold">Pacing Preference</Label>
        <RadioGroup
          value={formData.pacing}
          onValueChange={(value) => updateFormData('pacing', value)}
          disabled={mode === 'demo'}
          className="mt-3"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="slow" id="slow" />
            <Label htmlFor="slow" className="font-normal">
              Slow & Steady - Focus on character development and world-building
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="medium" id="medium" />
            <Label htmlFor="medium" className="font-normal">
              Balanced - Mix of action and introspection
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="fast" id="fast" />
            <Label htmlFor="fast" className="font-normal">
              Fast-Paced - Action-driven with quick plot progression
            </Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  )
}