import { AIServiceBase, AIGenerationOptions } from './ai-service-base'
import { ServiceResponse } from './types'
import { z } from 'zod'
import { AI_MODELS, AI_TEMPERATURE } from '../config/ai-settings'
import { ChapterStreamData, CharacterStreamData, AnalysisStreamData } from '@/lib/types/streaming-types'

// Context interface for stream data generation
interface StreamContext {
  genre?: string
  targetAudience?: string
  projectId?: string
  [key: string]: unknown
}

// Quality score schema
const qualityMetricsSchema = z.object({
  coherence: z.number().min(0).max(100),
  consistency: z.number().min(0).max(100),
  engagement: z.number().min(0).max(100),
  voiceAdherence: z.number().min(0).max(100)
})

const qualityScoreSchema = z.object({
  score: z.number().min(0).max(100),
  metrics: qualityMetricsSchema
})

// Chapter data extraction schema
const chapterDataSchema = z.object({
  title: z.string(),
  scenes: z.array(z.object({
    description: z.string(),
    characters: z.array(z.string()),
    location: z.string().optional()
  })),
  characters: z.array(z.string()),
  locations: z.array(z.string()),
  plotPoints: z.array(z.string()),
  themes: z.array(z.string()).optional(),
  conflicts: z.array(z.string()).optional()
})

// Character data extraction schema
const characterVoiceSchema = z.object({
  tone: z.string(),
  style: z.string(),
  vocabulary: z.array(z.string()),
  sentencePatterns: z.array(z.string()),
  narrativePerspective: z.string()
})

const characterProfileSchema = z.object({
  name: z.string(),
  description: z.string(),
  personality: z.array(z.string()),
  motivations: z.array(z.string()),
  fears: z.array(z.string()),
  backstory: z.string(),
  voice: characterVoiceSchema
})

const characterDataSchema = z.object({
  profile: characterProfileSchema,
  dialogue: z.array(z.object({
    text: z.string(),
    context: z.string().optional()
  })),
  relationships: z.array(z.object({
    character: z.string(),
    type: z.string(),
    description: z.string()
  })),
  arc: z.object({
    start: z.string(),
    middle: z.string(),
    end: z.string(),
    growth: z.string()
  })
})

export type ContentType = 'chapter' | 'character' | 'scene' | 'dialogue' | 'analysis'

export class StreamDataService extends AIServiceBase {
  private static instance: StreamDataService

  constructor() {
    super({
      name: 'StreamDataService',
      version: '1.0.0',
      endpoints: ['/api/ai/typed-stream'],
      dependencies: ['ai-service-base'],
      healthCheck: '/api/health'
    })
  }

  static getInstance(): StreamDataService {
    if (!StreamDataService.instance) {
      StreamDataService.instance = new StreamDataService()
    }
    return StreamDataService.instance
  }

  async generateStructuredData(
    contentType: ContentType,
    content: string,
    context?: StreamContext
  ): Promise<ServiceResponse<ChapterStreamData | CharacterStreamData | AnalysisStreamData | null>> {
    switch (contentType) {
      case 'chapter':
        return this.extractChapterData(content, context)
      case 'character':
        return this.extractCharacterData(content, context)
      case 'analysis':
        return this.generateAnalysisData(content, context)
      default:
        return null
    }
  }

  async calculateQualityScore(
    content: string,
    contentType: ContentType,
    context?: StreamContext
  ): Promise<ServiceResponse<{ score: number; metrics: Record<string, number> }>> {
    const systemPrompt = `You are an expert writing quality assessor specializing in creative fiction. 
Analyze the provided content and assess its quality across multiple dimensions.

Content Type: ${contentType}
${context?.genre ? `Genre: ${context.genre}` : ''}
${context?.targetAudience ? `Target Audience: ${context.targetAudience}` : ''}

Evaluate the content on these metrics (0-100 scale):
- Coherence: How well the ideas flow and connect logically
- Consistency: How well it maintains voice, style, and factual accuracy
- Engagement: How compelling and interesting the content is
- Voice Adherence: How well it matches the intended narrative voice

Provide an overall quality score (0-100) that represents the weighted average of these metrics.`

    const prompt = `Analyze the quality of this ${contentType} content:

"""
${content}
"""

Provide quality scores for each metric and an overall score.`

    const options: AIGenerationOptions = {
      model: AI_MODELS.PRIMARY,
      temperature: AI_TEMPERATURE.DETERMINISTIC,
      maxTokens: 500,
      systemPrompt,
      responseFormat: 'structured',
      structuredSchema: qualityScoreSchema
    }

    return this.generateWithAI<{ score: number; metrics: Record<string, number> }>(prompt, options)
  }

  private async extractChapterData(
    content: string,
    context?: StreamContext
  ): Promise<ServiceResponse<ChapterStreamData>> {
    const systemPrompt = `You are an expert literary analyst. Extract structured data from chapter content.
Identify key elements including scenes, characters, locations, plot points, themes, and conflicts.`

    const prompt = `Extract structured data from this chapter:

"""
${content}
"""

Provide:
1. A suitable chapter title based on the content
2. Scene breakdown with descriptions, characters, and locations
3. List of all characters mentioned
4. List of all locations mentioned
5. Key plot points that advance the story
6. Themes explored (if any)
7. Conflicts presented (if any)`

    const options: AIGenerationOptions = {
      model: AI_MODELS.PRIMARY,
      temperature: AI_TEMPERATURE.DETERMINISTIC,
      maxTokens: 1500,
      systemPrompt,
      responseFormat: 'structured',
      structuredSchema: chapterDataSchema
    }

    const result = await this.generateWithAI<any>(prompt, options)
    
    if (!result) {
      return {
        title: 'Untitled Chapter',
        content,
        scenes: [],
        wordCount: content.split(/\s+/).length,
        readingTime: Math.ceil(content.split(/\s+/).length / 250),
        characters: [],
        locations: [],
        plotPoints: []
      }
    }

    return {
      title: result.title,
      content,
      scenes: result.scenes,
      wordCount: content.split(/\s+/).length,
      readingTime: Math.ceil(content.split(/\s+/).length / 250),
      characters: result.characters,
      locations: result.locations,
      plotPoints: result.plotPoints,
      themes: result.themes,
      conflicts: result.conflicts
    }
  }

  private async extractCharacterData(
    content: string,
    context?: StreamContext
  ): Promise<ServiceResponse<CharacterStreamData>> {
    const systemPrompt = `You are an expert character analyst specializing in fiction writing.
Extract comprehensive character information from the provided content.
Analyze personality, motivations, voice patterns, and character development.`

    const prompt = `Extract character data from this content:

"""
${content}
"""

${context?.characterName ? `Character Name: ${context.characterName}` : ''}

Provide comprehensive character analysis including:
1. Character profile (name, description, personality traits, motivations, fears, backstory)
2. Voice characteristics (tone, style, vocabulary patterns, sentence patterns, narrative perspective)
3. Sample dialogue with context
4. Relationships with other characters
5. Character arc (beginning state, middle development, end state, overall growth)`

    const options: AIGenerationOptions = {
      model: AI_MODELS.PRIMARY,
      temperature: AI_TEMPERATURE.BALANCED,
      maxTokens: 2000,
      systemPrompt,
      responseFormat: 'structured',
      structuredSchema: characterDataSchema
    }

    return this.generateWithAI<CharacterStreamData>(prompt, options)
  }

  private async generateAnalysisData(
    content: string,
    context?: StreamContext
  ): Promise<ServiceResponse<AnalysisStreamData>> {
    // For analysis data, we'll use the content analysis service
    // This is a placeholder that returns basic analysis structure
    const wordCount = content.split(/\s+/).length
    const sentenceCount = content.split(/[.!?]+/).filter(s => s.trim()).length
    const paragraphCount = content.split(/\n\n+/).filter(p => p.trim()).length

    return {
      insights: [
        `Content contains ${wordCount} words across ${sentenceCount} sentences`,
        `Average sentence length: ${Math.round(wordCount / sentenceCount)} words`,
        `Organized into ${paragraphCount} paragraphs`
      ],
      metrics: {
        wordCount,
        sentenceCount,
        paragraphCount,
        avgSentenceLength: Math.round(wordCount / sentenceCount)
      },
      suggestions: [
        'Consider varying sentence length for better rhythm',
        'Ensure each paragraph has a clear focus',
        'Review for consistent voice throughout'
      ],
      themes: [],
      voiceProfile: {
        consistency: 85,
        appropriateness: 90
      }
    }
  }
}

// Export singleton instance
export const streamDataService = StreamDataService.getInstance()