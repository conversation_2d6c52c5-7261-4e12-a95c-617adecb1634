/**
 * API Endpoints Configuration
 * Centralized location for all API endpoint paths
 */

// Base API paths
export const API_BASE_PATH = '/api' as const

// API endpoint builder
export const apiEndpoint = (path: string) => `${API_BASE_PATH}${path}`

// API Endpoints organized by feature
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: apiEndpoint('/auth/login'),
    LOGOUT: apiEndpoint('/auth/logout'),
    REGISTER: apiEndpoint('/auth/register'),
    FORGOT_PASSWORD: apiEndpoint('/auth/forgot-password'),
    RESET_PASSWORD: apiEndpoint('/auth/reset-password'),
    VERIFY_EMAIL: apiEndpoint('/auth/verify-email'),
    REFRESH: apiEndpoint('/auth/refresh'),
    ME: apiEndpoint('/auth/me'),
  },

  // Projects
  PROJECTS: {
    LIST: apiEndpoint('/projects'),
    CREATE: apiEndpoint('/projects'),
    GET: (id: string) => apiEndpoint(`/projects/${id}`),
    UPDATE: (id: string) => apiEndpoint(`/projects/${id}`),
    DELETE: (id: string) => apiEndpoint(`/projects/${id}`),
    EXPORT: (id: string) => apiEndpoint(`/projects/${id}/export`),
    DUPLICATE: (id: string) => apiEndpoint(`/projects/${id}/duplicate`),
    COLLABORATORS: {
      LIST: (id: string) => apiEndpoint(`/projects/${id}/collaborators`),
      INVITE: (id: string) => apiEndpoint(`/projects/${id}/collaborators/invite`),
      REMOVE: (id: string, userId: string) => apiEndpoint(`/projects/${id}/collaborators/${userId}`),
      UPDATE_ROLE: (id: string, userId: string) => apiEndpoint(`/projects/${id}/collaborators/${userId}/role`),
    },
    ANALYTICS: (id: string) => apiEndpoint(`/projects/${id}/analytics`),
    VERSIONS: (id: string) => apiEndpoint(`/projects/${id}/versions`),
  },

  // Chapters
  CHAPTERS: {
    LIST: (projectId: string) => apiEndpoint(`/projects/${projectId}/chapters`),
    CREATE: (projectId: string) => apiEndpoint(`/projects/${projectId}/chapters`),
    GET: (projectId: string, chapterId: string) => apiEndpoint(`/projects/${projectId}/chapters/${chapterId}`),
    UPDATE: (projectId: string, chapterId: string) => apiEndpoint(`/projects/${projectId}/chapters/${chapterId}`),
    DELETE: (projectId: string, chapterId: string) => apiEndpoint(`/projects/${projectId}/chapters/${chapterId}`),
    REORDER: (projectId: string) => apiEndpoint(`/projects/${projectId}/chapters/reorder`),
    GENERATE: (projectId: string) => apiEndpoint(`/projects/${projectId}/chapters/generate`),
    ANALYZE: (projectId: string, chapterId: string) => apiEndpoint(`/projects/${projectId}/chapters/${chapterId}/analyze`),
  },

  // Characters
  CHARACTERS: {
    LIST: (projectId: string) => apiEndpoint(`/projects/${projectId}/characters`),
    CREATE: (projectId: string) => apiEndpoint(`/projects/${projectId}/characters`),
    GET: (projectId: string, characterId: string) => apiEndpoint(`/projects/${projectId}/characters/${characterId}`),
    UPDATE: (projectId: string, characterId: string) => apiEndpoint(`/projects/${projectId}/characters/${characterId}`),
    DELETE: (projectId: string, characterId: string) => apiEndpoint(`/projects/${projectId}/characters/${characterId}`),
    ANALYZE_ARC: (projectId: string, characterId: string) => apiEndpoint(`/projects/${projectId}/characters/${characterId}/arc`),
    GENERATE_PROFILE: (projectId: string) => apiEndpoint(`/projects/${projectId}/characters/generate`),
  },

  // Story Bible
  STORY_BIBLE: {
    GET: (projectId: string) => apiEndpoint(`/projects/${projectId}/story-bible`),
    UPDATE: (projectId: string) => apiEndpoint(`/projects/${projectId}/story-bible`),
    SECTIONS: {
      GET: (projectId: string, section: string) => apiEndpoint(`/projects/${projectId}/story-bible/${section}`),
      UPDATE: (projectId: string, section: string) => apiEndpoint(`/projects/${projectId}/story-bible/${section}`),
    },
    GENERATE: (projectId: string) => apiEndpoint(`/projects/${projectId}/story-bible/generate`),
  },

  // AI & Analysis
  AI: {
    GENERATE: apiEndpoint('/ai/generate'),
    COMPLETE: apiEndpoint('/ai/complete'),
    CHAT: apiEndpoint('/ai/chat'),
    TYPED_STREAM: apiEndpoint('/ai/typed-stream'),
    ANALYZE: apiEndpoint('/ai/analyze'),
    SUGGEST: apiEndpoint('/ai/suggest'),
    VOICE_ANALYSIS: apiEndpoint('/ai/voice-analysis'),
    CONTENT_IMPROVE: apiEndpoint('/ai/content/improve'),
  },

  // Voice Profiles
  VOICE: {
    PROFILES: {
      LIST: apiEndpoint('/voice/profiles'),
      CREATE: apiEndpoint('/voice/profiles'),
      GET: (id: string) => apiEndpoint(`/voice/profiles/${id}`),
      UPDATE: (id: string) => apiEndpoint(`/voice/profiles/${id}`),
      DELETE: (id: string) => apiEndpoint(`/voice/profiles/${id}`),
      TRAIN: (id: string) => apiEndpoint(`/voice/profiles/${id}/train`),
      ANALYZE: (id: string) => apiEndpoint(`/voice/profiles/${id}/analyze`),
    },
    TEMPLATES: apiEndpoint('/voice/templates'),
  },

  // Series Management
  SERIES: {
    LIST: apiEndpoint('/series'),
    CREATE: apiEndpoint('/series'),
    GET: (id: string) => apiEndpoint(`/series/${id}`),
    UPDATE: (id: string) => apiEndpoint(`/series/${id}`),
    DELETE: (id: string) => apiEndpoint(`/series/${id}`),
    BOOKS: (id: string) => apiEndpoint(`/series/${id}/books`),
    ADD_BOOK: (id: string) => apiEndpoint(`/series/${id}/books`),
    REMOVE_BOOK: (id: string, bookId: string) => apiEndpoint(`/series/${id}/books/${bookId}`),
    ANALYTICS: (id: string) => apiEndpoint(`/series/${id}/analytics`),
    CONTINUITY_ISSUES: (id: string) => apiEndpoint(`/series/${id}/continuity-issues`),
    UNIVERSE_RULES: (id: string) => apiEndpoint(`/series/${id}/universe-rules`),
    CHARACTER_ARCS: (id: string) => apiEndpoint(`/series/${id}/character-arcs`),
  },

  // Analytics
  ANALYTICS: {
    OVERVIEW: apiEndpoint('/analytics/overview'),
    WRITING: apiEndpoint('/analytics/writing'),
    AI_USAGE: apiEndpoint('/analytics/ai-usage'),
    BEHAVIORAL: apiEndpoint('/analytics/behavioral'),
    GOALS: apiEndpoint('/analytics/goals'),
    EXPORT: apiEndpoint('/analytics/export'),
  },

  // Analysis
  ANALYSIS: {
    CONTENT: apiEndpoint('/analysis/content'),
    READABILITY: apiEndpoint('/analysis/readability'),
    PACING: apiEndpoint('/analysis/pacing'),
    SENTIMENT: apiEndpoint('/analysis/sentiment'),
    ARC_SUGGESTIONS: apiEndpoint('/analysis/arc-suggestions'),
  },

  // Search
  SEARCH: {
    GLOBAL: apiEndpoint('/search'),
    PROJECTS: apiEndpoint('/search/projects'),
    CONTENT: apiEndpoint('/search/content'),
    SEMANTIC: apiEndpoint('/search/semantic'),
    SUGGESTIONS: apiEndpoint('/search/suggestions'),
  },

  // Export/Import
  EXPORT: {
    PROJECT: (id: string) => apiEndpoint(`/export/project/${id}`),
    CHAPTER: (projectId: string, chapterId: string) => apiEndpoint(`/export/project/${projectId}/chapter/${chapterId}`),
    ANALYTICS: apiEndpoint('/export/analytics'),
  },
  IMPORT: {
    PROJECT: apiEndpoint('/import/project'),
    DOCUMENT: apiEndpoint('/import/document'),
    PARSE: apiEndpoint('/import/parse'),
  },

  // User & Profile
  USER: {
    PROFILE: apiEndpoint('/user/profile'),
    UPDATE_PROFILE: apiEndpoint('/user/profile'),
    PREFERENCES: apiEndpoint('/user/preferences'),
    UPDATE_PREFERENCES: apiEndpoint('/user/preferences'),
    SUBSCRIPTION: apiEndpoint('/user/subscription'),
    DELETE_ACCOUNT: apiEndpoint('/user/delete'),
  },

  // Payment & Subscription
  PAYMENT: {
    CREATE_CHECKOUT: apiEndpoint('/payment/create-checkout-session'),
    CREATE_PORTAL: apiEndpoint('/payment/create-portal-session'),
    CHARGE: apiEndpoint('/payment/charge'),
    SUBSCRIPTION: {
      GET: apiEndpoint('/payment/subscription'),
      UPDATE: apiEndpoint('/payment/subscription'),
      CANCEL: apiEndpoint('/payment/subscription/cancel'),
    },
  },

  // Billing
  BILLING: {
    SUBSCRIPTIONS: apiEndpoint('/billing/subscriptions'),
    HISTORY: apiEndpoint('/billing/history'),
    PORTAL: apiEndpoint('/billing/subscriptions/portal'),
    CHECKOUT: apiEndpoint('/billing/subscriptions/checkout'),
  },

  // Webhooks
  WEBHOOKS: {
    STRIPE: apiEndpoint('/webhooks/stripe'),
    GITHUB: apiEndpoint('/webhooks/github'),
  },

  // Admin
  ADMIN: {
    USERS: {
      LIST: apiEndpoint('/admin/users'),
      GET: (id: string) => apiEndpoint(`/admin/users/${id}`),
      UPDATE: (id: string) => apiEndpoint(`/admin/users/${id}`),
      DELETE: (id: string) => apiEndpoint(`/admin/users/${id}`),
      IMPERSONATE: (id: string) => apiEndpoint(`/admin/users/${id}/impersonate`),
    },
    ANALYTICS: apiEndpoint('/admin/analytics'),
    SYSTEM: apiEndpoint('/admin/system'),
  },

  // References
  REFERENCES: {
    LIST: apiEndpoint('/references'),
    CREATE: apiEndpoint('/references'),
    GET: (id: string) => apiEndpoint(`/references/${id}`),
    UPDATE: (id: string) => apiEndpoint(`/references/${id}`),
    DELETE: (id: string) => apiEndpoint(`/references/${id}`),
    SUMMARIZE: (id: string) => apiEndpoint(`/references/${id}/summarize`),
  },

  // Templates
  TEMPLATES: {
    LIST: apiEndpoint('/templates'),
    GET: (id: string) => apiEndpoint(`/templates/${id}`),
    CREATE_PROJECT: apiEndpoint('/templates/create-project'),
  },

  // Error Reporting
  ERRORS: {
    REPORT: apiEndpoint('/errors/report'),
    BATCH_REPORT: apiEndpoint('/errors/batch-report'),
  },

  // Health Check
  HEALTH: apiEndpoint('/health'),
} as const

// Helper function to check if an endpoint requires authentication
export const isPublicEndpoint = (path: string): boolean => {
  const publicEndpoints = [
    API_ENDPOINTS.AUTH.LOGIN,
    API_ENDPOINTS.AUTH.REGISTER,
    API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
    API_ENDPOINTS.AUTH.RESET_PASSWORD,
    API_ENDPOINTS.AUTH.VERIFY_EMAIL,
    API_ENDPOINTS.WEBHOOKS.STRIPE,
    API_ENDPOINTS.HEALTH,
  ]
  
  return publicEndpoints.includes(path)
}

// Helper function to get endpoint with query params
export const withQueryParams = (endpoint: string, params: Record<string, string | number | boolean>): string => {
  const queryString = Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
    .join('&')
  
  return queryString ? `${endpoint}?${queryString}` : endpoint
}