'use client'

import { useEffect, useState } from 'react'
import { Users, BookOpen, PenTool, TrendingUp } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SocialProofStat {
  icon: React.ComponentType<{ className?: string }>
  value: string
  label: string
  increment?: number
}

const stats: SocialProofStat[] = [
  {
    icon: Users,
    value: '10,247',
    label: 'Active Authors',
    increment: 3
  },
  {
    icon: BookOpen,
    value: '2,834',
    label: 'Stories Created',
    increment: 2
  },
  {
    icon: PenTool,
    value: '1.2M+',
    label: 'Words Written',
    increment: 1000
  },
  {
    icon: TrendingUp,
    value: '98%',
    label: 'Satisfaction Rate'
  }
]

interface SocialProofBarProps {
  className?: string
  variant?: 'default' | 'compact' | 'detailed'
}

export function SocialProofBar({ className, variant = 'default' }: SocialProofBarProps) {
  const [animatedStats, setAnimatedStats] = useState(stats)

  useEffect(() => {
    if (variant !== 'default') return

    const interval = setInterval(() => {
      setAnimatedStats(current =>
        current.map(stat => {
          if (!stat.increment) return stat
          
          const currentValue = parseInt(stat.value.replace(/[^0-9]/g, ''))
          const newValue = currentValue + stat.increment
          
          if (stat.value.includes('M+')) {
            return {
              ...stat,
              value: `${(newValue / 1000000).toFixed(1)}M+`
            }
          }
          
          return {
            ...stat,
            value: newValue.toLocaleString()
          }
        })
      )
    }, 4000)

    return () => clearInterval(interval)
  }, [variant])

  if (variant === 'compact') {
    return (
      <div className={cn(
        'inline-flex items-center gap-6 text-sm text-muted-foreground',
        className
      )}>
        <span className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          <span className="font-medium">10K+ Authors</span>
        </span>
        <span className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          <span className="font-medium">98% Love It</span>
        </span>
      </div>
    )
  }

  return (
    <div className={cn(
      'w-full bg-muted/30 border-y border-border',
      className
    )}>
      <div className="container-wide">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 py-6">
          {animatedStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div
                key={index}
                className="flex flex-col items-center text-center space-y-2 group"
              >
                <div className="flex items-center gap-2">
                  <Icon className="w-5 h-5 text-primary transition-transform group-hover:scale-110" />
                  <span className="text-2xl font-bold text-foreground font-mono">
                    {stat.value}
                  </span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {stat.label}
                </span>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export function FloatingSocialProof() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 2000)
    return () => clearTimeout(timer)
  }, [])

  if (!isVisible) return null

  return (
    <div className="fixed bottom-4 left-4 z-50 animate-in slide-in-from-left fade-in duration-500">
      <div className="bg-card border border-border rounded-lg p-4 shadow-lg max-w-xs">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
            <Users className="w-5 h-5 text-primary" />
          </div>
          <div>
            <p className="text-sm font-medium">Sarah just started writing!</p>
            <p className="text-xs text-muted-foreground">2 minutes ago in California</p>
          </div>
        </div>
      </div>
    </div>
  )
}