import { createServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'

// Task types
export const TaskTypes = {
  // Content processing
  GENERATE_EMBEDDINGS: 'generate_embeddings',
  EXTRACT_TEXT: 'extract_text',
  ANALYZE_CONTENT: 'analyze_content',
  
  // Export tasks
  EXPORT_DOCX: 'export_docx',
  EXPORT_PDF: 'export_pdf',
  EXPORT_EPUB: 'export_epub',
  
  // AI tasks
  GENERATE_CHAPTER: 'generate_chapter',
  ANALYZE_CHARACTER_ARC: 'analyze_character_arc',
  CHECK_CONSISTENCY: 'check_consistency',
  
  // Maintenance tasks
  COMPRESS_MEMORY: 'compress_memory',
  CLEANUP_OLD_DATA: 'cleanup_old_data',
  GENERATE_ANALYTICS: 'generate_analytics',
  
  // Collaboration tasks
  NOTIFY_COLLABORATORS: 'notify_collaborators',
  SYNC_CHANGES: 'sync_changes',
  
  // Backup tasks
  BACKUP_PROJECT: 'backup_project',
  RESTORE_PROJECT: 'restore_project',
  
  // User data tasks
  EXPORT_USER_DATA: 'export_user_data',
  DELETE_USER_DATA: 'delete_user_data'
} as const

export type TaskType = typeof TaskTypes[keyof typeof TaskTypes]

// Task priority levels
export enum TaskPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

// Task status
export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Task data schemas
const taskDataSchemas = {
  generate_embeddings: z.object({
    contentId: z.string(),
    contentType: z.enum(['chapter', 'character', 'location', 'story_bible']),
    content: z.string()
  }),
  extract_text: z.object({
    fileId: z.string(),
    fileType: z.string(),
    filePath: z.string()
  }),
  export_docx: z.object({
    projectId: z.string(),
    userId: z.string(),
    includeComments: z.boolean().optional(),
    includeMetadata: z.boolean().optional()
  }),
  generate_chapter: z.object({
    projectId: z.string(),
    chapterId: z.string(),
    chapterNumber: z.number(),
    context: z.any()
  }),
  backup_project: z.object({
    projectId: z.string(),
    userId: z.string(),
    includeVersions: z.boolean().optional()
  }),
  export_user_data: z.object({
    userId: z.string(),
    email: z.string(),
    requestedAt: z.string()
  }),
  delete_user_data: z.object({
    userId: z.string(),
    email: z.string(),
    requestedAt: z.string()
  })
}

export type TaskData<T extends TaskType> = T extends keyof typeof taskDataSchemas 
  ? z.infer<typeof taskDataSchemas[T]>
  : Record<string, any>

interface ProcessingTask {
  id: string
  type: TaskType
  data: any
  status: TaskStatus
  priority: TaskPriority
  user_id: string
  project_id?: string
  attempts: number
  max_attempts: number
  error?: string
  result?: any
  started_at?: Date
  completed_at?: Date
  created_at: Date
  updated_at: Date
}

// Task handlers registry
type TaskHandler<T extends TaskType> = (
  task: ProcessingTask,
  data: TaskData<T>
) => Promise<any>

export class TaskQueueService {
  private static instance: TaskQueueService
  private handlers = new Map<TaskType, TaskHandler<any>>()
  private isProcessing = false

  private constructor() {
    this.registerDefaultHandlers()
  }

  static getInstance(): TaskQueueService {
    if (!TaskQueueService.instance) {
      TaskQueueService.instance = new TaskQueueService()
    }
    return TaskQueueService.instance
  }

  // Register a handler for a specific task type
  registerHandler<T extends TaskType>(
    type: T,
    handler: TaskHandler<T>
  ): void {
    this.handlers.set(type, handler)
    logger.info(`Registered handler for task type: ${type}`)
  }

  // Queue a new task
  async queueTask<T extends TaskType>(
    type: T,
    data: TaskData<T>,
    options?: {
      userId: string
      projectId?: string
      priority?: TaskPriority
      maxAttempts?: number
      scheduledFor?: Date
    }
  ): Promise<string> {
    try {
      // Validate data if schema exists
      const schema = taskDataSchemas[type as keyof typeof taskDataSchemas]
      if (schema) {
        schema.parse(data)
      }

      const supabase = await createServerClient()
      
      const task = {
        type,
        data,
        status: TaskStatus.PENDING,
        priority: options?.priority || TaskPriority.NORMAL,
        user_id: options?.userId || 'system',
        project_id: options?.projectId,
        max_attempts: options?.maxAttempts || 3,
        attempts: 0,
        scheduled_for: options?.scheduledFor || new Date()
      }

      const { data: newTask, error } = await supabase
        .from('processing_tasks')
        .insert(task)
        .select()
        .single()

      if (error) {
        logger.error('Error queueing task:', error)
        throw error
      }

      logger.info(`Task queued: ${type} (${newTask.id})`)
      return newTask.id
    } catch (error) {
      logger.error('Error queueing task:', error)
      throw error
    }
  }

  // Process pending tasks
  async processTasks(): Promise<void> {
    if (this.isProcessing) {
      return
    }

    this.isProcessing = true

    try {
      const supabase = await createServerClient()
      
      // Get pending tasks ordered by priority and creation time
      const { data: tasks, error } = await supabase
        .from('processing_tasks')
        .select('*')
        .eq('status', TaskStatus.PENDING)
        .lte('scheduled_for', new Date().toISOString())
        .lt('attempts', supabase.rpc('max_attempts'))
        .order('priority', { ascending: false })
        .order('created_at', { ascending: true })
        .limit(5)

      if (error) {
        logger.error('Error fetching tasks:', error)
        return
      }

      if (!tasks || tasks.length === 0) {
        return
      }

      // Process tasks in parallel
      await Promise.all(
        tasks.map(task => this.processTask(task as ProcessingTask))
      )
    } catch (error) {
      logger.error('Error processing tasks:', error)
    } finally {
      this.isProcessing = false
    }
  }

  // Process a single task
  private async processTask(task: ProcessingTask): Promise<void> {
    const supabase = await createServerClient()
    
    try {
      // Update status to processing
      await supabase
        .from('processing_tasks')
        .update({
          status: TaskStatus.PROCESSING,
          started_at: new Date().toISOString(),
          attempts: task.attempts + 1
        })
        .eq('id', task.id)

      // Get handler for task type
      const handler = this.handlers.get(task.type)
      if (!handler) {
        throw new Error(`No handler registered for task type: ${task.type}`)
      }

      // Execute handler
      const result = await handler(task, task.data)

      // Update task as completed
      await supabase
        .from('processing_tasks')
        .update({
          status: TaskStatus.COMPLETED,
          completed_at: new Date().toISOString(),
          result
        })
        .eq('id', task.id)

      logger.info(`Task completed: ${task.type} (${task.id})`)
    } catch (error) {
      logger.error(`Error processing task ${task.id}:`, error)
      
      const isLastAttempt = task.attempts >= task.max_attempts
      
      // Update task with error
      await supabase
        .from('processing_tasks')
        .update({
          status: isLastAttempt ? TaskStatus.FAILED : TaskStatus.PENDING,
          error: error instanceof Error ? error.message : 'Unknown error',
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id)
    }
  }

  // Get task status
  async getTaskStatus(taskId: string): Promise<ProcessingTask | null> {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('processing_tasks')
      .select('*')
      .eq('id', taskId)
      .single()

    if (error) {
      logger.error('Error fetching task status:', error)
      return null
    }

    return data as ProcessingTask
  }

  // Cancel a task
  async cancelTask(taskId: string): Promise<boolean> {
    const supabase = await createServerClient()
    
    const { error } = await supabase
      .from('processing_tasks')
      .update({
        status: TaskStatus.CANCELLED,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)
      .eq('status', TaskStatus.PENDING)

    if (error) {
      logger.error('Error cancelling task:', error)
      return false
    }

    return true
  }

  // Get tasks for a user or project
  async getTasks(options: {
    userId?: string
    projectId?: string
    status?: TaskStatus
    limit?: number
  }): Promise<ProcessingTask[]> {
    const supabase = await createServerClient()
    
    let query = supabase
      .from('processing_tasks')
      .select('*')

    if (options.userId) {
      query = query.eq('user_id', options.userId)
    }
    if (options.projectId) {
      query = query.eq('project_id', options.projectId)
    }
    if (options.status) {
      query = query.eq('status', options.status)
    }

    query = query
      .order('created_at', { ascending: false })
      .limit(options.limit || 50)

    const { data, error } = await query

    if (error) {
      logger.error('Error fetching tasks:', error)
      return []
    }

    return data as ProcessingTask[]
  }

  // Register default task handlers
  private registerDefaultHandlers(): void {
    // Generate embeddings handler
    this.registerHandler(TaskTypes.GENERATE_EMBEDDINGS, async (task, data) => {
      // This would integrate with the embedding service
      logger.info(`Generating embeddings for ${data.contentType}: ${data.contentId}`)
      // Implementation would go here
      return { success: true, message: 'Embeddings generated' }
    })

    // Export DOCX handler
    this.registerHandler(TaskTypes.EXPORT_DOCX, async (task, data) => {
      // This would integrate with the export service
      logger.info(`Exporting project ${data.projectId} to DOCX`)
      // Implementation would go here
      return { success: true, fileUrl: 'https://example.com/export.docx' }
    })

    // Backup project handler
    this.registerHandler(TaskTypes.BACKUP_PROJECT, async (task, data) => {
      // This would integrate with the backup service
      logger.info(`Backing up project ${data.projectId}`)
      // Implementation would go here
      return { success: true, backupId: 'backup_123' }
    })

    // Export user data handler
    this.registerHandler(TaskTypes.EXPORT_USER_DATA, async (task, data) => {
      logger.info(`Exporting data for user ${data.userId}`)
      
      const supabase = await createServerClient()
      
      // Collect all user data
      const userData: any = {
        exportDate: new Date().toISOString(),
        userId: data.userId,
        email: data.email
      }
      
      // Get user profile
      const { data: profile } = await supabase
        .from('users')
        .select('*')
        .eq('id', data.userId)
        .single()
      userData.profile = profile
      
      // Get projects
      const { data: projects } = await supabase
        .from('projects')
        .select('*')
        .eq('user_id', data.userId)
      userData.projects = projects || []
      
      // Get chapters
      const { data: chapters } = await supabase
        .from('chapters')
        .select('*')
        .eq('user_id', data.userId)
      userData.chapters = chapters || []
      
      // Get characters
      const { data: characters } = await supabase
        .from('characters')
        .select('*')
        .eq('user_id', data.userId)
      userData.characters = characters || []
      
      // Get preferences and settings
      const { data: preferences } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', data.userId)
        .single()
      userData.preferences = preferences
      
      // Create export file
      const exportJson = JSON.stringify(userData, null, 2)
      const fileName = `bookscribe-export-${data.userId}-${Date.now()}.json`
      
      // Upload to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('exports')
        .upload(fileName, exportJson, {
          contentType: 'application/json'
        })
      
      if (uploadError) throw uploadError
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('exports')
        .getPublicUrl(fileName)
      
      // Note: Email notification removed per user request
      // If email notification is needed later:
      // const { mailerooEmailService } = await import('./maileroo-email-service')
      // await mailerooEmailService.sendEmail(...)
      
      return { success: true, exportUrl: publicUrl }
    })

    // Delete user data handler
    this.registerHandler(TaskTypes.DELETE_USER_DATA, async (task, data) => {
      logger.info(`Deleting data for user ${data.userId}`)
      
      const supabase = await createServerClient()
      
      // Delete in order of dependencies
      // 1. Delete chapters
      await supabase.from('chapters').delete().eq('user_id', data.userId)
      
      // 2. Delete characters
      await supabase.from('characters').delete().eq('user_id', data.userId)
      
      // 3. Delete projects
      await supabase.from('projects').delete().eq('user_id', data.userId)
      
      // 4. Delete preferences
      await supabase.from('user_preferences').delete().eq('user_id', data.userId)
      
      // 5. Delete privacy settings
      await supabase.from('user_privacy_settings').delete().eq('user_id', data.userId)
      
      // 6. Delete consent history
      await supabase.from('user_consent_history').delete().eq('user_id', data.userId)
      
      // 7. Delete user storage files
      const { data: files } = await supabase.storage
        .from('user-uploads')
        .list(data.userId)
      
      if (files && files.length > 0) {
        const filePaths = files.map(f => `${data.userId}/${f.name}`)
        await supabase.storage.from('user-uploads').remove(filePaths)
      }
      
      // 8. Finally, delete user account
      await supabase.auth.admin.deleteUser(data.userId)
      
      // Send confirmation email
      const { emailService } = await import('./email-service')
      await emailService.sendEmail(
        data.email,
        'account_deleted',
        {
          userName: 'User'
        }
      )
      
      return { success: true, message: 'User data deleted successfully' }
    })

    // Add more default handlers as needed
  }

  // Cleanup old completed tasks
  async cleanupOldTasks(daysToKeep: number = 7): Promise<number> {
    const supabase = await createServerClient()
    
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

    const { data, error } = await supabase
      .from('processing_tasks')
      .delete()
      .in('status', [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED])
      .lt('updated_at', cutoffDate.toISOString())
      .select()

    if (error) {
      logger.error('Error cleaning up old tasks:', error)
      return 0
    }

    const count = data?.length || 0
    logger.info(`Cleaned up ${count} old tasks`)
    return count
  }
}

// Export singleton instance
export const taskQueueService = TaskQueueService.getInstance()