import { createClient } from '@/lib/supabase'
import { redirect, notFound } from 'next/navigation'
import { AgentDashboard } from '@/components/agents/agent-dashboard'

export default async function ProjectAgentsPage({ 
  params 
}: { 
  params: Promise<{ id: string }> 
}) {
  const { id } = await params
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  // Verify project ownership
  const { data: project, error } = await supabase
    .from('projects')
    .select('id, title, user_id')
    .eq('id', id)
    .eq('user_id', user.id)
    .single()
  
  if (error || !project) {
    notFound()
  }
  
  return (
    <div className="container-wide py-6 sm:py-8 lg:py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">AI Agent Dashboard</h1>
        <p className="text-muted-foreground">
          Monitor and control AI agents working on "{project.title}"
        </p>
      </div>
      
      <AgentDashboard projectId={id} />
    </div>
  )
}