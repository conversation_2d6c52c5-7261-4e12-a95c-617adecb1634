import { describe, it, expect, beforeEach } from '@jest/globals';
import { performance } from 'perf_hooks';
import { MemoryOptimizer } from '@/lib/services/memory-optimizer';
import { createClient } from '@/lib/supabase/server';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/services/logger');

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  contextCompression: 1000, // 1 second for large contexts
  contextMerge: 100, // 100ms for merging
  tokenCalculation: 10, // 10ms for token counting
  memoryStatsRetrieval: 500, // 500ms for full stats
  archiveOperation: 2000, // 2 seconds for archiving
};

describe('Memory Optimization Performance', () => {
  let memoryOptimizer: MemoryOptimizer;
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };

    (createClient as jest.Mock).mockReturnValue(mockSupabase);
    memoryOptimizer = new MemoryOptimizer();
  });

  describe('Context Compression Performance', () => {
    it('should compress large contexts efficiently', async () => {
      const contextSizes = [
        { chapters: 10, characters: 5 },
        { chapters: 50, characters: 20 },
        { chapters: 100, characters: 50 },
      ];

      for (const size of contextSizes) {
        const context = {
          chapters: Array(size.chapters).fill(null).map((_, i) => ({
            id: `ch-${i}`,
            content: 'Lorem ipsum dolor sit amet, '.repeat(1000), // ~30KB per chapter
            created_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
          })),
          characters: Array(size.characters).fill(null).map((_, i) => ({
            id: `char-${i}`,
            name: `Character ${i}`,
            backstory: 'Detailed backstory '.repeat(500), // ~15KB per character
            personality: 'Complex personality traits '.repeat(200),
          })),
        };

        const start = performance.now();
        const compressed = await memoryOptimizer.compressContext(context);
        const end = performance.now();

        const duration = end - start;
        const originalSize = JSON.stringify(context).length;
        const compressedSize = JSON.stringify(compressed).length;
        const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;

        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.contextCompression);
        expect(compressedSize).toBeLessThan(originalSize);

        console.log(
          `Compressing ${size.chapters} chapters, ${size.characters} characters: ` +
          `${duration.toFixed(2)}ms, ${compressionRatio.toFixed(1)}% reduction`
        );
      }
    });

    it('should handle edge cases efficiently', async () => {
      const edgeCases = [
        { name: 'Empty context', context: { chapters: [], characters: [] } },
        { name: 'Single large chapter', context: { 
          chapters: [{ id: '1', content: 'x'.repeat(100000) }] 
        }},
        { name: 'Many small items', context: { 
          chapters: Array(1000).fill({ id: '1', content: 'short' }) 
        }},
      ];

      for (const testCase of edgeCases) {
        const start = performance.now();
        await memoryOptimizer.compressContext(testCase.context);
        const end = performance.now();

        const duration = end - start;
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.contextCompression);

        console.log(`${testCase.name}: ${duration.toFixed(2)}ms`);
      }
    });
  });

  describe('Context Merging Performance', () => {
    it('should merge multiple contexts quickly', async () => {
      const contextCounts = [2, 5, 10, 20];

      for (const count of contextCounts) {
        const contexts = Array(count).fill(null).map((_, i) => ({
          chapters: Array(10).fill(null).map((_, j) => ({
            id: `context-${i}-ch-${j}`,
            content: `Chapter content ${i}-${j}`,
          })),
          characters: Array(5).fill(null).map((_, j) => ({
            id: `context-${i}-char-${j}`,
            name: `Character ${i}-${j}`,
          })),
        }));

        const start = performance.now();
        const merged = memoryOptimizer.mergeContexts(contexts);
        const end = performance.now();

        const duration = end - start;
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.contextMerge * Math.log(count));

        console.log(
          `Merging ${count} contexts: ${duration.toFixed(2)}ms, ` +
          `${merged.chapters.length} chapters, ${merged.characters.length} characters`
        );
      }
    });

    it('should handle duplicate detection efficiently', async () => {
      // Create contexts with many duplicates
      const baseContext = {
        chapters: Array(100).fill(null).map((_, i) => ({
          id: `ch-${i}`,
          content: `Chapter ${i}`,
        })),
      };

      const contexts = Array(10).fill(null).map(() => ({
        ...baseContext,
        chapters: [...baseContext.chapters, ...Array(10).fill(null).map((_, i) => ({
          id: `new-ch-${Math.random()}`,
          content: `New chapter`,
        }))],
      }));

      const start = performance.now();
      const merged = memoryOptimizer.mergeContexts(contexts);
      const end = performance.now();

      const duration = end - start;
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.contextMerge * 5);
      expect(merged.chapters.length).toBeLessThan(contexts.length * 110); // Should dedupe

      console.log(`Deduplication performance: ${duration.toFixed(2)}ms`);
    });
  });

  describe('Token Calculation Performance', () => {
    it('should calculate tokens quickly for various text sizes', async () => {
      const textSizes = [
        { name: 'Short', length: 100 },
        { name: 'Medium', length: 1000 },
        { name: 'Long', length: 10000 },
        { name: 'Very Long', length: 100000 },
      ];

      for (const size of textSizes) {
        const text = 'a'.repeat(size.length);
        
        const start = performance.now();
        const tokens = memoryOptimizer.calculateTokenUsage(text);
        const end = performance.now();

        const duration = end - start;
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.tokenCalculation);

        console.log(
          `Token calculation for ${size.name} text (${size.length} chars): ` +
          `${duration.toFixed(2)}ms, ${tokens} tokens`
        );
      }
    });

    it('should batch token calculations efficiently', async () => {
      const texts = Array(1000).fill(null).map((_, i) => 
        `This is text number ${i} with some content.`
      );

      const start = performance.now();
      const totalTokens = texts.reduce((sum, text) => 
        sum + memoryOptimizer.calculateTokenUsage(text), 0
      );
      const end = performance.now();

      const duration = end - start;
      const perText = duration / texts.length;

      expect(perText).toBeLessThan(0.1); // Less than 0.1ms per text

      console.log(
        `Batch token calculation (${texts.length} texts): ` +
        `${duration.toFixed(2)}ms total, ${perText.toFixed(3)}ms per text`
      );
    });
  });

  describe('Memory Stats Retrieval Performance', () => {
    it('should retrieve stats for projects with varying content sizes', async () => {
      const projectSizes = [
        { chapters: 10, characters: 5, references: 5 },
        { chapters: 50, characters: 20, references: 10 },
        { chapters: 100, characters: 50, references: 20 },
      ];

      for (const size of projectSizes) {
        // Mock database responses
        mockSupabase.select.mockImplementation(() => {
          if (mockSupabase.from.mock.lastCall?.[0] === 'chapters') {
            return {
              data: Array(size.chapters).fill({ content: 'x'.repeat(5000) }),
              error: null,
            };
          }
          if (mockSupabase.from.mock.lastCall?.[0] === 'characters') {
            return {
              data: Array(size.characters).fill({ backstory: 'x'.repeat(2000) }),
              error: null,
            };
          }
          return { data: [], error: null };
        });

        const start = performance.now();
        const stats = await memoryOptimizer.getMemoryStats('project-123');
        const end = performance.now();

        const duration = end - start;
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.memoryStatsRetrieval);

        console.log(
          `Stats for project with ${size.chapters} chapters: ` +
          `${duration.toFixed(2)}ms, ${stats.totalTokens} tokens`
        );
      }
    });
  });

  describe('Archive Operation Performance', () => {
    it('should archive old content efficiently', async () => {
      const contentCounts = [10, 50, 100];

      for (const count of contentCounts) {
        const oldContent = Array(count).fill(null).map((_, i) => ({
          id: `old-${i}`,
          content: 'Old content that needs archiving'.repeat(100),
          created_at: '2020-01-01',
        }));

        mockSupabase.select.mockResolvedValue({ data: oldContent, error: null });
        mockSupabase.insert.mockResolvedValue({ error: null });
        mockSupabase.delete.mockResolvedValue({ error: null });

        const start = performance.now();
        await memoryOptimizer.archiveOldContent('project-123');
        const end = performance.now();

        const duration = end - start;
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.archiveOperation);

        console.log(`Archiving ${count} old items: ${duration.toFixed(2)}ms`);
      }
    });
  });

  describe('Agent-Specific Optimization Performance', () => {
    it('should optimize context for different agents quickly', async () => {
      const largeContext = {
        chapters: Array(100).fill(null).map((_, i) => ({
          id: `ch-${i}`,
          content: 'Chapter content',
          characters: ['char-1', 'char-2'],
        })),
        characters: Array(50).fill(null).map((_, i) => ({
          id: `char-${i}`,
          name: `Character ${i}`,
          voice_profile: { tone: 'formal' },
        })),
        worldBuilding: {
          locations: Array(100).fill(null).map((_, i) => ({ id: `loc-${i}` })),
        },
      };

      const agents = [
        'character-developer',
        'chapter-planner',
        'writing-agent',
        'story-architect',
      ];

      for (const agentType of agents) {
        const start = performance.now();
        const optimized = await memoryOptimizer.optimizeForAgent(largeContext, agentType);
        const end = performance.now();

        const duration = end - start;
        const reduction = (1 - JSON.stringify(optimized).length / JSON.stringify(largeContext).length) * 100;

        expect(duration).toBeLessThan(100); // Should be very fast

        console.log(
          `Optimizing for ${agentType}: ${duration.toFixed(2)}ms, ` +
          `${reduction.toFixed(1)}% reduction`
        );
      }
    });
  });

  describe('Cache Performance', () => {
    it('should demonstrate effective caching', async () => {
      const projectId = 'cache-test-project';

      // First call - no cache
      const start1 = performance.now();
      await memoryOptimizer.getOptimizedContext(projectId);
      const end1 = performance.now();
      const firstCallDuration = end1 - start1;

      // Second call - should use cache
      const start2 = performance.now();
      await memoryOptimizer.getOptimizedContext(projectId);
      const end2 = performance.now();
      const secondCallDuration = end2 - start2;

      expect(secondCallDuration).toBeLessThan(firstCallDuration * 0.1); // 90% faster

      console.log(
        `Cache performance: First call ${firstCallDuration.toFixed(2)}ms, ` +
        `Cached call ${secondCallDuration.toFixed(2)}ms`
      );
    });
  });
});