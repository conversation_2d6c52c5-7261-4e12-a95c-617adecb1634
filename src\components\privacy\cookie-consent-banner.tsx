'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Shield, X, Settings2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ConsentOptions {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

export function CookieConsentBanner() {
  const [showBanner, setShowBanner] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [consent, setConsent] = useState<ConsentOptions>({
    necessary: true, // Always true
    analytics: false,
    marketing: false,
    functional: true
  })

  useEffect(() => {
    // Check if user has already given consent
    const storedConsent = localStorage.getItem('cookie-consent')
    if (!storedConsent) {
      setShowBanner(true)
    }
  }, [])

  const saveConsent = async (options: ConsentOptions) => {
    // Save to localStorage
    localStorage.setItem('cookie-consent', JSON.stringify(options))
    localStorage.setItem('cookie-consent-date', new Date().toISOString())

    // Save to database if user is logged in
    try {
      await fetch('/api/user/cookie-consent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ consent: options })
      })
    } catch (error) {
      console.error('Error saving consent:', error)
    }

    // Apply consent settings
    applyConsentSettings(options)
    setShowBanner(false)
  }

  const applyConsentSettings = (options: ConsentOptions) => {
    // Apply analytics consent
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: options.analytics ? 'granted' : 'denied',
        ad_storage: options.marketing ? 'granted' : 'denied'
      })
    }

    // Apply other consent settings as needed
    window.dispatchEvent(new CustomEvent('consent-updated', { detail: options }))
  }

  const acceptAll = () => {
    const allConsent = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true
    }
    setConsent(allConsent)
    saveConsent(allConsent)
  }

  const acceptSelected = () => {
    saveConsent(consent)
  }

  const rejectAll = () => {
    const minimalConsent = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false
    }
    setConsent(minimalConsent)
    saveConsent(minimalConsent)
  }

  if (!showBanner) return null

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 animate-in slide-in-from-bottom duration-500">
      <Card className="max-w-6xl mx-auto shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Shield className="h-8 w-8 text-primary" />
              <div>
                <h3 className="text-lg font-semibold">Privacy & Cookie Settings</h3>
                <p className="text-sm text-muted-foreground">
                  We use cookies to enhance your experience and analyze our traffic
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowBanner(false)}
              className="ml-4"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {!showDetails ? (
            <div className="space-y-4">
              <p className="text-sm">
                We use cookies and similar technologies to help personalize content, 
                tailor and measure ads, and provide a better experience. By clicking accept, 
                you agree to this, as outlined in our Cookie Policy.
              </p>
              <div className="flex flex-wrap gap-2">
                <Button onClick={acceptAll} className="flex-1 sm:flex-none">
                  Accept All
                </Button>
                <Button 
                  variant="outline" 
                  onClick={rejectAll}
                  className="flex-1 sm:flex-none"
                >
                  Reject All
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowDetails(!showDetails)}
                  className="flex-1 sm:flex-none"
                >
                  <Settings2 className="h-4 w-4 mr-2" />
                  Customize
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="necessary" className="text-base">
                      Necessary Cookies
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Essential for the website to function properly
                    </p>
                  </div>
                  <Switch
                    id="necessary"
                    checked={consent.necessary}
                    disabled
                    className="ml-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="analytics" className="text-base">
                      Analytics Cookies
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Help us understand how visitors use our website
                    </p>
                  </div>
                  <Switch
                    id="analytics"
                    checked={consent.analytics}
                    onCheckedChange={(checked) => 
                      setConsent({ ...consent, analytics: checked })
                    }
                    className="ml-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="marketing" className="text-base">
                      Marketing Cookies
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Used to deliver personalized advertisements
                    </p>
                  </div>
                  <Switch
                    id="marketing"
                    checked={consent.marketing}
                    onCheckedChange={(checked) => 
                      setConsent({ ...consent, marketing: checked })
                    }
                    className="ml-4"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="functional" className="text-base">
                      Functional Cookies
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Enable enhanced functionality and personalization
                    </p>
                  </div>
                  <Switch
                    id="functional"
                    checked={consent.functional}
                    onCheckedChange={(checked) => 
                      setConsent({ ...consent, functional: checked })
                    }
                    className="ml-4"
                  />
                </div>
              </div>

              <div className="flex flex-wrap gap-2 pt-4">
                <Button onClick={acceptSelected} className="flex-1 sm:flex-none">
                  Save Preferences
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowDetails(false)}
                  className="flex-1 sm:flex-none"
                >
                  Back
                </Button>
              </div>
            </div>
          )}

          <div className="mt-4 text-xs text-muted-foreground">
            By using our site, you agree to our{' '}
            <a href="/privacy" className="underline hover:text-primary">
              Privacy Policy
            </a>{' '}
            and{' '}
            <a href="/terms" className="underline hover:text-primary">
              Terms of Service
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}