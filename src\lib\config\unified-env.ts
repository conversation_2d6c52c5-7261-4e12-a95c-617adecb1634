/**
 * Unified Environment Configuration
 * Single source of truth for all environment variables
 */

import { z } from 'zod'

// ===== ENVIRONMENT SCHEMAS =====

const envSchema = z.object({
  // Core Configuration
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),
  NEXT_PUBLIC_API_URL: z.string().url().default('http://localhost:3000/api'),
  
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1).optional(),
  
  // OpenAI Configuration
  OPENAI_API_KEY: z.string().min(1),
  
  // Stripe Configuration (Optional)
  STRIPE_SECRET_KEY: z.string().min(1).optional(),
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().min(1).optional(),
  STRIPE_WEBHOOK_SECRET: z.string().min(1).optional(),
  
  // Feature Flags
  NEXT_PUBLIC_DEMO_MODE: z.coerce.boolean().default(false),
  NEXT_PUBLIC_DEV_BYPASS_AUTH: z.coerce.boolean().default(false),
  
  // Optional AI Providers
  GEMINI_API_KEY: z.string().optional(),
  XAI_API_KEY: z.string().optional(),
  
  // Monitoring
  SENTRY_DSN: z.string().optional(),
  SENTRY_ENVIRONMENT: z.string().default('development'),
  
  // Email
  EMAIL_API_KEY: z.string().optional(),
  EMAIL_FROM: z.string().email().default('<EMAIL>'),
  EMAIL_FROM_NAME: z.string().default('BookScribe AI'),
  EMAIL_ENDPOINT: z.string().url().default('https://smtp.maileroo.com/v1/send'),
})

// ===== PARSED ENVIRONMENT =====

// Parse and validate environment variables
let env: z.infer<typeof envSchema>

try {
  const parsedEnv = envSchema.safeParse(process.env)
  
  if (!parsedEnv.success) {
    console.error('❌ Invalid environment variables:')
    console.error(parsedEnv.error.flatten().fieldErrors)
    
    // In development, provide fallback values
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️  Using fallback values for development')
      env = {
        NODE_ENV: 'development',
        NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
        NEXT_PUBLIC_API_URL: 'http://localhost:3000/api',
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
        NEXT_PUBLIC_DEMO_MODE: false,
        NEXT_PUBLIC_DEV_BYPASS_AUTH: false,
        SENTRY_ENVIRONMENT: 'development',
        EMAIL_FROM: '<EMAIL>',
        EMAIL_FROM_NAME: 'BookScribe AI',
        EMAIL_ENDPOINT: 'https://smtp.maileroo.com/v1/send',
      } as z.infer<typeof envSchema>
    } else {
      throw new Error('Invalid environment variables')
    }
  } else {
    env = parsedEnv.data
  }
} catch (error) {
  // Fallback for build time
  env = {} as z.infer<typeof envSchema>
}

// ===== UNIFIED CONFIGURATION =====

export { env }

// Derived configurations
export const config = {
  // Application
  app: {
    name: 'BookScribe AI',
    url: env.NEXT_PUBLIC_APP_URL,
    apiUrl: env.NEXT_PUBLIC_API_URL,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
    isTest: env.NODE_ENV === 'test',
    isDemoMode: env.NEXT_PUBLIC_DEMO_MODE,
    devBypassAuth: env.NEXT_PUBLIC_DEV_BYPASS_AUTH && env.NODE_ENV === 'development',
  },
  
  // Database
  supabase: {
    url: env.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,
  },
  
  // AI Configuration
  ai: {
    openai: {
      apiKey: env.OPENAI_API_KEY,
      models: {
        primary: 'gpt-4.1-2025-04-14',
        fast: 'gpt-4o-mini',
        embedding: 'text-embedding-3-small',
      },
      temperature: {
        creative: 0.9,
        balanced: 0.7,
        focused: 0.5,
        analytical: 0.3,
        deterministic: 0.1,
      },
      tokenLimits: {
        chapter: 10000,
        scene: 8000,
        character: 6000,
        dialogue: 4000,
        summary: 2000,
        default: 4000,
      },
    },
    gemini: env.GEMINI_API_KEY ? {
      apiKey: env.GEMINI_API_KEY,
      enabled: true,
    } : undefined,
    xai: env.XAI_API_KEY ? {
      apiKey: env.XAI_API_KEY,
      enabled: true,
    } : undefined,
  },
  
  // Payments
  stripe: env.STRIPE_SECRET_KEY ? {
    secretKey: env.STRIPE_SECRET_KEY,
    publishableKey: env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    webhookSecret: env.STRIPE_WEBHOOK_SECRET,
    enabled: true,
  } : undefined,
  
  // Email
  email: env.EMAIL_API_KEY ? {
    apiKey: env.EMAIL_API_KEY,
    from: env.EMAIL_FROM,
    fromName: env.EMAIL_FROM_NAME,
    endpoint: env.EMAIL_ENDPOINT,
    enabled: true,
  } : undefined,
  
  // Monitoring
  sentry: env.SENTRY_DSN ? {
    dsn: env.SENTRY_DSN,
    environment: env.SENTRY_ENVIRONMENT,
    enabled: true,
  } : undefined,
  
  // Rate Limiting (hardcoded for consistency)
  rateLimits: {
    api: {
      default: 100,
      authenticated: 1000,
      ai: {
        generation: 30,
        analysis: 10,
        chat: 50,
      },
      webhooks: 500,
    },
    windows: {
      default: 60 * 60 * 1000, // 1 hour
      ai: 60 * 60 * 1000, // 1 hour
      burst: 60 * 1000, // 1 minute
    },
    burst: {
      default: 20,
      ai: 5,
    },
  },
  
  // Performance
  performance: {
    timeouts: {
      api: 30000, // 30s
      ai: 120000, // 2m
      upload: 300000, // 5m
      websocket: 60000, // 1m
    },
    batch: {
      default: 10,
      ai: 3,
      db: 100,
    },
    concurrency: {
      aiAgents: 3,
      uploads: 5,
      exports: 2,
    },
  },
  
  // Security
  security: {
    session: {
      duration: 24 * 60 * 60 * 1000, // 24 hours
      refresh: 60 * 60 * 1000, // 1 hour
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    },
    file: {
      maxSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: ['.txt', '.doc', '.docx', '.md', '.json'],
    },
  },
} as const

// ===== CLIENT-SAFE CONFIG =====

// Configuration safe to expose to the client
export const clientConfig = {
  app: {
    name: config.app.name,
    url: config.app.url,
    isDemoMode: config.app.isDemoMode,
  },
  supabase: {
    url: config.supabase.url,
    anonKey: config.supabase.anonKey,
  },
  stripe: config.stripe ? {
    publishableKey: config.stripe.publishableKey,
  } : undefined,
  features: {
    payments: !!config.stripe,
    email: !!config.email,
    monitoring: !!config.sentry,
    alternativeAI: !!(config.ai.gemini || config.ai.xai),
  },
} as const

// ===== VALIDATION HELPERS =====

/**
 * Check if a required service is configured
 */
export function requireService(service: 'stripe' | 'email' | 'sentry') {
  const serviceConfig = config[service]
  if (!serviceConfig) {
    throw new Error(`${service} is not configured. Please set the required environment variables.`)
  }
  return serviceConfig
}

/**
 * Get AI model configuration with fallbacks
 */
export function getAIModel(type: 'primary' | 'fast' | 'embedding' = 'primary') {
  // Check alternative providers if OpenAI fails
  if (!config.ai.openai.apiKey && config.ai.gemini) {
    console.warn('OpenAI not configured, falling back to Gemini')
    return 'gemini-pro'
  }
  
  return config.ai.openai.models[type]
}

/**
 * Get rate limit configuration
 */
export function getRateLimit(type: keyof typeof config.rateLimits.api) {
  const limit = config.rateLimits.api[type]
  return typeof limit === 'number' ? limit : limit.generation
}

// ===== TYPE EXPORTS =====

export type Config = typeof config
export type ClientConfig = typeof clientConfig
export type AIConfig = typeof config.ai
export type RateLimitConfig = typeof config.rateLimits

// Default export
export default config