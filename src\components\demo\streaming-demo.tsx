'use client';

import { useState } from 'react';
import { 
  useChapterStreaming, 
  useCharacterStreaming, 
  useAnalysisStreaming 
} from '@/hooks/use-typed-streaming';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Sparkles, User, BarChart3, Loader2, X } from 'lucide-react';
import { cn } from '@/lib/utils';

export function StreamingDemo() {
  const [activeTab, setActiveTab] = useState('chapter');

  // Chapter streaming
  const {
    content: chapterContent,
    structured: chapterData,
    progress: chapterProgress,
    isStreaming: isStreamingChapter,
    quality: chapterQuality,
    streamChapter,
    cancel: cancelChapter,
    reset: resetChapter
  } = useChapterStreaming({
    onComplete: (content, data) => {
      // Chapter streaming complete
    },
    onQuality: (quality) => {
      // Quality assessment updated
    }
  });

  // Character streaming
  const {
    content: characterContent,
    structured: characterData,
    progress: characterProgress,
    isStreaming: isStreamingCharacter,
    streamCharacter,
    cancel: cancelCharacter,
    reset: resetCharacter
  } = useCharacterStreaming();

  // Analysis streaming
  const {
    content: analysisContent,
    structured: analysisData,
    progress: analysisProgress,
    isStreaming: isStreamingAnalysis,
    streamAnalysis,
    cancel: cancelAnalysis,
    reset: resetAnalysis
  } = useAnalysisStreaming();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Typed Streaming Demo</h2>
        <p className="text-muted-foreground">
          Experience real-time content generation with typed responses and progress tracking
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="chapter" className="flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            Chapter
          </TabsTrigger>
          <TabsTrigger value="character" className="flex items-center gap-2">
            <User className="w-4 h-4" />
            Character
          </TabsTrigger>
          <TabsTrigger value="analysis" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analysis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chapter" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Stream Chapter Generation</CardTitle>
              <CardDescription>
                Generate a complete chapter with scenes, dialogue, and plot progression
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button
                  onClick={() => streamChapter(
                    'Write an exciting opening chapter for a mystery novel set in Victorian London',
                    {
                      projectId: 'demo-project',
                      voiceProfile: {
                        tone: 'mysterious',
                        style: 'descriptive',
                        vocabulary: ['gaslight', 'cobblestone', 'fog'],
                        sentencePatterns: ['complex', 'varied'],
                        narrativePerspective: 'third-limited'
                      }
                    }
                  )}
                  disabled={isStreamingChapter}
                >
                  {isStreamingChapter ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Streaming...
                    </>
                  ) : (
                    'Generate Chapter'
                  )}
                </Button>
                
                {isStreamingChapter && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={cancelChapter}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
                
                {chapterContent && !isStreamingChapter && (
                  <Button
                    variant="outline"
                    onClick={resetChapter}
                  >
                    Reset
                  </Button>
                )}
              </div>

              {(isStreamingChapter || chapterContent) && (
                <>
                  <StreamingProgress
                    progress={chapterProgress}
                    quality={chapterQuality}
                  />

                  <div className="space-y-4">
                    <div className="max-h-96 overflow-y-auto p-4 bg-muted rounded-lg">
                      <pre className="whitespace-pre-wrap font-serif">
                        {chapterContent || 'Waiting for content...'}
                      </pre>
                    </div>

                    {chapterData && (
                      <Alert>
                        <AlertDescription className="space-y-2">
                          <div className="font-semibold">Chapter Data:</div>
                          <div className="text-sm space-y-1">
                            <div>Title: {chapterData.title}</div>
                            <div>Word Count: {chapterData.wordCount}</div>
                            <div>Scenes: {chapterData.scenes.length}</div>
                            <div>Characters: {chapterData.characters.join(', ')}</div>
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="character" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Stream Character Development</CardTitle>
              <CardDescription>
                Create detailed character profiles with personality, backstory, and voice
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button
                  onClick={() => streamCharacter('Sherlock Holmes')}
                  disabled={isStreamingCharacter}
                >
                  {isStreamingCharacter ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Developing...
                    </>
                  ) : (
                    'Develop Character'
                  )}
                </Button>
                
                {isStreamingCharacter && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={cancelCharacter}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
                
                {characterContent && !isStreamingCharacter && (
                  <Button
                    variant="outline"
                    onClick={resetCharacter}
                  >
                    Reset
                  </Button>
                )}
              </div>

              {(isStreamingCharacter || characterContent) && (
                <>
                  <StreamingProgress progress={characterProgress} />

                  <div className="space-y-4">
                    <div className="max-h-96 overflow-y-auto p-4 bg-muted rounded-lg">
                      <pre className="whitespace-pre-wrap">
                        {characterContent || 'Waiting for content...'}
                      </pre>
                    </div>

                    {characterData && (
                      <div className="grid grid-cols-2 gap-4">
                        <Alert>
                          <AlertDescription>
                            <div className="font-semibold mb-2">Profile</div>
                            <div className="text-sm space-y-1">
                              <div>Name: {characterData.profile.name}</div>
                              <div>Age: {characterData.profile.age}</div>
                              <div>Personality: {characterData.profile.personality.join(', ')}</div>
                            </div>
                          </AlertDescription>
                        </Alert>
                        
                        <Alert>
                          <AlertDescription>
                            <div className="font-semibold mb-2">Voice</div>
                            <div className="text-sm space-y-1">
                              <div>Tone: {characterData.profile.voice.tone}</div>
                              <div>Style: {characterData.profile.voice.style}</div>
                            </div>
                          </AlertDescription>
                        </Alert>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Stream Content Analysis</CardTitle>
              <CardDescription>
                Analyze content for voice consistency, plot coherence, and quality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button
                  onClick={() => streamAnalysis(
                    'The detective examined the crime scene carefully, noting every detail.',
                    'voice_consistency'
                  )}
                  disabled={isStreamingAnalysis}
                >
                  {isStreamingAnalysis ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    'Analyze Content'
                  )}
                </Button>
                
                {isStreamingAnalysis && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={cancelAnalysis}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
                
                {analysisContent && !isStreamingAnalysis && (
                  <Button
                    variant="outline"
                    onClick={resetAnalysis}
                  >
                    Reset
                  </Button>
                )}
              </div>

              {(isStreamingAnalysis || analysisContent) && (
                <>
                  <StreamingProgress progress={analysisProgress} />

                  <div className="space-y-4">
                    <div className="max-h-96 overflow-y-auto p-4 bg-muted rounded-lg">
                      <pre className="whitespace-pre-wrap">
                        {analysisContent || 'Waiting for analysis...'}
                      </pre>
                    </div>

                    {analysisData && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-semibold">Overall Score</span>
                          <Badge variant={analysisData.overallScore > 80 ? 'default' : 'secondary'}>
                            {analysisData.overallScore}%
                          </Badge>
                        </div>
                        
                        {analysisData.results.map((result, index) => (
                          <Alert key={index}>
                            <AlertDescription>
                              <div className="flex items-center justify-between">
                                <span className="font-medium">{result.category}</span>
                                <span className="text-sm">{result.score}%</span>
                              </div>
                              <div className="text-sm text-muted-foreground mt-1">
                                {result.details}
                              </div>
                            </AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Progress component
function StreamingProgress({ 
  progress, 
  quality 
}: { 
  progress: { 
    tokens: number;
    estimatedTokens: number;
    percentComplete: number;
    tokensPerSecond: number;
    estimatedTimeRemaining: number;
  };
  quality?: number;
}) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between text-sm">
        <span>Progress</span>
        <span>{progress.percentComplete}%</span>
      </div>
      <Progress value={progress.percentComplete} className="h-2" />
      
      <div className="grid grid-cols-3 gap-4 text-xs text-muted-foreground">
        <div>
          <span className="font-medium">Tokens:</span> {progress.tokens} / {progress.estimatedTokens}
        </div>
        <div>
          <span className="font-medium">Speed:</span> {progress.tokensPerSecond.toFixed(1)} t/s
        </div>
        <div>
          <span className="font-medium">ETA:</span> {
            progress.estimatedTimeRemaining === Infinity 
              ? '...' 
              : `${Math.ceil(progress.estimatedTimeRemaining)}s`
          }
        </div>
      </div>
      
      {quality && (
        <div className="flex items-center justify-between text-sm mt-2">
          <span>Quality Score</span>
          <Badge variant={quality > 80 ? 'default' : 'secondary'}>
            {quality}%
          </Badge>
        </div>
      )}
    </div>
  );
}