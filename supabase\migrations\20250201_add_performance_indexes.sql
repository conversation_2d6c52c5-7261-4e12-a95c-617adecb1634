-- Add performance indexes for BookScribe
-- This migration adds indexes to improve query performance across the application

-- User-related indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Projects indexes
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_projects_user_status ON projects(user_id, status);
CREATE INDEX IF NOT EXISTS idx_projects_series_id ON projects(series_id) WHERE series_id IS NOT NULL;

-- Chapters indexes
CREATE INDEX IF NOT EXISTS idx_chapters_project_id ON chapters(project_id);
CREATE INDEX IF NOT EXISTS idx_chapters_order ON chapters(project_id, order_index);
CREATE INDEX IF NOT EXISTS idx_chapters_updated_at ON chapters(updated_at);
CREATE INDEX IF NOT EXISTS idx_chapters_user_id ON chapters(user_id);

-- Characters indexes
CREATE INDEX IF NOT EXISTS idx_characters_project_id ON characters(project_id);
CREATE INDEX IF NOT EXISTS idx_characters_user_id ON characters(user_id);

-- Series indexes
CREATE INDEX IF NOT EXISTS idx_series_user_id ON series(user_id);
CREATE INDEX IF NOT EXISTS idx_series_created_at ON series(created_at);

-- Series books indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'series_books') THEN
    CREATE INDEX IF NOT EXISTS idx_series_books_series_id ON series_books(series_id);
    CREATE INDEX IF NOT EXISTS idx_series_books_order ON series_books(series_id, order_in_series);
  END IF;
END $$;

-- Project collaborators indexes
CREATE INDEX IF NOT EXISTS idx_collaborators_project_id ON project_collaborators(project_id);
CREATE INDEX IF NOT EXISTS idx_collaborators_user_id ON project_collaborators(user_id);
CREATE INDEX IF NOT EXISTS idx_collaborators_status ON project_collaborators(project_id, status);

-- Writing sessions indexes
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON writing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_date ON writing_sessions(user_id, started_at);
CREATE INDEX IF NOT EXISTS idx_sessions_project_id ON writing_sessions(project_id);

-- Word count history indexes
CREATE INDEX IF NOT EXISTS idx_word_count_project ON word_count_history(project_id, recorded_at);
CREATE INDEX IF NOT EXISTS idx_word_count_chapter ON word_count_history(chapter_id, recorded_at) WHERE chapter_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_word_count_series ON word_count_history(series_id, recorded_at) WHERE series_id IS NOT NULL;

-- Content embeddings indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'content_embeddings') THEN
    CREATE INDEX IF NOT EXISTS idx_embeddings_content_type ON content_embeddings(content_type, content_id);
    CREATE INDEX IF NOT EXISTS idx_embeddings_project ON content_embeddings(project_id);
  END IF;
END $$;

-- Achievements indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_achievements') THEN
    CREATE INDEX IF NOT EXISTS idx_achievements_user ON user_achievements(user_id, unlocked_at);
    CREATE UNIQUE INDEX IF NOT EXISTS idx_achievements_type ON user_achievements(achievement_id, user_id);
  END IF;
END $$;

-- Notifications indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON notifications(user_id, read) WHERE read = false;

-- Processing tasks indexes
CREATE INDEX IF NOT EXISTS idx_tasks_status ON processing_tasks(status, scheduled_for) WHERE status = 'pending';
CREATE INDEX IF NOT EXISTS idx_tasks_user ON processing_tasks(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_project ON processing_tasks(project_id) WHERE project_id IS NOT NULL;

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_preferences_user ON user_preferences(user_id);

-- Project settings indexes
CREATE INDEX IF NOT EXISTS idx_project_settings_project ON project_settings(project_id);

-- Email preferences indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'email_preferences') THEN
    CREATE INDEX IF NOT EXISTS idx_email_preferences_user ON email_preferences(user_id);
  END IF;
END $$;

-- Privacy settings indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_privacy_settings') THEN
    CREATE INDEX IF NOT EXISTS idx_privacy_settings_user ON user_privacy_settings(user_id);
  END IF;
END $$;

-- Profiles indexes
CREATE INDEX IF NOT EXISTS idx_profiles_visibility ON profiles(visibility);
CREATE INDEX IF NOT EXISTS idx_profiles_featured ON profiles(is_featured) WHERE is_featured = true;

-- References indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reference_materials') THEN
    CREATE INDEX IF NOT EXISTS idx_references_project ON reference_materials(project_id);
    CREATE INDEX IF NOT EXISTS idx_references_type ON reference_materials(type);
  END IF;
END $$;

-- Locations indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'locations') THEN
    CREATE INDEX IF NOT EXISTS idx_locations_project ON locations(project_id);
    CREATE INDEX IF NOT EXISTS idx_locations_parent ON locations(parent_id) WHERE parent_id IS NOT NULL;
  END IF;
END $$;

-- Story bible indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'story_bible_entries') THEN
    CREATE INDEX IF NOT EXISTS idx_story_bible_project ON story_bible_entries(project_id);
    CREATE INDEX IF NOT EXISTS idx_story_bible_category ON story_bible_entries(category);
  END IF;
END $$;

-- Timeline events indexes (if table exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'timeline_events') THEN
    CREATE INDEX IF NOT EXISTS idx_timeline_project ON timeline_events(project_id);
    CREATE INDEX IF NOT EXISTS idx_timeline_date ON timeline_events(event_date);
    CREATE INDEX IF NOT EXISTS idx_timeline_chapter ON timeline_events(chapter_id) WHERE chapter_id IS NOT NULL;
  END IF;
END $$;

-- Full text search indexes (requires pg_trgm extension)
DO $$ 
BEGIN
  -- Enable pg_trgm extension if not already enabled
  CREATE EXTENSION IF NOT EXISTS pg_trgm;
  
  -- Create GIN indexes for full text search
  CREATE INDEX IF NOT EXISTS idx_chapters_content_fts ON chapters USING gin (content gin_trgm_ops);
  CREATE INDEX IF NOT EXISTS idx_characters_search ON characters USING gin ((name || ' ' || COALESCE(bio, '')) gin_trgm_ops);
  CREATE INDEX IF NOT EXISTS idx_projects_search ON projects USING gin ((name || ' ' || COALESCE(description, '')) gin_trgm_ops);
EXCEPTION
  WHEN OTHERS THEN
    -- Extension might not be available in some environments
    RAISE NOTICE 'pg_trgm extension not available, skipping full text search indexes';
END $$;

-- Analyze tables to update statistics
ANALYZE users;
ANALYZE projects;
ANALYZE chapters;
ANALYZE characters;
ANALYZE series;
ANALYZE project_collaborators;
ANALYZE writing_sessions;
ANALYZE word_count_history;
ANALYZE notifications;
ANALYZE processing_tasks;
ANALYZE user_preferences;
ANALYZE project_settings;
ANALYZE profiles;

-- Add comment to track migration
COMMENT ON SCHEMA public IS 'BookScribe database schema - Performance indexes added 2025-02-01';