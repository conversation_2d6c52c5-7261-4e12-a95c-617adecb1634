'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import {
  Brain,
  Users,
  BookOpen,
  PenTool,
  Edit3,
  Target,
  Zap,
  ChevronRight,
  PlayCircle,
  PauseCircle,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Activity,
  Sparkles,
  GitBranch,
  Settings,
  Info,
  ArrowRight
} from 'lucide-react'

interface AgentStatus {
  id: string
  name: string
  type: 'story-architect' | 'character-developer' | 'chapter-planner' | 'writing' | 'editor' | 'adaptive'
  status: 'idle' | 'queued' | 'running' | 'completed' | 'error'
  progress: number
  currentTask?: string
  output?: string
  error?: string
  startTime?: number
  endTime?: number
  quality?: number
}

interface AgentHandoff {
  from: string
  to: string
  data: string
  timestamp: number
}

interface PipelineStatus {
  isRunning: boolean
  currentPhase: 'planning' | 'development' | 'writing' | 'editing' | 'complete'
  totalProgress: number
  startTime?: number
  estimatedCompletion?: number
  activeAgents: string[]
}

interface VisualAgentPipelineProps {
  projectId: string
  onComplete?: (results: Record<string, unknown>) => void
}

const AGENT_CONFIG = {
  'story-architect': {
    name: 'Story Architect',
    icon: Brain,
    color: 'bg-purple-500',
    description: 'Creates comprehensive story structure'
  },
  'character-developer': {
    name: 'Character Developer',
    icon: Users,
    color: 'bg-pink-500',
    description: 'Develops detailed character profiles'
  },
  'chapter-planner': {
    name: 'Chapter Planner',
    icon: BookOpen,
    color: 'bg-info',
    description: 'Plans chapter outlines and pacing'
  },
  'writing': {
    name: 'Writing Agent',
    icon: PenTool,
    color: 'bg-success',
    description: 'Generates chapter content'
  },
  'editor': {
    name: 'Editor Agent',
    icon: Edit3,
    color: 'bg-orange-500',
    description: 'Refines and polishes content'
  },
  'adaptive': {
    name: 'Adaptive Planning',
    icon: Target,
    color: 'bg-info',
    description: 'Adjusts plans based on progress'
  }
}

export function VisualAgentPipeline({ projectId, onComplete }: VisualAgentPipelineProps) {
  const [agents, setAgents] = useState<AgentStatus[]>([
    { id: 'story-architect', name: 'Story Architect', type: 'story-architect', status: 'idle', progress: 0 },
    { id: 'character-developer', name: 'Character Developer', type: 'character-developer', status: 'idle', progress: 0 },
    { id: 'chapter-planner', name: 'Chapter Planner', type: 'chapter-planner', status: 'idle', progress: 0 },
    { id: 'writing-agent', name: 'Writing Agent', type: 'writing', status: 'idle', progress: 0 },
    { id: 'editor-agent', name: 'Editor Agent', type: 'editor', status: 'idle', progress: 0 },
    { id: 'adaptive-agent', name: 'Adaptive Planning', type: 'adaptive', status: 'idle', progress: 0 }
  ])
  
  const [pipeline, setPipeline] = useState<PipelineStatus>({
    isRunning: false,
    currentPhase: 'planning',
    totalProgress: 0,
    activeAgents: []
  })
  
  const [handoffs, setHandoffs] = useState<AgentHandoff[]>([])
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)
  const [autoRun, setAutoRun] = useState(true)
  const { toast } = useToast()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const startPipeline = async () => {
    setPipeline(prev => ({
      ...prev,
      isRunning: true,
      startTime: Date.now(),
      estimatedCompletion: Date.now() + 300000 // 5 minutes estimate
    }))

    // Simulate agent pipeline execution
    simulateAgentExecution()
  }

  const pausePipeline = () => {
    setPipeline(prev => ({ ...prev, isRunning: false }))
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const simulateAgentExecution = () => {
    let step = 0
    const maxSteps = 100
    
    intervalRef.current = setInterval(() => {
      step++
      
      // Update agent statuses
      setAgents(prevAgents => {
        const updated = [...prevAgents]
        
        // Story Architect (0-20%)
        if (step <= 20) {
          const agent = updated.find(a => a.id === 'story-architect')
          if (agent) {
            agent.status = 'running'
            agent.progress = (step / 20) * 100
            agent.currentTask = 'Analyzing story requirements...'
            if (step === 20) {
              agent.status = 'completed'
              agent.output = 'Story structure complete with 3-act breakdown'
              agent.quality = 92
            }
          }
        }
        
        // Character Developer (15-40%)
        if (step >= 15 && step <= 40) {
          const agent = updated.find(a => a.id === 'character-developer')
          if (agent) {
            if (step === 15) {
              addHandoff('story-architect', 'character-developer', 'Story structure data')
            }
            agent.status = 'running'
            agent.progress = ((step - 15) / 25) * 100
            agent.currentTask = 'Creating character profiles...'
            if (step === 40) {
              agent.status = 'completed'
              agent.output = '5 main characters developed'
              agent.quality = 88
            }
          }
        }
        
        // Chapter Planner (35-60%)
        if (step >= 35 && step <= 60) {
          const agent = updated.find(a => a.id === 'chapter-planner')
          if (agent) {
            if (step === 35) {
              addHandoff('character-developer', 'chapter-planner', 'Character profiles')
            }
            agent.status = 'running'
            agent.progress = ((step - 35) / 25) * 100
            agent.currentTask = 'Planning chapter outlines...'
            if (step === 60) {
              agent.status = 'completed'
              agent.output = '20 chapters outlined'
              agent.quality = 90
            }
          }
        }
        
        // Writing Agent (55-80%)
        if (step >= 55 && step <= 80) {
          const agent = updated.find(a => a.id === 'writing-agent')
          if (agent) {
            if (step === 55) {
              addHandoff('chapter-planner', 'writing-agent', 'Chapter outlines')
            }
            agent.status = 'running'
            agent.progress = ((step - 55) / 25) * 100
            agent.currentTask = 'Writing chapter content...'
            if (step === 80) {
              agent.status = 'completed'
              agent.output = 'First draft complete'
              agent.quality = 85
            }
          }
        }
        
        // Editor Agent (75-95%)
        if (step >= 75 && step <= 95) {
          const agent = updated.find(a => a.id === 'editor-agent')
          if (agent) {
            if (step === 75) {
              addHandoff('writing-agent', 'editor-agent', 'Draft content')
            }
            agent.status = 'running'
            agent.progress = ((step - 75) / 20) * 100
            agent.currentTask = 'Editing and polishing...'
            if (step === 95) {
              agent.status = 'completed'
              agent.output = 'Content polished and refined'
              agent.quality = 94
            }
          }
        }
        
        // Adaptive Agent (runs periodically)
        if (step % 20 === 0 && step > 0 && step < 100) {
          const agent = updated.find(a => a.id === 'adaptive-agent')
          if (agent) {
            agent.status = 'running'
            agent.progress = 100
            agent.currentTask = 'Analyzing progress...'
            setTimeout(() => {
              setAgents(prev => prev.map(a => 
                a.id === 'adaptive-agent' 
                  ? { ...a, status: 'completed', output: 'Adjustments applied' }
                  : a
              ))
            }, 2000)
          }
        }
        
        return updated
      })
      
      // Update pipeline progress
      setPipeline(prev => ({
        ...prev,
        totalProgress: (step / maxSteps) * 100,
        currentPhase: 
          step <= 20 ? 'planning' :
          step <= 40 ? 'development' :
          step <= 80 ? 'writing' :
          step <= 95 ? 'editing' : 'complete',
        activeAgents: agents
          .filter(a => a.status === 'running')
          .map(a => a.id)
      }))
      
      if (step >= maxSteps) {
        clearInterval(intervalRef.current!)
        setPipeline(prev => ({
          ...prev,
          isRunning: false,
          currentPhase: 'complete',
          totalProgress: 100
        }))
        
        toast({
          title: "Pipeline Complete",
          description: "All agents have finished processing successfully"
        })
        
        onComplete?.({
          story: 'Complete story structure',
          characters: 'All character profiles',
          chapters: 'All chapter outlines',
          content: 'Generated content'
        })
      }
    }, 300) // Update every 300ms for smooth animation
  }

  const addHandoff = (from: string, to: string, data: string) => {
    setHandoffs(prev => [...prev, {
      from,
      to,
      data,
      timestamp: Date.now()
    }])
  }

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'planning': return 'text-purple-500'
      case 'development': return 'text-pink-500'
      case 'writing': return 'text-success'
      case 'editing': return 'text-warning'
      case 'complete': return 'text-info'
      default: return 'text-gray-500'
    }
  }

  const selectedAgentData = selectedAgent 
    ? agents.find(a => a.id === selectedAgent)
    : null

  return (
    <div className="space-y-6">
      {/* Pipeline Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              AI Agent Pipeline
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getPhaseColor(pipeline.currentPhase)}>
                {pipeline.currentPhase.charAt(0).toUpperCase() + pipeline.currentPhase.slice(1)}
              </Badge>
              {pipeline.isRunning ? (
                <Button size="sm" variant="outline" onClick={pausePipeline}>
                  <PauseCircle className="h-4 w-4 mr-1" />
                  Pause
                </Button>
              ) : (
                <Button size="sm" onClick={startPipeline}>
                  <PlayCircle className="h-4 w-4 mr-1" />
                  Start Pipeline
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(pipeline.totalProgress)}%
                </span>
              </div>
              <Progress value={pipeline.totalProgress} className="h-3" />
            </div>
            
            {pipeline.estimatedCompletion && pipeline.isRunning && (
              <Alert>
                <Activity className="h-4 w-4" />
                <AlertDescription>
                  Estimated completion: {new Date(pipeline.estimatedCompletion).toLocaleTimeString()}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Agent Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {agents.map((agent) => {
          const config = AGENT_CONFIG[agent.type]
          const Icon = config.icon
          
          return (
            <Card 
              key={agent.id}
              className={`cursor-pointer transition-all ${
                selectedAgent === agent.id ? 'ring-2 ring-primary' : ''
              } ${
                agent.status === 'running' ? 'animate-pulse' : ''
              }`}
              onClick={() => setSelectedAgent(agent.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 rounded-full ${config.color} text-white`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div>
                      <CardTitle className="text-sm">{agent.name}</CardTitle>
                      <p className="text-xs text-muted-foreground">
                        {config.description}
                      </p>
                    </div>
                  </div>
                  {agent.status === 'completed' && agent.quality && (
                    <Badge variant="outline" className="text-xs">
                      {agent.quality}% quality
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    {agent.status === 'idle' && <AlertCircle className="h-4 w-4 text-gray-400" />}
                    {agent.status === 'queued' && <Clock className="h-4 w-4 text-warning" />}
                    {agent.status === 'running' && <Loader2 className="h-4 w-4 animate-spin text-info" />}
                    {agent.status === 'completed' && <CheckCircle2 className="h-4 w-4 text-success" />}
                    {agent.status === 'error' && <AlertCircle className="h-4 w-4 text-error" />}
                    <span className="text-sm capitalize">{agent.status}</span>
                  </div>
                  
                  {agent.status !== 'idle' && (
                    <Progress value={agent.progress} className="h-2" />
                  )}
                  
                  {agent.currentTask && agent.status === 'running' && (
                    <p className="text-xs text-muted-foreground truncate">
                      {agent.currentTask}
                    </p>
                  )}
                  
                  {agent.output && agent.status === 'completed' && (
                    <p className="text-xs text-success dark:text-green-400">
                      {agent.output}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Detailed View */}
      <Card>
        <CardHeader>
          <CardTitle>Pipeline Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="handoffs">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="handoffs">Handoffs</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="handoffs" className="mt-4">
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {handoffs.length > 0 ? (
                    handoffs.map((handoff, idx) => (
                      <div key={idx} className="flex items-center gap-3 p-3 bg-muted rounded">
                        <Badge variant="outline">
                          {AGENT_CONFIG[agents.find(a => a.id === handoff.from)?.type || 'story-architect'].name}
                        </Badge>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        <Badge variant="outline">
                          {AGENT_CONFIG[agents.find(a => a.id === handoff.to)?.type || 'story-architect'].name}
                        </Badge>
                        <span className="text-sm text-muted-foreground flex-1">
                          {handoff.data}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(handoff.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                    ))
                  ) : (
                    <p className="text-center text-muted-foreground py-8">
                      No handoffs yet. Start the pipeline to see agent interactions.
                    </p>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="output" className="mt-4">
              {selectedAgentData ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 rounded-full ${AGENT_CONFIG[selectedAgentData.type].color} text-white`}>
                      {(() => {
                        const Icon = AGENT_CONFIG[selectedAgentData.type].icon
                        return <Icon className="h-4 w-4" />
                      })()}
                    </div>
                    <h3 className="font-semibold">{selectedAgentData.name}</h3>
                  </div>
                  
                  {selectedAgentData.output ? (
                    <Card>
                      <CardContent className="pt-6">
                        <p className="text-sm">{selectedAgentData.output}</p>
                        {selectedAgentData.quality && (
                          <div className="mt-4 flex items-center gap-2">
                            <Zap className="h-4 w-4 text-warning" />
                            <span className="text-sm">
                              Quality Score: {selectedAgentData.quality}%
                            </span>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ) : (
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription>
                        No output available yet. The agent needs to complete its task first.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-8">
                  Select an agent to view its output
                </p>
              )}
            </TabsContent>
            
            <TabsContent value="settings" className="mt-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Auto-run Pipeline</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically progress through all agents
                    </p>
                  </div>
                  <Button
                    variant={autoRun ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setAutoRun(!autoRun)}
                  >
                    {autoRun ? 'Enabled' : 'Disabled'}
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Parallel Processing</h4>
                    <p className="text-sm text-muted-foreground">
                      Allow compatible agents to run simultaneously
                    </p>
                  </div>
                  <Badge variant="outline">Coming Soon</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Quality Threshold</h4>
                    <p className="text-sm text-muted-foreground">
                      Minimum quality score to proceed
                    </p>
                  </div>
                  <Badge variant="outline">85%</Badge>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}