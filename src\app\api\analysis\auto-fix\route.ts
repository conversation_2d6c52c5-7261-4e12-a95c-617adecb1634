import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { checkProjectAccess } from '@/lib/auth/collaboration-auth'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import OpenAI from 'openai'

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// Validation schema for auto-fix request
const autoFixRequestSchema = z.object({
  projectId: z.string().uuid(),
  content: z.string().min(1),
  issues: z.array(z.object({
    type: z.enum(['sentence_length', 'complex_words', 'passive_voice', 'readability']),
    priority: z.enum(['high', 'medium', 'low']),
    description: z.string(),
    example: z.string().optional(),
    startIndex: z.number().optional(),
    endIndex: z.number().optional(),
  })),
  targetAudience: z.string().optional(),
  targetGradeLevel: z.number().optional(),
})

interface TextFix {
  original: string
  suggestion: string
  reason: string
  startIndex: number
  endIndex: number
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = autoFixRequestSchema.parse(body)

    // Check if user has access to the project
    const permissions = await checkProjectAccess(authResult.user.id, validatedData.projectId)
    if (!permissions.canEdit) {
      return NextResponse.json(
        { error: 'You do not have permission to edit this project' },
        { status: 403 }
      )
    }

    // Validate OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      logger.error('OpenAI API key not configured')
      return NextResponse.json(
        { error: 'AI service not configured' },
        { status: 503 }
      )
    }

    // Group issues by type for more efficient processing
    const issuesByType = validatedData.issues.reduce((acc, issue) => {
      if (!acc[issue.type]) acc[issue.type] = []
      acc[issue.type].push(issue)
      return acc
    }, {} as Record<string, typeof validatedData.issues>)

    const fixes: TextFix[] = []

    // Handle sentence length issues
    if (issuesByType.sentence_length) {
      const longSentences = await fixLongSentences(validatedData.content, issuesByType.sentence_length)
      fixes.push(...longSentences)
    }

    // Handle complex words
    if (issuesByType.complex_words) {
      const simplifiedWords = await simplifyComplexWords(
        validatedData.content, 
        issuesByType.complex_words,
        validatedData.targetAudience
      )
      fixes.push(...simplifiedWords)
    }

    // Handle passive voice
    if (issuesByType.passive_voice) {
      const activeVoice = await convertToActiveVoice(validatedData.content, issuesByType.passive_voice)
      fixes.push(...activeVoice)
    }

    // Sort fixes by position (reverse order to apply from end to start)
    fixes.sort((a, b) => b.startIndex - a.startIndex)

    // Apply fixes to create the improved content
    let improvedContent = validatedData.content
    for (const fix of fixes) {
      improvedContent = 
        improvedContent.slice(0, fix.startIndex) + 
        fix.suggestion + 
        improvedContent.slice(fix.endIndex)
    }

    // Log the auto-fix operation
    logger.info('Auto-fix applied', {
      projectId: validatedData.projectId,
      userId: authResult.user.id,
      issueCount: validatedData.issues.length,
      fixCount: fixes.length,
    })

    return NextResponse.json({
      success: true,
      originalContent: validatedData.content,
      improvedContent,
      fixes,
      metrics: {
        issuesIdentified: validatedData.issues.length,
        fixesApplied: fixes.length,
        charactersChanged: Math.abs(improvedContent.length - validatedData.content.length),
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    logger.error('Error applying auto-fixes', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function fixLongSentences(content: string, issues: Array<{ example?: string }>): Promise<TextFix[]> {
  const fixes: TextFix[] = []
  
  // Find sentences over 30 words
  const sentenceRegex = /[^.!?]+[.!?]+/g
  let match
  
  while ((match = sentenceRegex.exec(content)) !== null) {
    const sentence = match[0].trim()
    const wordCount = sentence.split(/\s+/).length
    
    if (wordCount > 30) {
      try {
        const response = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a writing assistant. Break long sentences into shorter, clearer ones while preserving meaning. Return only the rewritten sentence(s).'
            },
            {
              role: 'user',
              content: `Break this long sentence into shorter ones: "${sentence}"`
            }
          ],
          max_tokens: 200,
          temperature: 0.3,
        })

        const suggestion = response.choices[0]?.message?.content?.trim()
        if (suggestion && suggestion !== sentence) {
          fixes.push({
            original: sentence,
            suggestion,
            reason: 'Sentence split for better readability',
            startIndex: match.index,
            endIndex: match.index + sentence.length
          })
        }
      } catch (err) {
        logger.error('Failed to fix long sentence', err)
      }
    }
  }
  
  return fixes
}

async function simplifyComplexWords(
  content: string, 
  issues: Array<{ example?: string }>,
  targetAudience?: string
): Promise<TextFix[]> {
  const fixes: TextFix[] = []
  
  // Extract examples of complex words from issues
  const complexWords = issues
    .map(issue => issue.example)
    .filter(Boolean)
    .join(', ')
  
  if (!complexWords) return fixes

  try {
    const audienceContext = targetAudience 
      ? `for a ${targetAudience} audience` 
      : 'for general readability'
      
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `You are a writing assistant. Suggest simpler alternatives for complex words ${audienceContext}. Return a JSON array of objects with 'word' and 'replacement' properties.`
        },
        {
          role: 'user',
          content: `Suggest simpler alternatives for these words: ${complexWords}`
        }
      ],
      max_tokens: 200,
      temperature: 0.3,
    })

    const suggestions = response.choices[0]?.message?.content?.trim()
    if (suggestions) {
      try {
        const parsed = JSON.parse(suggestions) as Array<{ word: string; replacement: string }>
        
        for (const { word, replacement } of parsed) {
          // Find all occurrences of the word in content
          const wordRegex = new RegExp(`\\b${word}\\b`, 'gi')
          let wordMatch
          
          while ((wordMatch = wordRegex.exec(content)) !== null) {
            fixes.push({
              original: word,
              suggestion: replacement,
              reason: 'Simplified complex word',
              startIndex: wordMatch.index,
              endIndex: wordMatch.index + word.length
            })
          }
        }
      } catch (parseErr) {
        logger.error('Failed to parse word simplification suggestions', parseErr)
      }
    }
  } catch (err) {
    logger.error('Failed to simplify complex words', err)
  }
  
  return fixes
}

async function convertToActiveVoice(content: string, issues: Array<{ example?: string }>): Promise<TextFix[]> {
  const fixes: TextFix[] = []
  
  // Extract passive voice examples
  const passiveExamples = issues
    .map(issue => issue.example)
    .filter(Boolean)
  
  if (passiveExamples.length === 0) return fixes

  for (const example of passiveExamples) {
    if (!example) continue
    
    const exampleIndex = content.indexOf(example)
    if (exampleIndex === -1) continue
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a writing assistant. Convert passive voice to active voice while preserving meaning. Return only the rewritten text.'
          },
          {
            role: 'user',
            content: `Convert this passive voice to active voice: "${example}"`
          }
        ],
        max_tokens: 100,
        temperature: 0.3,
      })

      const suggestion = response.choices[0]?.message?.content?.trim()
      if (suggestion && suggestion !== example) {
        fixes.push({
          original: example,
          suggestion,
          reason: 'Converted to active voice',
          startIndex: exampleIndex,
          endIndex: exampleIndex + example.length
        })
      }
    } catch (err) {
      logger.error('Failed to convert passive voice', err)
    }
  }
  
  return fixes
}