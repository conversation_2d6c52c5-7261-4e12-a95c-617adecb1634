import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { StepComponentProps } from './wizard-types'
import { SPACING } from '@/lib/config/ui-config'

export function StepGenreStyle({ formData, updateFormData, mode }: StepComponentProps) {
  return (
    <div className={SPACING.SPACE_Y.MD}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
        <div>
          <Label htmlFor="genre" className="text-base font-semibold">
            Genre
          </Label>
          <Select
            value={formData.genre}
            onValueChange={(value) => updateFormData('genre', value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger id="genre" className="mt-2">
              <SelectValue placeholder="Select a genre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fantasy">Fantasy</SelectItem>
              <SelectItem value="sci-fi">Science Fiction</SelectItem>
              <SelectItem value="mystery">Mystery</SelectItem>
              <SelectItem value="romance">Romance</SelectItem>
              <SelectItem value="thriller">Thriller</SelectItem>
              <SelectItem value="horror">Horror</SelectItem>
              <SelectItem value="literary">Literary Fiction</SelectItem>
              <SelectItem value="historical">Historical Fiction</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="subgenre" className="text-base font-semibold">
            Subgenre
          </Label>
          <Select
            value={formData.subgenre}
            onValueChange={(value) => updateFormData('subgenre', value)}
            disabled={mode === 'demo' || !formData.genre}
          >
            <SelectTrigger id="subgenre" className="mt-2">
              <SelectValue placeholder="Select a subgenre" />
            </SelectTrigger>
            <SelectContent>
              {formData.genre === 'fantasy' && (
                <>
                  <SelectItem value="epic-fantasy">Epic Fantasy</SelectItem>
                  <SelectItem value="urban-fantasy">Urban Fantasy</SelectItem>
                  <SelectItem value="dark-fantasy">Dark Fantasy</SelectItem>
                  <SelectItem value="sword-sorcery">Sword & Sorcery</SelectItem>
                </>
              )}
              {formData.genre === 'sci-fi' && (
                <>
                  <SelectItem value="space-opera">Space Opera</SelectItem>
                  <SelectItem value="cyberpunk">Cyberpunk</SelectItem>
                  <SelectItem value="hard-scifi">Hard Sci-Fi</SelectItem>
                  <SelectItem value="dystopian">Dystopian</SelectItem>
                </>
              )}
              {formData.genre === 'mystery' && (
                <>
                  <SelectItem value="cozy-mystery">Cozy Mystery</SelectItem>
                  <SelectItem value="police-procedural">Police Procedural</SelectItem>
                  <SelectItem value="noir">Noir</SelectItem>
                  <SelectItem value="amateur-sleuth">Amateur Sleuth</SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
        <div>
          <Label htmlFor="tone" className="text-base font-semibold">
            Tone
          </Label>
          <Select
            value={formData.tone}
            onValueChange={(value) => updateFormData('tone', value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger id="tone" className="mt-2">
              <SelectValue placeholder="Select tone" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="lighthearted">Lighthearted</SelectItem>
              <SelectItem value="serious">Serious</SelectItem>
              <SelectItem value="dark">Dark</SelectItem>
              <SelectItem value="humorous">Humorous</SelectItem>
              <SelectItem value="adventurous">Adventurous</SelectItem>
              <SelectItem value="mysterious">Mysterious</SelectItem>
              <SelectItem value="romantic">Romantic</SelectItem>
              <SelectItem value="suspenseful">Suspenseful</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="targetAudience" className="text-base font-semibold">
            Target Audience
          </Label>
          <Select
            value={formData.targetAudience}
            onValueChange={(value) => updateFormData('targetAudience', value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger id="targetAudience" className="mt-2">
              <SelectValue placeholder="Select audience" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="middle-grade">Middle Grade (8-12)</SelectItem>
              <SelectItem value="young-adult">Young Adult (13-17)</SelectItem>
              <SelectItem value="new-adult">New Adult (18-25)</SelectItem>
              <SelectItem value="adult">Adult (25+)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}