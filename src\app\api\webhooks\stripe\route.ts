import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import { headers } from 'next/headers'
import Stripe from 'stripe'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { config } from '@/lib/config'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/db/types'
import { TIME_MS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

import { successResponse, errorResponse, paginatedResponse, ErrorResponses } from '@/lib/api/response-utils'
import { applyRateLimit } from '@/lib/rate-limiter-unified'
const stripe = new Stripe(config.stripe.secretKey, {
  apiVersion: '2024-11-20.acacia' as Stripe.LatestApiVersion,
})

const endpointSecret = config.stripe.webhookSecret

export async function POST(req: NextRequest) {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'webhook' })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

  const body = await req.text()
  const signature = headers().get('stripe-signature')!

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(body, signature, endpointSecret)
  } catch (err) {
    logger.error('Stripe webhook signature verification failed', err)
    return ErrorResponses.validationError("Invalid request")
  }

  const supabase = createTypedServerClient()

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        await handleCheckoutComplete(session, supabase)
        break
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        await handleSubscriptionChange(subscription, supabase)
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        await handleSubscriptionCanceled(subscription, supabase)
        break
      }

      case 'customer.subscription.trial_will_end': {
        const subscription = event.data.object as Stripe.Subscription
        await handleTrialWillEnd(subscription, supabase)
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        await handlePaymentSucceeded(invoice, supabase)
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        await handlePaymentFailed(invoice, supabase)
        break
      }

      case 'payment_method.attached': {
        const paymentMethod = event.data.object as Stripe.PaymentMethod
        await handlePaymentMethodAttached(paymentMethod, supabase)
        break
      }

      default:
        logger.info(`Unhandled Stripe webhook event: ${event.type}`)
    }

    return successResponse({ received: true  })
  } catch (error) {
    logger.error('Error processing Stripe webhook', error)
    return ErrorResponses.internalError('Webhook processing failed')
  }
}

async function handleCheckoutComplete(
  session: Stripe.Checkout.Session,
  supabase: SupabaseClient<Database>
) {
  const { customer, subscription, metadata } = session

  if (!metadata?.userId) {
    logger.error('No userId in checkout session metadata')
    return
  }

  // Update or create user subscription
  const subscriptionData = {
    user_id: metadata.userId,
    stripe_customer_id: customer as string,
    stripe_subscription_id: subscription as string,
    tier: metadata.tier || 'starter',
    status: 'active',
    current_period_end: new Date(metadata.periodEnd || Date.now()),
    updated_at: new Date().toISOString(),
  }

  const { error } = await supabase
    .from('user_subscriptions')
    .upsert(subscriptionData, {
      onConflict: 'user_id'
    })

  if (error) {
    logger.error('Failed to update subscription after checkout', error)
    throw error
  }

  // Initialize subscription usage tracking
  await initializeSubscriptionUsage(metadata.userId, metadata.tier, supabase)

  logger.info('Checkout completed successfully', {
    userId: metadata.userId,
    tier: metadata.tier,
    subscriptionId: subscription
  })
}

async function handleSubscriptionChange(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient<Database>
) {
  const { data: existingSub } = await supabase
    .from('user_subscriptions')
    .select('user_id, tier')
    .eq('stripe_subscription_id', subscription.id)
    .single()

  if (!existingSub) {
    logger.warn('Subscription not found for update', { subscriptionId: subscription.id })
    return
  }

  const newTier = subscription.metadata.tier || existingSub.tier
  const oldTier = existingSub.tier

  // Update subscription
  const { error } = await supabase
    .from('user_subscriptions')
    .update({
      status: subscription.status,
      tier: newTier,
      current_period_end: new Date(subscription.current_period_end * TIME_MS.SECOND),
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id)

  if (error) {
    logger.error('Failed to update subscription', error)
    throw error
  }

  // Handle tier changes
  if (oldTier !== newTier) {
    await handleTierChange(existingSub.user_id, oldTier, newTier, supabase)
  }

  logger.info('Subscription updated', {
    userId: existingSub.user_id,
    oldTier,
    newTier,
    status: subscription.status
  })
}

async function handleSubscriptionCanceled(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient<Database>
) {
  const { error } = await supabase
    .from('user_subscriptions')
    .update({
      status: 'canceled',
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id)

  if (error) {
    logger.error('Failed to cancel subscription', error)
    throw error
  }

  logger.info('Subscription canceled', { subscriptionId: subscription.id })
}

async function handleTrialWillEnd(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient<Database>
) {
  const { data: sub } = await supabase
    .from('user_subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single()

  if (!sub) return

  // Send trial ending notification email
  const { sendEmail } = await import('@/lib/email')
  const { data: user } = await supabase
    .from('users')
    .select('email')
    .eq('id', sub.user_id)
    .single()

  if (user) {
    await sendEmail({
      to: user.email,
      subject: 'Your BookScribe trial is ending soon',
      html: `
        <h2>Your trial period is ending in 3 days</h2>
        <p>To continue using BookScribe without interruption, please add a payment method to your account.</p>
        <a href="${config.app.url}/settings/billing">Manage Billing</a>
      `
    })
  }

  logger.info('Trial ending notification sent', { userId: sub.user_id })
}

async function handlePaymentSucceeded(
  invoice: Stripe.Invoice,
  supabase: SupabaseClient<Database>
) {
  // Reset usage for new billing period
  const { data: sub } = await supabase
    .from('user_subscriptions')
    .select('user_id, tier')
    .eq('stripe_customer_id', invoice.customer)
    .single()

  if (sub) {
    await initializeSubscriptionUsage(sub.user_id, sub.tier, supabase)
    logger.info('Payment succeeded and usage reset', { userId: sub.user_id })
  }
}

async function handlePaymentFailed(
  invoice: Stripe.Invoice,
  supabase: SupabaseClient<Database>
) {
  const { data: sub } = await supabase
    .from('user_subscriptions')
    .select('user_id')
    .eq('stripe_customer_id', invoice.customer)
    .single()

  if (sub) {
    // Update subscription status
    await supabase
      .from('user_subscriptions')
      .update({ status: 'past_due' })
      .eq('user_id', sub.user_id)

    // Send payment failed email
    const { sendEmail } = await import('@/lib/email')
    const { data: user } = await supabase
      .from('users')
      .select('email')
      .eq('id', sub.user_id)
      .single()

    if (user) {
      await sendEmail({
        to: user.email,
        subject: 'Payment failed - Action required',
        html: `
          <h2>We couldn't process your payment</h2>
          <p>Please update your payment method to continue using BookScribe.</p>
          <a href="${config.app.url}/settings/billing">Update Payment Method</a>
        `
      })
    }

    logger.warn('Payment failed', { userId: sub.user_id })
  }
}

async function handlePaymentMethodAttached(
  paymentMethod: Stripe.PaymentMethod,
  supabase: SupabaseClient<Database>
) {
  logger.info('Payment method attached', {
    customerId: paymentMethod.customer,
    type: paymentMethod.type
  })
}

async function handleTierChange(
  userId: string,
  oldTier: string,
  newTier: string,
  supabase: SupabaseClient<Database>
) {
  // Handle downgrade - check limits
  interface TierLimit {
    collaborators: number
    ai_words: number
    projects: number
  }
  
  const tierLimits: Record<string, TierLimit> = {
    free: { collaborators: 0, ai_words: TIME_MS.SECOND, projects: 1 },
    starter: { collaborators: 0, ai_words: SIZE_LIMITS.MAX_DOCUMENT_CHARS, projects: 3 },
    professional: { collaborators: 2, ai_words: 200000, projects: 10 },
    studio: { collaborators: 5, ai_words: -1, projects: -1 }
  }

  const newLimits = tierLimits[newTier] || tierLimits.free

  // Check if user needs to reduce usage
  if (newTier === 'free' || newTier === 'starter') {
    // Remove collaborators if downgrading
    if (newLimits.collaborators === 0) {
      await supabase
        .from('project_collaborators')
        .delete()
        .eq('user_id', userId)
    }
  }

  // Update usage limits
  await supabase
    .from('subscription_usage')
    .update({
      ai_words_limit: newLimits.ai_words,
      projects_limit: newLimits.projects,
      collaborators_limit: newLimits.collaborators,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  logger.info('Tier change processed', { userId, oldTier, newTier })
}

interface FullTierLimit {
  ai_words: number
  projects: number
  collaborators: number
  export_limits: {
    pdf: number
    epub: number
    docx: number
  }
}

async function initializeSubscriptionUsage(
  userId: string,
  tier: string,
  supabase: SupabaseClient<Database>
) {
  const tierLimits: Record<string, FullTierLimit> = {
    free: {
      ai_words: TIME_MS.SECOND,
      projects: 1,
      collaborators: 0,
      export_limits: { pdf: 0, epub: 0, docx: 0 }
    },
    starter: {
      ai_words: SIZE_LIMITS.MAX_DOCUMENT_CHARS,
      projects: 3,
      collaborators: 0,
      export_limits: { pdf: 0, epub: 0, docx: -1 }
    },
    professional: {
      ai_words: 200000,
      projects: 10,
      collaborators: 2,
      export_limits: { pdf: -1, epub: -1, docx: -1 }
    },
    studio: {
      ai_words: -1, // unlimited
      projects: -1,
      collaborators: 5,
      export_limits: { pdf: -1, epub: -1, docx: -1 }
    }
  }

  const limits = tierLimits[tier] || tierLimits.free

  // Create or reset usage for new period
  const periodStart = new Date()
  const periodEnd = new Date()
  periodEnd.setMonth(periodEnd.getMonth() + 1)

  await supabase
    .from('subscription_usage')
    .upsert({
      user_id: userId,
      period_start: periodStart.toISOString(),
      period_end: periodEnd.toISOString(),
      ai_words_used: 0,
      ai_words_limit: limits.ai_words,
      projects_count: 0,
      projects_limit: limits.projects,
      collaborators_count: 0,
      collaborators_limit: limits.collaborators,
      export_limits: limits.export_limits,
      updated_at: new Date().toISOString()
    })

  logger.info('Subscription usage initialized', { userId, tier })
}