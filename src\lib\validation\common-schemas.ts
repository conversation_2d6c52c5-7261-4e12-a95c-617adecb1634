/**
 * Common Zod validation schemas used across API routes
 * Provides standardized validation for common data types
 */

import { z } from 'zod'
import { VALIDATION_RULES, CONTENT_LIMITS } from '@/lib/config/app-constants'
import { SIZE_LIMITS } from '@/lib/constants'

// UUID validation
export const uuidSchema = z.string().uuid()

// Common ID schemas
export const idSchema = z.string().min(1).max(100)
export const projectIdSchema = uuidSchema
export const userIdSchema = uuidSchema
export const chapterIdSchema = uuidSchema
export const characterIdSchema = uuidSchema
export const seriesIdSchema = uuidSchema

// Pagination schemas
export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

// Date range schemas
export const dateRangeSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
})

// Common string validations
export const titleSchema = z.string().min(1).max(CONTENT_LIMITS.MAX_TITLE_LENGTH).trim()
export const descriptionSchema = z.string().max(CONTENT_LIMITS.MAX_DESCRIPTION_LENGTH).trim().optional()
export const contentSchema = z.string().min(1).max(1000000) // 1MB limit
export const shortTextSchema = z.string().min(1).max(500).trim()
export const emailSchema = z.string().email().toLowerCase()
export const urlSchema = z.string().url()

// User input schemas
export const userNameSchema = z.string()
  .min(VALIDATION_RULES.MIN_USERNAME_LENGTH)
  .max(VALIDATION_RULES.MAX_USERNAME_LENGTH)
  .regex(VALIDATION_RULES.USERNAME_PATTERN, 'Username can only contain letters, numbers, underscores, and hyphens')
  .trim()
export const passwordSchema = z.string()
  .min(VALIDATION_RULES.MIN_PASSWORD_LENGTH, `Password must be at least ${VALIDATION_RULES.MIN_PASSWORD_LENGTH} characters`)
  .max(VALIDATION_RULES.MAX_PASSWORD_LENGTH)
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
export const displayNameSchema = z.string().min(1).max(100).trim()

// File upload schemas
export const fileUploadSchema = z.object({
  filename: z.string().max(255),
  mimeType: z.string().max(100),
  size: z.number().int().min(1).max(52428800), // 50MB max
  data: z.string().optional() // Base64 encoded
})

// AI-related schemas
export const aiModelSchema = z.enum([
  'gpt-4.1-2025-04-14',
  'gpt-4o-mini',
  'gpt-4-turbo',
  'gpt-4-0125-preview',
  'gpt-3.5-turbo',
  'grok-beta',
  'grok-vision-beta'
])

export const temperatureSchema = z.number().min(0).max(2).default(0.7)
export const maxTokensSchema = z.number().int().min(1).max(SIZE_LIMITS.EMBEDDING_TEXT_LIMIT).default(2000)

// Project settings schemas
export const genreSchema = z.enum([
  'fantasy',
  'science-fiction',
  'mystery',
  'thriller',
  'romance',
  'literary-fiction',
  'historical-fiction',
  'young-adult',
  'horror',
  'contemporary',
  'dystopian',
  'magical-realism',
  'adventure',
  'crime',
  'comedy'
])

export const writingStyleSchema = z.enum([
  'literary',
  'commercial',
  'minimalist',
  'descriptive',
  'dialogue-heavy',
  'action-packed',
  'introspective',
  'humorous',
  'poetic',
  'journalistic'
])

export const povSchema = z.enum([
  'first-person',
  'third-person-limited',
  'third-person-omniscient',
  'second-person',
  'multiple-pov'
])

export const tenseSchema = z.enum(['past', 'present', 'future'])

// Status schemas
export const projectStatusSchema = z.enum(['draft', 'in-progress', 'review', 'completed', 'published', 'archived'])
export const taskStatusSchema = z.enum(['pending', 'in-progress', 'completed', 'failed', 'cancelled'])
export const collaboratorStatusSchema = z.enum(['pending', 'active', 'inactive', 'removed'])

// Additional schemas for project content
export const projectNameSchema = z.string()
  .min(VALIDATION_RULES.MIN_PROJECT_NAME_LENGTH, 'Project name is required')
  .max(VALIDATION_RULES.MAX_PROJECT_NAME_LENGTH)
  .regex(VALIDATION_RULES.PROJECT_NAME_PATTERN, 'Project name contains invalid characters')
  .trim()

export const chapterContentSchema = z.string()
  .min(CONTENT_LIMITS.MIN_CHAPTER_WORDS * 4, 'Chapter content is too short') // Rough char to word conversion
  .max(CONTENT_LIMITS.MAX_CHAPTER_WORDS * 6, 'Chapter content is too long')

export const characterNameSchema = z.string()
  .min(1, 'Character name is required')
  .max(CONTENT_LIMITS.MAX_CHARACTER_NAME_LENGTH)
  .trim()

export const characterBioSchema = z.string()
  .max(CONTENT_LIMITS.MAX_CHARACTER_BIO_LENGTH)
  .optional()

// Role and permission schemas
export const roleSchema = z.enum(['viewer', 'commenter', 'editor', 'admin'])
export const subscriptionTierSchema = z.enum(['free', 'pro', 'enterprise'])

// Export format schema
export const exportFormatSchema = z.enum(['epub', 'pdf', 'docx', 'txt', 'md'])

// Enhanced AI generation options
export const aiGenerationOptionsSchema = z.object({
  model: aiModelSchema.optional(),
  temperature: temperatureSchema,
  maxTokens: maxTokensSchema.optional(),
  topP: z.number().min(0).max(1).default(1),
  frequencyPenalty: z.number().min(-2).max(2).default(0),
  presencePenalty: z.number().min(-2).max(2).default(0)
})

// Metadata schemas
export const metadataSchema = z.record(z.string(), z.unknown()).optional()
export const tagsSchema = z.array(z.string().max(50)).max(20).optional()

// Response schemas
export const successResponseSchema = z.object({
  success: z.literal(true),
  data: z.unknown().optional(),
  message: z.string().optional()
})

export const errorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  details: z.unknown().optional(),
  code: z.string().optional()
})

export const paginatedResponseSchema = <T extends z.ZodType>(itemSchema: T) => z.object({
  success: z.literal(true),
  data: z.array(itemSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasMore: z.boolean()
  })
})

// Utility function to create optional version of schema
export function makeOptional<T extends z.ZodType>(schema: T) {
  return schema.optional()
}

// Utility function to create nullable version of schema
export function makeNullable<T extends z.ZodType>(schema: T) {
  return schema.nullable()
}

// Export all schemas as a namespace for easy access
export const CommonSchemas = {
  // IDs
  uuid: uuidSchema,
  id: idSchema,
  projectId: projectIdSchema,
  userId: userIdSchema,
  chapterId: chapterIdSchema,
  characterId: characterIdSchema,
  seriesId: seriesIdSchema,
  
  // Strings
  title: titleSchema,
  description: descriptionSchema,
  content: contentSchema,
  shortText: shortTextSchema,
  email: emailSchema,
  url: urlSchema,
  
  // User
  userName: userNameSchema,
  password: passwordSchema,
  displayName: displayNameSchema,
  
  // Files
  fileUpload: fileUploadSchema,
  
  // AI
  aiModel: aiModelSchema,
  temperature: temperatureSchema,
  maxTokens: maxTokensSchema,
  
  // Project
  genre: genreSchema,
  writingStyle: writingStyleSchema,
  pov: povSchema,
  tense: tenseSchema,
  
  // Status
  projectStatus: projectStatusSchema,
  taskStatus: taskStatusSchema,
  collaboratorStatus: collaboratorStatusSchema,
  
  // Common
  pagination: paginationSchema,
  dateRange: dateRangeSchema,
  metadata: metadataSchema,
  tags: tagsSchema,
  
  // Responses
  successResponse: successResponseSchema,
  errorResponse: errorResponseSchema,
  paginatedResponse: paginatedResponseSchema
}

export default CommonSchemas