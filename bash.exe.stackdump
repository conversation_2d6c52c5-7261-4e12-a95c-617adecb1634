Stack trace:
Frame         Function      Args
0007FFFFA100  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9000) msys-2.0.dll+0x1FEBA
0007FFFFA100  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3D8) msys-2.0.dll+0x67F9
0007FFFFA100  000210046832 (000210285FF9, 0007FFFF9FB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA100  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA100  0002100690B4 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA3E0  00021006A49D (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC8A960000 ntdll.dll
7FFC89A30000 KERNEL32.DLL
7FFC88040000 KERNELBASE.dll
7FFC83910000 apphelp.dll
7FFC897B0000 USER32.dll
7FFC87E90000 win32u.dll
7FFC89C70000 GDI32.dll
7FFC87AB0000 gdi32full.dll
7FFC87BF0000 msvcp_win.dll
7FFC88430000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC888B0000 advapi32.dll
7FFC89980000 msvcrt.dll
7FFC896F0000 sechost.dll
7FFC89DA0000 RPCRT4.dll
7FFC86810000 CRYPTBASE.DLL
7FFC87DF0000 bcryptPrimitives.dll
7FFC88870000 IMM32.DLL
