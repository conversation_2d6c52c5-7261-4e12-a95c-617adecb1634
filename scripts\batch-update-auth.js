#!/usr/bin/env node

/**
 * Batch update script to migrate API routes to UnifiedAuthService
 * This updates imports and simple auth patterns automatically
 */

const fs = require('fs');
const path = require('path');

// Read the migration report
const reportPath = path.join(__dirname, 'auth-migration-report.json');
const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));

// Update patterns
const updatePatterns = [
  // Import updates
  {
    pattern: /import\s*{[^}]*}\s*from\s*['"]@\/lib\/api\/auth-helpers['"]/g,
    replacement: (match) => {
      // Extract the imports
      const imports = match.match(/{([^}]*)}/)[1];
      // Map old imports to new ones
      const importMap = {
        'authenticateUser': 'UnifiedAuthService',
        'authenticateUserForProject': 'UnifiedAuthService',
        'authenticateUserForSeries': 'UnifiedAuthService',
        'authenticateAdmin': 'UnifiedAuthService',
        'withAuth': 'UnifiedAuthService',
        'withAdmin': 'UnifiedAuthService',
        'withProjectAccess': 'UnifiedAuthService'
      };
      
      const importsList = imports.split(',').map(i => i.trim());
      const hasUnifiedImports = importsList.some(i => Object.keys(importMap).includes(i));
      
      if (hasUnifiedImports) {
        return `import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'`;
      }
      return match;
    }
  },
  {
    pattern: /import\s*{[^}]*}\s*from\s*['"]@\/lib\/api\/auth-middleware['"]/g,
    replacement: `import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'`
  },
  {
    pattern: /import\s*{[^}]*authenticateUser[^}]*}\s*from\s*['"]@\/lib\/auth['"]/g,
    replacement: `import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'`
  },
  // Simple function call replacements
  {
    pattern: /const\s*{\s*user\s*,\s*error:\s*authError\s*}\s*=\s*await\s+authenticateUser\(\s*\)/g,
    replacement: 'const user = await UnifiedAuthService.authenticateUser(request)'
  },
  {
    pattern: /const\s*authResult\s*=\s*await\s+requireAuth\(request\)/g,
    replacement: 'const user = await UnifiedAuthService.authenticateUser(request)'
  }
];

function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Apply update patterns
  updatePatterns.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      if (typeof replacement === 'function') {
        content = content.replace(pattern, replacement);
      } else {
        content = content.replace(pattern, replacement);
      }
      updated = true;
    }
  });
  
  // Add type import if using AuthenticatedRequest
  if (content.includes('UnifiedAuthService') && !content.includes('AuthenticatedRequest')) {
    content = content.replace(
      /import\s*{\s*UnifiedAuthService\s*}\s*from\s*['"]@\/lib\/auth\/unified-auth-service['"]/,
      `import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'`
    );
  }
  
  return { content, updated };
}

async function main() {
  console.log('🔄 Starting batch auth migration...\n');
  
  const apiPath = path.join(__dirname, '../src/app/api');
  let updatedCount = 0;
  
  // Process only simple files first (no complex wrappers)
  const simpleFiles = report.files.filter(f => 
    !f.patterns.includes('withAuth wrapper') &&
    !f.patterns.includes('withAdmin wrapper') &&
    !f.patterns.includes('withProjectAccess wrapper')
  );
  
  for (const file of simpleFiles) {
    const fullPath = path.join(apiPath, file.path);
    
    try {
      const { content, updated } = updateFile(fullPath);
      
      if (updated) {
        fs.writeFileSync(fullPath, content);
        console.log(`✅ Updated: ${file.path}`);
        updatedCount++;
      } else {
        console.log(`⏭️  Skipped: ${file.path} (no simple patterns found)`);
      }
    } catch (error) {
      console.error(`❌ Error updating ${file.path}:`, error.message);
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`- Files processed: ${simpleFiles.length}`);
  console.log(`- Files updated: ${updatedCount}`);
  console.log(`- Files requiring manual update: ${report.files.length - simpleFiles.length}`);
  
  // List files that need manual updates
  const complexFiles = report.files.filter(f => 
    f.patterns.includes('withAuth wrapper') ||
    f.patterns.includes('withAdmin wrapper') ||
    f.patterns.includes('withProjectAccess wrapper')
  );
  
  if (complexFiles.length > 0) {
    console.log('\n⚠️  Files requiring manual update (have wrapper patterns):');
    complexFiles.forEach(f => {
      console.log(`   - ${f.path} (${f.patterns.join(', ')})`);
    });
  }
}

main().catch(console.error);