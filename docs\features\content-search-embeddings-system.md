# BookScribe Content Search & Embeddings System

## Overview

The Content Search & Embeddings System provides advanced semantic search capabilities across all project content using AI-powered embeddings. It enables users to find content based on meaning, emotion, themes, and context rather than just keyword matching, making it invaluable for maintaining consistency and discovering connections in large projects.

## Architecture

### Database Schema

#### Content Embeddings Table
Stores vector embeddings for semantic search:

```sql
content_embeddings:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects
  - chapter_id: UUID - References chapters (optional)
  - content_type: TEXT - chapter, character, location, scene, dialogue, description, plot_point, theme, custom
  - content_id: UUID - ID of the source content
  - content_text: TEXT - Original text content
  - content_summary: TEXT - AI-generated summary
  - embedding: vector(1536) - OpenAI embeddings vector
  - metadata: JSONB - Additional searchable data
  - search_tags: TEXT[] - Extracted tags
  - emotional_tone: JSONB - Emotion analysis
  - themes: TEXT[] - Identified themes
  - importance_score: FLOAT - Content significance (0-1)
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
  - UNIQUE(content_type, content_id)
```

#### Search History Table
Tracks user searches for analytics:

```sql
search_history:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects
  - search_type: VARCHAR(50) - semantic, emotion, theme, character
  - query: TEXT - Search query
  - filters: JSONB - Applied filters
  - results_count: INTEGER
  - clicked_results: UUID[] - Results user clicked
  - search_duration_ms: INTEGER
  - created_at: TIMESTAMPTZ
```

## Embedding Generation

### Content Processing Pipeline
```typescript
interface EmbeddingPipeline {
  // 1. Extract content chunks
  extractChunks(content: string): ContentChunk[];
  
  // 2. Generate embeddings
  generateEmbeddings(chunks: ContentChunk[]): Promise<number[][]>;
  
  // 3. Analyze content
  analyzeContent(text: string): Promise<{
    summary: string;
    emotions: EmotionAnalysis;
    themes: string[];
    entities: Entity[];
  }>;
  
  // 4. Store embeddings
  storeEmbeddings(embeddings: EmbeddingData[]): Promise<void>;
}
```

### Chunk Strategy
```typescript
interface ChunkingStrategy {
  maxTokens: number; // 1000 tokens per chunk
  overlap: number; // 100 token overlap
  boundaries: 'sentence' | 'paragraph' | 'scene';
  preserveContext: boolean;
}
```

## Search Types

### 1. Semantic Search
Find content by meaning:

```typescript
interface SemanticSearch {
  query: string;
  similarity_threshold: number; // 0-1, default 0.7
  content_types?: ContentType[];
  limit?: number;
  include_context?: boolean;
}

// Example: "scenes where characters feel betrayed"
// Returns scenes with betrayal themes regardless of exact wording
```

### 2. Emotion Search
Find content by emotional tone:

```typescript
interface EmotionSearch {
  emotions: {
    joy?: number;
    sadness?: number;
    anger?: number;
    fear?: number;
    surprise?: number;
    disgust?: number;
  };
  intensity_threshold: number;
  combination: 'any' | 'all';
}

// Example: Find high-tension scenes (fear > 0.7, anger > 0.5)
```

### 3. Theme Search
Find content by thematic elements:

```typescript
interface ThemeSearch {
  themes: string[];
  match_mode: 'any' | 'all' | 'exact';
  include_related: boolean;
}

// Example: Find all content about "redemption" and "sacrifice"
```

### 4. Character Moment Search
Find specific character interactions:

```typescript
interface CharacterMomentSearch {
  characters: string[];
  interaction_type?: 'dialogue' | 'conflict' | 'romance' | 'any';
  emotional_context?: EmotionFilter;
}

// Example: Find all romantic moments between Elena and Marcus
```

## API Endpoints

### Search Endpoints

#### POST /api/search/semantic
Semantic similarity search:

```typescript
// Request
{
  query: "a character discovering their true heritage",
  project_id: "uuid",
  content_types: ["chapter", "scene"],
  limit: 20,
  filters: {
    chapters: [5, 6, 7],
    min_similarity: 0.75
  }
}

// Response
{
  results: [
    {
      id: "uuid",
      content_type: "scene",
      content_id: "uuid",
      chapter_id: "uuid",
      chapter_number: 6,
      text_preview: "Elena's hands trembled as she read the letter...",
      similarity_score: 0.89,
      context: {
        before: "The dusty attic held many secrets...",
        after: "Everything she believed was a lie..."
      },
      metadata: {
        characters: ["Elena"],
        location: "Attic",
        emotional_tone: { "surprise": 0.9, "fear": 0.6 }
      }
    }
  ],
  total_results: 5,
  search_id: "uuid"
}
```

#### POST /api/search/emotion
Search by emotional content:

```typescript
// Request
{
  project_id: "uuid",
  emotions: {
    sadness: 0.8,
    fear: 0.6
  },
  combination: "any",
  content_types: ["dialogue", "description"]
}

// Response
{
  results: [
    {
      id: "uuid",
      text_preview: "Tears rolled down her cheeks as she realized...",
      emotional_scores: {
        sadness: 0.85,
        fear: 0.65,
        anger: 0.3
      },
      dominant_emotion: "sadness",
      intensity: "high"
    }
  ]
}
```

#### POST /api/search/theme
Search by thematic content:

```typescript
// Request
{
  project_id: "uuid",
  themes: ["betrayal", "redemption"],
  match_mode: "any",
  include_related: true
}

// Response
{
  results: [
    {
      id: "uuid",
      matched_themes: ["betrayal"],
      related_themes: ["trust", "deception"],
      theme_strength: 0.82,
      text_preview: "He couldn't believe his mentor would..."
    }
  ]
}
```

#### POST /api/search/character-moments
Find character interactions:

```typescript
// Request
{
  project_id: "uuid",
  characters: ["Elena", "Marcus"],
  interaction_type: "conflict",
  emotional_context: {
    min_intensity: 0.7
  }
}

// Response
{
  moments: [
    {
      id: "uuid",
      chapter: 8,
      characters_involved: ["Elena", "Marcus"],
      interaction_type: "conflict",
      dialogue_preview: [
        { character: "Elena", text: "How could you lie to me?" },
        { character: "Marcus", text: "I was trying to protect you!" }
      ],
      emotional_context: {
        tension: 0.9,
        anger: 0.8,
        hurt: 0.7
      }
    }
  ]
}
```

### Indexing Endpoints

#### POST /api/search/index
Trigger content indexing:

```typescript
// Request
{
  project_id: "uuid",
  content_types: ["chapter", "character"],
  force_reindex: false
}

// Response
{
  indexed: {
    chapters: 25,
    characters: 15,
    locations: 0
  },
  embeddings_created: 450,
  processing_time_ms: 12500
}
```

## Search Features

### 1. Similarity Search Functions
Database functions for vector search:

```sql
-- Find similar content using cosine similarity
CREATE OR REPLACE FUNCTION search_similar_content(
  p_project_id UUID,
  p_query_embedding vector(1536),
  p_content_types TEXT[] DEFAULT NULL,
  p_limit INTEGER DEFAULT 10,
  p_threshold FLOAT DEFAULT 0.7
)
RETURNS TABLE (
  content_id UUID,
  content_type TEXT,
  similarity FLOAT,
  content_text TEXT,
  metadata JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ce.content_id,
    ce.content_type,
    1 - (ce.embedding <=> p_query_embedding) AS similarity,
    ce.content_text,
    ce.metadata
  FROM content_embeddings ce
  WHERE ce.project_id = p_project_id
    AND (p_content_types IS NULL OR ce.content_type = ANY(p_content_types))
    AND 1 - (ce.embedding <=> p_query_embedding) >= p_threshold
  ORDER BY ce.embedding <=> p_query_embedding
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;
```

### 2. Multi-modal Search
Combine different search types:

```typescript
interface MultiModalSearch {
  semantic_query?: string;
  emotion_filters?: EmotionFilter;
  theme_filters?: string[];
  character_filters?: string[];
  combine_mode: 'intersect' | 'union';
  weight_distribution: {
    semantic: number;
    emotion: number;
    theme: number;
  };
}
```

### 3. Search Refinement
Iterative search refinement:

```typescript
interface SearchRefinement {
  initial_search_id: string;
  refine_by: {
    more_like_this?: string[];
    less_like_this?: string[];
    adjust_emotions?: EmotionAdjustment;
    narrow_themes?: string[];
  };
}
```

## UI Components

### Search Interface
```tsx
<ContentSearch
  projectId={projectId}
  searchTypes={['semantic', 'emotion', 'theme']}
  onSearch={handleSearch}
  showFilters={true}
  realtimePreview={true}
/>
```

### Search Results
```tsx
<SearchResults
  results={searchResults}
  viewMode="list" | "cards" | "timeline"
  onResultClick={handleResultClick}
  showContext={true}
  highlightMatches={true}
/>
```

### Search History
```tsx
<SearchHistory
  userId={userId}
  projectId={projectId}
  onRepeatSearch={handleRepeat}
  showSavedSearches={true}
/>
```

### Emotion Picker
```tsx
<EmotionSearchPicker
  onEmotionSelect={handleEmotionSelect}
  allowIntensity={true}
  presets={emotionPresets}
/>
```

## Performance Optimization

### Vector Indexing
```sql
-- HNSW index for fast similarity search
CREATE INDEX idx_content_embeddings_vector 
ON content_embeddings 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Partial indexes for common queries
CREATE INDEX idx_embeddings_chapters 
ON content_embeddings(project_id, content_type) 
WHERE content_type = 'chapter';
```

### Caching Strategy
- Cache frequent queries for 1 hour
- Pre-compute common searches
- Store query embeddings
- Background index updates

### Batch Processing
```typescript
interface BatchIndexing {
  batch_size: 100;
  parallel_workers: 4;
  rate_limit: 100; // requests per minute
  checkpoint_interval: 1000; // items
}
```

## Content Analysis

### Emotion Detection
```typescript
interface EmotionAnalysis {
  primary_emotion: string;
  emotion_scores: {
    [emotion: string]: number;
  };
  intensity: 'low' | 'medium' | 'high';
  valence: number; // -1 to 1 (negative to positive)
  arousal: number; // 0 to 1 (calm to excited)
}
```

### Theme Extraction
```typescript
interface ThemeExtraction {
  explicit_themes: string[]; // Directly mentioned
  implicit_themes: string[]; // Inferred from context
  theme_confidence: { [theme: string]: number };
  related_concepts: string[];
}
```

### Entity Recognition
```typescript
interface EntityRecognition {
  characters: CharacterMention[];
  locations: LocationMention[];
  objects: ObjectMention[];
  events: EventMention[];
  relationships: RelationshipMention[];
}
```

## Security & Privacy

### Data Protection
- Embeddings are user-specific
- No cross-project search
- Secure vector storage
- Encrypted at rest

### Access Control
```sql
-- RLS for embeddings
CREATE POLICY "Users can search own content" ON content_embeddings
  FOR SELECT USING (
    user_id = auth.uid() OR
    project_id IN (
      SELECT project_id FROM project_collaborators
      WHERE user_id = auth.uid() AND status = 'accepted'
    )
  );
```

## Future Enhancements

1. **Multimodal Search**
   - Image-based search
   - Audio transcription search
   - Handwriting recognition

2. **Advanced Analytics**
   - Search pattern analysis
   - Content discovery recommendations
   - Automatic tagging

3. **Cross-Project Search**
   - Search across series
   - Universe-wide search
   - Shared knowledge base

4. **AI Enhancements**
   - Question answering
   - Automatic summarization
   - Content suggestions

## Related Systems
- Story Bible System (content source)
- AI Agent System (embedding generation)
- Analytics System (search metrics)
- Memory Management (context optimization)