#!/usr/bin/env node
/**
 * Script to identify large functions that should be refactored
 * Run with: npx tsx scripts/analyze-function-sizes.ts
 */

import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';

interface LargeFunction {
  file: string;
  name: string;
  lineCount: number;
  startLine: number;
  endLine: number;
  type: 'function' | 'method' | 'arrow' | 'component';
}

const FUNCTION_SIZE_THRESHOLD = 50;
const EXCLUDED_PATHS = [
  'node_modules',
  '.next',
  'public',
  'tests',
  'scripts',
  'coverage',
  'dist',
  'build',
];

function shouldExcludeFile(filePath: string): boolean {
  const normalizedPath = filePath.replace(/\\/g, '/');
  return EXCLUDED_PATHS.some(pattern => normalizedPath.includes(pattern));
}

function getAllTypeScriptFiles(dir: string): string[] {
  const files: string[] = [];
  
  function traverse(currentPath: string) {
    if (shouldExcludeFile(currentPath)) return;
    
    try {
      const entries = fs.readdirSync(currentPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(currentPath, entry.name);
        
        if (entry.isDirectory()) {
          traverse(fullPath);
        } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentPath}:`, error);
    }
  }
  
  traverse(dir);
  return files;
}

function analyzeFunctionSizes(filePath: string): LargeFunction[] {
  const largeFunctions: LargeFunction[] = [];
  
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    const sourceFile = ts.createSourceFile(
      filePath,
      content,
      ts.ScriptTarget.Latest,
      true
    );
    
    const lines = content.split('\n');
    
    function visit(node: ts.Node) {
      let functionName = '';
      let functionType: LargeFunction['type'] = 'function';
      let isFunctionNode = false;
      
      if (ts.isFunctionDeclaration(node)) {
        functionName = node.name?.text || '<anonymous>';
        functionType = 'function';
        isFunctionNode = true;
      } else if (ts.isMethodDeclaration(node)) {
        functionName = node.name?.getText() || '<anonymous>';
        functionType = 'method';
        isFunctionNode = true;
      } else if (ts.isArrowFunction(node) || ts.isFunctionExpression(node)) {
        const parent = node.parent;
        if (ts.isVariableDeclaration(parent) && parent.name) {
          functionName = parent.name.getText();
        } else if (ts.isPropertyAssignment(parent) && parent.name) {
          functionName = parent.name.getText();
        } else {
          functionName = '<anonymous arrow>';
        }
        functionType = 'arrow';
        
        // Check if it's a React component
        if (functionName && /^[A-Z]/.test(functionName)) {
          functionType = 'component';
        }
        isFunctionNode = true;
      }
      
      if (isFunctionNode) {
        const start = sourceFile.getLineAndCharacterOfPosition(node.getStart());
        const end = sourceFile.getLineAndCharacterOfPosition(node.getEnd());
        const lineCount = end.line - start.line + 1;
        
        if (lineCount > FUNCTION_SIZE_THRESHOLD) {
          largeFunctions.push({
            file: filePath,
            name: functionName,
            lineCount,
            startLine: start.line + 1,
            endLine: end.line + 1,
            type: functionType,
          });
        }
      }
      
      ts.forEachChild(node, visit);
    }
    
    visit(sourceFile);
  } catch (error) {
    console.error(`Error analyzing ${filePath}:`, error);
  }
  
  return largeFunctions;
}

function generateReport(largeFunctions: LargeFunction[]) {
  const report: string[] = ['# Large Functions Analysis Report\n'];
  
  report.push(`Generated on: ${new Date().toISOString()}`);
  report.push(`Function size threshold: ${FUNCTION_SIZE_THRESHOLD} lines`);
  report.push(`Total large functions found: ${largeFunctions.length}\n`);
  
  // Group by file
  const byFile = new Map<string, LargeFunction[]>();
  for (const func of largeFunctions) {
    if (!byFile.has(func.file)) {
      byFile.set(func.file, []);
    }
    byFile.get(func.file)!.push(func);
  }
  
  // Sort files by number of large functions
  const sortedFiles = Array.from(byFile.entries()).sort((a, b) => b[1].length - a[1].length);
  
  report.push('## Files with Large Functions\n');
  
  for (const [file, functions] of sortedFiles) {
    const relativeFile = path.relative(process.cwd(), file);
    report.push(`### ${relativeFile} (${functions.length} large functions)\n`);
    
    const sortedFunctions = functions.sort((a, b) => b.lineCount - a.lineCount);
    
    for (const func of sortedFunctions) {
      const emoji = func.lineCount > 100 ? '🔴' : func.lineCount > 75 ? '🟡' : '🟢';
      report.push(`${emoji} **${func.name}** (${func.type})`);
      report.push(`   - Lines: ${func.lineCount} (${func.startLine}-${func.endLine})`);
      report.push('');
    }
  }
  
  // Statistics
  report.push('\n## Statistics\n');
  
  const stats = {
    components: largeFunctions.filter(f => f.type === 'component'),
    functions: largeFunctions.filter(f => f.type === 'function'),
    methods: largeFunctions.filter(f => f.type === 'method'),
    arrows: largeFunctions.filter(f => f.type === 'arrow'),
  };
  
  report.push('### By Function Type:');
  report.push(`- React Components: ${stats.components.length}`);
  report.push(`- Regular Functions: ${stats.functions.length}`);
  report.push(`- Class Methods: ${stats.methods.length}`);
  report.push(`- Arrow Functions: ${stats.arrows.length}`);
  
  const veryLarge = largeFunctions.filter(f => f.lineCount > 100);
  const large = largeFunctions.filter(f => f.lineCount > 75 && f.lineCount <= 100);
  const moderate = largeFunctions.filter(f => f.lineCount <= 75);
  
  report.push('\n### By Size:');
  report.push(`- 🔴 Very Large (>100 lines): ${veryLarge.length}`);
  report.push(`- 🟡 Large (76-100 lines): ${large.length}`);
  report.push(`- 🟢 Moderate (51-75 lines): ${moderate.length}`);
  
  // Top 10 largest functions
  report.push('\n## Top 10 Largest Functions\n');
  const top10 = largeFunctions.sort((a, b) => b.lineCount - a.lineCount).slice(0, 10);
  
  for (let i = 0; i < top10.length; i++) {
    const func = top10[i];
    const relativeFile = path.relative(process.cwd(), func.file);
    report.push(`${i + 1}. **${func.name}** - ${func.lineCount} lines`);
    report.push(`   - File: ${relativeFile}`);
    report.push(`   - Type: ${func.type}`);
    report.push(`   - Location: lines ${func.startLine}-${func.endLine}`);
    report.push('');
  }
  
  return report.join('\n');
}

function main() {
  console.log('📏 BookScribe Function Size Analyzer\n');
  
  const srcPath = path.join(process.cwd(), 'src');
  console.log('🔍 Scanning TypeScript files...');
  
  const files = getAllTypeScriptFiles(srcPath);
  console.log(`Found ${files.length} TypeScript files to analyze\n`);
  
  const allLargeFunctions: LargeFunction[] = [];
  
  let processed = 0;
  for (const file of files) {
    const largeFunctions = analyzeFunctionSizes(file);
    allLargeFunctions.push(...largeFunctions);
    
    processed++;
    if (processed % 50 === 0) {
      console.log(`Processed ${processed}/${files.length} files...`);
    }
  }
  
  console.log(`\n✅ Analysis complete!`);
  
  if (allLargeFunctions.length === 0) {
    console.log('No large functions found!');
    return;
  }
  
  const report = generateReport(allLargeFunctions);
  const reportPath = path.join(process.cwd(), 'large-functions-report.md');
  fs.writeFileSync(reportPath, report);
  
  console.log(`\n📊 Report generated: ${reportPath}`);
  
  // Print summary
  console.log('\n📈 Summary:');
  console.log(`   - Total large functions: ${allLargeFunctions.length}`);
  console.log(`   - Files with large functions: ${new Set(allLargeFunctions.map(f => f.file)).size}`);
  
  const veryLarge = allLargeFunctions.filter(f => f.lineCount > 100).length;
  const large = allLargeFunctions.filter(f => f.lineCount > 75 && f.lineCount <= 100).length;
  const moderate = allLargeFunctions.filter(f => f.lineCount <= 75).length;
  
  console.log(`\n   Size breakdown:`);
  console.log(`   - 🔴 Very Large (>100 lines): ${veryLarge}`);
  console.log(`   - 🟡 Large (76-100 lines): ${large}`);
  console.log(`   - 🟢 Moderate (51-75 lines): ${moderate}`);
  
  const largest = allLargeFunctions.sort((a, b) => b.lineCount - a.lineCount)[0];
  if (largest) {
    console.log(`\n🏆 Largest function: ${largest.name} (${largest.lineCount} lines)`);
    console.log(`   in ${path.relative(process.cwd(), largest.file)}`);
  }
}

main();