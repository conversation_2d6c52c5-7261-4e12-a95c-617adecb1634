/**
 * Centralized localStorage and sessionStorage key definitions
 * Prevents key collisions and provides type-safe access
 */

export const STORAGE_KEYS = {
  // Authentication related keys
  AUTH: {
    REMEMBERED_EMAIL: 'bookscribe_remembered_email',
    REMEMBER_ME: 'bookscribe_remember_me',
    SESSION_TOKEN: 'bookscribe_session_token',
    REFRESH_TOKEN: 'bookscribe_refresh_token',
  },
  
  // User preferences and settings
  PREFERENCES: {
    THEME: 'bookscribe_theme',
    EDITOR_SETTINGS: 'bookscribe_editor_settings',
    PANEL_LAYOUT: 'bookscribe_panel_layout',
    RECENTLY_VIEWED: 'bookscribe_recently_viewed',
  },
  
  // Onboarding and tutorials
  ONBOARDING: {
    SEEN: (userId: string) => `bookscribe_onboarding_seen_${userId}`,
    PROGRESS: (userId: string) => `bookscribe_onboarding_progress_${userId}`,
    SKIPPED: 'bookscribe_onboarding_skipped',
  },
  
  // Voice and writing profiles
  VOICE: {
    PROFILES: 'bookscribe_voice_profiles',
    ACTIVE_PROFILE: 'bookscribe_active_voice_profile',
    TRAINING_DATA: 'bookscribe_voice_training_data',
  },
  
  // Feature flags and tooltips
  FEATURES: {
    TOOLTIPS_SEEN: 'bookscribe_feature_tooltips_seen',
    ANNOUNCEMENTS_READ: 'bookscribe_announcements_read',
    BETA_FEATURES: 'bookscribe_beta_features_enabled',
  },
  
  // Project and writing session data
  PROJECT: {
    DRAFT: (projectId: string) => `bookscribe_draft_${projectId}`,
    AUTO_SAVE: (projectId: string) => `bookscribe_autosave_${projectId}`,
    LAST_POSITION: (projectId: string) => `bookscribe_last_position_${projectId}`,
  },
  
  // Analytics and tracking
  ANALYTICS: {
    SESSION_ID: 'bookscribe_session_id',
    LAST_ACTIVITY: 'bookscribe_last_activity',
    USAGE_STATS: 'bookscribe_usage_stats',
  },
  
  // Collaboration
  COLLABORATION: {
    CURSOR_COLOR: 'bookscribe_cursor_color',
    USER_PRESENCE: 'bookscribe_user_presence',
    DRAFT_COMMENTS: 'bookscribe_draft_comments',
  },
  
  // Export and download
  EXPORT: {
    PREFERENCES: 'bookscribe_export_preferences',
    HISTORY: 'bookscribe_export_history',
  },
} as const

// Type-safe storage utility functions
export const storage = {
  get: (key: string): string | null => {
    try {
      return localStorage.getItem(key)
    } catch {
      return null
    }
  },
  
  set: (key: string, value: string): void => {
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
    }
  },
  
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Failed to remove from localStorage:', error)
    }
  },
  
  getJSON: <T>(key: string): T | null => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch {
      return null
    }
  },
  
  setJSON: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Failed to save JSON to localStorage:', error)
    }
  },
}

// Session storage utilities
export const sessionStorage = {
  get: (key: string): string | null => {
    try {
      return window.sessionStorage.getItem(key)
    } catch {
      return null
    }
  },
  
  set: (key: string, value: string): void => {
    try {
      window.sessionStorage.setItem(key, value)
    } catch (error) {
      console.error('Failed to save to sessionStorage:', error)
    }
  },
  
  remove: (key: string): void => {
    try {
      window.sessionStorage.removeItem(key)
    } catch (error) {
      console.error('Failed to remove from sessionStorage:', error)
    }
  },
}