# Missing Database Tables Report

## Tables Referenced in Code but Missing from Migrations

After analyzing the entire codebase and comparing with existing migration files, here are the database tables that are referenced in the code but do not appear to be created in any migration:

### 1. **ai_suggestion_feedback**
- Referenced in: Multiple API routes and services
- Purpose: Stores user feedback on AI suggestions
- Missing migration for this table

### 2. **ai_usage_daily**
- Referenced in: Analytics and usage tracking services
- Purpose: Daily aggregated AI usage statistics
- Missing migration for this table

### 3. **document_locks**
- Referenced in: Collaboration services
- Purpose: Manages document locking for concurrent editing
- Missing migration for this table

### 4. **editing_sessions**
- Referenced in: Analytics and tracking services
- Purpose: Tracks user editing sessions
- Missing migration for this table

### 5. **knowledge_items**
- Referenced in: Knowledge base and context management
- Purpose: Stores knowledge base items for projects
- Missing migration for this table

### 6. **notes**
- Referenced in: Project management components
- Purpose: User notes associated with projects
- Missing migration for this table

### 7. **profiles**
- Referenced in: User management and authentication
- Purpose: Extended user profile information
- Note: This might be handled by Supabase auth.users, but many components expect a separate profiles table

### 8. **project_snapshots**
- Referenced in: Version control and backup services
- Purpose: Project state snapshots for versioning
- Missing migration for this table

### 9. **quality_reports**
- Referenced in: Quality analysis services
- Purpose: Stores quality analysis reports
- Missing migration for this table

### 10. **story_bible**
- Referenced in: Story bible components and APIs
- Purpose: Story bible entries (note: story_bibles table exists, but code references both)
- May need to update code to use consistent table name

### 11. **story_bible_sections**
- Referenced in: Story bible organization
- Purpose: Sections within story bibles
- Missing migration for this table

### 12. **usage_events**
- Referenced in: Analytics and tracking
- Purpose: General usage event tracking
- Missing migration for this table

### 13. **usage_tracking**
- Referenced in: Analytics services
- Purpose: General usage tracking
- Note: May be redundant with other tracking tables

### 14. **user_profiles**
- Referenced in: User settings and preferences
- Purpose: User profile settings
- Missing migration for this table

### 15. **user_subscriptions**
- Referenced in: Subscription management
- Purpose: User subscription information
- Note: stripe_subscriptions exists, but code expects user_subscriptions

### 16. **users**
- Referenced in: Various components
- Purpose: User information
- Note: This is typically auth.users in Supabase, code may need updating

### 17. **project-files**
- Referenced in: File management
- Purpose: Project file storage metadata
- Missing migration for this table

## Tables with Potential Issues

### 1. **achievements** table structure mismatch
- The API expects columns: `title`, `description`, `category`, `tier`, `icon`, `target_value`
- The migration has columns: `name` (not `title`), and doesn't have `target_value`
- This will cause the achievements feature to fail

### 2. **Table naming inconsistencies**
- `story_bible` vs `story_bibles` - code uses both names
- Migration creates `story_bibles` but some code references `story_bible`

## Foreign Key Relationships Missing

Several tables reference foreign keys to tables that don't exist:
- References to `profiles(id)` when profiles table doesn't exist
- References to `users(id)` instead of `auth.users(id)`

## Recommendations

1. **Critical Tables to Create First:**
   - `profiles` - Many features depend on this
   - `ai_suggestion_feedback` - AI features won't work properly without it
   - `user_subscriptions` or update code to use `stripe_subscriptions`
   - `knowledge_items` - Knowledge base features won't work

2. **Fix Achievement Table Schema:**
   - Update the achievements table to match what the API expects
   - Add missing columns: `title` (rename from `name`), `target_value`

3. **Standardize Table References:**
   - Update code to consistently use `auth.users` instead of `users`
   - Standardize on either `story_bible` or `story_bibles`

4. **Create Migration for Analytics Tables:**
   - `ai_usage_daily`
   - `usage_events`
   - `usage_tracking`
   - `editing_sessions`

5. **Create Migration for Collaboration Tables:**
   - `document_locks`

6. **Create Migration for Project Management Tables:**
   - `notes`
   - `project_snapshots`
   - `knowledge_items`
   - `story_bible_sections`