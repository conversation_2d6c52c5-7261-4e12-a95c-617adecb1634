import { describe, it, expect, beforeEach } from '@jest/globals';
import { performance } from 'perf_hooks';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { StoryArchitect } from '@/lib/agents/story-architect';
import { CharacterDeveloper } from '@/lib/agents/character-developer';
import { ChapterPlanner } from '@/lib/agents/chapter-planner';
import { WritingAgent } from '@/lib/agents/writing-agent';

// Performance thresholds (in milliseconds)
const PERFORMANCE_THRESHOLDS = {
  agentInitialization: 100,
  taskCreation: 50,
  taskExecution: 5000,
  memoryUsage: 100 * 1024 * 1024, // 100MB
  concurrentTasks: 3000,
};

describe('Agent Orchestration Performance', () => {
  let orchestrator: AdvancedAgentOrchestrator;
  let mockContext: any;

  beforeEach(() => {
    orchestrator = new AdvancedAgentOrchestrator();
    mockContext = {
      projectId: 'perf-test-project',
      settings: {
        primaryGenre: 'fantasy',
        targetAudience: 'adult',
      },
      targetWordCount: 100000,
      targetChapters: 20,
    };
  });

  describe('Agent Initialization Performance', () => {
    it('should initialize agents within threshold', async () => {
      const agents = [
        StoryArchitect,
        CharacterDeveloper,
        ChapterPlanner,
        WritingAgent,
      ];

      for (const AgentClass of agents) {
        const start = performance.now();
        const agent = new AgentClass();
        const end = performance.now();

        const duration = end - start;
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.agentInitialization);
        
        console.log(`${agent.name} initialization: ${duration.toFixed(2)}ms`);
      }
    });
  });

  describe('Task Creation Performance', () => {
    it('should create tasks quickly', async () => {
      const taskCounts = [10, 50, 100, 500];

      for (const count of taskCounts) {
        const start = performance.now();
        
        const tasks = Array(count).fill(null).map((_, i) => ({
          id: `task-${i}`,
          agentId: 'test-agent',
          type: 'test' as const,
          input: { index: i },
          dependencies: i > 0 ? [`task-${i - 1}`] : [],
        }));

        orchestrator.createTaskPipeline(tasks);
        
        const end = performance.now();
        const duration = end - start;
        const perTask = duration / count;

        expect(perTask).toBeLessThan(PERFORMANCE_THRESHOLDS.taskCreation / count);
        
        console.log(`Creating ${count} tasks: ${duration.toFixed(2)}ms (${perTask.toFixed(2)}ms per task)`);
      }
    });
  });

  describe('Concurrent Task Execution', () => {
    it('should handle concurrent tasks efficiently', async () => {
      const mockAgent = {
        execute: jest.fn().mockImplementation(async () => {
          // Simulate work
          await new Promise(resolve => setTimeout(resolve, 100));
          return { success: true };
        }),
      };

      orchestrator.registerAgent('mock-agent', mockAgent as any);

      const concurrentCounts = [3, 5, 10];

      for (const count of concurrentCounts) {
        const tasks = Array(count).fill(null).map((_, i) => ({
          id: `concurrent-${i}`,
          agentId: 'mock-agent',
          type: 'test' as const,
          input: { index: i },
          dependencies: [],
        }));

        const start = performance.now();
        orchestrator.createTaskPipeline(tasks);
        await orchestrator.execute();
        const end = performance.now();

        const duration = end - start;
        
        // Should execute concurrently, so total time should be close to single task time
        expect(duration).toBeLessThan(200 * Math.ceil(count / 3)); // Max 3 concurrent
        
        console.log(`Executing ${count} concurrent tasks: ${duration.toFixed(2)}ms`);
      }
    });
  });

  describe('Memory Usage', () => {
    it('should maintain reasonable memory usage', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Create a large pipeline
      const tasks = Array(1000).fill(null).map((_, i) => ({
        id: `memory-test-${i}`,
        agentId: 'test-agent',
        type: 'test' as const,
        input: {
          data: 'x'.repeat(10000), // 10KB per task
        },
        dependencies: [],
      }));

      orchestrator.createTaskPipeline(tasks);

      const afterCreation = process.memoryUsage().heapUsed;
      const memoryIncrease = afterCreation - initialMemory;

      expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.memoryUsage);
      
      console.log(`Memory usage for 1000 tasks: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);

      // Clean up
      global.gc?.();
    });
  });

  describe('Event Emission Performance', () => {
    it('should emit events without significant overhead', async () => {
      const eventCounts = [100, 1000, 10000];
      const listeners = 10;

      for (const count of eventCounts) {
        const orchestrator = new AdvancedAgentOrchestrator();
        
        // Add multiple listeners
        const callbacks = Array(listeners).fill(null).map(() => jest.fn());
        callbacks.forEach(cb => orchestrator.on('progress', cb));

        const start = performance.now();
        
        // Emit many events
        for (let i = 0; i < count; i++) {
          orchestrator['emit']('progress', {
            completedTasks: i,
            totalTasks: count,
            percentage: (i / count) * 100,
          });
        }

        const end = performance.now();
        const duration = end - start;
        const perEvent = duration / count;

        expect(perEvent).toBeLessThan(0.1); // Less than 0.1ms per event
        
        console.log(`Emitting ${count} events to ${listeners} listeners: ${duration.toFixed(2)}ms`);
      }
    });
  });

  describe('Complex Pipeline Performance', () => {
    it('should handle complex dependency graphs efficiently', async () => {
      // Create a diamond dependency pattern
      const complexTasks = [
        { id: 'root', agentId: 'test', type: 'test' as const, input: {}, dependencies: [] },
        { id: 'a1', agentId: 'test', type: 'test' as const, input: {}, dependencies: ['root'] },
        { id: 'a2', agentId: 'test', type: 'test' as const, input: {}, dependencies: ['root'] },
        { id: 'b1', agentId: 'test', type: 'test' as const, input: {}, dependencies: ['a1', 'a2'] },
        { id: 'b2', agentId: 'test', type: 'test' as const, input: {}, dependencies: ['a1', 'a2'] },
        { id: 'c', agentId: 'test', type: 'test' as const, input: {}, dependencies: ['b1', 'b2'] },
      ];

      const start = performance.now();
      orchestrator.createTaskPipeline(complexTasks);
      const createTime = performance.now() - start;

      expect(createTime).toBeLessThan(50);
      
      console.log(`Complex pipeline creation: ${createTime.toFixed(2)}ms`);
    });
  });

  describe('Real-world Scenario Performance', () => {
    it('should complete a full novel generation pipeline within reasonable time', async () => {
      const mockAgents = {
        'story-architect': {
          execute: jest.fn().mockResolvedValue({
            storyStructure: { acts: [], themes: [] },
          }),
        },
        'character-developer': {
          execute: jest.fn().mockResolvedValue({
            characters: Array(10).fill({ name: 'Character', profile: {} }),
          }),
        },
        'chapter-planner': {
          execute: jest.fn().mockResolvedValue({
            chapters: Array(20).fill({ title: 'Chapter', outline: '' }),
          }),
        },
        'writing-agent': {
          execute: jest.fn().mockResolvedValue({
            content: 'Chapter content...',
          }),
        },
      };

      Object.entries(mockAgents).forEach(([id, agent]) => {
        orchestrator.registerAgent(id, agent as any);
      });

      // Real-world pipeline
      const novelTasks = [
        {
          id: 'story',
          agentId: 'story-architect',
          type: 'story-structure' as const,
          input: mockContext,
          dependencies: [],
        },
        {
          id: 'characters',
          agentId: 'character-developer',
          type: 'characters' as const,
          input: mockContext,
          dependencies: ['story'],
        },
        {
          id: 'chapters',
          agentId: 'chapter-planner',
          type: 'chapters' as const,
          input: mockContext,
          dependencies: ['story', 'characters'],
        },
        ...Array(20).fill(null).map((_, i) => ({
          id: `write-chapter-${i}`,
          agentId: 'writing-agent',
          type: 'chapter' as const,
          input: { chapterIndex: i },
          dependencies: ['chapters'],
        })),
      ];

      const start = performance.now();
      orchestrator.createTaskPipeline(novelTasks);
      await orchestrator.execute();
      const end = performance.now();

      const duration = end - start;
      
      // Should complete within 10 seconds for mocked execution
      expect(duration).toBeLessThan(10000);
      
      console.log(`Full novel pipeline (${novelTasks.length} tasks): ${duration.toFixed(2)}ms`);
    });
  });

  describe('Performance Under Load', () => {
    it('should maintain performance with many active pipelines', async () => {
      const pipelineCount = 10;
      const orchestrators = Array(pipelineCount).fill(null).map(() => 
        new AdvancedAgentOrchestrator()
      );

      const mockAgent = {
        execute: jest.fn().mockResolvedValue({ success: true }),
      };

      orchestrators.forEach(o => o.registerAgent('test', mockAgent as any));

      const start = performance.now();

      const promises = orchestrators.map((o, i) => {
        const tasks = Array(10).fill(null).map((_, j) => ({
          id: `pipeline-${i}-task-${j}`,
          agentId: 'test',
          type: 'test' as const,
          input: {},
          dependencies: j > 0 ? [`pipeline-${i}-task-${j - 1}`] : [],
        }));

        o.createTaskPipeline(tasks);
        return o.execute();
      });

      await Promise.all(promises);
      const end = performance.now();

      const duration = end - start;
      const perPipeline = duration / pipelineCount;

      console.log(`${pipelineCount} parallel pipelines: ${duration.toFixed(2)}ms (${perPipeline.toFixed(2)}ms per pipeline)`);
      
      expect(perPipeline).toBeLessThan(1000);
    });
  });
});