'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BookOpen, 
  Users, 
  TrendingUp, 
  AlertTriangle,
  Eye,
  Brain,
  Target
} from 'lucide-react';
import { 
  RadialBar<PERSON>hart, 
  RadialBar, 
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from 'recharts';
import { useToast } from '@/hooks/use-toast';
import type { ReadabilityData } from '@/lib/types/analysis';
import { SPACING, ICON_SIZES } from '@/lib/config/ui-config';

interface ReadabilityScorerProps {
  readabilityData: ReadabilityData;
  projectId: string;
}

export function ReadabilityScorer({ readabilityData, projectId }: ReadabilityScorerProps) {
  const [showDetailedReport, setShowDetailedReport] = useState(false);
  const [isTrackingProgress, setIsTrackingProgress] = useState(false);
  const [isApplyingFixes, setIsApplyingFixes] = useState(false);
  const { toast } = useToast();

  // Helper to map suggestion types to issue types for auto-fix API
  const getIssueType = (suggestionType: string): 'sentence_length' | 'complex_words' | 'passive_voice' | 'readability' => {
    const typeMap: Record<string, 'sentence_length' | 'complex_words' | 'passive_voice' | 'readability'> = {
      'sentence-structure': 'sentence_length',
      'vocabulary': 'complex_words',
      'passive-voice': 'passive_voice',
      'readability': 'readability'
    };
    return typeMap[suggestionType] || 'readability';
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'hsl(var(--color-success))'; // Green
    if (score >= 60) return 'hsl(var(--color-warning))'; // Yellow  
    if (score >= 40) return 'hsl(38 92% 60%)'; // Orange
    return 'hsl(var(--color-error))'; // Red
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Improvement';
  };

  const getGradeLevelColor = (level: number, target: string) => {
    const targetGrades = {
      'children_8_12': [3, 6],
      'young_adult_13_17': [7, 10],
      'new_adult_18_25': [10, 12],
      'adult_25_plus': [12, 16],
      'all_ages': [5, 8]
    } as const;

    type TargetKey = keyof typeof targetGrades;
    const range = (target in targetGrades) 
      ? targetGrades[target as TargetKey] 
      : [8, 12];
    if (level >= range[0] && level <= range[1]) return 'hsl(var(--color-success))';
    if (level < range[0] - 2 || level > range[1] + 2) return 'hsl(var(--color-error))';
    return 'hsl(var(--color-warning))';
  };

  const readabilityRadialData = [
    {
      name: 'Readability',
      value: readabilityData.score,
      fill: getScoreColor(readabilityData.score),
    }
  ];

  const audienceData = [
    { audience: 'Children', suitability: readabilityData.audienceAnalysis.childrenFriendly },
    { audience: 'Teens', suitability: readabilityData.audienceAnalysis.teenAppropriate },
    { audience: 'Adults', suitability: readabilityData.audienceAnalysis.adultLevel },
  ];

  const metricsData = [
    { 
      metric: 'Sentence Length', 
      value: readabilityData.metrics.avgSentenceLength,
      ideal: 15,
      unit: 'words'
    },
    { 
      metric: 'Complex Words', 
      value: readabilityData.metrics.complexWords,
      ideal: 15,
      unit: '%'
    },
    { 
      metric: 'Syllables/Word', 
      value: readabilityData.metrics.syllablesPerWord,
      ideal: 1.5,
      unit: 'avg'
    },
    { 
      metric: 'Passive Voice', 
      value: readabilityData.metrics.passiveVoice,
      ideal: 10,
      unit: '%'
    },
  ];

  return (
    <div className={SPACING.SPACE_Y.LG}
      {/* Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <BookOpen className={`${ICON_SIZES.MD} ${SPACING.GAP.SM.replace('gap-', 'mr-')}`} />
              Readability Analysis
            </span>
            <div className={`flex items-center ${SPACING.GAP.SM}`}
              <Badge 
                style={{ 
                  backgroundColor: getScoreColor(readabilityData.score) + '20',
                  color: getScoreColor(readabilityData.score)
                }}
              >
                {readabilityData.score}% - {getScoreLabel(readabilityData.score)}
              </Badge>
              {!readabilityData.ageAppropriate && (
                <Badge variant="destructive">
                  <AlertTriangle className={`${ICON_SIZES.XS} ${SPACING.GAP.XS.replace('gap-', 'mr-')}`} />
                  Age Mismatch
                </Badge>
              )}
            </div>
          </CardTitle>
          <CardDescription>
            Age-appropriate language analysis and reading complexity assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className={`grid grid-cols-1 md:grid-cols-2 ${SPACING.GAP.LG}`}>
            {/* Readability Score Radial */}
            <div className={SPACING.SPACE_Y.MD}
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <RadialBarChart cx="50%" cy="50%" innerRadius="40%" outerRadius="80%" data={readabilityRadialData}>
                    <RadialBar dataKey="value" cornerRadius={10} fill={getScoreColor(readabilityData.score)} />
                  </RadialBarChart>
                </ResponsiveContainer>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold" style={{ color: getScoreColor(readabilityData.score) }}>
                  {readabilityData.score}%
                </div>
                <div className="text-sm text-slate-600">Readability Score</div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="text-lg font-bold" style={{ color: getGradeLevelColor(readabilityData.gradeLevel, readabilityData.comparison.targetAudience || 'general') }}>
                    {readabilityData.gradeLevel.toFixed(1)}
                  </div>
                  <div className="text-xs text-slate-600">Grade Level</div>
                </div>
                <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="text-lg font-bold text-info">
                    {readabilityData.metrics.readingEase.toFixed(0)}
                  </div>
                  <div className="text-xs text-slate-600">Reading Ease</div>
                </div>
                <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="text-lg font-bold text-success">
                    {readabilityData.metrics.avgSentenceLength.toFixed(1)}
                  </div>
                  <div className="text-xs text-slate-600">Avg Sentence</div>
                </div>
                <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="text-lg font-bold text-warning">
                    {readabilityData.metrics.complexWords.toFixed(1)}%
                  </div>
                  <div className="text-xs text-slate-600">Complex Words</div>
                </div>
              </div>

              {/* Age Appropriateness Alert */}
              {!readabilityData.ageAppropriate && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Age Mismatch:</strong> Content complexity doesn&apos;t match target audience. 
                    Current grade level: {readabilityData.gradeLevel.toFixed(1)}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audience Suitability */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Audience Suitability
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={audienceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="audience" />
                <YAxis domain={[0, 100]} />
                <Tooltip
                  formatter={(value: number | string | undefined) => [`${value}%`, 'Suitability']}
                />
                <Bar 
                  dataKey="suitability" 
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div className="mt-4 p-3 bg-info-light dark:bg-blue-950/20 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Target Audience Benchmark:</span>
              <Badge variant="outline">
                {readabilityData.comparison.benchmarkScore}% expected
              </Badge>
            </div>
            <div className="mt-2">
              <div className="text-xs text-slate-600">
                Your content is {Math.abs(readabilityData.comparison.deviation || 0)}% 
                {(readabilityData.comparison.deviation || 0) > 0 ? ' above' : ' below'} the target complexity
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="w-5 h-5 mr-2" />
            Readability Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metricsData.map((metric, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{metric.metric}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{(metric.value || 0).toFixed(1)} {metric.unit}</span>
                    <Badge 
                      variant={Math.abs((metric.value || 0) - metric.ideal) <= metric.ideal * 0.2 ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      Target: {metric.ideal} {metric.unit}
                    </Badge>
                  </div>
                </div>
                <Progress 
                  value={Math.min(((metric.value || 0) / (metric.ideal * 2)) * 100, 100)} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Improvement Suggestions */}
      {readabilityData.suggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Improvement Suggestions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {readabilityData.suggestions.map((suggestion, index) => (
                <div 
                  key={index}
                  className="p-4 border rounded-lg bg-slate-50 dark:bg-slate-800"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant={
                          suggestion.priority === 'high' ? 'destructive' :
                          suggestion.priority === 'medium' ? 'secondary' : 'outline'
                        }
                        className="text-xs"
                      >
                        {suggestion.type}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {suggestion.priority} priority
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-sm mb-2">{suggestion.description}</p>
                  
                  {suggestion.example && (
                    <div className="mb-2 p-2 bg-error-light dark:bg-red-950/20 rounded text-xs">
                      <strong>Example issue:</strong> &quot;{suggestion.example}&quot;
                    </div>
                  )}
                  
                  {suggestion.improvement && (
                    <div className="p-2 bg-success-light dark:bg-green-950/20 rounded text-xs">
                      <strong>Suggested improvement:</strong> &quot;{suggestion.improvement}&quot;
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                setShowDetailedReport(true);
                toast({
                  title: "Opening Detailed Report",
                  description: "Preparing comprehensive readability analysis...",
                });
              }}
            >
              <Eye className="w-3 h-3 mr-1" />
              View Detailed Report
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={async () => {
                setIsTrackingProgress(true);
                try {
                  // Save current readability metrics for progress tracking
                  const response = await fetch('/api/analysis/progress', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      projectId,
                      metrics: {
                        overallScore: readabilityData.overallScore,
                        fleschKincaid: readabilityData.metrics.fleschKincaid,
                        gunningFog: readabilityData.metrics.gunningFog,
                        colemanLiau: readabilityData.metrics.colemanLiau,
                        ari: readabilityData.metrics.ari,
                        sentences: readabilityData.metrics.sentences,
                        words: readabilityData.metrics.words,
                        syllables: readabilityData.metrics.syllables,
                        complexWords: readabilityData.metrics.complexWords,
                      },
                      timestamp: new Date().toISOString(),
                    }),
                  });

                  if (!response.ok) {
                    throw new Error('Failed to track progress');
                  }

                  toast({
                    title: "Progress Tracking Started",
                    description: "Your readability metrics will be tracked over time.",
                  });
                } catch (error) {
                  toast({
                    title: "Error",
                    description: "Failed to start progress tracking.",
                    variant: "destructive",
                  });
                } finally {
                  setIsTrackingProgress(false);
                }
              }}
              disabled={isTrackingProgress}
            >
              <TrendingUp className="w-3 h-3 mr-1" />
              {isTrackingProgress ? "Starting..." : "Track Progress"}
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={async () => {
                setIsApplyingFixes(true);
                try {
                  // Prepare issues data for the auto-fix API
                  const issues = readabilityData.suggestions.map(suggestion => ({
                    type: getIssueType(suggestion.type),
                    priority: suggestion.priority,
                    description: suggestion.description,
                    example: suggestion.example,
                  }));

                  // Get the current content (this would come from the editor in a real implementation)
                  const response = await fetch('/api/analysis/auto-fix', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      projectId,
                      content: readabilityData.comparison.currentContent || '',
                      issues,
                      targetAudience: readabilityData.comparison.targetAudience,
                      targetGradeLevel: readabilityData.gradeLevel,
                    }),
                  });

                  if (!response.ok) {
                    throw new Error('Failed to apply auto-fixes');
                  }

                  const result = await response.json();

                  toast({
                    title: "Auto-Fix Applied",
                    description: `Applied ${result.fixes.length} improvements to enhance readability.`,
                  });

                  // In a real implementation, this would update the editor content
                  // For now, we'll just log the improved content
                  console.log('Improved content:', result.improvedContent);
                  console.log('Fixes applied:', result.fixes);

                } catch (error) {
                  toast({
                    title: "Error",
                    description: "Failed to apply auto-fixes.",
                    variant: "destructive",
                  });
                } finally {
                  setIsApplyingFixes(false);
                }
              }}
              disabled={isApplyingFixes}
            >
              <Target className="w-3 h-3 mr-1" />
              {isApplyingFixes ? "Applying..." : "Apply Auto-Fixes"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}