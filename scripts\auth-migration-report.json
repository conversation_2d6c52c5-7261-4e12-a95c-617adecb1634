{"totalFiles": 64, "timestamp": "2025-07-30T02:46:36.364Z", "files": [{"path": "voice-profiles/route.ts", "patterns": ["requireAuth"], "hasImports": true}, {"path": "voice-profiles/[id]/route.ts", "patterns": ["withAuth wrapper"], "hasImports": true}, {"path": "version-history/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "version-history/[id]/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "timeline/validate/route.ts", "patterns": [], "hasImports": true}, {"path": "timeline/events/route.ts", "patterns": ["withProjectAccess wrapper"], "hasImports": true}, {"path": "timeline/autofix/route.ts", "patterns": [], "hasImports": true}, {"path": "services/orchestrator/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "services/health/route.ts", "patterns": ["authenticateAdmin"], "hasImports": false}, {"path": "services/content/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "series/[id]/universe/route.ts", "patterns": ["authenticateUserForSeries"], "hasImports": true}, {"path": "search/theme/route.ts", "patterns": [], "hasImports": true}, {"path": "search/semantic/route.ts", "patterns": ["authenticateUserForProject"], "hasImports": true}, {"path": "search/index/route.ts", "patterns": ["authenticateUserForProject"], "hasImports": true}, {"path": "search/emotion/route.ts", "patterns": [], "hasImports": false}, {"path": "search/character-moments/route.ts", "patterns": [], "hasImports": true}, {"path": "relationships/graph/route.ts", "patterns": [], "hasImports": true}, {"path": "relationships/analyze/route.ts", "patterns": ["authenticateUserForProject"], "hasImports": false}, {"path": "references/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "references/[id]/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "references/[id]/summarize/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "projects/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "projects/route.refactored.ts", "patterns": ["requireAuth"], "hasImports": false}, {"path": "projects/invite/route.ts", "patterns": ["requireAuth"], "hasImports": true}, {"path": "projects/[id]/route.ts", "patterns": ["withProjectAccess wrapper"], "hasImports": true}, {"path": "projects/[id]/team/route.ts", "patterns": ["withProjectAccess wrapper"], "hasImports": true}, {"path": "profiles/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "profiles/[id]/favorite/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "profiles/[id]/clone/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "orchestration/progress/route.ts", "patterns": ["authenticateUserForProject"], "hasImports": true}, {"path": "memory/stats/route.ts", "patterns": [], "hasImports": true}, {"path": "memory/merge/route.ts", "patterns": [], "hasImports": false}, {"path": "memory/compress/route.ts", "patterns": [], "hasImports": true}, {"path": "import/pdf/route.ts", "patterns": [], "hasImports": true}, {"path": "import/epub/route.ts", "patterns": [], "hasImports": false}, {"path": "import/docx/route.ts", "patterns": [], "hasImports": true}, {"path": "health/route.ts", "patterns": [], "hasImports": true}, {"path": "content-analysis/route.ts", "patterns": [], "hasImports": true}, {"path": "consistency/check/route.ts", "patterns": [], "hasImports": true}, {"path": "collaboration/unlock/route.ts", "patterns": [], "hasImports": false}, {"path": "collaboration/sessions/route.ts", "patterns": [], "hasImports": true}, {"path": "collaboration/leave/route.ts", "patterns": [], "hasImports": true}, {"path": "billing/subscriptions/route.ts", "patterns": ["requireAuth"], "hasImports": true}, {"path": "billing/payments/charge/route.ts", "patterns": [], "hasImports": true}, {"path": "analytics/sessions/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "analytics/selections/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "analytics/selections/success-patterns/route.ts", "patterns": [], "hasImports": false}, {"path": "analytics/recommendations/route.ts", "patterns": [], "hasImports": true}, {"path": "analytics/quality/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "analytics/profiles/performance/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "analytics/productivity/route.ts", "patterns": [], "hasImports": false}, {"path": "analytics/chapters/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "analytics/ai-usage/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "analysis/progress/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "analysis/book-summary/route.ts", "patterns": [], "hasImports": true}, {"path": "analysis/auto-fix/route.ts", "patterns": [], "hasImports": false}, {"path": "ai/typed-stream/route.ts", "patterns": [], "hasImports": true}, {"path": "ai/suggestions/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "ai/structured-content/route.ts", "patterns": [], "hasImports": true}, {"path": "ai/stream-content/route.ts", "patterns": ["withAuth wrapper"], "hasImports": true}, {"path": "agents/initialize/route.ts", "patterns": ["authenticateUser"], "hasImports": true}, {"path": "agents/generate/route.ts", "patterns": [], "hasImports": true}, {"path": "admin/export-data/route.ts", "patterns": [], "hasImports": true}, {"path": "achievements/check/route.ts", "patterns": [], "hasImports": true}]}