import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function seedWordCounts() {
  try {
    // Get a sample user
    const { data: users, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)

    if (userError || !users?.length) {
      console.error('No users found')
      return
    }

    const userId = users[0].id

    // Get user's projects
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, title')
      .eq('user_id', userId)
      .limit(3)

    if (projectError || !projects?.length) {
      console.error('No projects found for user')
      return
    }

    // Generate word counts for the last 90 days
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 90)

    const wordCounts = []

    for (const project of projects) {
      let currentDate = new Date(startDate)
      
      while (currentDate <= endDate) {
        // Generate realistic word counts
        // Skip some days randomly (writer's block!)
        if (Math.random() > 0.3) {
          // Generate between 200-3000 words per day
          const baseWords = Math.floor(Math.random() * 2000) + 200
          
          // Add some variation for weekends (lower)
          const dayOfWeek = currentDate.getDay()
          const weekendMultiplier = (dayOfWeek === 0 || dayOfWeek === 6) ? 0.6 : 1
          
          const wordCount = Math.floor(baseWords * weekendMultiplier)

          wordCounts.push({
            project_id: project.id,
            user_id: userId,
            date: currentDate.toISOString().split('T')[0],
            word_count: wordCount,
            session_count: Math.floor(Math.random() * 3) + 1
          })
        }

        currentDate.setDate(currentDate.getDate() + 1)
      }
    }

    // Insert word counts
    const { data, error } = await supabase
      .from('word_count_history')
      .upsert(wordCounts, { onConflict: 'project_id,date' })

    if (error) {
      console.error('Error inserting word counts:', error)
    } else {
      console.log(`Successfully seeded ${wordCounts.length} word count entries`)
    }

  } catch (error) {
    console.error('Seed error:', error)
  }
}

seedWordCounts()