import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'

export const GET = UnifiedAuthService.withSeriesAccess(
  async (request, { params }: { params: { id: string } }) => {
    try {
      const { searchParams } = new URL(request.url)
      const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString())
      const seriesId = params.id
      const user = request.user!

      const supabase = createClient()

    // Get all projects in the series
    const { data: seriesBooks, error: booksError } = await supabase
      .from('series_books')
      .select('project_id')
      .eq('series_id', seriesId)

    if (booksError) {
      logger.error('Error fetching series books:', booksError)
      throw booksError
    }

    const projectIds = seriesBooks?.map(sb => sb.project_id) || []
    
    if (projectIds.length === 0) {
      return UnifiedResponse.success({ wordCounts: [] })
    }

    // Get word count history for all projects in the series
    const startDate = new Date(year, 0, 1).toISOString()
    const endDate = new Date(year, 11, 31, 23, 59, 59).toISOString()

    const { data: wordCounts, error: countsError } = await supabase
      .from('word_count_history')
      .select(`
        date,
        word_count,
        project_id,
        projects!inner (
          id,
          title
        )
      `)
      .in('project_id', projectIds)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: true })

    if (countsError) {
      logger.error('Error fetching word counts:', countsError)
      throw countsError
    }

    // Transform the data
    const transformedCounts = wordCounts?.map(wc => ({
      date: wc.date,
      word_count: wc.word_count,
      project_id: wc.project_id,
      project_title: wc.projects?.title || 'Unknown Project'
    })) || []

    return UnifiedResponse.success({ 
      wordCounts: transformedCounts,
      year,
      seriesId 
    })

    } catch (error) {
      logger.error('Error in word counts API:', error)
      return UnifiedResponse.error({
        message: 'Failed to fetch word counts',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500)
    }
  }
)