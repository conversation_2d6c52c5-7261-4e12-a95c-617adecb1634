#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const glob = require('glob');

async function standardizeErrorHandling() {
  console.log('🔄 Starting error handling standardization...');
  
  // Find all TypeScript/TSX files in API routes
  const apiFiles = glob.sync('src/app/api/**/*.{ts,tsx}', {
    ignore: [
      '**/node_modules/**', 
      '**/dist/**', 
      '**/build/**'
    ]
  });
  
  let updatedCount = 0;
  const updates = [];
  
  for (const file of apiFiles) {
    try {
      let content = await fs.readFile(file, 'utf-8');
      let hasChanges = false;
      const originalContent = content;
      
      // Update error handler imports
      const importReplacements = [
        // From app/api/error-handler
        {
          from: /import\s*{[^}]+}\s*from\s*['"]@\/app\/api\/error-handler['"]/g,
          to: "import { handleAPIError, with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler'"
        },
        // From lib/services/error-handler (keep for AI-specific)
        {
          from: /import\s*{\s*handleApiError\s*}\s*from\s*['"]@\/lib\/services\/error-handler['"]/g,
          to: "import { handleAPIError } from '@/lib/api/error-handler'"
        },
        // Simple handleApiError to handleAPIError
        {
          from: /handleApiError/g,
          to: 'handleAPIError'
        }
      ];
      
      for (const replacement of importReplacements) {
        if (content.match(replacement.from)) {
          if (typeof replacement.to === 'string') {
            content = content.replace(replacement.from, replacement.to);
          } else {
            content = content.replace(replacement.from, replacement.to);
          }
          hasChanges = true;
        }
      }
      
      // Update error handling patterns
      const errorPatterns = [
        // NextResponse.json with error to handleAPIError
        {
          from: /return\s+NextResponse\.json\(\s*{\s*error:\s*['"][^'"]+['"]\s*},\s*{\s*status:\s*(\d+)\s*}\s*\)/g,
          to: (match, status) => {
            if (status === '401') return "return handleAPIError(new AuthenticationError())";
            if (status === '403') return "return handleAPIError(new AuthorizationError())";
            if (status === '404') return "return handleAPIError(new NotFoundError('Resource'))";
            if (status === '400') return "return handleAPIError(new ValidationError('Invalid request'))";
            return match; // Keep other statuses as-is for now
          }
        },
        // catch blocks that just return errors
        {
          from: /catch\s*\(error\)\s*{\s*return\s+NextResponse\.json\(/g,
          to: 'catch (error) {\n    return handleAPIError(error'
        }
      ];
      
      for (const pattern of errorPatterns) {
        if (content.match(pattern.from)) {
          content = content.replace(pattern.from, pattern.to);
          hasChanges = true;
        }
      }
      
      if (hasChanges && content !== originalContent) {
        // Add import if not present
        if (!content.includes('@/lib/api/error-handler')) {
          const importMatch = content.match(/^import .* from ['"].*['"];?$/m);
          if (importMatch) {
            const insertPos = importMatch.index + importMatch[0].length;
            content = content.slice(0, insertPos) + 
              "\nimport { handleAPIError } from '@/lib/api/error-handler';" +
              content.slice(insertPos);
          }
        }
        
        await fs.writeFile(file, content);
        updatedCount++;
        updates.push(file);
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  // Remove duplicate error handler files
  const filesToDelete = [
    'src/app/api/error-handler.ts'
  ];
  
  for (const file of filesToDelete) {
    try {
      await fs.unlink(file);
      console.log(`🗑️  Deleted duplicate: ${file}`);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        console.error(`❌ Error deleting ${file}:`, error.message);
      }
    }
  }
  
  console.log(`\n✅ Error handling standardization complete!`);
  console.log(`📊 Updated ${updatedCount} files out of ${apiFiles.length} API files`);
  
  if (updates.length > 0) {
    console.log('\n📝 Files updated:');
    updates.forEach(file => console.log(`  - ${file}`));
  }
}

// Run the standardization
standardizeErrorHandling().catch(console.error);