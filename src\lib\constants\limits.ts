/**
 * Character and size limits
 */
export const SIZE_LIMITS = {
  // Document size limits
  MAX_DOCUMENT_CHARS: 50000,      // 50k characters
  LARGE_DOCUMENT_THRESHOLD: 100000, // 100k characters
  HUGE_DOCUMENT_THRESHOLD: 500000,  // 500k characters
  
  // Performance thresholds
  SIZE_CHANGE_THRESHOLD: 5000,     // 5k character change threshold
  EMBEDDING_TEXT_LIMIT: 8000,      // 8k character limit for embeddings
  
  // Memory estimates
  CHAR_TO_KB_RATIO: 2,            // 2 bytes per character estimate
  KB_PER_MB: 1024,                // KB to MB conversion
  
  // File sizes
  MAX_FILE_SIZE_MB: 10,           // 10MB max file size
  MAX_FILE_SIZE_BYTES: 10485760,  // 10MB in bytes
} as const

/**
 * Token limits for AI models
 */
export const TOKEN_LIMITS = {
  GPT4_MAX: 32000,          // Conservative GPT-4 limit
  GPT4_DEFAULT: 4000,       // Default max tokens
  EMBEDDING_MAX: 8191,      // Max tokens for embeddings
  COMPLETION_DEFAULT: 1000, // Default completion tokens
  STREAM_CHUNK_SIZE: 100,   // Tokens per stream chunk
} as const

/**
 * Demo content values
 */
export const DEMO_VALUES = {
  // Word counts
  DAILY_WORD_COUNT: 1250,
  DAILY_GOAL: 1000,
  
  // Weekly stats
  WEEK_MON: 1200,
  WEEK_WED: 1500,
  WEEK_THU: 1100,
  WEEK_FRI: 1300,
  WEEK_SUN: 1250,
  
  // Estimation tokens
  SMALL_REQUEST: 1000,
  MEDIUM_REQUEST: 1500,
  LARGE_REQUEST: 2000,
  XLARGE_REQUEST: 2500,
  HUGE_REQUEST: 3000,
} as const