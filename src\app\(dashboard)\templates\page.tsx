import { createClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import { LinkButton } from '@/components/ui/link-button'
import { TemplateBrowser } from '@/components/templates/template-browser'

export default async function TemplatesPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container-wide flex h-16 items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <LinkButton href="/dashboard" variant="ghost">← Back to Dashboard</LinkButton>
            <h1 className="text-2xl font-bold">Project Templates</h1>
          </div>
          <LinkButton href="/projects/new">Create Custom Project</LinkButton>
        </div>
      </header>
      
      <main className="container-wide py-6 sm:py-8 lg:py-10">
        <div className="max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px] mx-auto">
          <TemplateBrowser />
        </div>
      </main>
    </div>
  )
}