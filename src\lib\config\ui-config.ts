/**
 * UI Configuration
 * Centralized UI/UX consistency standards
 */

// Spacing scale - consistent padding/margin values
export const SPACING = {
  // Padding
  PADDING: {
    NONE: '',
    XS: 'p-2',
    SM: 'p-3',
    MD: 'p-4',
    LG: 'p-6',
    XL: 'p-8',
  },
  // Card-specific padding
  CARD: {
    HEADER: 'p-4',
    CONTENT: 'p-4 pt-0',
    FOOTER: 'p-4 pt-0',
  },
  // Gap values for flex/grid
  GAP: {
    NONE: '',
    XS: 'gap-1',
    SM: 'gap-2',
    MD: 'gap-4',
    LG: 'gap-6',
    XL: 'gap-8',
  },
  // Vertical spacing
  SPACE_Y: {
    NONE: '',
    XS: 'space-y-1',
    SM: 'space-y-2',
    MD: 'space-y-4',
    LG: 'space-y-6',
    XL: 'space-y-8',
  },
} as const

// Border radius scale
export const RADIUS = {
  NONE: '',
  SM: 'rounded-sm',
  MD: 'rounded-md',
  LG: 'rounded-lg',
  XL: 'rounded-xl',
  FULL: 'rounded-full',
  // Component-specific
  CARD: 'rounded-[var(--radius)]',
  BUTTON: 'rounded-md',
  INPUT: 'rounded-md',
  MODAL: 'rounded-lg',
  AVATAR: 'rounded-full',
} as const

// Shadow scale - Writer's Sanctuary theme
export const SHADOWS = {
  NONE: '',
  SOFT: 'shadow-soft',
  MEDIUM: 'shadow-medium',
  STRONG: 'shadow-strong',
  DRAMATIC: 'shadow-dramatic',
  // Hover states
  HOVER: {
    SOFT_TO_MEDIUM: 'shadow-soft hover:shadow-medium',
    MEDIUM_TO_STRONG: 'shadow-medium hover:shadow-strong',
    STRONG_TO_DRAMATIC: 'shadow-strong hover:shadow-dramatic',
  },
  // Component-specific
  CARD: 'shadow-soft hover:shadow-medium',
  BUTTON: 'shadow-sm hover:shadow-md',
  MODAL: 'shadow-dramatic',
} as const

// Typography scale
export const TYPOGRAPHY = {
  // Font sizes with appropriate line heights
  SIZE: {
    XS: 'text-xs',
    SM: 'text-sm',
    BASE: 'text-base',
    LG: 'text-lg',
    XL: 'text-xl',
    '2XL': 'text-2xl',
    '3XL': 'text-3xl',
    '4XL': 'text-4xl',
  },
  // Font weights
  WEIGHT: {
    NORMAL: 'font-normal',
    MEDIUM: 'font-medium',
    SEMIBOLD: 'font-semibold',
    BOLD: 'font-bold',
  },
  // Font families
  FAMILY: {
    LITERARY: 'font-literary',
    SANS: 'font-sans',
    MONO: 'font-mono',
  },
  // Presets for common use cases
  PRESETS: {
    // Headings
    H1: 'text-3xl font-literary font-bold',
    H2: 'text-2xl font-literary font-semibold',
    H3: 'text-xl font-literary font-semibold',
    H4: 'text-lg font-literary font-medium',
    // Card elements
    CARD_TITLE: 'text-lg font-literary font-semibold',
    CARD_DESCRIPTION: 'text-sm text-muted-foreground',
    // Body text
    BODY: 'text-base',
    BODY_SMALL: 'text-sm',
    // Labels and captions
    LABEL: 'text-sm font-medium',
    CAPTION: 'text-xs text-muted-foreground',
    // Buttons
    BUTTON: 'text-sm font-medium',
    BUTTON_LARGE: 'text-base font-medium',
  },
} as const

// Button sizes
export const BUTTON_SIZES = {
  ICON: 'sm', // Icon-only buttons
  INLINE: 'sm', // Inline actions
  PRIMARY: 'default', // Primary actions
  LARGE: 'lg', // Hero CTAs
} as const

// Status colors - using CSS variables for theme compatibility
export const STATUS_COLORS = {
  // Project status
  PROJECT: {
    ACTIVE: 'bg-primary/10 text-primary border-primary/20',
    DRAFT: 'bg-muted text-muted-foreground border-muted',
    COMPLETED: 'bg-success/10 text-success border-success/20',
    ARCHIVED: 'bg-muted/50 text-muted-foreground border-muted/50',
  },
  // General status
  STATUS: {
    SUCCESS: 'bg-success/10 text-success',
    WARNING: 'bg-warning/10 text-warning',
    ERROR: 'bg-destructive/10 text-destructive',
    INFO: 'bg-info/10 text-info',
  },
  // Badges
  BADGE: {
    DEFAULT: 'bg-secondary text-secondary-foreground',
    PRIMARY: 'bg-primary text-primary-foreground',
    SUCCESS: 'bg-success text-success-foreground',
    WARNING: 'bg-warning text-warning-foreground',
    DESTRUCTIVE: 'bg-destructive text-destructive-foreground',
  },
} as const

// Animation durations
export const ANIMATIONS = {
  FAST: 'duration-150',
  NORMAL: 'duration-200',
  SLOW: 'duration-300',
  // Transitions
  TRANSITION: {
    ALL: 'transition-all duration-200',
    COLORS: 'transition-colors duration-200',
    SHADOW: 'transition-shadow duration-200',
    TRANSFORM: 'transition-transform duration-200',
  },
} as const

// Z-index scale
export const Z_INDEX = {
  BEHIND: '-z-10',
  BASE: 'z-0',
  DROPDOWN: 'z-10',
  STICKY: 'z-20',
  MODAL_BACKDROP: 'z-40',
  MODAL: 'z-50',
  POPOVER: 'z-50',
  TOOLTIP: 'z-50',
  NOTIFICATION: 'z-50',
} as const

// Responsive breakpoints helpers
export const BREAKPOINTS = {
  HIDE: {
    MOBILE: 'hidden sm:block',
    TABLET: 'hidden md:block',
    DESKTOP: 'hidden lg:block',
  },
  SHOW: {
    MOBILE_ONLY: 'block sm:hidden',
    TABLET_ONLY: 'hidden sm:block md:hidden',
    DESKTOP_ONLY: 'hidden lg:block',
  },
} as const

// Component-specific configurations
export const COMPONENTS = {
  CARD: {
    ROOT: `${RADIUS.CARD} ${SHADOWS.CARD} ${ANIMATIONS.TRANSITION.SHADOW}`,
    HEADER: `${SPACING.CARD.HEADER} ${SPACING.SPACE_Y.XS}`,
    TITLE: TYPOGRAPHY.PRESETS.CARD_TITLE,
    DESCRIPTION: TYPOGRAPHY.PRESETS.CARD_DESCRIPTION,
    CONTENT: SPACING.CARD.CONTENT,
    FOOTER: SPACING.CARD.FOOTER,
  },
  BUTTON: {
    SIZE: {
      DEFAULT: BUTTON_SIZES.PRIMARY,
      ICON: BUTTON_SIZES.ICON,
      SMALL: BUTTON_SIZES.INLINE,
      LARGE: BUTTON_SIZES.LARGE,
    },
    RADIUS: RADIUS.BUTTON,
  },
  INPUT: {
    RADIUS: RADIUS.INPUT,
    SIZE: 'h-10',
  },
  MODAL: {
    RADIUS: RADIUS.MODAL,
    SHADOW: SHADOWS.MODAL,
    PADDING: SPACING.PADDING.LG,
  },
} as const

// Helper function to get consistent component classes
export function getComponentClasses(component: keyof typeof COMPONENTS) {
  return COMPONENTS[component]
}

// Icon sizes
export const ICON_SIZES = {
  XS: 'h-3 w-3',
  SM: 'h-4 w-4',
  MD: 'h-5 w-5',
  LG: 'h-6 w-6',
  XL: 'h-8 w-8',
  '2XL': 'h-10 w-10',
  // Component-specific
  BUTTON: 'h-4 w-4',
  CARD_HEADER: 'h-5 w-5',
  NAV: 'h-4 w-4',
  EMPTY_STATE: 'h-12 w-12',
  ERROR_STATE: 'h-16 w-16',
} as const

// Loading states
export const LOADING_STATES = {
  SPINNER_SIZES: {
    SM: 'h-4 w-4',
    MD: 'h-6 w-6',
    LG: 'h-8 w-8',
    XL: 'h-12 w-12',
  },
  SKELETON: {
    TEXT: 'h-4 bg-muted animate-pulse rounded',
    TITLE: 'h-6 bg-muted animate-pulse rounded',
    CARD: 'h-32 bg-muted animate-pulse rounded-lg',
    AVATAR: 'h-10 w-10 bg-muted animate-pulse rounded-full',
    BUTTON: 'h-10 w-20 bg-muted animate-pulse rounded-md',
  },
  DOTS: 'animate-pulse',
} as const

// Grid layouts
export const GRID = {
  COLS: {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    6: 'grid-cols-6',
    12: 'grid-cols-12',
  },
  RESPONSIVE: {
    CARDS: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    FEATURES: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    GALLERY: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    FORM: 'grid-cols-1 md:grid-cols-2',
  },
} as const

// Container widths
export const CONTAINERS = {
  XS: 'max-w-xs',
  SM: 'max-w-sm',
  MD: 'max-w-md',
  LG: 'max-w-lg',
  XL: 'max-w-xl',
  '2XL': 'max-w-2xl',
  '3XL': 'max-w-3xl',
  '4XL': 'max-w-4xl',
  '5XL': 'max-w-5xl',
  '6XL': 'max-w-6xl',
  '7XL': 'max-w-7xl',
  PROSE: 'max-w-prose',
  SCREEN: 'max-w-screen-xl',
} as const

// Aspect ratios
export const ASPECT_RATIOS = {
  SQUARE: 'aspect-square',
  VIDEO: 'aspect-video',
  PORTRAIT: 'aspect-[3/4]',
  LANDSCAPE: 'aspect-[4/3]',
  WIDE: 'aspect-[16/9]',
  ULTRAWIDE: 'aspect-[21/9]',
  BOOK_COVER: 'aspect-[6/9]',
} as const

// Backdrop filters
export const BACKDROPS = {
  BLUR: {
    SM: 'backdrop-blur-sm',
    MD: 'backdrop-blur-md',
    LG: 'backdrop-blur-lg',
    XL: 'backdrop-blur-xl',
  },
  BRIGHTNESS: {
    DARK: 'backdrop-brightness-50',
    DIM: 'backdrop-brightness-75',
    NORMAL: 'backdrop-brightness-100',
  },
} as const

// Focus states
export const FOCUS = {
  RING: 'focus:ring-2 focus:ring-ring focus:ring-offset-2',
  OUTLINE: 'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  WITHIN: 'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
  VISIBLE: 'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
} as const

// Hover states
export const HOVER = {
  OPACITY: 'hover:opacity-80',
  SCALE: 'hover:scale-105',
  BRIGHTNESS: 'hover:brightness-110',
  SHADOW: 'hover:shadow-lg',
  LIFT: 'hover:-translate-y-1',
  GLOW: 'hover:ring-2 hover:ring-primary/20',
} as const

// Transitions presets
export const TRANSITION_PRESETS = {
  FADE_IN: 'animate-in fade-in duration-500',
  FADE_OUT: 'animate-out fade-out duration-300',
  SLIDE_IN_UP: 'animate-in slide-in-from-bottom-4 duration-500',
  SLIDE_IN_DOWN: 'animate-in slide-in-from-top-4 duration-500',
  SLIDE_IN_LEFT: 'animate-in slide-in-from-left-4 duration-500',
  SLIDE_IN_RIGHT: 'animate-in slide-in-from-right-4 duration-500',
  SCALE_IN: 'animate-in zoom-in-90 duration-300',
  SCALE_OUT: 'animate-out zoom-out-90 duration-300',
} as const

// Empty states
export const EMPTY_STATES = {
  ICON_SIZE: ICON_SIZES['2XL'],
  CONTAINER: 'flex flex-col items-center justify-center text-center p-8 min-h-[300px]',
  ICON_WRAPPER: 'mb-4 p-4 bg-muted rounded-full',
  TITLE: 'text-lg font-semibold mb-2',
  DESCRIPTION: 'text-sm text-muted-foreground mb-4 max-w-sm',
} as const

// Error states
export const ERROR_STATES = {
  CONTAINER: 'flex flex-col items-center justify-center text-center p-8 min-h-[400px]',
  ICON_WRAPPER: 'mb-4 p-4 bg-destructive/10 rounded-full',
  ICON: 'h-8 w-8 text-destructive',
  TITLE: 'text-xl font-semibold mb-2',
  DESCRIPTION: 'text-sm text-muted-foreground mb-4 max-w-md',
} as const

// Form elements
export const FORM = {
  FIELD: 'space-y-2',
  LABEL: 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
  INPUT: 'flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  TEXTAREA: 'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  ERROR: 'text-sm font-medium text-destructive',
  HINT: 'text-sm text-muted-foreground',
} as const

// Badge variants
export const BADGE_VARIANTS = {
  DEFAULT: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
  PRIMARY: 'bg-primary text-primary-foreground hover:bg-primary/80',
  DESTRUCTIVE: 'bg-destructive text-destructive-foreground hover:bg-destructive/80',
  OUTLINE: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
  SUCCESS: 'bg-success text-success-foreground hover:bg-success/80',
  WARNING: 'bg-warning text-warning-foreground hover:bg-warning/80',
} as const

// Dividers
export const DIVIDERS = {
  HORIZONTAL: 'border-t border-border',
  VERTICAL: 'border-l border-border',
  DASHED: 'border-t border-dashed border-border',
  THICK: 'border-t-2 border-border',
  WITH_TEXT: 'relative flex items-center py-5',
} as const

// Helper function to combine spacing classes
export function spacing(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ')
}

// Helper function to get consistent icon size
export function getIconSize(size: keyof typeof ICON_SIZES = 'MD'): string {
  return ICON_SIZES[size]
}

// Helper function to create responsive grid classes
export function responsiveGrid(base: number, sm?: number, md?: number, lg?: number): string {
  const classes = [`grid-cols-${base}`]
  if (sm) classes.push(`sm:grid-cols-${sm}`)
  if (md) classes.push(`md:grid-cols-${md}`)
  if (lg) classes.push(`lg:grid-cols-${lg}`)
  return classes.join(' ')
}