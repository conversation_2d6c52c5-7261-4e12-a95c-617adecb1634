-- Create content index table for storing indexed content
CREATE TABLE IF NOT EXISTS content_index (
    id TEXT PRIMARY KEY, -- Format: {content_type}_{content_id}
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    content_type TEXT NOT NULL CHECK (content_type IN ('chapter', 'character', 'location', 'story_bible', 'note')),
    content_id UUID NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    search_vector TSVECTOR,
    metadata JSONB DEFAULT '{}',
    indexed_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes for performance
    CONSTRAINT unique_content_index UNIQUE (content_type, content_id)
);

-- Create indexes for efficient searching
CREATE INDEX IF NOT EXISTS idx_content_index_project_id ON content_index(project_id);
CREATE INDEX IF NOT EXISTS idx_content_index_content_type ON content_index(content_type);
CREATE INDEX IF NOT EXISTS idx_content_index_content_id ON content_index(content_id);
CREATE INDEX IF NOT EXISTS idx_content_index_search_vector ON content_index USING gin(search_vector);
CREATE INDEX IF NOT EXISTS idx_content_index_indexed_at ON content_index(indexed_at DESC);

-- Create search analytics table
CREATE TABLE IF NOT EXISTS search_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    result_count INTEGER NOT NULL DEFAULT 0,
    clicked_result_id TEXT,
    clicked_result_type TEXT,
    clicked_result_position INTEGER,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for search analytics
CREATE INDEX IF NOT EXISTS idx_search_analytics_project_id ON search_analytics(project_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_user_id ON search_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_query ON search_analytics(query);
CREATE INDEX IF NOT EXISTS idx_search_analytics_timestamp ON search_analytics(timestamp DESC);

-- Function to update search vectors
CREATE OR REPLACE FUNCTION update_search_vectors(p_project_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE content_index
    SET search_vector = to_tsvector('english', 
        COALESCE(title, '') || ' ' || 
        COALESCE(content, '')
    ),
    updated_at = NOW()
    WHERE project_id = p_project_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get popular searches
CREATE OR REPLACE FUNCTION get_popular_searches(
    p_project_id UUID,
    p_since TIMESTAMPTZ,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE(query TEXT, search_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sa.query,
        COUNT(*) as search_count
    FROM search_analytics sa
    WHERE sa.project_id = p_project_id
    AND sa.timestamp >= p_since
    GROUP BY sa.query
    ORDER BY search_count DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update timestamp
CREATE OR REPLACE FUNCTION update_content_index_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER content_index_updated_at
    BEFORE UPDATE ON content_index
    FOR EACH ROW
    EXECUTE FUNCTION update_content_index_updated_at();

-- Enable RLS
ALTER TABLE content_index ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;

-- RLS policies for content_index
CREATE POLICY "Users can view indexed content for their projects" ON content_index
    FOR SELECT USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
            UNION
            SELECT project_id FROM project_collaborators WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can index content for their projects" ON content_index
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
            UNION
            SELECT project_id FROM project_collaborators 
            WHERE user_id = auth.uid() AND role IN ('editor', 'admin')
        )
    );

CREATE POLICY "Users can update indexed content for their projects" ON content_index
    FOR UPDATE USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
            UNION
            SELECT project_id FROM project_collaborators 
            WHERE user_id = auth.uid() AND role IN ('editor', 'admin')
        )
    );

CREATE POLICY "Users can delete indexed content for their projects" ON content_index
    FOR DELETE USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
            UNION
            SELECT project_id FROM project_collaborators 
            WHERE user_id = auth.uid() AND role IN ('editor', 'admin')
        )
    );

-- RLS policies for search_analytics
CREATE POLICY "Users can view search analytics for their projects" ON search_analytics
    FOR SELECT USING (
        project_id IN (
            SELECT id FROM projects WHERE user_id = auth.uid()
            UNION
            SELECT project_id FROM project_collaborators 
            WHERE user_id = auth.uid() AND role IN ('editor', 'admin')
        )
    );

CREATE POLICY "Users can insert their own search analytics" ON search_analytics
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON content_index TO authenticated;
GRANT SELECT, INSERT ON search_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION update_search_vectors(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_popular_searches(UUID, TIMESTAMPTZ, INTEGER) TO authenticated;