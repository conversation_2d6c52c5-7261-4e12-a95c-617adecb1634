import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { z } from 'zod'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { applyRateLimit } from '@/lib/rate-limiter-unified'
import { handleAPIError } from '@/lib/api/error-handler'
import { ErrorResponses } from '@/lib/api/error-response'

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  try {
    const supabase = await createTypedServerClient()
    const { data: profiles, error } = await supabase
      .from('selection_profiles')
      .select('*')
      .eq('user_id', request.user!.id)
      .order('updated_at', { ascending: false })

    if (error) throw error
    return NextResponse.json({ profiles })
  } catch (error) {
    return handleAPIError(error, 'Profiles GET')
  }
})

const createProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be 100 characters or less'),
  description: z.string().max(500, 'Description must be 500 characters or less').optional(),
  category: z.enum(['custom', 'template', 'shared']).default('custom'),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()).max(10, 'Maximum 10 tags allowed').default([]),
  settings: z.record(z.unknown()).refine(
    (data) => Object.keys(data).length > 0,
    { message: 'Settings cannot be empty' }
  )
})

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const rateLimitResponse = await applyRateLimit(request as unknown as NextRequest, { type: 'authenticated' })
  if (rateLimitResponse) {
    return rateLimitResponse
  }
  try {
    const body = await request.json()
    const validationResult = createProfileSchema.safeParse(body)
    if (!validationResult.success) {
      return ErrorResponses.validationError(validationResult.error)
    }

    const { name, description, category, isPublic, tags, settings } = validationResult.data
    const supabase = await createTypedServerClient()
    const { data: profile, error } = await supabase
      .from('selection_profiles')
      .insert({
        user_id: request.user!.id,
        name,
        description: description || '',
        category,
        is_public: isPublic,
        is_featured: false,
        settings,
        tags,
        usage_count: 0,
      })
      .select()
      .single()

    if (error) throw error
    return NextResponse.json({ profile })
  } catch (error) {
    return handleAPIError(error, 'Profiles POST')
  }
})
