/**
 * Database schema type definitions
 * This file re-exports all database table types
 */

import type { Json } from '../base'
import type * as StoryTypes from '../story-structure'

// Re-export all table definitions
export * from './auth'
export * from './usage'
export * from './projects'
export * from './story'
export * from './characters'
export * from './chapters'
export * from './content'
export * from './writing'
export * from './ai'
export * from './processing'

// Import all table interfaces for the Database type
import type { AuthTables } from './auth'
import type { UsageTables } from './usage'
import type { ProjectTables } from './projects'
import type { StoryTables } from './story'
import type { CharacterTables } from './characters'
import type { ChapterTables } from './chapters'
import type { ContentTables } from './content'
import type { WritingTables } from './writing'
import type { AITables } from './ai'
import type { ProcessingTables } from './processing'

// Main Database interface
export interface Database {
  public: {
    Tables: AuthTables &
      UsageTables &
      ProjectTables &
      StoryTables &
      CharacterTables &
      ChapterTables &
      ContentTables &
      WritingTables &
      AITables &
      ProcessingTables
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}