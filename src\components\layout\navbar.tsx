"use client"

import { <PERSON><PERSON> } from '@/components/ui/button'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  BookOpen,
  PlusCircle,
  User,
  Settings,
  LogOut,
  Menu,
  Search,
  Feather
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/contexts/auth-context'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { Breadcrumb, BreadcrumbItem } from '@/components/ui/breadcrumb'
import { AchievementProgressWidget } from '@/components/achievements/achievement-progress-widget'
import { SignOutDialog } from '@/components/auth/sign-out-dialog'
import { ARIA_ROLES, SR_ONLY, FOCUS_CLASSES } from '@/lib/utils/accessibility-utils'
import { SPACING, TYPOGRAPHY, ICON_SIZES, SHADOWS, BACKDROPS, Z_INDEX } from '@/lib/config/ui-config'
import { cn } from '@/lib/utils'

interface NavbarProps {
  onMenuClick?: () => void
  showMenuButton?: boolean
  breadcrumbs?: BreadcrumbItem[]
}

export function Navbar({ onMenuClick, showMenuButton = true, breadcrumbs }: NavbarProps) {
  const { user, loading } = useAuth()
  
  const getUserInitials = (email: string) => {
    const emailPart = email.split('@')[0]
    return emailPart ? emailPart.slice(0, 2).toUpperCase() : 'U'
  }
  
  return (
    <>
    {/* Skip to main content link for keyboard navigation */}
    <a 
      href="#main-content" 
      className={cn(
        "absolute left-0 top-0 z-[60] px-4 py-2 bg-background text-foreground",
        "transform -translate-y-full transition-transform duration-200",
        "focus:translate-y-0",
        FOCUS_CLASSES.VISIBLE
      )}
    >
      Skip to main content
    </a>
    
    <header 
      className={cn('sticky top-0', Z_INDEX.STICKY, 'w-full border-b border-border bg-background/95', BACKDROPS.BLUR.MD, 'supports-[backdrop-filter]:bg-background/80', SHADOWS.SOFT)}
      role={ARIA_ROLES.BANNER}
    >
      <div className="container flex h-16 items-center justify-between px-4 md:px-6">
        <div className={cn('flex items-center', SPACING.GAP.MD)}>
          {showMenuButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick}
              className="md:hidden"
              aria-label="Toggle navigation menu"
            >
              <Menu className={ICON_SIZES.MD} />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          )}
          
          <Link href="/dashboard" className={cn('flex items-center', SPACING.GAP.SM)} aria-label="BookScribe AI Home">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 blur-sm opacity-20" />
              <div className="relative w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
                <Feather className={cn(ICON_SIZES.MD, 'text-primary-foreground')} />
              </div>
            </div>
            <span className={cn(TYPOGRAPHY.FAMILY.LITERARY, TYPOGRAPHY.WEIGHT.BOLD, TYPOGRAPHY.SIZE.XL, 'text-foreground')}>BookScribe AI</span>
          </Link>
        </div>
        
        {/* Desktop Navigation */}
        <nav 
          className={cn('hidden md:flex items-center', SPACING.GAP.LG)}
          role={ARIA_ROLES.NAVIGATION}
          aria-label="Main navigation"
        >
          <Link
            href="/dashboard"
            className={cn(TYPOGRAPHY.PRESETS.LABEL, 'transition-colors hover:text-primary text-muted-foreground hover:text-foreground')}
            aria-label="Go to Dashboard"
          >
            Dashboard
          </Link>
          <Link
            href="/projects"
            className={cn(TYPOGRAPHY.PRESETS.LABEL, 'transition-colors hover:text-primary text-muted-foreground hover:text-foreground')}
            aria-label="View all projects"
          >
            Projects
          </Link>
          <Link
            href="/series"
            className={cn(TYPOGRAPHY.PRESETS.LABEL, 'transition-colors hover:text-primary text-muted-foreground hover:text-foreground')}
            aria-label="Manage book series"
          >
            Series
          </Link>
          <Link
            href="/universes"
            className={cn(TYPOGRAPHY.PRESETS.LABEL, 'transition-colors hover:text-primary text-muted-foreground hover:text-foreground')}
            aria-label="Manage story universes"
          >
            Universes
          </Link>
        </nav>
        
        <div className={cn('flex items-center', SPACING.GAP.SM)}>
          {/* Search (future feature) */}
          <Button variant="ghost" size="icon" className="hidden md:flex" aria-label="Search">
            <Search className={ICON_SIZES.SM} />
            <span className="sr-only">Search</span>
          </Button>
          
          {/* Achievement Progress */}
          {user && <AchievementProgressWidget />}
          
          {/* Theme Toggle */}
          <ThemeToggle />
          
          {/* New Project Button */}
          <Button asChild size="sm" variant="literary" className="hidden md:flex">
            <Link href="/projects/new" className={cn('flex items-center', SPACING.GAP.SM)}>
              <Feather className={ICON_SIZES.SM} />
              <span>New Story</span>
            </Link>
          </Button>
          
          {/* Mobile New Project Button */}
          <Button asChild size="icon" variant="literary" className="md:hidden">
            <Link href="/projects/new">
              <Feather className={ICON_SIZES.SM} />
              <span className="sr-only">New Story</span>
            </Link>
          </Button>
          
          {/* User Menu */}
          {!loading && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="relative"
                  aria-label={user ? `User menu for ${user.email}` : 'User menu'}
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  {user ? (
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                      {getUserInitials(user.email || '')}
                    </div>
                  ) : (
                    <User className="h-4 w-4" />
                  )}
                  <span className={SR_ONLY}>User menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="end" 
                className="w-56"
                role={ARIA_ROLES.MENU}
                aria-label="User account menu"
              >
                {user ? (
                  <>
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user.user_metadata?.full_name || 'User'}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard" className="cursor-pointer">
                        <BookOpen className="mr-2 h-4 w-4" />
                        Dashboard
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/projects" className="cursor-pointer">
                        <BookOpen className="mr-2 h-4 w-4" />
                        My Projects
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/settings" className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <SignOutDialog>
                      <DropdownMenuItem
                        onSelect={(e) => e.preventDefault()}
                        className="cursor-pointer text-error focus:text-error"
                        role={ARIA_ROLES.MENUITEM}
                      >
                        <LogOut className="mr-2 h-4 w-4" aria-hidden="true" />
                        Sign out
                      </DropdownMenuItem>
                    </SignOutDialog>
                  </>
                ) : (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/login" className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        Sign in
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/signup" className="cursor-pointer">
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Sign up
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </header>
    {breadcrumbs && breadcrumbs.length > 0 && (
      <div className="border-b border-border bg-card/30">
        <div className="container px-4 md:px-6 py-3">
          <Breadcrumb items={breadcrumbs} />
        </div>
      </div>
    )}
  </>
  )
}