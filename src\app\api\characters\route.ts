import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { createCharacterSchema, characterQuerySchema } from '@/lib/validation/schemas'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { baseSchemas } from '@/lib/validation/common-schemas'

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    project_id: searchParams.get('project_id'),
    role: searchParams.get('role'),
    search: searchParams.get('search'),
    limit: searchParams.get('limit'),
    offset: searchParams.get('offset')
  };

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    querySchema: characterQuerySchema,
    rateLimitKey: 'characters-list',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // Validate project access if project_id is provided
      if (queryParams.project_id) {
        const supabase = await createTypedServerClient();
        const { data: project } = await supabase
          .from('projects')
          .select('id')
          .eq('id', queryParams.project_id)
          .eq('user_id', user.id)
          .single();
        
        if (!project) {
          const { data: collaborator } = await supabase
            .from('project_collaborators')
            .select('role')
            .eq('project_id', queryParams.project_id)
            .eq('user_id', user.id)
            .eq('status', 'active')
            .single();
          
          if (!collaborator) {
            return { valid: false, error: 'Access denied to this project' };
          }
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const validatedQuery = context.query;

  try {
    const supabase = await createTypedServerClient();

    // First get user's projects
    const { data: userProjects, error: projectsError } = await supabase
      .from('projects')
      .select('id')
      .eq('user_id', user.id)

    if (projectsError) {
      logger.error('Error fetching user projects:', projectsError)
      return NextResponse.json({ error: 'Failed to fetch user projects' }, { status: 500 })
    }

    // If user has no projects, return empty array
    if (!userProjects || userProjects.length === 0) {
      logger.info('User has no projects', {
        userId: user.id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.success({ 
        characters: [],
        pagination: {
          total: 0,
          limit: validatedQuery.limit || 50,
          offset: validatedQuery.offset || 0,
          hasMore: false
        }
      });
    }

    // Extract project IDs
    const projectIds = userProjects.map(p => p.id)

    // Build query for characters
    let query = supabase
      .from('characters')
      .select('*', { count: 'exact' })
      .in('project_id', projectIds)

    // Apply filters
    if (validatedQuery.project_id && projectIds.includes(validatedQuery.project_id)) {
      query = query.eq('project_id', validatedQuery.project_id)
    }
    if (validatedQuery.role) {
      query = query.eq('role', validatedQuery.role)
    }
    if (validatedQuery.search) {
      query = query.or(`name.ilike.%${validatedQuery.search}%,description.ilike.%${validatedQuery.search}%`)
    }

    // Apply pagination
    if (validatedQuery.limit) {
      query = query.limit(validatedQuery.limit)
    }
    if (validatedQuery.offset) {
      query = query.range(validatedQuery.offset, (validatedQuery.offset + (validatedQuery.limit || 50)) - 1)
    }

    // Order by creation date
    query = query.order('created_at', { ascending: false })

    const { data: characters, error, count } = await query;

    if (error) {
      logger.error('Error fetching characters:', error, {
        userId: user.id,
        projectId: validatedQuery.project_id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error('Failed to fetch characters');
    }

    const limit = validatedQuery.limit || 50;
    const offset = validatedQuery.offset || 0;
    const hasMore = (count || 0) > offset + limit;

    logger.info('Characters fetched successfully', {
      userId: user.id,
      count: characters?.length || 0,
      total: count || 0,
      projectId: validatedQuery.project_id,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      characters: characters || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore,
        nextOffset: hasMore ? offset + limit : null
      }
    });

  } catch (error) {
    logger.error('Error in characters GET:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to fetch characters');
  }
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: createCharacterSchema,
    rateLimitKey: 'character-create',
    rateLimitCost: 5,
    maxBodySize: 50 * 1024, // 50KB - characters can have detailed profiles
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      const body = await req.json();
      const { project_id, name, description } = body;
      
      // Check for malicious content in character data
      const textToCheck = `${name || ''} ${description || ''}`;
      if (textToCheck.match(/<script|javascript:|onerror|onclick|<iframe|<object|<embed/i)) {
        return { valid: false, error: 'Character data contains potentially malicious content' };
      }
      
      // Verify project ownership
      const supabase = await createTypedServerClient();
      const { data: project } = await supabase
        .from('projects')
        .select('id, user_id')
        .eq('id', project_id)
        .single();
      
      if (!project) {
        return { valid: false, error: 'Project not found' };
      }
      
      const isOwner = project.user_id === user.id;
      if (!isOwner) {
        const { data: collaborator } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', project_id)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();
        
        if (!collaborator || collaborator.role === 'viewer') {
          return { valid: false, error: 'Insufficient permissions to create characters' };
        }
      }
      
      // Check character limit based on subscription
      const { data: existingCharacters } = await supabase
        .from('characters')
        .select('id', { count: 'exact', head: true })
        .eq('project_id', project_id);
      
      const characterCount = existingCharacters?.length || 0;
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('tier')
        .eq('user_id', isOwner ? project.user_id : user.id)
        .eq('status', 'active')
        .single();
      
      const tier = subscription?.tier || 'free';
      const limits = {
        free: 10,
        starter: 50,
        professional: 200,
        studio: -1 // unlimited
      };
      
      const limit = limits[tier as keyof typeof limits];
      if (limit !== -1 && characterCount >= limit) {
        return { 
          valid: false, 
          error: `Character limit (${limit}) reached for ${tier} tier` 
        };
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const validatedData = context.body;

  try {
    const supabase = await createTypedServerClient();

    // Project ownership already verified in validator

    // Check if character_id already exists in this project
    const { data: existingCharacter } = await supabase
      .from('characters')
      .select('id')
      .eq('project_id', validatedData.project_id)
      .eq('character_id', validatedData.character_id)
      .single()

    if (existingCharacter) {
      logger.warn('Duplicate character ID attempt', {
        userId: user.id,
        projectId: validatedData.project_id,
        characterId: validatedData.character_id,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error(
        'Character with this ID already exists in the project',
        409
      );
    }

    // Create character
    const { data: character, error } = await supabase
      .from('characters')
      .insert({
        ...validatedData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      logger.error('Error creating character:', error, {
        userId: user.id,
        projectId: validatedData.project_id,
        clientIP: context.clientIP
      });
      
      if (error.code === '23505') { // Unique constraint violation
        return UnifiedResponse.error(
          'A character with this name already exists',
          409
        );
      }
      
      return UnifiedResponse.error('Failed to create character');
    }

    logger.info('Character created successfully', {
      userId: user.id,
      characterId: character.id,
      projectId: validatedData.project_id,
      role: validatedData.role,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success(
      { character },
      'Character created successfully',
      201
    );

  } catch (error) {
    logger.error('Error in characters POST:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to create character');
  }
});