import { z } from 'zod';
import { 
  CONTENT_LIMITS,
  VALIDATION_RULES,
  SESSION_CONFIG,
  STORAGE_CONFIG,
  COLLABORATION_CONFIG
} from '../config/app-constants';
import { ERROR_MESSAGES } from '../config/error-messages';

// ===== COMMON VALIDATION SCHEMAS =====

// String validations with proper error messages
export const emailSchema = z.string()
  .email(ERROR_MESSAGES.VALIDATION.INVALID_EMAIL)
  .toLowerCase()
  .trim();

export const urlSchema = z.string()
  .url(ERROR_MESSAGES.VALIDATION.INVALID_URL)
  .trim();

export const phoneSchema = z.string()
  .regex(/^\+?[1-9]\d{1,14}$/, ERROR_MESSAGES.VALIDATION.INVALID_PHONE)
  .trim();

export const uuidSchema = z.string()
  .uuid('Invalid ID format');

export const passwordSchema = z.string()
  .min(VALIDATION_RULES.MIN_PASSWORD_LENGTH, 
    `Password must be at least ${VALIDATION_RULES.MIN_PASSWORD_LENGTH} characters`)
  .max(VALIDATION_RULES.MAX_PASSWORD_LENGTH,
    `Password must be less than ${VALIDATION_RULES.MAX_PASSWORD_LENGTH} characters`)
  .refine(
    (password) => {
      if (!VALIDATION_RULES.PASSWORD_REQUIRE_UPPERCASE) return true;
      return /[A-Z]/.test(password);
    },
    'Password must contain at least one uppercase letter'
  )
  .refine(
    (password) => {
      if (!VALIDATION_RULES.PASSWORD_REQUIRE_LOWERCASE) return true;
      return /[a-z]/.test(password);
    },
    'Password must contain at least one lowercase letter'
  )
  .refine(
    (password) => {
      if (!VALIDATION_RULES.PASSWORD_REQUIRE_NUMBER) return true;
      return /[0-9]/.test(password);
    },
    'Password must contain at least one number'
  )
  .refine(
    (password) => {
      if (!VALIDATION_RULES.PASSWORD_REQUIRE_SPECIAL) return true;
      return /[!@#$%^&*(),.?":{}|<>]/.test(password);
    },
    'Password must contain at least one special character'
  );

export const usernameSchema = z.string()
  .min(VALIDATION_RULES.MIN_USERNAME_LENGTH,
    `Username must be at least ${VALIDATION_RULES.MIN_USERNAME_LENGTH} characters`)
  .max(VALIDATION_RULES.MAX_USERNAME_LENGTH,
    `Username must be less than ${VALIDATION_RULES.MAX_USERNAME_LENGTH} characters`)
  .regex(VALIDATION_RULES.USERNAME_PATTERN, 
    'Username can only contain letters, numbers, underscores, and hyphens')
  .trim();

// ===== USER & AUTH SCHEMAS =====

export const signUpSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  username: usernameSchema.optional(),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms'),
  marketingConsent: z.boolean().optional()
}).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: ERROR_MESSAGES.VALIDATION.PASSWORDS_DONT_MATCH,
    path: ['confirmPassword']
  }
);

export const signInSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD),
  rememberMe: z.boolean().optional()
});

export const resetPasswordSchema = z.object({
  email: emailSchema
});

export const updatePasswordSchema = z.object({
  currentPassword: z.string().min(1, ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD),
  newPassword: passwordSchema,
  confirmPassword: z.string()
}).refine(
  (data) => data.newPassword === data.confirmPassword,
  {
    message: ERROR_MESSAGES.VALIDATION.PASSWORDS_DONT_MATCH,
    path: ['confirmPassword']
  }
);

// ===== PROJECT SCHEMAS =====

export const projectNameSchema = z.string()
  .min(VALIDATION_RULES.MIN_PROJECT_NAME_LENGTH,
    `Project name must be at least ${VALIDATION_RULES.MIN_PROJECT_NAME_LENGTH} character`)
  .max(VALIDATION_RULES.MAX_PROJECT_NAME_LENGTH,
    `Project name must be less than ${VALIDATION_RULES.MAX_PROJECT_NAME_LENGTH} characters`)
  .regex(VALIDATION_RULES.PROJECT_NAME_PATTERN,
    'Project name contains invalid characters')
  .trim();

export const createProjectSchema = z.object({
  title: projectNameSchema,
  description: z.string()
    .max(CONTENT_LIMITS.MAX_DESCRIPTION_LENGTH,
      `Description must be less than ${CONTENT_LIMITS.MAX_DESCRIPTION_LENGTH} characters`)
    .optional(),
  primary_genre: z.string().min(1, ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD),
  subgenre: z.string().optional(),
  target_word_count: z.number()
    .min(CONTENT_LIMITS.MIN_BOOK_WORDS, 
      `Book must be at least ${CONTENT_LIMITS.MIN_BOOK_WORDS.toLocaleString()} words`)
    .max(CONTENT_LIMITS.MAX_BOOK_WORDS,
      `Book cannot exceed ${CONTENT_LIMITS.MAX_BOOK_WORDS.toLocaleString()} words`)
    .optional(),
  target_chapters: z.number()
    .int()
    .positive()
    .optional(),
  narrative_voice: z.enum(['first_person', 'third_person_limited', 'third_person_omniscient', 'second_person'])
    .optional(),
  tense: z.enum(['past', 'present', 'future']).optional(),
  target_audience: z.string().optional(),
  content_warnings: z.array(z.string()).optional()
});

// ===== CHAPTER SCHEMAS =====

export const chapterTitleSchema = z.string()
  .max(CONTENT_LIMITS.MAX_TITLE_LENGTH,
    `Chapter title must be less than ${CONTENT_LIMITS.MAX_TITLE_LENGTH} characters`)
  .trim();

export const chapterContentSchema = z.string()
  .min(CONTENT_LIMITS.MIN_CHAPTER_WORDS,
    `Chapter must be at least ${CONTENT_LIMITS.MIN_CHAPTER_WORDS.toLocaleString()} words`)
  .refine(
    (content) => {
      const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
      return wordCount <= CONTENT_LIMITS.MAX_CHAPTER_WORDS;
    },
    `Chapter cannot exceed ${CONTENT_LIMITS.MAX_CHAPTER_WORDS.toLocaleString()} words`
  );

export const createChapterSchema = z.object({
  project_id: uuidSchema,
  chapter_number: z.number().int().positive(),
  title: chapterTitleSchema.optional(),
  outline: z.string()
    .max(CONTENT_LIMITS.MAX_SYNOPSIS_LENGTH,
      `Outline must be less than ${CONTENT_LIMITS.MAX_SYNOPSIS_LENGTH} characters`)
    .optional(),
  content: chapterContentSchema.optional(),
  target_word_count: z.number()
    .min(CONTENT_LIMITS.MIN_CHAPTER_WORDS)
    .max(CONTENT_LIMITS.MAX_CHAPTER_WORDS)
    .optional(),
  pov_character: z.string().optional(),
  status: z.enum(['planned', 'writing', 'review', 'complete']).default('planned')
});

// ===== CHARACTER SCHEMAS =====

export const characterNameSchema = z.string()
  .min(1, ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD)
  .max(CONTENT_LIMITS.MAX_CHARACTER_NAME_LENGTH,
    `Character name must be less than ${CONTENT_LIMITS.MAX_CHARACTER_NAME_LENGTH} characters`)
  .trim();

export const characterBioSchema = z.string()
  .max(CONTENT_LIMITS.MAX_CHARACTER_BIO_LENGTH,
    `Character bio must be less than ${CONTENT_LIMITS.MAX_CHARACTER_BIO_LENGTH} characters`);

export const createCharacterSchema = z.object({
  project_id: uuidSchema,
  name: characterNameSchema,
  role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']),
  description: characterBioSchema.optional(),
  backstory: characterBioSchema.optional(),
  personality_traits: z.object({
    traits: z.array(z.string().max(100)).max(20),
    strengths: z.array(z.string().max(100)).max(10),
    flaws: z.array(z.string().max(100)).max(10),
    motivations: z.array(z.string().max(100)).max(10)
  }).optional(),
  voice_data: z.object({
    speaking_style: z.string().max(500),
    vocabulary: z.array(z.string().max(50)).max(CONTENT_LIMITS.MAX_VOICE_EXAMPLES),
    mannerisms: z.array(z.string().max(CONTENT_LIMITS.MAX_VOICE_TRAIT_LENGTH))
      .max(CONTENT_LIMITS.MAX_VOICE_MANNERISMS)
  }).optional()
});

// ===== SERIES SCHEMAS =====

export const seriesTitleSchema = z.string()
  .min(1, ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD)
  .max(CONTENT_LIMITS.MAX_SERIES_TITLE_LENGTH,
    `Series title must be less than ${CONTENT_LIMITS.MAX_SERIES_TITLE_LENGTH} characters`)
  .trim();

export const createSeriesSchema = z.object({
  title: seriesTitleSchema,
  description: z.string()
    .max(CONTENT_LIMITS.MAX_DESCRIPTION_LENGTH)
    .optional(),
  genre: z.string().optional(),
  target_audience: z.string().optional(),
  planned_book_count: z.number()
    .int()
    .positive()
    .max(CONTENT_LIMITS.MAX_SERIES_BOOKS,
      `Series cannot have more than ${CONTENT_LIMITS.MAX_SERIES_BOOKS} books`)
    .optional()
});

// ===== FILE UPLOAD SCHEMAS =====

export const fileUploadSchema = z.object({
  file: z.instanceof(File)
    .refine(
      (file) => file.size <= STORAGE_CONFIG.MAX_FILE_SIZE_MB * 1024 * 1024,
      `File size must be less than ${STORAGE_CONFIG.MAX_FILE_SIZE_MB}MB`
    ),
  type: z.enum(['image', 'document', 'export'])
});

export const imageUploadSchema = z.object({
  file: z.instanceof(File)
    .refine(
      (file) => STORAGE_CONFIG.ALLOWED_IMAGE_TYPES.includes(file.type),
      'Invalid image format. Supported formats: JPEG, PNG, GIF, WebP'
    )
    .refine(
      (file) => file.size <= STORAGE_CONFIG.MAX_IMAGE_SIZE_MB * 1024 * 1024,
      `Image size must be less than ${STORAGE_CONFIG.MAX_IMAGE_SIZE_MB}MB`
    )
});

// ===== COLLABORATION SCHEMAS =====

export const inviteCollaboratorSchema = z.object({
  email: emailSchema,
  role: z.enum(COLLABORATION_CONFIG.PERMISSION_LEVELS),
  message: z.string().max(500).optional()
});

// ===== WRITING SESSION SCHEMAS =====

export const writingSessionSchema = z.object({
  project_id: uuidSchema,
  chapter_id: uuidSchema.optional(),
  duration_minutes: z.number()
    .min(SESSION_CONFIG.MIN_SESSION_DURATION_MINUTES,
      `Session must be at least ${SESSION_CONFIG.MIN_SESSION_DURATION_MINUTES} minutes`)
    .max(SESSION_CONFIG.MAX_SESSION_DURATION_HOURS * 60,
      `Session cannot exceed ${SESSION_CONFIG.MAX_SESSION_DURATION_HOURS} hours`),
  words_written: z.number().int().min(0),
  mode: z.enum(['writing', 'editing', 'planning', 'research']).optional()
});

// ===== EXPORT SCHEMAS =====

export const exportRequestSchema = z.object({
  project_id: uuidSchema,
  format: z.enum(STORAGE_CONFIG.ALLOWED_EXPORT_FORMATS),
  chapters: z.array(z.number().int().positive()).optional(),
  includeMetadata: z.boolean().optional(),
  includeComments: z.boolean().optional(),
  includeRevisionHistory: z.boolean().optional()
});

// ===== PAYMENT SCHEMAS =====

export const subscriptionChangeSchema = z.object({
  plan: z.enum(['free', 'pro', 'enterprise']),
  paymentMethodId: z.string().optional(),
  billingInterval: z.enum(['monthly', 'yearly']).optional()
});

// ===== SETTINGS SCHEMAS =====

export const userPreferencesSchema = z.object({
  theme: z.string().optional(),
  editorFont: z.string().optional(),
  editorFontSize: z.number().min(10).max(24).optional(),
  autoSave: z.boolean().optional(),
  autoSaveInterval: z.number().min(30).max(600).optional(),
  spellCheck: z.boolean().optional(),
  showWordCount: z.boolean().optional(),
  defaultProjectView: z.enum(['grid', 'list', 'timeline']).optional(),
  emailNotifications: z.object({
    weeklyProgress: z.boolean().optional(),
    milestones: z.boolean().optional(),
    collaborations: z.boolean().optional(),
    marketing: z.boolean().optional()
  }).optional()
});

// ===== SEARCH & FILTER SCHEMAS =====

export const searchQuerySchema = z.object({
  query: z.string().min(2, 'Search query must be at least 2 characters'),
  type: z.enum(['all', 'projects', 'chapters', 'characters']).optional(),
  filters: z.object({
    genre: z.string().optional(),
    status: z.string().optional(),
    dateRange: z.object({
      start: z.string().datetime().optional(),
      end: z.string().datetime().optional()
    }).optional()
  }).optional(),
  sort: z.object({
    field: z.string(),
    order: z.enum(['asc', 'desc'])
  }).optional(),
  pagination: z.object({
    page: z.number().int().positive().default(1),
    limit: z.number().int().positive().max(100).default(20)
  }).optional()
});

// ===== HELPER FUNCTIONS =====

export function validateWithSchema<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: Record<string, string[]> } {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  // Format errors into a more user-friendly structure
  const errors: Record<string, string[]> = {};
  
  result.error.errors.forEach((error) => {
    const path = error.path.join('.');
    if (!errors[path]) {
      errors[path] = [];
    }
    errors[path].push(error.message);
  });
  
  return { success: false, errors };
}

// Export all schemas
export * from './schemas'; // Re-export existing schemas for backward compatibility