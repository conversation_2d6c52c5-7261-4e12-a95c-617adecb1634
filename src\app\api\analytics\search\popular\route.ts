import { NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'

const querySchema = z.object({
  projectId: z.string().uuid(),
  limit: z.string().optional().default('5').transform(val => Math.min(parseInt(val, 10), 10))
})

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const searchParams = Object.fromEntries(request.nextUrl.searchParams)
    const validationResult = querySchema.safeParse(searchParams)
    
    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid query parameters',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400)
    }
    
    const { projectId, limit } = validationResult.data
    
    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()
    
    if (!projectAccess) {
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single()
      
      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403)
      }
    }
    
    // Get popular searches from the last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    // Note: This assumes we have a search_analytics table
    // In production, you might want to use a materialized view or cache this data
    const { data: searches, error } = await supabase
      .rpc('get_popular_searches', {
        p_project_id: projectId,
        p_since: thirtyDaysAgo.toISOString(),
        p_limit: limit
      })
    
    if (error) {
      // If the function doesn't exist, return empty array
      if (error.code === '42883') {
        return UnifiedResponse.success({
          searches: []
        })
      }
      
      logger.error('Failed to get popular searches:', error)
      return UnifiedResponse.error({
        message: 'Failed to get popular searches',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500)
    }
    
    return UnifiedResponse.success({
      searches: searches || []
    })
  } catch (error) {
    logger.error('Error getting popular searches:', error)
    return UnifiedResponse.error({
      message: 'Failed to get popular searches',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500)
  }
})