# Chapter Planner Agent Documentation

## Overview

The Chapter Planner Agent transforms high-level story structures into detailed, scene-by-scene chapter outlines. It ensures perfect pacing, maintains narrative momentum, and creates blueprints that guide the Writing Agent to produce compelling content.

## Agent Profile

- **Name**: Chapter Planner Agent
- **Model**: GPT-4.1 (Premium) / GPT-o4 mini (Starter)
- **Purpose**: Create detailed chapter outlines with scene-level precision
- **Context Window**: 128k tokens
- **Specialization**: Pacing, scene structure, narrative flow

## Core Responsibilities

### 1. Chapter Structure Design
- Breaks acts into individual chapters
- Allocates word counts per chapter
- Designs chapter arcs and mini-climaxes
- Creates compelling openings and cliffhangers

### 2. Scene Planning
- Develops scene-by-scene breakdowns
- Establishes scene goals and conflicts
- Plans character interactions
- Manages information revelation

### 3. Pacing Management
- Balances action and reflection
- Controls narrative tension
- Manages subplot integration
- Ensures reading momentum

### 4. Content Distribution
- Spreads plot points effectively
- Manages character appearances
- Distributes thematic elements
- Controls revelation timing

## Input Requirements

```typescript
interface ChapterPlannerInput {
  storyStructure: StoryStructure;
  characterProfiles: CharacterProfiles;
  projectSettings: ProjectSettings;
  targetWordCount: number;
  targetChapters: number;
  genre: string;
  pacingPreference: PacingStyle;
}
```

## Output Structure

```typescript
interface ChapterOutlines {
  chapters: ChapterOutline[];
  pacingMap: PacingMap;
  sceneSummaries: SceneSummary[];
  contentDistribution: ContentDistribution;
  readingFlow: ReadingFlowAnalysis;
}

interface ChapterOutline {
  chapterNumber: number;
  title: string;
  wordCountTarget: number;
  purpose: string;
  scenes: Scene[];
  pointOfView: string;
  timeline: ChapterTimeline;
  hooks: {
    opening: string;
    closing: string;
  };
  themes: string[];
  characterBeats: CharacterBeat[];
}

interface Scene {
  sceneNumber: number;
  setting: string;
  timeOfDay: string;
  presentCharacters: string[];
  purpose: string[];
  conflict: string;
  resolution: string;
  wordCountTarget: number;
  emotionalTone: string;
  sensoryDetails: string[];
  dialogueNotes: string;
}
```

## Agent Workflow

```mermaid
sequenceDiagram
    participant StoryArch as Story Structure
    participant ChapterPlan as Chapter Planner
    participant Pacing as Pacing Analyzer
    participant Scene as Scene Builder
    
    StoryArch->>ChapterPlan: Acts & plot points
    ChapterPlan->>ChapterPlan: Divide into chapters
    ChapterPlan->>Pacing: Analyze flow
    Pacing-->>ChapterPlan: Pacing feedback
    ChapterPlan->>Scene: Build scenes
    Scene-->>ChapterPlan: Scene details
    ChapterPlan->>ChapterPlan: Add hooks
    ChapterPlan-->>Writing: Chapter outlines
```

## Planning Methodologies

### 1. Three-Act Chapter Distribution
```typescript
const chapterDistribution = {
  act1: {
    percentage: 25,
    chapterTypes: ['hook', 'worldBuilding', 'incitingIncident'],
    pacingStyle: 'accelerating'
  },
  act2: {
    percentage: 50,
    chapterTypes: ['risingAction', 'midpoint', 'complications'],
    pacingStyle: 'variable'
  },
  act3: {
    percentage: 25,
    chapterTypes: ['climaxBuildup', 'climax', 'resolution'],
    pacingStyle: 'intense'
  }
};
```

### 2. Scene Structure Framework
```typescript
interface SceneStructure {
  entry: {
    hook: string;
    establishSetting: boolean;
    characterState: string;
  };
  middle: {
    conflict: string;
    stakes: string;
    attempts: string[];
    complications: string[];
  };
  exit: {
    resolution: 'full' | 'partial' | 'cliffhanger';
    characterChange: string;
    setupNext: string;
  };
}
```

## Pacing Strategies

### Genre-Specific Pacing
1. **Thriller**: Short chapters, constant tension
2. **Literary**: Longer chapters, reflective pacing
3. **Romance**: Emotional beats, tension/release
4. **Mystery**: Clue distribution, revelation timing
5. **Fantasy**: World-building balanced with action

### Pacing Techniques
- **Cliffhanger Endings**: 60% of chapters
- **Scene Variety**: Action/Reflection balance
- **Tension Curves**: Rising/falling patterns
- **Page-Turner Elements**: Micro-hooks throughout
- **Breathing Room**: Strategic slow moments

## Content Distribution

### Plot Point Allocation
```typescript
interface PlotDistribution {
  majorPlotPoints: {
    chapter: number;
    plotPoint: string;
    impact: 'high' | 'medium' | 'low';
  }[];
  subplotBeats: {
    subplot: string;
    chapters: number[];
    progression: 'linear' | 'intermittent';
  }[];
  revelations: {
    information: string;
    chapter: number;
    method: 'dialogue' | 'action' | 'discovery';
  }[];
}
```

### Character Appearance Management
- Protagonist presence: 85-95% of chapters
- Major characters: 60-80% of chapters
- Supporting cast: Strategic appearances
- Character introduction pacing
- Relationship development beats

## Advanced Features

### 1. Multi-POV Planning
- POV rotation patterns
- Voice consistency per POV
- Information management across POVs
- Timeline synchronization
- Perspective advantages

### 2. Subplot Integration
- Parallel plot tracking
- Intersection points
- Thematic echoes
- Pacing balance
- Resolution timing

### 3. Series Considerations
- Book-ending satisfaction
- Series arc progression
- Character growth staging
- World expansion pacing
- Mystery/question management

## Quality Standards

### Chapter Effectiveness Metrics
1. **Hook Strength**: Opening compels continuation
2. **Scene Purpose**: Every scene advances story
3. **Pacing Score**: Maintains reader engagement
4. **Character Development**: Growth in each chapter
5. **Cliffhanger Quality**: Ending demands next chapter

### Structural Requirements
- Clear chapter goals
- Logical scene flow
- Balanced content types
- Emotional variety
- Narrative momentum

## Integration Points

### 1. Writing Agent Handoff
```typescript
interface WritingHandoff {
  chapterOutline: ChapterOutline;
  previousChapters: ChapterSummary[];
  characterStates: CharacterStateMap;
  activeSubplots: SubplotStatus[];
  worldDetails: LocationDetails[];
}
```

### 2. Adaptive Planning Feedback
```typescript
interface AdaptiveFeedback {
  completedChapters: number;
  deviations: PlotDeviation[];
  readerFeedback?: ReaderMetrics;
  suggestedAdjustments: Adjustment[];
}
```

## Configuration Options

### Chapter Length Preferences
1. **Short**: 2,000-3,000 words
2. **Standard**: 3,000-5,000 words
3. **Long**: 5,000-8,000 words
4. **Variable**: Mixed lengths for pacing

### Scene Density Settings
- **High**: 5-8 scenes per chapter
- **Medium**: 3-5 scenes per chapter
- **Low**: 1-3 scenes per chapter
- **Dynamic**: Varies by chapter needs

## Planning Tools

### Chapter Templates
```typescript
const chapterTemplates = {
  opening: {
    structure: ['hook', 'worldEstablish', 'characterIntro', 'inciting'],
    pacing: 'measured',
    wordDistribution: [15, 35, 30, 20]
  },
  action: {
    structure: ['quickStart', 'escalation', 'climax', 'aftermath'],
    pacing: 'rapid',
    wordDistribution: [10, 40, 35, 15]
  },
  revelation: {
    structure: ['buildup', 'discovery', 'processing', 'implications'],
    pacing: 'suspenseful',
    wordDistribution: [30, 20, 25, 25]
  }
};
```

### Scene Transition Patterns
1. **Direct Continuation**: Immediate follow-up
2. **Time Jump**: Hours/days later
3. **Location Shift**: New setting
4. **POV Switch**: Different character
5. **Parallel Action**: Meanwhile...

## Best Practices

### Effective Chapter Planning
1. Start with chapter purpose
2. Build scenes to serve purpose
3. Layer in subplots naturally
4. Create scene variety
5. End with forward momentum

### Common Techniques
- Opening with action or intrigue
- Closing with questions
- Balancing dialogue and narrative
- Using sensory anchors
- Creating emotional resonance

## Performance Metrics

### Processing Efficiency
- Planning time: 60-120 seconds per book
- Revision cycles: 1-2 average
- Context usage: 50-70%
- Success rate: 92%

### Output Quality
- Scene purposefulness: 95%
- Pacing effectiveness: 88%
- Hook quality: 90%
- Structure coherence: 94%

## Troubleshooting

### Common Issues
1. **Uneven Pacing**: Automated pacing analysis
2. **Weak Transitions**: Scene connection review
3. **Character Absence**: Appearance tracking
4. **Information Dumps**: Distribution analysis
5. **Repetitive Structures**: Pattern variation

### Solutions
- Pacing heat maps
- Transition smoothing
- Character scheduling
- Information spreading
- Template rotation

## Future Enhancements

### Planned Features
1. **Interactive Planning**: Visual chapter builder
2. **Reader Psychology**: Engagement prediction
3. **Dynamic Adjustment**: Real-time optimization
4. **Scene Generator**: Detailed scene writing
5. **Pacing Simulator**: Reading flow testing

### Research Areas
- Neuroscience of reading engagement
- Chapter length optimization
- Scene transition effectiveness
- Cliffhanger psychology
- Multi-device reading patterns