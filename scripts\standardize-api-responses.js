#!/usr/bin/env node

/**
 * Script to help standardize API responses across all routes
 * Updates routes to use the new response utilities
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Transformation rules
const transformations = [
  // Success responses
  {
    name: 'NextResponse.json with success true',
    pattern: /NextResponse\.json\(\s*{\s*success:\s*true,?\s*([^}]*)\s*}\s*\)/g,
    replacement: (match, content) => {
      // Extract data from content
      const dataMatch = content.match(/data:\s*([^,}]+)/);
      if (dataMatch) {
        return `successResponse(${dataMatch[1].trim()})`;
      }
      return match;
    }
  },
  
  // Direct data responses
  {
    name: 'Direct NextResponse.json',
    pattern: /NextResponse\.json\(\s*{\s*(\w+):\s*([^}]+)\s*}\s*\)/g,
    replacement: (match, key, value) => {
      if (key === 'error') {
        return `ErrorResponses.internalError(${value})`;
      }
      return `successResponse({ ${key}: ${value} })`;
    }
  },
  
  // Error responses with status
  {
    name: 'Error responses',
    pattern: /NextResponse\.json\(\s*{\s*error:\s*(['"`][^'"`]+['"`])\s*},\s*{\s*status:\s*(\d+)\s*}\s*\)/g,
    replacement: (match, message, status) => {
      switch (status) {
        case '400': return `ErrorResponses.badRequest(${message})`;
        case '401': return `ErrorResponses.unauthorized(${message})`;
        case '403': return `ErrorResponses.forbidden(${message})`;
        case '404': return `ErrorResponses.notFound(${message})`;
        case '409': return `ErrorResponses.conflict(${message})`;
        case '429': return `ErrorResponses.rateLimitExceeded()`;
        case '500': return `ErrorResponses.internalError(${message})`;
        default: return `errorResponse(${message}, undefined, undefined, ${status})`;
      }
    }
  },
  
  // handleAPIError calls
  {
    name: 'handleAPIError to ErrorResponses',
    pattern: /handleAPIError\(new\s+ValidationError\(['"`]([^'"`]+)['"`]\)\)/g,
    replacement: (match, message) => `ErrorResponses.validationError("${message}")`
  },
  
  {
    name: 'handleAPIError generic',
    pattern: /handleAPIError\(([^)]+)\)/g,
    replacement: 'ErrorResponses.internalError($1.message)'
  }
];

// Import additions
const requiredImports = {
  responseUtils: `import { successResponse, errorResponse, paginatedResponse, ErrorResponses } from '@/lib/api/response-utils'`,
  types: `import type { SuccessResponse, ErrorResponse, PaginatedResponse } from '@/lib/api/response-utils'`
};

function addImports(content) {
  // Check if imports already exist
  if (content.includes('@/lib/api/response-utils')) {
    return content;
  }
  
  // Find the last import statement
  const importMatches = content.match(/import\s+.*?from\s+['"].*?['"];?\s*\n/g);
  if (importMatches) {
    const lastImport = importMatches[importMatches.length - 1];
    const lastImportIndex = content.lastIndexOf(lastImport);
    
    // Insert new imports after the last import
    return content.substring(0, lastImportIndex + lastImport.length) +
           requiredImports.responseUtils + '\n' +
           content.substring(lastImportIndex + lastImport.length);
  }
  
  // If no imports found, add at the beginning
  return requiredImports.responseUtils + '\n\n' + content;
}

function transformFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let transformed = false;
  
  // Apply transformations
  transformations.forEach(({ name, pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      transformed = true;
    }
  });
  
  // Add imports if transformed
  if (transformed) {
    content = addImports(content);
  }
  
  return { content, transformed };
}

async function main() {
  console.log('🔄 Standardizing API responses...\n');
  
  const apiPath = path.join(__dirname, '../src/app/api');
  const files = glob.sync('**/*.ts', { cwd: apiPath, absolute: true });
  
  // Read the report to focus on files needing attention
  const reportPath = path.join(__dirname, 'api-patterns-report.json');
  const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
  
  const filesNeedingAttention = report.files.filter(f => 
    f.responseStyle === 'unknown' || !f.hasErrorHandling
  );
  
  console.log(`Found ${filesNeedingAttention.length} files needing standardization\n`);
  
  let updatedCount = 0;
  const updateResults = [];
  
  // Process only a few files as examples
  const filesToProcess = filesNeedingAttention.slice(0, 5);
  
  for (const fileInfo of filesToProcess) {
    const fullPath = path.join(apiPath, fileInfo.file);
    
    try {
      const { content, transformed } = transformFile(fullPath);
      
      if (transformed) {
        // Save the transformed file
        fs.writeFileSync(fullPath, content);
        console.log(`✅ Updated: ${fileInfo.file}`);
        updatedCount++;
        
        updateResults.push({
          file: fileInfo.file,
          status: 'updated',
          transformations: 'applied'
        });
      } else {
        console.log(`⏭️  Skipped: ${fileInfo.file} (no patterns matched)`);
        updateResults.push({
          file: fileInfo.file,
          status: 'skipped',
          reason: 'no patterns matched'
        });
      }
    } catch (error) {
      console.error(`❌ Error processing ${fileInfo.file}:`, error.message);
      updateResults.push({
        file: fileInfo.file,
        status: 'error',
        error: error.message
      });
    }
  }
  
  // Summary
  console.log(`\n📊 Summary:`);
  console.log(`- Files processed: ${filesToProcess.length}`);
  console.log(`- Files updated: ${updatedCount}`);
  console.log(`- Files remaining: ${filesNeedingAttention.length - filesToProcess.length}`);
  
  // Save update log
  const logPath = path.join(__dirname, 'api-response-standardization.log');
  fs.writeFileSync(logPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    processed: updateResults,
    remaining: filesNeedingAttention.slice(5).map(f => f.file)
  }, null, 2));
  
  console.log(`\n📝 Update log saved to: ${logPath}`);
  console.log('\n⚠️  This was a limited run. Review the changes and run again for more files.');
}

main().catch(console.error);