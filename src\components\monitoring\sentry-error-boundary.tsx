'use client';

import { ErrorBoundary } from '@sentry/nextjs';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { getUserFriendlyError } from '@/lib/config/error-messages';

interface SentryErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{
    error: Error;
    resetError: () => void;
  }>;
  showDialog?: boolean;
  beforeCapture?: (scope: any, error: Error | null) => void;
}

function DefaultErrorFallback({ 
  error, 
  resetError 
}: { 
  error: Error; 
  resetError: () => void;
}) {
  const router = useRouter();
  const userFriendlyMessage = getUserFriendlyError(error);

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="max-w-lg sm:max-w-xl lg:max-w-2xl lg:max-w-3xl xl:max-w-4xl w-full">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-8 h-8 text-destructive" />
          </div>
          <CardTitle className="text-2xl">Something went wrong</CardTitle>
          <CardDescription className="text-base mt-2">
            {userFriendlyMessage}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={resetError}
              className="flex-1"
              variant="default"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            <Button
              onClick={() => router.push('/dashboard')}
              className="flex-1"
              variant="outline"
            >
              <Home className="w-4 h-4 mr-2" />
              Go to Dashboard
            </Button>
          </div>
          
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4 p-4 bg-muted rounded-lg">
              <summary className="cursor-pointer text-sm font-medium">
                Error Details (Development Only)
              </summary>
              <pre className="mt-2 text-xs overflow-auto">
                {error.stack || error.message}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export function SentryErrorBoundary({
  children,
  fallback = DefaultErrorFallback,
  showDialog = false,
  beforeCapture,
}: SentryErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={fallback}
      showDialog={showDialog}
      beforeCapture={beforeCapture}
    >
      {children}
    </ErrorBoundary>
  );
}