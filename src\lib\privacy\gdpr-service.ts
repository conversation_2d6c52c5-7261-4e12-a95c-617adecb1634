import { createServerClient } from '@/lib/supabase';
import { createAdminClient } from '@/lib/supabase/admin';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

/**
 * GDPR Compliance Service for BookScribe
 * Implements data privacy regulations including:
 * - Right to Access (data export)
 * - Right to Erasure (data deletion)
 * - Right to Rectification (data correction)
 * - Right to Data Portability
 * - Consent Management
 */

// GDPR Request Types
export enum GDPRRequestType {
  ACCESS = 'access',
  ERASURE = 'erasure',
  RECTIFICATION = 'rectification',
  PORTABILITY = 'portability',
  CONSENT_WITHDRAWAL = 'consent_withdrawal',
  PROCESSING_RESTRICTION = 'processing_restriction'
}

// GDPR Request Status
export enum GDPRRequestStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

// Data Categories for GDPR
export enum DataCategory {
  PERSONAL_INFO = 'personal_info',
  CONTENT_DATA = 'content_data',
  USAGE_DATA = 'usage_data',
  AI_INTERACTIONS = 'ai_interactions',
  BILLING_DATA = 'billing_data',
  COMMUNICATION_DATA = 'communication_data',
  TECHNICAL_DATA = 'technical_data'
}

// GDPR Request Schema
export const gdprRequestSchema = z.object({
  type: z.nativeEnum(GDPRRequestType),
  reason: z.string().min(1).max(1000).optional(),
  dataCategories: z.array(z.nativeEnum(DataCategory)).optional(),
  verificationToken: z.string().optional(),
  additionalInfo: z.record(z.any()).optional()
});

// User Consent Schema
export const userConsentSchema = z.object({
  marketing: z.boolean(),
  analytics: z.boolean(),
  thirdPartySharing: z.boolean(),
  aiDataProcessing: z.boolean(),
  performanceTracking: z.boolean()
});

export interface GDPRRequest {
  id: string;
  userId: string;
  type: GDPRRequestType;
  status: GDPRRequestStatus;
  requestedAt: Date;
  processedAt?: Date;
  expiresAt?: Date;
  dataCategories?: DataCategory[];
  downloadUrl?: string;
  metadata?: Record<string, any>;
}

export interface UserConsent {
  userId: string;
  marketing: boolean;
  analytics: boolean;
  thirdPartySharing: boolean;
  aiDataProcessing: boolean;
  performanceTracking: boolean;
  consentedAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface DataExport {
  userId: string;
  exportDate: Date;
  dataCategories: Record<DataCategory, any>;
  format: 'json' | 'csv' | 'pdf';
  encrypted: boolean;
}

export class GDPRService {
  /**
   * Creates a new GDPR request
   */
  static async createRequest(
    userId: string,
    requestData: z.infer<typeof gdprRequestSchema>
  ): Promise<GDPRRequest> {
    try {
      const adminClient = createAdminClient();
      
      // Calculate expiry based on request type
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + (requestData.type === GDPRRequestType.ERASURE ? 30 : 7));

      const { data, error } = await adminClient
        .from('gdpr_requests')
        .insert({
          user_id: userId,
          type: requestData.type,
          status: GDPRRequestStatus.PENDING,
          data_categories: requestData.dataCategories,
          reason: requestData.reason,
          metadata: requestData.additionalInfo,
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Log the request for audit trail
      logger.info('GDPR request created', {
        requestId: data.id,
        userId,
        type: requestData.type,
        dataCategories: requestData.dataCategories
      });

      // Send confirmation email
      await this.sendRequestConfirmation(userId, data);

      return this.formatGDPRRequest(data);
    } catch (error) {
      logger.error('Failed to create GDPR request', { error, userId, requestData });
      throw error;
    }
  }

  /**
   * Processes a GDPR request based on type
   */
  static async processRequest(requestId: string): Promise<void> {
    try {
      const adminClient = createAdminClient();
      
      // Get the request
      const { data: request, error } = await adminClient
        .from('gdpr_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (error || !request) throw error || new Error('Request not found');

      // Update status to in progress
      await adminClient
        .from('gdpr_requests')
        .update({ status: GDPRRequestStatus.IN_PROGRESS })
        .eq('id', requestId);

      // Process based on type
      switch (request.type) {
        case GDPRRequestType.ACCESS:
          await this.processAccessRequest(request);
          break;
        
        case GDPRRequestType.ERASURE:
          await this.processErasureRequest(request);
          break;
        
        case GDPRRequestType.RECTIFICATION:
          await this.processRectificationRequest(request);
          break;
        
        case GDPRRequestType.PORTABILITY:
          await this.processPortabilityRequest(request);
          break;
        
        case GDPRRequestType.CONSENT_WITHDRAWAL:
          await this.processConsentWithdrawal(request);
          break;
        
        case GDPRRequestType.PROCESSING_RESTRICTION:
          await this.processRestrictionRequest(request);
          break;
        
        default:
          throw new Error(`Unknown request type: ${request.type}`);
      }

      // Mark as completed
      await adminClient
        .from('gdpr_requests')
        .update({
          status: GDPRRequestStatus.COMPLETED,
          processed_at: new Date().toISOString()
        })
        .eq('id', requestId);

      logger.info('GDPR request processed', { requestId, type: request.type });

    } catch (error) {
      logger.error('Failed to process GDPR request', { error, requestId });
      
      // Mark as failed
      const adminClient = createAdminClient();
      await adminClient
        .from('gdpr_requests')
        .update({
          status: GDPRRequestStatus.REJECTED,
          metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
        })
        .eq('id', requestId);
      
      throw error;
    }
  }

  /**
   * Processes data access request (Right to Access)
   */
  private static async processAccessRequest(request: any): Promise<void> {
    const userId = request.user_id;
    const dataCategories = request.data_categories || Object.values(DataCategory);
    
    // Collect all user data
    const exportData = await this.collectUserData(userId, dataCategories);
    
    // Generate secure download link
    const downloadUrl = await this.generateSecureDownloadLink(userId, exportData);
    
    // Update request with download URL
    const adminClient = createAdminClient();
    await adminClient
      .from('gdpr_requests')
      .update({ download_url: downloadUrl })
      .eq('id', request.id);
    
    // Send notification
    await this.sendDataReadyNotification(userId, downloadUrl);
  }

  /**
   * Processes data erasure request (Right to be Forgotten)
   */
  private static async processErasureRequest(request: any): Promise<void> {
    const userId = request.user_id;
    
    // Verify user wants to proceed (double confirmation)
    if (!request.metadata?.confirmed) {
      throw new Error('Erasure request requires confirmation');
    }
    
    // Create final data export before deletion
    const finalExport = await this.collectUserData(userId, Object.values(DataCategory));
    await this.archiveUserData(userId, finalExport);
    
    // Delete user data in order
    await this.deleteUserContent(userId);
    await this.deleteUserAnalytics(userId);
    await this.deleteUserCommunications(userId);
    await this.anonymizeUserTransactions(userId);
    await this.deleteUserAccount(userId);
    
    logger.info('User data erased', { userId });
  }

  /**
   * Collects all user data for export
   */
  static async collectUserData(
    userId: string,
    categories: DataCategory[]
  ): Promise<Record<DataCategory, any>> {
    const adminClient = createAdminClient();
    const data: Record<string, any> = {};

    // Personal Information
    if (categories.includes(DataCategory.PERSONAL_INFO)) {
      const { data: profile } = await adminClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      const { data: user } = await adminClient.auth.admin.getUserById(userId);
      
      data[DataCategory.PERSONAL_INFO] = {
        profile,
        email: user?.user?.email,
        metadata: user?.user?.user_metadata,
        created_at: user?.user?.created_at,
        last_sign_in: user?.user?.last_sign_in_at
      };
    }

    // Content Data
    if (categories.includes(DataCategory.CONTENT_DATA)) {
      const { data: projects } = await adminClient
        .from('projects')
        .select('*, chapters(*), characters(*)')
        .eq('user_id', userId);
      
      const { data: references } = await adminClient
        .from('reference_materials')
        .select('*')
        .eq('user_id', userId);
      
      data[DataCategory.CONTENT_DATA] = {
        projects,
        references,
        totalProjects: projects?.length || 0,
        totalChapters: projects?.reduce((acc, p) => acc + (p.chapters?.length || 0), 0) || 0
      };
    }

    // Usage Data
    if (categories.includes(DataCategory.USAGE_DATA)) {
      const { data: sessions } = await adminClient
        .from('writing_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1000);
      
      const { data: analytics } = await adminClient
        .from('user_analytics')
        .select('*')
        .eq('user_id', userId);
      
      data[DataCategory.USAGE_DATA] = {
        sessions,
        analytics,
        totalSessions: sessions?.length || 0
      };
    }

    // AI Interactions
    if (categories.includes(DataCategory.AI_INTERACTIONS)) {
      const { data: aiUsage } = await adminClient
        .from('ai_usage_logs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1000);
      
      const { data: suggestions } = await adminClient
        .from('ai_suggestions')
        .select('*')
        .eq('user_id', userId);
      
      data[DataCategory.AI_INTERACTIONS] = {
        usage: aiUsage,
        suggestions,
        totalInteractions: aiUsage?.length || 0
      };
    }

    // Billing Data
    if (categories.includes(DataCategory.BILLING_DATA)) {
      const { data: subscriptions } = await adminClient
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId);
      
      const { data: transactions } = await adminClient
        .from('billing_history')
        .select('*')
        .eq('user_id', userId);
      
      data[DataCategory.BILLING_DATA] = {
        subscriptions: this.sanitizeBillingData(subscriptions),
        transactions: this.sanitizeBillingData(transactions)
      };
    }

    // Communication Data
    if (categories.includes(DataCategory.COMMUNICATION_DATA)) {
      const { data: emails } = await adminClient
        .from('email_logs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(500);
      
      const { data: notifications } = await adminClient
        .from('notifications')
        .select('*')
        .eq('user_id', userId);
      
      data[DataCategory.COMMUNICATION_DATA] = {
        emails: emails?.map(e => ({
          ...e,
          content: undefined // Remove content for privacy
        })),
        notifications
      };
    }

    // Technical Data
    if (categories.includes(DataCategory.TECHNICAL_DATA)) {
      const { data: devices } = await adminClient
        .from('user_devices')
        .select('*')
        .eq('user_id', userId);
      
      const { data: sessions } = await adminClient
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100);
      
      data[DataCategory.TECHNICAL_DATA] = {
        devices,
        sessions: sessions?.map(s => ({
          ...s,
          ip_address: this.anonymizeIP(s.ip_address)
        }))
      };
    }

    return data as Record<DataCategory, any>;
  }

  /**
   * Updates user consent preferences
   */
  static async updateConsent(
    userId: string,
    consent: z.infer<typeof userConsentSchema>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<UserConsent> {
    try {
      const adminClient = createAdminClient();
      
      // Store consent record
      const { data, error } = await adminClient
        .from('user_consent')
        .insert({
          user_id: userId,
          ...consent,
          ip_address: ipAddress,
          user_agent: userAgent,
          consented_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Update user preferences based on consent
      await this.applyConsentPreferences(userId, consent);

      logger.info('User consent updated', {
        userId,
        consent,
        ipAddress: this.anonymizeIP(ipAddress)
      });

      return {
        userId,
        ...consent,
        consentedAt: new Date(data.consented_at),
        ipAddress: data.ip_address,
        userAgent: data.user_agent
      };
    } catch (error) {
      logger.error('Failed to update consent', { error, userId });
      throw error;
    }
  }

  /**
   * Gets current user consent status
   */
  static async getConsentStatus(userId: string): Promise<UserConsent | null> {
    try {
      const supabase = await createServerClient();
      
      const { data, error } = await supabase
        .from('user_consent')
        .select('*')
        .eq('user_id', userId)
        .order('consented_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) return null;

      return {
        userId,
        marketing: data.marketing,
        analytics: data.analytics,
        thirdPartySharing: data.third_party_sharing,
        aiDataProcessing: data.ai_data_processing,
        performanceTracking: data.performance_tracking,
        consentedAt: new Date(data.consented_at),
        ipAddress: data.ip_address,
        userAgent: data.user_agent
      };
    } catch (error) {
      logger.error('Failed to get consent status', { error, userId });
      return null;
    }
  }

  /**
   * Applies consent preferences to user settings
   */
  private static async applyConsentPreferences(
    userId: string,
    consent: z.infer<typeof userConsentSchema>
  ): Promise<void> {
    const adminClient = createAdminClient();
    
    // Update user preferences
    await adminClient
      .from('user_preferences')
      .upsert({
        user_id: userId,
        analytics_enabled: consent.analytics,
        marketing_emails: consent.marketing,
        performance_tracking: consent.performanceTracking,
        updated_at: new Date().toISOString()
      });

    // If analytics disabled, delete existing analytics data
    if (!consent.analytics) {
      await this.deleteUserAnalytics(userId);
    }

    // If marketing disabled, unsubscribe from all lists
    if (!consent.marketing) {
      await this.unsubscribeFromMarketing(userId);
    }
  }

  /**
   * Deletes user content data
   */
  private static async deleteUserContent(userId: string): Promise<void> {
    const adminClient = createAdminClient();
    
    // Delete in order of dependencies
    await adminClient.from('ai_suggestions').delete().eq('user_id', userId);
    await adminClient.from('chapter_versions').delete().eq('user_id', userId);
    await adminClient.from('chapters').delete().eq('user_id', userId);
    await adminClient.from('characters').delete().eq('user_id', userId);
    await adminClient.from('reference_materials').delete().eq('user_id', userId);
    await adminClient.from('projects').delete().eq('user_id', userId);
  }

  /**
   * Deletes user analytics data
   */
  private static async deleteUserAnalytics(userId: string): Promise<void> {
    const adminClient = createAdminClient();
    
    await adminClient.from('user_analytics').delete().eq('user_id', userId);
    await adminClient.from('writing_sessions').delete().eq('user_id', userId);
    await adminClient.from('ai_usage_logs').delete().eq('user_id', userId);
    await adminClient.from('user_sessions').delete().eq('user_id', userId);
  }

  /**
   * Deletes user communication data
   */
  private static async deleteUserCommunications(userId: string): Promise<void> {
    const adminClient = createAdminClient();
    
    await adminClient.from('email_logs').delete().eq('user_id', userId);
    await adminClient.from('notifications').delete().eq('user_id', userId);
    await adminClient.from('user_feedback').delete().eq('user_id', userId);
  }

  /**
   * Anonymizes user transactions (keep for legal requirements)
   */
  private static async anonymizeUserTransactions(userId: string): Promise<void> {
    const adminClient = createAdminClient();
    const anonymousId = `deleted_user_${Date.now()}`;
    
    await adminClient
      .from('billing_history')
      .update({
        user_id: anonymousId,
        customer_email: '<EMAIL>',
        customer_name: 'Deleted User'
      })
      .eq('user_id', userId);
  }

  /**
   * Deletes user account
   */
  private static async deleteUserAccount(userId: string): Promise<void> {
    const adminClient = createAdminClient();
    
    // Delete profile
    await adminClient.from('profiles').delete().eq('id', userId);
    
    // Delete auth user
    await adminClient.auth.admin.deleteUser(userId);
  }

  /**
   * Archives user data before deletion
   */
  private static async archiveUserData(userId: string, data: any): Promise<void> {
    const adminClient = createAdminClient();
    
    // Store encrypted archive for legal requirements
    await adminClient
      .from('deleted_user_archives')
      .insert({
        anonymous_id: `deleted_${Date.now()}`,
        encrypted_data: await this.encryptData(JSON.stringify(data)),
        deleted_at: new Date().toISOString(),
        retention_until: new Date(Date.now() + 7 * 365 * 24 * 60 * 60 * 1000).toISOString() // 7 years
      });
  }

  /**
   * Generates secure download link for data export
   */
  private static async generateSecureDownloadLink(
    userId: string,
    data: any
  ): Promise<string> {
    // In production, this would upload to secure storage and return a signed URL
    // For now, we'll store in database with expiry
    const adminClient = createAdminClient();
    
    const { data: export, error } = await adminClient
      .from('gdpr_exports')
      .insert({
        user_id: userId,
        data: await this.encryptData(JSON.stringify(data)),
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      })
      .select()
      .single();

    if (error) throw error;

    // Return download URL
    return `/api/gdpr/download/${export.id}`;
  }

  /**
   * Encrypts sensitive data
   */
  private static async encryptData(data: string): Promise<string> {
    // In production, use proper encryption
    // For now, return base64 encoded
    return Buffer.from(data).toString('base64');
  }

  /**
   * Anonymizes IP address for privacy
   */
  private static anonymizeIP(ip?: string): string {
    if (!ip) return 'unknown';
    
    if (ip.includes(':')) {
      // IPv6 - keep first 3 segments
      const parts = ip.split(':');
      return parts.slice(0, 3).join(':') + '::/48';
    } else {
      // IPv4 - keep first 2 octets
      const parts = ip.split('.');
      return parts.slice(0, 2).join('.') + '.0.0';
    }
  }

  /**
   * Sanitizes billing data to remove sensitive information
   */
  private static sanitizeBillingData(data: any): any {
    if (!data) return null;
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeBillingData(item));
    }
    
    const sanitized = { ...data };
    
    // Remove sensitive fields
    delete sanitized.stripe_customer_id;
    delete sanitized.stripe_subscription_id;
    delete sanitized.stripe_payment_method_id;
    delete sanitized.card_last4;
    delete sanitized.card_brand;
    
    return sanitized;
  }

  /**
   * Sends request confirmation email
   */
  private static async sendRequestConfirmation(userId: string, request: any): Promise<void> {
    // Implementation would send email via email service
    logger.info('GDPR request confirmation sent', { userId, requestId: request.id });
  }

  /**
   * Sends data ready notification
   */
  private static async sendDataReadyNotification(userId: string, downloadUrl: string): Promise<void> {
    // Implementation would send email via email service
    logger.info('GDPR data ready notification sent', { userId, downloadUrl });
  }

  /**
   * Unsubscribes user from marketing
   */
  private static async unsubscribeFromMarketing(userId: string): Promise<void> {
    const adminClient = createAdminClient();
    
    await adminClient
      .from('email_preferences')
      .update({
        marketing_emails: false,
        newsletter: false,
        product_updates: false,
        promotional_offers: false
      })
      .eq('user_id', userId);
  }

  /**
   * Processes rectification request
   */
  private static async processRectificationRequest(request: any): Promise<void> {
    // Implementation for data correction requests
    logger.info('Processing rectification request', { requestId: request.id });
  }

  /**
   * Processes portability request
   */
  private static async processPortabilityRequest(request: any): Promise<void> {
    // Similar to access request but in machine-readable format
    const userId = request.user_id;
    const data = await this.collectUserData(userId, Object.values(DataCategory));
    
    // Convert to standard format (e.g., JSON-LD)
    const portableData = this.convertToPortableFormat(data);
    
    const downloadUrl = await this.generateSecureDownloadLink(userId, portableData);
    
    const adminClient = createAdminClient();
    await adminClient
      .from('gdpr_requests')
      .update({ download_url: downloadUrl })
      .eq('id', request.id);
  }

  /**
   * Processes consent withdrawal
   */
  private static async processConsentWithdrawal(request: any): Promise<void> {
    const userId = request.user_id;
    const categories = request.metadata?.categories || ['all'];
    
    // Withdraw specific consents
    const newConsent = {
      marketing: !categories.includes('marketing') && !categories.includes('all'),
      analytics: !categories.includes('analytics') && !categories.includes('all'),
      thirdPartySharing: false, // Always withdraw third party
      aiDataProcessing: !categories.includes('ai') && !categories.includes('all'),
      performanceTracking: !categories.includes('performance') && !categories.includes('all')
    };
    
    await this.updateConsent(userId, newConsent);
  }

  /**
   * Processes processing restriction request
   */
  private static async processRestrictionRequest(request: any): Promise<void> {
    const userId = request.user_id;
    const adminClient = createAdminClient();
    
    // Mark user account as restricted
    await adminClient
      .from('profiles')
      .update({
        processing_restricted: true,
        restriction_reason: request.reason,
        restricted_at: new Date().toISOString()
      })
      .eq('id', userId);
    
    logger.info('Processing restriction applied', { userId });
  }

  /**
   * Converts data to portable format
   */
  private static convertToPortableFormat(data: any): any {
    // Convert to standardized format for portability
    return {
      '@context': 'https://schema.org',
      '@type': 'Person',
      identifier: data[DataCategory.PERSONAL_INFO]?.profile?.id,
      email: data[DataCategory.PERSONAL_INFO]?.email,
      dateCreated: data[DataCategory.PERSONAL_INFO]?.created_at,
      owns: data[DataCategory.CONTENT_DATA]?.projects?.map((p: any) => ({
        '@type': 'CreativeWork',
        name: p.title,
        text: p.description,
        dateCreated: p.created_at
      }))
    };
  }

  /**
   * Formats GDPR request from database
   */
  private static formatGDPRRequest(data: any): GDPRRequest {
    return {
      id: data.id,
      userId: data.user_id,
      type: data.type,
      status: data.status,
      requestedAt: new Date(data.created_at),
      processedAt: data.processed_at ? new Date(data.processed_at) : undefined,
      expiresAt: data.expires_at ? new Date(data.expires_at) : undefined,
      dataCategories: data.data_categories,
      downloadUrl: data.download_url,
      metadata: data.metadata
    };
  }
}

export default GDPRService;