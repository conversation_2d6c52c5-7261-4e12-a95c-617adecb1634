import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { logger } from '@/lib/services/logger';
import { applyRateLimit } from '@/lib/rate-limiter-unified';

// Common validation schemas
export const commonSchemas = {
  uuid: z.string().uuid('Invalid UUID format'),
  email: z.string().email('Invalid email format'),
  url: z.string().url('Invalid URL format'),
  positiveInt: z.number().int().positive('Must be a positive integer'),
  nonEmptyString: z.string().min(1, 'Cannot be empty'),
  safeString: z.string().max(10000, 'String too long').regex(/^[^<>'"]*$/, 'Contains unsafe characters'),
  jsonString: z.string().refine((str) => {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }, 'Invalid JSON format'),
  fileSize: z.number().max(50 * 1024 * 1024, 'File too large (max 50MB)'),
  pagination: z.object({
    page: z.number().int().min(1).max(1000).default(1),
    limit: z.number().int().min(1).max(100).default(20)
  })
};

// Request validation configuration
export interface RequestValidationConfig {
  // Body validation
  bodySchema?: z.ZodSchema;
  maxBodySize?: number; // bytes
  
  // Query parameter validation
  querySchema?: z.ZodSchema;
  
  // Header validation
  requiredHeaders?: string[];
  headerSchema?: z.ZodSchema;
  
  // Content type validation
  allowedContentTypes?: string[];
  
  // Rate limiting
  rateLimitKey?: string;
  rateLimitCost?: number;
  
  // Security checks
  validateCSRF?: boolean;
  validateOrigin?: boolean;
  allowedOrigins?: string[];
  
  // Request size limits
  maxRequestSize?: number;
  
  // Custom validation function
  customValidator?: (request: NextRequest) => Promise<{ valid: boolean; error?: string }>;
}

export interface ValidationContext {
  body?: any;
  query?: any;
  headers?: any;
  params?: any;
  user?: any;
  clientIP?: string;
  userAgent?: string;
}

/**
 * Comprehensive request validation middleware
 */
export class RequestValidationMiddleware {
  /**
   * Validates incoming requests based on configuration
   */
  static async validateRequest(
    request: NextRequest,
    config: RequestValidationConfig = {}
  ): Promise<{ success: true; context: ValidationContext } | NextResponse> {
    const startTime = Date.now();
    const clientIP = this.getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    try {
      // 1. Rate limiting (if configured)
      if (config.rateLimitKey || config.rateLimitCost) {
        const rateLimitResponse = await applyRateLimit(request, {
          type: config.rateLimitKey as any || 'api',
          cost: config.rateLimitCost || 1
        });
        
        if (rateLimitResponse) {
          this.logValidationEvent('rate_limit_exceeded', request, {
            rateLimitKey: config.rateLimitKey,
            cost: config.rateLimitCost,
            clientIP,
            executionTime: Date.now() - startTime
          });
          return rateLimitResponse;
        }
      }

      // 2. Request size validation
      const contentLength = parseInt(request.headers.get('content-length') || '0');
      const maxSize = config.maxRequestSize || config.maxBodySize || 10 * 1024 * 1024; // 10MB default
      
      if (contentLength > maxSize) {
        this.logValidationEvent('request_too_large', request, {
          contentLength,
          maxSize,
          clientIP
        });
        
        return NextResponse.json(
          { error: 'Request too large', maxSize },
          { status: 413 }
        );
      }

      // 3. Content type validation
      if (config.allowedContentTypes && config.allowedContentTypes.length > 0) {
        const contentType = request.headers.get('content-type') || '';
        const isAllowed = config.allowedContentTypes.some(type => 
          contentType.toLowerCase().includes(type.toLowerCase())
        );
        
        if (!isAllowed && request.method !== 'GET') {
          this.logValidationEvent('invalid_content_type', request, {
            providedContentType: contentType,
            allowedTypes: config.allowedContentTypes,
            clientIP
          });
          
          return NextResponse.json(
            { error: 'Unsupported content type', allowedTypes: config.allowedContentTypes },
            { status: 415 }
          );
        }
      }

      // 4. Origin validation (CORS security)
      if (config.validateOrigin && config.allowedOrigins) {
        const origin = request.headers.get('origin');
        if (origin && !config.allowedOrigins.includes(origin)) {
          this.logValidationEvent('invalid_origin', request, {
            providedOrigin: origin,
            allowedOrigins: config.allowedOrigins,
            clientIP
          });
          
          return NextResponse.json(
            { error: 'Origin not allowed' },
            { status: 403 }
          );
        }
      }

      // 5. CSRF token validation
      if (config.validateCSRF && request.method !== 'GET') {
        const csrfToken = request.headers.get('x-csrf-token') || 
                         request.headers.get('x-xsrf-token');
        
        if (!csrfToken) {
          this.logValidationEvent('missing_csrf_token', request, { clientIP });
          
          return NextResponse.json(
            { error: 'CSRF token required' },
            { status: 403 }
          );
        }
        
        // Validate CSRF token format (basic check)
        if (!/^[a-zA-Z0-9_-]{32,}$/.test(csrfToken)) {
          this.logValidationEvent('invalid_csrf_token', request, {
            tokenFormat: 'invalid',
            clientIP
          });
          
          return NextResponse.json(
            { error: 'Invalid CSRF token format' },
            { status: 403 }
          );
        }
      }

      // 6. Required headers validation
      if (config.requiredHeaders && config.requiredHeaders.length > 0) {
        const missingHeaders = config.requiredHeaders.filter(header => 
          !request.headers.get(header)
        );
        
        if (missingHeaders.length > 0) {
          this.logValidationEvent('missing_required_headers', request, {
            missingHeaders,
            clientIP
          });
          
          return NextResponse.json(
            { error: 'Missing required headers', missingHeaders },
            { status: 400 }
          );
        }
      }

      // 7. Header schema validation
      let validatedHeaders = {};
      if (config.headerSchema) {
        const headers = Object.fromEntries(request.headers.entries());
        const headerValidation = config.headerSchema.safeParse(headers);
        
        if (!headerValidation.success) {
          this.logValidationEvent('header_validation_failed', request, {
            errors: headerValidation.error.errors,
            clientIP
          });
          
          return NextResponse.json(
            { 
              error: 'Header validation failed', 
              details: this.formatZodErrors(headerValidation.error)
            },
            { status: 400 }
          );
        }
        
        validatedHeaders = headerValidation.data;
      }

      // 8. Query parameter validation
      let validatedQuery = {};
      if (config.querySchema) {
        const searchParams = Object.fromEntries(request.nextUrl.searchParams.entries());
        const queryValidation = config.querySchema.safeParse(searchParams);
        
        if (!queryValidation.success) {
          this.logValidationEvent('query_validation_failed', request, {
            errors: queryValidation.error.errors,
            providedQuery: searchParams,
            clientIP
          });
          
          return NextResponse.json(
            { 
              error: 'Query parameter validation failed', 
              details: this.formatZodErrors(queryValidation.error)
            },
            { status: 400 }
          );
        }
        
        validatedQuery = queryValidation.data;
      }

      // 9. Body validation
      let validatedBody = {};
      if (config.bodySchema && request.method !== 'GET') {
        try {
          const body = await request.json();
          
          // Check for potentially dangerous content
          if (this.containsMaliciousContent(body)) {
            this.logValidationEvent('malicious_content_detected', request, {
              suspiciousPatterns: this.detectSuspiciousPatterns(body),
              clientIP
            });
            
            return NextResponse.json(
              { error: 'Request contains potentially malicious content' },
              { status: 400 }
            );
          }
          
          const bodyValidation = config.bodySchema.safeParse(body);
          
          if (!bodyValidation.success) {
            this.logValidationEvent('body_validation_failed', request, {
              errors: bodyValidation.error.errors,
              clientIP
            });
            
            return NextResponse.json(
              { 
                error: 'Request body validation failed', 
                details: this.formatZodErrors(bodyValidation.error)
              },
              { status: 400 }
            );
          }
          
          validatedBody = bodyValidation.data;
        } catch (error) {
          this.logValidationEvent('body_parse_failed', request, {
            parseError: error instanceof Error ? error.message : 'Unknown error',
            clientIP
          });
          
          return NextResponse.json(
            { error: 'Invalid JSON in request body' },
            { status: 400 }
          );
        }
      }

      // 10. Custom validation
      if (config.customValidator) {
        const customResult = await config.customValidator(request);
        if (!customResult.valid) {
          this.logValidationEvent('custom_validation_failed', request, {
            customError: customResult.error,
            clientIP
          });
          
          return NextResponse.json(
            { error: customResult.error || 'Custom validation failed' },
            { status: 400 }
          );
        }
      }

      // 11. Security headers check
      const securityIssues = this.checkSecurityHeaders(request);
      if (securityIssues.length > 0) {
        this.logValidationEvent('security_header_issues', request, {
          issues: securityIssues,
          clientIP
        });
        
        // Log but don't reject - security headers are recommendations
        logger.warn('Security header issues detected', {
          issues: securityIssues,
          clientIP,
          path: request.nextUrl.pathname
        });
      }

      // Success - return validation context
      const validationContext: ValidationContext = {
        body: validatedBody,
        query: validatedQuery,
        headers: validatedHeaders,
        clientIP,
        userAgent
      };

      this.logValidationEvent('validation_success', request, {
        executionTime: Date.now() - startTime,
        clientIP,
        validatedFields: {
          body: !!config.bodySchema,
          query: !!config.querySchema,
          headers: !!config.headerSchema
        }
      });

      return { success: true, context: validationContext };

    } catch (error) {
      this.logValidationEvent('validation_system_error', request, {
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
        clientIP
      });
      
      logger.error('Request validation system error', error);
      
      return NextResponse.json(
        { error: 'Request validation system error' },
        { status: 500 }
      );
    }
  }

  /**
   * Detects potentially malicious content in request data
   */
  private static containsMaliciousContent(data: any): boolean {
    const dataString = JSON.stringify(data).toLowerCase();
    
    // Common attack patterns
    const maliciousPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /on\w+\s*=/gi, // Event handlers like onclick=
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /eval\s*\(/gi,
      /function\s*\(/gi,
      /\$\{.*?\}/g, // Template literals
      /__proto__/gi,
      /constructor/gi,
      /prototype/gi,
      /alert\s*\(/gi,
      /confirm\s*\(/gi,
      /prompt\s*\(/gi,
      /document\./gi,
      /window\./gi,
      /location\./gi,
      /\.innerHTML/gi,
      /\.outerHTML/gi,
      /fromCharCode/gi,
      /String\.fromCharCode/gi,
      /unescape/gi,
      /decodeURIComponent/gi,
      /atob/gi,
      /btoa/gi,
      // SQL injection patterns
      /union\s+select/gi,
      /drop\s+table/gi,
      /delete\s+from/gi,
      /insert\s+into/gi,
      /update\s+set/gi,
      /exec\s*\(/gi,
      /execute\s*\(/gi,
      // NoSQL injection patterns
      /\$where/gi,
      /\$ne/gi,
      /\$gt/gi,
      /\$lt/gi,
      /\$regex/gi,
      // Path traversal
      /\.\.\//g,
      /\.\.\\/g,
      // Command injection
      /\|\s*cat/gi,
      /\|\s*ls/gi,
      /\|\s*rm/gi,
      /\|\s*curl/gi,
      /\|\s*wget/gi,
      /\|\s*nc/gi,
      /\|\s*netcat/gi,
      /&&/g,
      /\|\|/g,
      /;\s*(cat|ls|rm|curl|wget)/gi
    ];
    
    return maliciousPatterns.some(pattern => pattern.test(dataString));
  }

  /**
   * Detects specific suspicious patterns for detailed logging
   */
  private static detectSuspiciousPatterns(data: any): string[] {
    const dataString = JSON.stringify(data);
    const patterns = [];
    
    if (/<script/i.test(dataString)) patterns.push('script_tag');
    if (/javascript:/i.test(dataString)) patterns.push('javascript_protocol');
    if (/on\w+\s*=/i.test(dataString)) patterns.push('event_handler');
    if (/union\s+select/i.test(dataString)) patterns.push('sql_injection');
    if (/\$where/i.test(dataString)) patterns.push('nosql_injection');
    if (/\.\.\//g.test(dataString)) patterns.push('path_traversal');
    if (/\|\s*cat/i.test(dataString)) patterns.push('command_injection');
    if (/__proto__/i.test(dataString)) patterns.push('prototype_pollution');
    
    return patterns;
  }

  /**
   * Checks for recommended security headers
   */
  private static checkSecurityHeaders(request: NextRequest): string[] {
    const issues = [];
    const headers = request.headers;
    
    // Check for security headers (these should be set by the client/proxy)
    if (!headers.get('x-content-type-options')) {
      issues.push('Missing X-Content-Type-Options header');
    }
    
    if (!headers.get('x-frame-options') && !headers.get('content-security-policy')) {
      issues.push('Missing X-Frame-Options or CSP header');
    }
    
    if (!headers.get('x-xss-protection')) {
      issues.push('Missing X-XSS-Protection header');
    }
    
    if (!headers.get('strict-transport-security') && request.nextUrl.protocol === 'https:') {
      issues.push('Missing Strict-Transport-Security header');
    }
    
    if (!headers.get('referrer-policy')) {
      issues.push('Missing Referrer-Policy header');
    }
    
    return issues;
  }

  /**
   * Formats Zod validation errors for client response
   */
  private static formatZodErrors(error: z.ZodError): any {
    return error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    }));
  }

  /**
   * Extracts client IP address from request
   */
  private static getClientIP(request: NextRequest): string {
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const clientIP = request.headers.get('x-client-ip');
    
    if (forwardedFor) {
      return forwardedFor.split(',')[0].trim();
    }
    
    if (realIP) return realIP;
    if (clientIP) return clientIP;
    
    return request.ip || 'unknown';
  }

  /**
   * Logs validation events for monitoring and security analysis
   */
  private static logValidationEvent(
    eventType: string,
    request: NextRequest,
    details: Record<string, any>
  ): void {
    const logData = {
      eventType,
      method: request.method,
      path: request.nextUrl.pathname,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      userAgent: request.headers.get('user-agent'),
      referer: request.headers.get('referer'),
      timestamp: new Date().toISOString(),
      ...details
    };

    if (eventType.includes('failed') || eventType.includes('exceeded') || eventType.includes('malicious')) {
      logger.warn(`Request validation: ${eventType}`, logData);
    } else {
      logger.info(`Request validation: ${eventType}`, logData);
    }
  }
}

/**
 * Decorator for API routes that automatically applies request validation
 */
export function withRequestValidation(config: RequestValidationConfig = {}) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (request: NextRequest, ...args: any[]) {
      const validationResult = await RequestValidationMiddleware.validateRequest(request, config);
      
      // If validation failed, return the error response
      if ('success' in validationResult && !validationResult.success) {
        return validationResult;
      }
      
      if (validationResult instanceof NextResponse) {
        return validationResult;
      }

      const { context } = validationResult;
      
      try {
        // Call the original method with validation context
        return await originalMethod.call(this, request, context, ...args);
      } catch (error) {
        logger.error(`API route error in ${propertyKey}`, {
          error,
          path: request.nextUrl.pathname,
          method: request.method,
          clientIP: context.clientIP
        });
        
        throw error;
      }
    };

    return descriptor;
  };
}

export default RequestValidationMiddleware;