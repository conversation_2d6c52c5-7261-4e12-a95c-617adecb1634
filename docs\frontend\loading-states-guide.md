# Loading States Standardization Guide

## Overview
All loading states should use the unified loading components from `@/components/ui/unified-loading`.

## Available Components

### Basic Loading States
```tsx
import { Loading, PageLoading, SectionLoading, InlineLoading } from '@/components/ui/unified-loading'

// Basic loading spinner
<Loading size="md" variant="spinner" text="Loading..." />

// Full page loading
<PageLoading text="Loading dashboard..." />

// Section loading
<SectionLoading className="py-8" />

// Inline loading (for buttons, etc)
<InlineLoading />
```

### Skeleton Presets
```tsx
import { 
  SkeletonCard, 
  SkeletonList, 
  SkeletonTable, 
  SkeletonForm 
} from '@/components/ui/unified-loading'

// Card skeleton
<SkeletonCard className="w-full" />

// List skeleton
<SkeletonList items={5} />

// Table skeleton
<SkeletonTable rows={10} cols={4} />

// Form skeleton
<SkeletonForm fields={4} />
```

### Context-Specific Loaders
```tsx
import { 
  WritingLoading, 
  AILoading, 
  ProjectLoading, 
  CharacterLoading 
} from '@/components/ui/unified-loading'

// Writing-specific loading
<WritingLoading text="Saving your chapter..." />

// AI generation loading
<AILoading text="AI is generating content..." />

// Project loading
<ProjectLoading text="Loading project data..." />

// Character loading
<CharacterLoading text="Loading character profiles..." />
```

### Loading Button
```tsx
import { LoadingButton } from '@/components/ui/unified-loading'

<LoadingButton 
  isLoading={isSubmitting}
  loadingText="Saving..."
  variant="default"
  size="default"
>
  Save Changes
</LoadingButton>
```

## Migration Examples

### Before (using bare Skeleton):
```tsx
import { Skeleton } from '@/components/ui/skeleton'

export default function Loading() {
  return (
    <div>
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-4 w-64" />
    </div>
  )
}
```

### After (using unified components):
```tsx
import { PageLoading } from '@/components/ui/unified-loading'

export default function Loading() {
  return <PageLoading text="Loading..." />
}
```

## Best Practices

1. **Use semantic loading components**: Choose the right preset for your use case
2. **Provide loading text**: Always include descriptive text for better UX
3. **Match loading to content**: Use context-specific loaders (WritingLoading, AILoading, etc)
4. **Consistent sizing**: Use the size prop consistently across your app
5. **Accessibility**: Loading states are announced to screen readers

## Loading State Patterns

### Dashboard Loading
```tsx
export default function DashboardLoading() {
  return (
    <div className="container py-6 space-y-6">
      <SkeletonCard />
      <SkeletonTable rows={5} />
    </div>
  )
}
```

### Form Page Loading
```tsx
export default function SettingsLoading() {
  return (
    <div className="container py-6">
      <SkeletonForm fields={6} />
    </div>
  )
}
```

### List Page Loading
```tsx
export default function ProjectsLoading() {
  return (
    <div className="container py-6">
      <SkeletonList items={10} />
    </div>
  )
}
```
