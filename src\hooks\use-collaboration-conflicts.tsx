'use client'

import { useState, useEffect, useCallback } from 'react'
import { collaborationService, type CollaborationChange, type CollaborationEvent } from '@/lib/services/unified-collaboration-service'
import { toast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'

interface ConflictData {
  id: string
  changes: CollaborationChange[]
  userNames?: Map<string, string>
}

interface UseCollaborationConflictsProps {
  sessionId: string
  userId: string
  onConflictResolved?: (change: CollaborationChange) => void
}

export function useCollaborationConflicts({
  sessionId,
  userId,
  onConflictResolved
}: UseCollaborationConflictsProps) {
  const [conflicts, setConflicts] = useState<ConflictData[]>([])
  const [isResolving, setIsResolving] = useState(false)

  // Subscribe to collaboration events
  useEffect(() => {
    const handleCollaborationEvent = (event: CollaborationEvent) => {
      if (event.type === 'content.changed' && event.data.type === 'conflict') {
        // New conflicts detected
        const newConflicts = event.data.conflicts as ConflictData[]
        setConflicts(prev => [...prev, ...newConflicts])
        
        toast({
          title: "Editing Conflict Detected",
          description: `${newConflicts.length} conflict${newConflicts.length > 1 ? 's' : ''} need${newConflicts.length > 1 ? '' : 's'} resolution`,
          variant: "destructive"
        })
      }
    }

    const unsubscribe = collaborationService.subscribe(sessionId, handleCollaborationEvent)

    // Check for existing conflicts
    const existingConflicts = collaborationService.getPendingConflicts(sessionId)
    if (existingConflicts.length > 0) {
      setConflicts(existingConflicts as ConflictData[])
    }

    return () => {
      unsubscribe()
    }
  }, [sessionId])

  // Resolve a single conflict
  const resolveConflict = useCallback(async (conflictId: string, resolution: CollaborationChange) => {
    setIsResolving(true)
    try {
      collaborationService.resolveConflict(sessionId, conflictId, resolution)
      
      // Remove resolved conflict from state
      setConflicts(prev => prev.filter(c => c.id !== conflictId))
      
      // Notify callback
      if (onConflictResolved) {
        onConflictResolved(resolution)
      }
      
      toast({
        title: "Conflict Resolved",
        description: "The editing conflict has been resolved successfully"
      })
    } catch (error) {
      logger.error('Failed to resolve conflict', error)
      toast({
        title: "Resolution Failed",
        description: "Failed to resolve the conflict. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsResolving(false)
    }
  }, [sessionId, onConflictResolved])

  // Resolve all conflicts with a strategy
  const resolveAllConflicts = useCallback(async (strategy: 'mine' | 'theirs' | 'newest' | 'oldest') => {
    setIsResolving(true)
    try {
      const resolutions: Array<{ conflictId: string; change: CollaborationChange }> = []
      
      conflicts.forEach(conflict => {
        let selectedChange: CollaborationChange | undefined
        
        switch (strategy) {
          case 'mine':
            selectedChange = conflict.changes.find(c => c.userId === userId)
            break
          case 'theirs':
            selectedChange = conflict.changes.find(c => c.userId !== userId)
            break
          case 'newest':
            selectedChange = conflict.changes.reduce((newest, current) => 
              current.timestamp > newest.timestamp ? current : newest
            )
            break
          case 'oldest':
            selectedChange = conflict.changes.reduce((oldest, current) => 
              current.timestamp < oldest.timestamp ? current : oldest
            )
            break
        }
        
        if (selectedChange) {
          resolutions.push({ conflictId: conflict.id, change: selectedChange })
        }
      })
      
      // Apply all resolutions
      for (const { conflictId, change } of resolutions) {
        collaborationService.resolveConflict(sessionId, conflictId, change)
        if (onConflictResolved) {
          onConflictResolved(change)
        }
      }
      
      // Clear conflicts
      setConflicts([])
      
      toast({
        title: "All Conflicts Resolved",
        description: `${resolutions.length} conflicts have been resolved using the "${strategy}" strategy`
      })
    } catch (error) {
      logger.error('Failed to resolve all conflicts', error)
      toast({
        title: "Resolution Failed",
        description: "Failed to resolve conflicts. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsResolving(false)
    }
  }, [conflicts, sessionId, userId, onConflictResolved])

  // Set conflict resolution strategy
  const setResolutionStrategy = useCallback((strategy: 'merge' | 'last-write-wins' | 'first-write-wins' | 'manual') => {
    collaborationService.setConflictResolutionStrategy({ type: strategy })
    
    toast({
      title: "Strategy Updated",
      description: `Conflict resolution strategy set to "${strategy.replace('-', ' ')}"`
    })
  }, [])

  return {
    conflicts,
    isResolving,
    resolveConflict,
    resolveAllConflicts,
    setResolutionStrategy,
    hasConflicts: conflicts.length > 0
  }
}