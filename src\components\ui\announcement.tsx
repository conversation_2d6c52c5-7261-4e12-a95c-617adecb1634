import * as React from "react"
import { announce, ARIA_LIVE } from "@/lib/utils/accessibility-utils"

export interface AnnouncementProps {
  message: string
  politeness?: keyof typeof ARIA_LIVE
  announce?: boolean
}

/**
 * Announcement component for screen reader notifications
 * Use this to announce dynamic content changes to screen reader users
 */
export function Announcement({ 
  message, 
  politeness = "POLITE",
  announce: shouldAnnounce = true 
}: AnnouncementProps) {
  React.useEffect(() => {
    if (shouldAnnounce && message) {
      announce(message, politeness)
    }
  }, [message, politeness, shouldAnnounce])

  return null
}

export interface LiveRegionProps {
  children: React.ReactNode
  politeness?: keyof typeof ARIA_LIVE
  atomic?: boolean
  relevant?: "additions" | "removals" | "text" | "all"
  className?: string
}

/**
 * Live region component for dynamic content
 * Announces changes to screen readers automatically
 */
export function LiveRegion({ 
  children, 
  politeness = "POLITE",
  atomic = false,
  relevant = "additions text",
  className
}: LiveRegionProps) {
  return (
    <div
      role="status"
      aria-live={ARIA_LIVE[politeness]}
      aria-atomic={atomic}
      aria-relevant={relevant}
      className={className}
    >
      {children}
    </div>
  )
}

/**
 * Hook to announce messages to screen readers
 */
export function useAnnounce() {
  return React.useCallback((message: string, politeness: keyof typeof ARIA_LIVE = "POLITE") => {
    announce(message, politeness)
  }, [])
}