'use client'

import { useEffect, useState, useCallback } from 'react'
import { logger } from '@/lib/services/logger';

import { useParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { isDemoMode } from '@/lib/config/client-config'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import Link from 'next/link'
import { 
  ArrowLeft, 
  BookOpen, 
  FileText, 
  TrendingUp,
  Clock,
  CheckCircle2,
  AlertCircle,
  Edit3,
  BarChart3,
  Users,
  Map,
  GitBranch
} from 'lucide-react'
import { Database } from '@/lib/db/types'

type Project = Database['public']['Tables']['projects']['Row']
type Chapter = Database['public']['Tables']['chapters']['Row']
type StoryBible = Database['public']['Tables']['story_bible']['Row']

interface ChapterWithAnalysis extends Chapter {
  analysis?: {
    pacing: number
    tension: number
    characterCount: number
    sceneCount: number
  }
}

export default function BookOverviewPage() {
  const params = useParams()
  const projectId = params.id as string
  const [project, setProject] = useState<Project | null>(null)
  const [chapters, setChapters] = useState<ChapterWithAnalysis[]>([])
  const [storyBible, setStoryBible] = useState<StoryBible | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'timeline' | 'stats'>('grid')
  
  const supabase = createClient()

  const loadProjectData = useCallback(async () => {
    setIsLoading(true)
    try {
      // Check if demo mode
      if (isDemoMode() && projectId === 'demo-project') {
        // Use demo data
        setProject({
          id: 'demo-project',
          user_id: 'demo-user',
          title: 'The Crystal Kingdoms',
          description: 'A fantasy epic about a young mage discovering ancient crystal magic',
          primary_genre: 'Fantasy',
          subgenre: 'Epic Fantasy',
          custom_genre: null,
          narrative_voice: null,
          tense: null,
          tone_options: null,
          writing_style: null,
          custom_style_description: null,
          structure_type: null,
          pacing_preference: null,
          chapter_structure: null,
          timeline_complexity: null,
          custom_structure_notes: null,
          protagonist_types: null,
          antagonist_types: null,
          character_complexity: null,
          character_arc_types: null,
          custom_character_concepts: null,
          time_period: null,
          geographic_setting: null,
          world_type: null,
          magic_tech_level: null,
          custom_setting_description: null,
          major_themes: null,
          philosophical_themes: null,
          social_themes: null,
          custom_themes: null,
          target_audience: null,
          content_rating: null,
          research_needs: null,
          fact_checking_level: null,
          custom_research_notes: null,
          content_warnings: null,
          cultural_sensitivity_notes: null,
          project_scope: null,
          series_type: null,
          interconnection_level: null,
          custom_scope_description: null,
          target_word_count: 100000,
          current_word_count: 11500,
          target_chapters: null,
          chapter_count_type: null,
          pov_character_count: null,
          pov_character_type: null,
          initial_concept: null,
          status: 'writing',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
      } else {
        // Load from database
        const { data: projectData } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single()
        
        if (projectData) setProject(projectData)
      }

      // Load chapters with analysis
      if (isDemoMode() && projectId === 'demo-project') {
        // Use demo chapters
        const demoChapters = [
          { 
            id: '1', 
            project_id: 'demo-project', 
            chapter_number: 1, 
            title: "The Awakening", 
            target_word_count: 4000,
            actual_word_count: 3500, 
            outline: "Aria discovers her magical abilities at Aethermoor Academy",
            content: null,
            scenes_data: null,
            character_states: null,
            status: 'complete',
            ai_notes: null,
            pov_character: "Aria Stormwind",
            plot_advancement: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          { 
            id: '2', 
            project_id: 'demo-project', 
            chapter_number: 2, 
            title: "First Lessons", 
            target_word_count: 4000,
            actual_word_count: 4200, 
            outline: "Aria begins her formal magical training",
            content: null,
            scenes_data: null,
            character_states: null,
            status: 'complete',
            ai_notes: null,
            pov_character: "Aria Stormwind",
            plot_advancement: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          { 
            id: '3', 
            project_id: 'demo-project', 
            chapter_number: 3, 
            title: "The Prophecy", 
            target_word_count: 4000,
            actual_word_count: 3800, 
            outline: "The ancient crystal prophecy is revealed",
            content: null,
            scenes_data: null,
            character_states: null,
            status: 'writing',
            ai_notes: null,
            pov_character: "Aria Stormwind",
            plot_advancement: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          { 
            id: '4', 
            project_id: 'demo-project', 
            chapter_number: 4, 
            title: "Journey Begins", 
            target_word_count: 4000,
            actual_word_count: 0, 
            outline: "Aria sets out on her quest to find the crystal shards",
            content: null,
            scenes_data: null,
            character_states: null,
            status: 'planned',
            ai_notes: null,
            pov_character: "Aria Stormwind",
            plot_advancement: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
        ]
        const chaptersWithAnalysis = demoChapters.map(ch => ({
          ...ch,
          analysis: {
            pacing: Math.random() * 100,
            tension: Math.random() * 100,
            characterCount: Math.floor(Math.random() * 10) + 1,
            sceneCount: Math.floor(Math.random() * 5) + 1
          }
        }))
        setChapters(chaptersWithAnalysis)
      } else {
        const { data: chaptersData } = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId)
          .order('chapter_number')
        
        if (chaptersData) {
          // Add mock analysis data for demonstration
          const chaptersWithAnalysis = chaptersData.map(ch => ({
            ...ch,
            analysis: {
              pacing: Math.random() * 100,
              tension: Math.random() * 100,
              characterCount: Math.floor(Math.random() * 10) + 1,
              sceneCount: Math.floor(Math.random() * 5) + 1
            }
          }))
          setChapters(chaptersWithAnalysis)
        }
      }

      // Load story bible
      if (isDemoMode() && projectId === 'demo-project') {
        // Use demo story bible
        setStoryBible({
          id: 'demo-bible',
          project_id: 'demo-project',
          entry_type: 'character',
          entry_key: 'demo_characters',
          entry_data: {
            characters: [
              { type: 'character', name: 'Aria Stormwind', content: 'Young mage protagonist' },
              { type: 'location', name: 'Aethermoor Academy', content: 'Ancient magical school' },
              { type: 'plot', name: 'The Crystal Prophecy', content: 'Ancient prophecy about crystal magic' }
            ]
          },
          chapter_introduced: null,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
      } else {
        const { data: storyBibleData } = await supabase
          .from('story_bible')
          .select('*')
          .eq('project_id', projectId)
          .single()
        
        if (storyBibleData) setStoryBible(storyBibleData)
      }
      
    } catch (error) {
      logger.error('Error loading project data:', error);
    } finally {
      setIsLoading(false)
    }
  }, [projectId, supabase])

  useEffect(() => {
    loadProjectData()
  }, [loadProjectData])

  const getChapterStatus = (status: string) => {
    switch (status) {
      case 'complete':
        return { icon: CheckCircle2, color: 'text-success', bgColor: 'bg-success-light' }
      case 'writing':
        return { icon: Edit3, color: 'text-info', bgColor: 'bg-info-light' }
      case 'review':
        return { icon: AlertCircle, color: 'text-warning', bgColor: 'bg-warning-light' }
      default:
        return { icon: FileText, color: 'text-gray-500', bgColor: 'bg-gray-50' }
    }
  }

  const totalWordCount = chapters.reduce((sum, ch) => sum + (ch.actual_word_count || 0), 0)
  const averageChapterLength = chapters.length > 0 ? Math.round(totalWordCount / chapters.length) : 0
  const completedChapters = chapters.filter(ch => ch.status === 'complete').length
  const progressPercentage = project?.target_word_count ? (totalWordCount / project.target_word_count) * 100 : 0

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <BookOpen className="h-12 w-12 mx-auto mb-4 animate-pulse" />
          <p>Loading book overview...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">{project?.title || 'Book Overview'}</h1>
            <p className="text-muted-foreground">{project?.description}</p>
          </div>
        </div>
        <Link href={`/projects/${projectId}/write`}>
          <Button>
            <Edit3 className="h-4 w-4 mr-2" />
            Continue Writing
          </Button>
        </Link>
      </div>

      {/* Progress Overview */}
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Words</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalWordCount.toLocaleString()}</div>
            <Progress value={progressPercentage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {progressPercentage.toFixed(1)}% of {project?.target_word_count?.toLocaleString()} target
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chapters</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedChapters}/{chapters.length}</div>
            <p className="text-xs text-muted-foreground">
              {chapters.length - completedChapters} in progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Chapter</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageChapterLength.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">words per chapter</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Est. Completion</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {project?.target_chapters ? 
                Math.ceil((project.target_chapters - chapters.length) * 7) : '?'
              }
            </div>
            <p className="text-xs text-muted-foreground">days at current pace</p>
          </CardContent>
        </Card>
      </div>

      {/* View Tabs */}
      <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as 'grid' | 'timeline' | 'stats')} className="space-y-4">
        <TabsList>
          <TabsTrigger value="grid">Chapter Grid</TabsTrigger>
          <TabsTrigger value="timeline">Timeline View</TabsTrigger>
          <TabsTrigger value="stats">Analytics</TabsTrigger>
        </TabsList>

        {/* Grid View */}
        <TabsContent value="grid" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {chapters.map((chapter) => {
              const status = getChapterStatus(chapter.status || 'draft')
              const StatusIcon = status.icon
              
              return (
                <Card key={chapter.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">
                          Chapter {chapter.chapter_number}
                        </CardTitle>
                        <CardDescription>{chapter.title}</CardDescription>
                      </div>
                      <div className={`p-2 rounded-full ${status.bgColor}`}>
                        <StatusIcon className={`h-4 w-4 ${status.color}`} />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Words:</span>
                      <span className="font-medium">{chapter.actual_word_count?.toLocaleString() || 0}</span>
                    </div>
                    
                    {chapter.analysis && (
                      <>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span>Pacing</span>
                            <span>{chapter.analysis.pacing.toFixed(0)}%</span>
                          </div>
                          <Progress value={chapter.analysis.pacing} className="h-1" />
                        </div>
                        
                        <div className="flex gap-2 flex-wrap">
                          <Badge variant="secondary" className="text-xs">
                            <Users className="h-3 w-3 mr-1" />
                            {chapter.analysis.characterCount} characters
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            <Map className="h-3 w-3 mr-1" />
                            {chapter.analysis.sceneCount} scenes
                          </Badge>
                        </div>
                      </>
                    )}
                    
                    <div className="flex gap-2">
                      <Link href={`/projects/${projectId}/write?chapter=${chapter.id}`} className="flex-1">
                        <Button size="sm" variant="outline" className="w-full">
                          <Edit3 className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </Link>
                      <Button size="sm" variant="ghost">
                        <BarChart3 className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        {/* Timeline View */}
        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Story Timeline</CardTitle>
              <CardDescription>Visualize your book&apos;s progression and key events</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] pr-4">
                <div className="space-y-4">
                  {chapters.map((chapter, index) => (
                    <div key={chapter.id} className="flex gap-4">
                      <div className="flex flex-col items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                          chapter.status === 'complete' ? 'bg-success-light text-success' : 'bg-gray-100 text-gray-700'
                        }`}>
                          {chapter.chapter_number}
                        </div>
                        {index < chapters.length - 1 && (
                          <div className="w-0.5 h-20 bg-gray-200 mt-2" />
                        )}
                      </div>
                      <div className="flex-1 pb-8">
                        <h4 className="font-medium">{chapter.title || `Chapter ${chapter.chapter_number}`}</h4>
                        <p className="text-sm text-muted-foreground mt-1">
                          {chapter.outline || 'No outline available'}
                        </p>
                        <div className="flex gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {chapter.actual_word_count?.toLocaleString() || 0} words
                          </Badge>
                          <Badge variant={chapter.status === 'complete' ? 'default' : 'secondary'} className="text-xs">
                            {chapter.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Stats View */}
        <TabsContent value="stats" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Writing Velocity</CardTitle>
                <CardDescription>Track your writing pace over time</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <TrendingUp className="h-4 w-4" />
                  <AlertDescription>
                    You&apos;re averaging {Math.round(totalWordCount / chapters.length)} words per chapter
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Story Elements</CardTitle>
                <CardDescription>Overview of your book&apos;s components</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4" />
                    Characters
                  </span>
                  <Badge>{storyBible?.entry_data && typeof storyBible.entry_data === 'object' && !Array.isArray(storyBible.entry_data) && 'characters' in storyBible.entry_data && Array.isArray(storyBible.entry_data.characters) ? storyBible.entry_data.characters.length : 0}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="flex items-center gap-2 text-sm">
                    <Map className="h-4 w-4" />
                    Locations
                  </span>
                  <Badge>{storyBible?.entry_data && typeof storyBible.entry_data === 'object' ? Object.keys(storyBible.entry_data).filter(key => key.includes('location')).length : 0}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="flex items-center gap-2 text-sm">
                    <GitBranch className="h-4 w-4" />
                    Plot Threads
                  </span>
                  <Badge>{storyBible?.entry_data && typeof storyBible.entry_data === 'object' && !Array.isArray(storyBible.entry_data) && 'plotThreads' in storyBible.entry_data && Array.isArray(storyBible.entry_data.plotThreads) ? storyBible.entry_data.plotThreads.length : 0}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}