'use client'

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Mic, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Users,
  FileText
} from 'lucide-react'

interface VoiceMetric {
  character: string
  consistency: number
  trend: 'up' | 'down' | 'stable'
  samples: number
  lastAnalyzed?: string
}

interface VoiceConsistencyMetricsProps {
  projectId?: string
}

export function VoiceConsistencyMetrics({ projectId }: VoiceConsistencyMetricsProps) {
  const [metrics, setMetrics] = useState<VoiceMetric[]>([])
  const [overallConsistency, setOverallConsistency] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchVoiceMetrics()
  }, [projectId])

  const fetchVoiceMetrics = async () => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams()
      if (projectId) params.append('projectId', projectId)
      
      const response = await fetch(`/api/analysis/voice-consistency?${params}`)
      if (!response.ok) throw new Error('Failed to fetch voice metrics')
      
      const data = await response.json()
      const metricsData = data.metrics || []
      
      setMetrics(metricsData)
      
      if (metricsData.length > 0) {
        const avgConsistency = Math.round(
          metricsData.reduce((sum: number, m: VoiceMetric) => sum + m.consistency, 0) / metricsData.length
        )
        setOverallConsistency(avgConsistency)
      } else {
        setOverallConsistency(0)
      }
    } catch (error) {
      logger.error('Failed to fetch voice metrics:', error)
      // If the API is not available, show empty state instead of mock data
      setMetrics([])
      setOverallConsistency(0)
    } finally {
      setLoading(false)
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-success" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-error" />
      default:
        return <Minus className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getConsistencyColor = (score: number) => {
    if (score >= 90) return 'text-success'
    if (score >= 80) return 'text-info'
    if (score >= 70) return 'text-warning'
    return 'text-error'
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-20 w-full" />
        {[1, 2, 3].map((i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <div className="text-center p-6 bg-muted rounded-lg">
        <Mic className="h-8 w-8 mx-auto mb-2 text-primary" />
        <div className="text-3xl font-bold mb-1">
          <span className={getConsistencyColor(overallConsistency)}>
            {overallConsistency}%
          </span>
        </div>
        <p className="text-sm text-muted-foreground">Overall Voice Consistency</p>
      </div>

      {/* Character Metrics */}
      <div className="space-y-4">
        <h4 className="font-medium flex items-center gap-2">
          <Users className="h-4 w-4" />
          Character Voice Analysis
        </h4>
        
        {metrics.map((metric) => (
          <div key={metric.character} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium">{metric.character}</span>
                {getTrendIcon(metric.trend)}
              </div>
              <div className="flex items-center gap-2">
                <span className={`font-semibold ${getConsistencyColor(metric.consistency)}`}>
                  {metric.consistency}%
                </span>
                <Badge variant="outline" className="text-xs">
                  <FileText className="h-3 w-3 mr-1" />
                  {metric.samples} samples
                </Badge>
              </div>
            </div>
            <Progress value={metric.consistency} className="h-2" />
            {metric.lastAnalyzed && (
              <p className="text-xs text-muted-foreground">
                Last analyzed: {metric.lastAnalyzed}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Tips */}
      <div className="p-4 bg-primary/5 rounded-lg">
        <p className="text-sm">
          <strong>Tip:</strong> Voice consistency above 85% indicates strong character 
          differentiation. Use the Voice Analyzer in your editor to improve scores.
        </p>
      </div>
    </div>
  )
}