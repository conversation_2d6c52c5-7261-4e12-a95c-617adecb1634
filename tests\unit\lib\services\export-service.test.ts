import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { ExportService } from '@/lib/services/export-service';
import { createClient } from '@/lib/supabase/server';
import * as fs from 'fs/promises';
import * as path from 'path';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/services/logger');
jest.mock('fs/promises');
jest.mock('archiver');
jest.mock('docx');
jest.mock('epub-gen');

describe('ExportService', () => {
  let mockSupabase: any;
  let exportService: ExportService;

  beforeEach(() => {
    jest.clearAllMocks();

    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      single: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      storage: {
        from: jest.fn().mockReturnValue({
          upload: jest.fn(),
          getPublicUrl: jest.fn(),
          remove: jest.fn(),
        }),
      },
    };

    (createClient as jest.Mock).mockReturnValue(mockSupabase);
    exportService = new ExportService();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('exportProject', () => {
    const mockProject = {
      id: 'project-123',
      title: 'Test Novel',
      description: 'A test novel for export',
      settings: { primaryGenre: 'fantasy' },
    };

    const mockChapters = [
      { id: '1', title: 'Chapter 1', content: 'Content 1', order_index: 1 },
      { id: '2', title: 'Chapter 2', content: 'Content 2', order_index: 2 },
    ];

    beforeEach(() => {
      mockSupabase.single.mockResolvedValue({ data: mockProject, error: null });
      mockSupabase.select.mockResolvedValue({ data: mockChapters, error: null });
    });

    it('should export project as markdown', async () => {
      const mockWriteFile = fs.writeFile as jest.MockedFunction<typeof fs.writeFile>;
      mockWriteFile.mockResolvedValue(undefined);

      const result = await exportService.exportProject('project-123', 'markdown');

      expect(result.format).toBe('markdown');
      expect(result.status).toBe('completed');
      expect(mockWriteFile).toHaveBeenCalledWith(
        expect.stringContaining('.md'),
        expect.stringContaining('# Test Novel'),
        'utf-8'
      );
    });

    it('should export project as DOCX', async () => {
      const { Document, Packer, Paragraph } = await import('docx');
      const mockPacker = {
        toBuffer: jest.fn().mockResolvedValue(Buffer.from('docx content')),
      };
      (Packer as any) = mockPacker;

      const result = await exportService.exportProject('project-123', 'docx');

      expect(result.format).toBe('docx');
      expect(result.status).toBe('completed');
      expect(mockPacker.toBuffer).toHaveBeenCalled();
    });

    it('should export project as EPUB', async () => {
      const mockEpubGen = (await import('epub-gen')).default;
      (mockEpubGen as any).mockImplementation((options: any, outputPath: string) => {
        return Promise.resolve();
      });

      const result = await exportService.exportProject('project-123', 'epub');

      expect(result.format).toBe('epub');
      expect(result.status).toBe('completed');
      expect(mockEpubGen).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Novel',
          author: expect.any(String),
          content: expect.any(Array),
        }),
        expect.stringContaining('.epub')
      );
    });

    it('should export project as PDF', async () => {
      // Mock PDF generation (using puppeteer or similar)
      const mockGeneratePDF = jest.fn().mockResolvedValue(Buffer.from('pdf content'));
      exportService.generatePDF = mockGeneratePDF;

      const result = await exportService.exportProject('project-123', 'pdf');

      expect(result.format).toBe('pdf');
      expect(result.status).toBe('completed');
      expect(mockGeneratePDF).toHaveBeenCalled();
    });

    it('should handle export errors gracefully', async () => {
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: new Error('Project not found'),
      });

      await expect(exportService.exportProject('invalid-id', 'markdown'))
        .rejects.toThrow('Project not found');
    });
  });

  describe('batch export', () => {
    it('should export multiple chapters efficiently', async () => {
      const manyChapters = Array(100).fill(null).map((_, i) => ({
        id: `ch-${i}`,
        title: `Chapter ${i + 1}`,
        content: `Content for chapter ${i + 1}`,
        order_index: i + 1,
      }));

      mockSupabase.select.mockResolvedValue({ data: manyChapters, error: null });

      const result = await exportService.exportChapters('project-123', 'markdown');

      expect(result.chapters).toBe(100);
      expect(result.status).toBe('completed');
    });

    it('should respect memory limits during export', async () => {
      const largeContent = 'x'.repeat(10 * 1024 * 1024); // 10MB per chapter
      const largeChapters = Array(10).fill(null).map((_, i) => ({
        id: `ch-${i}`,
        title: `Chapter ${i + 1}`,
        content: largeContent,
        order_index: i + 1,
      }));

      mockSupabase.select.mockResolvedValue({ data: largeChapters, error: null });

      // Should process in chunks to avoid memory issues
      const result = await exportService.exportChapters('project-123', 'markdown', {
        chunkSize: 5,
      });

      expect(result.status).toBe('completed');
      expect(result.processedInChunks).toBe(true);
    });
  });

  describe('export formats', () => {
    it('should include metadata in exports', async () => {
      const project = {
        id: 'project-123',
        title: 'Test Novel',
        author: 'Test Author',
        copyright: '© 2024 Test Author',
        metadata: {
          isbn: '978-0-12345-678-9',
          publisher: 'Test Publisher',
          language: 'en',
        },
      };

      mockSupabase.single.mockResolvedValue({ data: project, error: null });

      const exported = await exportService.exportWithMetadata('project-123', 'epub');

      expect(exported.metadata).toEqual(expect.objectContaining({
        isbn: '978-0-12345-678-9',
        publisher: 'Test Publisher',
      }));
    });

    it('should format markdown with proper styling', async () => {
      const chapters = [
        {
          id: '1',
          title: 'The Beginning',
          content: 'It was a **dark** and *stormy* night.',
        },
      ];

      const markdown = await exportService.formatAsMarkdown(chapters);

      expect(markdown).toContain('# The Beginning');
      expect(markdown).toContain('**dark**');
      expect(markdown).toContain('*stormy*');
    });

    it('should preserve formatting in DOCX export', async () => {
      const richContent = {
        paragraphs: [
          { text: 'Normal text', style: 'normal' },
          { text: 'Bold text', style: 'bold' },
          { text: 'Italic text', style: 'italic' },
        ],
      };

      const docx = await exportService.formatAsDocx([richContent]);

      expect(docx).toBeDefined();
      // Would check actual DOCX formatting here
    });
  });

  describe('export queue', () => {
    it('should queue exports for large projects', async () => {
      const jobId = await exportService.queueExport('project-123', 'pdf', 'user-123');

      expect(jobId).toBeDefined();
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          project_id: 'project-123',
          format: 'pdf',
          status: 'queued',
          user_id: 'user-123',
        })
      );
    });

    it('should process queued exports in order', async () => {
      const queuedJobs = [
        { id: 'job-1', created_at: '2024-01-01T10:00:00Z' },
        { id: 'job-2', created_at: '2024-01-01T10:01:00Z' },
        { id: 'job-3', created_at: '2024-01-01T10:02:00Z' },
      ];

      mockSupabase.select.mockResolvedValue({ data: queuedJobs, error: null });

      const nextJob = await exportService.getNextQueuedJob();

      expect(nextJob.id).toBe('job-1'); // Oldest first
    });

    it('should update job status during processing', async () => {
      const jobId = 'job-123';

      await exportService.updateJobStatus(jobId, 'processing');
      expect(mockSupabase.update).toHaveBeenCalledWith({ status: 'processing' });

      await exportService.updateJobStatus(jobId, 'completed', { url: 'https://...' });
      expect(mockSupabase.update).toHaveBeenCalledWith({
        status: 'completed',
        completed_at: expect.any(String),
        result: { url: 'https://...' },
      });
    });
  });

  describe('file storage', () => {
    it('should upload exported files to storage', async () => {
      const file = Buffer.from('exported content');
      const filename = 'export.pdf';

      mockSupabase.storage.from().upload.mockResolvedValue({
        data: { path: `exports/${filename}` },
        error: null,
      });

      mockSupabase.storage.from().getPublicUrl.mockReturnValue({
        data: { publicUrl: 'https://storage.example.com/exports/export.pdf' },
      });

      const url = await exportService.uploadExport(file, filename, 'project-123');

      expect(url).toBe('https://storage.example.com/exports/export.pdf');
      expect(mockSupabase.storage.from).toHaveBeenCalledWith('exports');
    });

    it('should clean up old exports', async () => {
      const oldExports = [
        { name: 'old-export-1.pdf', created_at: '2023-01-01' },
        { name: 'old-export-2.epub', created_at: '2023-01-01' },
      ];

      mockSupabase.storage.from().list.mockResolvedValue({
        data: oldExports,
        error: null,
      });

      await exportService.cleanupOldExports(30); // 30 days

      expect(mockSupabase.storage.from().remove).toHaveBeenCalledWith([
        'old-export-1.pdf',
        'old-export-2.epub',
      ]);
    });
  });

  describe('import functionality', () => {
    it('should import markdown files', async () => {
      const markdownContent = `# Chapter 1
      
This is the first chapter.

# Chapter 2

This is the second chapter.`;

      const imported = await exportService.importMarkdown(markdownContent);

      expect(imported.chapters).toHaveLength(2);
      expect(imported.chapters[0].title).toBe('Chapter 1');
      expect(imported.chapters[1].title).toBe('Chapter 2');
    });

    it('should import DOCX files', async () => {
      const mockDocxContent = {
        paragraphs: [
          { text: 'Chapter 1', isHeading: true },
          { text: 'Content for chapter 1' },
          { text: 'Chapter 2', isHeading: true },
          { text: 'Content for chapter 2' },
        ],
      };

      const imported = await exportService.importDocx(Buffer.from('docx'));

      expect(imported.chapters).toBeDefined();
      expect(imported.chapters.length).toBeGreaterThan(0);
    });

    it('should validate imported content', async () => {
      const invalidContent = 'Not a valid format';

      await expect(exportService.importProject(invalidContent, 'unknown'))
        .rejects.toThrow('Unsupported import format');
    });
  });

  describe('export customization', () => {
    it('should apply custom templates', async () => {
      const template = {
        header: '<!-- Custom Header -->',
        footer: '<!-- Custom Footer -->',
        chapterTemplate: '## {{title}}\n\n{{content}}\n\n---',
      };

      const result = await exportService.exportWithTemplate(
        'project-123',
        'markdown',
        template
      );

      expect(result.content).toContain('<!-- Custom Header -->');
      expect(result.content).toContain('<!-- Custom Footer -->');
    });

    it('should support export filtering', async () => {
      const filter = {
        chaptersOnly: ['1', '3', '5'], // Export specific chapters
        includeMetadata: false,
        includeCharacters: true,
      };

      const result = await exportService.exportWithFilter('project-123', 'markdown', filter);

      expect(result.chapters).toHaveLength(3);
      expect(result.includesCharacters).toBe(true);
    });
  });
});