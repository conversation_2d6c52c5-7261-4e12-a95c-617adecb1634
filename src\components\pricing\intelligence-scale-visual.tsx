'use client'

import { Book, Library, Building2, Globe2, Infinity } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

const scaleItems = [
  {
    tier: 'Starter',
    icon: Book,
    label: 'Single Story',
    description: 'Perfect for novellas & short stories',
    size: 'w-16 h-16',
    color: 'text-success dark:text-green-400'
  },
  {
    tier: 'Writer',
    icon: Library,
    label: 'Novel Length',
    description: 'Handles full-length novels with ease',
    size: 'w-20 h-20',
    color: 'text-info dark:text-blue-400'
  },
  {
    tier: 'Author',
    icon: Building2,
    label: 'Multiple Books',
    description: 'Manages trilogies & series',
    size: 'w-24 h-24',
    color: 'text-purple-600 dark:text-purple-400'
  },
  {
    tier: 'Professional',
    icon: Globe2,
    label: 'Epic Series',
    description: 'Powers expansive story worlds',
    size: 'w-28 h-28',
    color: 'text-warning dark:text-orange-400'
  },
  {
    tier: 'Studio',
    icon: Infinity,
    label: 'Unlimited Scope',
    description: 'No limits on complexity',
    size: 'w-32 h-32',
    color: 'text-primary'
  }
]

export function IntelligenceScaleVisual() {
  return (
    <div className="py-12">
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold font-literary-display mb-2">
          Intelligence That Scales With Your Vision
        </h3>
        <p className="text-muted-foreground">
          Our AI grows more sophisticated as your stories become more complex
        </p>
      </div>

      <div className="flex flex-wrap items-end justify-center gap-8 px-4">
        {scaleItems.map((item, index) => {
          const Icon = item.icon
          return (
            <div key={item.tier} className="flex flex-col items-center space-y-3">
              <div className="relative group">
                <div className={`${item.size} ${item.color} transition-all duration-300 group-hover:scale-110`}>
                  <Icon className="w-full h-full" strokeWidth={1.5} />
                </div>
                <Badge 
                  variant="outline" 
                  className="absolute -top-2 -right-2 text-xs"
                >
                  {item.tier}
                </Badge>
              </div>
              <div className="text-center space-y-1">
                <p className="font-semibold">{item.label}</p>
                <p className="text-xs text-muted-foreground max-w-[150px]">
                  {item.description}
                </p>
              </div>
            </div>
          )
        })}
      </div>

      <div className="mt-8 flex items-center justify-center">
        <div className="h-1 w-full max-w-3xl bg-gradient-to-r from-green-600 via-blue-600 via-purple-600 via-orange-600 to-primary rounded-full" />
      </div>
    </div>
  )
}