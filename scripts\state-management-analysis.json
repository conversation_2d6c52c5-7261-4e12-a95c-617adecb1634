{"timestamp": "2025-08-01T23:19:47.227Z", "summary": {"totalContexts": 5, "totalStores": 21, "migrationCandidates": 3, "keepAsContext": 2}, "contexts": {"migrate": [{"file": "src\\components\\error\\error-reporting.tsx", "name": "error-reporting.tsx", "hasGlobalState": false, "hasComplexLogic": true, "isAuthRelated": false, "isThemeProvider": false, "isFormContext": false, "recommendation": "Migrate to <PERSON><PERSON><PERSON> (Complex logic)"}, {"file": "src\\hooks\\use-error-handling.tsx", "name": "use-error-handling.tsx", "hasGlobalState": false, "hasComplexLogic": true, "isAuthRelated": false, "isThemeProvider": false, "isFormContext": false, "recommendation": "Migrate to <PERSON><PERSON><PERSON> (Complex logic)"}, {"file": "src\\lib\\panels\\panel-context.tsx", "name": "panel-context.tsx", "hasGlobalState": false, "hasComplexLogic": true, "isAuthRelated": false, "isThemeProvider": false, "isFormContext": false, "recommendation": "Migrate to <PERSON><PERSON><PERSON> (Complex logic)"}], "keep": [{"file": "src\\contexts\\auth-context.tsx", "name": "auth-context.tsx", "hasGlobalState": false, "hasComplexLogic": false, "isAuthRelated": true, "isThemeProvider": false, "isFormContext": false, "recommendation": "Keep as Context (Auth wrapper)"}, {"file": "src\\contexts\\celebration-context.tsx", "name": "celebration-context.tsx", "hasGlobalState": false, "hasComplexLogic": false, "isAuthRelated": true, "isThemeProvider": false, "isFormContext": false, "recommendation": "Keep as Context (Auth wrapper)"}]}, "stores": {"files": ["src\\app\\api\\analysis\\auto-fix\\route.ts", "src\\app\\api\\billing\\payments\\charge\\route.ts", "src\\app\\api\\billing\\payments\\create-payment-intent\\route.ts", "src\\app\\api\\billing\\subscriptions\\checkout\\route.ts", "src\\app\\api\\billing\\subscriptions\\portal\\route.ts", "src\\app\\api\\billing\\subscriptions\\route.ts", "src\\app\\api\\demo\\generate-story\\route.ts", "src\\app\\api\\profiles\\route.ts", "src\\app\\api\\projects\\route.ts", "src\\lib\\db\\repositories\\base-repository.ts", "src\\lib\\db\\repositories\\chapter-repository.ts", "src\\lib\\db\\repositories\\project-repository.ts", "src\\lib\\openai.ts", "src\\lib\\project-templates.ts", "src\\lib\\services\\streaming-handler.ts", "src\\lib\\settings\\settings-store.ts", "src\\lib\\themes\\custom-themes-store.ts", "src\\stores\\editor-store.ts", "src\\stores\\project-store.ts"], "patterns": {"hasDevtools": 0, "hasPersist": 3, "hasImmer": 0, "hasSelectors": 3, "hasAsyncActions": 18}}, "recommendations": [{"priority": "high", "task": "Migrate global state contexts to Zustand", "files": []}, {"priority": "medium", "task": "Standardize Zustand store patterns", "details": "Add devtools, persist, and TypeScript types to all stores"}, {"priority": "low", "task": "Create store documentation", "details": "Document store interfaces and usage patterns"}]}