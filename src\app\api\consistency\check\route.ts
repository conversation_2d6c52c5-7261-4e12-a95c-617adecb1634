import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { ConsistencyValidator } from '@/lib/services/consistency-validator'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

const ConsistencyCheckSchema = z.object({
  projectId: z.string(),
  chapterId: z.string().optional(),
  content: z.string().optional(),
  checkType: z.enum(['chapter', 'book', 'timeline', 'characters']).optional(),
})

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body = await request.json()
    const { projectId, chapterId, content, checkType = 'chapter' } = ConsistencyCheckSchema.parse(body)

    const supabase = await createTypedServerClient()

    // Verify project ownership
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*, chapters(*)')
      .eq('id', projectId)
      .eq('user_id', authResult.user.id)
      .single()

    if (projectError || !project) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Initialize consistency validator
    const validator = new ConsistencyValidator()
    await validator.initialize()

    // Build book context from project data
    const bookContext = {
      projectId,
      settings: project.project_settings || {},
      projectSelections: project.project_selections || {},
      storyPrompt: project.story_prompt,
      targetWordCount: project.target_word_count,
      targetChapters: project.target_chapters,
      currentChapters: project.chapters || [],
      characters: project.metadata?.characters || {},
      worldBuilding: project.metadata?.worldBuilding || {},
      timeline: project.metadata?.timeline || [],
    }

    // Perform consistency check based on type
    let consistencyReport
    
    switch (checkType) {
      case 'chapter':
        if (!chapterId || !content) {
          return handleAPIError(new ValidationError('Invalid request'))
        }
        consistencyReport = await validator.validateChapterConsistency(
          { content, chapterNumber: project.chapters.findIndex((c: any) => c.id === chapterId) + 1 },
          bookContext
        )
        break
        
      case 'book':
        consistencyReport = await validator.validateBookConsistency(bookContext)
        break
        
      case 'timeline':
        consistencyReport = await validator.validateTimelineConsistency(bookContext)
        break
        
      case 'characters':
        consistencyReport = await validator.validateCharacterConsistency(bookContext)
        break
        
      default:
        return handleAPIError(new ValidationError('Invalid request'))
    }

    if (!consistencyReport.success) {
      throw new Error(consistencyReport.error || 'Consistency check failed')
    }

    // Save consistency report
    const { error: insertError } = await supabase
      .from('consistency_reports')
      .insert({
        user_id: authResult.user.id,
        project_id: projectId,
        chapter_id: chapterId,
        check_type: checkType,
        overall_score: consistencyReport.data?.overallScore || 0,
        issues: consistencyReport.data?.issues || [],
        character_consistency: consistencyReport.data?.characterConsistency || 0,
        timeline_consistency: consistencyReport.data?.timelineConsistency || 0,
        plot_consistency: consistencyReport.data?.plotConsistency || 0,
        world_consistency: consistencyReport.data?.worldConsistency || 0,
        style_consistency: consistencyReport.data?.styleConsistency || 0,
      })

    if (insertError) {
      logger.error('Error saving consistency report:', insertError)
    }

    return NextResponse.json({
      success: true,
      report: consistencyReport.data,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleRouteError(error, 'Consistency Check')
  }
}