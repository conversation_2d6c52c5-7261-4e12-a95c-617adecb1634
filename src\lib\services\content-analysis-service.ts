import { AIServiceBase, AIGenerationOptions } from './ai-service-base'
import { ServiceResponse } from './types'
import { z } from 'zod'
import { AI_MODELS, AI_TEMPERATURE } from '../config/ai-settings'

// Analysis result schemas
const grammarSuggestionSchema = z.object({
  id: z.string(),
  type: z.literal('grammar'),
  severity: z.enum(['error', 'warning', 'suggestion']),
  message: z.string(),
  range: z.object({
    startLineNumber: z.number(),
    startColumn: z.number(),
    endLineNumber: z.number(),
    endColumn: z.number()
  }),
  replacement: z.string().optional(),
  explanation: z.string()
})

const styleSuggestionSchema = z.object({
  id: z.string(),
  type: z.literal('style'),
  severity: z.enum(['error', 'warning', 'suggestion']),
  message: z.string(),
  range: z.object({
    startLineNumber: z.number(),
    startColumn: z.number(),
    endLineNumber: z.number(),
    endColumn: z.number()
  }),
  replacement: z.string().optional(),
  explanation: z.string()
})

const characterSuggestionSchema = z.object({
  id: z.string(),
  type: z.literal('character'),
  severity: z.enum(['error', 'warning', 'suggestion']),
  message: z.string(),
  range: z.object({
    startLineNumber: z.number(),
    startColumn: z.number(),
    endLineNumber: z.number(),
    endColumn: z.number()
  }),
  explanation: z.string()
})

const plotSuggestionSchema = z.object({
  id: z.string(),
  type: z.literal('plot'),
  severity: z.enum(['error', 'warning', 'suggestion']),
  message: z.string(),
  range: z.object({
    startLineNumber: z.number(),
    startColumn: z.number(),
    endLineNumber: z.number(),
    endColumn: z.number()
  }),
  explanation: z.string()
})

const pacingSuggestionSchema = z.object({
  id: z.string(),
  type: z.literal('pacing'),
  severity: z.enum(['error', 'warning', 'suggestion']),
  message: z.string(),
  range: z.object({
    startLineNumber: z.number(),
    startColumn: z.number(),
    endLineNumber: z.number(),
    endColumn: z.number()
  }),
  explanation: z.string()
})

const dialogueSuggestionSchema = z.object({
  id: z.string(),
  type: z.literal('dialogue'),
  severity: z.enum(['error', 'warning', 'suggestion']),
  message: z.string(),
  range: z.object({
    startLineNumber: z.number(),
    startColumn: z.number(),
    endLineNumber: z.number(),
    endColumn: z.number()
  }),
  explanation: z.string()
})

const suggestionSchema = z.union([
  grammarSuggestionSchema,
  styleSuggestionSchema,
  characterSuggestionSchema,
  plotSuggestionSchema,
  pacingSuggestionSchema,
  dialogueSuggestionSchema
])

const voiceProfileSchema = z.object({
  tone: z.string(),
  style: z.string(),
  complexity: z.string(),
  vocabulary: z.string()
})

const analysisResultSchema = z.object({
  suggestions: z.array(suggestionSchema),
  voiceProfile: voiceProfileSchema
})

export type AnalysisType = 'grammar' | 'style' | 'character' | 'plot' | 'pacing' | 'dialogue'
export type Suggestion = z.infer<typeof suggestionSchema>
export type VoiceProfile = z.infer<typeof voiceProfileSchema>
export type AnalysisResult = z.infer<typeof analysisResultSchema>

export class ContentAnalysisService extends AIServiceBase {
  private static instance: ContentAnalysisService

  constructor() {
    super({
      name: 'ContentAnalysisService',
      version: '1.0.0',
      endpoints: ['/api/analysis'],
      dependencies: ['ai-service-base'],
      healthCheck: '/api/health'
    })
  }

  static getInstance(): ContentAnalysisService {
    if (!ContentAnalysisService.instance) {
      ContentAnalysisService.instance = new ContentAnalysisService()
    }
    return ContentAnalysisService.instance
  }

  async analyzeContent(
    content: string,
    analysisTypes: AnalysisType[] = ['grammar', 'style'],
    projectContext?: {
      genre?: string
      targetAudience?: string
      writingStyle?: string
    }
  ): Promise<ServiceResponse<AnalysisResult>> {
    // Split content into lines for position tracking
    const lines = content.split('\n')
    
    // Build the analysis prompt
    const systemPrompt = this.buildAnalysisPrompt(analysisTypes, projectContext)
    
    const prompt = `Analyze the following text for ${analysisTypes.join(', ')} issues. 
For each issue found:
- Provide a unique ID
- Specify the type (${analysisTypes.join(', ')})
- Set severity (error for critical issues, warning for important issues, suggestion for improvements)
- Provide a clear message about the issue
- Include the exact line and column range where the issue occurs
- Provide a detailed explanation
- For grammar and style issues, suggest a replacement if applicable

Also analyze the overall voice profile of the text, identifying:
- Tone (e.g., formal, casual, dramatic, humorous)
- Style (e.g., descriptive, concise, flowery, direct)
- Complexity (e.g., simple, moderate, complex)
- Vocabulary (e.g., basic, intermediate, advanced, specialized)

Text to analyze:
"""
${content}
"""

Remember to provide specific line and column numbers based on the text structure above.`

    const options: AIGenerationOptions = {
      model: AI_MODELS.PRIMARY,
      temperature: AI_TEMPERATURE.DETERMINISTIC,
      maxTokens: 3000,
      systemPrompt,
      responseFormat: 'structured',
      structuredSchema: analysisResultSchema
    }

    const result = await this.generateWithAI<AnalysisResult>(prompt, options)
    
    if (!result) {
      return {
        suggestions: [],
        voiceProfile: {
          tone: 'unknown',
          style: 'unknown',
          complexity: 'unknown',
          vocabulary: 'unknown'
        }
      }
    }

    // Validate and adjust ranges if needed
    const validatedSuggestions = result.suggestions.map(suggestion => {
      const range = suggestion.range
      const maxLine = lines.length
      const lineLength = lines[Math.min(range.startLineNumber - 1, maxLine - 1)]?.length || 0
      
      return {
        ...suggestion,
        range: {
          startLineNumber: Math.max(1, Math.min(range.startLineNumber, maxLine)),
          startColumn: Math.max(1, Math.min(range.startColumn, lineLength)),
          endLineNumber: Math.max(1, Math.min(range.endLineNumber, maxLine)),
          endColumn: Math.max(1, Math.min(range.endColumn, lineLength))
        }
      }
    })

    return {
      suggestions: validatedSuggestions,
      voiceProfile: result.voiceProfile
    }
  }

  private buildAnalysisPrompt(
    analysisTypes: AnalysisType[],
    projectContext?: {
      genre?: string
      targetAudience?: string
      writingStyle?: string
    }
  ): string {
    let prompt = `You are an expert writing coach and editor with deep expertise in creative writing, linguistics, and narrative craft.`
    
    if (projectContext) {
      if (projectContext.genre) {
        prompt += ` You specialize in ${projectContext.genre} fiction.`
      }
      if (projectContext.targetAudience) {
        prompt += ` The target audience is ${projectContext.targetAudience}.`
      }
      if (projectContext.writingStyle) {
        prompt += ` The desired writing style is ${projectContext.writingStyle}.`
      }
    }

    prompt += '\n\nAnalysis Focus:\n'

    const analysisDescriptions: Record<AnalysisType, string> = {
      grammar: `Grammar Analysis: Check for grammatical errors including:
- Subject-verb agreement
- Tense consistency
- Punctuation errors
- Sentence fragments or run-ons
- Pronoun clarity
- Parallel structure`,
      
      style: `Style Analysis: Evaluate writing style for:
- Sentence variety and flow
- Word choice and repetition
- Active vs passive voice usage
- Clarity and conciseness
- Tone consistency
- Show vs tell balance`,
      
      character: `Character Analysis: Assess character elements:
- Character voice consistency
- Motivation clarity
- Character actions matching their established traits
- Dialogue authenticity
- Character development and growth
- Relationship dynamics`,
      
      plot: `Plot Analysis: Examine story structure:
- Plot holes or inconsistencies
- Cause and effect logic
- Conflict development
- Tension and pacing
- Foreshadowing effectiveness
- Resolution satisfaction`,
      
      pacing: `Pacing Analysis: Review narrative rhythm:
- Scene length appropriateness
- Action/reflection balance
- Tension escalation
- Chapter hooks and cliffhangers
- Narrative momentum
- Reader engagement maintenance`,
      
      dialogue: `Dialogue Analysis: Evaluate conversations:
- Natural speech patterns
- Character voice distinction
- Subtext and implication
- Dialogue tags effectiveness
- Balance with narrative
- Purpose and advancement of plot`
    }

    analysisTypes.forEach(type => {
      if (analysisDescriptions[type]) {
        prompt += '\n' + analysisDescriptions[type] + '\n'
      }
    })

    return prompt
  }

  async assessWritingQuality(content: string): Promise<ServiceResponse<number>> {
    return this.assessQuality(content, 'writing', {
      'vivid': 5,
      'engaging': 5,
      'clear': 5,
      'original': 10
    })
  }
}

// Export singleton instance
export const contentAnalysisService = ContentAnalysisService.getInstance()