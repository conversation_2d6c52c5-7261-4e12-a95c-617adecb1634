import { TIME_MS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

/**
 * AI Configuration Settings
 * Centralized configuration for all AI-related settings
 */

export const AI_MODELS = {
  // Primary models for different tasks
  PRIMARY: 'gpt-4.1',  // Latest GPT-4.1 model (July 2025) - 1M token context
  MEDIUM: 'gpt-4.1-mini',  // Mid-tier model - 80% cheaper than GPT-4.1
  FAST: 'gpt-4o-mini',  // Budget model - 92.5% cheaper than GPT-4.1
  FAST_REASONING: 'gpt-4o-mini',  // For reasoning-heavy fast operations (o4-mini not yet available)
  
  // Embedding models for semantic search and recommendations
  EMBEDDING: 'text-embedding-3-small',  // $0.00002/1K tokens - extremely cost effective
  EMBEDDING_LARGE: 'text-embedding-3-large',  // $0.00013/1K tokens - premium accuracy

  // xAI models
  XAI_PRIMARY: 'grok-3',  // Grok model for creative writing and reasoning
  XAI_VISION: 'grok-vision-beta',  // Grok with vision capabilities
  XAI_FAST: 'grok-3-mini',  // Fast Grok model for quick operations

  // Alternative models (fallbacks)
  ALTERNATIVE_PRIMARY: 'gpt-4.1',  // Primary fallback
  ALTERNATIVE_MEDIUM: 'gpt-4.1-mini',  // Medium fallback  
  ALTERNATIVE_FAST: 'gpt-4o-mini',  // Fast fallback
  LEGACY_PRIMARY: 'gpt-4o',  // Legacy for compatibility
  LEGACY_FAST: 'gpt-4o-mini',  // Legacy fast model

  // Model selection based on task complexity and cost optimization
  TASKS: {
    // High complexity tasks - Use GPT-4.1 for best quality
    STORY_STRUCTURE: 'gpt-4.1',  // Complex narrative structure creation
    CHARACTER_DEVELOPMENT: 'gpt-4.1',  // Deep character profiles & arcs
    CHAPTER_WRITING: 'gpt-4.1',  // Creative long-form content
    CREATIVE_WRITING: 'gpt-4.1',  // High-quality creative content
    DIALOGUE_GENERATION: 'gpt-4.1',  // Natural, character-specific dialogue
    
    // Medium complexity tasks - Use GPT-4.1-mini for balance
    CHAPTER_PLANNING: 'gpt-4.1-mini',  // Structured scene organization
    EDITING: 'gpt-4.1-mini',  // Quality improvements & consistency
    CONTENT_GENERATION: 'gpt-4.1-mini',  // General content creation
    ANALYSIS: 'gpt-4.1-mini',  // Story analysis & insights
    
    // Low complexity tasks - Use GPT-4o-mini for speed & cost
    ADAPTIVE_PLANNING: 'gpt-4o-mini',  // Quick story adjustments
    SUGGESTIONS: 'gpt-4o-mini',  // Simple suggestions & tips
    
    // Embedding tasks - Use text-embedding models for semantic operations
    SEMANTIC_SEARCH: 'text-embedding-3-small',  // Content search & discovery
    CONTENT_SIMILARITY: 'text-embedding-3-small',  // Finding similar content
    RECOMMENDATIONS: 'text-embedding-3-small',  // Smart recommendations
    RELATIONSHIP_ANALYSIS: 'text-embedding-3-small',  // Character relationships
    THEME_EXTRACTION: 'text-embedding-3-small',  // Theme & emotion analysis
  },
  
  // Fallback models for xAI Grok (when OpenAI fails)
  FALLBACK_TASKS: {
    STORY_STRUCTURE: 'grok-3',         // GPT 4.1 fallback to grok-3
    CHARACTER_DEVELOPMENT: 'grok-3',   // GPT 4.1 fallback to grok-3
    CHAPTER_PLANNING: 'grok-3',        // GPT 4.1 fallback to grok-3
    CHAPTER_WRITING: 'grok-3',         // GPT 4.1 fallback to grok-3
    CONTENT_GENERATION: 'grok-3',      // GPT 4.1 fallback to grok-3
    EDITING: 'grok-3-mini',            // 4o-mini fallback to grok-3-mini
    ANALYSIS: 'grok-3-mini',           // 4o-mini fallback to grok-3-mini
    SUGGESTIONS: 'grok-3-mini',        // 4o-mini fallback to grok-3-mini
    ADAPTIVE_PLANNING: 'grok-3-mini',  // 4o-mini fallback to grok-3-mini
    CREATIVE_WRITING: 'grok-3',        // GPT 4.1 fallback to grok-3
    DIALOGUE_GENERATION: 'grok-3'      // GPT 4.1 fallback to grok-3
  }
} as const;

export const AI_TEMPERATURE = {
  // Temperature settings for different types of generation
  CREATIVE_HIGH: 0.9,      // For highly creative content
  CREATIVE_MEDIUM: 0.8,    // For story generation, character development
  BALANCED: 0.7,           // Default for most tasks
  FOCUSED: 0.5,            // For planning and structure
  PRECISE: 0.3,            // For editing and consistency
  DETERMINISTIC: 0.1,      // For analysis and validation
  
  // Task-specific temperatures
  TASKS: {
    STORY_STRUCTURE: 0.8,
    CHARACTER_DEVELOPMENT: 0.8,
    CHAPTER_PLANNING: 0.7,
    CHAPTER_WRITING: 0.8,
    CONTENT_GENERATION: 0.7,
    EDITING: 0.3,
    ANALYSIS: 0.3,
    SUGGESTIONS: 0.5,
    ADAPTIVE_PLANNING: 0.5
  }
} as const;

export const AI_MAX_TOKENS = {
  // Token limits for different generation types
  EXTRA_LARGE: 10000,   // For complete story structures
  LARGE: SIZE_LIMITS.EMBEDDING_TEXT_LIMIT,         // For chapter content
  MEDIUM: 6000,        // For character profiles, outlines
  STANDARD: 4000,      // Default for most tasks
  SMALL: 2000,         // For short generations
  TINY: TIME_MS.SECOND,           // For quick responses
  
  // Task-specific limits
  TASKS: {
    STORY_STRUCTURE: 10000,
    CHARACTER_DEVELOPMENT: 6000,
    CHAPTER_PLANNING: 4000,
    CHAPTER_WRITING: 10000,
    CONTENT_GENERATION: 2000,
    EDITING: 2000,
    ANALYSIS: TIME_MS.SECOND,
    SUGGESTIONS: TIME_MS.SECOND,
    ADAPTIVE_PLANNING: 4000
  }
} as const;

export const AI_RETRY_CONFIG = {
  // Retry configuration
  MAX_RETRIES: 3,
  INITIAL_DELAY: TIME_MS.SECOND,      // 1 second
  BACKOFF_MULTIPLIER: 2,    // Exponential backoff
  MAX_DELAY: 30000,         // 30 seconds max
  TIMEOUT: 120000,          // 2 minutes timeout
  
  // Task-specific retries
  TASKS: {
    CRITICAL: { maxRetries: 5, timeout: 180000 },
    STANDARD: { maxRetries: 3, timeout: 120000 },
    QUICK: { maxRetries: 2, timeout: 60000 }
  }
} as const;

export const AI_CONCURRENCY = {
  // Concurrency limits
  MAX_CONCURRENT_TASKS: 3,
  MAX_CONCURRENT_CHAPTERS: 2,
  MAX_CONCURRENT_ANALYSIS: 5,
  
  // Queue settings
  QUEUE_CHECK_INTERVAL: TIME_MS.SECOND,  // 1 second
  QUEUE_MAX_SIZE: 100,
  PRIORITY_LEVELS: {
    CRITICAL: 4,
    HIGH: 3,
    MEDIUM: 2,
    LOW: 1
  }
} as const;

export const AI_QUALITY_THRESHOLDS = {
  // Quality assessment thresholds
  BESTSELLER: 95,        // NYT bestseller quality
  EXCELLENT: 90,         // Award-worthy quality
  PUBLISHER_READY: 85,   // Ready for traditional publishing
  GOOD: 80,             // Strong with minor improvements needed
  ACCEPTABLE: 75,       // Baseline acceptable quality
  NEEDS_WORK: 70,       // Requires significant improvement
  POOR: 60,             // Major rewrite needed
  
  // Minimum scores for different content types (raised for bestseller standards)
  MINIMUM_SCORES: {
    CHAPTER_CONTENT: 85,      // Was 75
    CHARACTER_PROFILE: 88,    // Was 80
    STORY_STRUCTURE: 92,      // Was 85
    DIALOGUE: 85,            // Was 70
    DESCRIPTION: 85,         // Was 70
    SCENE: 85,               // New
    PLOT_OUTLINE: 90,        // New
    OPENING_HOOK: 95         // New - First pages must be exceptional
  }
} as const;

export const AI_CONTEXT_LIMITS = {
  // Context window management - Updated for GPT-4.1's 1M token context
  MAX_CONTEXT_TOKENS: 1000000,  // 1M tokens for GPT-4.1
  MAX_CONTEXT_TOKENS_MINI: 128000,  // 128K for GPT-4.1-mini and GPT-4o-mini
  RESERVED_OUTPUT_TOKENS: 10000,  // Reserve more for longer outputs
  MAX_INPUT_TOKENS: 990000,  // Nearly 1M input for GPT-4.1
  
  // Embedding limits
  EMBEDDING_MAX_TOKENS: 8191,  // Max tokens for embedding models
  
  // Context compression thresholds
  COMPRESSION_THRESHOLD: SIZE_LIMITS.MAX_DOCUMENT_CHARS,  // Compress only when really needed
  SUMMARY_MAX_LENGTH: TIME_MS.TOAST_DURATION,  // Larger summaries with more context
  
  // Memory limits - Can include much more context now
  MAX_CHAPTER_CONTEXT: 20,     // Include up to 20 previous chapters
  MAX_CHARACTER_CONTEXT: 50,   // Include up to 50 characters
  MAX_SCENE_CONTEXT: 30        // Include up to 30 scenes
} as const;

export const AI_RATE_LIMITS = {
  // Rate limiting per minute (based on OpenAI API limits as of July 2025)
  REQUESTS_PER_MINUTE: {
    'gpt-4.1': 500,              // GPT-4.1 tier limits
    'gpt-4.1-mini': TIME_MS.SECOND,        // GPT-4.1-mini higher limits
    'gpt-4o': 500,               // GPT-4o tier limits
    'gpt-4o-mini': TIME_MS.SECOND,         // GPT-4o mini higher limits
    'text-embedding-3-small': TIME_MS.TOAST_DURATION, // Embedding limits
    'text-embedding-3-large': TIME_MS.TOAST_DURATION  // Embedding limits
  },

  // Token limits per minute (TPM)
  TOKENS_PER_MINUTE: {
    'gpt-4.1': 300000,           // GPT-4.1 token limits
    'gpt-4.1-mini': 1000000,     // GPT-4.1-mini higher token limits
    'gpt-4o': 300000,            // GPT-4o token limits
    'gpt-4o-mini': 2000000,      // GPT-4o mini higher token limits
    'text-embedding-3-small': 5000000, // Embedding token limits
    'text-embedding-3-large': 5000000  // Embedding token limits
  }
} as const;

// Helper functions to get rate limits for specific models
export function getRequestsPerMinute(model: string): number {
  return AI_RATE_LIMITS.REQUESTS_PER_MINUTE[model as keyof typeof AI_RATE_LIMITS.REQUESTS_PER_MINUTE] || 60; // Default fallback
}

export function getTokensPerMinute(model: string): number {
  return AI_RATE_LIMITS.TOKENS_PER_MINUTE[model as keyof typeof AI_RATE_LIMITS.TOKENS_PER_MINUTE] || 40000; // Default fallback
}

// Helper functions for configuration
export function getModelForTask(task: keyof typeof AI_MODELS.TASKS): string {
  return AI_MODELS.TASKS[task] || AI_MODELS.PRIMARY;
}

export function isEmbeddingTask(task: keyof typeof AI_MODELS.TASKS): boolean {
  const embeddingTasks = [
    'SEMANTIC_SEARCH',
    'CONTENT_SIMILARITY', 
    'RECOMMENDATIONS',
    'RELATIONSHIP_ANALYSIS',
    'THEME_EXTRACTION'
  ];
  return embeddingTasks.includes(task);
}

export function getContextLimitForModel(model: string): number {
  if (model === AI_MODELS.PRIMARY) {
    return AI_CONTEXT_LIMITS.MAX_CONTEXT_TOKENS;
  }
  return AI_CONTEXT_LIMITS.MAX_CONTEXT_TOKENS_MINI;
}

export function getTemperatureForTask(task: keyof typeof AI_TEMPERATURE.TASKS): number {
  return AI_TEMPERATURE.TASKS[task] || AI_TEMPERATURE.BALANCED;
}

export function getMaxTokensForTask(task: keyof typeof AI_MAX_TOKENS.TASKS): number {
  return AI_MAX_TOKENS.TASKS[task] || AI_MAX_TOKENS.STANDARD;
}

export function getRetryConfigForPriority(priority: 'CRITICAL' | 'STANDARD' | 'QUICK') {
  return AI_RETRY_CONFIG.TASKS[priority] || AI_RETRY_CONFIG.TASKS.STANDARD;
}

// Dynamic configuration based on user preferences
export interface UserAIPreferences {
  preferredModel?: string;
  creativityLevel?: 'high' | 'medium' | 'low';
  outputLength?: 'short' | 'medium' | 'long';
  qualityVsSpeed?: 'quality' | 'balanced' | 'speed';
}

export function getAIConfig(
  task: keyof typeof AI_MODELS.TASKS,
  userPrefs?: UserAIPreferences
) {
  let model = getModelForTask(task);
  let temperature = getTemperatureForTask(task);
  let maxTokens = getMaxTokensForTask(task);
  
  // Apply user preferences
  if (userPrefs) {
    // Model preference
    if (userPrefs.preferredModel) {
      model = userPrefs.preferredModel;
    } else if (userPrefs.qualityVsSpeed === 'speed') {
      model = AI_MODELS.FAST;
    }
    
    // Temperature based on creativity
    if (userPrefs.creativityLevel === 'high') {
      temperature = Math.min(temperature + 0.2, 1.0);
    } else if (userPrefs.creativityLevel === 'low') {
      temperature = Math.max(temperature - 0.2, 0.1);
    }
    
    // Token limit based on length preference
    if (userPrefs.outputLength === 'long') {
      maxTokens = Math.min(maxTokens * 1.5, AI_MAX_TOKENS.EXTRA_LARGE);
    } else if (userPrefs.outputLength === 'short') {
      maxTokens = Math.max(maxTokens * 0.5, AI_MAX_TOKENS.TINY);
    }
  }
  
  return {
    model,
    temperature,
    max_tokens: Math.floor(maxTokens),
    top_p: 1,
    frequency_penalty: 0,
    presence_penalty: 0
  };
}

// Export default configuration
export const DEFAULT_AI_CONFIG = {
  model: AI_MODELS.PRIMARY,
  temperature: AI_TEMPERATURE.BALANCED,
  max_tokens: AI_MAX_TOKENS.STANDARD,
  retryConfig: AI_RETRY_CONFIG.TASKS.STANDARD,
  concurrency: AI_CONCURRENCY.MAX_CONCURRENT_TASKS
} as const;