import { z } from 'zod';
import { logger } from '@/lib/services/logger';

import { demoConfig } from './demo-config';
import { validateEnv } from './validate-env';

// Create a flexible schema that works for both demo and production modes
const createEnvSchema = (isDemoMode: boolean = false) => {
  return z.object({
    // App Configuration
    NEXT_PUBLIC_APP_URL: z.string().url().default(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
    NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

    // Supabase Configuration
    NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
    NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
    SUPABASE_SERVICE_ROLE_KEY: z.string().min(1),
    SUPABASE_WEBHOOK_SECRET: z.string().optional(),
    SUPABASE_JWT_SECRET: z.string().optional(),

    // OpenAI Configuration - optional in demo mode
    OPENAI_API_KEY: isDemoMode ? z.string().optional().default('demo_key') : z.string().min(1),

    // Google Gemini Configuration (optional)
    GENKIT_API_KEY: z.string().optional(),

    // xAI Configuration (optional - used as fallback)
    XAI_API_KEY: z.string().optional(),
    NEXT_PUBLIC_AI_FALLBACK_ENABLED: z.string().optional().default('true'),

    // Stripe Configuration - optional in demo mode
    STRIPE_SECRET_KEY: isDemoMode ? z.string().optional().default('demo_key') : z.string().min(1),
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: isDemoMode ? z.string().optional().default('demo_key') : z.string().min(1),
    STRIPE_WEBHOOK_SECRET: isDemoMode ? z.string().optional().default('demo_key') : z.string().min(1),

    // Stripe Price IDs - optional in demo mode
    STRIPE_PRICE_ID_BASIC: isDemoMode ? z.string().optional().default('demo_price') : z.string().min(1),
    STRIPE_PRICE_ID_PRO: isDemoMode ? z.string().optional().default('demo_price') : z.string().min(1),
    STRIPE_PRICE_ID_ENTERPRISE: isDemoMode ? z.string().optional().default('demo_price') : z.string().min(1),

    // Admin Configuration
    ADMIN_EMAILS: z.string().optional().default(''),

    // Development Configuration
    NEXT_PUBLIC_DEV_BYPASS_AUTH: z.string().optional().default('false'),
    DEV_USER_EMAIL: z.string().optional(),
    DEV_USER_ID: z.string().optional(),

    // Build Configuration
    npm_package_version: z.string().optional(),
    NEXT_PHASE: z.string().optional(),
  });
};

type EnvConfig = z.infer<ReturnType<typeof createEnvSchema>>;

class Config {
  private static instance: Config;
  private config: EnvConfig;

  private constructor() {
    try {
      // Check if we're explicitly in demo mode first
      const isDemoMode = process.env.NEXT_PUBLIC_DEMO_MODE === 'true';

      if (isDemoMode) {
        logger.warn('🚧 Running in DEMO MODE with dummy configuration');
        logger.warn('⚠️  API features (Supabase, OpenAI, Stripe); will not work');
        logger.warn('📝 To use full features, set up your .env.local file');
        const demoSchema = createEnvSchema(true);
        this.config = demoSchema.parse(demoConfig);
        return;
      }

      // Try to parse actual environment variables
      try {
        const prodSchema = createEnvSchema(false);
        this.config = prodSchema.parse(process.env);
      } catch (_validationError) {
        // If validation fails, fall back to demo mode
        logger.warn('⚠️  Environment validation failed, falling back to DEMO MODE');
        logger.warn('🚧 Running in DEMO MODE with dummy configuration');
        logger.warn('📝 To use full features, set up your .env.local file properly');
        const demoSchema = createEnvSchema(true);
        this.config = demoSchema.parse(demoConfig);
      }
    } catch (error) {
      logger.error('Config initialization error:', error);
      if (error instanceof z.ZodError) {
        const missingVars = error.errors.map(err => err.path.join('.')).join(', ');
        logger.error(`Missing or invalid environment variables: ${missingVars}`);
        logger.error('Validation errors:', error.errors);
        logger.info('💡 Tip: Copy .env.example to .env.local and fill in your values');
        logger.info('🚀 Or set NEXT_PUBLIC_DEMO_MODE=true to run in demo mode');
        throw new Error(`Missing or invalid environment variables: ${missingVars}`);
      }
      throw error;
    }
  }

  static getInstance(): Config {
    if (!Config.instance) {
      Config.instance = new Config();
    }
    return Config.instance;
  }

  get env(): EnvConfig {
    return this.config;
  }

  get isProduction(): boolean {
    return this.config.NODE_ENV === 'production';
  }

  get isDevelopment(): boolean {
    return this.config.NODE_ENV === 'development';
  }

  get isTest(): boolean {
    return this.config.NODE_ENV === 'test';
  }

  get supabase() {
    return {
      url: this.config.NEXT_PUBLIC_SUPABASE_URL,
      anonKey: this.config.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      serviceRoleKey: this.config.SUPABASE_SERVICE_ROLE_KEY,
      webhookSecret: this.config.SUPABASE_WEBHOOK_SECRET,
      jwtSecret: this.config.SUPABASE_JWT_SECRET,
    };
  }

  get stripe() {
    return {
      secretKey: this.config.STRIPE_SECRET_KEY,
      publishableKey: this.config.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
      webhookSecret: this.config.STRIPE_WEBHOOK_SECRET,
      prices: {
        basic: this.config.STRIPE_PRICE_ID_BASIC,
        pro: this.config.STRIPE_PRICE_ID_PRO,
        enterprise: this.config.STRIPE_PRICE_ID_ENTERPRISE,
      },
    };
  }

  get openai() {
    return {
      apiKey: this.config.OPENAI_API_KEY,
    };
  }

  get xai() {
    return {
      apiKey: this.config.XAI_API_KEY,
      enabled: this.config.NEXT_PUBLIC_AI_FALLBACK_ENABLED === 'true'
    };
  }

  get app() {
    return {
      url: this.config.NEXT_PUBLIC_APP_URL,
    };
  }

  get adminEmails(): string[] {
    return this.config.ADMIN_EMAILS
      .split(',')
      .map(email => email.trim())
      .filter(email => email.length > 0);
  }

  get dev() {
    return {
      bypassAuth: this.config.NEXT_PUBLIC_DEV_BYPASS_AUTH === 'true',
      userEmail: this.config.DEV_USER_EMAIL,
      userId: this.config.DEV_USER_ID,
    };
  }

  get build() {
    return {
      version: this.config.npm_package_version,
      phase: this.config.NEXT_PHASE,
    };
  }
}

// Lazy initialization to prevent issues during module loading
let configInstance: Config | null = null;

export const config = new Proxy({} as Config, {
  get(_target, prop, receiver) {
    if (!configInstance) {
      configInstance = Config.getInstance();
    }
    return Reflect.get(configInstance, prop, receiver);
  }
}) as Config;

export const getConfig = () => {
  if (!configInstance) {
    configInstance = Config.getInstance();
  }
  return configInstance;
};

export type { EnvConfig };
export { Config };

// Re-export other configuration modules
export * from './database-tables';
export * from './api-endpoints';
export * from './network-config';
export * from './app-constants';
export * from './ai-settings';
export * from './ui-config';
export * from './error-messages';
export * from './storage-keys';
export * from './animation-timing';
export * from './file-limits';
export * from './api-routes';
export * from './knowledge-base';
export * from './development';