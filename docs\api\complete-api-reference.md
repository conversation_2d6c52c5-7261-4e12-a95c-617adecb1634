# BookScribe Complete API Reference

## Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Response Format](#response-format)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [API Endpoints by Category](#api-endpoints-by-category)

## Overview

The BookScribe API is a RESTful API built with Next.js API Routes. All endpoints follow consistent patterns for authentication, error handling, and response formats.

### Base URL
```
Production: https://bookscribe.ai/api
Development: http://localhost:3000/api
```

### Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Authentication

All API endpoints (except health and public endpoints) require JWT authentication via Supabase Auth.

```typescript
// Example authenticated request
const response = await fetch('/api/projects', {
  headers: {
    'Authorization': `Bearer ${session.access_token}`,
    'Content-Type': 'application/json'
  }
});
```

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "metadata": {
    "timestamp": "2025-01-30T12:00:00Z",
    "version": "1.0.0"
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error context
    }
  }
}
```

## Error Handling

### Common Error Codes
- `AUTH_REQUIRED` - Authentication required
- `FORBIDDEN` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `VALIDATION_ERROR` - Invalid request data
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error

## Rate Limiting

Rate limits are based on subscription tier:
- **Free**: 20 requests/hour
- **Starter**: 100 requests/hour
- **Professional**: 500 requests/hour
- **Premium**: Unlimited

## API Endpoints by Category

### 🏠 Health & Status

#### GET /api/health
Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-30T12:00:00Z",
  "services": {
    "database": "connected",
    "ai": "operational"
  }
}
```

---

### 📝 Projects

#### GET /api/projects
Get all projects for authenticated user.

**Query Parameters:**
- `include`: Comma-separated list of relations (`chapters`, `characters`, `collaborators`)
- `sort`: Sort field (`created_at`, `updated_at`, `title`)
- `order`: Sort order (`asc`, `desc`)
- `limit`: Number of results (default: 20, max: 100)
- `offset`: Pagination offset

**Response:**
```json
{
  "success": true,
  "data": {
    "projects": [
      {
        "id": "uuid",
        "title": "My Novel",
        "description": "A thrilling adventure",
        "settings": {},
        "created_at": "2025-01-30T12:00:00Z",
        "updated_at": "2025-01-30T12:00:00Z",
        "word_count": 50000,
        "chapter_count": 20
      }
    ],
    "total": 10,
    "hasMore": false
  }
}
```

#### POST /api/projects
Create a new project.

**Request Body:**
```json
{
  "title": "My New Novel",
  "description": "An epic fantasy adventure",
  "settings": {
    "genre": "fantasy",
    "targetWordCount": 100000,
    "targetChapters": 30
  }
}
```

#### GET /api/projects/[id]
Get a specific project by ID.

**Path Parameters:**
- `id`: Project UUID

**Response:** Single project object with full details.

#### PUT /api/projects/[id]
Update a project.

**Path Parameters:**
- `id`: Project UUID

**Request Body:** Partial project object with fields to update.

#### DELETE /api/projects/[id]
Delete a project.

**Path Parameters:**
- `id`: Project UUID

#### GET /api/projects/grouped
Get projects grouped by series.

**Response:**
```json
{
  "success": true,
  "data": {
    "standalone": [...],
    "series": {
      "seriesId": {
        "series": {...},
        "projects": [...]
      }
    }
  }
}
```

#### POST /api/projects/[id]/export
Export project in various formats.

**Path Parameters:**
- `id`: Project UUID

**Request Body:**
```json
{
  "format": "docx" | "epub" | "pdf",
  "options": {
    "includeMetadata": true,
    "includeComments": false,
    "formatting": "standard"
  }
}
```

---

### 📚 Chapters

#### GET /api/projects/[id]/chapters
Get all chapters for a project.

**Path Parameters:**
- `projectId`: Project UUID

**Query Parameters:**
- `includeContent`: Include full chapter content (default: false)
- `status`: Filter by status (`draft`, `review`, `published`)

#### POST /api/chapters
Create a new chapter.

**Request Body:**
```json
{
  "project_id": "uuid",
  "chapter_number": 1,
  "title": "The Beginning",
  "content": "Chapter content...",
  "status": "draft"
}
```

#### GET /api/chapters/[id]
Get a specific chapter.

#### PUT /api/chapters/[id]
Update a chapter.

#### DELETE /api/chapters/[id]
Delete a chapter.

#### POST /api/chapters/create-version
Create a version snapshot of a chapter.

**Request Body:**
```json
{
  "chapter_id": "uuid",
  "description": "Before major revision"
}
```

#### POST /api/chapters/restore-version
Restore a chapter from a previous version.

**Request Body:**
```json
{
  "version_id": "uuid"
}
```

---

### 👥 Characters

#### GET /api/characters
Get all characters for authenticated user.

**Query Parameters:**
- `project_id`: Filter by project
- `role`: Filter by role (`protagonist`, `antagonist`, `major`, `supporting`, `minor`)

#### POST /api/characters
Create a new character.

**Request Body:**
```json
{
  "project_id": "uuid",
  "name": "John Doe",
  "role": "protagonist",
  "profile": {
    "age": 35,
    "occupation": "Detective",
    "background": "Former military...",
    "personality": {...},
    "appearance": "..."
  }
}
```

#### GET /api/characters/[id]
Get a specific character.

#### PUT /api/characters/[id]
Update a character.

#### DELETE /api/characters/[id]
Delete a character.

#### POST /api/characters/bulk
Create or update multiple characters.

**Request Body:**
```json
{
  "characters": [
    {...},
    {...}
  ]
}
```

#### POST /api/characters/[id]/share
Share a character with another user.

---

### 🤖 AI Agents

#### POST /api/agents/generate
Generate content using AI agents.

**Request Body:**
```json
{
  "project_id": "uuid",
  "agent_type": "story_architect" | "character_developer" | "chapter_planner" | "writing_agent",
  "input": {
    // Agent-specific input
  }
}
```

#### POST /api/agents/edit
Edit content using AI.

**Request Body:**
```json
{
  "content": "Original content",
  "instructions": "Make it more suspenseful",
  "context": {
    "project_id": "uuid",
    "chapter_id": "uuid"
  }
}
```

#### POST /api/agents/initialize
Initialize agent pipeline for a project.

**Request Body:**
```json
{
  "project_id": "uuid",
  "settings": {
    "targetWordCount": 100000,
    "targetChapters": 30
  }
}
```

#### POST /api/agents/adjust-plan
Adjust story plan based on changes.

**Request Body:**
```json
{
  "project_id": "uuid",
  "changes": {
    "type": "plot_change",
    "description": "Character dies earlier",
    "affected_chapters": [5, 6, 7]
  }
}
```

#### POST /api/agents/chat
Chat with AI assistant about your project.

**Request Body:**
```json
{
  "project_id": "uuid",
  "message": "How can I improve the pacing?",
  "context": {
    "chapter_id": "uuid",
    "include_story_bible": true
  }
}
```

---

### 🤖 AI Services

#### POST /api/ai/chat
General AI chat endpoint.

**Request Body:**
```json
{
  "messages": [
    {"role": "user", "content": "Hello"}
  ],
  "model": "gpt-4",
  "temperature": 0.7
}
```

#### POST /api/ai/stream-content
Stream AI-generated content.

**Request Body:**
```json
{
  "prompt": "Continue the story...",
  "context": {...},
  "stream": true
}
```

**Response:** Server-sent events stream.

#### POST /api/ai/structured-content
Generate structured content with schema validation.

**Request Body:**
```json
{
  "prompt": "Create a character profile",
  "schema": {
    // JSON schema for validation
  }
}
```

#### POST /api/ai/suggestions
Get AI suggestions for content.

**Request Body:**
```json
{
  "content": "Current text",
  "type": "plot" | "character" | "dialogue" | "description",
  "count": 3
}
```

---

### 📊 Analytics

#### GET /api/analytics/productivity
Get productivity analytics.

**Query Parameters:**
- `user_id`: User UUID
- `project_id`: Project UUID (optional)
- `start_date`: ISO date string
- `end_date`: ISO date string

**Response:** See Analytics documentation for detailed schema.

#### GET /api/analytics/quality
Get content quality metrics.

**Query Parameters:**
- `project_id`: Project UUID
- `chapter_id`: Chapter UUID (optional)

#### GET /api/analytics/sessions
Get writing session data.

#### GET /api/analytics/ai-usage
Get AI usage statistics.

#### GET /api/analytics/chapters
Get chapter-level analytics.

#### GET /api/analytics/behavioral
Get user behavior patterns.

#### GET /api/analytics/profiles/performance
Get AI profile performance metrics.

#### GET /api/analytics/recommendations
Get personalized recommendations.

#### GET /api/analytics/selections
Get project selection analytics.

#### GET /api/analytics/selections/success-patterns
Analyze successful project patterns.

#### GET /api/analytics/export
Export analytics data.

**Query Parameters:**
- `format`: Export format (`csv`, `json`, `pdf`)
- `date_range`: Date range for export

---

### 📚 Series Management

#### GET /api/series
Get all series for authenticated user.

#### POST /api/series
Create a new series.

**Request Body:**
```json
{
  "title": "The Chronicles",
  "description": "Epic fantasy series",
  "universe_id": "uuid",
  "planned_books": 5
}
```

#### GET /api/series/[id]
Get series details.

#### PUT /api/series/[id]
Update series information.

#### DELETE /api/series/[id]
Delete a series.

#### GET /api/series/[id]/books
Get all books in a series.

#### GET /api/series/[id]/characters
Get all characters across the series.

#### GET /api/series/[id]/character-arcs
Get character arc progression across books.

#### GET /api/series/[id]/universe
Get universe details for the series.

#### POST /api/series/[id]/universe
Update universe information.

#### GET /api/series/[id]/continuity
Check continuity across the series.

#### GET /api/series/[id]/continuity-issues
Get detected continuity issues.

#### POST /api/series/[id]/character-continuity
Check character continuity.

#### GET /api/series/[id]/analytics
Get series-wide analytics.

#### GET /api/series/[id]/word-counts
Get word count statistics across the series.

---

### 🌍 Universe Management

#### GET /api/universes
Get all universes.

#### POST /api/universes
Create a new universe.

**Request Body:**
```json
{
  "name": "Middle Earth",
  "description": "A fantasy world",
  "rules": {
    "magic_system": {...},
    "technology_level": "medieval",
    "species": [...]
  }
}
```

#### GET /api/universes/[id]
Get universe details.

#### PUT /api/universes/[id]
Update universe.

#### DELETE /api/universes/[id]
Delete universe.

#### GET /api/universes/timeline-events
Get all timeline events across universe.

---

### 📖 Story Bible

#### GET /api/story-bible
Get story bible for a project.

**Query Parameters:**
- `project_id`: Project UUID

#### POST /api/story-bible
Create story bible entry.

**Request Body:**
```json
{
  "project_id": "uuid",
  "category": "world" | "character" | "plot" | "theme",
  "title": "Magic System",
  "content": "Detailed description...",
  "tags": ["magic", "rules"]
}
```

#### PUT /api/story-bible/[id]
Update story bible entry.

#### DELETE /api/story-bible/[id]
Delete story bible entry.

#### POST /api/story-bible/bulk
Bulk operations on story bible.

---

### 🔍 Search

#### POST /api/search/semantic
Semantic search across content.

**Request Body:**
```json
{
  "query": "scenes with conflict",
  "project_id": "uuid",
  "scope": "chapters" | "characters" | "all"
}
```

#### POST /api/search/character-moments
Find specific character moments.

**Request Body:**
```json
{
  "character_id": "uuid",
  "moment_type": "growth" | "conflict" | "revelation"
}
```

#### POST /api/search/emotion
Search by emotional content.

**Request Body:**
```json
{
  "emotions": ["joy", "fear", "anger"],
  "project_id": "uuid"
}
```

#### POST /api/search/theme
Search by thematic elements.

**Request Body:**
```json
{
  "themes": ["redemption", "sacrifice"],
  "project_id": "uuid"
}
```

---

### 👥 Collaboration

#### POST /api/collaboration/join
Join a collaboration session.

**Request Body:**
```json
{
  "project_id": "uuid",
  "chapter_id": "uuid"
}
```

#### POST /api/collaboration/leave
Leave collaboration session.

#### POST /api/collaboration/cursor
Update cursor position.

**Request Body:**
```json
{
  "project_id": "uuid",
  "chapter_id": "uuid",
  "position": {
    "line": 10,
    "column": 15
  }
}
```

#### POST /api/collaboration/change
Broadcast content change.

**Request Body:**
```json
{
  "project_id": "uuid",
  "chapter_id": "uuid",
  "change": {
    "type": "insert" | "delete",
    "position": 100,
    "content": "new text"
  }
}
```

#### POST /api/collaboration/lock
Lock a section for editing.

#### POST /api/collaboration/unlock
Unlock a section.

#### GET /api/collaboration/sessions
Get active collaboration sessions.

---

### 💳 Billing & Subscriptions

#### POST /api/billing/subscriptions
Create or update subscription.

**Request Body:**
```json
{
  "price_id": "price_xxx",
  "payment_method_id": "pm_xxx"
}
```

#### GET /api/billing/subscriptions
Get current subscription.

#### POST /api/billing/subscriptions/portal
Create customer portal session.

**Response:**
```json
{
  "url": "https://billing.stripe.com/session/xxx"
}
```

#### POST /api/billing/subscriptions/checkout
Create checkout session.

**Request Body:**
```json
{
  "price_id": "price_xxx",
  "success_url": "https://app.bookscribe.ai/success",
  "cancel_url": "https://app.bookscribe.ai/pricing"
}
```

#### GET /api/billing/history
Get billing history.

#### POST /api/billing/webhooks/stripe
Stripe webhook endpoint (internal use).

---

### 📤 Import/Export

#### POST /api/import/docx
Import from DOCX file.

**Request Body:** Multipart form data with file.

#### POST /api/import/epub
Import from EPUB file.

**Request Body:** Multipart form data with file.

#### POST /api/import/pdf
Import from PDF file (text extraction).

**Request Body:** Multipart form data with file.

#### POST /api/projects/[id]/export
Export project (see Projects section).

---

### 🎯 Goals & Achievements

#### GET /api/goals
Get user writing goals.

#### POST /api/goals
Create a writing goal.

**Request Body:**
```json
{
  "type": "daily_words" | "project_completion" | "streak",
  "target": 1000,
  "deadline": "2025-12-31"
}
```

#### GET /api/goals/progress
Get goal progress.

#### GET /api/goals/recommendations
Get goal recommendations based on patterns.

#### GET /api/achievements
Get unlocked achievements.

#### GET /api/achievements/stats
Get achievement statistics.

#### POST /api/achievements/check
Check for new achievements.

---

### 📧 Email & Notifications

#### GET /api/notifications
Get user notifications.

#### PUT /api/notifications/[id]
Mark notification as read.

#### POST /api/email/send
Send email (admin only).

#### GET /api/email/preferences
Get email preferences.

#### PUT /api/email/preferences
Update email preferences.

---

### 🔧 Service Management

#### GET /api/services/health
Check all services health.

#### GET /api/services/analytics
Get service analytics.

#### POST /api/services/content
Content service operations.

#### POST /api/services/orchestrator
Orchestration service operations.

---

### 👥 Team & Invitations

#### GET /api/projects/[id]/team
Get project team members.

#### POST /api/projects/[id]/collaborators/invite
Invite collaborator to project.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "role": "editor" | "viewer" | "commenter"
}
```

#### POST /api/invitations/[token]/accept
Accept project invitation.

---

### 🎙️ Voice Profiles

#### GET /api/voice-profiles
Get all voice profiles.

#### POST /api/voice-profiles
Create voice profile.

**Request Body:**
```json
{
  "name": "Detective Voice",
  "character_id": "uuid",
  "traits": {
    "tone": "gruff",
    "vocabulary": "street-smart",
    "speech_patterns": [...]
  }
}
```

#### GET /api/voice-profiles/[id]
Get voice profile details.

#### PUT /api/voice-profiles/[id]
Update voice profile.

#### POST /api/voice-profiles/[id]/train
Train voice profile with samples.

#### POST /api/voice-profiles/[id]/train-from-content
Train from existing content.

---

### 📈 Analysis

#### POST /api/analysis/content
Analyze content quality and metrics.

**Request Body:**
```json
{
  "content": "Chapter text...",
  "analysis_type": ["readability", "emotion", "pacing"]
}
```

#### POST /api/analysis/voice
Analyze voice consistency.

#### GET /api/analysis/progress
Get writing progress analysis.

#### POST /api/analysis/auto-fix
Auto-fix detected issues.

#### GET /api/analysis/character-development-grid
Get character development analysis.

#### GET /api/analysis/arc-patterns
Identify story arc patterns.

#### POST /api/analysis/arc-suggestions
Get arc improvement suggestions.

#### POST /api/analysis/arc-predictions
Predict arc developments.

---

### 🕐 Timeline

#### GET /api/timeline/events
Get timeline events.

#### POST /api/timeline/validate
Validate timeline consistency.

#### POST /api/timeline/autofix
Auto-fix timeline issues.

---

### 📜 Version History

#### GET /api/version-history
Get version history for content.

**Query Parameters:**
- `entity_type`: Type of entity (`chapter`, `character`)
- `entity_id`: Entity UUID

#### GET /api/version-history/[id]
Get specific version details.

---

### 🔗 Relationships

#### POST /api/relationships/analyze
Analyze character relationships.

#### GET /api/relationships/graph
Get relationship graph data.

---

### 💾 Memory Management

#### POST /api/memory/compress
Compress project context.

#### POST /api/memory/merge
Merge memory contexts.

#### GET /api/memory/stats
Get memory usage statistics.

---

### 🔄 Processing & Orchestration

#### GET /api/processing-tasks
Get processing task queue.

#### GET /api/processing-tasks/[id]
Get task status.

#### POST /api/orchestration/start
Start book generation orchestration.

#### GET /api/orchestration/progress
Get orchestration progress.

---

### 🔍 Consistency Checking

#### POST /api/consistency/check
Check project consistency.

**Request Body:**
```json
{
  "project_id": "uuid",
  "check_types": ["timeline", "character", "plot", "world"]
}
```

---

### 📊 Writing Sessions

#### GET /api/writing/sessions
Get writing sessions.

#### POST /api/writing/sessions
Start writing session.

#### GET /api/writing/goals/[id]
Get writing goal details.

---

### 🎪 Demo & Samples

#### GET /api/sample-projects
Get sample projects for demo.

#### POST /api/demo/generate
Generate demo content.

---

## Pagination

Most list endpoints support pagination:

```
GET /api/projects?limit=20&offset=40
```

Response includes pagination metadata:
```json
{
  "data": {...},
  "pagination": {
    "total": 100,
    "limit": 20,
    "offset": 40,
    "hasNext": true,
    "hasPrev": true
  }
}
```

## Filtering & Sorting

Many endpoints support filtering and sorting:

```
GET /api/projects?status=active&sort=updated_at&order=desc
```

## Webhooks

BookScribe can send webhooks for various events:

- `project.created`
- `project.updated`
- `chapter.completed`
- `goal.achieved`
- `subscription.changed`

Configure webhooks in project settings.

## SDK Examples

### JavaScript/TypeScript
```typescript
import { BookScribeClient } from '@bookscribe/sdk';

const client = new BookScribeClient({
  apiKey: process.env.BOOKSCRIBE_API_KEY
});

const projects = await client.projects.list();
const chapter = await client.chapters.create({
  project_id: 'uuid',
  title: 'Chapter 1'
});
```

### Python
```python
from bookscribe import BookScribeClient

client = BookScribeClient(api_key=os.environ['BOOKSCRIBE_API_KEY'])

projects = client.projects.list()
chapter = client.chapters.create(
    project_id='uuid',
    title='Chapter 1'
)
```

## Best Practices

1. **Use appropriate HTTP methods**: GET for reading, POST for creating, PUT for updating, DELETE for removing
2. **Include only necessary data**: Use query parameters to limit response size
3. **Handle rate limits**: Implement exponential backoff for retries
4. **Cache responses**: Cache GET requests when appropriate
5. **Use webhooks**: For real-time updates instead of polling

## Support

For API support, contact <EMAIL> or visit our [developer documentation](https://developers.bookscribe.ai).