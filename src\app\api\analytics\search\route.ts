import { NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'

const searchEventSchema = z.object({
  projectId: z.string().uuid(),
  query: z.string().min(1).max(100),
  resultCount: z.number().int().min(0),
  clickedResult: z.object({
    id: z.string(),
    type: z.string(),
    position: z.number().int().min(0)
  }).optional(),
  timestamp: z.string().datetime()
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const body = await request.json()
    const validationResult = searchEventSchema.safeParse(body)
    
    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid search event data',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400)
    }
    
    const { projectId, query, resultCount, clickedResult, timestamp } = validationResult.data
    
    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()
    
    if (!projectAccess) {
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single()
      
      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403)
      }
    }
    
    // Store search event
    const { error } = await supabase
      .from('search_analytics')
      .insert({
        project_id: projectId,
        user_id: user.id,
        query: query.toLowerCase().trim(),
        result_count: resultCount,
        clicked_result_id: clickedResult?.id,
        clicked_result_type: clickedResult?.type,
        clicked_result_position: clickedResult?.position,
        timestamp
      })
    
    if (error) {
      logger.error('Failed to store search event:', error)
      return UnifiedResponse.error({
        message: 'Failed to track search event',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500)
    }
    
    return UnifiedResponse.success({
      message: 'Search event tracked successfully'
    })
  } catch (error) {
    logger.error('Error tracking search event:', error)
    return UnifiedResponse.error({
      message: 'Failed to track search event',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500)
  }
})