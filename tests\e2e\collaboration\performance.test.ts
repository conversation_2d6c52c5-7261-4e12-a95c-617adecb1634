import { test, expect, Page, BrowserContext } from '@playwright/test';
import { createTestUsers, deleteTestUsers, TestUser } from '../helpers/test-users';
import { createTestProject, deleteTestProject, TestProject } from '../helpers/test-projects';
import { 
  measureSyncLatency, 
  waitForAllCollaboratorsSync,
  simulateTyping 
} from '../helpers/realtime-helpers';
import { getNetworkMetrics } from '../helpers/network-helpers';

describe('Collaboration Performance E2E Tests', () => {
  const userCounts = [2, 5, 10]; // Test with different numbers of collaborators
  
  test.describe.configure({ mode: 'serial' }); // Run tests sequentially

  for (const userCount of userCounts) {
    test(`should handle ${userCount} concurrent users efficiently`, async ({ browser }) => {
      const users: TestUser[] = [];
      const contexts: BrowserContext[] = [];
      const pages: Page[] = [];
      let project: TestProject;

      try {
        // Setup
        users.push(...await createTestUsers(userCount));
        project = await createTestProject({
          title: `Performance Test - ${userCount} Users`,
          description: 'Testing collaboration performance',
          ownerId: users[0].id
        });

        // Add all users as collaborators
        for (let i = 1; i < users.length; i++) {
          await addCollaborator(project.id, users[0].id, users[i].email, 'editor');
        }

        // Create contexts and pages
        for (let i = 0; i < userCount; i++) {
          const context = await browser.newContext();
          contexts.push(context);
          
          const page = await context.newPage();
          pages.push(page);
          
          await loginUser(page, users[i]);
          await page.goto(`/projects/${project.id}/editor`);
          await page.waitForSelector('[data-testid="editor-content"]');
        }

        // Performance test 1: Measure sync latency
        const latencies: number[] = [];
        for (let i = 0; i < 5; i++) {
          const sourceIdx = i % userCount;
          const targetIdx = (i + 1) % userCount;
          
          const latency = await measureSyncLatency(
            pages[sourceIdx],
            pages[targetIdx],
            `Performance test ${i}`
          );
          latencies.push(latency);
        }

        const avgLatency = latencies.reduce((a, b) => a + b) / latencies.length;
        console.log(`Average sync latency for ${userCount} users: ${avgLatency}ms`);
        
        // Assert reasonable latency
        expect(avgLatency).toBeLessThan(1000); // Should sync within 1 second

        // Performance test 2: Simultaneous typing
        const startTime = Date.now();
        const typingPromises = pages.map(async (page, idx) => {
          await simulateTyping(
            page,
            `User ${idx} is typing some content. This is a performance test. `
          );
        });

        await Promise.all(typingPromises);
        
        // Wait for all to sync
        const expectedLength = pages.length * 65; // Approximate length of typed content
        await Promise.all(pages.map(page => 
          page.waitForFunction(
            (minLength) => {
              const content = document.querySelector('[data-testid="editor-content"]')?.textContent || '';
              return content.length >= minLength;
            },
            expectedLength * 0.9, // Allow 10% variance
            { timeout: 30000 }
          )
        ));

        const syncTime = Date.now() - startTime;
        console.log(`Time to sync ${userCount} users typing simultaneously: ${syncTime}ms`);

        // Performance test 3: Memory usage
        const memoryUsage = await pages[0].evaluate(() => {
          if ('memory' in performance && (performance as any).memory) {
            return {
              usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
              totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
              jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
            };
          }
          return null;
        });

        if (memoryUsage) {
          const heapUsageMB = memoryUsage.usedJSHeapSize / (1024 * 1024);
          console.log(`Heap usage with ${userCount} users: ${heapUsageMB.toFixed(2)}MB`);
          
          // Memory should scale reasonably
          expect(heapUsageMB).toBeLessThan(200 * userCount); // Max 200MB per user
        }

        // Performance test 4: Network metrics
        const networkMetrics = await getNetworkMetrics(pages[0]);
        console.log(`Network metrics for ${userCount} users:`, networkMetrics);

      } finally {
        // Cleanup
        if (project!) {
          await deleteTestProject(project.id);
        }
        await deleteTestUsers(users);
        for (const context of contexts) {
          await context.close();
        }
      }
    });
  }

  test('should handle large document collaboration', async ({ browser }) => {
    const users: TestUser[] = [];
    const contexts: BrowserContext[] = [];
    const pages: Page[] = [];
    let project: TestProject;

    try {
      // Create 3 users for this test
      users.push(...await createTestUsers(3));
      project = await createTestProject({
        title: 'Large Document Test',
        description: 'Testing performance with large documents',
        ownerId: users[0].id
      });

      // Setup collaborators
      for (let i = 1; i < users.length; i++) {
        await addCollaborator(project.id, users[0].id, users[i].email, 'editor');
      }

      // Create pages
      for (let i = 0; i < users.length; i++) {
        const context = await browser.newContext();
        contexts.push(context);
        
        const page = await context.newPage();
        pages.push(page);
        
        await loginUser(page, users[i]);
        await page.goto(`/projects/${project.id}/editor`);
        await page.waitForSelector('[data-testid="editor-content"]');
      }

      // Generate large content (100KB+)
      const largeContent = Array(1000).fill(null).map((_, i) => 
        `This is paragraph ${i}. It contains some text to make the document larger. ` +
        `The quick brown fox jumps over the lazy dog. This helps test performance with large documents. `
      ).join('\n\n');

      console.log(`Large content size: ${(largeContent.length / 1024).toFixed(2)}KB`);

      // User 1 pastes large content
      const pasteStartTime = Date.now();
      await pages[0].fill('[data-testid="editor-content"]', largeContent);
      
      // Measure sync time for large content
      await waitForAllCollaboratorsSync(pages, largeContent);
      const syncDuration = Date.now() - pasteStartTime;
      
      console.log(`Large document sync time: ${syncDuration}ms`);
      expect(syncDuration).toBeLessThan(10000); // Should sync within 10 seconds

      // Test editing performance with large document
      const editLatencies: number[] = [];
      
      for (let i = 0; i < 3; i++) {
        // Each user makes an edit at different positions
        const position = Math.floor(Math.random() * 500); // Random paragraph
        const editText = ` [Edit by User ${i}]`;
        
        const editStart = Date.now();
        
        // Navigate to position and add edit
        await pages[i].click('[data-testid="editor-content"]');
        await pages[i].keyboard.press('Control+Home'); // Go to start
        for (let j = 0; j < position; j++) {
          await pages[i].keyboard.press('ArrowDown');
        }
        await pages[i].keyboard.press('End');
        await pages[i].type('[data-testid="editor-content"]', editText);
        
        // Wait for sync on other pages
        const otherPages = pages.filter((_, idx) => idx !== i);
        await Promise.all(otherPages.map(page => 
          page.waitForFunction(
            (text) => {
              const content = document.querySelector('[data-testid="editor-content"]')?.textContent || '';
              return content.includes(text);
            },
            editText,
            { timeout: 5000 }
          )
        ));
        
        editLatencies.push(Date.now() - editStart);
      }

      const avgEditLatency = editLatencies.reduce((a, b) => a + b) / editLatencies.length;
      console.log(`Average edit latency in large document: ${avgEditLatency}ms`);
      expect(avgEditLatency).toBeLessThan(2000); // Edits should sync within 2 seconds

    } finally {
      // Cleanup
      if (project!) {
        await deleteTestProject(project.id);
      }
      await deleteTestUsers(users);
      for (const context of contexts) {
        await context.close();
      }
    }
  });

  test('should maintain performance during extended collaboration session', async ({ browser }) => {
    const users: TestUser[] = [];
    const contexts: BrowserContext[] = [];
    const pages: Page[] = [];
    let project: TestProject;

    try {
      // Create 4 users
      users.push(...await createTestUsers(4));
      project = await createTestProject({
        title: 'Extended Session Test',
        description: 'Testing performance over time',
        ownerId: users[0].id
      });

      // Setup collaborators
      for (let i = 1; i < users.length; i++) {
        await addCollaborator(project.id, users[0].id, users[i].email, 'editor');
      }

      // Create pages
      for (let i = 0; i < users.length; i++) {
        const context = await browser.newContext();
        contexts.push(context);
        
        const page = await context.newPage();
        pages.push(page);
        
        await loginUser(page, users[i]);
        await page.goto(`/projects/${project.id}/editor`);
        await page.waitForSelector('[data-testid="editor-content"]');
      }

      const performanceMetrics: any[] = [];
      const testDurationMinutes = 2; // Run for 2 minutes
      const measurementIntervalMs = 10000; // Measure every 10 seconds
      
      const endTime = Date.now() + (testDurationMinutes * 60 * 1000);
      let measurementCount = 0;

      while (Date.now() < endTime) {
        measurementCount++;
        const measurementStart = Date.now();
        
        // Each user types something
        const editPromises = pages.map(async (page, idx) => {
          const text = `Measurement ${measurementCount}, User ${idx}: ${new Date().toISOString()}\n`;
          await page.click('[data-testid="editor-content"]');
          await page.keyboard.press('End');
          await page.type('[data-testid="editor-content"]', text);
        });

        await Promise.all(editPromises);
        
        // Measure sync latency
        const syncLatency = await measureSyncLatency(
          pages[0],
          pages[pages.length - 1],
          `Latency test ${measurementCount}`
        );

        // Get memory usage
        const memoryUsage = await pages[0].evaluate(() => {
          if ('memory' in performance && (performance as any).memory) {
            return (performance as any).memory.usedJSHeapSize / (1024 * 1024);
          }
          return 0;
        });

        // Get network metrics
        const networkMetrics = await getNetworkMetrics(pages[0]);

        performanceMetrics.push({
          timestamp: Date.now() - measurementStart,
          measurement: measurementCount,
          syncLatency,
          memoryUsageMB: memoryUsage,
          networkRequests: networkMetrics.totalRequests
        });

        // Wait for next measurement interval
        await new Promise(resolve => setTimeout(resolve, measurementIntervalMs));
      }

      // Analyze performance degradation
      const firstMetric = performanceMetrics[0];
      const lastMetric = performanceMetrics[performanceMetrics.length - 1];

      const latencyIncrease = ((lastMetric.syncLatency - firstMetric.syncLatency) / firstMetric.syncLatency) * 100;
      const memoryIncrease = ((lastMetric.memoryUsageMB - firstMetric.memoryUsageMB) / firstMetric.memoryUsageMB) * 100;

      console.log('Extended session performance metrics:');
      console.log(`Initial latency: ${firstMetric.syncLatency}ms, Final: ${lastMetric.syncLatency}ms (${latencyIncrease.toFixed(2)}% increase)`);
      console.log(`Initial memory: ${firstMetric.memoryUsageMB.toFixed(2)}MB, Final: ${lastMetric.memoryUsageMB.toFixed(2)}MB (${memoryIncrease.toFixed(2)}% increase)`);

      // Performance should not degrade significantly
      expect(latencyIncrease).toBeLessThan(50); // Less than 50% latency increase
      expect(memoryIncrease).toBeLessThan(100); // Less than 100% memory increase

    } finally {
      // Cleanup
      if (project!) {
        await deleteTestProject(project.id);
      }
      await deleteTestUsers(users);
      for (const context of contexts) {
        await context.close();
      }
    }
  });

  test('should handle rapid user join/leave scenarios', async ({ browser }) => {
    const totalUsers = 10;
    const users: TestUser[] = await createTestUsers(totalUsers);
    let project: TestProject;

    try {
      project = await createTestProject({
        title: 'Join/Leave Performance Test',
        description: 'Testing performance with users joining and leaving',
        ownerId: users[0].id
      });

      // Add all users as collaborators
      for (let i = 1; i < users.length; i++) {
        await addCollaborator(project.id, users[0].id, users[i].email, 'editor');
      }

      // First user stays connected throughout
      const stableContext = await browser.newContext();
      const stablePage = await stableContext.newPage();
      await loginUser(stablePage, users[0]);
      await stablePage.goto(`/projects/${project.id}/editor`);
      await stablePage.waitForSelector('[data-testid="editor-content"]');

      const joinLeaveMetrics: any[] = [];

      // Simulate users joining and leaving
      for (let round = 0; round < 3; round++) {
        const roundStart = Date.now();
        const contexts: BrowserContext[] = [];
        const pages: Page[] = [];

        // Users join
        const joinPromises = users.slice(1, 6).map(async (user, idx) => {
          const context = await browser.newContext();
          contexts.push(context);
          
          const page = await context.newPage();
          pages.push(page);
          
          await loginUser(page, user);
          await page.goto(`/projects/${project.id}/editor`);
          await page.waitForSelector('[data-testid="editor-content"]');
        });

        await Promise.all(joinPromises);
        const joinTime = Date.now() - roundStart;

        // Measure performance with all users
        const latency = await measureSyncLatency(
          stablePage,
          pages[0],
          `Round ${round} test`
        );

        // Users leave
        const leaveStart = Date.now();
        await Promise.all(contexts.map(ctx => ctx.close()));
        const leaveTime = Date.now() - leaveStart;

        joinLeaveMetrics.push({
          round,
          joinTime,
          leaveTime,
          latencyWithAllUsers: latency
        });
      }

      // Analyze metrics
      console.log('Join/Leave performance metrics:', joinLeaveMetrics);
      
      const avgJoinTime = joinLeaveMetrics.reduce((a, b) => a + b.joinTime, 0) / joinLeaveMetrics.length;
      const avgLeaveTime = joinLeaveMetrics.reduce((a, b) => a + b.leaveTime, 0) / joinLeaveMetrics.length;
      
      expect(avgJoinTime).toBeLessThan(10000); // Users should join within 10 seconds
      expect(avgLeaveTime).toBeLessThan(5000); // Cleanup should be fast

      await stableContext.close();

    } finally {
      // Cleanup
      if (project!) {
        await deleteTestProject(project.id);
      }
      await deleteTestUsers(users);
    }
  });
});

// Helper functions
async function loginUser(page: Page, user: TestUser) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('[data-testid="login-btn"]');
  await page.waitForURL('/dashboard');
}

async function addCollaborator(projectId: string, ownerId: string, collaboratorEmail: string, role: string) {
  console.log(`Adding ${collaboratorEmail} as ${role} to project ${projectId}`);
}