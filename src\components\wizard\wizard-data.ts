export const genreOptions = [
  'Fantasy', 
  'Science Fiction', 
  'Mystery', 
  'Romance', 
  'Thriller', 
  'Horror',
  'Historical Fiction', 
  'Literary Fiction', 
  'Young Adult', 
  'Children\'s',
  'Non-Fiction', 
  'Biography', 
  'Self-Help', 
  'Other'
]

export const toneOptions = [
  'Epic', 
  'Adventurous', 
  'Dark', 
  'Mysterious', 
  'Humorous', 
  'Lighthearted',
  'Serious', 
  'Emotional', 
  'Romantic', 
  'Action-packed', 
  'Suspenseful', 
  'Gritty',
  'Whimsical', 
  'Melancholic', 
  'Inspiring', 
  'Satirical', 
  'Philosophical', 
  'Introspective',
  'Fast-paced', 
  'Contemplative', 
  'Dramatic', 
  'Intense', 
  'Nostalgic', 
  'Hopeful'
]

export const themeOptions = [
  // Universal Themes
  'Love', 
  'Death', 
  'Power', 
  'Freedom', 
  'Justice', 
  'Redemption', 
  'Sacrifice', 
  'Hope',
  'Fear', 
  'Identity', 
  'Family', 
  'Friendship', 
  'Betrayal', 
  'Forgiveness', 
  'Destiny', 
  '<PERSON>',
  'Good vs Evil', 
  'Coming of Age', 
  'Loss', 
  'Survival', 
  'Truth', 
  'Honor', 
  'Courage', 
  'Wisdom',
  
  // Genre-Specific Themes
  'Time', 
  'Technology', 
  'Nature', 
  'Magic', 
  'War', 
  'Peace', 
  'Revolution', 
  'Discovery',
  'Adventure', 
  'Mystery', 
  'Transformation', 
  'Isolation', 
  'Community', 
  'Legacy', 
  'Memory', 
  'Dreams',
  'Ambition', 
  'Corruption', 
  'Innocence', 
  'Experience', 
  'Tradition', 
  'Progress', 
  'Faith', 
  'Doubt',
  'Duty', 
  'Passion', 
  'Vengeance', 
  'Loyalty', 
  'Deception', 
  'Reality', 
  'Illusion', 
  'Chaos',
  'Order', 
  'Creation', 
  'Destruction', 
  'Knowledge', 
  'Ignorance', 
  'Pride', 
  'Humility', 
  'Greed'
]

export const targetAudienceOptions = [
  { value: 'children', label: 'Children (8-12)' },
  { value: 'young-adult', label: 'Young Adult (13-17)' },
  { value: 'new-adult', label: 'New Adult (18-25)' },
  { value: 'adult', label: 'Adult (25+)' },
  { value: 'all-ages', label: 'All Ages' }
]