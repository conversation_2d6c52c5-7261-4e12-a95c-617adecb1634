'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Brain, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Pause, 
  RotateCcw,
  Zap,
  Eye,
  Trash2
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { aiProcessingQueue } from '@/lib/services/ai-processing-queue';
import type { ProcessingProgress, ProcessingTask } from '@/lib/services/ai-processing-queue';
import { ComponentErrorBoundary } from '@/components/error/component-error-boundary';
import { TIME_SECONDS } from '@/lib/constants'

interface ProcessingStatusProps {
  projectId: string;
  userId: string;
  compact?: boolean;
}

function ProcessingStatusComponent({ projectId, compact = false }: ProcessingStatusProps) {
  const [progress, setProgress] = useState<ProcessingProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProgress = async () => {
      try {
        const projectProgress = await aiProcessingQueue.getProjectProgress(projectId);
        setProgress(projectProgress);
      } catch (_error) {
        // Failed to fetch processing progress - non-critical error
      } finally {
        setIsLoading(false);
      }
    };

    fetchProgress();
    
    // Update every 2 seconds
    const interval = setInterval(fetchProgress, 2000);
    return () => clearInterval(interval);
  }, [projectId]);

  const getTaskTypeLabel = (type: ProcessingTask['type']): string => {
    const labels = {
      'content_analysis': 'Content Analysis',
      'character_development': 'Character Development',
      'plot_consistency': 'Plot Consistency',
      'pacing_analysis': 'Pacing Analysis',
      'voice_matching': 'Voice Matching',
      'world_building': 'World Building',
      'timeline_validation': 'Timeline Validation',
    };
    return labels[type] || type;
  };

  const getTaskIcon = (type: ProcessingTask['type']) => {
    const icons = {
      'content_analysis': Brain,
      'character_development': Brain,
      'plot_consistency': CheckCircle,
      'pacing_analysis': Zap,
      'voice_matching': Brain,
      'world_building': Brain,
      'timeline_validation': Clock,
    };
    const Icon = icons[type] || Brain;
    return <Icon className="h-4 w-4" />;
  };

  const getStatusColor = (status: ProcessingTask['status']) => {
    switch (status) {
      case 'pending': return 'bg-warning-light text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'processing': return 'bg-info-light text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed': return 'bg-success-light text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'failed': return 'bg-error-light text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getPriorityColor = (priority: ProcessingTask['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-error';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-warning';
      case 'low': return 'bg-success';
      default: return 'bg-gray-500';
    }
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < TIME_SECONDS.HOUR) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / TIME_SECONDS.HOUR)}h ${Math.floor((seconds % TIME_SECONDS.HOUR) / 60)}m`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-full">
          <div className="flex items-center gap-2 text-slate-500">
            <Brain className="h-4 w-4 animate-pulse" />
            <span className="text-sm">Loading processing status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!progress || !progress.isActive) {
    return compact ? null : (
      <Card>
        <CardContent className="flex items-center justify-center py-6 sm:py-8 lg:py-10">
          <div className="text-center">
            <Brain className="h-8 w-8 mx-auto mb-2 text-slate-400" />
            <p className="text-sm text-slate-600 dark:text-slate-400">No active AI processing</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className="p-3 bg-info-light dark:bg-blue-950/20 rounded-lg">
        <div className="flex items-center gap-2 mb-1">
          <Brain className="h-4 w-4 text-info animate-pulse" />
          <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
            AI Processing Active
          </span>
        </div>
        {progress.currentTask && (
          <p className="text-xs text-blue-700 dark:text-blue-300 mb-1">
            {getTaskTypeLabel(progress.currentTask.type)}
          </p>
        )}
        <div className="flex items-center justify-between text-xs text-info dark:text-blue-400">
          <span>{progress.queuedTasks.length} tasks in queue</span>
          <span>
            {formatDuration(progress.estimatedTimeRemaining)} remaining
          </span>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-info animate-pulse" />
              AI Processing Status
            </CardTitle>
            <CardDescription>
              Real-time processing updates for your project
            </CardDescription>
          </div>
          <Badge variant="outline" className="text-info border-blue-200">
            {progress.totalTasks} Total Tasks
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm text-slate-600">
              {progress.completedCount} / {progress.totalTasks} completed
            </span>
          </div>
          <Progress 
            value={(progress.completedCount / progress.totalTasks) * 100} 
            className="h-2"
          />
          <div className="flex items-center justify-between text-xs text-slate-500">
            <span>
              Estimated time remaining: {formatDuration(progress.estimatedTimeRemaining)}
            </span>
            <span>
              {Math.round((progress.completedCount / progress.totalTasks) * 100)}% complete
            </span>
          </div>
        </div>

        {/* Current Task */}
        {progress.currentTask && (
          <div className="p-4 bg-info-light dark:bg-blue-950/20 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                {getTaskIcon(progress.currentTask.type)}
                <span className="font-medium text-blue-800 dark:text-blue-200">
                  Currently Processing
                </span>
              </div>
              <Badge className={getStatusColor(progress.currentTask.status)}>
                {progress.currentTask.status}
              </Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {getTaskTypeLabel(progress.currentTask.type)}
              </p>
              <div className="flex items-center justify-between text-xs text-info dark:text-blue-400">
                <span>Priority: {progress.currentTask.priority}</span>
                {progress.currentTask.startedAt && (
                  <span>
                    Started {formatDistanceToNow(progress.currentTask.startedAt, { addSuffix: true })}
                  </span>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Task Queue */}
        {progress.queuedTasks.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">Queued Tasks ({progress.queuedTasks.length})</h4>
              <Button variant="outline" size="sm">
                <Pause className="h-4 w-4 mr-1" />
                Pause Queue
              </Button>
            </div>
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {progress.queuedTasks.slice(0, 10).map((task, index) => (
                  <div key={task.id} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`} />
                        <span className="text-xs text-slate-500">#{index + 1}</span>
                      </div>
                      {getTaskIcon(task.type)}
                      <div>
                        <p className="text-sm font-medium">
                          {getTaskTypeLabel(task.type)}
                        </p>
                        <p className="text-xs text-slate-500">
                          Est. {formatDuration(task.estimatedDuration)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Badge variant="outline" className={getStatusColor(task.status)}>
                        {task.status}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
                {progress.queuedTasks.length > 10 && (
                  <div className="text-center py-2">
                    <span className="text-xs text-slate-500">
                      +{progress.queuedTasks.length - 10} more tasks
                    </span>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Recent Completions */}
        {progress.completedTasks.length > 0 && (
          <div>
            <h4 className="font-medium mb-3">Recent Completions</h4>
            <ScrollArea className="h-32">
              <div className="space-y-2">
                {progress.completedTasks.slice(-5).reverse().map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-2 bg-success-light dark:bg-green-950/20 rounded-lg">
                    <div className="flex items-center gap-2">
                      {task.status === 'completed' ? (
                        <CheckCircle className="h-4 w-4 text-success" />
                      ) : (
                        <XCircle className="h-4 w-4 text-error" />
                      )}
                      <span className="text-sm">{getTaskTypeLabel(task.type)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {task.completedAt && (
                        <span className="text-xs text-slate-500">
                          {format(task.completedAt, 'HH:mm')}
                        </span>
                      )}
                      <Button variant="ghost" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Pause className="h-4 w-4 mr-1" />
              Pause All
            </Button>
            <Button variant="outline" size="sm">
              <RotateCcw className="h-4 w-4 mr-1" />
              Retry Failed
            </Button>
          </div>
          <Button variant="outline" size="sm">
            <Trash2 className="h-4 w-4 mr-1" />
            Clear Completed
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Export wrapped component with error boundary
export function ProcessingStatus(props: ProcessingStatusProps) {
  return (
    <ComponentErrorBoundary componentName="Processing Status">
      <ProcessingStatusComponent {...props} />
    </ComponentErrorBoundary>
  );
}