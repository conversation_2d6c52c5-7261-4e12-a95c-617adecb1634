import { describe, it, expect } from '@jest/globals';
import { UnifiedResponse } from '@/lib/api/unified-response';
import { NextResponse } from 'next/server';

describe('UnifiedResponse', () => {
  describe('success', () => {
    it('should create a success response with data', () => {
      const data = { id: 1, name: 'Test' };
      const response = UnifiedResponse.success(data);
      
      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(200);
    });

    it('should create a success response with custom status', () => {
      const data = { created: true };
      const response = UnifiedResponse.success(data, 201);
      
      expect(response.status).toBe(201);
    });

    it('should create a success response with meta information', () => {
      const data = { items: [] };
      const meta = { page: 1, total: 100 };
      const response = UnifiedResponse.success(data, 200, meta);
      
      expect(response.status).toBe(200);
    });
  });

  describe('error', () => {
    it('should create an error response', () => {
      const response = UnifiedResponse.error('Something went wrong');
      
      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(500);
    });

    it('should create an error response with custom status', () => {
      const response = UnifiedResponse.error('Not found', 404);
      
      expect(response.status).toBe(404);
    });

    it('should create an error response with details', () => {
      const details = { field: 'email', issue: 'invalid format' };
      const response = UnifiedResponse.error('Validation failed', 400, details);
      
      expect(response.status).toBe(400);
    });
  });

  describe('badRequest', () => {
    it('should create a 400 bad request response', () => {
      const response = UnifiedResponse.badRequest('Invalid input');
      
      expect(response.status).toBe(400);
    });

    it('should handle validation errors', () => {
      const errors = [
        { field: 'email', message: 'Invalid email' },
        { field: 'password', message: 'Too short' }
      ];
      const response = UnifiedResponse.badRequest('Validation failed', errors);
      
      expect(response.status).toBe(400);
    });
  });

  describe('unauthorized', () => {
    it('should create a 401 unauthorized response', () => {
      const response = UnifiedResponse.unauthorized();
      
      expect(response.status).toBe(401);
    });

    it('should accept custom message', () => {
      const response = UnifiedResponse.unauthorized('Invalid token');
      
      expect(response.status).toBe(401);
    });
  });

  describe('forbidden', () => {
    it('should create a 403 forbidden response', () => {
      const response = UnifiedResponse.forbidden();
      
      expect(response.status).toBe(403);
    });

    it('should accept custom message', () => {
      const response = UnifiedResponse.forbidden('Insufficient permissions');
      
      expect(response.status).toBe(403);
    });
  });

  describe('notFound', () => {
    it('should create a 404 not found response', () => {
      const response = UnifiedResponse.notFound();
      
      expect(response.status).toBe(404);
    });

    it('should accept custom message', () => {
      const response = UnifiedResponse.notFound('Project not found');
      
      expect(response.status).toBe(404);
    });
  });

  describe('conflict', () => {
    it('should create a 409 conflict response', () => {
      const response = UnifiedResponse.conflict('Resource already exists');
      
      expect(response.status).toBe(409);
    });
  });

  describe('tooManyRequests', () => {
    it('should create a 429 too many requests response', () => {
      const response = UnifiedResponse.tooManyRequests();
      
      expect(response.status).toBe(429);
    });

    it('should include retry after header', () => {
      const response = UnifiedResponse.tooManyRequests(60);
      
      expect(response.status).toBe(429);
      expect(response.headers.get('Retry-After')).toBe('60');
    });
  });

  describe('serverError', () => {
    it('should create a 500 server error response', () => {
      const response = UnifiedResponse.serverError();
      
      expect(response.status).toBe(500);
    });

    it('should hide error details in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';
      
      const error = new Error('Database connection failed');
      const response = UnifiedResponse.serverError(error);
      
      expect(response.status).toBe(500);
      
      process.env.NODE_ENV = originalEnv;
    });

    it('should show error details in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';
      
      const error = new Error('Database connection failed');
      const response = UnifiedResponse.serverError(error);
      
      expect(response.status).toBe(500);
      
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('stream', () => {
    it('should create a streaming response', () => {
      const stream = new ReadableStream();
      const response = UnifiedResponse.stream(stream);
      
      expect(response).toBeInstanceOf(Response);
      expect(response.headers.get('Content-Type')).toBe('text/event-stream');
    });

    it('should set proper headers for SSE', () => {
      const stream = new ReadableStream();
      const response = UnifiedResponse.stream(stream);
      
      expect(response.headers.get('Cache-Control')).toBe('no-cache');
      expect(response.headers.get('Connection')).toBe('keep-alive');
    });
  });

  describe('redirect', () => {
    it('should create a redirect response', () => {
      const response = UnifiedResponse.redirect('/dashboard');
      
      expect(response.status).toBe(307);
    });

    it('should support permanent redirects', () => {
      const response = UnifiedResponse.redirect('/new-location', 301);
      
      expect(response.status).toBe(301);
    });
  });

  describe('noContent', () => {
    it('should create a 204 no content response', () => {
      const response = UnifiedResponse.noContent();
      
      expect(response.status).toBe(204);
    });
  });
});