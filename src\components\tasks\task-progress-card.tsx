'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Loader2, 
  CheckCircle, 
  XCircle,
  Clock,
  FileText,
  Bot,
  Download
} from 'lucide-react'
import { TaskStatus } from '@/lib/services/task-queue-service'

interface TaskProgressCardProps {
  taskId: string
  onComplete?: (result: any) => void
  onError?: (error: string) => void
}

export function TaskProgressCard({ taskId, onComplete, onError }: TaskProgressCardProps) {
  const [task, setTask] = useState<any>(null)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const fetchTaskStatus = async () => {
      try {
        const response = await fetch(`/api/processing-tasks/${taskId}`)
        if (response.ok) {
          const data = await response.json()
          setTask(data.task)
          
          // Calculate progress
          if (data.task.status === TaskStatus.COMPLETED) {
            setProgress(100)
            onComplete?.(data.task.result)
          } else if (data.task.status === TaskStatus.FAILED) {
            setProgress(0)
            onError?.(data.task.error)
          } else if (data.task.status === TaskStatus.PROCESSING) {
            // Estimate progress based on time elapsed
            const started = new Date(data.task.started_at).getTime()
            const now = Date.now()
            const elapsed = now - started
            const estimatedDuration = 30000 // 30 seconds estimated
            setProgress(Math.min((elapsed / estimatedDuration) * 100, 90))
          }
        }
      } catch (error) {
        console.error('Error fetching task status:', error)
      }
    }

    fetchTaskStatus()
    
    // Poll for updates while task is not complete
    const interval = setInterval(() => {
      if (task && [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED].includes(task.status)) {
        clearInterval(interval)
      } else {
        fetchTaskStatus()
      }
    }, 2000)

    return () => clearInterval(interval)
  }, [taskId, task?.status])

  if (!task) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    )
  }

  const getIcon = () => {
    switch (task.status) {
      case TaskStatus.COMPLETED:
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case TaskStatus.FAILED:
        return <XCircle className="h-5 w-5 text-red-500" />
      case TaskStatus.PROCESSING:
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />
    }
  }

  const getTaskTypeIcon = () => {
    if (task.type.includes('export')) return <Download className="h-4 w-4" />
    if (task.type.includes('generate') || task.type.includes('analyze')) return <Bot className="h-4 w-4" />
    return <FileText className="h-4 w-4" />
  }

  const formatTaskType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getTaskTypeIcon()}
              <div>
                <h4 className="text-sm font-medium">
                  {formatTaskType(task.type)}
                </h4>
                <p className="text-xs text-muted-foreground">
                  {task.status === TaskStatus.PROCESSING ? 'Processing...' : 
                   task.status === TaskStatus.COMPLETED ? 'Completed' :
                   task.status === TaskStatus.FAILED ? 'Failed' :
                   'Queued'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={
                task.status === TaskStatus.COMPLETED ? 'success' :
                task.status === TaskStatus.FAILED ? 'destructive' :
                task.status === TaskStatus.PROCESSING ? 'default' :
                'secondary'
              }>
                {task.status}
              </Badge>
              {getIcon()}
            </div>
          </div>
          
          {task.status === TaskStatus.PROCESSING && (
            <Progress value={progress} className="h-2" />
          )}
          
          {task.error && (
            <p className="text-xs text-red-500">
              {task.error}
            </p>
          )}
          
          {task.result && task.status === TaskStatus.COMPLETED && (
            <div className="text-xs text-muted-foreground">
              Task completed successfully
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}