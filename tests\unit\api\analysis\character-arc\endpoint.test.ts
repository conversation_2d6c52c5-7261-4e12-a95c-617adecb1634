import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { NextRequest } from 'next/server'

// Polyfill Response.json if missing
if (typeof Response.json !== 'function') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Response.json = (data: any, init?: ResponseInit) =>
    new Response(JSON.stringify(data), {
      ...(init || {}),
      headers: { 'content-type': 'application/json', ...(init?.headers || {}) }
    })
}

jest.mock('@/lib/auth/unified-auth-service', () => ({
  UnifiedAuthService: { withAuth: (handler: any) => handler }
}))

jest.mock('@/lib/ai/openai-client', () => ({
  openai: { chat: { completions: { create: jest.fn() } } }
}))

jest.mock('@/lib/supabase', () => ({
  createTypedServerClient: jest.fn()
}))

jest.mock('@/lib/services/logger', () => ({
  logger: { error: jest.fn() }
}))

jest.mock('@/lib/db/project-access', () => ({
  verifyProjectAccess: jest.fn(),
  PROJECT_ACCESS_ERROR: 'No access'
}))

const { GET } = require('@/app/api/analysis/character-arc/route')
const { createTypedServerClient } = require('@/lib/supabase')
const { openai } = require('@/lib/ai/openai-client')
const { verifyProjectAccess } = require('@/lib/db/project-access')

const mockOpenAI = openai.chat.completions.create as jest.Mock
const mockCreateClient = createTypedServerClient as jest.Mock
const mockVerifyProjectAccess = verifyProjectAccess as jest.Mock

describe('Character Arc Analysis GET endpoint', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockVerifyProjectAccess.mockResolvedValue({ id: '11111111-1111-1111-1111-111111111111' })
    mockOpenAI.mockResolvedValue({
      choices: [{ message: { content: JSON.stringify({ events: [] }) } }]
    })
  })

  it('includes relationships array in response when relationships exist', async () => {
    const mockSupabase = {
      from: jest.fn((table: string) => {
        if (table === 'characters') {
          return {
            select: jest.fn((cols: string) => {
              if (cols === '*') {
                return {
                  eq: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                      single: jest.fn().mockResolvedValue({
                        data: { id: '22222222-2222-2222-2222-222222222222', name: 'Alice' }
                      })
                    })
                  })
                }
              }
              // project characters
              return {
                eq: jest.fn().mockResolvedValue({
                  data: [
                    { id: '22222222-2222-2222-2222-222222222222', name: 'Alice' },
                    { id: '33333333-3333-3333-3333-333333333333', name: 'Bob' }
                  ]
                })
              }
            })
          }
        }
        if (table === 'chapters') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                  data: [
                    {
                      id: 'ch1',
                      chapter_number: 1,
                      content: 'Alice helped Bob. Bob thanked Alice.',
                      title: 'Ch1'
                    }
                  ]
                })
              })
            })
          }
        }
        return { select: jest.fn() }
      })
    }

    mockCreateClient.mockResolvedValue(mockSupabase)

    const request = new NextRequest(
      'http://localhost/api/analysis/character-arc?projectId=11111111-1111-1111-1111-111111111111&characterId=22222222-2222-2222-2222-222222222222'
    )
    ;(request as any).user = { id: 'user1' }

    const response = await GET(request as any)
    const body = await response.json()

    expect(response.status).toBe(200)
    expect(body.data.relationships).toEqual([
      { characterId: '33333333-3333-3333-3333-333333333333', characterName: 'Bob', type: 'ally', interactions: 2 }
    ])
  })

  it('returns empty relationships array when no chapters found', async () => {
    const mockSupabase = {
      from: jest.fn((table: string) => {
        if (table === 'characters') {
          return {
            select: jest.fn((cols: string) => {
              if (cols === '*') {
                return {
                  eq: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                      single: jest.fn().mockResolvedValue({
                        data: { id: '22222222-2222-2222-2222-222222222222', name: 'Alice' }
                      })
                    })
                  })
                }
              }
              return {
                eq: jest.fn().mockResolvedValue({
                  data: [{ id: '22222222-2222-2222-2222-222222222222', name: 'Alice' }]
                })
              }
            })
          }
        }
        if (table === 'chapters') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({ data: [] })
              })
            })
          }
        }
        return { select: jest.fn() }
      })
    }

    mockCreateClient.mockResolvedValue(mockSupabase)

    const request = new NextRequest(
      'http://localhost/api/analysis/character-arc?projectId=11111111-1111-1111-1111-111111111111&characterId=22222222-2222-2222-2222-222222222222'
    )
    ;(request as any).user = { id: 'user1' }

    const response = await GET(request as any)
    const body = await response.json()

    expect(response.status).toBe(200)
    expect(body.data.relationships).toEqual([])
    expect(mockOpenAI).not.toHaveBeenCalled()
  })
})

