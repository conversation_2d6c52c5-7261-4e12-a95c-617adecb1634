import { EventEmitter } from 'events';
import { logger } from '@/lib/services/logger';
import { trackError } from '@/lib/monitoring/sentry';

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: number;
  userId?: string;
  sessionId?: string;
}

export interface CollaborationEvent {
  type: 'cursor' | 'selection' | 'edit' | 'presence' | 'awareness';
  data: any;
  userId: string;
  timestamp: number;
}

export interface ConnectionOptions {
  url: string;
  token: string;
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  debug?: boolean;
}

export class WebSocketClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private options: Required<ConnectionOptions>;
  private reconnectAttempts = 0;
  private heartbeatTimer?: NodeJS.Timeout;
  private reconnectTimer?: NodeJS.Timeout;
  private messageQueue: WebSocketMessage[] = [];
  private isConnecting = false;
  private sessionId: string;

  constructor(options: ConnectionOptions) {
    super();
    
    this.options = {
      reconnect: true,
      reconnectInterval: 1000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      debug: false,
      ...options,
    };
    
    this.sessionId = this.generateSessionId();
  }

  connect(): void {
    if (this.isConnecting || this.isConnected()) {
      return;
    }

    this.isConnecting = true;
    this.emit('connecting');

    try {
      // Add authentication token to URL
      const url = new URL(this.options.url);
      url.searchParams.set('token', this.options.token);
      url.searchParams.set('sessionId', this.sessionId);

      this.ws = new WebSocket(url.toString());
      this.setupEventListeners();
    } catch (error) {
      this.isConnecting = false;
      this.handleError('Connection failed', error);
      this.scheduleReconnect();
    }
  }

  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.emit('connected');
      this.log('WebSocket connected');
      
      // Start heartbeat
      this.startHeartbeat();
      
      // Send queued messages
      this.flushMessageQueue();
    };

    this.ws.onclose = (event) => {
      this.isConnecting = false;
      this.emit('disconnected', event.code, event.reason);
      this.log(`WebSocket disconnected: ${event.code} ${event.reason}`);
      
      this.stopHeartbeat();
      
      if (this.options.reconnect && !event.wasClean) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      this.handleError('WebSocket error', error);
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data) as WebSocketMessage;
        this.handleMessage(message);
      } catch (error) {
        this.handleError('Failed to parse message', error);
      }
    };
  }

  private handleMessage(message: WebSocketMessage): void {
    this.log('Received message:', message.type);
    
    switch (message.type) {
      case 'pong':
        // Heartbeat response
        break;
        
      case 'error':
        this.emit('error', new Error(message.payload.message));
        break;
        
      case 'collaboration':
        this.emit('collaboration', message.payload as CollaborationEvent);
        break;
        
      case 'presence':
        this.emit('presence', message.payload);
        break;
        
      case 'sync':
        this.emit('sync', message.payload);
        break;
        
      default:
        this.emit('message', message);
    }
  }

  send(type: string, payload: any): void {
    const message: WebSocketMessage = {
      type,
      payload,
      timestamp: Date.now(),
      sessionId: this.sessionId,
    };

    if (this.isConnected()) {
      try {
        this.ws!.send(JSON.stringify(message));
        this.log('Sent message:', type);
      } catch (error) {
        this.handleError('Failed to send message', error);
        this.queueMessage(message);
      }
    } else {
      this.queueMessage(message);
    }
  }

  // Collaboration-specific methods
  sendCursorPosition(position: { line: number; column: number }): void {
    this.send('collaboration', {
      type: 'cursor',
      data: position,
    });
  }

  sendSelection(selection: { start: any; end: any }): void {
    this.send('collaboration', {
      type: 'selection',
      data: selection,
    });
  }

  sendEdit(edit: { 
    operation: 'insert' | 'delete' | 'replace';
    position: any;
    content?: string;
    length?: number;
  }): void {
    this.send('collaboration', {
      type: 'edit',
      data: edit,
    });
  }

  sendPresence(presence: { 
    status: 'active' | 'idle' | 'away';
    cursor?: any;
    selection?: any;
  }): void {
    this.send('presence', presence);
  }

  disconnect(): void {
    this.options.reconnect = false;
    this.stopHeartbeat();
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.emit('disconnected', 1000, 'Client disconnect');
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.send('ping', { timestamp: Date.now() });
      }
    }, this.options.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = undefined;
    }
  }

  private scheduleReconnect(): void {
    if (!this.options.reconnect || 
        this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.emit('reconnectFailed');
      return;
    }

    this.clearReconnectTimer();
    
    const delay = Math.min(
      this.options.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      30000
    );
    
    this.reconnectAttempts++;
    this.emit('reconnecting', this.reconnectAttempts, delay);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = undefined;
    }
  }

  private queueMessage(message: WebSocketMessage): void {
    this.messageQueue.push(message);
    
    // Limit queue size
    if (this.messageQueue.length > 100) {
      this.messageQueue.shift();
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!;
      try {
        this.ws!.send(JSON.stringify(message));
      } catch (error) {
        this.handleError('Failed to send queued message', error);
        this.messageQueue.unshift(message);
        break;
      }
    }
  }

  private handleError(context: string, error: any): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`WebSocket ${context}:`, error);
    
    trackError(error, {
      tags: { component: 'websocket', context },
      extra: { sessionId: this.sessionId }
    });
    
    this.emit('error', new Error(`${context}: ${errorMessage}`));
  }

  private log(...args: any[]): void {
    if (this.options.debug) {
      logger.debug('[WebSocket]', ...args);
    }
  }

  private generateSessionId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}