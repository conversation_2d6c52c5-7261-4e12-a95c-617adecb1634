'use client'

import dynamic from 'next/dynamic'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Loading skeleton for chart components
const ChartSkeleton = ({ title }: { title: string }) => (
  <Card>
    <CardHeader>
      <CardTitle>{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <Skeleton className="h-[300px] w-full" />
    </CardContent>
  </Card>
)

// Lazy load analytics dashboard
export const LazyAnalyticsDashboard = dynamic(
  () => import('./analytics-dashboard').then(mod => ({ default: mod.AnalyticsDashboard })),
  {
    loading: () => (
      <div className="space-y-4">
        <ChartSkeleton title="Loading Analytics..." />
      </div>
    ),
    ssr: false
  }
)

// Lazy load writing analytics dashboard
export const LazyWritingAnalyticsDashboard = dynamic(
  () => import('./writing-analytics-dashboard'),
  {
    loading: () => (
      <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        <ChartSkeleton title="Writing Progress" />
        <ChartSkeleton title="Daily Word Count" />
        <ChartSkeleton title="Chapter Progress" />
      </div>
    ),
    ssr: false
  }
)

// Lazy load individual analytics components
export const LazyProfilePerformanceMetrics = dynamic(
  () => import('./components/profile-performance-metrics').then(mod => ({ 
    default: mod.ProfilePerformanceMetrics 
  })),
  {
    loading: () => <ChartSkeleton title="Profile Performance" />,
    ssr: false
  }
)

export const LazySuccessPatternAnalysis = dynamic(
  () => import('./components/success-pattern-analysis').then(mod => ({ 
    default: mod.SuccessPatternAnalysis 
  })),
  {
    loading: () => <ChartSkeleton title="Success Patterns" />,
    ssr: false
  }
)

export const LazyVoiceConsistencyMetrics = dynamic(
  () => import('./components/voice-consistency-metrics').then(mod => ({ 
    default: mod.VoiceConsistencyMetrics 
  })),
  {
    loading: () => <ChartSkeleton title="Voice Consistency" />,
    ssr: false
  }
)

export const LazySmartRecommendations = dynamic(
  () => import('./components/smart-recommendations').then(mod => ({ 
    default: mod.SmartRecommendations 
  })),
  {
    loading: () => <ChartSkeleton title="Recommendations" />,
    ssr: false
  }
)