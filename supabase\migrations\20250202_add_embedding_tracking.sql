-- Add embedding tracking columns to chapters table
ALTER TABLE chapters
ADD COLUMN IF NOT EXISTS embedding_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS embedding_generated_at timestamp with time zone;

-- Add embedding tracking columns to story_bible table
ALTER TABLE story_bible
ADD COLUMN IF NOT EXISTS embedding_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS embedding_generated_at timestamp with time zone;

-- Add indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_chapters_embedding_pending 
ON chapters(embedding_generated) 
WHERE embedding_generated = false AND content IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_story_bible_embedding_pending 
ON story_bible(embedding_generated) 
WHERE embedding_generated = false AND data IS NOT NULL;

-- Add function to automatically mark embeddings as outdated when content changes
CREATE OR REPLACE FUNCTION mark_embedding_outdated()
RETURNS TRIGGER AS $$
BEGIN
  -- For chapters
  IF TG_TABLE_NAME = 'chapters' THEN
    IF OLD.content IS DISTINCT FROM NEW.content THEN
      NEW.embedding_generated = false;
      NEW.embedding_generated_at = NULL;
    END IF;
  END IF;
  
  -- For story_bible
  IF TG_TABLE_NAME = 'story_bible' THEN
    IF OLD.data IS DISTINCT FROM NEW.data THEN
      NEW.embedding_generated = false;
      NEW.embedding_generated_at = NULL;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER chapters_embedding_outdated
BEFORE UPDATE ON chapters
FOR EACH ROW
EXECUTE FUNCTION mark_embedding_outdated();

CREATE TRIGGER story_bible_embedding_outdated
BEFORE UPDATE ON story_bible
FOR EACH ROW
EXECUTE FUNCTION mark_embedding_outdated();