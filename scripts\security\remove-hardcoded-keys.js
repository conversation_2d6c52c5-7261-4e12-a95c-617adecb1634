#!/usr/bin/env node

/**
 * Security Script: Remove Hardcoded Service Role Keys
 * 
 * This script identifies and helps remove hardcoded Supabase service role keys
 * from the codebase as identified in the security audit.
 * 
 * CRITICAL: This addresses the security audit finding of hardcoded production keys.
 */

const fs = require('fs');
const path = require('path');

// Files identified in security audit that contain hardcoded keys
const CRITICAL_FILES = [
  '.claude/settings.local.json',
  'scripts/test/test-migration.mjs',
  'scripts/test/simple-test.js',
  'scripts/db/run-consolidated-migration.mjs',
  'scripts/db/execute-migrations.mjs',
  'scripts/db/create-test-chapter.js'
];

// Pattern to detect Supabase service role keys
const SERVICE_KEY_PATTERNS = [
  /eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*/g, // JWT pattern
  /sk_[a-zA-Z0-9_-]{40,}/g, // Service key pattern
  /SUPABASE_SERVICE_ROLE_KEY["']?\s*[:=]\s*["'][^"']+["']/gi,
  /service_role["']?\s*[:=]\s*["'][^"']+["']/gi
];

function scanFileForKeys(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return { found: false, reason: 'File does not exist' };
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const findings = [];

    SERVICE_KEY_PATTERNS.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          findings.push({
            pattern: index,
            match: match.substring(0, 50) + '...', // Truncate for security
            line: content.substring(0, content.indexOf(match)).split('\n').length
          });
        });
      }
    });

    return {
      found: findings.length > 0,
      findings,
      content: findings.length > 0 ? content : null
    };
  } catch (error) {
    return { found: false, reason: `Error reading file: ${error.message}` };
  }
}

function createBackup(filePath) {
  const backupPath = `${filePath}.backup-${Date.now()}`;
  try {
    fs.copyFileSync(filePath, backupPath);
    return backupPath;
  } catch (error) {
    console.error(`Failed to create backup for ${filePath}:`, error.message);
    return null;
  }
}

function removeKeysFromFile(filePath, content) {
  let cleanedContent = content;

  // Replace hardcoded keys with environment variable references
  SERVICE_KEY_PATTERNS.forEach(pattern => {
    cleanedContent = cleanedContent.replace(pattern, (match) => {
      if (match.includes('SUPABASE_SERVICE_ROLE_KEY') || match.includes('service_role')) {
        // Replace with environment variable reference
        if (match.includes(':')) {
          return match.replace(/["'][^"']+["']/, 'process.env.SUPABASE_SERVICE_ROLE_KEY');
        } else if (match.includes('=')) {
          return match.replace(/["'][^"']+["']/, 'process.env.SUPABASE_SERVICE_ROLE_KEY');
        }
      }
      // For JWT patterns, replace with placeholder
      return 'REMOVED_FOR_SECURITY';
    });
  });

  return cleanedContent;
}

function generateSecurityReport() {
  const report = {
    timestamp: new Date().toISOString(),
    scannedFiles: [],
    criticalFindings: [],
    recommendations: []
  };

  console.log('🔍 Scanning for hardcoded service role keys...\n');

  CRITICAL_FILES.forEach(filePath => {
    console.log(`Scanning: ${filePath}`);
    const result = scanFileForKeys(filePath);
    
    report.scannedFiles.push({
      path: filePath,
      found: result.found,
      reason: result.reason,
      findings: result.findings || []
    });

    if (result.found) {
      console.log(`❌ CRITICAL: Found ${result.findings.length} potential keys in ${filePath}`);
      result.findings.forEach(finding => {
        console.log(`   Line ${finding.line}: ${finding.match}`);
      });
      
      report.criticalFindings.push({
        file: filePath,
        findings: result.findings
      });
    } else {
      const reason = result.reason ? ` (${result.reason})` : '';
      console.log(`✅ Clean: ${filePath}${reason}`);
    }
  });

  // Scan additional common locations
  const additionalPaths = [
    'scripts/',
    '.env.local',
    '.env.example',
    'test/',
    'tests/'
  ];

  console.log('\n🔍 Scanning additional locations...\n');

  additionalPaths.forEach(searchPath => {
    if (fs.existsSync(searchPath)) {
      if (fs.statSync(searchPath).isDirectory()) {
        const files = fs.readdirSync(searchPath, { withFileTypes: true });
        files.forEach(file => {
          if (file.isFile() && (file.name.endsWith('.js') || file.name.endsWith('.mjs') || file.name.endsWith('.ts'))) {
            const fullPath = path.join(searchPath, file.name);
            const result = scanFileForKeys(fullPath);
            
            if (result.found) {
              console.log(`❌ FOUND: ${fullPath} contains ${result.findings.length} potential keys`);
              report.criticalFindings.push({
                file: fullPath,
                findings: result.findings
              });
            }
          }
        });
      } else {
        const result = scanFileForKeys(searchPath);
        if (result.found) {
          console.log(`❌ FOUND: ${searchPath} contains ${result.findings.length} potential keys`);
          report.criticalFindings.push({
            file: searchPath,
            findings: result.findings
          });
        }
      }
    }
  });

  // Generate recommendations
  if (report.criticalFindings.length > 0) {
    report.recommendations = [
      'IMMEDIATE ACTION REQUIRED: Hardcoded service role keys found',
      '1. Regenerate the Supabase service role key immediately',
      '2. Update environment variables with the new key',
      '3. Remove or replace hardcoded keys in the identified files',
      '4. Review git history for any committed keys',
      '5. Update any deployed environments with new keys',
      '6. Monitor access logs for unauthorized usage'
    ];
  } else {
    report.recommendations = [
      'Good: No hardcoded service role keys detected',
      'Continue monitoring with regular security scans',
      'Ensure all new code uses environment variables'
    ];
  }

  return report;
}

function fixFile(filePath, dryRun = true) {
  const result = scanFileForKeys(filePath);
  
  if (!result.found) {
    console.log(`✅ No keys found in ${filePath}`);
    return false;
  }

  if (dryRun) {
    console.log(`📋 DRY RUN: Would fix ${result.findings.length} issues in ${filePath}`);
    return true;
  }

  // Create backup
  const backupPath = createBackup(filePath);
  if (!backupPath) {
    console.error(`❌ Failed to create backup for ${filePath}, skipping fix`);
    return false;
  }

  try {
    const cleanedContent = removeKeysFromFile(filePath, result.content);
    fs.writeFileSync(filePath, cleanedContent, 'utf8');
    console.log(`✅ Fixed ${filePath} (backup: ${backupPath})`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to fix ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔒 BookScribe Security Key Removal Tool\n');
  
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--fix');
  const generateReport = args.includes('--report');

  if (generateReport) {
    console.log('📊 Generating security report...\n');
    const report = generateSecurityReport();
    
    // Save report
    const reportPath = `security-scan-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 Report saved to: ${reportPath}`);
    console.log(`\n📊 Summary:`);
    console.log(`- Files scanned: ${report.scannedFiles.length}`);
    console.log(`- Files with keys: ${report.criticalFindings.length}`);
    
    if (report.criticalFindings.length > 0) {
      console.log(`\n❌ CRITICAL FINDINGS:`);
      report.criticalFindings.forEach(finding => {
        console.log(`   ${finding.file}: ${finding.findings.length} keys`);
      });
      
      console.log(`\n🚨 RECOMMENDATIONS:`);
      report.recommendations.forEach(rec => {
        console.log(`   ${rec}`);
      });
      
      process.exit(1); // Exit with error code for CI/CD
    } else {
      console.log('\n✅ No hardcoded keys detected');
    }
    
    return;
  }

  if (dryRun) {
    console.log('🔍 DRY RUN MODE - No files will be modified');
    console.log('Use --fix to actually remove keys\n');
  } else {
    console.log('🛠️  FIX MODE - Files will be modified (backups created)\n');
  }

  let foundIssues = false;

  CRITICAL_FILES.forEach(filePath => {
    if (fixFile(filePath, dryRun)) {
      foundIssues = true;
    }
  });

  if (foundIssues) {
    console.log('\n🚨 CRITICAL SECURITY ISSUES FOUND');
    console.log('Follow the security audit recommendations immediately:');
    console.log('1. Regenerate Supabase service role key');
    console.log('2. Update environment variables');
    console.log('3. Run this script with --fix to clean files');
    console.log('4. Review git history for committed keys');
    
    if (dryRun) {
      console.log('\nRun with --fix to automatically clean the files');
    }
    
    process.exit(1);
  } else {
    console.log('\n✅ No hardcoded keys found in critical files');
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  scanFileForKeys,
  generateSecurityReport,
  fixFile
};