'use client'

import { useState, useEffect } from 'react'
import { useTheme } from '@/hooks/use-theme'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function ThemeTestPage() {
  const { theme, setTheme, themes, mounted } = useTheme()
  const [domClasses, setDomClasses] = useState('')
  const [localStorageValue, setLocalStorageValue] = useState('')
  const [cssVariable, setCssVariable] = useState('')

  useEffect(() => {
    if (!mounted) return

    const updateDebugInfo = () => {
      // Get DOM classes
      setDomClasses(document.documentElement.className)
      
      // Get localStorage value
      setLocalStorageValue(localStorage.getItem('bookscribe-theme') || 'none')
      
      // Get a CSS variable value to verify theme is applied
      const computedStyle = getComputedStyle(document.documentElement)
      setCssVariable(computedStyle.getPropertyValue('--background').trim())
    }

    updateDebugInfo()

    // Update on class changes
    const observer = new MutationObserver(updateDebugInfo)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })

    return () => observer.disconnect()
  }, [mounted])

  if (!mounted) {
    return <div className="container-wide mx-auto p-8">Loading theme system...</div>
  }

  return (
    <div className="container-wide mx-auto p-8 space-y-6">
      <h1 className="text-3xl font-bold">Theme System Test</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Current Theme State</CardTitle>
          <CardDescription>Debug information about the current theme</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <strong>Theme (from hook):</strong> <Badge variant="outline">{theme || 'none'}</Badge>
          </div>
          <div>
            <strong>DOM Classes:</strong> <code className="text-sm bg-muted p-1 rounded">{domClasses || 'none'}</code>
          </div>
          <div>
            <strong>LocalStorage:</strong> <code className="text-sm bg-muted p-1 rounded">{localStorageValue}</code>
          </div>
          <div>
            <strong>CSS Variable (--background):</strong> <code className="text-sm bg-muted p-1 rounded">{cssVariable || 'not found'}</code>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Available Themes</CardTitle>
          <CardDescription>Click to switch between themes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
            {themes.map((themeData) => (
              <Button
                key={themeData.id}
                variant={theme === themeData.id ? 'default' : 'outline'}
                onClick={() => setTheme(themeData.id)}
                className="justify-start"
              >
                <div className="text-left">
                  <div>{themeData.name}</div>
                  <div className="text-xs opacity-70">{themeData.mode} theme</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Theme Colors Preview</CardTitle>
          <CardDescription>Visual representation of current theme colors</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
            <div className="space-y-2">
              <div className="h-16 bg-background border rounded" />
              <div className="text-xs text-center">Background</div>
            </div>
            <div className="space-y-2">
              <div className="h-16 bg-card border rounded" />
              <div className="text-xs text-center">Card</div>
            </div>
            <div className="space-y-2">
              <div className="h-16 bg-primary rounded" />
              <div className="text-xs text-center">Primary</div>
            </div>
            <div className="space-y-2">
              <div className="h-16 bg-secondary rounded" />
              <div className="text-xs text-center">Secondary</div>
            </div>
            <div className="space-y-2">
              <div className="h-16 bg-accent rounded" />
              <div className="text-xs text-center">Accent</div>
            </div>
            <div className="space-y-2">
              <div className="h-16 bg-muted rounded" />
              <div className="text-xs text-center">Muted</div>
            </div>
            <div className="space-y-2">
              <div className="h-16 bg-destructive rounded" />
              <div className="text-xs text-center">Destructive</div>
            </div>
            <div className="space-y-2">
              <div className="h-16 bg-border rounded" />
              <div className="text-xs text-center">Border</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Theme Switching Test</CardTitle>
          <CardDescription>Test rapid theme switching</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button
              onClick={() => {
                // Cycle through all themes rapidly
                let index = 0
                const interval = setInterval(() => {
                  const currentTheme = themes[index]
                  if (currentTheme) {
                    setTheme(currentTheme.id)
                  }
                  index = (index + 1) % themes.length
                  if (index === 0) clearInterval(interval)
                }, 500)
              }}
            >
              Cycle All Themes
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                // Clear localStorage and reload
                localStorage.removeItem('bookscribe-theme')
                window.location.reload()
              }}
            >
              Reset Theme
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}