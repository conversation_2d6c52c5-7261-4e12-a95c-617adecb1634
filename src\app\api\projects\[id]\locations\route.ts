import { NextRequest, NextResponse } from 'next/server';
import { handleAPIError, ValidationError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';

export const runtime = 'nodejs';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: projectId } = params;
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    const { data: locations, error } = await supabase
      .from('locations')
      .select(`
        *,
        parent_location:locations!parent_location_id(
          id,
          name
        ),
        series:series(
          id,
          title
        ),
        universe:universes(
          id,
          name
        )
      `)
      .eq('project_id', projectId)
      .order('name', { ascending: true });

    if (error) {
      logger.error('Error fetching locations:', error);
      return UnifiedResponse.error({
        message: 'Failed to fetch locations',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500);
    }

    return UnifiedResponse.success({ locations });
  } catch (error) {
    logger.error('Error in GET /api/projects/[id]/locations:', error);
    return UnifiedResponse.error({
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      details: error
    }, undefined, 500);
  }
}

export async function POST(request: NextRequest, props: RouteParams) {
  try {
    const params = await props.params;
    const { id: projectId } = params;
    const supabase = await createTypedServerClient();
    
    const { data: user, error: authError } = await supabase.auth.getUser();
    if (authError || !user?.user) {
      return handleAPIError(new AuthenticationError());
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.user.id)
      .single();

    if (projectError || !project) {
      return handleAPIError(new NotFoundError('Resource'));
    }

    const body = await request.json();
    const {
      name,
      description,
      parentLocationId,
      locationType = 'other',
      features = [],
      significance,
      isShareable = false,
      seriesId,
      universeId
    } = body;

    if (!name?.trim()) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    const { data: location, error } = await supabase
      .from('locations')
      .insert({
        project_id: projectId,
        series_id: seriesId,
        universe_id: universeId,
        name: name.trim(),
        description: description?.trim(),
        parent_location_id: parentLocationId,
        location_type: locationType,
        features,
        significance: significance?.trim(),
        is_shareable: isShareable
      })
      .select(`
        *,
        parent_location:locations!parent_location_id(
          id,
          name
        )
      `)
      .single();

    if (error) {
      logger.error('Error creating location:', error);
      return UnifiedResponse.error({
        message: 'Failed to create location',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500);
    }

    return UnifiedResponse.created({ location });
  } catch (error) {
    logger.error('Error in POST /api/projects/[id]/locations:', error);
    return UnifiedResponse.error({
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      details: error
    }, undefined, 500);
  }
}