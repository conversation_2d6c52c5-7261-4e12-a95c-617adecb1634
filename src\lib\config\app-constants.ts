import { TIME_MS } from '@/lib/constants'
import { TIME_SECONDS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

/**
 * Application Constants and Configuration
 * Centralized configuration for all non-AI related settings
 */

// Writing and Content Limits
export const CONTENT_LIMITS = {
  // Chapter limits
  MIN_CHAPTER_WORDS: 2000,
  MAX_CHAPTER_WORDS: 10000,
  IDEAL_CHAPTER_WORDS: 4000,
  
  // Book limits
  MIN_BOOK_WORDS: SIZE_LIMITS.MAX_DOCUMENT_CHARS,
  MAX_BOOK_WORDS: 300000,
  
  // Title and description limits
  MAX_TITLE_LENGTH: 200,
  MAX_DESCRIPTION_LENGTH: TIME_MS.SECOND,
  MAX_SYNOPSIS_LENGTH: TIME_MS.TOAST_DURATION,
  
  // Character limits
  MAX_CHARACTER_NAME_LENGTH: 100,
  MAX_CHARACTER_BIO_LENGTH: TIME_MS.TOAST_DURATION,
  MAX_CHARACTERS_PER_PROJECT: 100,
  
  // Series limits
  MAX_SERIES_BOOKS: 20,
  MAX_SERIES_TITLE_LENGTH: 200,
  
  // Voice profile limits
  MAX_VOICE_EXAMPLES: 10,
  MAX_VOICE_TRAIT_LENGTH: 200,
  MAX_VOICE_MANNERISMS: 20,
} as const;

// Session and Activity Tracking
export const SESSION_CONFIG = {
  // Writing session settings
  MIN_SESSION_DURATION_MINUTES: 5,
  MAX_SESSION_DURATION_HOURS: 8,
  SESSION_IDLE_TIMEOUT_MINUTES: 30,
  AUTO_SAVE_INTERVAL_SECONDS: 60,
  
  // Activity tracking
  ACTIVITY_PING_INTERVAL_SECONDS: 30,
  ACTIVITY_HISTORY_DAYS: 90,
  
  // Streak calculations
  STREAK_MINIMUM_WORDS: 100,
  STREAK_GRACE_HOURS: 36, // Allow 36 hours between sessions to maintain streak
} as const;

// Analytics and Metrics
export const ANALYTICS_CONFIG = {
  // Quality score thresholds
  QUALITY_EXCELLENT: 90,
  QUALITY_GOOD: 80,
  QUALITY_FAIR: 70,
  QUALITY_POOR: 60,
  
  // Productivity metrics
  HIGH_PRODUCTIVITY_WPH: 500, // Words per hour
  AVERAGE_PRODUCTIVITY_WPH: 250,
  LOW_PRODUCTIVITY_WPH: 100,
  
  // Data retention
  ANALYTICS_RETENTION_DAYS: 365,
  DETAILED_METRICS_DAYS: 90,
  
  // Sampling rates
  QUALITY_CHECK_INTERVAL_WORDS: TIME_MS.SECOND,
  VOICE_CHECK_INTERVAL_WORDS: 500,
} as const;

// UI/UX Constants
export const UI_CONFIG = {
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // List limits
  RECENT_PROJECTS_LIMIT: 6,
  DASHBOARD_ACTIVITY_LIMIT: 10,
  SEARCH_RESULTS_LIMIT: 50,
  
  // Notifications
  TOAST_DURATION_MS: TIME_MS.TOAST_DURATION,
  SUCCESS_REDIRECT_DELAY_MS: 2000,
  
  // Debounce/throttle times
  SEARCH_DEBOUNCE_MS: 300,
  RESIZE_THROTTLE_MS: 100,
  SCROLL_THROTTLE_MS: 50,
  
  // Animation durations
  MODAL_TRANSITION_MS: 200,
  PANEL_SLIDE_MS: 300,
  FADE_DURATION_MS: 150,
} as const;

// File Upload and Storage
export const STORAGE_CONFIG = {
  // File size limits
  MAX_FILE_SIZE_MB: 50,
  MAX_IMAGE_SIZE_MB: 10,
  MAX_EXPORT_SIZE_MB: 100,
  
  // File type restrictions
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXPORT_FORMATS: ['epub', 'pdf', 'docx', 'txt', 'md'],
  
  // Storage quotas (per user)
  FREE_STORAGE_GB: 1,
  PRO_STORAGE_GB: 10,
  ENTERPRISE_STORAGE_GB: 100,
} as const;

// Collaboration Settings
export const COLLABORATION_CONFIG = {
  // Real-time sync
  SYNC_INTERVAL_MS: TIME_MS.SECOND,
  CONFLICT_RESOLUTION_TIMEOUT_MS: TIME_MS.TOAST_DURATION,
  MAX_COLLABORATORS: 10,
  
  // Permissions
  PERMISSION_LEVELS: ['viewer', 'commenter', 'editor', 'admin'] as const,
  INVITE_EXPIRY_DAYS: 7,
  
  // Presence
  PRESENCE_UPDATE_INTERVAL_MS: TIME_MS.TOAST_DURATION,
  PRESENCE_TIMEOUT_MS: 30000,
} as const;

// Export Settings
export const EXPORT_CONFIG = {
  // Processing limits
  MAX_EXPORT_CHAPTERS: 100,
  EXPORT_TIMEOUT_MINUTES: 30,
  EXPORT_QUEUE_SIZE: 5,
  
  // Format-specific settings
  EPUB: {
    MAX_IMAGE_WIDTH: 800,
    MAX_IMAGE_HEIGHT: 1200,
    COMPRESSION_QUALITY: 0.8,
  },
  PDF: {
    PAGE_WIDTH: 612, // Letter size in points
    PAGE_HEIGHT: 792,
    MARGIN: 72, // 1 inch
    FONT_SIZE: 12,
  },
} as const;

// Cache Configuration
export const CACHE_CONFIG = {
  // TTL values (in seconds)
  USER_DATA_TTL: TIME_SECONDS.HOUR, // 1 hour
  PROJECT_DATA_TTL: 1800, // 30 minutes
  ANALYTICS_TTL: 300, // 5 minutes
  STATIC_CONTENT_TTL: 86400, // 24 hours
  
  // Cache size limits
  MAX_CACHE_SIZE_MB: 100,
  MAX_CACHE_ENTRIES: TIME_MS.SECOND,
} as const;

// API Rate Limiting (per user)
export const API_RATE_LIMITS = {
  // Requests per minute
  AUTHENTICATED_RPM: 60,
  UNAUTHENTICATED_RPM: 20,
  
  // Specific endpoint limits
  ENDPOINTS: {
    '/api/generate': 10,
    '/api/export': 5,
    '/api/analytics': 30,
    '/api/sync': 120,
  },
  
  // Burst allowance
  BURST_MULTIPLIER: 2,
  RATE_LIMIT_WINDOW_MS: 60000, // 1 minute
} as const;

// Performance Thresholds
export const PERFORMANCE_CONFIG = {
  // Response time targets (in ms)
  TARGET_API_RESPONSE_MS: 200,
  TARGET_PAGE_LOAD_MS: TIME_MS.SECOND,
  TARGET_INTERACTION_MS: 100,
  
  // Performance budgets
  MAX_BUNDLE_SIZE_KB: 500,
  MAX_INITIAL_LOAD_KB: 200,
  
  // Monitoring
  PERFORMANCE_SAMPLE_RATE: 0.1, // 10% of requests
  SLOW_REQUEST_THRESHOLD_MS: TIME_MS.TYPING_TIMEOUT,
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  // Username/email
  MIN_USERNAME_LENGTH: 3,
  MAX_USERNAME_LENGTH: 30,
  USERNAME_PATTERN: /^[a-zA-Z0-9_-]+$/,
  
  // Password
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 128,
  PASSWORD_REQUIRE_UPPERCASE: true,
  PASSWORD_REQUIRE_LOWERCASE: true,
  PASSWORD_REQUIRE_NUMBER: true,
  PASSWORD_REQUIRE_SPECIAL: true,
  
  // Project names
  MIN_PROJECT_NAME_LENGTH: 1,
  MAX_PROJECT_NAME_LENGTH: 100,
  PROJECT_NAME_PATTERN: /^[^<>:"/\\|?*]+$/,
} as const;

// Feature Flags and Limits by Subscription
export const SUBSCRIPTION_LIMITS = {
  FREE: {
    MAX_PROJECTS: 3,
    MAX_CHAPTERS_PER_PROJECT: 10,
    MAX_AI_REQUESTS_PER_MONTH: 100,
    MAX_EXPORTS_PER_MONTH: 5,
    FEATURES: {
      BASIC_AI: true,
      COLLABORATION: false,
      ADVANCED_ANALYTICS: false,
      CUSTOM_THEMES: false,
      PRIORITY_SUPPORT: false,
    },
  },
  PRO: {
    MAX_PROJECTS: 50,
    MAX_CHAPTERS_PER_PROJECT: 100,
    MAX_AI_REQUESTS_PER_MONTH: TIME_MS.SECOND,
    MAX_EXPORTS_PER_MONTH: 50,
    FEATURES: {
      BASIC_AI: true,
      COLLABORATION: true,
      ADVANCED_ANALYTICS: true,
      CUSTOM_THEMES: true,
      PRIORITY_SUPPORT: false,
    },
  },
  ENTERPRISE: {
    MAX_PROJECTS: -1, // Unlimited
    MAX_CHAPTERS_PER_PROJECT: -1, // Unlimited
    MAX_AI_REQUESTS_PER_MONTH: -1, // Unlimited
    MAX_EXPORTS_PER_MONTH: -1, // Unlimited
    FEATURES: {
      BASIC_AI: true,
      COLLABORATION: true,
      ADVANCED_ANALYTICS: true,
      CUSTOM_THEMES: true,
      PRIORITY_SUPPORT: true,
    },
  },
} as const;

// Helper functions
export function getSubscriptionLimit(
  tier: keyof typeof SUBSCRIPTION_LIMITS,
  limit: keyof typeof SUBSCRIPTION_LIMITS.FREE
): number | boolean | Record<string, boolean> {
  return SUBSCRIPTION_LIMITS[tier][limit];
}

export function isFeatureEnabled(
  tier: keyof typeof SUBSCRIPTION_LIMITS,
  feature: keyof typeof SUBSCRIPTION_LIMITS.FREE.FEATURES
): boolean {
  return SUBSCRIPTION_LIMITS[tier].FEATURES[feature];
}

export function getQualityLabel(score: number): string {
  if (score >= ANALYTICS_CONFIG.QUALITY_EXCELLENT) return 'Excellent';
  if (score >= ANALYTICS_CONFIG.QUALITY_GOOD) return 'Good';
  if (score >= ANALYTICS_CONFIG.QUALITY_FAIR) return 'Fair';
  return 'Needs Improvement';
}

export function getProductivityLevel(wordsPerHour: number): string {
  if (wordsPerHour >= ANALYTICS_CONFIG.HIGH_PRODUCTIVITY_WPH) return 'High';
  if (wordsPerHour >= ANALYTICS_CONFIG.AVERAGE_PRODUCTIVITY_WPH) return 'Average';
  return 'Low';
}