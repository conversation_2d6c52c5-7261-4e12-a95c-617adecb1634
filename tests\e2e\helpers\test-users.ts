import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface TestUser {
  id: string;
  email: string;
  password: string;
  name: string;
}

export async function createTestUser(userData: {
  email: string;
  password: string;
  name: string;
}): Promise<TestUser> {
  // Create auth user
  const { data: authData, error: authError } = await supabase.auth.admin.createUser({
    email: userData.email,
    password: userData.password,
    email_confirm: true,
    user_metadata: {
      name: userData.name
    }
  });

  if (authError) {
    throw new Error(`Failed to create test user: ${authError.message}`);
  }

  // Create profile
  const { error: profileError } = await supabase
    .from('profiles')
    .insert({
      id: authData.user.id,
      email: userData.email,
      name: userData.name,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

  if (profileError) {
    // Cleanup auth user if profile creation fails
    await supabase.auth.admin.deleteUser(authData.user.id);
    throw new Error(`Failed to create user profile: ${profileError.message}`);
  }

  return {
    id: authData.user.id,
    email: userData.email,
    password: userData.password,
    name: userData.name
  };
}

export async function deleteTestUser(userId: string): Promise<void> {
  try {
    // Delete associated data first
    await supabase.from('project_members').delete().eq('user_id', userId);
    await supabase.from('projects').delete().eq('owner_id', userId);
    await supabase.from('profiles').delete().eq('id', userId);
    
    // Delete auth user
    const { error } = await supabase.auth.admin.deleteUser(userId);
    if (error) {
      console.error(`Failed to delete test user ${userId}:`, error);
    }
  } catch (error) {
    console.error(`Error cleaning up test user ${userId}:`, error);
  }
}

export async function loginTestUser(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (error) {
    throw new Error(`Failed to login test user: ${error.message}`);
  }

  return data;
}

export async function createTestUsers(count: number): Promise<TestUser[]> {
  const users: TestUser[] = [];
  
  for (let i = 0; i < count; i++) {
    const user = await createTestUser({
      email: `test-user-${uuidv4()}@test.com`,
      password: 'TestPassword123!',
      name: `Test User ${i + 1}`
    });
    users.push(user);
  }

  return users;
}

export async function deleteTestUsers(users: TestUser[]): Promise<void> {
  await Promise.all(users.map(user => deleteTestUser(user.id)));
}