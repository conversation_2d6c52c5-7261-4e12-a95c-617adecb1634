import { BaseEmailProvider } from './base-provider'
import { EmailRequest, EmailResponse, EmailStatus } from '../types'
import { logger } from '../../services/logger'
import { config as appConfig } from '../../config/unified-env'

interface MailerooConfig {
  apiKey: string
  defaultFrom: string
  defaultReplyTo?: string
}

interface MailerooEmailData {
  from: string
  to: string[]
  subject: string
  html: string
  text?: string
  reply_to?: string
  cc?: string[]
  bcc?: string[]
  attachments?: Array<{
    filename: string
    content: string
    type: string
  }>
  headers?: Record<string, string>
}

interface MailerooResponse {
  success: boolean
  message_id?: string
  error?: string
}

/**
 * Maileroo Email Provider
 * Documentation: https://maileroo.com/api-documentation/email-sending-api
 */
export class MailerooProvider extends BaseEmailProvider {
  private apiKey: string
  private defaultFrom: string
  private defaultReplyTo?: string
  private endpoint: string

  constructor(config: MailerooConfig) {
    super('maileroo', config)
    this.apiKey = config.apiKey
    this.defaultFrom = config.defaultFrom
    this.defaultReplyTo = config.defaultReplyTo
    this.endpoint = appConfig.email?.endpoint || ''
  }

  async send(request: EmailRequest): Promise<EmailResponse> {
    try {
      const emailData = this.buildEmailData(request)
      
      const response = await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify(emailData)
      })

      const data = await response.json() as MailerooResponse

      if (response.ok && data.success) {
        this.logEmail('sent', request, { 
          success: true, 
          messageId: data.message_id,
          provider: this.name 
        })
        
        return {
          success: true,
          messageId: data.message_id,
          provider: this.name,
          timestamp: new Date()
        }
      } else {
        const errorMessage = data.error || `HTTP ${response.status} error`
        throw new Error(errorMessage)
      }
    } catch (error) {
      this.logEmail('failed', request, undefined, error as Error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send email',
        provider: this.name
      }
    }
  }

  async getStatus(messageId: string): Promise<EmailStatus> {
    // Maileroo doesn't provide a direct status API
    // Would need to implement webhook handling for status updates
    logger.warn('Maileroo does not support direct status queries', { messageId })
    return 'pending'
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test the API key by sending a test request
      // Maileroo doesn't have a dedicated validation endpoint
      // We'll do a basic check that the key is present
      if (!this.apiKey || this.apiKey.length < 10) {
        logger.error('Invalid Maileroo API key')
        return false
      }

      logger.info('Maileroo configuration appears valid')
      return true
    } catch (error) {
      logger.error('Maileroo configuration validation error', error as Error)
      return false
    }
  }

  private buildEmailData(request: EmailRequest): MailerooEmailData {
    const recipients = this.normalizeEmails(request.to)
    
    const emailData: MailerooEmailData = {
      from: request.from || this.defaultFrom,
      to: recipients,
      subject: request.subject,
      html: request.html,
      text: request.text || this.stripHtml(request.html)
    }

    // Add optional fields
    const replyTo = request.replyTo || this.defaultReplyTo
    if (replyTo) {
      emailData.reply_to = replyTo
    }

    if (request.cc?.length) {
      emailData.cc = request.cc
    }

    if (request.bcc?.length) {
      emailData.bcc = request.bcc
    }

    if (request.headers) {
      emailData.headers = request.headers
    }

    // Convert attachments
    if (request.attachments?.length) {
      emailData.attachments = request.attachments.map(att => ({
        filename: att.filename,
        content: typeof att.content === 'string' ? att.content : att.content.toString('base64'),
        type: att.type || 'application/octet-stream'
      }))
    }

    return emailData
  }
}