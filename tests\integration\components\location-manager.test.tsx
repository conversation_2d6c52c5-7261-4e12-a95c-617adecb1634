import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LocationManager } from '@/components/locations/location-manager'
import { createMockSupabaseClient } from '../setup'

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}))

// Mock the logger
jest.mock('@/lib/services/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}))

describe('LocationManager Component Integration', () => {
  const mockProjectId = 'test-project-123'
  let mockFetch: jest.SpyInstance

  beforeEach(() => {
    mockFetch = jest.spyOn(global, 'fetch')
  })

  afterEach(() => {
    mockFetch.mockRestore()
  })

  describe('Location Loading and Display', () => {
    it('should load and display locations on mount', async () => {
      const mockLocations = [
        {
          id: 'loc-1',
          name: 'The Shire',
          description: 'A peaceful place',
          location_type: 'region',
          features: ['Green hills', 'Hobbit holes'],
          is_shareable: true,
          parent_location: null,
        },
        {
          id: 'loc-2',
          name: 'Bag End',
          description: 'Bilbo\'s home',
          location_type: 'building',
          features: ['Round door'],
          is_shareable: false,
          parent_location: { id: 'loc-1', name: 'The Shire' },
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: mockLocations },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      // Should show loading state initially
      expect(screen.getByText(/loading/i)).toBeInTheDocument()

      // Wait for locations to load
      await waitFor(() => {
        expect(screen.getByText('The Shire')).toBeInTheDocument()
        expect(screen.getByText('Bag End')).toBeInTheDocument()
      })

      // Verify API was called correctly
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining(`/api/projects/${mockProjectId}/locations`),
        expect.objectContaining({
          method: 'GET',
        })
      )

      // Check location count display
      expect(screen.getByText(/2 total/)).toBeInTheDocument()
    })

    it('should handle empty location list', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: [] },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText(/No locations created yet/i)).toBeInTheDocument()
        expect(screen.getByText(/Create your first location/i)).toBeInTheDocument()
      })
    })

    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: 'Failed to fetch locations',
            userMessage: 'Unable to load locations. Please try again.',
          },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText(/Unable to load locations/i)).toBeInTheDocument()
      })
    })
  })

  describe('Location Creation', () => {
    it('should create a new location successfully', async () => {
      // Mock initial load
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: [] },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText(/No locations created yet/i)).toBeInTheDocument()
      })

      // Click add location button
      const addButton = screen.getByRole('button', { name: /add location/i })
      await userEvent.click(addButton)

      // Dialog should open
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument()
      })

      // Fill in the form
      const nameInput = screen.getByLabelText(/name/i)
      const descriptionInput = screen.getByLabelText(/description/i)
      await userEvent.type(nameInput, 'Rivendell')
      await userEvent.type(descriptionInput, 'Elven realm')

      // Mock the create API call
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            location: {
              id: 'new-loc-123',
              name: 'Rivendell',
              description: 'Elven realm',
              location_type: 'city',
              features: [],
              is_shareable: false,
            },
          },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      // Mock the refresh call
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            locations: [{
              id: 'new-loc-123',
              name: 'Rivendell',
              description: 'Elven realm',
              location_type: 'city',
              features: [],
              is_shareable: false,
              parent_location: null,
            }],
          },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      // Submit the form
      const submitButton = screen.getByRole('button', { name: /create/i })
      await userEvent.click(submitButton)

      // Wait for the new location to appear
      await waitFor(() => {
        expect(screen.getByText('Rivendell')).toBeInTheDocument()
      })

      // Verify the API calls
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining(`/api/projects/${mockProjectId}/locations`),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('Rivendell'),
        })
      )
    })

    it('should validate required fields', async () => {
      // Mock initial load
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: [] },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText(/No locations created yet/i)).toBeInTheDocument()
      })

      // Open dialog
      const addButton = screen.getByRole('button', { name: /add location/i })
      await userEvent.click(addButton)

      // Try to submit without filling required fields
      const submitButton = screen.getByRole('button', { name: /create/i })
      await userEvent.click(submitButton)

      // Should show validation error
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument()
      })
    })
  })

  describe('View Mode Switching', () => {
    it('should switch between tree, list, and map views', async () => {
      const mockLocations = [
        {
          id: 'loc-1',
          name: 'World',
          location_type: 'world',
          features: [],
          is_shareable: false,
          parent_location: null,
          map_position: { x: 400, y: 300 },
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: mockLocations },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText('World')).toBeInTheDocument()
      })

      // Default should be tree view
      expect(screen.getByText(/Hierarchical view/i)).toBeInTheDocument()

      // Switch to list view
      const listButton = screen.getByRole('button', { name: /list/i })
      await userEvent.click(listButton)
      expect(screen.getByText(/List view/i)).toBeInTheDocument()

      // Switch to map view
      const mapButton = screen.getByRole('button', { name: /map/i })
      await userEvent.click(mapButton)
      
      // Map view should render with loading state first
      await waitFor(() => {
        expect(screen.getByText(/Initializing map.../i)).toBeInTheDocument()
      })
      
      // Then show the actual map
      await waitFor(() => {
        expect(screen.getByText(/Location Map/i)).toBeInTheDocument()
      })
    })
  })

  describe('Search and Filter', () => {
    it('should filter locations by search query', async () => {
      const mockLocations = [
        {
          id: 'loc-1',
          name: 'The Shire',
          location_type: 'region',
          features: [],
          is_shareable: false,
          parent_location: null,
        },
        {
          id: 'loc-2',
          name: 'Rivendell',
          location_type: 'city',
          features: [],
          is_shareable: false,
          parent_location: null,
        },
        {
          id: 'loc-3',
          name: 'Shire Gardens',
          location_type: 'building',
          features: [],
          is_shareable: false,
          parent_location: null,
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: mockLocations },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText('The Shire')).toBeInTheDocument()
        expect(screen.getByText('Rivendell')).toBeInTheDocument()
        expect(screen.getByText('Shire Gardens')).toBeInTheDocument()
      })

      // Search for "shire"
      const searchInput = screen.getByPlaceholderText(/search locations/i)
      await userEvent.type(searchInput, 'shire')

      // Should filter to show only Shire-related locations
      await waitFor(() => {
        expect(screen.getByText('The Shire')).toBeInTheDocument()
        expect(screen.getByText('Shire Gardens')).toBeInTheDocument()
        expect(screen.queryByText('Rivendell')).not.toBeInTheDocument()
      })
    })

    it('should filter locations by type', async () => {
      const mockLocations = [
        {
          id: 'loc-1',
          name: 'Middle Earth',
          location_type: 'world',
          features: [],
          is_shareable: false,
          parent_location: null,
        },
        {
          id: 'loc-2',
          name: 'Gondor',
          location_type: 'country',
          features: [],
          is_shareable: false,
          parent_location: null,
        },
        {
          id: 'loc-3',
          name: 'Minas Tirith',
          location_type: 'city',
          features: [],
          is_shareable: false,
          parent_location: null,
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: mockLocations },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText('Middle Earth')).toBeInTheDocument()
        expect(screen.getByText('Gondor')).toBeInTheDocument()
        expect(screen.getByText('Minas Tirith')).toBeInTheDocument()
      })

      // Filter by city type
      const typeSelect = screen.getByRole('combobox')
      await userEvent.selectOptions(typeSelect, 'city')

      // Should show only cities
      await waitFor(() => {
        expect(screen.queryByText('Middle Earth')).not.toBeInTheDocument()
        expect(screen.queryByText('Gondor')).not.toBeInTheDocument()
        expect(screen.getByText('Minas Tirith')).toBeInTheDocument()
      })
    })
  })

  describe('Error Boundary Integration', () => {
    it('should display error boundary when component crashes', async () => {
      // Force an error by providing invalid data
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: null }, // This will cause an error
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      // Mock console.error to avoid noise in tests
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText(/component error/i)).toBeInTheDocument()
        expect(screen.getByText(/auto-recover shortly/i)).toBeInTheDocument()
      })

      consoleSpy.mockRestore()
    })
  })

  describe('LocationMapView Loading States', () => {
    it('should show loading state when data is being fetched', async () => {
      // Mock slow API response
      mockFetch.mockImplementation(() => new Promise(resolve => {
        setTimeout(() => {
          resolve({
            ok: true,
            json: async () => ({
              success: true,
              data: { locations: [] },
              meta: { timestamp: new Date().toISOString(), version: '1.0' },
            }),
          })
        }, 1000)
      }))

      render(<LocationManager projectId={mockProjectId} />)

      // Should show loading state
      expect(screen.getByText(/loading/i)).toBeInTheDocument()

      // Switch to map view while loading
      const mapButton = screen.getByRole('button', { name: /map/i })
      await userEvent.click(mapButton)

      // Map should show loading state
      expect(screen.getByText(/Loading locations.../i)).toBeInTheDocument()

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText(/No Locations to Map/i)).toBeInTheDocument()
      }, { timeout: 2000 })
    })

    it('should show position saving indicator when dragging locations', async () => {
      const mockLocations = [{
        id: 'loc-1',
        name: 'Test Location',
        location_type: 'city',
        features: [],
        is_shareable: false,
        parent_location: null,
      }]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: mockLocations },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText('Test Location')).toBeInTheDocument()
      })

      // Switch to map view
      const mapButton = screen.getByRole('button', { name: /map/i })
      await userEvent.click(mapButton)

      // Wait for map to load
      await waitFor(() => {
        expect(screen.getByText(/Location Map/i)).toBeInTheDocument()
      })

      // The position save indicator would appear during drag operations
      // This is hard to test without actual drag simulation
    })

    it('should show action overlay when performing operations', async () => {
      const mockLocations = [{
        id: 'loc-1',
        name: 'Test Location',
        location_type: 'city',
        features: [],
        is_shareable: false,
        parent_location: null,
      }]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { locations: mockLocations },
          meta: { timestamp: new Date().toISOString(), version: '1.0' },
        }),
      })

      render(<LocationManager projectId={mockProjectId} />)

      await waitFor(() => {
        expect(screen.getByText('Test Location')).toBeInTheDocument()
      })

      // Switch to map view
      const mapButton = screen.getByRole('button', { name: /map/i })
      await userEvent.click(mapButton)

      // Action overlay would appear during delete/edit operations
      // Testing the concept
      expect(screen.queryByText(/Processing.../i)).not.toBeInTheDocument()
    })
  })
})