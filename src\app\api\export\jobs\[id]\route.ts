import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'

// GET - Get job status
export const GET = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: jobId } = await params
    const user = request.user!
    const supabase = await createTypedServerClient()

    const { data: job, error } = await supabase
      .from('export_jobs')
      .select('*')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single()

    if (error || !job) {
      return UnifiedResponse.error('Export job not found', 404)
    }

    return UnifiedResponse.success({
      id: job.id,
      projectId: job.project_id,
      projectTitle: job.project_title,
      format: job.format,
      status: job.status,
      progress: job.progress,
      currentStep: job.current_step,
      fileUrl: job.file_url,
      fileSize: job.file_size,
      error: job.error,
      createdAt: job.created_at,
      updatedAt: job.updated_at,
      estimatedTime: job.estimated_time,
      processingTime: job.processing_time
    })
  } catch (error) {
    logger.error('Get export job error:', error)
    return UnifiedResponse.error('Failed to retrieve export job')
  }
})

// DELETE - Delete job and associated file
export const DELETE = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: jobId } = await params
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Get job to check ownership and file URL
    const { data: job, error: fetchError } = await supabase
      .from('export_jobs')
      .select('file_url')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !job) {
      return UnifiedResponse.error('Export job not found', 404)
    }

    // Delete associated file if exists
    if (job.file_url) {
      try {
        const url = new URL(job.file_url)
        const filePath = url.pathname.split('/storage/v1/object/public/project-files/')[1]
        if (filePath) {
          await supabase.storage
            .from('project-files')
            .remove([filePath])
        }
      } catch (storageError) {
        logger.warn('Failed to delete export file:', storageError)
      }
    }

    // Delete job record
    const { error: deleteError } = await supabase
      .from('export_jobs')
      .delete()
      .eq('id', jobId)

    if (deleteError) {
      logger.error('Failed to delete export job:', deleteError)
      return UnifiedResponse.error('Failed to delete export job')
    }

    return UnifiedResponse.success({ success: true })
  } catch (error) {
    logger.error('Delete export job error:', error)
    return UnifiedResponse.error('Failed to delete export job')
  }
})