/**
 * Story structure and content type definitions
 */

// JSONB Type Definitions for structured data
export interface StoryStructure {
  acts?: Array<{
    number: number
    description: string
    chapters: number[]
    key_events: string[]
  }>
  plot_points?: Array<{
    type: 'inciting_incident' | 'plot_point_1' | 'midpoint' | 'plot_point_2' | 'climax' | 'resolution'
    chapter: number
    description: string
  }>
  world_building?: {
    setting: string
    rules: string[]
    locations: Array<{ name: string; description: string }>
  }
  timeline?: Array<{
    date: string
    event: string
    chapter?: number
  }>
}

export interface CharacterData {
  [character_id: string]: {
    name: string
    role: string
    description: string
    backstory: string
    personality_traits: string[]
    relationships: Array<{ character_id: string; relationship: string; description: string }>
    voice_data?: {
      speaking_style: string
      vocabulary: string[]
      mannerisms: string[]
    }
  }
}

export interface ChapterScenes {
  scenes: Array<{
    number: number
    setting: string
    characters: string[]
    objectives: string[]
    conflicts: string[]
    resolutions: string[]
  }>
}

export interface CharacterStates {
  [character_id: string]: {
    emotional_state: string
    goals: string[]
    knowledge: string[]
    relationships: { [other_character_id: string]: string }
  }
}

export interface PlotAdvancement {
  main_plot: {
    threads: string[]
    advancement: string
    conflicts_introduced: string[]
    conflicts_resolved: string[]
  }
  subplots: Array<{
    name: string
    advancement: string
    status: 'active' | 'resolved' | 'paused'
  }>
}

export interface AiAnalysis {
  objectives?: string[]
  conflicts?: string[]
  resolutions?: string[]
  notes?: string[]
  quality_score?: number
  suggestions?: string[]
}

export interface VoiceData {
  speaking_style: string
  vocabulary: string[]
  mannerisms: string[]
}

export interface PersonalityTraits {
  traits: string[]
  strengths: string[]
  flaws: string[]
  motivations: string[]
}

export interface CharacterArc {
  starting_point: string
  transformation: string
  ending_point: string
  key_moments: Array<{
    chapter: number
    description: string
  }>
}

export interface Relationships {
  [character_id: string]: {
    relationship_type: string
    description: string
    history: string
    current_status: string
  }
}

export interface QualityScore {
  overall: number
  structure: number
  character_development: number
  pacing: number
  dialogue: number
  description: number
  notes: string[]
}