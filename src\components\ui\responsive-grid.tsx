import { cn } from '@/lib/utils'
import { HTMLAttributes, forwardRef } from 'react'

interface ResponsiveGridProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * Number of columns at different breakpoints
   * Defaults to responsive auto-fit grid
   */
  cols?: {
    base?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
    '3xl'?: number
  }
  /**
   * Gap size between grid items
   */
  gap?: 'sm' | 'md' | 'lg' | 'xl' | 'responsive'
  /**
   * Minimum column width for auto-fit grids
   */
  minColWidth?: string
}

export const ResponsiveGrid = forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ className, cols, gap = 'responsive', minColWidth = '300px', children, ...props }, ref) => {
    // Build responsive column classes
    const colClasses = cols ? [
      cols.base && `grid-cols-${cols.base}`,
      cols.sm && `sm:grid-cols-${cols.sm}`,
      cols.md && `md:grid-cols-${cols.md}`,
      cols.lg && `lg:grid-cols-${cols.lg}`,
      cols.xl && `xl:grid-cols-${cols.xl}`,
      cols['2xl'] && `2xl:grid-cols-${cols['2xl']}`,
      cols['3xl'] && `3xl:grid-cols-${cols['3xl']}`,
    ].filter(Boolean).join(' ') : null
    
    const gapClasses = {
      sm: 'gap-3 lg:gap-4',
      md: 'gap-4 lg:gap-6',
      lg: 'gap-6 lg:gap-8',
      xl: 'gap-8 lg:gap-10',
      responsive: 'gap-4 sm:gap-5 lg:gap-6 xl:gap-8 2xl:gap-10'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          'grid',
          gapClasses[gap],
          colClasses || 'grid-responsive',
          className
        )}
        style={!cols ? {
          gridTemplateColumns: `repeat(auto-fit, minmax(min(100%, ${minColWidth}), 1fr))`
        } : undefined}
        {...props}
      >
        {children}
      </div>
    )
  }
)

ResponsiveGrid.displayName = 'ResponsiveGrid'

// Preset grid layouts
export const StatsGrid = forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ className, ...props }, ref) => {
    return (
      <ResponsiveGrid
        ref={ref}
        cols={{ base: 1, sm: 2, lg: 3, xl: 4, '2xl': 5 }}
        gap="responsive"
        className={className}
        {...props}
      />
    )
  }
)

StatsGrid.displayName = 'StatsGrid'

export const ProjectGrid = forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ className, ...props }, ref) => {
    return (
      <ResponsiveGrid
        ref={ref}
        minColWidth="350px"
        gap="responsive"
        className={className}
        {...props}
      />
    )
  }
)

ProjectGrid.displayName = 'ProjectGrid'

export const FeatureGrid = forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ className, ...props }, ref) => {
    return (
      <ResponsiveGrid
        ref={ref}
        cols={{ base: 1, md: 2, xl: 3 }}
        gap="lg"
        className={className}
        {...props}
      />
    )
  }
)

FeatureGrid.displayName = 'FeatureGrid'

// Masonry grid for variable height items
export const MasonryGrid = forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ className, gap = 'responsive', children, ...props }, ref) => {
    const gapClasses = {
      sm: 'gap-3 lg:gap-4',
      md: 'gap-4 lg:gap-6',
      lg: 'gap-6 lg:gap-8',
      xl: 'gap-8 lg:gap-10',
      responsive: 'gap-4 sm:gap-5 lg:gap-6 xl:gap-8 2xl:gap-10'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          'columns-1 sm:columns-2 lg:columns-3 xl:columns-4 2xl:columns-5',
          gapClasses[gap],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

MasonryGrid.displayName = 'MasonryGrid'