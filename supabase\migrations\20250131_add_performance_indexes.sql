-- Add performance indexes for new features

-- Voice Profiles indexes
CREATE INDEX IF NOT EXISTS idx_voice_profiles_user_id ON voice_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_profiles_project_id ON voice_profiles(project_id);
CREATE INDEX IF NOT EXISTS idx_voice_profiles_series_id ON voice_profiles(series_id);
CREATE INDEX IF NOT EXISTS idx_voice_profiles_character_id ON voice_profiles(character_id);
CREATE INDEX IF NOT EXISTS idx_voice_profiles_is_global ON voice_profiles(is_global);
CREATE INDEX IF NOT EXISTS idx_voice_profiles_type ON voice_profiles(type);
CREATE INDEX IF NOT EXISTS idx_voice_profiles_composite_user_project ON voice_profiles(user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_voice_profiles_composite_user_global ON voice_profiles(user_id, is_global);

-- Voice Consistency Checks indexes
CREATE INDEX IF NOT EXISTS idx_voice_consistency_checks_project_id ON voice_consistency_checks(project_id);
CREATE INDEX IF NOT EXISTS idx_voice_consistency_checks_voice_profile_id ON voice_consistency_checks(voice_profile_id);
CREATE INDEX IF NOT EXISTS idx_voice_consistency_checks_chapter_id ON voice_consistency_checks(chapter_id);
CREATE INDEX IF NOT EXISTS idx_voice_consistency_checks_character_name ON voice_consistency_checks(character_name);
CREATE INDEX IF NOT EXISTS idx_voice_consistency_checks_created_at ON voice_consistency_checks(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_voice_consistency_checks_composite_project_character ON voice_consistency_checks(project_id, character_name);

-- Locations indexes
CREATE INDEX IF NOT EXISTS idx_locations_project_id ON locations(project_id);
CREATE INDEX IF NOT EXISTS idx_locations_parent_id ON locations(parent_id);
CREATE INDEX IF NOT EXISTS idx_locations_location_type ON locations(location_type);
CREATE INDEX IF NOT EXISTS idx_locations_composite_project_parent ON locations(project_id, parent_id);
CREATE INDEX IF NOT EXISTS idx_locations_composite_project_type ON locations(project_id, location_type);

-- Story Bible indexes
CREATE INDEX IF NOT EXISTS idx_story_bible_project_id ON story_bible(project_id);
CREATE INDEX IF NOT EXISTS idx_story_bible_entry_type ON story_bible(entry_type);
CREATE INDEX IF NOT EXISTS idx_story_bible_entry_key ON story_bible(entry_key);
CREATE INDEX IF NOT EXISTS idx_story_bible_composite_project_type ON story_bible(project_id, entry_type);
CREATE INDEX IF NOT EXISTS idx_story_bible_composite_project_type_key ON story_bible(project_id, entry_type, entry_key);
CREATE INDEX IF NOT EXISTS idx_story_bible_updated_at ON story_bible(updated_at DESC);

-- Story Bibles (single bible per project) indexes
CREATE INDEX IF NOT EXISTS idx_story_bibles_project_id ON story_bibles(project_id);
CREATE INDEX IF NOT EXISTS idx_story_bibles_updated_at ON story_bibles(updated_at DESC);

-- Writing Sessions indexes
CREATE INDEX IF NOT EXISTS idx_writing_sessions_user_id ON writing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_project_id ON writing_sessions(project_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_started_at ON writing_sessions(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_composite_user_project ON writing_sessions(user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_writing_sessions_composite_user_started ON writing_sessions(user_id, started_at DESC);

-- Writing Goals indexes
CREATE INDEX IF NOT EXISTS idx_writing_goals_user_id ON writing_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_writing_goals_project_id ON writing_goals(project_id);
CREATE INDEX IF NOT EXISTS idx_writing_goals_goal_type ON writing_goals(goal_type);
CREATE INDEX IF NOT EXISTS idx_writing_goals_is_active ON writing_goals(is_active);
CREATE INDEX IF NOT EXISTS idx_writing_goals_composite_user_type_active ON writing_goals(user_id, goal_type, is_active);
CREATE INDEX IF NOT EXISTS idx_writing_goals_composite_project_active ON writing_goals(project_id, is_active);

-- Writing Goal Progress indexes
CREATE INDEX IF NOT EXISTS idx_writing_goal_progress_goal_id ON writing_goal_progress(goal_id);
CREATE INDEX IF NOT EXISTS idx_writing_goal_progress_date ON writing_goal_progress(date DESC);
CREATE INDEX IF NOT EXISTS idx_writing_goal_progress_composite_goal_date ON writing_goal_progress(goal_id, date DESC);

-- Projects indexes (additional)
CREATE INDEX IF NOT EXISTS idx_projects_series_id ON projects(series_id);

-- Chapters indexes (additional)
CREATE INDEX IF NOT EXISTS idx_chapters_project_id ON chapters(project_id);
CREATE INDEX IF NOT EXISTS idx_chapters_chapter_number ON chapters(chapter_number);
CREATE INDEX IF NOT EXISTS idx_chapters_composite_project_number ON chapters(project_id, chapter_number);

-- Characters indexes (additional)
CREATE INDEX IF NOT EXISTS idx_characters_project_id ON characters(project_id);
CREATE INDEX IF NOT EXISTS idx_characters_character_type ON characters(character_type);
CREATE INDEX IF NOT EXISTS idx_characters_composite_project_type ON characters(project_id, character_type);

-- Add indexes for text search optimization (using GIN for full-text search)
CREATE INDEX IF NOT EXISTS idx_story_bible_content_search ON story_bible USING gin(to_tsvector('english', COALESCE(entry_data->>'content', '')));
CREATE INDEX IF NOT EXISTS idx_locations_name_search ON locations USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_locations_description_search ON locations USING gin(to_tsvector('english', COALESCE(description, '')));

-- Add indexes for JSON field queries (using GIN for JSONB)
CREATE INDEX IF NOT EXISTS idx_voice_profiles_patterns ON voice_profiles USING gin(patterns);
CREATE INDEX IF NOT EXISTS idx_story_bible_entry_data ON story_bible USING gin(entry_data);
CREATE INDEX IF NOT EXISTS idx_locations_attributes ON locations USING gin(attributes);
CREATE INDEX IF NOT EXISTS idx_locations_coordinates ON locations USING gin(coordinates);

-- Comment on why these indexes were added
COMMENT ON INDEX idx_voice_profiles_user_id IS 'Optimize queries filtering voice profiles by user';
COMMENT ON INDEX idx_voice_profiles_project_id IS 'Optimize queries filtering voice profiles by project';
COMMENT ON INDEX idx_voice_consistency_checks_project_id IS 'Optimize voice consistency analytics queries';
COMMENT ON INDEX idx_locations_project_id IS 'Optimize location queries by project';
COMMENT ON INDEX idx_story_bible_project_id IS 'Optimize story bible queries by project';
COMMENT ON INDEX idx_story_bible_content_search IS 'Enable full-text search on story bible content';