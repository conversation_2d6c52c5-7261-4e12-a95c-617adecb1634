import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { applyRateLimit } from '@/lib/rate-limiter-unified'

// Schema for creating a new series
const createSeriesSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  genre: z.string().optional(),
  target_audience: z.string().optional(),
  planned_book_count: z.number().int().positive().optional(),
  overall_arc_description: z.string().optional(),
  shared_universe_rules: z.record(z.unknown()).optional(),
  character_continuity: z.record(z.unknown()).optional(),
  timeline_span: z.string().optional(),
})

// GET - Fetch all series for the current user
export async function GET() {
  try {
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Fetch series with book counts
    const { data: series, error } = await supabase
      .from('series')
      .select(`
        *,
        series_books (
          count
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      logger.error('Error fetching series:', error)
      return NextResponse.json({ error: 'Failed to fetch series' }, { status: 500 })
    }

    // Transform the data to include book count
    const seriesWithCounts = series?.map(s => ({
      ...s,
      book_count: s.series_books?.[0]?.count || 0,
      series_books: undefined // Remove the raw count data
    })) || []

    return NextResponse.json({ series: seriesWithCounts })
  } catch (error) {
    logger.error('Error in series GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create a new series
export async function POST(request: Request) {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'authenticated' })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

  try {
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = createSeriesSchema.parse(body)

    // Create the series
    const { data: series, error } = await supabase
      .from('series')
      .insert({
        user_id: user.id,
        ...validatedData,
        current_book_count: 0,
        publication_status: 'planning',
      })
      .select()
      .single()

    if (error) {
      logger.error('Error creating series:', error)
      return NextResponse.json({ error: 'Failed to create series' }, { status: 500 })
    }

    return NextResponse.json({ series }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }
    logger.error('Error in series POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}