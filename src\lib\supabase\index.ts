// Unified Supabase client exports
export { 
  createTypedBrowserClient as create<PERSON>rowser<PERSON><PERSON>,
  createTypedServerClient as createServer<PERSON><PERSON>,
  getBrowserClient,
  createClient
} from './unified-client'

// For backward compatibility
export { createTypedBrowserClient as supabase } from './unified-client'
export { createClient as createBrowserSupabaseClient } from './unified-client'
export { createTypedServerClient as createServerSupabaseClient } from './unified-client'