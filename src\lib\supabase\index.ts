// Unified Supabase client exports
export {
  createTypedBrowserClient as create<PERSON>rowser<PERSON><PERSON>,
  createTypedServerClient,
  createTypedServerClient as createServerClient,
  getBrowserClient,
  createClient
} from './unified-client'

// For backward compatibility
export { createTypedBrowserClient as supabase } from './unified-client'
export { createClient as createBrowserSupabaseClient } from './unified-client'
export { createTypedServerClient as createServerSupabaseClient } from './unified-client'