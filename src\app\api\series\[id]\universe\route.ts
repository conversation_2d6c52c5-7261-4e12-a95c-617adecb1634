import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server';
import { createTypedServerClient } from '@/lib/supabase';
import type { Character as _Character, StoryBible as _StoryBible } from '@/lib/db/types';
import { logger } from '@/lib/services/logger';
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service';

// Universe-related type definitions
interface UniverseBook {
  id: string;
  name: string;
  status: string;
  word_count: number;
  series_order: number;
  characters?: BookCharacter[];
  story_bible?: StoryBibleEntry[];
}

interface BookCharacter {
  name: string;
  role: string;
  description: string;
}

interface StoryBibleEntry {
  category: string;
  title: string;
  content: string;
}


interface UniverseElement {
  name: string;
  description?: string;
  appearances: string[];
}

interface UniverseElements {
  characters: UniverseElement[];
  locations: UniverseElement[];
  concepts: UniverseElement[];
  summary: {
    totalCharacters: number;
    totalLocations: number;
    totalConcepts: number;
  };
}

interface UniverseUpdateData {
  shared_universe?: string | null;
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;

    // Check authentication and series access
    const { user, error: authError } = await authenticateUserForSeries(seriesId);
    if (authError) return authError;

    const supabase = await createTypedServerClient();

    // Get series with universe information
    const { data: series, error: seriesError } = await supabase
      .from('book_series')
      .select('*')
      .eq('id', seriesId)
      .single();

    if (seriesError) {
      if (seriesError.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'));
      }
      throw seriesError;
    }

    if (!series.shared_universe) {
      return NextResponse.json({
        universe: null,
        message: 'This series is not part of a shared universe'
      });
    }

    // Get all series that share the same universe
    const { data: universeSeries, error: universeError } = await supabase
      .from('book_series')
      .select(`
        *,
        books:projects!series_id(
          id,
          name,
          status,
          word_count,
          series_order
        )
      `)
      .eq('shared_universe', series.shared_universe)
      .order('created_at', { ascending: true });

    if (universeError) throw universeError;

    // Get shared universe elements (characters, locations, etc.)
    const universeElements = await getUniverseElements(series.shared_universe);

    const totalBooks = universeSeries?.reduce((sum, s) => sum + (s.books?.length || 0), 0) || 0;
    const totalWordCount = universeSeries?.reduce((sum, s) => 
      sum + (s.books?.reduce((bookSum: number, book: UniverseBook) => bookSum + (book.word_count || 0), 0) || 0), 0
    ) || 0;

    return NextResponse.json({
      universe: {
        name: series.shared_universe,
        totalSeries: universeSeries?.length || 0,
        totalBooks,
        totalWordCount,
        series: universeSeries?.map(s => ({
          id: s.id,
          name: s.name,
          description: s.description,
          status: s.status,
          bookCount: s.books?.length || 0,
          createdAt: new Date(s.created_at),
          books: s.books?.map((book: UniverseBook) => ({
            id: book.id,
            name: book.name,
            status: book.status,
            wordCount: book.word_count,
            order: book.series_order
          })).sort((a: { order: number }, b: { order: number }) => a.order - b.order) || []
        })) || [],
        elements: universeElements
      }
    });
  } catch (error) {
    logger.error('Universe API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;

    // Check authentication and series access
    const { user, error: authError } = await authenticateUserForSeries(seriesId);
    if (authError) return authError;

    const body = await request.json();
    const { universeName, action } = body; // action: 'join', 'create', 'leave'

    if (!action) {
      return handleAPIError(new ValidationError('Invalid request'));
    }

    const updateData: UniverseUpdateData = {};

    switch (action) {
      case 'create':
        if (!universeName) {
          return handleAPIError(new ValidationError('Invalid request'));
        }
        updateData.shared_universe = universeName;
        break;
        
      case 'join':
        if (!universeName) {
          return handleAPIError(new ValidationError('Invalid request'));
        }
        
        // Verify universe exists
        const supabase = await createTypedServerClient();
        const { data: existingUniverse } = await supabase
          .from('book_series')
          .select('shared_universe')
          .eq('shared_universe', universeName)
          .limit(1);

        if (!existingUniverse || existingUniverse.length === 0) {
          return handleAPIError(new ValidationError('Invalid request'));
        }
        
        updateData.shared_universe = universeName;
        break;
        
      case 'leave':
        updateData.shared_universe = null;
        break;
        
      default:
        return handleAPIError(new ValidationError('Invalid request'));
    }

    const supabase = await createTypedServerClient();
    const { data: series, error: updateError } = await supabase
      .from('book_series')
      .update(updateData)
      .eq('id', seriesId)
      .select()
      .single();

    if (updateError) {
      if (updateError.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'));
      }
      throw updateError;
    }

    // Track analytics
    try {
      await supabase
        .from('selection_analytics')
        .insert({
          user_id: user.id,
          event_type: `universe_${action}`,
          selection_data: {
            seriesId,
            seriesName: series.name,
            universeName: action === 'leave' ? 'none' : universeName,
            action
          }
        });
    } catch (analyticsError) {
      logger.warn('Failed to track universe action analytics:', analyticsError);
    }

    return NextResponse.json({ 
      success: true,
      series: {
        id: series.id,
        name: series.name,
        sharedUniverse: series.shared_universe
      },
      action,
      message: getActionMessage(action, universeName)
    });
  } catch (error) {
    logger.error('Universe management API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function getUniverseElements(universeName: string): Promise<UniverseElements> {
  try {
    // Get all series in the universe
    const { data: series } = await supabase
      .from('book_series')
      .select(`
        id,
        books:projects!series_id(
          id,
          characters(
            name,
            role,
            description
          ),
          story_bible(
            category,
            title,
            content
          )
        )
      `)
      .eq('shared_universe', universeName);

    const elements = {
      characters: new Map(),
      locations: new Map(),
      concepts: new Map(),
      timeline: []
    };

    series?.forEach(s => {
      s.books?.forEach((book: { id: unknown; characters: { name: unknown; role: unknown; description: unknown; }[]; story_bible: { category: unknown; title: unknown; content: unknown; }[]; }) => {
        // Collect characters
        book.characters?.forEach((char: { name: unknown; role: unknown; description: unknown; }) => {
          if (!elements.characters.has(String(char.name))) {
            elements.characters.set(String(char.name), {
              name: String(char.name),
              role: String(char.role),
              description: String(char.description),
              appearances: []
            });
          }
          elements.characters.get(String(char.name))?.appearances.push(String(book.id));
        });

        // Collect world-building elements
        book.story_bible?.forEach((entry: { category: unknown; title: unknown; content: unknown; }) => {
          if (String(entry.category) === 'locations' || String(entry.category) === 'setting') {
            if (!elements.locations.has(String(entry.title))) {
              elements.locations.set(String(entry.title), {
                name: String(entry.title),
                description: String(entry.content),
                appearances: []
              });
            }
            elements.locations.get(String(entry.title))?.appearances.push(String(book.id));
          } else if (String(entry.category) === 'concepts' || String(entry.category) === 'world-building') {
            if (!elements.concepts.has(String(entry.title))) {
              elements.concepts.set(String(entry.title), {
                name: String(entry.title),
                description: String(entry.content),
                appearances: []
              });
            }
            elements.concepts.get(String(entry.title))?.appearances.push(String(book.id));
          }
        });
      });
    });

    return {
      characters: Array.from(elements.characters.values()).slice(0, 20), // Limit results
      locations: Array.from(elements.locations.values()).slice(0, 20),
      concepts: Array.from(elements.concepts.values()).slice(0, 20),
      summary: {
        totalCharacters: elements.characters.size,
        totalLocations: elements.locations.size,
        totalConcepts: elements.concepts.size
      }
    };
  } catch (error) {
    logger.error('Error getting universe elements:', error);
    return {
      characters: [],
      locations: [],
      concepts: [],
      summary: { totalCharacters: 0, totalLocations: 0, totalConcepts: 0 }
    };
  }
}

function getActionMessage(action: string, universeName?: string) {
  switch (action) {
    case 'create':
      return `Created new shared universe: "${universeName}"`;
    case 'join':
      return `Joined shared universe: "${universeName}"`;
    case 'leave':
      return 'Left shared universe';
    default:
      return 'Universe action completed';
  }
}