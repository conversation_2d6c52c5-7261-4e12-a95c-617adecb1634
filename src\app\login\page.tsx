'use client'

import { Suspense, useEffect } from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { AuthForm } from '@/components/auth/auth-form'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { BookOpen, ArrowLeft } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

function LoginPageContent() {
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  useEffect(() => {
    // Check for error parameters
    const error = searchParams.get('error')
    const message = searchParams.get('message')
    
    if (error === 'session_expired') {
      toast({
        title: 'Session Expired',
        description: 'Your session has expired. Please sign in again.',
        variant: 'destructive',
      })
    } else if (error === 'unauthorized') {
      toast({
        title: 'Access Denied',
        description: 'Please sign in to access this page.',
        variant: 'destructive',
      })
    } else if (message === 'logout') {
      toast({
        title: 'Signed Out',
        description: 'You have been successfully signed out.',
      })
    }
  }, [searchParams, toast])
  
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="p-4 flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </Link>
        <ThemeToggle />
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8">
          {/* Logo */}
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-primary rounded-lg flex items-center justify-center mb-4">
              <BookOpen className="w-8 h-8 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold">BookScribe AI</h1>
            <p className="text-muted-foreground">Your AI-powered writing companion</p>
          </div>

          {/* Auth Form */}
          <Suspense fallback={<div>Loading...</div>}>
            <AuthForm mode="login" />
          </Suspense>

          {/* Footer Links */}
          <div className="text-center text-sm text-muted-foreground">
            <Link href="/privacy" className="hover:underline">Privacy Policy</Link>
            {' • '}
            <Link href="/terms" className="hover:underline">Terms of Service</Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginPageContent />
    </Suspense>
  )
}