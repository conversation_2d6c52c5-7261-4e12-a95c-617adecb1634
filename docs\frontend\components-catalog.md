# BookScribe Frontend Components Catalog

## Table of Contents
- [Overview](#overview)
- [Component Architecture](#component-architecture)
- [UI Component Library](#ui-component-library)
- [Feature Components](#feature-components)
- [Page Components](#page-components)
- [State Management](#state-management)
- [Theme System](#theme-system)
- [Performance Optimization](#performance-optimization)

## Overview

BookScribe's frontend is built with Next.js 15, React, and TypeScript, following a component-based architecture with over 300 specialized components organized into logical domains.

### Technology Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: Shadcn/ui (Radix UI primitives)
- **Styling**: Tailwind CSS with CSS custom properties
- **State Management**: Zustand + React Context
- **Type Safety**: TypeScript with strict mode
- **Performance**: React Suspense, lazy loading, code splitting

## Component Architecture

### Directory Structure

```
src/components/
├── ui/                    # Base UI components (Shadcn/ui)
├── editor/               # Writing editor components
├── dashboard/            # Dashboard and overview components
├── agents/               # AI agent visualization
├── analytics/            # Analytics and insights
├── characters/           # Character management
├── collaboration/        # Real-time collaboration
├── series/              # Series management
├── universe/            # Universe building
├── auth/                # Authentication components
├── error/               # Error handling
├── loading/             # Loading states
└── layout/              # Layout components
```

### Component Hierarchy

```mermaid
graph TD
    subgraph "Base Layer"
        UI[UI Components]
        Theme[Theme Provider]
        Error[Error Boundaries]
    end
    
    subgraph "Feature Layer"
        Editor[Editor Components]
        Analytics[Analytics Components]
        Agents[Agent Components]
        Characters[Character Components]
    end
    
    subgraph "Page Layer"
        Dashboard[Dashboard]
        Projects[Projects]
        Write[Write Page]
        Settings[Settings]
    end
    
    UI --> Editor
    UI --> Analytics
    Theme --> UI
    Error --> Feature Layer
    Feature Layer --> Page Layer
```

## UI Component Library

### Core UI Components (Shadcn/ui)

#### Button Component
```typescript
// src/components/ui/button.tsx
interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
}

// Usage
<Button variant="default" size="lg">
  Start Writing
</Button>
```

#### Dialog Component
```typescript
// src/components/ui/dialog.tsx
<Dialog>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Title</DialogTitle>
      <DialogDescription>Description</DialogDescription>
    </DialogHeader>
    {/* Content */}
  </DialogContent>
</Dialog>
```

#### Form Components
```typescript
// Validated form field with Zod
<ValidatedFormField
  name="title"
  label="Project Title"
  placeholder="Enter project title"
  required
  validation={z.string().min(1).max(255)}
/>
```

### Custom UI Extensions

#### AI Loading Component
```typescript
// src/components/ui/ai-loading.tsx
<AILoading 
  message="Generating chapter outline..."
  progress={0.75}
  showCancel
/>
```

#### Command Palette
```typescript
// src/components/ui/command-palette.tsx
<CommandPalette
  commands={[
    {
      id: 'new-project',
      label: 'Create New Project',
      shortcut: ['⌘', 'N'],
      action: () => createProject()
    }
  ]}
/>
```

#### Unified Empty State
```typescript
// src/components/ui/unified-empty-state.tsx
<UnifiedEmptyState
  icon={BookOpenIcon}
  title="No projects yet"
  description="Create your first project to start writing"
  action={{
    label: "Create Project",
    onClick: () => router.push('/projects/new')
  }}
/>
```

## Feature Components

### Editor Components

#### Monaco Editor Integration
```typescript
// src/components/editor/unified-monaco-editor.tsx
<UnifiedMonacoEditor
  value={content}
  onChange={handleChange}
  language="markdown"
  theme="writers-sanctuary"
  options={{
    fontSize: 16,
    lineHeight: 28,
    wordWrap: 'on',
    minimap: { enabled: false }
  }}
  onMount={handleEditorMount}
/>
```

#### Collaborative Editor
```typescript
// src/components/editor/collaborative-monaco-editor.tsx
<CollaborativeMonacoEditor
  projectId={projectId}
  chapterId={chapterId}
  collaborators={collaborators}
  onCursorChange={handleCursorChange}
  conflictResolution="operational-transform"
/>
```

#### Story Bible Panel
```typescript
// src/components/editor/story-bible-panel.tsx
<StoryBiblePanel
  projectId={projectId}
  characters={characters}
  worldBuilding={worldData}
  timeline={timelineEvents}
  onUpdate={handleBibleUpdate}
/>
```

#### Writing Tools Sidebar
```typescript
// src/components/editor/project-tools-sidebar.tsx
<ProjectToolsSidebar
  tools={[
    'ai-assistant',
    'character-reference',
    'plot-tracker',
    'voice-analyzer',
    'word-count'
  ]}
  position="right"
  collapsible
/>
```

### AI Agent Components

#### Visual Agent Pipeline
```typescript
// src/components/agents/visual-agent-pipeline.tsx
<VisualAgentPipeline
  agents={[
    { type: 'story-architect', status: 'completed' },
    { type: 'character-developer', status: 'running' },
    { type: 'chapter-planner', status: 'pending' }
  ]}
  progress={0.45}
  estimatedTime="5 minutes remaining"
/>
```

#### Agent Dashboard
```typescript
// src/components/agents/agent-dashboard.tsx
<AgentDashboard
  projectId={projectId}
  activeAgents={activeAgents}
  completedTasks={completedTasks}
  upcomingTasks={upcomingTasks}
  performance={performanceMetrics}
/>
```

### Analytics Components

#### Writing Analytics Dashboard
```typescript
// src/components/analytics/writing-analytics-dashboard.tsx
<WritingAnalyticsDashboard
  timeRange="last30days"
  metrics={{
    totalWords: 45000,
    sessionsCount: 23,
    averageWordsPerSession: 1956,
    writingStreak: 7
  }}
  chartData={chartData}
/>
```

#### Quality Metrics Display
```typescript
// src/components/analytics/components/quality-metrics.tsx
<QualityMetrics
  scores={{
    readability: 85,
    consistency: 92,
    pacing: 78,
    characterDevelopment: 88,
    dialogue: 90
  }}
  recommendations={qualityRecommendations}
/>
```

### Character Management

#### Character Manager
```typescript
// src/components/characters/character-manager.tsx
<CharacterManager
  projectId={projectId}
  characters={characters}
  onCreateCharacter={handleCreate}
  onEditCharacter={handleEdit}
  onDeleteCharacter={handleDelete}
  showRelationships
  enableAIGeneration
/>
```

#### Character Arc Visualizer
```typescript
// src/components/analysis/character-arc-visualizer.tsx
<CharacterArcVisualizer
  character={character}
  chapters={chapters}
  arcType="transformation"
  showEmotionalJourney
  interactive
/>
```

### Collaboration Components

#### Collaborative Cursors
```typescript
// src/components/collaboration/collaborative-cursors.tsx
<CollaborativeCursors
  users={[
    { id: '1', name: 'John', color: '#FF6B6B', position: { line: 10, col: 45 } },
    { id: '2', name: 'Jane', color: '#4ECDC4', position: { line: 25, col: 12 } }
  ]}
  showLabels
  animateMovement
/>
```

#### Conflict Resolution Dialog
```typescript
// src/components/collaboration/conflict-resolution-dialog.tsx
<ConflictResolutionDialog
  conflicts={[
    {
      type: 'content',
      yourVersion: 'Your text...',
      theirVersion: 'Their text...',
      baseVersion: 'Original text...'
    }
  ]}
  onResolve={handleConflictResolution}
  strategies={['yours', 'theirs', 'merge', 'custom']}
/>
```

### Series & Universe Components

#### Series Consistency Dashboard
```typescript
// src/components/series/series-consistency-dashboard.tsx
<SeriesConsistencyDashboard
  seriesId={seriesId}
  books={books}
  continuityIssues={issues}
  characterProgression={characterData}
  worldConsistency={worldData}
/>
```

#### Universe Manager
```typescript
// src/components/universe/universe-manager.tsx
<UniverseManager
  universeId={universeId}
  settings={{
    magicSystem: magicRules,
    technology: techLevel,
    geography: worldMap,
    cultures: cultures
  }}
  sharedCharacters={characters}
  timeline={timelineEvents}
/>
```

## Page Components

### Dashboard Page
```typescript
// src/components/dashboard/dashboard-wrapper.tsx
<DashboardWrapper>
  <StatsOverview stats={userStats} />
  <QuickActionsWidget actions={quickActions} />
  <RecentProjects projects={recentProjects} />
  <WritingStreak currentStreak={streak} />
  <AIUsageWidget usage={aiUsage} />
</DashboardWrapper>
```

### Project Creation Wizard
```typescript
// src/components/wizard/unified-project-wizard.tsx
<UnifiedProjectWizard
  steps={[
    'genre-style',
    'structure-pacing',
    'characters-world',
    'themes-content',
    'technical-specs'
  ]}
  onComplete={handleWizardComplete}
  saveProgress
  showStepIndicator
/>
```

### Write Page Layout
```typescript
// src/components/editor/write-page-editor.tsx
<WritePageEditor
  project={project}
  chapter={currentChapter}
  layout="three-panel" // editor, outline, tools
  panels={{
    left: <ChapterNavigator />,
    center: <UnifiedMonacoEditor />,
    right: <ProjectToolsSidebar />
  }}
  headerComponents={<WritePageHeader />}
/>
```

## State Management

### Zustand Stores

#### Project Store
```typescript
// stores/projectStore.ts
interface ProjectStore {
  currentProject: Project | null
  chapters: Chapter[]
  characters: Character[]
  isLoading: boolean
  
  // Actions
  setProject: (project: Project) => void
  updateChapter: (id: string, content: string) => void
  addCharacter: (character: Character) => void
}
```

#### Editor Store
```typescript
// stores/editorStore.ts
interface EditorStore {
  content: string
  cursorPosition: Position
  selectedText: string
  wordCount: number
  
  // Collaboration
  collaborators: Collaborator[]
  remoteCursors: RemoteCursor[]
  
  // Actions
  updateContent: (content: string) => void
  setCursor: (position: Position) => void
}
```

### React Context Providers

#### Theme Provider
```typescript
// src/components/theme-provider.tsx
<ThemeProvider
  defaultTheme="writers-sanctuary"
  themes={availableThemes}
  enableSystemTheme
>
  {children}
</ThemeProvider>
```

#### Settings Provider
```typescript
// src/components/settings/settings-provider.tsx
<SettingsProvider
  defaultSettings={{
    editor: { fontSize: 16, lineHeight: 1.8 },
    ai: { model: 'gpt-4', temperature: 0.7 },
    accessibility: { reduceMotion: false }
  }}
>
  {children}
</SettingsProvider>
```

## Theme System

### Writer's Sanctuary Theme
```css
/* CSS Custom Properties */
:root[data-theme="writers-sanctuary"] {
  --background: 39 35 20;      /* Warm paper tone */
  --foreground: 24 24 27;      /* Dark ink */
  --card: 39 36 25;           /* Slightly darker paper */
  --primary: 24 24 27;        /* Ink black */
  --secondary: 39 36 30;      /* Aged paper */
  --accent: 34 197 94;        /* Emerald accent */
  
  /* Typography */
  --font-serif: 'Crimson Text', Georgia, serif;
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}
```

### Theme Components

#### Theme Toggle
```typescript
// src/components/ui/theme-toggle.tsx
<ThemeToggle
  themes={['writers-sanctuary', 'midnight-ink', 'forest-manuscript']}
  showLabel
  position="top-right"
/>
```

#### Theme Customization Hub
```typescript
// src/components/customization/customization-hub.tsx
<CustomizationHub
  categories={['theme', 'typography', 'editor', 'accessibility']}
  livePreview
  exportSettings
  importSettings
/>
```

## Performance Optimization

### Lazy Loading
```typescript
// Lazy load heavy components
const LazyMonacoEditor = lazy(() => import('./editor/lazy-monaco-editor'))
const LazyAnalytics = lazy(() => import('./analytics/lazy-analytics'))

// Usage with Suspense
<Suspense fallback={<EditorSkeleton />}>
  <LazyMonacoEditor {...props} />
</Suspense>
```

### Code Splitting
```typescript
// Dynamic imports for feature components
const loadAgentDashboard = () => import('./agents/agent-dashboard')
const loadUniverseManager = () => import('./universe/universe-manager')
```

### Memoization
```typescript
// Memoize expensive computations
const MemoizedCharacterGraph = memo(CharacterRelationshipGraph, (prev, next) => {
  return prev.characters.length === next.characters.length &&
         prev.relationships.length === next.relationships.length
})
```

### Virtual Scrolling
```typescript
// For long lists
<VirtualList
  items={chapters}
  itemHeight={80}
  renderItem={(chapter) => <ChapterCard {...chapter} />}
  overscan={5}
/>
```

## Component Best Practices

### Accessibility
- All interactive components support keyboard navigation
- ARIA labels and descriptions
- Focus management and skip links
- Screen reader announcements
- High contrast mode support

### Error Handling
```typescript
// Component-level error boundaries
<ComponentErrorBoundary
  fallback={<ErrorFallback />}
  onError={handleError}
>
  <ComplexComponent />
</ComponentErrorBoundary>
```

### Loading States
```typescript
// Consistent loading patterns
<AsyncBoundary
  loading={<ComponentSkeleton />}
  error={<ErrorState />}
  data={data}
>
  {(data) => <Component data={data} />}
</AsyncBoundary>
```

### Testing
- Component unit tests with React Testing Library
- Integration tests for complex interactions
- Visual regression tests with Playwright
- Accessibility tests with axe-core