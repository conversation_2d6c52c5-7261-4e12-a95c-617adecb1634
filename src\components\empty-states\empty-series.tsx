'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  BookOpenCheck, 
  Plus, 
  Layers3,
  Link2,
  Workflow,
  ArrowRight
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export function EmptySeries() {
  const router = useRouter()

  return (
    <div className="max-w-4xl mx-auto py-12">
      <Card className="border-2 border-dashed border-muted-foreground/20">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/10 flex items-center justify-center">
            <BookOpenCheck className="w-8 h-8 text-primary" />
          </div>
          
          <h2 className="text-2xl font-literary-display text-foreground mb-4">
            No Series Created Yet
          </h2>
          
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto">
            Organize your multi-book stories into series. Connect related novels, manage story arcs, and track character development across books.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Layers3 className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Multi-Book Planning</h3>
              <p className="text-sm text-muted-foreground">
                Plan overarching story arcs across multiple novels
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Link2 className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Connected Stories</h3>
              <p className="text-sm text-muted-foreground">
                Link characters and plot threads between books
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Workflow className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Series Bible</h3>
              <p className="text-sm text-muted-foreground">
                Maintain consistency with a centralized story bible
              </p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 justify-center">
            <Button 
              size="lg" 
              onClick={() => router.push('/series/new')}
              className="group"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Series
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => router.push('/projects')}
            >
              <BookOpenCheck className="w-4 h-4 mr-2" />
              View Projects
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}