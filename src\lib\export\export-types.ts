import type { Chapter, Character, Project, StoryBible } from '@/lib/db/types'
import type { UserSubscription } from '@/lib/subscription'

// Extended project interface for export purposes
export interface ExportProject extends Project {
  author_name?: string
  isbn?: string
  publisher?: string
  language?: string
  cover_image_url?: string
  dedication?: string
  acknowledgments?: string
  author_bio?: string
}

// Database client interface
export interface DatabaseClient {
  projects: {
    getById: (id: string) => Promise<Project | null>;
  };
  chapters: {
    getAll: (projectId: string) => Promise<Chapter[]>;
  };
  characters: {
    getAll: (projectId: string) => Promise<Character[]>;
  };
  storyBible: {
    getAll: (projectId: string) => Promise<StoryBible[]>;
  };
}

export interface ExportOptions {
  format: 'pdf' | 'epub' | 'docx' | 'txt' | 'markdown';
  includeMetadata: boolean;
  includeFrontMatter: boolean;
  includeChapterBreaks: boolean;
  includeTableOfContents: boolean;
  customStyling?: {
    fontFamily?: string;
    fontSize?: number;
    lineHeight?: number;
    paragraphSpacing?: number;
    margins?: {
      top?: number;
      bottom?: number;
      left?: number;
      right?: number;
    };
    headerFooter?: {
      includePageNumbers?: boolean;
      includeTitle?: boolean;
      includeAuthor?: boolean;
    };
  };
  watermark?: boolean;
  sceneBreakSymbol?: string;
  chapterNumbering?: 'numeric' | 'word' | 'roman';
  includeCharacterList?: boolean;
  includeWorldBuilding?: boolean;
  includeOutline?: boolean;
  includeNotes?: boolean;
  trim?: boolean;
  excludeEmptyChapters?: boolean;
}