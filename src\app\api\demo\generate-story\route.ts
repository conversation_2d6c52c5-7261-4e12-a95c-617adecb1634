import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { openai } from '@/lib/openai'
import { rateLimiter } from '@/lib/rate-limiter-unified'
import { UnifiedResponse } from '@/lib/api/unified-response'

// Demo generation schema
const demoGenerationSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().min(10).max(500),
  genre: z.string(),
  targetAudience: z.string(),
  themes: z.array(z.string()).min(1).max(5),
  tones: z.array(z.string()).min(1).max(5),
  protagonist: z.string().optional(),
  setting: z.string().optional(),
})

export async function POST(req: NextRequest) {
  try {
    // Rate limiting - allow 5 demo generations per IP per hour
    const ip = req.headers.get('x-forwarded-for') || 'unknown'
    const { success } = await rateLimiter.limit(`demo-generation:${ip}`, {
      requests: 5,
      window: '1h'
    })

    if (!success) {
      return UnifiedResponse.rateLimited(60 * 60, 5) // 1 hour retry, 5 requests limit
    }

    const body = await req.json()
    const validatedData = demoGenerationSchema.parse(body)

    // Generate story structure using OpenAI
    const prompt = `Create a compelling story structure for a ${validatedData.genre} novel with the following details:
Title: ${validatedData.title}
Description: ${validatedData.description}
Target Audience: ${validatedData.targetAudience}
Themes: ${validatedData.themes.join(', ')}
Tones: ${validatedData.tones.join(', ')}
${validatedData.protagonist ? `Protagonist: ${validatedData.protagonist}` : ''}
${validatedData.setting ? `Setting: ${validatedData.setting}` : ''}

Generate a structured response with:
1. Expanded premise (2-3 sentences)
2. Three-act structure overview
3. 5 key plot points
4. 3 main characters with brief descriptions
5. Central conflict
6. Potential ending

Keep it concise but compelling.`

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { 
          role: 'system', 
          content: 'You are a master storyteller and novelist. Create compelling, structured story outlines that captivate readers.'
        },
        { role: 'user', content: prompt }
      ],
      temperature: 0.8,
      max_tokens: 800,
    })

    const storyStructure = completion.choices[0].message.content

    // Parse the structure into a more structured format
    const structuredResponse = {
      title: validatedData.title,
      genre: validatedData.genre,
      generatedStructure: storyStructure,
      chapters: generateChapterOutline(validatedData.genre),
      estimatedWordCount: 80000,
      generatedAt: new Date().toISOString(),
      expiresIn: '30 minutes',
    }

    return UnifiedResponse.success(structuredResponse, {
      requestId: `demo-${Date.now()}`,
    })
  } catch (error) {
    console.error('Demo generation error:', error)
    
    if (error instanceof z.ZodError) {
      return UnifiedResponse.validationError(error)
    }

    return UnifiedResponse.error({
      message: 'Failed to generate story structure',
      code: 'AI_SERVICE_ERROR',
      details: error
    }, undefined, 500)
  }
}

// Generate basic chapter outline based on genre
function generateChapterOutline(genre: string): Array<{ number: number; title: string; description: string }> {
  const templates: Record<string, Array<{ title: string; description: string }>> = {
    fantasy: [
      { title: "The Ordinary World", description: "Introduce protagonist in their normal life" },
      { title: "The Call to Adventure", description: "Inciting incident disrupts the ordinary" },
      { title: "Crossing the Threshold", description: "Hero enters the new world" },
      { title: "Tests and Allies", description: "Hero faces challenges and meets companions" },
      { title: "The Ordeal", description: "Major crisis point" },
    ],
    mystery: [
      { title: "The Crime", description: "Discovery of the mystery" },
      { title: "Initial Investigation", description: "First clues and red herrings" },
      { title: "Deepening Mystery", description: "Complications arise" },
      { title: "False Solutions", description: "Wrong theories tested" },
      { title: "The Revelation", description: "Truth uncovered" },
    ],
    romance: [
      { title: "The Meet", description: "Characters first encounter" },
      { title: "The Attraction", description: "Initial chemistry develops" },
      { title: "The First Conflict", description: "Obstacles to romance appear" },
      { title: "The Connection", description: "Deeper bonds form" },
      { title: "The Crisis", description: "Relationship tested" },
    ],
    default: [
      { title: "Setup", description: "Establish world and characters" },
      { title: "Catalyst", description: "Event that starts the journey" },
      { title: "Rising Action", description: "Conflicts intensify" },
      { title: "Climax", description: "Peak of tension" },
      { title: "Resolution", description: "Aftermath and new normal" },
    ]
  }

  const template = templates[genre.toLowerCase()] || templates.default
  return template.map((chapter, index) => ({
    number: index + 1,
    ...chapter
  }))
}