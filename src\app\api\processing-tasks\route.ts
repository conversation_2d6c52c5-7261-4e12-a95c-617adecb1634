import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    const projectId = url.searchParams.get('projectId')
    
    const supabase = await createTypedServerClient()

    let query = supabase
      .from('processing_tasks')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(50)

    // Filter by user if not admin
    if (userId && userId === user.id) {
      query = query.eq('user_id', userId)
    } else if (!user.role?.includes('admin')) {
      query = query.eq('user_id', user.id)
    }

    if (projectId) {
      query = query.eq('project_id', projectId)
    }

    const { data: tasks, error } = await query

    if (error) {
      throw error
    }

    return NextResponse.json({ tasks: tasks || [] })
  } catch (error) {
    console.error('Error fetching processing tasks:', error)
    return NextResponse.json(
      { error: 'Failed to fetch processing tasks' },
      { status: 500 }
    )
  }
})