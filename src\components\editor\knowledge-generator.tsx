'use client'

import { useState } from 'react'

import { logger } from '@/lib/services/logger';

// Import the interface from the main component to ensure consistency
import type { GeneratedKnowledgeItem } from './knowledge-input-panel'

// Keep local interface for backward compatibility
interface KnowledgeItem {
  id: string
  type: 'character' | 'location' | 'story-arc' | 'theme' | 'conflict' | 'world-building' | 'timeline' | 'setting' | 'plot-device' | 'research' | 'note'
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  category?: string
  importance?: 'low' | 'medium' | 'high'
  connections?: string[] // IDs of related items
}
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Wand2, 
  FileText, 
  Users as _Users, 
  MapPin as _MapPin, 
  BookOpen as _BookOpen, 
  Zap,
  CheckCircle,
  AlertCircle as _AlertCircle,
  Loader2
} from 'lucide-react'
import { TIME_MS } from '@/lib/constants'

interface KnowledgeGeneratorProps {
  projectId: string
  onGenerated: (items: GeneratedKnowledgeItem[]) => void
  onClose: () => void
}


export function KnowledgeGenerator({ projectId: _projectId, onGenerated, onClose }: KnowledgeGeneratorProps) {
  const [generationType, setGenerationType] = useState<'blank' | 'ai-generated'>('blank')
  const [projectDescription, setProjectDescription] = useState('')
  const [genre, setGenre] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [generatedItems, setGeneratedItems] = useState<KnowledgeItem[]>([])

  // Example prompts for future use - keeping for reference
  /* const generationPrompts = [
    {
      id: 'story-overview',
      category: 'Story Concept',
      prompt: 'Describe your story in a few sentences',
      example: 'A fantasy adventure about a young mage discovering ancient magic in a world where technology and magic coexist'
    },
    {
      id: 'main-characters',
      category: 'Characters',
      prompt: 'Who are the main characters?',
      example: 'Elena (protagonist mage), Marcus (tech-savvy companion), The Shadow Council (antagonists)'
    },
    {
      id: 'setting',
      category: 'World Building',
      prompt: 'Describe the world and setting',
      example: 'Neo-Arcadia: A futuristic city where magical academies stand alongside tech corporations'
    },
    {
      id: 'themes',
      category: 'Themes & Conflicts',
      prompt: 'What are the main themes and conflicts?',
      example: 'Balance between tradition and progress, personal growth, the cost of power'
    }
  ]; */

  const handleGenerateKnowledge = async () => {
    if (!projectDescription.trim()) return

    setIsGenerating(true)
    setGenerationProgress(0)

    try {
      // Simulate AI generation process
      const items = []
      
      // Generate story overview
      setGenerationProgress(25)
      items.push({
        id: `gen_${Date.now()}_1`,
        type: 'research' as const,
        title: 'Story Overview',
        content: `# ${projectDescription}

## Genre & Style
- Primary Genre: ${genre || 'Literary Fiction'}
- Target Audience: Adult readers
- Narrative Style: Third person limited
- Tone: Contemplative and immersive

## Story Structure
- Act I: Setup and inciting incident
- Act II: Rising action and complications
- Act III: Climax and resolution
- Estimated length: 80,000-100,000 words`,
        importance: 'high' as const,
        tags: ['story-overview', 'foundation'],
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      // Generate main characters
      setGenerationProgress(50)
      items.push({
        id: `gen_${Date.now()}_2`,
        type: 'character' as const,
        title: 'Main Characters',
        content: `## Protagonist
**Name**: [To be determined based on story]
- Age: 25-35
- Background: Complex past that drives the narrative
- Goals: Personal growth and overcoming internal conflicts
- Flaws: Self-doubt, tendency to isolate
- Arc: From uncertainty to self-acceptance

## Supporting Characters
**Mentor Figure**
- Role: Guides protagonist through challenges
- Relationship: Initially distant, becomes trusted ally
- Secrets: Hidden knowledge relevant to main conflict

**Antagonist**
- Motivation: Opposing worldview to protagonist
- Methods: Psychological manipulation rather than direct confrontation
- Connection: Shared history with protagonist

## Character Relationships
- Central relationship drives emotional core of story
- Secondary relationships provide support and conflict
- Each character serves the theme and plot progression`,
        importance: 'high' as const,
        tags: ['characters', 'foundation'],
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      // Generate world building
      setGenerationProgress(75)
      items.push({
        id: `gen_${Date.now()}_3`,
        type: 'world-building' as const,
        title: 'World & Setting',
        content: `## Primary Setting
**Location**: [Main setting based on story description]
- Physical description: Detailed environment that reflects themes
- Atmosphere: Mood and emotional resonance of the space
- Historical significance: How the past shapes the present
- Cultural elements: Social norms, traditions, conflicts

## Secondary Locations
**Supporting Settings**
- Each location serves specific narrative purpose
- Contrasts with or complements main setting
- Provides opportunities for character development
- Advances plot through environmental storytelling

## World Rules & Logic
**Internal Consistency**
- Established rules that govern the story world
- Consequences and limitations that create tension
- Social structures and hierarchies
- Economic and political systems (if relevant)

## Sensory Details
- Visual elements that create vivid imagery
- Sounds, smells, textures that immerse readers
- Seasonal changes or time-based variations
- How characters interact with their environment`,
        importance: 'medium' as const,
        tags: ['world-building', 'setting'],
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      // Generate themes and conflicts
      setGenerationProgress(90)
      items.push({
        id: `gen_${Date.now()}_4`,
        type: 'theme' as const,
        title: 'Themes & Central Conflicts',
        content: `## Primary Theme
**Central Message**: [Based on story description]
- How theme manifests through character actions
- Symbolic elements that reinforce the theme
- Dialogue and internal monologue that explores theme
- Resolution that provides thematic satisfaction

## Secondary Themes
**Supporting Ideas**
- Complementary themes that add depth
- How they intersect with the primary theme
- Character arcs that embody different aspects
- Subplots that explore thematic variations

## Central Conflicts
**External Conflict**
- Protagonist vs. antagonist/society/nature
- Obstacles that create dramatic tension
- Stakes that matter to readers
- Escalating complications

**Internal Conflict**
- Protagonist's psychological struggle
- Competing desires or values
- Character flaws that create problems
- Growth required for resolution

## Thematic Questions
- What questions does the story ask?
- How do characters embody different answers?
- What insights emerge through the narrative?
- How does the ending address these questions?`,
        importance: 'medium' as const,
        tags: ['themes', 'conflict'],
        createdAt: new Date(),
        updatedAt: new Date(),
        connections: []
      })

      setGenerationProgress(100)
      setGeneratedItems(items)
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, TIME_MS.SECOND))
      
    } catch (error) {
      logger.error('Generation failed:', error);
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAcceptGeneration = () => {
    onGenerated(generatedItems)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-mono">
            <Wand2 className="h-5 w-5 text-info" />
            Initialize Knowledge Base
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Generation Type Selection */}
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setGenerationType('blank')}
              className={`p-4 rounded-lg border-2 transition-colors ${
                generationType === 'blank'
                  ? 'border-info bg-info-light dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
              }`}
            >
              <FileText className="h-8 w-8 mx-auto mb-2 text-gray-600" />
              <h3 className="font-mono font-semibold">Blank Story</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Start with empty knowledge base
              </p>
            </button>

            <button
              onClick={() => setGenerationType('ai-generated')}
              className={`p-4 rounded-lg border-2 transition-colors ${
                generationType === 'ai-generated'
                  ? 'border-info bg-info-light dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
              }`}
            >
              <Zap className="h-8 w-8 mx-auto mb-2 text-info" />
              <h3 className="font-mono font-semibold">AI Generated</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Generate from story description
              </p>
            </button>
          </div>

          {/* AI Generation Form */}
          {generationType === 'ai-generated' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-mono font-medium mb-2">
                  Story Description
                </label>
                <Textarea
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  placeholder="Describe your story concept, main characters, and setting..."
                  className="font-mono"
                  rows={4}
                />
              </div>

              <div>
                <label className="block text-sm font-mono font-medium mb-2">
                  Genre
                </label>
                <Input
                  value={genre}
                  onChange={(e) => setGenre(e.target.value)}
                  placeholder="e.g., Fantasy, Mystery, Romance, Science Fiction..."
                  className="font-mono"
                />
              </div>

              {/* Generation Progress */}
              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-info" />
                    <span className="text-sm font-mono">Generating knowledge base...</span>
                  </div>
                  <Progress value={generationProgress} className="w-full" />
                </div>
              )}

              {/* Generated Items Preview */}
              {generatedItems.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-mono font-semibold">Generated Items:</h4>
                  <div className="grid grid-cols-1 gap-2 max-h-60 overflow-y-auto">
                    {generatedItems.map((item) => (
                      <div key={item.id} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                        <CheckCircle className="h-4 w-4 text-success" />
                        <span className="font-mono text-sm">{item.title}</span>
                        <Badge className="ml-auto font-mono text-xs">
                          {item.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose} className="font-mono">
              Cancel
            </Button>
            
            {generationType === 'blank' ? (
              <Button onClick={() => { onGenerated([]); onClose(); }} className="font-mono">
                Create Blank Story
              </Button>
            ) : (
              <>
                {generatedItems.length === 0 ? (
                  <Button 
                    onClick={handleGenerateKnowledge}
                    disabled={!projectDescription.trim() || isGenerating}
                    className="font-mono"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-4 w-4 mr-2" />
                        Generate Knowledge Base
                      </>
                    )}
                  </Button>
                ) : (
                  <Button onClick={handleAcceptGeneration} className="font-mono">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Accept & Continue
                  </Button>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
