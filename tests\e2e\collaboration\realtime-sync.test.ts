import { test, expect, Page, BrowserContext } from '@playwright/test';
import { createTestUser, deleteTestUser, TestUser } from '../helpers/test-users';
import { createTestProject, deleteTestProject, TestProject } from '../helpers/test-projects';
import { waitForRealtimeUpdate, measureSyncLatency } from '../helpers/realtime-helpers';

describe('Real-time Synchronization E2E Tests', () => {
  let contexts: BrowserContext[] = [];
  let users: TestUser[] = [];
  let project: TestProject;

  test.beforeAll(async ({ browser }) => {
    // Create multiple test users for collaboration
    for (let i = 0; i < 3; i++) {
      const user = await createTestUser({
        email: `user${i}@test.com`,
        password: 'TestPassword123!',
        name: `Test User ${i}`
      });
      users.push(user);

      const context = await browser.newContext();
      contexts.push(context);
    }

    // Create shared project
    project = await createTestProject({
      title: 'Real-time Sync Test',
      description: 'Testing real-time synchronization',
      ownerId: users[0].id
    });

    // Add other users as collaborators
    for (let i = 1; i < users.length; i++) {
      await addCollaborator(project.id, users[0].id, users[i].email, 'editor');
    }
  });

  test.afterAll(async () => {
    // Cleanup
    await deleteTestProject(project.id);
    for (const user of users) {
      await deleteTestUser(user.id);
    }
    for (const context of contexts) {
      await context.close();
    }
  });

  test('should sync text changes in real-time across multiple users', async () => {
    const pages: Page[] = [];
    
    // Login all users and navigate to editor
    for (let i = 0; i < users.length; i++) {
      const page = await contexts[i].newPage();
      pages.push(page);
      
      await loginUser(page, users[i]);
      await page.goto(`/projects/${project.id}/editor`);
      await page.waitForSelector('[data-testid="editor-content"]');
    }

    // User 0 types
    await pages[0].click('[data-testid="editor-content"]');
    await pages[0].type('[data-testid="editor-content"]', 'User 0 is typing this sentence. ');

    // Wait for sync on other pages
    await Promise.all(pages.slice(1).map(page => 
      waitForRealtimeUpdate(page, 'User 0 is typing this sentence.')
    ));

    // User 1 types
    await pages[1].click('[data-testid="editor-content"]');
    await pages[1].keyboard.press('End');
    await pages[1].type('[data-testid="editor-content"]', 'User 1 adds more text. ');

    // Wait for sync
    await Promise.all([pages[0], pages[2]].map(page => 
      waitForRealtimeUpdate(page, 'User 1 adds more text.')
    ));

    // User 2 types
    await pages[2].click('[data-testid="editor-content"]');
    await pages[2].keyboard.press('End');
    await pages[2].type('[data-testid="editor-content"]', 'User 2 completes the paragraph.');

    // Wait for sync
    await Promise.all([pages[0], pages[1]].map(page => 
      waitForRealtimeUpdate(page, 'User 2 completes the paragraph.')
    ));

    // Verify all pages have the same content
    const expectedContent = 'User 0 is typing this sentence. User 1 adds more text. User 2 completes the paragraph.';
    for (const page of pages) {
      const content = await page.textContent('[data-testid="editor-content"]');
      expect(content).toBe(expectedContent);
    }

    // Cleanup pages
    for (const page of pages) {
      await page.close();
    }
  });

  test('should sync cursor positions and selections', async () => {
    const page1 = await contexts[0].newPage();
    const page2 = await contexts[1].newPage();

    await loginUser(page1, users[0]);
    await loginUser(page2, users[1]);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);

    // Set initial content
    await page1.click('[data-testid="editor-content"]');
    await page1.fill('[data-testid="editor-content"]', 'This is a test sentence for cursor tracking.');
    
    await waitForRealtimeUpdate(page2, 'This is a test sentence for cursor tracking.');

    // User 1 places cursor
    await page1.click('[data-testid="editor-content"]');
    await page1.keyboard.press('Home');
    await page1.keyboard.press('ArrowRight', { count: 5 }); // After "This "

    // User 2 should see User 1's cursor
    const user1Cursor = page2.locator(`[data-testid="remote-cursor-${users[0].id}"]`);
    await expect(user1Cursor).toBeVisible();
    
    // Check cursor position (approximate)
    const cursorPosition = await user1Cursor.boundingBox();
    expect(cursorPosition).toBeTruthy();

    // User 1 selects text
    await page1.keyboard.down('Shift');
    await page1.keyboard.press('ArrowRight', { count: 2 }); // Select "is"
    await page1.keyboard.up('Shift');

    // User 2 should see the selection
    const user1Selection = page2.locator(`[data-testid="remote-selection-${users[0].id}"]`);
    await expect(user1Selection).toBeVisible();
    await expect(user1Selection).toHaveCSS('background-color', /rgba?\(.*\)/); // Has highlight color

    await page1.close();
    await page2.close();
  });

  test('should handle rapid concurrent edits without conflicts', async () => {
    const pageCount = 3;
    const pages: Page[] = [];
    const editPromises: Promise<void>[] = [];

    // Setup pages
    for (let i = 0; i < pageCount; i++) {
      const page = await contexts[i].newPage();
      pages.push(page);
      await loginUser(page, users[i]);
      await page.goto(`/projects/${project.id}/editor`);
      await page.waitForSelector('[data-testid="editor-content"]');
    }

    // Clear content
    await pages[0].fill('[data-testid="editor-content"]', '');
    await Promise.all(pages.slice(1).map(page => 
      page.waitForFunction(() => {
        const editor = document.querySelector('[data-testid="editor-content"]');
        return editor?.textContent === '';
      })
    ));

    // Each user rapidly types their content
    for (let i = 0; i < pageCount; i++) {
      editPromises.push((async () => {
        const page = pages[i];
        await page.click('[data-testid="editor-content"]');
        
        // Type multiple lines quickly
        for (let j = 0; j < 5; j++) {
          await page.keyboard.press('End');
          await page.type('[data-testid="editor-content"]', `User ${i} - Line ${j}\n`, { delay: 10 });
        }
      })());
    }

    // Wait for all edits to complete
    await Promise.all(editPromises);

    // Wait for sync to stabilize
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify all pages have all content
    for (const page of pages) {
      const content = await page.textContent('[data-testid="editor-content"]');
      
      // Should contain lines from all users
      for (let i = 0; i < pageCount; i++) {
        for (let j = 0; j < 5; j++) {
          expect(content).toContain(`User ${i} - Line ${j}`);
        }
      }
    }

    // Cleanup
    for (const page of pages) {
      await page.close();
    }
  });

  test('should sync non-text changes (formatting, styles)', async () => {
    const page1 = await contexts[0].newPage();
    const page2 = await contexts[1].newPage();

    await loginUser(page1, users[0]);
    await loginUser(page2, users[1]);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);

    // Add content
    await page1.fill('[data-testid="editor-content"]', 'This text will be formatted.');
    await waitForRealtimeUpdate(page2, 'This text will be formatted.');

    // Select and bold text
    await page1.click('[data-testid="editor-content"]');
    await page1.keyboard.press('Control+A'); // Select all
    await page1.click('[data-testid="format-bold-btn"]');

    // Wait for formatting sync
    await page2.waitForFunction(() => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      const boldText = editor?.querySelector('strong, b');
      return boldText?.textContent === 'This text will be formatted.';
    });

    // Apply italic
    await page1.click('[data-testid="format-italic-btn"]');

    // Wait for italic sync
    await page2.waitForFunction(() => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      const italicText = editor?.querySelector('em, i');
      return italicText !== null;
    });

    // Insert a heading
    await page1.keyboard.press('End');
    await page1.keyboard.press('Enter');
    await page1.click('[data-testid="format-heading-btn"]');
    await page1.type('[data-testid="editor-content"]', 'This is a heading');

    // Verify heading on page2
    await page2.waitForFunction(() => {
      const editor = document.querySelector('[data-testid="editor-content"]');
      const heading = editor?.querySelector('h1, h2, h3');
      return heading?.textContent === 'This is a heading';
    });

    await page1.close();
    await page2.close();
  });

  test('should measure and optimize sync latency', async () => {
    const page1 = await contexts[0].newPage();
    const page2 = await contexts[1].newPage();

    await loginUser(page1, users[0]);
    await loginUser(page2, users[1]);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);

    const latencies: number[] = [];

    // Measure latency for multiple edits
    for (let i = 0; i < 10; i++) {
      const testText = `Latency test ${i} - ${Date.now()}`;
      const latency = await measureSyncLatency(page1, page2, testText);
      latencies.push(latency);
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Calculate statistics
    const avgLatency = latencies.reduce((a, b) => a + b) / latencies.length;
    const maxLatency = Math.max(...latencies);
    const minLatency = Math.min(...latencies);

    console.log(`Sync Latency - Avg: ${avgLatency}ms, Min: ${minLatency}ms, Max: ${maxLatency}ms`);

    // Performance assertions
    expect(avgLatency).toBeLessThan(500); // Average under 500ms
    expect(maxLatency).toBeLessThan(1000); // Max under 1 second
    expect(minLatency).toBeGreaterThan(0); // Sanity check

    await page1.close();
    await page2.close();
  });

  test('should handle connection drops and reconnection', async () => {
    const page1 = await contexts[0].newPage();
    const page2 = await contexts[1].newPage();

    await loginUser(page1, users[0]);
    await loginUser(page2, users[1]);

    await page1.goto(`/projects/${project.id}/editor`);
    await page2.goto(`/projects/${project.id}/editor`);

    // Initial content
    await page1.fill('[data-testid="editor-content"]', 'Initial content before disconnect.');
    await waitForRealtimeUpdate(page2, 'Initial content before disconnect.');

    // Simulate connection drop for page2
    await page2.context().setOffline(true);
    
    // Page2 should show offline indicator
    await expect(page2.locator('[data-testid="connection-status-offline"]')).toBeVisible();

    // Page1 makes changes while page2 is offline
    await page1.click('[data-testid="editor-content"]');
    await page1.keyboard.press('End');
    await page1.type('[data-testid="editor-content"]', '\nChanges made while user 2 was offline.');

    // Page2 makes offline changes
    await page2.click('[data-testid="editor-content"]');
    await page2.keyboard.press('End');
    await page2.type('[data-testid="editor-content"]', '\nOffline changes from user 2.');

    // Reconnect page2
    await page2.context().setOffline(false);

    // Wait for reconnection and sync
    await expect(page2.locator('[data-testid="connection-status-online"]')).toBeVisible();
    
    // Both pages should have merged content
    await page1.waitForFunction(() => {
      const content = document.querySelector('[data-testid="editor-content"]')?.textContent || '';
      return content.includes('Offline changes from user 2');
    });

    await page2.waitForFunction(() => {
      const content = document.querySelector('[data-testid="editor-content"]')?.textContent || '';
      return content.includes('Changes made while user 2 was offline');
    });

    const finalContent1 = await page1.textContent('[data-testid="editor-content"]');
    const finalContent2 = await page2.textContent('[data-testid="editor-content"]');
    
    expect(finalContent1).toBe(finalContent2);
    expect(finalContent1).toContain('Initial content before disconnect');
    expect(finalContent1).toContain('Changes made while user 2 was offline');
    expect(finalContent1).toContain('Offline changes from user 2');

    await page1.close();
    await page2.close();
  });

  test('should sync project metadata changes', async () => {
    const ownerPage = await contexts[0].newPage();
    const collaboratorPage = await contexts[1].newPage();

    await loginUser(ownerPage, users[0]);
    await loginUser(collaboratorPage, users[1]);

    // Both navigate to project settings
    await ownerPage.goto(`/projects/${project.id}/settings`);
    await collaboratorPage.goto(`/projects/${project.id}/settings`);

    // Owner changes project title
    await ownerPage.fill('[data-testid="project-title-input"]', 'Updated Project Title');
    await ownerPage.click('[data-testid="save-settings-btn"]');

    // Collaborator should see the update
    await collaboratorPage.waitForFunction(() => {
      const titleInput = document.querySelector('[data-testid="project-title-input"]') as HTMLInputElement;
      return titleInput?.value === 'Updated Project Title';
    });

    // Owner changes description
    await ownerPage.fill('[data-testid="project-description-input"]', 'New description with more details');
    await ownerPage.click('[data-testid="save-settings-btn"]');

    // Verify sync
    await collaboratorPage.waitForFunction(() => {
      const descInput = document.querySelector('[data-testid="project-description-input"]') as HTMLInputElement;
      return descInput?.value === 'New description with more details';
    });

    await ownerPage.close();
    await collaboratorPage.close();
  });
});

// Helper functions
async function loginUser(page: Page, user: TestUser) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('[data-testid="login-btn"]');
  await page.waitForURL('/dashboard');
}

async function addCollaborator(projectId: string, ownerId: string, collaboratorEmail: string, role: string) {
  // This would typically make an API call to add collaborator
  // For testing, we'll simulate this through the database
  console.log(`Adding ${collaboratorEmail} as ${role} to project ${projectId}`);
}