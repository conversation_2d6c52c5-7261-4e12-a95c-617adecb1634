import { TIME_MS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

/**
 * Centralized animation timing and delay configurations
 * Ensures consistent timing across the application
 */

export const TIMING = {
  // Debounce delays for various inputs
  DEBOUNCE: {
    SEARCH: 300,        // Search input debounce
    INPUT: 500,         // General input fields
    SAVE: TIME_MS.SECOND,         // Auto-save operations
    SCROLL: 150,        // Scroll event handlers
    RESIZE: 200,        // Window resize handlers
    VALIDATION: 300,    // Form validation
  },
  
  // Animation durations
  ANIMATION: {
    INSTANT: 0,
    FAST: 150,          // Quick transitions
    NORMAL: 200,        // Standard animations
    MEDIUM: 300,        // Moderate animations
    SLOW: 500,          // Slower, more noticeable animations
    VERY_SLOW: 800,     // Dramatic animations
  },
  
  // Toast notification display times
  TOAST: {
    SUCCESS: TIME_MS.TYPING_TIMEOUT,      // Success messages
    ERROR: TIME_MS.TOAST_DURATION,        // Error messages (longer for reading)
    INFO: 4000,         // Information messages
    WARNING: 4500,      // Warning messages
    LOADING: 0,         // Indefinite (manually dismissed)
  },
  
  // Loading and timeout configurations
  LOADING: {
    MIN_DISPLAY: 300,   // Minimum time to show loading state
    SKELETON: 200,      // Delay before showing skeleton
    SPINNER: 500,       // Delay before showing spinner
  },
  
  // API and network timeouts
  TIMEOUT: {
    SHORT: TIME_MS.TOAST_DURATION,        // 5 seconds
    MEDIUM: 10000,      // 10 seconds
    LONG: 30000,        // 30 seconds
    UPLOAD: 120000,     // 2 minutes for file uploads
  },
  
  // Polling and refresh intervals
  POLLING: {
    FAST: TIME_MS.SECOND,         // Real-time updates
    NORMAL: TIME_MS.TOAST_DURATION,       // Standard polling
    SLOW: 30000,        // Background checks
    PRESENCE: TIME_MS.TYPING_TIMEOUT,     // User presence updates
  },
  
  // Retry delays with exponential backoff
  RETRY: {
    INITIAL: TIME_MS.SECOND,      // First retry delay
    MAX: 30000,         // Maximum retry delay
    MULTIPLIER: 2,      // Backoff multiplier
    JITTER: 0.1,        // Random jitter factor
  },
  
  // Transition and hover delays
  TRANSITION: {
    HOVER: 100,         // Hover state delay
    TOOLTIP: 500,       // Tooltip appearance delay
    DROPDOWN: 150,      // Dropdown menu delay
    MODAL: 200,         // Modal appearance
  },
  
  // Auto-dismiss timers
  AUTO_DISMISS: {
    BANNER: 10000,      // Announcement banners
    ALERT: SIZE_LIMITS.EMBEDDING_TEXT_LIMIT,        // Alert messages
    HINT: TIME_MS.TOAST_DURATION,         // Hint bubbles
  },
} as const

// Utility functions for common timing patterns
export const timing = {
  // Create a debounced function
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number = TIMING.DEBOUNCE.INPUT
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  },
  
  // Create a throttled function
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number = TIMING.DEBOUNCE.SCROLL
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  },
  
  // Calculate retry delay with exponential backoff
  getRetryDelay: (attempt: number): number => {
    const delay = Math.min(
      TIMING.RETRY.INITIAL * Math.pow(TIMING.RETRY.MULTIPLIER, attempt - 1),
      TIMING.RETRY.MAX
    )
    // Add jitter to prevent thundering herd
    const jitter = delay * TIMING.RETRY.JITTER * (Math.random() - 0.5) * 2
    return Math.round(delay + jitter)
  },
  
  // Sleep/delay utility
  sleep: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  },
  
  // Animate a value over time
  animate: (
    from: number,
    to: number,
    duration: number,
    onUpdate: (value: number) => void,
    easing: (t: number) => number = t => t // linear by default
  ): (() => void) => {
    const start = Date.now()
    let animationId: number
    
    const frame = () => {
      const elapsed = Date.now() - start
      const progress = Math.min(elapsed / duration, 1)
      const easedProgress = easing(progress)
      const value = from + (to - from) * easedProgress
      
      onUpdate(value)
      
      if (progress < 1) {
        animationId = requestAnimationFrame(frame)
      }
    }
    
    animationId = requestAnimationFrame(frame)
    
    // Return cancel function
    return () => cancelAnimationFrame(animationId)
  },
}

// Easing functions for animations
export const easing = {
  linear: (t: number) => t,
  easeInQuad: (t: number) => t * t,
  easeOutQuad: (t: number) => t * (2 - t),
  easeInOutQuad: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeInCubic: (t: number) => t * t * t,
  easeOutCubic: (t: number) => (--t) * t * t + 1,
  easeInOutCubic: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
}