# Form Validation Guide for BookScribe

This guide outlines the standardized form validation patterns used throughout BookScribe to ensure consistency, accessibility, and great user experience.

## Table of Contents
- [Core Principles](#core-principles)
- [Implementation Steps](#implementation-steps)
- [Validation Utilities](#validation-utilities)
- [Component Usage](#component-usage)
- [Common Patterns](#common-patterns)
- [Migration Guide](#migration-guide)

## Core Principles

1. **Validate on blur** - Provide immediate feedback when users leave a field
2. **Show errors only after touch** - Don't show errors until user has interacted
3. **Clear, actionable error messages** - Tell users exactly how to fix issues
4. **Required field indicators** - Always mark required fields with asterisks
5. **Accessible by default** - Proper ARIA attributes and keyboard navigation
6. **Consistent styling** - Use standardized error states and messages

## Implementation Steps

### 1. Define Your Schema

Use Zod schemas with our validation utilities:

```typescript
import { z } from 'zod';
import { 
  email, 
  password, 
  required, 
  title, 
  description 
} from '@/lib/validation/form-utils';

const myFormSchema = z.object({
  email: email(),
  password: password({ minLength: 10 }),
  title: title({ max: 100 }),
  description: description({ required: true, maxLength: 500 }),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms'
  })
});

type MyFormData = z.infer<typeof myFormSchema>;
```

### 2. Set Up Form State and Validation

```typescript
import { useState } from 'react';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useToast } from '@/hooks/use-toast';

function MyForm() {
  const { toast } = useToast();
  
  // Form state
  const [formData, setFormData] = useState<MyFormData>({
    email: '',
    password: '',
    title: '',
    description: '',
    acceptTerms: false
  });

  // Validation hook
  const validation = useFormValidation({
    schema: myFormSchema,
    validateOnBlur: true,
    validateOnChange: false, // Only for password/email fields
    debounceMs: 500
  });

  // Submit handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await validation.validateForm(formData);
    if (!result.isValid) return;

    try {
      // API call here
      await saveData(formData);
      
      toast({
        title: 'Success',
        description: 'Your data has been saved.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save data.',
        variant: 'destructive'
      });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields here */}
    </form>
  );
}
```

### 3. Use ValidatedFormField Components

```typescript
import { 
  ValidatedFormField, 
  RequiredFieldIndicator,
  FormSection,
  FormErrorSummary 
} from '@/components/ui/validated-form-field';

<form onSubmit={handleSubmit} className="space-y-6">
  <RequiredFieldIndicator />
  <FormErrorSummary errors={validation.errors} />
  
  <FormSection title="Account Information">
    <ValidatedFormField
      id="email"
      type="email"
      label="Email Address"
      value={formData.email}
      onChange={(value) => setFormData({ ...formData, email: value })}
      onBlur={() => validation.handleFieldBlur('email', formData.email, formData)}
      error={validation.getFieldError('email')}
      touched={validation.isFieldTouched('email')}
      required
      placeholder="<EMAIL>"
      description="We'll use this for notifications"
    />
    
    <ValidatedFormField
      id="password"
      type="password"
      label="Password"
      value={formData.password}
      onChange={(value) => {
        setFormData({ ...formData, password: value });
        // Enable onChange validation for passwords
        validation.handleFieldChange('password', value, { ...formData, password: value });
      }}
      onBlur={() => validation.handleFieldBlur('password', formData.password, formData)}
      error={validation.getFieldError('password')}
      touched={validation.isFieldTouched('password')}
      required
      placeholder="Create a secure password"
    />
  </FormSection>
  
  <Button type="submit" disabled={validation.isValidating}>
    {validation.isValidating ? (
      <>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Saving...
      </>
    ) : (
      'Save'
    )}
  </Button>
</form>
```

## Validation Utilities

### Common Field Validators

```typescript
// Required field
required('Field name')

// Email with custom message
email('Please enter your work email')

// Password with options
password({
  minLength: 12,
  requireSpecialChars: true
})

// Positive integers
positiveInt('Chapter count')

// Currency
currency({ min: 0.01, max: 9999.99 })

// Date ranges
date({ 
  min: new Date(), 
  max: addDays(new Date(), 30) 
})

// Tags array
tags({ min: 1, max: 5, maxLength: 20 })
```

### BookScribe-Specific Validators

```typescript
// Word count
wordCount({ min: 100, max: 10000 })

// Chapter number
chapterNumber()

// Genre selection
genre()

// Project status
projectStatus()
```

## Component Usage

### ValidatedFormField Props

- `id` - Unique field identifier
- `type` - 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select'
- `label` - Field label
- `value` - Current field value
- `onChange` - Value change handler
- `onBlur` - Blur event handler
- `error` - Error message (if any)
- `touched` - Whether field has been interacted with
- `required` - Whether field is required
- `disabled` - Whether field is disabled
- `placeholder` - Placeholder text
- `description` - Help text shown below label

### Select Field Example

```typescript
<ValidatedFormField
  id="genre"
  type="select"
  label="Genre"
  value={formData.genre}
  onChange={(value) => setFormData({ ...formData, genre: value })}
  onBlur={() => validation.handleFieldBlur('genre', formData.genre, formData)}
  error={validation.getFieldError('genre')}
  touched={validation.isFieldTouched('genre')}
  required
  options={[
    { value: 'fantasy', label: 'Fantasy' },
    { value: 'scifi', label: 'Science Fiction' },
    { value: 'mystery', label: 'Mystery' }
  ]}
/>
```

### Textarea with Character Count

```typescript
<ValidatedFormField
  id="description"
  type="textarea"
  label="Project Description"
  value={formData.description}
  onChange={(value) => setFormData({ ...formData, description: value })}
  onBlur={() => validation.handleFieldBlur('description', formData.description, formData)}
  error={validation.getFieldError('description')}
  touched={validation.isFieldTouched('description')}
  placeholder="Describe your project..."
  rows={4}
  maxLength={500}
  showCharacterCount
/>
```

## Common Patterns

### Conditional Fields

```typescript
{formData.projectType === 'series' && (
  <ValidatedFormField
    id="seriesName"
    type="text"
    label="Series Name"
    value={formData.seriesName || ''}
    onChange={(value) => setFormData({ ...formData, seriesName: value })}
    onBlur={() => validation.handleFieldBlur('seriesName', formData.seriesName, formData)}
    error={validation.getFieldError('seriesName')}
    touched={validation.isFieldTouched('seriesName')}
    required={formData.projectType === 'series'}
  />
)}
```

### Multi-Step Forms

```typescript
const [currentStep, setCurrentStep] = useState(1);

const validateStep = async (step: number) => {
  const stepFields = {
    1: ['email', 'password'],
    2: ['title', 'description'],
    3: ['genre', 'targetWords']
  };
  
  const fieldsToValidate = stepFields[step];
  const errors = [];
  
  for (const field of fieldsToValidate) {
    if (validation.hasFieldError(field)) {
      errors.push(field);
    }
  }
  
  return errors.length === 0;
};
```

### File Upload Fields

```typescript
<div className="space-y-2">
  <Label htmlFor="cover-image">Cover Image</Label>
  <Input
    id="cover-image"
    type="file"
    accept="image/*"
    onChange={(e) => {
      const file = e.target.files?.[0];
      if (file) {
        // Validate file
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          validation.setFieldError('coverImage', 'File must be less than 5MB');
        } else {
          setFormData({ ...formData, coverImage: file });
        }
      }
    }}
    disabled={loading}
  />
  {validation.getFieldError('coverImage') && (
    <p className="text-sm text-destructive mt-1">
      {validation.getFieldError('coverImage')}
    </p>
  )}
</div>
```

## Migration Guide

### Before (Old Pattern)

```typescript
// Inline validation
const [errors, setErrors] = useState({});

const handleSubmit = (e) => {
  e.preventDefault();
  
  const newErrors = {};
  if (!formData.email) {
    newErrors.email = 'Email is required';
  }
  if (!formData.password) {
    newErrors.password = 'Password is required';
  }
  
  setErrors(newErrors);
  if (Object.keys(newErrors).length > 0) return;
  
  // Submit...
};

// Basic input
<div>
  <Label htmlFor="email">Email</Label>
  <Input
    id="email"
    type="email"
    value={formData.email}
    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
  />
  {errors.email && <p className="text-red-500">{errors.email}</p>}
</div>
```

### After (New Pattern)

```typescript
// Schema-based validation
const schema = z.object({
  email: email(),
  password: password()
});

const validation = useFormValidation({
  schema,
  validateOnBlur: true
});

const handleSubmit = async (e) => {
  e.preventDefault();
  
  const result = await validation.validateForm(formData);
  if (!result.isValid) return;
  
  // Submit...
};

// ValidatedFormField
<ValidatedFormField
  id="email"
  type="email"
  label="Email"
  value={formData.email}
  onChange={(value) => setFormData({ ...formData, email: value })}
  onBlur={() => validation.handleFieldBlur('email', formData.email, formData)}
  error={validation.getFieldError('email')}
  touched={validation.isFieldTouched('email')}
  required
/>
```

## Accessibility Checklist

- [ ] All form fields have associated labels
- [ ] Required fields are marked with asterisks and aria-required
- [ ] Error messages have role="alert" and aria-live="polite"
- [ ] Form has error summary at the top
- [ ] Fields have proper aria-describedby for descriptions and errors
- [ ] Focus management works correctly
- [ ] Keyboard navigation is fully supported
- [ ] Screen reader announcements are clear

## Testing Forms

```typescript
// Example test
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

test('shows validation errors on blur', async () => {
  render(<MyForm />);
  
  const emailInput = screen.getByLabelText('Email');
  await userEvent.click(emailInput);
  await userEvent.tab(); // Blur
  
  await waitFor(() => {
    expect(screen.getByText('Email is required')).toBeInTheDocument();
  });
});

test('submits form with valid data', async () => {
  render(<MyForm />);
  
  await userEvent.type(screen.getByLabelText('Email'), '<EMAIL>');
  await userEvent.type(screen.getByLabelText('Password'), 'SecurePass123!');
  await userEvent.click(screen.getByRole('button', { name: 'Submit' }));
  
  await waitFor(() => {
    expect(screen.getByText('Success')).toBeInTheDocument();
  });
});
```

## Summary

By following these patterns, all forms in BookScribe will:
- Provide consistent user experience
- Be fully accessible
- Have proper validation and error handling
- Show helpful error messages
- Work well on all devices
- Be easy to test and maintain