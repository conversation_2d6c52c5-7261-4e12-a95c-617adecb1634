'use client'

import { useEffect, useCallback, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { TrendingUp } from 'lucide-react'
import { useDebounce } from '@/hooks/use-debounce'

interface SearchAnalyticsProps {
  projectId: string
  userId: string
}

interface SearchEvent {
  query: string
  resultCount: number
  clickedResult?: {
    id: string
    type: string
    position: number
  }
  timestamp: Date
}

export function useSearchAnalytics(projectId: string) {
  const trackSearch = useCallback(async (event: Omit<SearchEvent, 'timestamp'>) => {
    try {
      await fetch('/api/analytics/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          ...event,
          timestamp: new Date()
        })
      })
    } catch (error) {
      console.error('Failed to track search event:', error)
    }
  }, [projectId])

  const trackSearchQuery = useCallback((query: string, resultCount: number) => {
    if (query.trim()) {
      trackSearch({ query, resultCount })
    }
  }, [trackSearch])

  const trackResultClick = useCallback((query: string, result: { id: string; type: string }, position: number) => {
    trackSearch({
      query,
      resultCount: 0,
      clickedResult: {
        id: result.id,
        type: result.type,
        position
      }
    })
  }, [trackSearch])

  return {
    trackSearchQuery,
    trackResultClick
  }
}

// Popular searches component
export function PopularSearches({ 
  projectId, 
  onSearchSelect 
}: { 
  projectId: string
  onSearchSelect: (query: string) => void 
}) {
  const [popularSearches, setPopularSearches] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchPopularSearches = async () => {
      try {
        const response = await fetch(`/api/analytics/search/popular?projectId=${projectId}`)
        if (response.ok) {
          const data = await response.json()
          setPopularSearches(data.data.searches || [])
        }
      } catch (error) {
        console.error('Failed to fetch popular searches:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPopularSearches()
  }, [projectId])

  if (isLoading || popularSearches.length === 0) {
    return null
  }

  return (
    <div className="space-y-2">
      <p className="text-sm font-medium text-muted-foreground">Popular searches</p>
      <div className="flex flex-wrap gap-2">
        {popularSearches.map((search, idx) => (
          <Button
            key={idx}
            variant="outline"
            size="sm"
            onClick={() => onSearchSelect(search)}
            className="text-xs"
          >
            <TrendingUp className="w-3 h-3 mr-1" />
            {search}
          </Button>
        ))}
      </div>
    </div>
  )
}