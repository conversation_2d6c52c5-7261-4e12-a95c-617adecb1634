# Components Needing Proper Loading/Empty States

Based on the codebase analysis, here are React components that fetch data but lack proper loading states, empty states, or both:

## Components with Basic/Missing Loading States

### 1. **src/components/admin/ai-usage-stats.tsx**
- **Current**: Basic text "Loading AI usage data..." (line 155)
- **Missing**: Proper skeleton loader, empty state when no data
- **Data fetched**: AI usage logs from Supabase

### 2. **src/components/admin/users-management.tsx**
- **Current**: Basic text "Loading users..." (line 184)
- **Missing**: Proper skeleton loader for table, empty state when no users
- **Data fetched**: User data from Supabase

### 3. **src/components/admin/system-health.tsx**
- **Current**: No visible loading state during initial load
- **Missing**: Loading skeletons for metric cards, empty states
- **Data fetched**: System health metrics from multiple endpoints

### 4. **src/components/admin/subscriptions-overview.tsx**
- **Needs checking**: Likely similar issues as other admin components
- **Data fetched**: Subscription data from Supabase

### 5. **src/components/analytics/writing-analytics-dashboard.tsx**
- **Current**: Basic spinner with text (lines 209-215)
- **Missing**: Skeleton loaders for charts and stats cards
- **Data fetched**: Writing statistics and analytics

### 6. **src/components/series/series-character-map.tsx**
- **Current**: Basic spinner animation (lines 154-161)
- **Missing**: Skeleton loaders for character cards, proper empty state
- **Data fetched**: Character data across series

### 7. **src/components/voice/voice-trainer-enhanced.tsx**
- **Needs checking**: Voice training data loading states
- **Data fetched**: Voice profiles and training data

### 8. **src/components/profile/profile-form.tsx**
- **Needs checking**: Profile data loading and empty states
- **Data fetched**: User profile information

### 9. **src/components/wizard/unified-project-wizard.tsx**
- **Needs checking**: Template and project data loading
- **Data fetched**: Project templates and user data

### 10. **src/components/onboarding/progress-tracker.tsx**
- **Needs checking**: Onboarding progress data
- **Data fetched**: User onboarding status

## Components with Good Loading/Empty States (for reference)

### ✅ **src/components/projects/projects-list.tsx**
- Uses `LoadingSkeleton` component
- Has proper empty state with `EmptyState` component
- Good example to follow

### ✅ **src/components/editor/knowledge-base-editor.tsx**
- Has loading state with proper UI
- Has empty state with helpful actions
- Comprehensive error handling

## Recommended Actions

1. **Create reusable loading skeletons** for common patterns:
   - Table skeleton (for admin tables)
   - Chart skeleton (for analytics)
   - Card grid skeleton (for dashboards)
   - Metric card skeleton (for stats)

2. **Use existing components**:
   - `LoadingSkeleton` from `@/components/ui/loading-skeleton`
   - `EmptyState` from `@/components/ui/empty-state`
   - `Skeleton` from `@/components/ui/skeleton`

3. **Standard pattern to implement**:
   ```tsx
   if (loading) {
     return <LoadingSkeleton variant="table" rows={5} />
   }
   
   if (!data || data.length === 0) {
     return (
       <EmptyState
         icon={IconComponent}
         title="No data found"
         description="Get started by adding your first item"
         actions={[
           { label: 'Add Item', onClick: handleAdd }
         ]}
       />
     )
   }
   ```

4. **Priority components to fix**:
   - Admin dashboard components (high visibility)
   - Analytics dashboard (complex UI needs proper loading)
   - Series character map (important feature)