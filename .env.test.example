# Test Environment Variables
# Copy this to .env.test and fill in your test values

# Playwright Configuration
PLAYWRIGHT_BASE_URL=http://localhost:3001

# Test Database (use a separate database for tests)
NEXT_PUBLIC_SUPABASE_URL=your_test_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_test_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_test_supabase_service_role_key

# Test Authentication
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!

# CI/CD
CI=false