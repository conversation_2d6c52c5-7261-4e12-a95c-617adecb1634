#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Migrating from SendGrid to Maileroo...\n');

// Files to update
const filesToUpdate = [
  'src/app/api/user/export-data/route.ts',
  'src/app/api/auth/reset-password/route.ts',
  'src/app/api/auth/verify-email/route.ts',
  'src/app/api/achievements/check/route.ts',
  'src/lib/services/task-queue-service.ts',
];

// Update imports
function updateImports(content) {
  // Replace SendGrid imports
  content = content.replace(
    /import\s*{\s*emailService\s*}\s*from\s*['"]@\/lib\/services\/email-service['"]/g,
    "import { mailerooEmailService as emailService } from '@/lib/services/maileroo-email-service'"
  );
  
  content = content.replace(
    /import\s*{\s*emailService\s*}\s*from\s*['"]\.\.\/email-service['"]/g,
    "import { mailerooEmailService as emailService } from '../maileroo-email-service'"
  );

  content = content.replace(
    /import\s*{\s*emailService\s*}\s*from\s*['"]\.\/email-service['"]/g,
    "import { mailerooEmailService as emailService } from './maileroo-email-service'"
  );

  // Update any direct EmailService imports
  content = content.replace(
    /import\s*{\s*EmailService\s*}\s*from\s*['"]@\/lib\/services['"]/g,
    "import { MailerooEmailService } from '@/lib/services'"
  );

  return content;
}

// Process each file
let updatedCount = 0;
let errorCount = 0;

for (const filePath of filesToUpdate) {
  const fullPath = path.join(process.cwd(), filePath);
  
  try {
    if (fs.existsSync(fullPath)) {
      let content = fs.readFileSync(fullPath, 'utf8');
      const originalContent = content;
      
      // Update imports
      content = updateImports(content);
      
      // Check if file was actually modified
      if (content !== originalContent) {
        fs.writeFileSync(fullPath, content);
        console.log(`✅ Updated: ${filePath}`);
        updatedCount++;
      } else {
        console.log(`⏭️  Skipped: ${filePath} (no changes needed)`);
      }
    } else {
      console.log(`❌ Not found: ${filePath}`);
      errorCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    errorCount++;
  }
}

// Update environment variables template
const envExamplePath = path.join(process.cwd(), '.env.example');
if (fs.existsSync(envExamplePath)) {
  let envContent = fs.readFileSync(envExamplePath, 'utf8');
  
  // Comment out SendGrid
  envContent = envContent.replace(
    /^SENDGRID_API_KEY=/gm,
    '# SENDGRID_API_KEY='
  );
  
  // Add Maileroo if not present
  if (!envContent.includes('MAILEROO_API_KEY')) {
    envContent += `
# Maileroo (Email Service)
MAILEROO_API_KEY=your_maileroo_api_key
MAILEROO_API_URL=https://api.maileroo.com/v1
MAILEROO_FROM_EMAIL=<EMAIL>
MAILEROO_FROM_NAME=BookScribe AI
`;
  }
  
  fs.writeFileSync(envExamplePath, envContent);
  console.log('✅ Updated .env.example with Maileroo configuration');
}

// Create email queue processor if it doesn't exist
const processorPath = path.join(process.cwd(), 'src/app/api/cron/process-email-queue/route.ts');
if (!fs.existsSync(processorPath)) {
  const processorDir = path.dirname(processorPath);
  if (!fs.existsSync(processorDir)) {
    fs.mkdirSync(processorDir, { recursive: true });
  }
  
  const processorContent = `import { NextRequest, NextResponse } from 'next/server'
import { mailerooEmailService } from '@/lib/services'

// This endpoint should be called by a cron job (e.g., Vercel Cron or external service)
// Run every 5 minutes: */5 * * * *
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret (optional but recommended)
    const authHeader = request.headers.get('authorization')
    if (authHeader !== \`Bearer \${process.env.CRON_SECRET}\`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Process email queue
    await mailerooEmailService.processEmailQueue()

    return NextResponse.json({ 
      success: true, 
      message: 'Email queue processed',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error processing email queue:', error)
    return NextResponse.json(
      { error: 'Failed to process email queue' },
      { status: 500 }
    )
  }
}
`;
  
  fs.writeFileSync(processorPath, processorContent);
  console.log('✅ Created email queue processor endpoint');
}

// Summary
console.log('\n📊 Migration Summary:');
console.log(`   Files updated: ${updatedCount}`);
console.log(`   Errors: ${errorCount}`);

console.log('\n📝 Next Steps:');
console.log('1. Add Maileroo credentials to your .env.local:');
console.log('   MAILEROO_API_KEY=your_api_key');
console.log('   MAILEROO_API_URL=https://api.maileroo.com/v1');
console.log('   MAILEROO_FROM_EMAIL=<EMAIL>');
console.log('   MAILEROO_FROM_NAME=BookScribe AI');
console.log('   CRON_SECRET=your_cron_secret');
console.log('\n2. Set up a cron job to call /api/cron/process-email-queue every 5 minutes');
console.log('3. Configure Maileroo webhooks for email tracking (optional)');
console.log('4. Test email sending with the new service');
console.log('\n✅ Migration script completed!');