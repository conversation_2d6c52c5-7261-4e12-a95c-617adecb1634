{"timestamp": "2025-08-01T23:15:27.262Z", "totalFiles": 13, "alreadyUnified": 1, "needsUpdate": 12, "errors": 0, "files": [{"file": "src\\app\\(dashboard)\\achievements\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\analytics\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\billing\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\dashboard\\loading.tsx", "status": "already-unified", "imports": ["unified-loading"]}, {"file": "src\\app\\(dashboard)\\goals\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\projects\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\projects\\[id]\\write\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\series\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\settings\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(dashboard)\\universes\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\(marketing)\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}, {"file": "src\\app\\loading.tsx", "status": "needs-update", "imports": ["Skeleton"], "recommendation": "Convert to unified loading components"}]}