import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Lightbulb } from 'lucide-react'
import { StepComponentProps } from './wizard-types'
import { SPACING } from '@/lib/config/ui-config'

export function StepBasics({ formData, updateFormData, mode }: StepComponentProps) {
  return (
    <div className={SPACING.SPACE_Y.MD}>
      <div>
        <Label htmlFor="title" className="text-base font-semibold">
          Story Title
        </Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => updateFormData('title', e.target.value)}
          placeholder="Enter your story title"
          className="mt-2"
          disabled={mode === 'demo'}
        />
      </div>
      
      <div>
        <Label htmlFor="description" className="text-base font-semibold">
          Story Description
        </Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => updateFormData('description', e.target.value)}
          placeholder="Describe your story in a few sentences..."
          className="mt-2 min-h-[120px]"
          disabled={mode === 'demo'}
        />
        {formData.description && (
          <p className="text-sm text-muted-foreground mt-1">
            {formData.description.length}/500 characters
          </p>
        )}
      </div>
      
      {mode === 'demo' && (
        <Card className="border-dashed border-primary/30 bg-primary/5">
          <CardContent className={SPACING.PADDING.SM}>
            <div className="flex items-start gap-2">
              <Lightbulb className="h-4 w-4 text-primary mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-primary">Demo Mode</p>
                <p className="text-muted-foreground">
                  In demo mode, you can explore the wizard but won't be able to generate a real project.
                  Sign up to create your own stories!
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}