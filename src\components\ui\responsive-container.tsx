import { cn } from '@/lib/utils'
import { HTMLAttributes, forwardRef } from 'react'

interface ResponsiveContainerProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * Container variant
   * - 'default': Standard centered container with responsive padding
   * - 'wide': Wider container for better use of large screens
   * - 'full': Full width with edge padding
   */
  variant?: 'default' | 'wide' | 'full'
  /**
   * Max width constraint
   * - 'sm': Max 640px
   * - 'md': Max 768px
   * - 'lg': Max 1024px
   * - 'xl': Max 1280px
   * - '2xl': Max 1536px
   * - '3xl': Max 1920px
   * - '4xl': Max 2560px
   * - 'none': No max width
   */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | 'none'
  /**
   * Padding size
   * - 'none': No padding
   * - 'sm': Small responsive padding
   * - 'md': Medium responsive padding
   * - 'lg': Large responsive padding
   */
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export const ResponsiveContainer = forwardRef<HTMLDivElement, ResponsiveContainerProps>(
  ({ className, variant = 'default', maxWidth = '2xl', padding = 'md', children, ...props }, ref) => {
    const variants = {
      default: 'container',
      wide: 'container-wide',
      full: 'container-full'
    }
    
    const maxWidthClasses = {
      sm: 'max-w-screen-sm',
      md: 'max-w-screen-md',
      lg: 'max-w-screen-lg',
      xl: 'max-w-screen-xl',
      '2xl': 'max-w-screen-2xl',
      '3xl': 'max-w-[1920px]',
      '4xl': 'max-w-[2560px]',
      none: ''
    }
    
    const paddingClasses = {
      none: '',
      sm: 'px-3 sm:px-4 lg:px-6 xl:px-8',
      md: 'px-4 sm:px-6 lg:px-8 xl:px-10 2xl:px-12',
      lg: 'px-6 sm:px-8 lg:px-10 xl:px-12 2xl:px-16'
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          variants[variant],
          maxWidth !== 'none' && maxWidthClasses[maxWidth],
          paddingClasses[padding],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

ResponsiveContainer.displayName = 'ResponsiveContainer'

// Utility wrapper for main content areas
export const MainContent = forwardRef<HTMLDivElement, ResponsiveContainerProps>(
  ({ className, ...props }, ref) => {
    return (
      <ResponsiveContainer
        ref={ref}
        variant="wide"
        maxWidth="4xl"
        padding="md"
        className={cn('py-6 sm:py-8 lg:py-10', className)}
        {...props}
      />
    )
  }
)

MainContent.displayName = 'MainContent'

// Utility wrapper for form/modal content
export const FormContainer = forwardRef<HTMLDivElement, ResponsiveContainerProps>(
  ({ className, ...props }, ref) => {
    return (
      <ResponsiveContainer
        ref={ref}
        variant="default"
        maxWidth="2xl"
        padding="md"
        className={cn('py-4 sm:py-6', className)}
        {...props}
      />
    )
  }
)

FormContainer.displayName = 'FormContainer'