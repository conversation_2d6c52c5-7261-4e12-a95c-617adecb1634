'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  LineChart, 
  Line, 
  AreaChart,
  Area,
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts'
import { format, subDays, startOfDay } from 'date-fns'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { formatNumber } from '@/lib/utils'

interface MemoryUsageData {
  date: string
  totalTokens: number
  storyContent: number
  characters: number
  worldBuilding: number
  plotTimeline: number
  metadata: number
}

interface MemoryUsageChartProps {
  projectId: string
  className?: string
}

export function MemoryUsageChart({ projectId, className }: MemoryUsageChartProps) {
  const [data, setData] = useState<MemoryUsageData[]>([])
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchUsageData = async () => {
      setIsLoading(true)
      try {
        const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90
        const response = await fetch(
          `/api/memory/usage-history?projectId=${projectId}&days=${days}`
        )
        
        if (response.ok) {
          const result = await response.json()
          setData(result.data || generateMockData(days))
        } else {
          // Use mock data if API fails
          setData(generateMockData(days))
        }
      } catch (error) {
        console.error('Failed to fetch usage data:', error)
        // Use mock data on error
        const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90
        setData(generateMockData(days))
      } finally {
        setIsLoading(false)
      }
    }

    fetchUsageData()
  }, [projectId, timeRange])

  // Generate mock data for demonstration
  const generateMockData = (days: number): MemoryUsageData[] => {
    const data: MemoryUsageData[] = []
    const baseTokens = 50000
    
    for (let i = days - 1; i >= 0; i--) {
      const date = startOfDay(subDays(new Date(), i))
      const growth = Math.random() * 2000 - 500 // Random growth/shrink
      const totalTokens = Math.max(
        baseTokens + (days - i) * 1000 + growth,
        baseTokens
      )
      
      data.push({
        date: format(date, 'MMM dd'),
        totalTokens: Math.round(totalTokens),
        storyContent: Math.round(totalTokens * 0.4),
        characters: Math.round(totalTokens * 0.25),
        worldBuilding: Math.round(totalTokens * 0.15),
        plotTimeline: Math.round(totalTokens * 0.12),
        metadata: Math.round(totalTokens * 0.08)
      })
    }
    
    return data
  }

  const calculateTrend = () => {
    if (data.length < 2) return { value: 0, direction: 'neutral' }
    
    const latest = data[data.length - 1].totalTokens
    const previous = data[0].totalTokens
    const change = ((latest - previous) / previous) * 100
    
    return {
      value: Math.abs(change),
      direction: change > 5 ? 'up' : change < -5 ? 'down' : 'neutral'
    }
  }

  const trend = calculateTrend()

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background/95 backdrop-blur border rounded-lg shadow-lg p-3">
          <p className="font-medium">{label}</p>
          <div className="space-y-1 mt-2">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center justify-between gap-4 text-sm">
                <span style={{ color: entry.color }}>{entry.name}</span>
                <span className="font-medium">{formatNumber(entry.value)}</span>
              </div>
            ))}
          </div>
        </div>
      )
    }
    return null
  }

  if (isLoading) {
    return (
      <div className="h-64 flex items-center justify-center">
        <p className="text-muted-foreground">Loading usage data...</p>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">Token Usage Over Time</h4>
          <Badge variant="outline" className="flex items-center gap-1">
            {trend.direction === 'up' && <TrendingUp className="h-3 w-3 text-red-600" />}
            {trend.direction === 'down' && <TrendingDown className="h-3 w-3 text-green-600" />}
            {trend.direction === 'neutral' && <Minus className="h-3 w-3" />}
            <span>{trend.value.toFixed(1)}%</span>
          </Badge>
        </div>
        
        <Select value={timeRange} onValueChange={(value: '7d' | '30d' | '90d') => setTimeRange(value)}>
          <SelectTrigger className="w-24">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">7 days</SelectItem>
            <SelectItem value="30d">30 days</SelectItem>
            <SelectItem value="90d">90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data}>
            <defs>
              <linearGradient id="colorTotal" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="totalTokens"
              stroke="#3b82f6"
              fillOpacity={1}
              fill="url(#colorTotal)"
              strokeWidth={2}
              name="Total Tokens"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Breakdown by Category */}
      <Card>
        <CardContent className="pt-6">
          <h4 className="text-sm font-medium mb-4">Memory Distribution Trend</h4>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.slice(-7)}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="date" 
                  className="text-xs"
                  tick={{ fill: 'currentColor' }}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fill: 'currentColor' }}
                  tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend 
                  wrapperStyle={{ fontSize: '12px' }}
                  iconType="rect"
                />
                <Bar dataKey="storyContent" stackId="a" fill="#3b82f6" name="Story" />
                <Bar dataKey="characters" stackId="a" fill="#8b5cf6" name="Characters" />
                <Bar dataKey="worldBuilding" stackId="a" fill="#10b981" name="World" />
                <Bar dataKey="plotTimeline" stackId="a" fill="#f59e0b" name="Plot" />
                <Bar dataKey="metadata" stackId="a" fill="#6b7280" name="Meta" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}