# BookScribe Reference Materials System

## Overview

The Reference Materials System allows authors to upload, organize, and utilize research materials, images, documents, and other reference content within their writing projects. It includes AI-powered summarization, tagging, and integration with the writing process to ensure easy access to research during content creation.

## Architecture

### Database Schema

#### Reference Materials Table
Core storage for reference content:

```sql
reference_materials:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - user_id: UUID - References auth.users
  - title: VARCHAR(255) - Material title
  - description: TEXT - Material description
  - material_type: VARCHAR(50) - document, image, video, audio, link, note
  - file_url: TEXT - Storage URL for uploaded files
  - file_name: VARCHAR(255) - Original filename
  - file_size: BIGINT - File size in bytes
  - mime_type: VARCHAR(100) - File MIME type
  - content_text: TEXT - Extracted/transcribed text
  - summary: TEXT - AI-generated summary
  - tags: TEXT[] - Searchable tags
  - metadata: JSONB - Type-specific metadata
  - source_url: TEXT - Original source URL
  - author: <PERSON><PERSON><PERSON><PERSON>(255) - Material author/creator
  - publication_date: DATE - When material was published
  - relevance_score: FLOAT - AI-calculated relevance (0-1)
  - usage_count: INTEGER - Times referenced in project
  - is_pinned: BOOLEAN - Pin to quick access
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Reference Categories Table
Organize materials into categories:

```sql
reference_categories:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - name: VARCHAR(100) - Category name
  - description: TEXT
  - color: VARCHAR(7) - Hex color code
  - icon: VARCHAR(50) - Icon identifier
  - parent_category_id: UUID - For nested categories
  - sort_order: INTEGER
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Reference Material Categories Junction
Many-to-many relationship:

```sql
reference_material_categories:
  - material_id: UUID - References reference_materials
  - category_id: UUID - References reference_categories
  - PRIMARY KEY (material_id, category_id)
```

#### Reference Usage Table
Track where references are used:

```sql
reference_usage:
  - id: UUID (Primary Key)
  - material_id: UUID - References reference_materials
  - project_id: UUID - References projects
  - chapter_id: UUID - References chapters
  - character_id: UUID - References characters
  - usage_type: VARCHAR(50) - citation, inspiration, fact_check
  - usage_context: TEXT - How it was used
  - created_at: TIMESTAMPTZ
```

## Reference Types

### 1. Documents
Research papers, articles, PDFs:

```typescript
interface DocumentReference {
  material_type: 'document';
  metadata: {
    page_count: number;
    word_count: number;
    language: string;
    document_type: 'pdf' | 'docx' | 'txt' | 'epub';
    extracted_sections: {
      title: string;
      content: string;
      page_number?: number;
    }[];
    key_points: string[];
    citations: Citation[];
  };
}
```

### 2. Images
Visual references, maps, character art:

```typescript
interface ImageReference {
  material_type: 'image';
  metadata: {
    dimensions: { width: number; height: number };
    format: string;
    color_palette: string[];
    detected_objects: string[];
    text_in_image: string;
    location_data?: {
      latitude: number;
      longitude: number;
    };
    ai_description: string;
  };
}
```

### 3. Web Links
Online articles, resources:

```typescript
interface WebReference {
  material_type: 'link';
  metadata: {
    url: string;
    domain: string;
    scraped_content: string;
    last_accessed: Date;
    page_title: string;
    meta_description: string;
    main_content: string;
    publication_date?: Date;
    author?: string;
  };
}
```

### 4. Notes
Quick research notes:

```typescript
interface NoteReference {
  material_type: 'note';
  metadata: {
    note_type: 'research' | 'idea' | 'quote' | 'observation';
    related_materials: string[]; // IDs of related references
    formatted_content: string; // Markdown content
  };
}
```

### 5. Audio/Video
Interviews, documentaries:

```typescript
interface MediaReference {
  material_type: 'audio' | 'video';
  metadata: {
    duration: number; // seconds
    transcript: string;
    timestamps: {
      time: number;
      text: string;
      speaker?: string;
    }[];
    key_moments: {
      time: number;
      description: string;
      tags: string[];
    }[];
  };
}
```

## API Endpoints

### Reference Management

#### GET /api/references
List project references:

```typescript
// Request
GET /api/references?project_id=uuid&material_type=document&tags=history

// Response
{
  references: [
    {
      id: "uuid",
      title: "Medieval Castle Architecture",
      material_type: "document",
      file_name: "castle_architecture.pdf",
      summary: "Comprehensive guide to castle construction...",
      tags: ["history", "architecture", "medieval"],
      relevance_score: 0.92,
      usage_count: 5,
      is_pinned: true,
      categories: ["World Building", "Architecture"]
    }
  ],
  total: 45,
  categories: [
    {
      id: "uuid",
      name: "World Building",
      count: 12
    }
  ]
}
```

#### POST /api/references
Upload new reference:

```typescript
// Request (multipart/form-data)
{
  file: File,
  project_id: "uuid",
  title: "Medieval Castle Architecture",
  description: "Reference for castle descriptions",
  tags: ["history", "architecture"],
  category_ids: ["uuid1", "uuid2"]
}

// Response
{
  reference: {
    id: "uuid",
    file_url: "https://storage.../file.pdf",
    processing_status: "extracting_text",
    estimated_completion: "2024-01-15T10:35:00Z"
  }
}
```

#### POST /api/references/{id}/summarize
Generate AI summary:

```typescript
// Request
{
  summary_type: "key_points" | "detailed" | "chapter_specific",
  context: {
    chapter_id?: "uuid",
    focus_areas?: ["architecture", "daily_life"]
  }
}

// Response
{
  summary: {
    main_points: [
      "Castles served both defensive and residential purposes",
      "Construction typically took 5-10 years"
    ],
    relevant_quotes: [
      {
        text: "The keep was the heart of the castle...",
        page: 45
      }
    ],
    suggested_applications: [
      "Use for Chapter 7 castle arrival scene",
      "Reference for siege in Chapter 15"
    ]
  }
}
```

### Reference Usage

#### POST /api/references/{id}/usage
Track reference usage:

```typescript
// Request
{
  chapter_id: "uuid",
  usage_type: "citation",
  usage_context: "Used for castle layout in arrival scene"
}
```

#### GET /api/references/{id}/usage
Get usage history:

```typescript
// Response
{
  usage_history: [
    {
      chapter: "Chapter 7: Arrival",
      usage_type: "inspiration",
      usage_context: "Castle exterior description",
      used_at: "2024-01-10T14:30:00Z"
    }
  ],
  related_content: [
    {
      type: "chapter",
      id: "uuid",
      title: "Chapter 7: Arrival",
      relevance: 0.85
    }
  ]
}
```

### Reference Categories

#### POST /api/references/categories
Create category:

```typescript
// Request
{
  project_id: "uuid",
  name: "Historical Research",
  color: "#8B4513",
  icon: "book-open"
}
```

#### PUT /api/references/{id}/categories
Update reference categories:

```typescript
// Request
{
  category_ids: ["uuid1", "uuid2", "uuid3"]
}
```

## Content Processing

### Text Extraction
```typescript
class ReferenceProcessor {
  async processDocument(file: File): Promise<ProcessedContent> {
    const extractor = this.getExtractor(file.type);
    
    // Extract text content
    const rawText = await extractor.extract(file);
    
    // Process extracted text
    const processed = await this.processText(rawText);
    
    return {
      content_text: processed.text,
      metadata: {
        page_count: processed.pages,
        word_count: processed.words,
        key_sections: processed.sections,
        detected_language: processed.language
      }
    };
  }
  
  private async processText(text: string) {
    return {
      text: this.cleanText(text),
      sections: this.extractSections(text),
      keywords: await this.extractKeywords(text),
      entities: await this.extractEntities(text)
    };
  }
}
```

### AI Summarization
```typescript
class ReferenceSummarizer {
  async summarize(
    content: string, 
    options: SummarizationOptions
  ): Promise<Summary> {
    const prompt = this.buildPrompt(content, options);
    
    const summary = await ai.complete({
      prompt,
      model: 'gpt-4',
      temperature: 0.3
    });
    
    return {
      main_points: this.extractPoints(summary),
      key_quotes: this.extractQuotes(content, summary),
      relevance_analysis: this.analyzeRelevance(summary, options.context)
    };
  }
}
```

### Image Analysis
```typescript
class ImageAnalyzer {
  async analyze(imageUrl: string): Promise<ImageAnalysis> {
    // AI vision analysis
    const vision = await ai.vision({
      image_url: imageUrl,
      tasks: ['describe', 'detect_objects', 'extract_text', 'analyze_style']
    });
    
    // Color extraction
    const colors = await this.extractColorPalette(imageUrl);
    
    return {
      description: vision.description,
      detected_objects: vision.objects,
      text_in_image: vision.text,
      style_analysis: vision.style,
      color_palette: colors,
      suggested_uses: this.suggestUses(vision)
    };
  }
}
```

## UI Components

### Reference Library
```tsx
<ReferenceLibrary
  projectId={projectId}
  viewMode="grid" | "list" | "categories"
  onReferenceSelect={handleSelect}
  allowUpload={true}
  searchEnabled={true}
/>
```

### Reference Upload
```tsx
<ReferenceUpload
  projectId={projectId}
  acceptedTypes={['image/*', 'application/pdf', '.docx']}
  maxSize={50 * 1024 * 1024} // 50MB
  onUpload={handleUpload}
  showProgress={true}
/>
```

### Reference Viewer
```tsx
<ReferenceViewer
  reference={selectedReference}
  showSummary={true}
  showUsageHistory={true}
  allowAnnotations={true}
  onAnnotate={handleAnnotation}
/>
```

### Quick Reference Panel
```tsx
<QuickReferencePanel
  projectId={projectId}
  pinnedReferences={pinnedRefs}
  recentReferences={recentRefs}
  position="right"
  collapsible={true}
/>
```

### Reference Search
```tsx
<ReferenceSearch
  projectId={projectId}
  searchIn={['title', 'content', 'tags', 'summary']}
  filters={{
    types: ['document', 'image'],
    categories: selectedCategories,
    dateRange: lastMonth
  }}
  onResultSelect={handleSelect}
/>
```

## Integration Features

### Writing Integration
```typescript
// Insert reference in editor
interface ReferenceInsertion {
  reference_id: string;
  insertion_type: 'citation' | 'link' | 'footnote' | 'inline';
  format: {
    style: 'academic' | 'informal' | 'footnote';
    include_summary: boolean;
  };
}

// Quick access in editor
interface EditorReferencePanel {
  pinned_references: Reference[];
  recent_references: Reference[];
  search_enabled: boolean;
  drag_drop_enabled: boolean;
}
```

### AI Context Integration
```typescript
// Provide references to AI agents
interface AIReferenceContext {
  relevant_references: Reference[];
  reference_summaries: Summary[];
  extracted_facts: Fact[];
  suggested_applications: Suggestion[];
}
```

### Smart Suggestions
```typescript
class ReferenceSuggestions {
  async suggestReferences(
    context: WritingContext
  ): Promise<Reference[]> {
    // Analyze current writing
    const topics = await this.extractTopics(context.current_text);
    
    // Find relevant references
    const suggestions = await this.findRelevant({
      topics,
      project_id: context.project_id,
      exclude_used: context.recently_used
    });
    
    return this.rankByRelevance(suggestions, context);
  }
}
```

## Performance Optimization

### Storage Strategy
```typescript
interface StorageConfig {
  max_file_size: 50 * 1024 * 1024; // 50MB
  allowed_types: string[];
  compression: {
    images: { quality: 0.85, format: 'webp' };
    documents: { extract_text: true, store_original: true };
  };
  cdn_enabled: true;
}
```

### Caching
```typescript
interface CacheStrategy {
  summaries: { ttl: 86400 }; // 24 hours
  extracted_text: { ttl: 604800 }; // 7 days
  image_analysis: { ttl: 2592000 }; // 30 days
  search_results: { ttl: 3600 }; // 1 hour
}
```

### Database Indexes
```sql
CREATE INDEX idx_reference_materials_project 
  ON reference_materials(project_id);
CREATE INDEX idx_reference_materials_type 
  ON reference_materials(material_type);
CREATE INDEX idx_reference_materials_tags 
  ON reference_materials USING GIN(tags);
CREATE INDEX idx_reference_materials_relevance 
  ON reference_materials(relevance_score DESC) 
  WHERE relevance_score > 0.5;
```

## Security & Privacy

### Access Control
```sql
-- Users can only access their project's references
CREATE POLICY "Project reference access" ON reference_materials
  FOR ALL USING (
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    ) OR
    project_id IN (
      SELECT project_id FROM project_collaborators
      WHERE user_id = auth.uid() AND status = 'accepted'
    )
  );
```

### Content Security
- Virus scanning on upload
- File type validation
- Size limits enforcement
- Secure storage with encryption
- Signed URLs for access

## Future Enhancements

1. **Advanced Analysis**
   - OCR for handwritten notes
   - Audio transcription
   - Video frame extraction
   - 3D model support

2. **Research Tools**
   - Web clipper browser extension
   - Automatic citation formatting
   - Research paper database integration
   - Fact-checking integration

3. **Collaboration**
   - Shared reference libraries
   - Annotation collaboration
   - Reference recommendations
   - Community research pools

4. **AI Enhancement**
   - Auto-tagging with ML
   - Content-based recommendations
   - Research gap identification
   - Source credibility scoring

## Related Systems
- Story Bible System (reference integration)
- Content Search System (reference search)
- AI Agent System (context provision)
- Writing Editor (reference insertion)