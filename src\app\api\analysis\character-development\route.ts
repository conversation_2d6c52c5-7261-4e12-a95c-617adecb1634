import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { openai } from '@/lib/ai/openai-client'
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

const querySchema = z.object({
  projectId: z.string().uuid(),
  characterId: z.string().uuid()
})

interface DevelopmentAspect {
  initial: string
  current: string
  progress: number
  milestones: {
    chapter: number
    description: string
    impact: 'minor' | 'moderate' | 'major'
  }[]
}

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    const characterId = searchParams.get('characterId')
    
    const validation = querySchema.safeParse({ projectId, characterId })
    if (!validation.success) {
      return UnifiedResponse.error('Invalid query parameters', 400, validation.error.errors)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const project = await verifyProjectAccess(projectId, user.id)
    if (!project) {
      return UnifiedResponse.error(PROJECT_ACCESS_ERROR, 404)
    }

    // Get character data
    const { data: character } = await supabase
      .from('characters')
      .select('*')
      .eq('id', characterId)
      .eq('project_id', projectId)
      .single()

    if (!character) {
      return UnifiedResponse.error('Character not found', 404)
    }

    // Get chapters
    const { data: chapters } = await supabase
      .from('chapters')
      .select('id, chapter_number, content, title')
      .eq('project_id', projectId)
      .order('chapter_number')

    // Initialize development aspects
    const aspects: Record<string, DevelopmentAspect> = {
      goals: {
        initial: character.goals || 'Unknown goals',
        current: character.goals || 'Unknown goals',
        progress: 0,
        milestones: []
      },
      relationships: {
        initial: 'Starting relationships',
        current: 'Current relationships',
        progress: 0,
        milestones: []
      },
      beliefs: {
        initial: character.beliefs || 'Core beliefs',
        current: character.beliefs || 'Core beliefs',
        progress: 0,
        milestones: []
      },
      skills: {
        initial: 'Basic abilities',
        current: 'Enhanced abilities',
        progress: 0,
        milestones: []
      },
      flaws: {
        initial: character.flaws || 'Character flaws',
        current: character.flaws || 'Character flaws',
        progress: 0,
        milestones: []
      },
      strengths: {
        initial: character.strengths || 'Core strengths',
        current: character.strengths || 'Core strengths',
        progress: 0,
        milestones: []
      }
    }

    // Analyze development across chapters
    if (chapters && chapters.length > 0) {
      for (const chapter of chapters) {
        if (!chapter.content || !chapter.content.includes(character.name)) {
          continue
        }

        try {
          const analysis = await analyzeCharacterDevelopment(character, chapter)
          
          // Update aspects based on analysis
          for (const [aspectId, milestones] of Object.entries(analysis)) {
            if (aspects[aspectId] && Array.isArray(milestones)) {
              aspects[aspectId].milestones.push(...milestones)
              aspects[aspectId].progress = Math.min(100, aspects[aspectId].milestones.length * 10)
              
              // Update current state based on latest milestone
              if (milestones.length > 0) {
                const latestMilestone = milestones[milestones.length - 1]
                if (latestMilestone.impact === 'major') {
                  aspects[aspectId].current = latestMilestone.description
                }
              }
            }
          }
        } catch (error) {
          logger.error('Failed to analyze chapter:', { chapterId: chapter.id, error })
        }
      }
    }

    // Calculate overall progress and consistency
    const aspectValues = Object.values(aspects)
    const overallProgress = Math.round(
      aspectValues.reduce((sum, a) => sum + a.progress, 0) / aspectValues.length
    )
    
    // Consistency is higher if progress is balanced across aspects
    const progressValues = aspectValues.map(a => a.progress)
    const avgProgress = overallProgress
    const variance = progressValues.reduce((sum, p) => sum + Math.pow(p - avgProgress, 2), 0) / progressValues.length
    const consistency = Math.round(100 - Math.min(variance, 100))

    return UnifiedResponse.success({
      characterId,
      characterName: character.name,
      aspects,
      overallProgress,
      consistency
    })
  } catch (error) {
    logger.error('Character development analysis error:', error)
    return UnifiedResponse.error('Failed to analyze character development')
  }
})

async function analyzeCharacterDevelopment(
  character: any,
  chapter: any
): Promise<Record<string, any[]>> {
  try {
    const prompt = `Analyze character development for "${character.name}" in this chapter.

Character Profile:
- Name: ${character.name}
- Goals: ${character.goals || 'Not specified'}
- Personality: ${character.personality || 'Not specified'}
- Flaws: ${character.flaws || 'Not specified'}

Chapter ${chapter.chapter_number}: ${chapter.title || ''}
${chapter.content.substring(0, 2000)}...

Identify development milestones in these aspects:
1. goals - Changes in what the character wants
2. relationships - How connections with others evolve
3. beliefs - Shifts in worldview or values
4. skills - New abilities or competencies gained
5. flaws - How weaknesses are addressed or worsen
6. strengths - How core strengths evolve

For each milestone found, specify:
- aspect (one of the above)
- description (brief, under 100 chars)
- impact (minor/moderate/major)

Only include actual development, not static descriptions.`

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a literary analyst. Identify concrete character development milestones.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      response_format: { type: 'json_object' },
      max_tokens: 800
    })

    const result = JSON.parse(response.choices[0].message.content || '{}')
    
    // Structure the milestones by aspect
    const milestonesByAspect: Record<string, any[]> = {}
    
    if (result.milestones && Array.isArray(result.milestones)) {
      for (const milestone of result.milestones) {
        const aspect = milestone.aspect || 'goals'
        if (!milestonesByAspect[aspect]) {
          milestonesByAspect[aspect] = []
        }
        
        milestonesByAspect[aspect].push({
          chapter: chapter.chapter_number,
          description: (milestone.description || '').substring(0, 100),
          impact: milestone.impact || 'minor'
        })
      }
    }
    
    return milestonesByAspect
  } catch (error) {
    logger.error('AI analysis failed:', error)
    return {}
  }
}