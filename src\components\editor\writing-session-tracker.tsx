'use client';

import { useEffect, useState } from 'react';
import { writingSessionTracker } from '@/lib/services/writing-session-tracker';
import { Card } from '@/components/ui/card';
import { Timer, TrendingUp, PenTool } from 'lucide-react';
import { TIME_MS } from '@/lib/constants'
import { TIME_SECONDS } from '@/lib/constants'

interface WritingSessionDisplayProps {
  userId: string;
  projectId: string;
  chapterId?: string;
  variant?: 'compact' | 'full';
}

export function WritingSessionTracker({ 
  userId, 
  projectId, 
  chapterId,
  variant = 'compact' 
}: WritingSessionDisplayProps) {
  const [sessionStats, setSessionStats] = useState<{
    duration: number;
    wordsWritten: number;
    wordsPerMinute: number;
  } | null>(null);

  useEffect(() => {
    const updateStats = () => {
      const stats = writingSessionTracker.getSessionStats(userId, projectId, chapterId);
      setSessionStats(stats);
    };

    // Update immediately
    updateStats();

    // Update every second
    const interval = setInterval(updateStats, TIME_MS.SECOND);

    return () => clearInterval(interval);
  }, [userId, projectId, chapterId]);

  if (!sessionStats || sessionStats.duration === 0) {
    return null;
  }

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / TIME_SECONDS.HOUR);
    const minutes = Math.floor((seconds % TIME_SECONDS.HOUR) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (variant === 'compact') {
    return (
      <div className="flex items-center gap-4 sm:gap-5 lg:gap-6 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <Timer className="w-3.5 h-3.5" />
          <span>{formatDuration(sessionStats.duration)}</span>
        </div>
        <div className="flex items-center gap-1">
          <PenTool className="w-3.5 h-3.5" />
          <span>{sessionStats.wordsWritten} words</span>
        </div>
        <div className="flex items-center gap-1">
          <TrendingUp className="w-3.5 h-3.5" />
          <span>{sessionStats.wordsPerMinute} WPM</span>
        </div>
      </div>
    );
  }

  return (
    <Card className="p-4">
      <h3 className="text-sm font-medium mb-3">Current Session</h3>
      <div className="grid grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
        <div>
          <div className="flex items-center gap-2 text-muted-foreground mb-1">
            <Timer className="w-4 h-4" />
            <span className="text-xs">Duration</span>
          </div>
          <p className="text-lg font-semibold">{formatDuration(sessionStats.duration)}</p>
        </div>
        <div>
          <div className="flex items-center gap-2 text-muted-foreground mb-1">
            <PenTool className="w-4 h-4" />
            <span className="text-xs">Words Written</span>
          </div>
          <p className="text-lg font-semibold">{sessionStats.wordsWritten}</p>
        </div>
        <div>
          <div className="flex items-center gap-2 text-muted-foreground mb-1">
            <TrendingUp className="w-4 h-4" />
            <span className="text-xs">Words/Minute</span>
          </div>
          <p className="text-lg font-semibold">{sessionStats.wordsPerMinute}</p>
        </div>
      </div>
    </Card>
  );
}