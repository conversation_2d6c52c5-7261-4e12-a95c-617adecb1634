import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { z } from 'zod'
<<<<<<< HEAD
import { createServerClient } from '@/lib/supabase'
import { mailerooEmailService as emailService } from '@/lib/services/email/maileroo-email-service'
=======
import { createTypedServerClient } from '@/lib/supabase'
import { emailService } from '@/lib/email/service'
>>>>>>> dd974a56edaaeb3bb72610137e1e155511ba61d9
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

// Validation schema
const preferencesSchema = z.object({
  marketing: z.boolean().optional(),
  progress: z.boolean().optional(),
  achievements: z.boolean().optional(),
  collaboration: z.boolean().optional(),
  newsletter: z.boolean().optional()
})

export async function GET(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const preferences = await emailService.getEmailPreferences(user.id)
    
    return NextResponse.json({ preferences })

  } catch (error) {
    logger.error('Get email preferences error:', error)
    return NextResponse.json(
      { error: 'Failed to get email preferences' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createTypedServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    
    // Validate request
    const validationResult = preferencesSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    await emailService.updateEmailPreferences(user.id, validationResult.data)
    
    return NextResponse.json({ 
      message: 'Email preferences updated successfully' 
    })

  } catch (error) {
    logger.error('Update email preferences error:', error)
    return NextResponse.json(
      { error: 'Failed to update email preferences' },
      { status: 500 }
    )
  }
}