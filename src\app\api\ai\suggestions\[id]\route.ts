import { NextResponse } from 'next/server'
import { handleAPIError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { CONCURRENCY_LIMITS } from '@/lib/constants'

const updateSuggestionSchema = z.object({
  accepted: z.boolean().optional(),
  feedback: z.string().optional(),
  rating: z.number().min(CONCURRENCY_LIMITS.MIN_BATCH_SIZE).max(CONCURRENCY_LIMITS.MAX_BATCH_SIZE).optional(),
  applied_at: z.string().datetime().optional()
})

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const supabase = await createTypedServerClient()

    const { data: suggestion, error } = await supabase
      .from('ai_suggestions')
      .select(`
        *,
        projects (
          id,
          title
        ),
        chapters (
          id,
          title,
          chapter_number
        )
      `)
      .eq('id', params.id)
      .eq('user_id', authResult.user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'))
      }
      logger.error('Error fetching AI suggestion:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ suggestion })

  } catch (error) {
    return handleRouteError(error, 'AI Suggestion GET')
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const body = await request.json()
    const validatedData = updateSuggestionSchema.parse(body)

    const supabase = await createTypedServerClient()

    // Verify ownership
    const { data: existing } = await supabase
      .from('ai_suggestions')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', authResult.user.id)
      .single()

    if (!existing) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // If accepting the suggestion, set applied_at timestamp
    const updateData = {
      ...validatedData,
      applied_at: validatedData.accepted === true && !validatedData.applied_at 
        ? new Date().toISOString() 
        : validatedData.applied_at,
      updated_at: new Date().toISOString()
    }

    // Update suggestion
    const { data: updated, error } = await supabase
      .from('ai_suggestions')
      .update(updateData)
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      logger.error('Error updating suggestion:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ suggestion: updated })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid update data',
        details: error.errors
      }, { status: 400 })
    }

    return handleRouteError(error, 'AI Suggestion PATCH')
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return authResult.response!
    }

    const supabase = await createTypedServerClient()

    // Verify ownership
    const { data: existing } = await supabase
      .from('ai_suggestions')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', authResult.user.id)
      .single()

    if (!existing) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Delete suggestion
    const { error } = await supabase
      .from('ai_suggestions')
      .delete()
      .eq('id', params.id)

    if (error) {
      logger.error('Error deleting suggestion:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    return handleRouteError(error, 'AI Suggestion DELETE')
  }
}