'use client'

import { logger } from '@/lib/services/logger'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import {
  Mic,
  BookOpen,
  User,
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  Upload,
  FileText,
  Sparkles,
  Save
} from 'lucide-react'

interface VoiceProfile {
  id: string
  name: string
  description?: string
  type: 'author' | 'character' | 'narrator'
  confidence: number
  training_samples_count: number
  total_words_analyzed: number
  created_at: string
  updated_at: string
}

interface VoiceTrainerProps {
  projectId?: string
  seriesId?: string
  characterId?: string
  onProfileCreated?: (profile: VoiceProfile) => void
  existingProfile?: VoiceProfile
}

export function VoiceTrainer({
  projectId,
  seriesId,
  characterId,
  onProfileCreated,
  existingProfile
}: VoiceTrainerProps) {
  const [isCreating, setIsCreating] = useState(false)
  const [isTraining, setIsTraining] = useState(false)
  const [profileName, setProfileName] = useState(existingProfile?.name || '')
  const [profileDescription, setProfileDescription] = useState(existingProfile?.description || '')
  const [profileType, setProfileType] = useState<'author' | 'character' | 'narrator'>(
    existingProfile?.type || 'author'
  )
  const [trainingText, setTrainingText] = useState('')
  const [trainingTexts, setTrainingTexts] = useState<string[]>([])
  const [currentProfile, setCurrentProfile] = useState<VoiceProfile | null>(existingProfile || null)
  const [activeTab, setActiveTab] = useState('create')
  const { toast } = useToast()

  const handleCreateProfile = async () => {
    if (!profileName.trim()) {
      toast({ title: 'Error', description: 'Please enter a profile name', variant: 'destructive' })
      return
    }

    setIsCreating(true)
    try {
      const response = await fetch('/api/voice-profiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: profileName,
          description: profileDescription,
          type: profileType,
          projectId,
          seriesId,
          characterId,
          isGlobal: !projectId && !seriesId && !characterId
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create profile')
      }

      const { profile } = await response.json()
      setCurrentProfile(profile)
      setActiveTab('train')
      toast({ title: 'Success', description: 'Voice profile created successfully' })
      
      if (onProfileCreated) {
        onProfileCreated(profile)
      }
    } catch (error) {
      logger.error('Error creating profile:', error)
      toast({ title: 'Error', description: error instanceof Error ? error.message : 'Failed to create voice profile', variant: 'destructive' })
    } finally {
      setIsCreating(false)
    }
  }

  const handleAddTrainingText = () => {
    if (trainingText.trim().length < 100) {
      toast({ title: 'Error', description: 'Training text must be at least 100 characters', variant: 'destructive' })
      return
    }

    setTrainingTexts([...trainingTexts, trainingText.trim()])
    setTrainingText('')
    toast({ title: 'Success', description: 'Training text added' })
  }

  const handleTrainProfile = async () => {
    if (!currentProfile) {
      toast({ title: 'Error', description: 'Please create a profile first', variant: 'destructive' })
      return
    }

    if (trainingTexts.length === 0) {
      toast({ title: 'Error', description: 'Please add at least one training text', variant: 'destructive' })
      return
    }

    setIsTraining(true)
    try {
      const response = await fetch(`/api/voice-profiles/${currentProfile.id}/train`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          texts: trainingTexts,
          source: 'manual_entry'
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to train profile')
      }

      const { profile, trainedSamples } = await response.json()
      setCurrentProfile(profile)
      setTrainingTexts([])
      toast({ title: 'Success', description: `Successfully trained with ${trainedSamples} samples` })
    } catch (error) {
      logger.error('Error training profile:', error)
      toast({ title: 'Error', description: error instanceof Error ? error.message : 'Failed to train voice profile', variant: 'destructive' })
    } finally {
      setIsTraining(false)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'author':
        return <BookOpen className="h-4 w-4" />
      case 'character':
        return <User className="h-4 w-4" />
      case 'narrator':
        return <Mic className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-success'
    if (confidence >= 0.6) return 'text-warning'
    return 'text-error'
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-primary" />
          Voice Profile Trainer
        </CardTitle>
        <CardDescription>
          {currentProfile 
            ? `Training "${currentProfile.name}" voice profile`
            : 'Create and train a custom voice profile for consistent writing style'
          }
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create">
              {currentProfile ? 'Profile Details' : 'Create Profile'}
            </TabsTrigger>
            <TabsTrigger value="train" disabled={!currentProfile}>
              Train Profile
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-4">
            {currentProfile ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(currentProfile.type)}
                    <h3 className="text-lg font-semibold">{currentProfile.name}</h3>
                    <Badge variant="outline">{currentProfile.type}</Badge>
                  </div>
                  <Badge className={getConfidenceColor(currentProfile.confidence)}>
                    {Math.round(currentProfile.confidence * 100)}% confidence
                  </Badge>
                </div>

                {currentProfile.description && (
                  <p className="text-sm text-muted-foreground">
                    {currentProfile.description}
                  </p>
                )}

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label className="text-muted-foreground">Training Samples</Label>
                    <p className="font-medium">{currentProfile.training_samples_count}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Words Analyzed</Label>
                    <p className="font-medium">{currentProfile.total_words_analyzed.toLocaleString()}</p>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Continue training this profile by adding more text samples in the "Train Profile" tab.
                  </AlertDescription>
                </Alert>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="profileName">Profile Name</Label>
                  <Input
                    id="profileName"
                    placeholder="e.g., My Writing Style, Character Voice"
                    value={profileName}
                    onChange={(e) => setProfileName(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="profileDescription">Description (Optional)</Label>
                  <Textarea
                    id="profileDescription"
                    placeholder="Describe this voice profile..."
                    value={profileDescription}
                    onChange={(e) => setProfileDescription(e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="profileType">Profile Type</Label>
                  <Select value={profileType} onValueChange={(value: any) => setProfileType(value)}>
                    <SelectTrigger id="profileType">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="author">
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-4 w-4" />
                          Author Voice
                        </div>
                      </SelectItem>
                      <SelectItem value="character">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Character Voice
                        </div>
                      </SelectItem>
                      <SelectItem value="narrator">
                        <div className="flex items-center gap-2">
                          <Mic className="h-4 w-4" />
                          Narrator Voice
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button 
                  onClick={handleCreateProfile} 
                  disabled={isCreating || !profileName.trim()}
                  className="w-full"
                >
                  {isCreating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Profile...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Voice Profile
                    </>
                  )}
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="train" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="trainingText">Training Text</Label>
                <Textarea
                  id="trainingText"
                  placeholder="Paste or type a sample of the writing style you want to capture (minimum 100 characters)..."
                  value={trainingText}
                  onChange={(e) => setTrainingText(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <div className="flex items-center justify-between mt-2">
                  <span className="text-sm text-muted-foreground">
                    {trainingText.length} characters
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleAddTrainingText}
                    disabled={trainingText.trim().length < 100}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Sample
                  </Button>
                </div>
              </div>

              {trainingTexts.length > 0 && (
                <div className="space-y-2">
                  <Label>Training Samples ({trainingTexts.length})</Label>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {trainingTexts.map((text, index) => (
                      <div key={index} className="p-3 bg-muted rounded-lg">
                        <p className="text-sm line-clamp-2">{text}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {text.split(/\s+/).length} words
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Alert>
                <Sparkles className="h-4 w-4" />
                <AlertDescription>
                  Add multiple text samples for better accuracy. The more diverse samples you provide, 
                  the better the AI will understand and replicate the voice.
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleTrainProfile}
                disabled={isTraining || trainingTexts.length === 0}
                className="w-full"
              >
                {isTraining ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Training Profile...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Train Voice Profile
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}