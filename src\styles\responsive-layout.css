/* Responsive Layout Enhancements */

/* Override default container for wider layouts */
@layer utilities {
  /* Wide container variant */
  .container-wide {
    @apply w-full mx-auto;
    padding-left: clamp(1rem, 3vw, 4rem);
    padding-right: clamp(1rem, 3vw, 4rem);
    max-width: min(100%, 2200px);
  }
  
  /* Full width container with padding */
  .container-full {
    @apply w-full;
    padding-left: clamp(1rem, 2vw, 3rem);
    padding-right: clamp(1rem, 2vw, 3rem);
  }
  
  /* Fluid typography for better responsiveness */
  .text-fluid-sm {
    font-size: clamp(0.875rem, 0.8rem + 0.3vw, 1rem);
  }
  
  .text-fluid-base {
    font-size: clamp(1rem, 0.9rem + 0.4vw, 1.125rem);
  }
  
  .text-fluid-lg {
    font-size: clamp(1.125rem, 1rem + 0.5vw, 1.25rem);
  }
  
  .text-fluid-xl {
    font-size: clamp(1.25rem, 1.1rem + 0.6vw, 1.5rem);
  }
  
  .text-fluid-2xl {
    font-size: clamp(1.5rem, 1.3rem + 0.8vw, 2rem);
  }
  
  .text-fluid-3xl {
    font-size: clamp(1.875rem, 1.5rem + 1.5vw, 2.5rem);
  }
  
  .text-fluid-4xl {
    font-size: clamp(2.25rem, 1.8rem + 2vw, 3.5rem);
  }
  
  /* Responsive grid utilities */
  .grid-responsive {
    @apply grid gap-4 lg:gap-6 xl:gap-8;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
  }
  
  .grid-responsive-sm {
    @apply grid gap-3 lg:gap-4 xl:gap-6;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 250px), 1fr));
  }
  
  .grid-responsive-lg {
    @apply grid gap-6 lg:gap-8 xl:gap-10;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 400px), 1fr));
  }
}

/* Responsive spacing utilities */
@layer utilities {
  .p-responsive {
    @apply p-4 sm:p-6 lg:p-8 xl:p-10 2xl:p-12;
  }
  
  .px-responsive {
    @apply px-4 sm:px-6 lg:px-8 xl:px-10 2xl:px-12;
  }
  
  .py-responsive {
    @apply py-4 sm:py-6 lg:py-8 xl:py-10 2xl:py-12;
  }
  
  .mt-responsive {
    @apply mt-4 sm:mt-6 lg:mt-8 xl:mt-10 2xl:mt-12;
  }
  
  .mb-responsive {
    @apply mb-4 sm:mb-6 lg:mb-8 xl:mb-10 2xl:mb-12;
  }
  
  .gap-responsive {
    @apply gap-4 sm:gap-6 lg:gap-8 xl:gap-10 2xl:gap-12;
  }
}

/* Better use of screen real estate on large screens */
@media (min-width: 1920px) {
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

/* Responsive card layouts */
.card-responsive {
  @apply w-full;
  container-type: inline-size;
}

@container (min-width: 600px) {
  .card-responsive {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 2rem;
  }
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .table-responsive {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .table-responsive table {
    min-width: 600px;
  }
}

/* Editor responsiveness */
.editor-container {
  height: calc(100vh - 4rem);
  max-height: 900px;
}

@media (min-width: 1536px) {
  .editor-container {
    max-height: 1200px;
  }
}

/* Modal and dialog responsiveness */
.modal-responsive {
  @apply w-full max-w-lg sm:max-w-xl md:max-w-2xl lg:max-w-3xl xl:max-w-4xl;
}

.dialog-responsive {
  @apply w-[95vw] max-w-lg sm:w-[90vw] sm:max-w-xl md:w-[85vw] md:max-w-2xl lg:w-[80vw] lg:max-w-3xl xl:max-w-4xl;
}