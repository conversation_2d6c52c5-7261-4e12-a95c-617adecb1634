import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { VersionHistoryService } from '@/lib/version-history'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()
    const body = await request.json()
    const { chapterId, versionId } = body

    if (!chapterId || !versionId) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Verify user owns the chapter
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select('project_id')
      .eq('id', chapterId)
      .single()

    if (chapterError || !chapter) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Verify user owns the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', chapter.project_id)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return handleAPIError(new AuthorizationError())
    }

    // Verify user owns the version
    const { data: version, error: versionError } = await supabase
      .from('chapter_versions')
      .select('id')
      .eq('id', versionId)
      .eq('user_id', user.id)
      .single()

    if (versionError || !version) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Restore version
    const versionService = new VersionHistoryService()
    const success = await versionService.restoreChapterVersion(chapterId, versionId, user.id)

    if (!success) {
      return NextResponse.json({ error: 'Failed to restore version' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    logger.error('Restore version error:', error)
    return NextResponse.json(
      { error: 'Failed to restore version' },
      { status: 500 }
    )
  }
})