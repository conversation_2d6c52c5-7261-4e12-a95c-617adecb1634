/**
 * Accessibility Settings Section
 * Accessibility and usability options
 */

'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  Zap, 
  Focus, 
  Volume2,
  Contrast,
  MousePointer,
  RotateCcw,
  Info
} from 'lucide-react';

import { useAccessibilitySettings } from '@/lib/settings/settings-store';
import { defaultAccessibilitySettings } from '@/lib/settings/settings-types';

export function AccessibilitySettingsSection() {
  const { accessibility, updateAccessibility } = useAccessibilitySettings();

  const resetAccessibility = () => {
    updateAccessibility(defaultAccessibilitySettings);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-primary" />
            <h3 className="text-lg font-semibold">Accessibility</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={resetAccessibility}
            className="text-xs"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Configure accessibility features to improve usability and comfort.
        </p>
      </div>

      {/* Visual Accessibility */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Contrast className="w-4 h-4" />
            Visual Accessibility
          </CardTitle>
          <CardDescription>
            Adjust visual elements for better visibility and comfort
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>High Contrast Mode</Label>
              <p className="text-xs text-muted-foreground">
                Increase contrast between text and background colors
              </p>
            </div>
            <Switch
              checked={accessibility.highContrast}
              onCheckedChange={(checked) => updateAccessibility({ highContrast: checked })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Reduced Motion
              </Label>
              <p className="text-xs text-muted-foreground">
                Minimize animations and transitions for motion sensitivity
              </p>
            </div>
            <Switch
              checked={accessibility.reducedMotion}
              onCheckedChange={(checked) => updateAccessibility({ reducedMotion: checked })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Navigation Accessibility */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Focus className="w-4 h-4" />
            Navigation & Focus
          </CardTitle>
          <CardDescription>
            Improve keyboard navigation and focus visibility
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <MousePointer className="w-4 h-4" />
                Enhanced Focus Indicators
              </Label>
              <p className="text-xs text-muted-foreground">
                Make focus outlines more visible for keyboard navigation
              </p>
            </div>
            <Switch
              checked={accessibility.enhancedFocus}
              onCheckedChange={(checked) => updateAccessibility({ enhancedFocus: checked })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Screen Reader Support */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Volume2 className="w-4 h-4" />
            Screen Reader Support
          </CardTitle>
          <CardDescription>
            Optimize the interface for screen readers and assistive technology
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Screen Reader Optimized</Label>
              <p className="text-xs text-muted-foreground">
                Add extra labels and descriptions for screen readers
              </p>
            </div>
            <Switch
              checked={accessibility.screenReaderOptimized}
              onCheckedChange={(checked) => updateAccessibility({ screenReaderOptimized: checked })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Accessibility Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Info className="w-4 h-4" />
            Current Settings
          </CardTitle>
          <CardDescription>
            Overview of your current accessibility configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">High Contrast</span>
                <Badge variant={accessibility.highContrast ? 'default' : 'secondary'}>
                  {accessibility.highContrast ? 'On' : 'Off'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Reduced Motion</span>
                <Badge variant={accessibility.reducedMotion ? 'default' : 'secondary'}>
                  {accessibility.reducedMotion ? 'On' : 'Off'}
                </Badge>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Enhanced Focus</span>
                <Badge variant={accessibility.enhancedFocus ? 'default' : 'secondary'}>
                  {accessibility.enhancedFocus ? 'On' : 'Off'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Screen Reader</span>
                <Badge variant={accessibility.screenReaderOptimized ? 'default' : 'secondary'}>
                  {accessibility.screenReaderOptimized ? 'On' : 'Off'}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Accessibility Tips */}
      <Card className="border-blue-200 bg-info-light/50 dark:border-blue-800 dark:bg-blue-950/20">
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2 text-blue-700 dark:text-blue-300">
            <Info className="w-4 h-4" />
            Accessibility Tips
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-blue-700 dark:text-blue-300">
          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
              <div>
                <strong>Keyboard Navigation:</strong> Use Tab to navigate, Enter to activate, and Escape to close dialogs.
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
              <div>
                <strong>Text Size:</strong> Use the Typography settings to adjust text size for better readability.
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
              <div>
                <strong>Themes:</strong> Choose high-contrast themes if you have difficulty distinguishing colors.
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
              <div>
                <strong>Screen Readers:</strong> Enable screen reader optimization for better compatibility with assistive technology.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Quick Accessibility Presets</CardTitle>
          <CardDescription>
            Apply common accessibility configurations with one click
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Button
              variant="outline"
              className="h-auto p-4 text-left"
              onClick={() => updateAccessibility({
                highContrast: true,
                reducedMotion: true,
                enhancedFocus: true,
                screenReaderOptimized: false,
              })}
            >
              <div>
                <div className="font-medium">Maximum Accessibility</div>
                <div className="text-xs text-muted-foreground mt-1">
                  High contrast, reduced motion, enhanced focus
                </div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="h-auto p-4 text-left"
              onClick={() => updateAccessibility({
                highContrast: false,
                reducedMotion: false,
                enhancedFocus: false,
                screenReaderOptimized: true,
              })}
            >
              <div>
                <div className="font-medium">Screen Reader Optimized</div>
                <div className="text-xs text-muted-foreground mt-1">
                  Optimized for screen readers and assistive tech
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
