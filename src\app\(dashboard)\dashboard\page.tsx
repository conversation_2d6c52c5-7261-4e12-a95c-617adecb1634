import { createServerClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import DashboardPageClient from './page-client'


export default async function DashboardPage() {
  const supabase = await createServerClient()
  
  // Try to get user, but don't redirect here - let middleware handle it
  const { data: { user } } = await supabase.auth.getUser()
  
  // If no user at this point, middleware should have already redirected
  // This is a fallback only
  if (!user) {
    redirect('/login?error=session_expired')
  }
  
  const { data: projects } = await supabase
    .from('projects')
    .select(`
      id, 
      title, 
      primary_genre,
      series_books!series_books_project_id_fkey(
        series_id,
        book_number,
        series:series(
          id,
          title,
          universe_id,
          universe:universe_id(
            id,
            name
          )
        )
      )
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
  
  return (
    <DashboardPageClient 
      userId={user.id} 
      projects={projects || []}
    />
  )
}