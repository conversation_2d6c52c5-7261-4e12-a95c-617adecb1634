'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Clock,
  Edit3,
  <PERSON>ap,
  Brain,
  BarChart3,
  Coffee,
  Moon,
  Sun
} from 'lucide-react'

interface BehavioralData {
  peakHours: Array<{ hour: number; productivity: number }>
  writingModes: Array<{ mode: string; percentage: number }>
  frequentActions: Array<{ action: string; count: number }>
}

interface BehavioralInsightsProps {
  data: BehavioralData
}

export function BehavioralInsights({ data }: BehavioralInsightsProps) {
  const getHourLabel = (hour: number) => {
    if (hour === 0) return '12 AM'
    if (hour === 12) return '12 PM'
    if (hour < 12) return `${hour} AM`
    return `${hour - 12} PM`
  }

  const getTimeOfDayIcon = (hour: number) => {
    if (hour >= 5 && hour < 12) return <Sun className="h-4 w-4 text-warning" />
    if (hour >= 12 && hour < 17) return <Coffee className="h-4 w-4 text-warning" />
    if (hour >= 17 && hour < 21) return <Moon className="h-4 w-4 text-info" />
    return <Moon className="h-4 w-4 text-gray-500" />
  }

  const getModeIcon = (mode: string) => {
    const icons: Record<string, React.ReactNode> = {
      'Sprint Writing': <Zap className="h-4 w-4 text-warning" />,
      'Deep Focus': <Brain className="h-4 w-4 text-purple-500" />,
      'Editing': <Edit3 className="h-4 w-4 text-info" />,
      'Planning': <BarChart3 className="h-4 w-4 text-success" />
    }
    return icons[mode] || <Edit3 className="h-4 w-4 text-gray-500" />
  }

  // Find peak productivity hours
  const sortedHours = [...(data.peakHours || [])]
    .sort((a, b) => b.productivity - a.productivity)
    .slice(0, 3)

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Peak Productivity Hours */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-primary" />
            <h4 className="font-semibold">Peak Productivity Hours</h4>
          </div>
          
          <div className="space-y-3">
            {sortedHours.map((item, index) => (
              <div key={item.hour} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getTimeOfDayIcon(item.hour)}
                    <span className="font-medium">{getHourLabel(item.hour)}</span>
                    {index === 0 && (
                      <Badge variant="default" className="text-xs">Best</Badge>
                    )}
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {item.productivity}% productive
                  </span>
                </div>
                <Progress value={item.productivity} className="h-2" />
              </div>
            ))}
          </div>

          <p className="text-sm text-muted-foreground">
            You're most productive at {getHourLabel(sortedHours[0]?.hour || 9)}
          </p>
        </div>
      </Card>

      {/* Preferred Writing Modes */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Edit3 className="h-5 w-5 text-primary" />
            <h4 className="font-semibold">Writing Mode Preferences</h4>
          </div>
          
          <div className="space-y-3">
            {(data.writingModes || []).map((mode) => (
              <div key={mode.mode} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getModeIcon(mode.mode)}
                    <span className="font-medium">{mode.mode}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {mode.percentage}%
                  </span>
                </div>
                <Progress value={mode.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Frequent Actions */}
      <Card className="p-6 md:col-span-2">
        <div className="space-y-4">
          <h4 className="font-semibold">Activity Patterns</h4>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
            {(data.frequentActions || []).slice(0, 8).map((action) => (
              <div key={action.action} className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-primary">
                  {action.count.toLocaleString()}
                </div>
                <p className="text-sm text-muted-foreground">{action.action}</p>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Insights Summary */}
      <Card className="p-6 md:col-span-2 bg-primary/5 border-primary/20">
        <div className="space-y-3">
          <h4 className="font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            Behavioral Insights Summary
          </h4>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• Schedule important writing sessions during your peak hours</li>
            <li>• Your most effective mode is {data.writingModes?.[0]?.mode || 'Sprint Writing'}</li>
            <li>• Consider time-blocking for deep focus sessions</li>
            <li>• Your consistency patterns suggest regular writing habits</li>
          </ul>
        </div>
      </Card>
    </div>
  )
}