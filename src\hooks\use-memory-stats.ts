'use client'

import { useState, useEffect, useCallback } from 'react'
import { logger } from '@/lib/services/logger'

export interface MemoryContext {
  id: string
  type: 'chapter' | 'character' | 'location' | 'plot' | 'theme' | 'reference'
  name: string
  tokens: number
  lastAccessed: Date
  importance: number
  compressed: boolean
}

export interface MemoryBreakdown {
  storyContent: number
  characters: number
  worldBuilding: number
  plotTimeline: number
  metadata: number
  aiPrompts: number
}

export interface MemoryStats {
  totalTokens: number
  contexts: MemoryContext[]
  breakdown: MemoryBreakdown
  compressionRate: number
  cacheSize: number
  lastOptimization: Date | null
}

export interface MemoryOptimizationResult {
  optimizedTokens: number
  removedContexts: string[]
  compressedContexts: string[]
  mergedContexts: { from: string[]; to: string }[]
}

interface UseMemoryStatsReturn {
  stats: MemoryStats | null
  isLoading: boolean
  error: Error | null
  refresh: () => Promise<void>
  optimizeMemory: () => Promise<MemoryOptimizationResult>
  clearCache: () => Promise<void>
}

export function useMemoryStats(projectId: string): UseMemoryStatsReturn {
  const [stats, setStats] = useState<MemoryStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const fetchStats = useCallback(async () => {
    if (!projectId) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/memory/stats?projectId=${projectId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch memory stats: ${response.statusText}`)
      }

      const data = await response.json()
      
      // Transform the data to match our interface
      const transformedStats: MemoryStats = {
        totalTokens: data.stats?.totalTokens || 0,
        contexts: data.stats?.contexts || [],
        breakdown: data.stats?.breakdown || {
          storyContent: 0,
          characters: 0,
          worldBuilding: 0,
          plotTimeline: 0,
          metadata: 0,
          aiPrompts: 0
        },
        compressionRate: data.stats?.compressionRatio || 0,
        cacheSize: data.stats?.cacheSize || 0,
        lastOptimization: data.stats?.lastCompression ? new Date(data.stats.lastCompression) : null
      }

      setStats(transformedStats)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch memory stats')
      logger.error('Error fetching memory stats:', error)
      setError(error)
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  const optimizeMemory = useCallback(async (): Promise<MemoryOptimizationResult> => {
    if (!projectId) {
      throw new Error('Project ID is required')
    }

    try {
      // First, try compression
      const compressResponse = await fetch('/api/memory/compress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      })

      if (!compressResponse.ok) {
        throw new Error(`Compression failed: ${compressResponse.statusText}`)
      }

      const compressData = await compressResponse.json()

      // Then, try merging
      const mergeResponse = await fetch('/api/memory/merge', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      })

      if (!mergeResponse.ok) {
        throw new Error(`Merge failed: ${mergeResponse.statusText}`)
      }

      const mergeData = await mergeResponse.json()

      // Combine results
      const result: MemoryOptimizationResult = {
        optimizedTokens: compressData.optimizedTokens || stats?.totalTokens || 0,
        removedContexts: compressData.removedContexts || [],
        compressedContexts: compressData.compressedContexts || [],
        mergedContexts: mergeData.mergedContexts || []
      }

      // Refresh stats after optimization
      await fetchStats()

      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to optimize memory')
      logger.error('Error optimizing memory:', error)
      throw error
    }
  }, [projectId, stats, fetchStats])

  const clearCache = useCallback(async () => {
    if (!projectId) return

    try {
      const response = await fetch('/api/memory/cache/clear', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId })
      })

      if (!response.ok) {
        throw new Error(`Failed to clear cache: ${response.statusText}`)
      }

      // Refresh stats after clearing cache
      await fetchStats()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to clear cache')
      logger.error('Error clearing cache:', error)
      throw error
    }
  }, [projectId, fetchStats])

  // Initial fetch
  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  // Auto-refresh every 30 seconds if component is mounted
  useEffect(() => {
    const interval = setInterval(fetchStats, 30000)
    return () => clearInterval(interval)
  }, [fetchStats])

  return {
    stats,
    isLoading,
    error,
    refresh: fetchStats,
    optimizeMemory,
    clearCache
  }
}