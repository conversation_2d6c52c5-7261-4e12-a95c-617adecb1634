'use client'

import { useState } from 'react'
import { Navbar } from '@/components/layout/navbar'
import { Sidebar } from '@/components/layout/sidebar'
import { Sheet, SheetContent } from '@/components/ui/sheet'
import { useBreadcrumbs } from '@/hooks/use-breadcrumbs'
import { DashboardClientWrapper } from '../client-wrapper'
import { AchievementNotifier } from '@/components/achievements/achievement-notifier'
import { ErrorBoundary } from '@/components/error-boundary'

export function DashboardLayoutClient({ children }: { children: React.ReactNode }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const breadcrumbs = useBreadcrumbs()

  return (
    <DashboardClientWrapper>
      <div className="h-screen flex bg-background text-foreground overflow-hidden">
        {/* Desktop Sidebar */}
        <aside className="hidden md:flex md:w-64 md:flex-col">
          <Sidebar />
        </aside>
        
        {/* Mobile Sidebar in Sheet */}
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetContent side="left" className="p-0 w-80">
            <Sidebar onClose={() => setIsMobileMenuOpen(false)} />
          </SheetContent>
        </Sheet>
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar 
            breadcrumbs={breadcrumbs} 
            onMenuClick={() => setIsMobileMenuOpen(true)}
            showMenuButton={true}
          />
          <main className="flex-1 overflow-y-auto">
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </main>
        </div>
        
        <AchievementNotifier />
      </div>
    </DashboardClientWrapper>
  )
}