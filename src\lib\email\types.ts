export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export interface EmailOptions {
  to: string | string[]
  from?: string
  replyTo?: string
  cc?: string[]
  bcc?: string[]
  attachments?: EmailAttachment[]
  headers?: Record<string, string>
  tags?: string[]
  metadata?: Record<string, any>
}

export interface EmailAttachment {
  filename: string
  content: Buffer | string
  contentType?: string
  encoding?: string
}

export interface SendEmailOptions extends EmailOptions {
  template: EmailTemplateType
  data: EmailTemplateData
}

export type EmailTemplateType = 
  | 'welcome'
  | 'password-reset'
  | 'subscription-confirmation'
  | 'subscription-change'
  | 'writing-milestone'
  | 'collaboration-invite'
  | 'weekly-progress'
  | 'export-ready'
  | 'achievement-unlocked'

// Base template data that all emails share
interface BaseEmailTemplateData {
  user?: {
    name?: string
    email: string
  }
}

// Specific template data types
export interface WelcomeEmailData extends BaseEmailTemplateData {
  verificationUrl?: string
  appName: string
}

export interface PasswordResetEmailData extends BaseEmailTemplateData {
  resetUrl: string
  expiresIn: string
}

export interface SubscriptionConfirmationEmailData extends BaseEmailTemplateData {
  planName: string
  planPrice: number
  billingCycle: 'monthly' | 'yearly'
  nextBillingDate: string
}

export interface SubscriptionChangeEmailData extends BaseEmailTemplateData {
  oldPlan: string
  newPlan: string
  changeDate: string
  priceDifference?: number
}

export interface WritingMilestoneEmailData extends BaseEmailTemplateData {
  milestone: string
  wordsWritten: number
  projectName: string
  achievement?: string
}

export interface CollaborationInviteEmailData extends BaseEmailTemplateData {
  inviterName: string
  projectName: string
  inviteUrl: string
  role: 'viewer' | 'commenter' | 'editor' | 'admin'
}

export interface WeeklyProgressEmailData extends BaseEmailTemplateData {
  wordsWritten: number
  chaptersCompleted: number
  writingStreak: number
  topProject?: {
    name: string
    progress: number
  }
  insights?: string[]
}

export interface ExportReadyEmailData extends BaseEmailTemplateData {
  projectName: string
  exportFormat: string
  downloadUrl: string
  expiresAt: string
}

export interface AchievementUnlockedEmailData extends BaseEmailTemplateData {
  achievementName: string
  achievementDescription: string
  achievementIcon?: string
  unlockedAt: string
}

// Union type for all template data
export type EmailTemplateData = 
  | WelcomeEmailData
  | PasswordResetEmailData
  | SubscriptionConfirmationEmailData
  | SubscriptionChangeEmailData
  | WritingMilestoneEmailData
  | CollaborationInviteEmailData
  | WeeklyProgressEmailData
  | ExportReadyEmailData
  | AchievementUnlockedEmailData

export interface EmailProvider {
  name: string
  sendEmail(options: EmailOptions & { subject: string; html: string; text: string }): Promise<EmailResult>
  verifyConfiguration(): Promise<boolean>
}

export interface EmailResult {
  id: string
  success: boolean
  error?: string
  provider: string
}

export interface EmailQueueItem {
  id: string
  to: string | string[]
  template: EmailTemplateType
  data: EmailTemplateData
  status: 'pending' | 'processing' | 'sent' | 'failed'
  attempts: number
  error?: string
  scheduledFor?: Date
  sentAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface EmailPreferences {
  userId: string
  marketing: boolean
  progress: boolean
  achievements: boolean
  collaboration: boolean
  newsletter: boolean
  unsubscribeToken: string
  createdAt: Date
  updatedAt: Date
}