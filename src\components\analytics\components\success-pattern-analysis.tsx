'use client'

import { logger } from '@/lib/services/logger'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  TrendingUp,
  BookOpen,
  Target,
  Sparkles,
  Clock,
  Users,
  BarChart3
} from 'lucide-react'

interface SuccessPattern {
  factor: string
  category: string
  successRate: number
  projectCount: number
  insight: string
  icon: React.ReactNode
}

interface SuccessPatternAnalysisProps {
  userId: string
}

export function SuccessPatternAnalysis({ userId }: SuccessPatternAnalysisProps) {
  const [patterns, setPatterns] = useState<SuccessPattern[]>([])
  const [topRecommendation, setTopRecommendation] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSuccessPatterns()
  }, [userId])

  const fetchSuccessPatterns = async () => {
    try {
      const response = await fetch(`/api/analytics/selections/success-patterns?userId=${userId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch success patterns')
      }
      
      const data = await response.json()
      setPatterns(data.patterns || [])
      setTopRecommendation(data.topRecommendation || '')
    } catch (error) {
      logger.error('Failed to fetch success patterns:', error)
      // Show empty state instead of mock data
      setPatterns([])
      setTopRecommendation('')
    } finally {
      setLoading(false)
    }
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      genre: 'bg-purple-100 text-purple-800',
      narrative: 'bg-info-light text-blue-800',
      structure: 'bg-success-light text-green-800',
      behavior: 'bg-orange-100 text-orange-800',
      approach: 'bg-pink-100 text-pink-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-20 w-full" />
        {[1, 2, 3, 4].map((i) => (
          <Skeleton key={i} className="h-24 w-full" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Top Recommendation */}
      {topRecommendation && (
        <Card className="p-4 bg-primary/5 border-primary/20">
          <div className="flex items-start gap-3">
            <TrendingUp className="h-5 w-5 text-primary mt-0.5" />
            <div>
              <h4 className="font-semibold mb-1">Recommended Approach</h4>
              <p className="text-sm text-muted-foreground">{topRecommendation}</p>
            </div>
          </div>
        </Card>
      )}

      {/* Success Patterns */}
      <div className="space-y-4">
        {patterns.map((pattern, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-start gap-4">
              {pattern.icon}
              <div className="flex-1 space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">{pattern.factor}</h4>
                  <Badge className={getCategoryColor(pattern.category)}>
                    {pattern.category}
                  </Badge>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Progress value={pattern.successRate} className="h-2" />
                  </div>
                  <span className="text-sm font-medium">{pattern.successRate}%</span>
                </div>
                <p className="text-sm text-muted-foreground">{pattern.insight}</p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <BarChart3 className="h-3 w-3" />
                  <span>Based on {pattern.projectCount} projects</span>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}