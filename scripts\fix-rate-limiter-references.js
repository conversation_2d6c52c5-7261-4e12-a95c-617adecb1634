#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const glob = require('glob');

async function fixRateLimiterReferences() {
  console.log('🔄 Fixing remaining rate limiter references...');
  
  // Find all TypeScript/TSX files
  const files = glob.sync('src/**/*.{ts,tsx}', {
    ignore: [
      '**/node_modules/**', 
      '**/dist/**', 
      '**/build/**',
      'src/lib/rate-limiter-unified.ts'
    ]
  });
  
  let updatedCount = 0;
  const updates = [];
  
  for (const file of files) {
    try {
      let content = await fs.readFile(file, 'utf-8');
      let hasChanges = false;
      const originalContent = content;
      
      // Fix generalLimiter references
      if (content.includes('generalLimiter')) {
        // Replace generalLimiter with apiLimiter (which exists in unified)
        content = content.replace(/generalLimiter/g, 'apiLimiter');
        hasChanges = true;
      }
      
      // Fix rate limiter usage pattern for new unified API
      // Old: limiter.check(requests, clientIP)
      // New: checkRateLimit(req, config)
      if (content.includes('.limiter.check(') || content.includes('limiter.check(')) {
        // This is more complex, so we'll need to handle it case by case
        // For now, let's at least fix the simple cases
        content = content.replace(
          /const\s*{\s*limiter,\s*requests\s*}\s*=\s*rateLimiters\.(\w+)/g,
          'const rateLimitConfig = rateLimiters.$1'
        );
        hasChanges = true;
      }
      
      // Fix createRateLimitResponse calls with wrong parameters
      if (content.includes('createRateLimitResponse(')) {
        // Fix calls with two parameters (remaining, reset) to single parameter
        content = content.replace(
          /createRateLimitResponse\(\s*[\w.]+\.remaining,\s*[\w.]+\.reset\s*\)/g,
          'createRateLimitResponse()'
        );
        content = content.replace(
          /createRateLimitResponse\(\s*[\w.]+\.reset\s*\)/g,
          'createRateLimitResponse()'
        );
        hasChanges = true;
      }
      
      if (hasChanges && content !== originalContent) {
        await fs.writeFile(file, content);
        updatedCount++;
        updates.push(file);
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log(`\n✅ Fixed ${updatedCount} files`);
  
  if (updates.length > 0) {
    console.log('\n📝 Files updated:');
    updates.forEach(file => console.log(`  - ${file}`));
  }
}

// Run the fix
fixRateLimiterReferences().catch(console.error);