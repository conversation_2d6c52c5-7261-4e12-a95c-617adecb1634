import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

// GET - Fetch analytics for a series
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Verify series ownership
    const { data: series } = await supabase
      .from('series')
      .select('id')
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (!series) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    const service = getSeriesService(true)
    const { analytics, error } = await service.getSeriesAnalytics(seriesId)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch series analytics' }, { status: 500 })
    }

    return NextResponse.json({ analytics })
  } catch (error) {
    logger.error('Error in series analytics GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}