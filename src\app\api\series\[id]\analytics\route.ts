import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getSeriesService } from '@/lib/services/series-service'
import { logger } from '@/lib/services/logger'

// GET - Fetch analytics for a series
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const seriesId = params.id
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify series ownership
    const { data: series } = await supabase
      .from('series')
      .select('id')
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (!series) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 })
    }

    const service = getSeriesService(true)
    const { analytics, error } = await service.getSeriesAnalytics(seriesId)

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch series analytics' }, { status: 500 })
    }

    return NextResponse.json({ analytics })
  } catch (error) {
    logger.error('Error in series analytics GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}