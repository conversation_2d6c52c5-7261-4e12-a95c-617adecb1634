-- Test that the tables are working correctly

-- Test 1: Check writing_sessions structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'writing_sessions'
ORDER BY ordinal_position;

-- Test 2: Check ai_usage_logs structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'ai_usage_logs'
ORDER BY ordinal_position;

-- Test 3: Try a simple insert into writing_sessions (will fail if no auth, but shows structure is OK)
DO $$
BEGIN
    -- This will fail due to RLS, but if we get an RLS error instead of a column error, the table is fine
    INSERT INTO writing_sessions (user_id, project_id, session_date, words_written)
    VALUES (gen_random_uuid(), null, CURRENT_DATE, 100);
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Expected error (likely RLS): %', SQLERRM;
END $$;

-- If you see results from the first two queries and an RLS error (not a column error) from the third,
-- then your tables are set up correctly and you can proceed with the achievement system migration!