import { NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UnifiedResponse } from '@/lib/api/unified-response';
import { VoiceProfileManager } from '@/lib/services/voice-profile-manager';
import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

const voiceProfileManager = new VoiceProfileManager();

// Validation schema
const trainProfileSchema = z.object({
  texts: z.array(z.string().min(100)).min(1),
  source: z.enum(['manual_entry', 'file_upload', 'project_content']).default('manual_entry')
});

export const POST = UnifiedAuthService.withAuth(async (
  request,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params;
    const user = request.user!;
    const supabase = await createTypedServerClient();

    // Parse and validate request body
    const body = await request.json();
    const validationResult = trainProfileSchema.safeParse(body);
    
    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid request data',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400);
    }

    const { texts, source } = validationResult.data;

    // Check if user owns this profile
    const { data: profile, error: checkError } = await supabase
      .from('voice_profiles')
      .select('user_id')
      .eq('id', id)
      .single();

    if (checkError || !profile || profile.user_id !== user.id) {
      return UnifiedResponse.error({
        message: 'Not authorized to train this voice profile',
        code: 'FORBIDDEN'
      }, undefined, 403);
    }

    // Train the profile
    const success = await voiceProfileManager.trainVoiceProfile(
      id,
      texts,
      source
    );

    if (!success) {
      return UnifiedResponse.error({
        message: 'Failed to train voice profile',
        code: 'TRAINING_FAILED'
      }, undefined, 500);
    }

    // Fetch updated profile
    const { data: updatedProfile, error: fetchError } = await supabase
      .from('voice_profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      logger.error('Error fetching updated profile:', fetchError);
      return UnifiedResponse.error({
        message: 'Training completed but failed to fetch updated profile',
        code: 'DATABASE_ERROR',
        details: fetchError
      }, undefined, 500);
    }

    return UnifiedResponse.success({
      profile: updatedProfile,
      trainedSamples: texts.length,
      message: `Successfully trained voice profile with ${texts.length} samples`
    });
  } catch (error) {
    logger.error('Error training voice profile:', error);
    return UnifiedResponse.error({
      message: 'Failed to train voice profile',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500);
  }
});