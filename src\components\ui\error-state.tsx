import * as React from "react"
import { cn } from "@/lib/utils"
import { AlertCircle, RefreshCw, Home, ArrowLeft } from "lucide-react"
import { Button } from "./button"
import { Alert, AlertDescription, AlertTitle } from "./alert"
import { SPACING, TYPOGRAPHY } from "@/lib/config/ui-config"

interface ErrorStateProps {
  error?: Error | string
  title?: string
  description?: string
  retry?: () => void
  reset?: () => void
  goBack?: () => void
  goHome?: () => void
  className?: string
  variant?: "default" | "destructive" | "minimal"
  children?: React.ReactNode
}

export function ErrorState({
  error,
  title = "Something went wrong",
  description,
  retry,
  reset,
  goBack,
  goHome,
  className,
  variant = "default",
  children
}: ErrorStateProps) {
  const errorMessage = error instanceof Error ? error.message : error
  const displayDescription = description || errorMessage || "An unexpected error occurred. Please try again."

  if (variant === "minimal") {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{displayDescription}</AlertDescription>
        {retry && (
          <Button
            variant="outline"
            size="sm"
            onClick={retry}
            className="mt-2"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Try again
          </Button>
        )}
      </Alert>
    )
  }

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center text-center",
        SPACING.PADDING.LG,
        SPACING.SPACE_Y.MD,
        "min-h-[400px]",
        className
      )}
    >
      <div className={cn(
        "rounded-full p-3",
        variant === "destructive" ? "bg-destructive/10" : "bg-muted"
      )}>
        <AlertCircle className={cn(
          "h-8 w-8",
          variant === "destructive" ? "text-destructive" : "text-muted-foreground"
        )} />
      </div>
      
      <div className={cn(SPACING.SPACE_Y.SM, "max-w-md")}>
        <h3 className={cn(TYPOGRAPHY.PRESETS.H3, "text-foreground")}>
          {title}
        </h3>
        
        <p className={cn(TYPOGRAPHY.PRESETS.BODY_SMALL, "text-muted-foreground")}>
          {displayDescription}
        </p>
        
        {process.env.NODE_ENV === "development" && errorMessage && (
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-xs text-muted-foreground hover:text-foreground">
              Error details
            </summary>
            <pre className="mt-2 whitespace-pre-wrap rounded-md bg-muted p-2 text-xs">
              {errorMessage}
            </pre>
          </details>
        )}
      </div>
      
      <div className={cn("flex flex-wrap items-center justify-center", SPACING.GAP.SM)}>
        {retry && (
          <Button
            variant="default"
            onClick={retry}
            size="default"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Try again
          </Button>
        )}
        
        {reset && (
          <Button
            variant={retry ? "outline" : "default"}
            onClick={reset}
            size="default"
          >
            Reset
          </Button>
        )}
        
        {goBack && (
          <Button
            variant="outline"
            onClick={goBack}
            size="default"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go back
          </Button>
        )}
        
        {goHome && (
          <Button
            variant="outline"
            onClick={goHome}
            size="default"
          >
            <Home className="h-4 w-4 mr-2" />
            Go home
          </Button>
        )}
      </div>
      
      {children}
    </div>
  )
}

// Error boundary component
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends React.Component<
  {
    children: React.ReactNode
    fallback?: (error: Error, reset: () => void) => React.ReactNode
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  },
  ErrorBoundaryState
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error caught by boundary:", error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  reset = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.reset)
      }

      return (
        <ErrorState
          error={this.state.error}
          title="Application Error"
          description="The application encountered an unexpected error."
          retry={this.reset}
        />
      )
    }

    return this.props.children
  }
}