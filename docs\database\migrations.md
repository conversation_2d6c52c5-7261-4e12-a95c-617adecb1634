# BookScribe Database Migrations Guide

## Overview

This guide covers BookScribe's database migration strategy, patterns, and best practices for maintaining and evolving the PostgreSQL schema through Supabase.

## Migration System Architecture

### Migration Structure

```
supabase/migrations/
├── 20240101000000_initial_schema.sql
├── 20240115000000_add_series_support.sql
├── 20240201000000_add_collaboration.sql
├── 20240215000000_add_analytics.sql
├── 20240301000000_add_voice_profiles.sql
├── 20240315000000_add_achievements.sql
├── 20240401000000_performance_indexes.sql
├── 20240415000000_add_universe_features.sql
└── 20240501000000_add_word_count_history.sql
```

### Naming Convention
- Format: `YYYYMMDDHHMMSS_description.sql`
- Timestamps ensure execution order
- Descriptive names for clarity
- Use underscores, not hyphens

## Migration Patterns

### 1. Safe Column Addition
```sql
-- Migration: Add new column safely
BEGIN;

-- Add column as nullable first
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS writing_techniques JSONB;

-- Add default value for existing rows
UPDATE projects 
SET writing_techniques = '{}'::jsonb 
WHERE writing_techniques IS NULL;

-- Add NOT NULL constraint after backfill
ALTER TABLE projects 
ALTER COLUMN writing_techniques SET DEFAULT '{}'::jsonb;

COMMIT;

-- Rollback
BEGIN;
ALTER TABLE projects 
DROP COLUMN IF EXISTS writing_techniques;
COMMIT;
```

### 2. Index Creation Pattern
```sql
-- Migration: Add indexes with minimal locking
BEGIN;

-- Create index concurrently (outside transaction)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chapters_project_status 
ON chapters(project_id, status);

-- Verify index is valid
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE indexname = 'idx_chapters_project_status'
  ) THEN
    RAISE EXCEPTION 'Index creation failed';
  END IF;
END $$;

COMMIT;

-- Rollback
DROP INDEX CONCURRENTLY IF EXISTS idx_chapters_project_status;
```

### 3. Table Renaming Pattern
```sql
-- Migration: Rename table safely
BEGIN;

-- Rename table
ALTER TABLE old_table_name RENAME TO new_table_name;

-- Update foreign key constraints
ALTER TABLE referencing_table 
DROP CONSTRAINT old_fk_constraint,
ADD CONSTRAINT new_fk_constraint 
  FOREIGN KEY (column_name) 
  REFERENCES new_table_name(id);

-- Update RLS policies
DROP POLICY IF EXISTS "old_policy" ON new_table_name;
CREATE POLICY "new_policy" ON new_table_name
  FOR SELECT USING (auth.uid() = user_id);

COMMIT;
```

### 4. Data Type Change Pattern
```sql
-- Migration: Change column type safely
BEGIN;

-- Add temporary column
ALTER TABLE chapters 
ADD COLUMN word_count_new INTEGER;

-- Copy and transform data
UPDATE chapters 
SET word_count_new = CAST(word_count AS INTEGER);

-- Swap columns
ALTER TABLE chapters 
DROP COLUMN word_count,
ALTER COLUMN word_count_new RENAME TO word_count;

-- Add constraints
ALTER TABLE chapters 
ADD CONSTRAINT word_count_positive CHECK (word_count >= 0);

COMMIT;
```

### 5. JSONB Schema Evolution
```sql
-- Migration: Evolve JSONB structure
BEGIN;

-- Add new field to JSONB
UPDATE projects 
SET settings = jsonb_set(
  COALESCE(settings, '{}'::jsonb),
  '{advanced_features}',
  '{"enabled": false}'::jsonb,
  true
)
WHERE settings IS NOT NULL 
  AND settings->>'advanced_features' IS NULL;

-- Migrate old structure to new
UPDATE projects 
SET settings = jsonb_set(
  settings,
  '{writing_style}',
  to_jsonb(settings->>'style'),
  true
)
WHERE settings->>'style' IS NOT NULL;

-- Remove deprecated fields
UPDATE projects 
SET settings = settings - 'style'
WHERE settings ? 'style';

COMMIT;
```

## Complex Migration Examples

### 1. Adding Series Support
```sql
-- Migration: 20240115000000_add_series_support.sql
BEGIN;

-- Create series table
CREATE TABLE IF NOT EXISTS series (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  planned_books INTEGER,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Add series support to projects
ALTER TABLE projects 
ADD COLUMN series_id UUID REFERENCES series(id),
ADD COLUMN series_order INTEGER;

-- Create index for series queries
CREATE INDEX idx_projects_series ON projects(series_id, series_order);

-- Add RLS policies
ALTER TABLE series ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own series" ON series
  FOR ALL USING (auth.uid() = user_id);

-- Create trigger for updated_at
CREATE TRIGGER update_series_updated_at
  BEFORE UPDATE ON series
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

COMMIT;
```

### 2. Performance Optimization Migration
```sql
-- Migration: 20240401000000_performance_indexes.sql
BEGIN;

-- Analyze table statistics first
ANALYZE projects;
ANALYZE chapters;
ANALYZE characters;

-- Create covering indexes for common queries
CREATE INDEX CONCURRENTLY idx_projects_user_status_updated 
ON projects(user_id, status, updated_at DESC)
INCLUDE (title, current_word_count);

CREATE INDEX CONCURRENTLY idx_chapters_project_number 
ON chapters(project_id, chapter_number)
INCLUDE (title, actual_word_count, status);

-- Partial indexes for filtered queries
CREATE INDEX CONCURRENTLY idx_active_collaborations 
ON collaboration_sessions(project_id)
WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_recent_ai_generations 
ON ai_generations(user_id, created_at DESC)
WHERE created_at > CURRENT_DATE - INTERVAL '30 days';

-- JSONB indexes
CREATE INDEX CONCURRENTLY idx_projects_settings_genre 
ON projects USING gin((settings->'genre'));

-- Function-based indexes
CREATE INDEX CONCURRENTLY idx_chapters_word_count_range 
ON chapters(project_id, actual_word_count)
WHERE actual_word_count BETWEEN 2000 AND 5000;

COMMIT;
```

### 3. Adding Analytics Tables
```sql
-- Migration: 20240215000000_add_analytics.sql
BEGIN;

-- Create analytics schema
CREATE SCHEMA IF NOT EXISTS analytics;

-- Writing patterns table
CREATE TABLE analytics.writing_patterns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Time patterns
  peak_hours INTEGER[],
  preferred_days INTEGER[],
  average_session_duration INTEGER,
  
  -- Productivity metrics
  words_per_hour FLOAT,
  consistency_score FLOAT,
  
  -- Calculated weekly
  calculated_at TIMESTAMP DEFAULT NOW(),
  week_start DATE NOT NULL,
  
  UNIQUE(user_id, week_start)
);

-- Content quality trends
CREATE TABLE analytics.quality_trends (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  
  -- Quality metrics over time
  metrics JSONB NOT NULL,
  
  -- Aggregation period
  period_type VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'monthly'
  period_start DATE NOT NULL,
  
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(project_id, period_type, period_start)
);

-- Create materialized view for dashboard
CREATE MATERIALIZED VIEW analytics.user_dashboard AS
WITH recent_sessions AS (
  SELECT 
    user_id,
    COUNT(*) as session_count,
    SUM(words_written) as total_words,
    AVG(duration_seconds) as avg_duration
  FROM writing_sessions
  WHERE started_at > CURRENT_DATE - INTERVAL '30 days'
  GROUP BY user_id
),
project_stats AS (
  SELECT 
    user_id,
    COUNT(*) as project_count,
    SUM(current_word_count) as total_project_words
  FROM projects
  WHERE status != 'archived'
  GROUP BY user_id
)
SELECT 
  u.id as user_id,
  COALESCE(rs.session_count, 0) as recent_sessions,
  COALESCE(rs.total_words, 0) as recent_words,
  COALESCE(rs.avg_duration, 0) as avg_session_duration,
  COALESCE(ps.project_count, 0) as active_projects,
  COALESCE(ps.total_project_words, 0) as total_words
FROM auth.users u
LEFT JOIN recent_sessions rs ON rs.user_id = u.id
LEFT JOIN project_stats ps ON ps.user_id = u.id;

-- Create indexes
CREATE INDEX idx_analytics_patterns_user ON analytics.writing_patterns(user_id);
CREATE INDEX idx_analytics_trends_project ON analytics.quality_trends(project_id);
CREATE UNIQUE INDEX idx_dashboard_user ON analytics.user_dashboard(user_id);

-- Refresh function
CREATE OR REPLACE FUNCTION refresh_user_dashboard()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY analytics.user_dashboard;
END;
$$ LANGUAGE plpgsql;

COMMIT;
```

## Migration Testing

### 1. Test Migration Script
```sql
-- test_migration.sql
-- Run in transaction to test without committing

BEGIN;

-- Run your migration
\i 20240501000000_your_migration.sql

-- Test queries
SELECT COUNT(*) FROM your_new_table;
SELECT * FROM your_new_table LIMIT 5;

-- Verify constraints
\d+ your_new_table

-- Test rollback
ROLLBACK;
```

### 2. Performance Testing
```sql
-- Before migration
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM projects 
WHERE user_id = 'test-uuid' 
  AND status = 'active';

-- Run migration
\i performance_migration.sql

-- After migration
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM projects 
WHERE user_id = 'test-uuid' 
  AND status = 'active';

-- Compare execution times
```

## Best Practices

### 1. Migration Safety
- Always use transactions (BEGIN/COMMIT)
- Test on staging environment first
- Include rollback procedures
- Avoid locking operations during peak hours
- Use CONCURRENTLY for index creation

### 2. Data Integrity
```sql
-- Always validate data before constraints
-- Check for nulls before NOT NULL
SELECT COUNT(*) FROM table WHERE column IS NULL;

-- Check for duplicates before UNIQUE
SELECT column, COUNT(*) 
FROM table 
GROUP BY column 
HAVING COUNT(*) > 1;

-- Verify foreign key references
SELECT t1.id 
FROM table1 t1 
LEFT JOIN table2 t2 ON t1.fk_id = t2.id 
WHERE t2.id IS NULL;
```

### 3. JSONB Migrations
```sql
-- Validate JSONB structure
SELECT COUNT(*) 
FROM table 
WHERE NOT (jsonb_typeof(data->'field') = 'object');

-- Safe JSONB updates
UPDATE table 
SET data = jsonb_set(
  data,
  '{new_field}',
  '"default_value"'::jsonb,
  true
)
WHERE data IS NOT NULL
  AND data->>'new_field' IS NULL;
```

### 4. Performance Considerations
```sql
-- Check table size before operations
SELECT 
  pg_size_pretty(pg_total_relation_size('table_name')) as total_size,
  pg_size_pretty(pg_relation_size('table_name')) as table_size,
  pg_size_pretty(pg_indexes_size('table_name')) as indexes_size;

-- Monitor long-running migrations
SELECT 
  pid,
  now() - pg_stat_activity.query_start AS duration,
  query
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
  AND state = 'active';
```

## Rollback Procedures

### 1. Simple Rollback
```sql
-- Rollback: 20240501000000_add_feature.sql
BEGIN;

-- Reverse operations in opposite order
DROP INDEX IF EXISTS new_index;
ALTER TABLE table_name DROP COLUMN IF EXISTS new_column;
DROP TABLE IF EXISTS new_table CASCADE;

COMMIT;
```

### 2. Data-Preserving Rollback
```sql
-- Backup data before dropping
BEGIN;

-- Create backup table
CREATE TABLE backup_data AS 
SELECT id, important_column 
FROM table_to_modify;

-- Perform rollback
ALTER TABLE table_to_modify 
DROP COLUMN important_column;

-- Restore if needed
-- ALTER TABLE table_to_modify 
-- ADD COLUMN important_column TYPE;
-- UPDATE table_to_modify t
-- SET important_column = b.important_column
-- FROM backup_data b
-- WHERE t.id = b.id;

COMMIT;
```

## Monitoring Migrations

### 1. Migration History Table
```sql
CREATE TABLE IF NOT EXISTS migration_history (
  id SERIAL PRIMARY KEY,
  filename VARCHAR(255) NOT NULL UNIQUE,
  executed_at TIMESTAMP DEFAULT NOW(),
  execution_time_ms INTEGER,
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  rolled_back BOOLEAN DEFAULT false
);
```

### 2. Migration Metrics
```sql
-- Track migration performance
INSERT INTO migration_history 
  (filename, execution_time_ms, success, error_message)
VALUES 
  ('20240501000000_migration.sql', 1234, true, null);

-- Query migration history
SELECT 
  filename,
  executed_at,
  execution_time_ms,
  success,
  CASE 
    WHEN execution_time_ms > 5000 THEN 'SLOW'
    WHEN execution_time_ms > 1000 THEN 'MODERATE'
    ELSE 'FAST'
  END as performance
FROM migration_history
ORDER BY executed_at DESC;
```

## Future Migration Planning

### 1. Scheduled Migrations
- Large data transformations during off-peak
- Index rebuilds during maintenance windows
- Batch processing for huge tables

### 2. Zero-Downtime Migrations
- Use blue-green deployments
- Implement feature flags
- Gradual rollout strategies
- Backward-compatible changes

### 3. Migration Automation
```bash
#!/bin/bash
# migrate.sh

# Generate migration file
TIMESTAMP=$(date +%Y%m%d%H%M%S)
DESCRIPTION=$1
FILENAME="${TIMESTAMP}_${DESCRIPTION}.sql"

# Create migration template
cat > "supabase/migrations/$FILENAME" << EOF
-- Migration: $DESCRIPTION
-- Generated: $(date)

BEGIN;

-- Add your migration SQL here

COMMIT;

-- Rollback
-- BEGIN;
-- -- Add rollback SQL here
-- COMMIT;
EOF

echo "Created migration: $FILENAME"
```