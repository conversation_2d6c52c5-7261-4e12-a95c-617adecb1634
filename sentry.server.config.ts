// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

const SENTRY_DSN = process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN;
const isProduction = process.env.NODE_ENV === 'production';

Sentry.init({
  dsn: SENTRY_DSN,
  
  // Performance Monitoring
  tracesSampleRate: isProduction ? 0.1 : 1.0, // 10% in production, 100% in development
  
  // Disable in development unless debugging
  enabled: isProduction || process.env.SENTRY_DEBUG === 'true',
  
  environment: process.env.NODE_ENV,
  
  // Set release
  release: process.env.NEXT_PUBLIC_APP_VERSION,
  
  // Server-specific error filtering
  ignoreErrors: [
    // Ignore expected Next.js errors
    'NEXT_NOT_FOUND',
    'NEXT_REDIRECT',
    // Ignore network errors
    'ECONNRESET',
    'ECONNREFUSED',
    'ETIMEDOUT',
    'EPIPE',
    'ENOTFOUND',
  ],
  
  beforeSend(event, hint) {
    // Don't send events in development unless explicitly enabled
    if (!isProduction && process.env.SENTRY_DEBUG !== 'true') {
      return null;
    }
    
    // Remove sensitive server data
    if (event.extra) {
      delete event.extra.process;
      delete event.extra.os;
    }
    
    // Remove sensitive headers
    if (event.request?.headers) {
      const sensitiveHeaders = [
        'authorization',
        'cookie',
        'x-api-key',
        'x-supabase-auth',
        'x-forwarded-for',
        'x-real-ip',
      ];
      
      sensitiveHeaders.forEach(header => {
        if (event.request?.headers?.[header]) {
          event.request.headers[header] = '[Filtered]';
        }
      });
    }
    
    // Remove sensitive query parameters
    if (event.request?.query_string) {
      const sensitiveParams = ['token', 'apiKey', 'api_key', 'password'];
      const queryParams = new URLSearchParams(event.request.query_string);
      
      sensitiveParams.forEach(param => {
        if (queryParams.has(param)) {
          queryParams.set(param, '[Filtered]');
        }
      });
      
      event.request.query_string = queryParams.toString();
    }
    
    return event;
  },
  
  // Integrations
  integrations: [
    // Automatically instrument Node.js libraries and frameworks
    ...Sentry.autoDiscoverNodePerformanceMonitoringIntegrations(),
  ],
});
