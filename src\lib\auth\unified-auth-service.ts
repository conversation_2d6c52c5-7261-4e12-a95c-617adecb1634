import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { APIError, handleAPIError } from '@/lib/api/error-handler'
import { Database } from '@/lib/db/types'

type User = Database['public']['Tables']['profiles']['Row']

export interface AuthenticatedRequest extends NextRequest {
  user?: User
}

/**
 * Unified Authentication Service
 * Consolidates all authentication logic in one place
 */
export class UnifiedAuthService {
  /**
   * Authenticate a user from request headers
   */
  static async authenticateUser(request: NextRequest): Promise<User | null> {
    try {
      const authHeader = request.headers.get('authorization')
      if (!authHeader?.startsWith('Bearer ')) {
        return null
      }

      const token = authHeader.substring(7)
      const supabase = await createServerClient()
      
      // Verify the JWT token
      const { data: { user }, error } = await supabase.auth.getUser(token)
      
      if (error || !user) {
        logger.error('Authentication failed', error)
        return null
      }

      // Get full user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError || !profile) {
        logger.error('Failed to fetch user profile', profileError)
        return null
      }

      return profile
    } catch (error) {
      logger.error('Authentication error', error)
      return null
    }
  }

  /**
   * Authenticate and verify admin access
   */
  static async authenticateAdmin(request: NextRequest): Promise<User | null> {
    const user = await this.authenticateUser(request)
    
    if (!user) {
      return null
    }

    // Check admin status in user_roles or profile metadata
    const supabase = await createServerClient()
    const { data: roles } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .eq('role', 'admin')
      .single()

    if (!roles) {
      logger.warn(`Non-admin user ${user.id} attempted admin access`)
      return null
    }

    return user
  }

  /**
   * Authenticate and verify project access
   */
  static async authenticateProjectAccess(
    request: NextRequest, 
    projectId: string
  ): Promise<User | null> {
    const user = await this.authenticateUser(request)
    
    if (!user) {
      return null
    }

    const supabase = await createServerClient()
    
    // Check if user owns the project
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (project) {
      return user
    }

    // Check if user has team access
    const { data: teamMember } = await supabase
      .from('project_team_members')
      .select('role')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!teamMember) {
      logger.warn(`User ${user.id} attempted unauthorized access to project ${projectId}`)
      return null
    }

    return user
  }

  /**
   * Authenticate and verify series access
   */
  static async authenticateSeriesAccess(
    request: NextRequest,
    seriesId: string
  ): Promise<User | null> {
    const user = await this.authenticateUser(request)
    
    if (!user) {
      return null
    }

    const supabase = await createServerClient()
    
    // Check if user owns the series
    const { data: series } = await supabase
      .from('series')
      .select('id')
      .eq('id', seriesId)
      .eq('user_id', user.id)
      .single()

    if (!series) {
      logger.warn(`User ${user.id} attempted unauthorized access to series ${seriesId}`)
      return null
    }

    return user
  }

  /**
   * Middleware wrapper for authentication
   */
  static withAuth(
    handler: (request: AuthenticatedRequest) => Promise<NextResponse>
  ) {
    return async (request: NextRequest): Promise<NextResponse> => {
      try {
        const user = await this.authenticateUser(request)
        
        if (!user) {
          return handleAPIError(
            new APIError('Authentication required', 401)
          )
        }

        const authenticatedRequest = request as AuthenticatedRequest
        authenticatedRequest.user = user
        
        return handler(authenticatedRequest)
      } catch (error) {
        logger.error('Auth middleware error', error)
        return handleAPIError(
          new APIError('Authentication failed', 500)
        )
      }
    }
  }

  /**
   * Middleware wrapper for admin authentication
   */
  static withAdmin(
    handler: (request: AuthenticatedRequest) => Promise<NextResponse>
  ) {
    return async (request: NextRequest): Promise<NextResponse> => {
      try {
        const user = await this.authenticateAdmin(request)
        
        if (!user) {
          return handleAPIError(
            new APIError('Admin access required', 403)
          )
        }

        const authenticatedRequest = request as AuthenticatedRequest
        authenticatedRequest.user = user
        
        return handler(authenticatedRequest)
      } catch (error) {
        logger.error('Admin auth middleware error', error)
        return handleAPIError(
          new APIError('Authentication failed', 500)
        )
      }
    }
  }

  /**
   * Middleware wrapper for project access
   */
  static withProjectAccess(
    handler: (request: AuthenticatedRequest, context: any) => Promise<NextResponse>
  ) {
    return async (
      request: NextRequest,
      context: { params: { id: string } }
    ): Promise<NextResponse> => {
      try {
        const projectId = context.params.id
        const user = await this.authenticateProjectAccess(request, projectId)
        
        if (!user) {
          return handleAPIError(
            new APIError('Project access denied', 403)
          )
        }

        const authenticatedRequest = request as AuthenticatedRequest
        authenticatedRequest.user = user
        
        return handler(authenticatedRequest, context)
      } catch (error) {
        logger.error('Project auth middleware error', error)
        return handleAPIError(
          new APIError('Authentication failed', 500)
        )
      }
    }
  }

  /**
   * Middleware wrapper for series access
   */
  static withSeriesAccess(
    handler: (request: AuthenticatedRequest, context: any) => Promise<NextResponse>
  ) {
    return async (
      request: NextRequest,
      context: { params: { id: string } }
    ): Promise<NextResponse> => {
      try {
        const seriesId = context.params.id
        const user = await this.authenticateSeriesAccess(request, seriesId)
        
        if (!user) {
          return handleAPIError(
            new APIError('Series access denied', 403)
          )
        }

        const authenticatedRequest = request as AuthenticatedRequest
        authenticatedRequest.user = user
        
        return handler(authenticatedRequest, context)
      } catch (error) {
        logger.error('Series auth middleware error', error)
        return handleAPIError(
          new APIError('Authentication failed', 500)
        )
      }
    }
  }
}