'use client'

import { useState, useEffect } from 'react'
import { UniverseManager } from '@/components/universe/universe-manager'
import { useAuth } from '@/contexts/auth-context'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Loader2, Globe, BookOpen, Users, Link2 } from 'lucide-react'
import Link from 'next/link'

interface Universe {
  id: string
  name: string
  description?: string
  rules: Record<string, unknown>
  created_at: string
  series?: Array<{
    id: string
    title: string
    description?: string
  }>
  timeline_events?: Array<{
    id: string
    event_name: string
    description: string
    event_date?: string
    relative_date?: string
    event_type: string
    importance: string
  }>
}

export default function UniversesPage() {
  const { user } = useAuth()
  const [selectedUniverse, setSelectedUniverse] = useState<Universe | null>(null)
  const [universeStats, setUniverseStats] = useState({
    totalUniverses: 0,
    totalSeries: 0,
    totalEvents: 0
  })

  const handleUniverseSelect = (universe: Universe) => {
    setSelectedUniverse(universe)
  }

  const calculateStats = (universes: Universe[]) => {
    const stats = {
      totalUniverses: universes.length,
      totalSeries: universes.reduce((sum, u) => sum + (u.series?.length || 0), 0),
      totalEvents: universes.reduce((sum, u) => sum + (u.timeline_events?.length || 0), 0)
    }
    setUniverseStats(stats)
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container-wide mx-auto p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Universe Management</h1>
          <p className="text-muted-foreground mt-1">
            Create and manage shared universes for your interconnected series
          </p>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Universes</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{universeStats.totalUniverses}</div>
            <p className="text-xs text-muted-foreground">
              Shared worlds you've created
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connected Series</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{universeStats.totalSeries}</div>
            <p className="text-xs text-muted-foreground">
              Series across all universes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Timeline Events</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{universeStats.totalEvents}</div>
            <p className="text-xs text-muted-foreground">
              Cross-universe events
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Selected Universe Quick Info */}
      {selectedUniverse && (
        <Card className="border-l-4 border-l-primary">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  {selectedUniverse.name}
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {selectedUniverse.description || 'No description'}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {selectedUniverse.series?.length || 0} Series
                </Badge>
                <Badge variant="outline">
                  {selectedUniverse.timeline_events?.length || 0} Events
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
              {selectedUniverse.series && selectedUniverse.series.length > 0 && (
                <div className="flex items-center gap-2">
                  <Link2 className="h-4 w-4" />
                  <span className="text-sm">Connected to:</span>
                  <div className="flex gap-1">
                    {selectedUniverse.series.slice(0, 3).map((series) => (
                      <Link key={series.id} href={`/series/${series.id}`}>
                        <Badge variant="secondary" className="hover:bg-secondary/80 cursor-pointer">
                          {series.title}
                        </Badge>
                      </Link>
                    ))}
                    {selectedUniverse.series.length > 3 && (
                      <Badge variant="outline">
                        +{selectedUniverse.series.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Universe Manager Component */}
      <UniverseManager 
        userId={user.id} 
        onUniverseSelect={handleUniverseSelect}
      />

      {/* Help Section */}
      <Card className="bg-muted/50">
        <CardHeader>
          <CardTitle className="text-lg">About Shared Universes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>
              <strong>Shared Universes</strong> allow you to create interconnected worlds where multiple series can coexist. 
              Characters, locations, and events can be shared across different book series within the same universe.
            </p>
            <p>
              <strong>Key Features:</strong>
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Share characters between series with different roles</li>
              <li>Create universe-wide timeline events</li>
              <li>Maintain consistency across all connected stories</li>
              <li>Define universe-specific rules and limitations</li>
            </ul>
            <div className="flex gap-2 mt-4">
              <Link href="/series">
                <Button variant="outline" size="sm">
                  Manage Series
                </Button>
              </Link>
              <Link href="/help">
                <Button variant="outline" size="sm">
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}