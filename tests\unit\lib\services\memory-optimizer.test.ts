import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { MemoryOptimizer } from '@/lib/services/memory-optimizer';
import { createClient } from '@/lib/supabase/server';

// Mock dependencies
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/services/logger');

describe('MemoryOptimizer', () => {
  let mockSupabase: any;
  let memoryOptimizer: MemoryOptimizer;

  beforeEach(() => {
    jest.clearAllMocks();

    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };

    (createClient as jest.Mock).mockReturnValue(mockSupabase);
    memoryOptimizer = new MemoryOptimizer();
  });

  describe('getMemoryStats', () => {
    it('should return memory statistics for a project', async () => {
      const mockChapters = [
        { id: '1', content: 'Chapter 1 content...', word_count: 1000 },
        { id: '2', content: 'Chapter 2 content...', word_count: 1500 },
      ];

      const mockCharacters = [
        { id: '1', name: 'John', backstory: 'Long backstory...', personality: 'Complex...' },
        { id: '2', name: 'Jane', backstory: 'Another story...', personality: 'Detailed...' },
      ];

      mockSupabase.select.mockImplementation(() => {
        if (mockSupabase.from.mock.calls[0][0] === 'chapters') {
          return { data: mockChapters, error: null };
        }
        if (mockSupabase.from.mock.calls[0][0] === 'characters') {
          return { data: mockCharacters, error: null };
        }
        return { data: [], error: null };
      });

      const stats = await memoryOptimizer.getMemoryStats('project-123');

      expect(stats).toEqual({
        totalTokens: expect.any(Number),
        breakdown: {
          chapters: expect.any(Number),
          characters: expect.any(Number),
          worldBuilding: expect.any(Number),
          plotElements: expect.any(Number),
          references: expect.any(Number),
        },
        recommendations: expect.any(Array),
      });
    });

    it('should provide optimization recommendations', async () => {
      // Mock large content that exceeds thresholds
      const largeChapters = Array(50).fill(null).map((_, i) => ({
        id: `ch-${i}`,
        content: 'Very long chapter content...'.repeat(100),
        word_count: 5000,
      }));

      mockSupabase.select.mockResolvedValue({ data: largeChapters, error: null });

      const stats = await memoryOptimizer.getMemoryStats('project-123');

      expect(stats.recommendations).toContainEqual(
        expect.objectContaining({
          type: 'archive-old-chapters',
          priority: 'high',
        })
      );
    });
  });

  describe('compressContext', () => {
    it('should compress context by summarizing old content', async () => {
      const context = {
        chapters: [
          { id: '1', content: 'Long chapter content that needs compression...'.repeat(50) },
          { id: '2', content: 'Another long chapter...'.repeat(50) },
        ],
        characters: [
          { id: '1', backstory: 'Detailed backstory...'.repeat(20) },
        ],
      };

      const compressed = await memoryOptimizer.compressContext(context);

      expect(compressed.chapters[0].content).toContain('[SUMMARIZED]');
      expect(compressed.chapters[0].content.length).toBeLessThan(context.chapters[0].content.length);
    });

    it('should preserve recent content uncompressed', async () => {
      const recentContent = 'This is recent content that should not be compressed';
      const context = {
        chapters: [
          { id: '1', content: recentContent, created_at: new Date().toISOString() },
        ],
      };

      const compressed = await memoryOptimizer.compressContext(context);

      expect(compressed.chapters[0].content).toBe(recentContent);
      expect(compressed.chapters[0].content).not.toContain('[SUMMARIZED]');
    });

    it('should maintain essential plot points during compression', async () => {
      const context = {
        chapters: [
          { 
            id: '1', 
            content: 'The hero discovers the magical sword. This changes everything. More filler content...',
            plot_points: ['hero finds sword', 'magical discovery'],
          },
        ],
      };

      const compressed = await memoryOptimizer.compressContext(context);

      expect(compressed.chapters[0].content).toContain('magical sword');
      expect(compressed.chapters[0].content).toContain('hero discovers');
    });
  });

  describe('mergeContexts', () => {
    it('should merge multiple contexts without duplication', () => {
      const context1 = {
        chapters: [{ id: '1', content: 'Chapter 1' }],
        characters: [{ id: 'char-1', name: 'John' }],
      };

      const context2 = {
        chapters: [
          { id: '1', content: 'Chapter 1' }, // Duplicate
          { id: '2', content: 'Chapter 2' },
        ],
        characters: [{ id: 'char-2', name: 'Jane' }],
      };

      const merged = memoryOptimizer.mergeContexts([context1, context2]);

      expect(merged.chapters).toHaveLength(2);
      expect(merged.characters).toHaveLength(2);
      expect(merged.chapters.map(c => c.id)).toEqual(['1', '2']);
    });

    it('should prioritize newer versions when merging', () => {
      const context1 = {
        chapters: [{ id: '1', content: 'Old version', updated_at: '2023-01-01' }],
      };

      const context2 = {
        chapters: [{ id: '1', content: 'New version', updated_at: '2023-06-01' }],
      };

      const merged = memoryOptimizer.mergeContexts([context1, context2]);

      expect(merged.chapters[0].content).toBe('New version');
    });
  });

  describe('optimizeForAgent', () => {
    it('should filter context based on agent type', async () => {
      const fullContext = {
        chapters: Array(20).fill(null).map((_, i) => ({ id: `ch-${i}`, content: 'Chapter content' })),
        characters: Array(10).fill(null).map((_, i) => ({ id: `char-${i}`, name: `Character ${i}` })),
        worldBuilding: { locations: Array(15).fill(null).map((_, i) => ({ id: `loc-${i}` })) },
      };

      // Character Developer agent should get all characters but limited chapters
      const characterContext = await memoryOptimizer.optimizeForAgent(
        fullContext,
        'character-developer'
      );

      expect(characterContext.characters).toHaveLength(10); // All characters
      expect(characterContext.chapters.length).toBeLessThan(20); // Limited chapters

      // Chapter Planner should get recent chapters and relevant characters
      const chapterContext = await memoryOptimizer.optimizeForAgent(
        fullContext,
        'chapter-planner'
      );

      expect(chapterContext.chapters.length).toBeGreaterThan(0);
      expect(chapterContext.chapters.length).toBeLessThanOrEqual(10); // Recent limit
    });

    it('should include agent-specific relevant data', async () => {
      const context = {
        chapters: [{ id: '1', content: 'Chapter with dialogue' }],
        characters: [
          { id: '1', name: 'John', voice_profile: { tone: 'formal', patterns: ['always says indeed'] } },
        ],
      };

      const writingContext = await memoryOptimizer.optimizeForAgent(
        context,
        'writing-agent'
      );

      // Writing agent should get voice profiles for dialogue
      expect(writingContext.characters[0].voice_profile).toBeDefined();
    });
  });

  describe('archiveOldContent', () => {
    it('should move old content to archive', async () => {
      const oldChapters = [
        { id: '1', content: 'Old content', created_at: '2022-01-01' },
        { id: '2', content: 'Very old content', created_at: '2021-01-01' },
      ];

      mockSupabase.select.mockResolvedValue({ data: oldChapters, error: null });
      mockSupabase.insert.mockResolvedValue({ error: null });
      mockSupabase.delete.mockResolvedValue({ error: null });

      await memoryOptimizer.archiveOldContent('project-123');

      // Should insert into archive
      expect(mockSupabase.from).toHaveBeenCalledWith('archived_content');
      expect(mockSupabase.insert).toHaveBeenCalled();

      // Should delete from active content
      expect(mockSupabase.delete).toHaveBeenCalled();
    });

    it('should keep summaries of archived content', async () => {
      const contentToArchive = {
        id: '1',
        content: 'This is a very long chapter that will be archived...'.repeat(100),
        created_at: '2021-01-01',
      };

      mockSupabase.select.mockResolvedValue({ data: [contentToArchive], error: null });

      await memoryOptimizer.archiveOldContent('project-123');

      const insertCall = mockSupabase.insert.mock.calls[0][0];
      expect(insertCall[0].summary).toBeDefined();
      expect(insertCall[0].summary.length).toBeLessThan(contentToArchive.content.length);
    });
  });

  describe('calculateTokenUsage', () => {
    it('should estimate token count for text', () => {
      const text = 'This is a sample text for token counting.';
      const tokens = memoryOptimizer.calculateTokenUsage(text);

      expect(tokens).toBeGreaterThan(0);
      expect(tokens).toBeLessThan(text.length); // Tokens should be less than character count
    });

    it('should handle different text formats', () => {
      const plainText = 'Simple text';
      const jsonText = JSON.stringify({ complex: { nested: { object: 'value' } } });
      const codeText = 'function example() { return "code"; }';

      const plainTokens = memoryOptimizer.calculateTokenUsage(plainText);
      const jsonTokens = memoryOptimizer.calculateTokenUsage(jsonText);
      const codeTokens = memoryOptimizer.calculateTokenUsage(codeText);

      expect(plainTokens).toBeGreaterThan(0);
      expect(jsonTokens).toBeGreaterThan(0);
      expect(codeTokens).toBeGreaterThan(0);
    });
  });

  describe('getOptimizationSettings', () => {
    it('should return user-specific optimization settings', async () => {
      const mockSettings = {
        auto_archive_days: 90,
        compression_threshold: 50000,
        max_context_tokens: 100000,
        preserve_plot_points: true,
      };

      mockSupabase.single.mockResolvedValue({ data: mockSettings, error: null });

      const settings = await memoryOptimizer.getOptimizationSettings('user-123');

      expect(settings).toEqual(mockSettings);
    });

    it('should return default settings if user has none', async () => {
      mockSupabase.single.mockResolvedValue({ data: null, error: { code: 'PGRST116' } });

      const settings = await memoryOptimizer.getOptimizationSettings('user-123');

      expect(settings).toEqual({
        auto_archive_days: 180,
        compression_threshold: 100000,
        max_context_tokens: 150000,
        preserve_plot_points: true,
      });
    });
  });

  describe('performance optimization', () => {
    it('should cache frequently accessed contexts', async () => {
      const context = { chapters: [{ id: '1', content: 'Cached content' }] };

      // First call - should hit database
      await memoryOptimizer.getOptimizedContext('project-123');
      expect(mockSupabase.from).toHaveBeenCalled();

      // Reset mock
      jest.clearAllMocks();

      // Second call within cache window - should use cache
      await memoryOptimizer.getOptimizedContext('project-123');
      expect(mockSupabase.from).not.toHaveBeenCalled();
    });

    it('should invalidate cache on content updates', async () => {
      // Warm cache
      await memoryOptimizer.getOptimizedContext('project-123');
      
      // Update content
      await memoryOptimizer.updateContext('project-123', { 
        chapters: [{ id: '2', content: 'New chapter' }] 
      });

      // Should invalidate cache
      jest.clearAllMocks();
      await memoryOptimizer.getOptimizedContext('project-123');
      expect(mockSupabase.from).toHaveBeenCalled();
    });
  });
});