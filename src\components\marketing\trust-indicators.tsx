import { Shield, Lock, RefreshCw, Download, Star, Award } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TrustIndicator {
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
}

const indicators: TrustIndicator[] = [
  {
    icon: Shield,
    title: '30-Day Guarantee',
    description: 'Full refund, no questions asked'
  },
  {
    icon: Lock,
    title: 'Bank-Level Security',
    description: 'Your work is encrypted and private'
  },
  {
    icon: RefreshCw,
    title: 'Cancel Anytime',
    description: 'No contracts or hidden fees'
  },
  {
    icon: Download,
    title: 'Export Everything',
    description: 'Your work is always yours'
  }
]

interface TrustIndicatorsProps {
  className?: string
  variant?: 'default' | 'compact' | 'inline'
}

export function TrustIndicators({ className, variant = 'default' }: TrustIndicatorsProps) {
  if (variant === 'inline') {
    return (
      <div className={cn('flex flex-wrap items-center gap-4 text-sm', className)}>
        {indicators.slice(0, 2).map((indicator, index) => {
          const Icon = indicator.icon
          return (
            <div key={index} className="flex items-center gap-2 text-muted-foreground">
              <Icon className="w-4 h-4 text-primary" />
              <span>{indicator.title}</span>
            </div>
          )
        })}
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={cn('grid grid-cols-2 gap-3', className)}>
        {indicators.map((indicator, index) => {
          const Icon = indicator.icon
          return (
            <div
              key={index}
              className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 border border-border/50"
            >
              <Icon className="w-5 h-5 text-primary shrink-0" />
              <div>
                <p className="text-sm font-medium">{indicator.title}</p>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-4 gap-4', className)}>
      {indicators.map((indicator, index) => {
        const Icon = indicator.icon
        return (
          <div
            key={index}
            className="flex flex-col items-center text-center p-4 rounded-lg hover:bg-muted/50 transition-colors"
          >
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-3">
              <Icon className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold text-sm mb-1">{indicator.title}</h3>
            <p className="text-xs text-muted-foreground">{indicator.description}</p>
          </div>
        )
      })}
    </div>
  )
}

export function RatingBadge({ className }: { className?: string }) {
  return (
    <div className={cn(
      'inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-primary/10 border border-primary/20',
      className
    )}>
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star key={i} className="w-3.5 h-3.5 fill-primary text-primary" />
        ))}
      </div>
      <span className="text-sm font-medium text-primary">4.9/5</span>
      <span className="text-xs text-muted-foreground">(2,847 reviews)</span>
    </div>
  )
}

export function AwardBadges({ className }: { className?: string }) {
  return (
    <div className={cn('flex flex-wrap items-center gap-4', className)}>
      <div className="flex items-center gap-2 text-sm">
        <Award className="w-5 h-5 text-primary" />
        <span className="font-medium">Best Writing Tool 2024</span>
      </div>
      <div className="flex items-center gap-2 text-sm">
        <Award className="w-5 h-5 text-primary" />
        <span className="font-medium">Editor's Choice</span>
      </div>
    </div>
  )
}