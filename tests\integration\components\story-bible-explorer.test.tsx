import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { StoryBibleExplorer } from '@/components/story-bible/story-bible-explorer'
import { useStoryBible } from '@/hooks/use-story-bible'
import { useToast } from '@/hooks/use-toast'

// Mock hooks
jest.mock('@/hooks/use-story-bible')
jest.mock('@/hooks/use-toast')

const mockUseStoryBible = useStoryBible as jest.MockedFunction<typeof useStoryBible>
const mockUseToast = useToast as jest.MockedFunction<typeof useToast>

describe('StoryBibleExplorer', () => {
  const mockToast = jest.fn()
  const defaultProps = {
    projectId: 'test-project-123',
    readOnly: false,
    isAIEnabled: true
  }

  const mockStoryBibleData = {
    worldRules: [
      { id: '1', category: 'Magic System', content: 'Magic requires verbal incantations' },
      { id: '2', category: 'Technology', content: 'Steam-powered machinery is common' }
    ],
    timeline: [
      { 
        id: 't1', 
        event: 'The Great War', 
        date: 'Year 1000', 
        description: 'A massive conflict that shaped the world'
      },
      { 
        id: 't2', 
        event: 'Discovery of Magic', 
        date: 'Year 500', 
        description: 'First recorded use of magical abilities'
      }
    ],
    themes: ['Power and corruption', 'Redemption', 'The cost of progress'],
    plotThreads: [
      { 
        id: 'p1', 
        name: 'The Prophecy', 
        description: 'An ancient prophecy foretells a chosen one',
        status: 'active' 
      },
      { 
        id: 'p2', 
        name: 'Political Intrigue', 
        description: 'Court politics threaten the kingdom',
        status: 'resolved' 
      }
    ],
    entries: []
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseToast.mockReturnValue({ toast: mockToast } as any)
    mockUseStoryBible.mockReturnValue({
      loading: false,
      saving: false,
      error: null,
      storyBible: mockStoryBibleData,
      addEntry: jest.fn(),
      updateEntry: jest.fn(),
      deleteEntry: jest.fn(),
      loadStoryBible: jest.fn(),
      generateFromAI: jest.fn()
    })
  })

  describe('Rendering', () => {
    it('should render story bible explorer with all sections', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('Story Bible')).toBeInTheDocument()
        expect(screen.getByText('World Rules')).toBeInTheDocument()
        expect(screen.getByText('Timeline')).toBeInTheDocument()
        expect(screen.getByText('Themes')).toBeInTheDocument()
        expect(screen.getByText('Plot Threads')).toBeInTheDocument()
      })
    })

    it('should display loading state', () => {
      mockUseStoryBible.mockReturnValue({
        loading: true,
        saving: false,
        error: null,
        storyBible: null,
        addEntry: jest.fn(),
        updateEntry: jest.fn(),
        deleteEntry: jest.fn(),
        loadStoryBible: jest.fn(),
        generateFromAI: jest.fn()
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      expect(screen.getByText('Loading story bible...')).toBeInTheDocument()
    })

    it('should display error state', () => {
      mockUseStoryBible.mockReturnValue({
        loading: false,
        saving: false,
        error: 'Failed to load story bible',
        storyBible: null,
        addEntry: jest.fn(),
        updateEntry: jest.fn(),
        deleteEntry: jest.fn(),
        loadStoryBible: jest.fn(),
        generateFromAI: jest.fn()
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      expect(screen.getByText(/Failed to load story bible/)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
    })

    it('should hide action buttons in read-only mode', () => {
      render(<StoryBibleExplorer {...defaultProps} readOnly={true} />)

      expect(screen.queryByRole('button', { name: /add rule/i })).not.toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /generate from ai/i })).not.toBeInTheDocument()
    })
  })

  describe('World Rules Section', () => {
    it('should display all world rules', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('Magic System')).toBeInTheDocument()
        expect(screen.getByText('Magic requires verbal incantations')).toBeInTheDocument()
        expect(screen.getByText('Technology')).toBeInTheDocument()
        expect(screen.getByText('Steam-powered machinery is common')).toBeInTheDocument()
      })
    })

    it('should allow adding a new world rule', async () => {
      const mockAddEntry = jest.fn()
      mockUseStoryBible.mockReturnValue({
        ...mockUseStoryBible(),
        addEntry: mockAddEntry
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      const addButton = screen.getByRole('button', { name: /add rule/i })
      await userEvent.click(addButton)

      // Fill in the form in the dialog
      const categoryInput = screen.getByLabelText(/category/i)
      const contentInput = screen.getByLabelText(/content/i)
      
      await userEvent.type(categoryInput, 'Geography')
      await userEvent.type(contentInput, 'The world consists of seven continents')

      const saveButton = screen.getByRole('button', { name: /save/i })
      await userEvent.click(saveButton)

      expect(mockAddEntry).toHaveBeenCalledWith(
        'world_rule',
        expect.objectContaining({
          category: 'Geography',
          content: 'The world consists of seven continents'
        })
      )
    })

    it('should allow editing a world rule', async () => {
      const mockUpdateEntry = jest.fn()
      mockUseStoryBible.mockReturnValue({
        ...mockUseStoryBible(),
        updateEntry: mockUpdateEntry
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      // Click edit button on first rule
      const editButtons = screen.getAllByRole('button', { name: /edit/i })
      await userEvent.click(editButtons[0])

      // Modify content
      const contentInput = screen.getByDisplayValue('Magic requires verbal incantations')
      await userEvent.clear(contentInput)
      await userEvent.type(contentInput, 'Magic requires both verbal and somatic components')

      const saveButton = screen.getByRole('button', { name: /save/i })
      await userEvent.click(saveButton)

      expect(mockUpdateEntry).toHaveBeenCalledWith(
        '1',
        expect.objectContaining({
          content: 'Magic requires both verbal and somatic components'
        })
      )
    })

    it('should allow deleting a world rule', async () => {
      const mockDeleteEntry = jest.fn()
      mockUseStoryBible.mockReturnValue({
        ...mockUseStoryBible(),
        deleteEntry: mockDeleteEntry
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      // Click delete button on first rule
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i })
      await userEvent.click(deleteButtons[0])

      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await userEvent.click(confirmButton)

      expect(mockDeleteEntry).toHaveBeenCalledWith('1')
    })
  })

  describe('Timeline Section', () => {
    it('should display timeline events in chronological order', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      await waitFor(() => {
        const events = screen.getAllByTestId('timeline-event')
        expect(events).toHaveLength(2)
        expect(events[0]).toHaveTextContent('Discovery of Magic')
        expect(events[1]).toHaveTextContent('The Great War')
      })
    })

    it('should allow adding a timeline event', async () => {
      const mockAddEntry = jest.fn()
      mockUseStoryBible.mockReturnValue({
        ...mockUseStoryBible(),
        addEntry: mockAddEntry
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      const addButton = screen.getByRole('button', { name: /add event/i })
      await userEvent.click(addButton)

      // Fill in the form
      await userEvent.type(screen.getByLabelText(/event name/i), 'The Peace Treaty')
      await userEvent.type(screen.getByLabelText(/date/i), 'Year 1010')
      await userEvent.type(screen.getByLabelText(/description/i), 'End of the Great War')

      const saveButton = screen.getByRole('button', { name: /save/i })
      await userEvent.click(saveButton)

      expect(mockAddEntry).toHaveBeenCalledWith(
        'timeline_event',
        expect.objectContaining({
          event: 'The Peace Treaty',
          date: 'Year 1010',
          description: 'End of the Great War'
        })
      )
    })
  })

  describe('Themes Section', () => {
    it('should display all themes', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('Power and corruption')).toBeInTheDocument()
        expect(screen.getByText('Redemption')).toBeInTheDocument()
        expect(screen.getByText('The cost of progress')).toBeInTheDocument()
      })
    })

    it('should allow adding a new theme', async () => {
      const mockAddEntry = jest.fn()
      mockUseStoryBible.mockReturnValue({
        ...mockUseStoryBible(),
        addEntry: mockAddEntry
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      const addButton = screen.getByRole('button', { name: /add theme/i })
      await userEvent.click(addButton)

      await userEvent.type(screen.getByLabelText(/theme/i), 'Love conquers all')

      const saveButton = screen.getByRole('button', { name: /save/i })
      await userEvent.click(saveButton)

      expect(mockAddEntry).toHaveBeenCalledWith(
        'theme',
        expect.objectContaining({
          theme: 'Love conquers all'
        })
      )
    })
  })

  describe('Plot Threads Section', () => {
    it('should display plot threads with status indicators', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('The Prophecy')).toBeInTheDocument()
        expect(screen.getByText('active')).toBeInTheDocument()
        expect(screen.getByText('Political Intrigue')).toBeInTheDocument()
        expect(screen.getByText('resolved')).toBeInTheDocument()
      })
    })

    it('should filter plot threads by status', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      // Click on filter for active threads
      const activeFilter = screen.getByRole('button', { name: /active/i })
      await userEvent.click(activeFilter)

      expect(screen.getByText('The Prophecy')).toBeInTheDocument()
      expect(screen.queryByText('Political Intrigue')).not.toBeInTheDocument()
    })
  })

  describe('AI Generation', () => {
    it('should show AI generation button when enabled', () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      expect(screen.getByRole('button', { name: /generate from ai/i })).toBeInTheDocument()
    })

    it('should hide AI generation button when disabled', () => {
      render(<StoryBibleExplorer {...defaultProps} isAIEnabled={false} />)

      expect(screen.queryByRole('button', { name: /generate from ai/i })).not.toBeInTheDocument()
    })

    it('should call generateFromAI when clicking generate button', async () => {
      const mockGenerateFromAI = jest.fn()
      mockUseStoryBible.mockReturnValue({
        ...mockUseStoryBible(),
        generateFromAI: mockGenerateFromAI
      })

      render(<StoryBibleExplorer {...defaultProps} />)

      const generateButton = screen.getByRole('button', { name: /generate from ai/i })
      await userEvent.click(generateButton)

      expect(mockGenerateFromAI).toHaveBeenCalled()
    })
  })

  describe('Search and Filter', () => {
    it('should filter content based on search term', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText(/search story bible/i)
      await userEvent.type(searchInput, 'magic')

      await waitFor(() => {
        expect(screen.getByText('Magic System')).toBeInTheDocument()
        expect(screen.getByText('Discovery of Magic')).toBeInTheDocument()
        expect(screen.queryByText('Technology')).not.toBeInTheDocument()
        expect(screen.queryByText('The Great War')).not.toBeInTheDocument()
      })
    })

    it('should clear search when clicking clear button', async () => {
      render(<StoryBibleExplorer {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText(/search story bible/i)
      await userEvent.type(searchInput, 'magic')

      const clearButton = screen.getByRole('button', { name: /clear/i })
      await userEvent.click(clearButton)

      expect(searchInput).toHaveValue('')
      expect(screen.getByText('Technology')).toBeInTheDocument()
    })
  })

  describe('Export Functionality', () => {
    it('should export story bible as JSON', async () => {
      const mockCreateObjectURL = jest.fn()
      const mockRevokeObjectURL = jest.fn()
      global.URL.createObjectURL = mockCreateObjectURL
      global.URL.revokeObjectURL = mockRevokeObjectURL

      render(<StoryBibleExplorer {...defaultProps} />)

      const exportButton = screen.getByRole('button', { name: /export/i })
      await userEvent.click(exportButton)

      const jsonOption = screen.getByRole('menuitem', { name: /export as json/i })
      await userEvent.click(jsonOption)

      expect(mockCreateObjectURL).toHaveBeenCalled()
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Export successful',
        description: 'Story bible exported as JSON'
      })
    })

    it('should export story bible as markdown', async () => {
      const mockCreateObjectURL = jest.fn()
      const mockRevokeObjectURL = jest.fn()
      global.URL.createObjectURL = mockCreateObjectURL
      global.URL.revokeObjectURL = mockRevokeObjectURL

      render(<StoryBibleExplorer {...defaultProps} />)

      const exportButton = screen.getByRole('button', { name: /export/i })
      await userEvent.click(exportButton)

      const mdOption = screen.getByRole('menuitem', { name: /export as markdown/i })
      await userEvent.click(mdOption)

      expect(mockCreateObjectURL).toHaveBeenCalled()
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Export successful',
        description: 'Story bible exported as Markdown'
      })
    })
  })
})