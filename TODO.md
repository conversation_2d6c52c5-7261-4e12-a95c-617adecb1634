# BookScribe AI - TODO Tracking

This file tracks all TODO items, unimplemented features, and technical debt in the codebase.

## Priority 1 - Critical Issues (Must Fix)

### Search Implementation
- **File**: `src/app/(dashboard)/search/page.tsx`
- **Issue**: Currently using mock data instead of real search
- **Solution**: Implement Supabase full-text search across projects, chapters, characters, and locations
- **Status**: TODO

### Error Handling
- **File**: `src/components/error/error-reporting.tsx`
- **Issue**: Fixed - was throwing wrong error variable
- **Status**: COMPLETED ✓

### Batch Error Reporting
- **File**: `src/components/error/error-reporting.tsx` 
- **Issue**: Was not implemented
- **Status**: COMPLETED ✓

## Priority 2 - Important Features

### Export Service - Cover Images
- **File**: `src/lib/export/export-service.ts` (line 786)
- **Issue**: EPUB export doesn't fetch actual cover images from storage
- **Solution**: Implement cover image fetching from Supabase Storage
- **Status**: TODO

### Real-time Collaboration
- **Files**: `src/lib/collaboration/*`
- **Issue**: WebSocket endpoints need to be connected to actual backend
- **Solution**: Implement WebSocket server for real-time collaboration
- **Status**: TODO

### Email Templates
- **Files**: `src/lib/email/providers/*`
- **Issue**: Some email providers have incomplete implementations
- **Solution**: Complete provider implementations or remove unused ones
- **Status**: TODO

## Priority 3 - Code Quality

### Constants Extraction
- **Issue**: Hardcoded values scattered throughout codebase
- **Solution**: Created `src/lib/constants/index.ts` - migrate all hardcoded values
- **Status**: IN PROGRESS

### Console.log Cleanup
- **Files**: Various demo and debug files
- **Issue**: Console.log statements in production code
- **Solution**: Remove or convert to proper logging
- **Status**: PARTIALLY COMPLETED

### Mock Data in Demos
- **Files**: `src/lib/demo/mock-data.ts` and demo components
- **Issue**: This is intentional - demos should use mock data
- **Status**: NO ACTION NEEDED

## Priority 4 - Documentation

### API Documentation
- **File**: `docs/API_DOCUMENTATION.md`
- **Issue**: May need updates for new endpoints
- **Status**: TODO - Review needed

### Component Documentation
- **Issue**: Some complex components lack proper JSDoc comments
- **Status**: TODO

## Completed Items ✓

1. Fixed error handling bug in error-reporting.tsx
2. Implemented batch error reporting functionality
3. Created constants file for hardcoded values
4. Updated error reporting to use constants
5. Removed unnecessary console.log in collaboration demo
6. Updated placeholder email from example.com to bookscribe.ai

## Notes

- Mock data in demo components is intentional and should remain
- Console.log in debug mode (websocket-client.ts) is acceptable
- Some TODOs in collaboration features depend on backend implementation
- Export service needs Supabase Storage integration for full functionality

Last Updated: 2025-01-25