'use client';

/* eslint-disable jsx-a11y/alt-text */

import { useState, useEffect } from 'react';
import { logger } from '@/lib/services/logger';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Upload, 
  Plus, 
  Search, 
  FileText, 
  Image, 
  Link as LinkIcon, 
  StickyNote,
  Brain,
  Download,
  Edit,
  Trash2,
  Eye,
  BookOpen,
  Paperclip
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ReferenceMaterial {
  id: string;
  projectId: string;
  type: 'document' | 'image' | 'url' | 'note' | 'research';
  title: string;
  description?: string;
  fileUrl?: string;
  fileSize?: number;
  mimeType?: string;
  content?: string;
  tags: string[];
  aiSummary?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ReferenceMaterialsProps {
  projectId: string;
  userId: string;
}

export function ReferenceMaterials({ projectId, userId }: ReferenceMaterialsProps) {
  const [materials, setMaterials] = useState<ReferenceMaterial[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<ReferenceMaterial | null>(null);

  useEffect(() => {
    fetchMaterials();
  }, [projectId]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchMaterials = async () => {
    try {
      const response = await fetch(`/api/references?projectId=${projectId}`);
      if (response.ok) {
        const data = await response.json();
        setMaterials(data.materials);
      }
    } catch (error) {
      logger.error('Error fetching materials:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesType = typeFilter === 'all' || material.type === typeFilter;

    return matchesSearch && matchesType;
  });

  const getTypeIcon = (type: ReferenceMaterial['type']) => {
    switch (type) {
      case 'document': return <FileText className="h-4 w-4" />;
      case 'image': return <Image className="h-4 w-4" />;
      case 'url': return <LinkIcon className="h-4 w-4" />;
      case 'note': return <StickyNote className="h-4 w-4" />;
      case 'research': return <Brain className="h-4 w-4" />;
      default: return <Paperclip className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: ReferenceMaterial['type']) => {
    switch (type) {
      case 'document': return 'bg-info-light text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'image': return 'bg-success-light text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'url': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'note': return 'bg-warning-light text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'research': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const handleFileUpload = async (file: File, title: string, description: string, tags: string[]) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('projectId', projectId);
      formData.append('userId', userId);
      formData.append('title', title);
      formData.append('description', description);
      formData.append('tags', JSON.stringify(tags));

      const response = await fetch('/api/references/upload', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        await fetchMaterials();
        setIsUploadDialogOpen(false);
      }
    } catch (error) {
      logger.error('Error uploading file:', error);
    }
  };

  const handleDelete = async (materialId: string) => {
    try {
      const response = await fetch(`/api/references/${materialId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setMaterials(materials.filter(m => m.id !== materialId));
      }
    } catch (error) {
      logger.error('Error deleting material:', error);
    }
  };

  const generateAISummary = async (materialId: string) => {
    try {
      const response = await fetch(`/api/references/${materialId}/summarize`, {
        method: 'POST',
      });

      if (response.ok) {
        await fetchMaterials();
      }
    } catch (error) {
      logger.error('Error generating AI summary:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-8 w-8 animate-pulse mx-auto mb-4 text-info" />
          <p className="text-slate-600 dark:text-slate-400">Loading reference materials...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Reference Materials</h2>
          <p className="text-slate-600 dark:text-slate-400">
            Research files, images, and notes for your project
          </p>
        </div>
        <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Material
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Reference Material</DialogTitle>
              <DialogDescription>
                Upload files, add links, or create notes to reference in your writing
              </DialogDescription>
            </DialogHeader>
            <AddMaterialForm 
              projectId={projectId}
              userId={userId}
              onSubmit={handleFileUpload}
              onClose={() => setIsUploadDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search materials..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="document">Documents</SelectItem>
            <SelectItem value="image">Images</SelectItem>
            <SelectItem value="url">Links</SelectItem>
            <SelectItem value="note">Notes</SelectItem>
            <SelectItem value="research">Research</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Materials Grid */}
      {filteredMaterials.length === 0 ? (
        <Card className="p-12">
          <div className="text-center">
            <Upload className="h-12 w-12 mx-auto mb-4 text-slate-400" />
            <h3 className="text-lg font-medium mb-2">No reference materials yet</h3>
            <p className="text-slate-600 dark:text-slate-400 mb-4">
              {searchQuery || typeFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Add files, links, or notes to help with your writing'
              }
            </p>
            {!searchQuery && typeFilter === 'all' && (
              <Button onClick={() => setIsUploadDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Material
              </Button>
            )}
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredMaterials.map((material) => (
            <Card key={material.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(material.type)}
                    <Badge className={`text-xs ${getTypeColor(material.type)}`}>
                      {material.type}
                    </Badge>
                  </div>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" onClick={() => setSelectedMaterial(material)}>
                      <Eye className="h-3 w-3" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDelete(material.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <CardTitle className="text-lg line-clamp-2">{material.title}</CardTitle>
                {material.description && (
                  <CardDescription className="text-sm line-clamp-2">
                    {material.description}
                  </CardDescription>
                )}
              </CardHeader>

              <CardContent className="space-y-3">
                {/* File Info */}
                {material.fileSize && (
                  <div className="text-xs text-slate-500">
                    Size: {formatFileSize(material.fileSize)}
                  </div>
                )}

                {/* AI Summary */}
                {material.aiSummary ? (
                  <div className="p-3 bg-info-light dark:bg-blue-950/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Brain className="h-3 w-3 text-info" />
                      <span className="text-xs font-medium text-blue-800 dark:text-blue-200">
                        AI Summary
                      </span>
                    </div>
                    <p className="text-xs text-blue-700 dark:text-blue-300 line-clamp-3">
                      {material.aiSummary}
                    </p>
                  </div>
                ) : (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full text-xs"
                    onClick={() => generateAISummary(material.id)}
                  >
                    <Brain className="h-3 w-3 mr-1" />
                    Generate AI Summary
                  </Button>
                )}

                {/* Tags */}
                {material.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {material.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {material.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{material.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Metadata */}
                <div className="text-xs text-slate-500 pt-2 border-t">
                  Added {formatDistanceToNow(material.createdAt, { addSuffix: true })}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  {material.fileUrl && (
                    <Button variant="outline" size="sm" className="flex-1">
                      <Download className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  )}
                  <Button variant="outline" size="sm" className="flex-1">
                    <LinkIcon className="h-3 w-3 mr-1" />
                    Reference
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Material Viewer Dialog */}
      {selectedMaterial && (
        <MaterialViewer 
          material={selectedMaterial}
          onClose={() => setSelectedMaterial(null)}
        />
      )}
    </div>
  );
}

function AddMaterialForm({ 
  projectId, 
  userId, 
  onSubmit, 
  onClose 
}: { 
  projectId: string;
  userId: string;
  onSubmit: (file: File, title: string, description: string, tags: string[]) => void;
  onClose: () => void;
}) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [materialType, setMaterialType] = useState<'file' | 'url' | 'note'>('file');
  const [url, setUrl] = useState('');
  const [content, setContent] = useState('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      if (!title) {
        setTitle(selectedFile.name.replace(/\.[^/.]+$/, ""));
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (materialType === 'file' && file) {
      const tagList = tags.split(',').map(t => t.trim()).filter(Boolean);
      onSubmit(file, title, description, tagList);
    } else {
      // Handle URL or note creation
      try {
        const response = await fetch('/api/references', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            projectId,
            userId,
            type: materialType === 'url' ? 'url' : 'note',
            title,
            description,
            content: materialType === 'url' ? url : content,
            tags: tags.split(',').map(t => t.trim()).filter(Boolean),
          }),
        });

        if (response.ok) {
          onClose();
        }
      } catch (error) {
        logger.error('Error creating material:', error);
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="text-sm font-medium">Material Type</label>
        <Select value={materialType} onValueChange={(value: 'file' | 'url' | 'note') => setMaterialType(value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="file">Upload File</SelectItem>
            <SelectItem value="url">Add Link</SelectItem>
            <SelectItem value="note">Create Note</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {materialType === 'file' && (
        <div>
          <label className="text-sm font-medium">File</label>
          <Input
            type="file"
            onChange={handleFileChange}
            accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.md"
            required
          />
        </div>
      )}

      {materialType === 'url' && (
        <div>
          <label className="text-sm font-medium">URL</label>
          <Input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://example.com"
            required
          />
        </div>
      )}

      {materialType === 'note' && (
        <div>
          <label className="text-sm font-medium">Content</label>
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Write your note here..."
            rows={6}
            required
          />
        </div>
      )}

      <div>
        <label className="text-sm font-medium">Title</label>
        <Input
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Material title"
          required
        />
      </div>
      
      <div>
        <label className="text-sm font-medium">Description</label>
        <Textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Optional description..."
          rows={3}
        />
      </div>

      <div>
        <label className="text-sm font-medium">Tags (comma-separated)</label>
        <Input
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          placeholder="research, character, worldbuilding"
        />
      </div>

      <div className="flex items-center justify-between pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit">
          Add Material
        </Button>
      </div>
    </form>
  );
}

function MaterialViewer({ 
  material, 
  onClose 
}: { 
  material: ReferenceMaterial;
  onClose: () => void;
}) {
  return (
    <Dialog open={!!material} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {material.type === 'document' && <FileText className="h-5 w-5" />}
            {material.type === 'image' && <Image className="h-5 w-5" />}
            {material.type === 'url' && <LinkIcon className="h-5 w-5" />}
            {material.type === 'note' && <StickyNote className="h-5 w-5" />}
            {material.type === 'research' && <Brain className="h-5 w-5" />}
            {material.title}
          </DialogTitle>
          <DialogDescription>
            {material.description}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {material.aiSummary && (
            <div className="p-4 bg-info-light dark:bg-blue-950/20 rounded-lg">
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">AI Summary</h4>
              <p className="text-blue-700 dark:text-blue-300">{material.aiSummary}</p>
            </div>
          )}

          {material.content && (
            <div className="whitespace-pre-wrap text-sm bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
              {material.content}
            </div>
          )}

          {material.tags.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Tags</h4>
              <div className="flex flex-wrap gap-2">
                {material.tags.map(tag => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}