import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  BookOpen, 
  Code, 
  Zap, 
  Users, 
  Settings, 
  FileText,
  ArrowRight,
  ExternalLink,
  Play,
  Download
} from 'lucide-react'
import Link from 'next/link'

export default function DocumentationPage() {
  return (
    <div className="container-wide mx-auto px-4 py-6 sm:py-8 lg:py-10 max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px] xl:max-w-[1400px] 2xl:max-w-[1600px]">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Documentation</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Everything you need to know about using BookScribe AI to write amazing novels. 
          From your first project to advanced AI collaboration techniques.
        </p>
      </div>

      {/* Quick Start */}
      <Card className="mb-12 bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Play className="h-6 w-6" />
            Quick Start Guide
          </CardTitle>
          <CardDescription>
            Get up and running with BookScribe AI in under 10 minutes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                1
              </div>
              <div>
                <h3 className="font-semibold">Create Account</h3>
                <p className="text-sm text-muted-foreground">Sign up and verify your email</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                2
              </div>
              <div>
                <h3 className="font-semibold">Start New Project</h3>
                <p className="text-sm text-muted-foreground">Use our project wizard to set up your novel</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                3
              </div>
              <div>
                <h3 className="font-semibold">Begin Writing</h3>
                <p className="text-sm text-muted-foreground">Let our AI agents help you craft your story</p>
              </div>
            </div>
          </div>
          <div className="flex gap-3">
            <Button asChild>
              <Link href="/signup">
                Get Started Free
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/demo">
                Try the Demo
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Documentation Sections */}
      <div className="grid md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 mb-12">
        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              Getting Started
            </CardTitle>
            <CardDescription>
              Essential guides for new users
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Account Setup & First Project
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Understanding AI Agents
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Project Wizard Overview
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Editor Interface Tour
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Writing Features */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-blue-500" />
              Writing Features
            </CardTitle>
            <CardDescription>
              Master the core writing tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Story Bible Management
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Character Development
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Chapter Planning & Outlining
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Voice Profiles & Style
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* AI Collaboration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5 text-green-500" />
              AI Collaboration
            </CardTitle>
            <CardDescription>
              Working effectively with AI agents
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Multi-Agent Workflow
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Context Management
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Prompt Engineering Tips
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Quality Control & Editing
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Series & Universes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-500" />
              Series & Universes
            </CardTitle>
            <CardDescription>
              Managing complex multi-book projects
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Creating Book Series
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Universe Management
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Character Continuity
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Timeline Management
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Export & Publishing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5 text-orange-500" />
              Export & Publishing
            </CardTitle>
            <CardDescription>
              Getting your work ready for publication
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Export Formats (EPUB, PDF, DOCX)
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Manuscript Formatting
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Version Control
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Collaboration & Reviews
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Account & Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-gray-500" />
              Account & Settings
            </CardTitle>
            <CardDescription>
              Managing your BookScribe experience
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Profile & Preferences
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Subscription Management
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Theme Customization
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
              <li>
                <Link href="/help" className="flex items-center justify-between text-sm hover:text-primary">
                  Privacy & Security
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Additional Resources */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Additional Resources
          </CardTitle>
          <CardDescription>
            More ways to learn and get help
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
            <Button variant="outline" asChild>
              <a href="https://blog.bookscribe.ai" target="_blank" rel="noopener noreferrer">
                Blog & Tutorials
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href="https://discord.gg/bookscribe" target="_blank" rel="noopener noreferrer">
                Community Discord
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href="https://youtube.com/@bookscribeai" target="_blank" rel="noopener noreferrer">
                Video Tutorials
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/contact">
                Contact Support
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* API Documentation Note */}
      <Card>
        <CardHeader>
          <CardTitle>Looking for API Documentation?</CardTitle>
          <CardDescription>
            Developer resources for integrating with BookScribe AI
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            If you're looking for API documentation to integrate BookScribe AI into your own applications, 
            our developer resources are coming soon. We're building comprehensive API access for:
          </p>
          <ul className="list-disc pl-6 space-y-1 text-muted-foreground mb-4">
            <li>Project management and content retrieval</li>
            <li>AI agent orchestration</li>
            <li>Export and publishing workflows</li>
            <li>User and subscription management</li>
          </ul>
          <Button variant="outline" asChild>
            <a href="mailto:<EMAIL>?subject=API Access Interest">
              Join API Beta List
              <ExternalLink className="ml-2 h-4 w-4" />
            </a>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}