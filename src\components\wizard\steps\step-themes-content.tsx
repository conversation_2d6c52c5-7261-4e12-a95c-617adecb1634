'use client';

import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Globe, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface StepThemesContentProps {
  formData: {
    themes: string;
    contentRating?: string;
    triggerWarnings?: string;
    [key: string]: string | undefined;
  };
  updateFormData: (field: string, value: string) => void;
  mode?: 'live' | 'demo';
}

export function StepThemesContent({ formData, updateFormData, mode }: StepThemesContentProps) {
  return (
    <Card className="max-w-4xl mx-auto shadow-sm">
      <CardHeader>
        <div className="flex items-center gap-2 mb-4">
          <Globe className="h-6 w-6 text-primary" />
          <CardTitle>Themes & Content Guidelines</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="themes">Core Themes</Label>
          <Textarea
            id="themes"
            placeholder="What are the main themes of your story? (e.g., redemption, power and corruption, family bonds, self-discovery)"
            value={formData.themes}
            onChange={(e) => updateFormData('themes', e.target.value)}
            className="min-h-[100px]"
          />
          <p className="text-sm text-muted-foreground">
            Themes give your story depth and meaning beyond the surface plot
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="contentRating">Content Rating</Label>
          <Select 
            value={formData.contentRating || 'teen'}
            onValueChange={(value) => updateFormData('contentRating', value)}
          >
            <SelectTrigger id="contentRating">
              <SelectValue placeholder="Select content rating" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="middle-grade">Middle Grade (8-12)</SelectItem>
              <SelectItem value="teen">Teen (13-17)</SelectItem>
              <SelectItem value="young-adult">Young Adult (16+)</SelectItem>
              <SelectItem value="adult">Adult (18+)</SelectItem>
              <SelectItem value="mature">Mature (21+)</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            This helps the AI adjust language, violence, and romantic content appropriately
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="triggerWarnings">Content Warnings (Optional)</Label>
          <Textarea
            id="triggerWarnings"
            placeholder="List any sensitive topics your story will address (e.g., violence, loss, trauma)"
            value={formData.triggerWarnings || ''}
            onChange={(e) => updateFormData('triggerWarnings', e.target.value)}
            className="min-h-[80px]"
          />
          <p className="text-sm text-muted-foreground">
            This helps readers make informed choices and helps the AI handle sensitive topics appropriately
          </p>
        </div>

        {mode === 'demo' && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              In demo mode, themes are pre-selected to showcase the AI's capabilities. 
              In a real project, you can customize these completely.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}