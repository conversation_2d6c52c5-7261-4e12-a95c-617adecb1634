// Test helper for route handlers
// This file provides exports for testing Next.js route handlers

import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Re-export route handlers from their actual locations
// This is a helper file for tests to import route handlers uniformly

export type RouteHandler = (request: NextRequest, context?: any) => Promise<NextResponse> | NextResponse

// Helper function to create a test-friendly route handler wrapper
export function createRouteHandler(handler: RouteHandler): RouteHandler {
  return handler
}

// Re-export common route handlers for testing
// Tests should import the actual route handlers from their respective files
// This is just a type definition file for test compatibility

export const GET: RouteHandler = async (request: NextRequest) => {
  throw new Error('GET handler not implemented - import from the actual route file')
}

export const POST: RouteHandler = async (request: NextRequest) => {
  throw new Error('POST handler not implemented - import from the actual route file')
}

export const PUT: RouteHandler = async (request: NextRequest) => {
  throw new Error('PUT handler not implemented - import from the actual route file')
}

export const DELETE: RouteHandler = async (request: NextRequest) => {
  throw new Error('DELETE handler not implemented - import from the actual route file')
}

export const PATCH: RouteHandler = async (request: NextRequest) => {
  throw new Error('PATCH handler not implemented - import from the actual route file')
}