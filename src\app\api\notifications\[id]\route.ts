import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/db/types'
import { logger } from '@/lib/services/logger'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export const GET = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const user = request.user!
    const supabase: SupabaseClient<Database> = await createTypedServerClient()

    const { data: notification, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'))
      }
      logger.error('Error fetching notification:', error)
      return NextResponse.json({ error: 'Failed to fetch notification' }, { status: 500 })
    }

    return NextResponse.json({ notification })
  } catch (error) {
    logger.error('Error in notification GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
})

export const PATCH = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const user = request.user!
    const supabase: SupabaseClient<Database> = await createTypedServerClient()

    const body = await request.json()
    const { read } = body

    const updateData: Record<string, unknown> = {}
    if (typeof read === 'boolean') {
      updateData.read = read
      if (read) {
        updateData.read_at = new Date().toISOString()
      }
    }

    const { data: notification, error } = await supabase
      .from('notifications')
      .update(updateData)
      .eq('id', params.id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'))
      }
      logger.error('Error updating notification:', error)
      return NextResponse.json({ error: 'Failed to update notification' }, { status: 500 })
    }

    return NextResponse.json({ notification })
  } catch (error) {
    logger.error('Error in notification PATCH:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
})

export const DELETE = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const user = request.user!
    const supabase: SupabaseClient<Database> = await createTypedServerClient()

    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', params.id)
      .eq('user_id', user.id)

    if (error) {
      logger.error('Error deleting notification:', error)
      return NextResponse.json({ error: 'Failed to delete notification' }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'Notification deleted' })
  } catch (error) {
    logger.error('Error in notification DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
})