import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { v4 as uuidv4 } from 'uuid'

const ALLOWED_FILE_TYPES = {
  'application/pdf': { ext: 'pdf', category: 'document' },
  'application/msword': { ext: 'doc', category: 'document' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { ext: 'docx', category: 'document' },
  'text/plain': { ext: 'txt', category: 'document' },
  'text/markdown': { ext: 'md', category: 'document' },
  'image/jpeg': { ext: 'jpg', category: 'image' },
  'image/png': { ext: 'png', category: 'image' },
  'image/gif': { ext: 'gif', category: 'image' },
  'image/webp': { ext: 'webp', category: 'image' }
}

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

function sanitizeFileName(fileName: string): string {
  // Remove any path components and special characters
  const baseName = fileName.split(/[/\\]/).pop() || ''
  return baseName.replace(/[^a-zA-Z0-9.-]/g, '_').substring(0, 255)
}

function generateSecureFilename(originalName: string): string {
  const sanitized = sanitizeFileName(originalName)
  const timestamp = Date.now()
  const uuid = uuidv4().split('-')[0]
  const ext = sanitized.split('.').pop() || 'bin'
  return `${timestamp}_${uuid}.${ext}`
}

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    const projectId = formData.get('projectId') as string
    const userId = formData.get('userId') as string
    const title = formData.get('title') as string
    const description = formData.get('description') as string
    const tagsJson = formData.get('tags') as string

    // Basic input validation
    if (!file || !projectId || !userId || !title) {
      return UnifiedResponse.error('Missing required fields', 400)
    }

    // Validate user matches authenticated user
    if (userId !== user.id) {
      return UnifiedResponse.error('Unauthorized', 403)
    }

    // Verify project access
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (!project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Parse tags
    let tags: string[] = []
    try {
      tags = tagsJson ? JSON.parse(tagsJson) : []
    } catch {
      logger.warn('Invalid tags JSON:', tagsJson)
    }

    // Validate file type
    const fileTypeInfo = ALLOWED_FILE_TYPES[file.type]
    if (!fileTypeInfo) {
      return UnifiedResponse.error(`File type not allowed. Allowed types: ${Object.keys(ALLOWED_FILE_TYPES).join(', ')}`, 400)
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return UnifiedResponse.error(`File size exceeds maximum of ${MAX_FILE_SIZE / (1024 * 1024)}MB`, 400)
    }

    // Generate secure filename
    const secureFileName = generateSecureFilename(file.name)
    const filePath = `reference-materials/${projectId}/${secureFileName}`

    // Read file buffer for upload
    const fileBuffer = await file.arrayBuffer()
    
    const { error: uploadError } = await supabase.storage
      .from('project-files')
      .upload(filePath, fileBuffer, {
        contentType: file.type,
        upsert: false
      })

    if (uploadError) {
      logger.error('Upload error:', uploadError)
      return UnifiedResponse.error('File upload failed')
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('project-files')
      .getPublicUrl(filePath)

    // Create database record
    const materialData = {
      project_id: projectId,
      user_id: userId,
      type: fileTypeInfo.category,
      title: title.substring(0, 255),
      description: description?.substring(0, 1000) || null,
      file_url: publicUrl,
      file_size: file.size,
      mime_type: file.type,
      tags: tags || [],
      is_processed: false,
      processing_status: 'pending'
    }

    const { data: material, error: dbError } = await supabase
      .from('reference_materials')
      .insert(materialData)
      .select()
      .single()

    if (dbError) {
      // Clean up uploaded file if database insert fails
      await supabase.storage
        .from('project-files')
        .remove([filePath])
      
      logger.error('Database error:', dbError)
      return UnifiedResponse.error('Failed to save reference material')
    }

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    }

    // Queue for processing if it's a text-based file
    if (['document'].includes(fileTypeInfo.category) && ['text/plain', 'text/markdown'].includes(file.type)) {
      try {
        // For now, just mark as ready for processing
        await supabase
          .from('reference_materials')
          .update({ processing_status: 'queued' })
          .eq('id', material.id)
      } catch (processingError) {
        logger.warn('Failed to queue material processing:', processingError)
      }
    }

    return UnifiedResponse.success({ material: formattedMaterial })
  } catch (error) {
    logger.error('Upload error:', error)
    return UnifiedResponse.error('Failed to upload file')
  }
})