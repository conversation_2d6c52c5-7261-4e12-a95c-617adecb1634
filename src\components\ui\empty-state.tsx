import * as React from "react"
import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"
import { But<PERSON> } from "./button"
import { SPACING, TYPOGRAPHY } from "@/lib/config/ui-config"

interface EmptyStateProps {
  icon?: LucideIcon
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
    variant?: "default" | "outline" | "secondary"
  }
  className?: string
  children?: React.ReactNode
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className,
  children
}: EmptyStateProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center text-center",
        SPACING.PADDING.LG,
        SPACING.SPACE_Y.MD,
        "min-h-[400px]",
        className
      )}
    >
      {Icon && (
        <div className="rounded-full bg-muted p-3">
          <Icon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
      
      <div className={cn(SPACING.SPACE_Y.SM, "max-w-sm")}>
        <h3 className={cn(TYPOGRAPHY.PRESETS.H3, "text-foreground")}>
          {title}
        </h3>
        
        {description && (
          <p className={cn(TYPOGRAPHY.PRESETS.BODY_SMALL, "text-muted-foreground")}>
            {description}
          </p>
        )}
      </div>
      
      {action && (
        <Button
          variant={action.variant || "default"}
          onClick={action.onClick}
          size="default"
        >
          {action.label}
        </Button>
      )}
      
      {children}
    </div>
  )
}