import { NextResponse } from 'next/server'
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import Stripe from 'stripe'
import { createTypedServerClient } from '@/lib/supabase'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/utils/response'
import { globalLimiter } from '@/lib/rate-limiter-unified'
import { config } from '@/lib/config'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'
import { logger } from '@/lib/services/logger'
import { RATE_LIMITS } from '@/lib/constants'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

const stripe = new Stripe(config.stripe.secretKey, {
  apiVersion: '2025-06-30.basil',
})

// Validation schema for payment intent
const createPaymentIntentSchema = z.object({
  amount: z.number()
    .int('Amount must be an integer')
    .min(50, 'Amount must be at least 50 cents')
    .max(999999, 'Amount exceeds maximum allowed'),
  currency: z.string()
    .regex(/^[a-z]{3}$/, 'Currency must be a 3-letter ISO code')
    .default('usd'),
  description: baseSchemas.description.max(500).optional(),
  statementDescriptor: z.string()
    .max(22)
    .regex(/^[a-zA-Z0-9\s\-\.\*]+$/, 'Invalid characters in statement descriptor')
    .optional(),
  metadata: z.record(z.string())
    .optional()
    .refine(
      (meta) => {
        if (!meta) return true;
        // Ensure metadata values are not too long
        return Object.values(meta).every(val => val.length <= 500);
      },
      'Metadata values must not exceed 500 characters'
    ),
  receiptEmail: baseSchemas.email.optional(),
  setupFutureUsage: z.enum(['on_session', 'off_session']).optional()
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: createPaymentIntentSchema,
    rateLimitKey: 'payment-create',
    rateLimitCost: 10, // High cost for payment operations
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const body = await req.json();
      const { amount } = body;

      // Validate amount limits based on user tier
      const supabase = await createTypedServerClient();
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('tier')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      const tier = subscription?.tier || 'free';
      const maxAmount = {
        free: 5000, // $50
        starter: 50000, // $500
        professional: 200000, // $2000
        studio: 999999 // $9999.99
      };

      if (amount > maxAmount[tier as keyof typeof maxAmount]) {
        return { 
          valid: false, 
          error: `Payment amount exceeds limit for ${tier} tier` 
        };
      }

      // Check for recent payment attempts to prevent abuse
      const recentPayments = await supabase
        .from('payment_intents')
        .select('id')
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
        .limit(3);

      if (recentPayments.data && recentPayments.data.length >= 3) {
        return { 
          valid: false, 
          error: 'Too many payment attempts. Please wait a moment.' 
        };
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { amount, currency, description, statementDescriptor, metadata, receiptEmail, setupFutureUsage } = context.body;

  try {
    const supabase = await createTypedServerClient();

    // Get or create Stripe customer
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id, email')
      .eq('id', user.id)
      .single();

    let customerId = profile?.stripe_customer_id;

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: receiptEmail || profile?.email || user.email,
        metadata: {
          userId: user.id
        }
      });
      customerId = customer.id;

      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id);
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      customer: customerId,
      description: description || `Payment from ${user.email}`,
      statement_descriptor: statementDescriptor,
      receipt_email: receiptEmail,
      setup_future_usage: setupFutureUsage,
      metadata: {
        userId: user.id,
        userEmail: user.email!,
        timestamp: new Date().toISOString(),
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never' // Prevent redirect-based payment methods
      },
    });

    // Store payment intent in database for tracking
    await supabase
      .from('payment_intents')
      .insert({
        id: paymentIntent.id,
        user_id: user.id,
        amount,
        currency,
        status: paymentIntent.status,
        description,
        metadata: {
          ...metadata,
          clientIP: context.clientIP
        }
      });

    logger.info('Payment intent created', {
      paymentIntentId: paymentIntent.id,
      userId: user.id,
      amount,
      currency,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      amount,
      currency
    });

  } catch (error) {
    logger.error('Payment intent creation error:', error, {
      userId: user.id,
      amount,
      clientIP: context.clientIP
    });
    
    if (error instanceof Stripe.errors.StripeError) {
      return UnifiedResponse.error(
        error.message || 'Payment processing error',
        error.statusCode || 400
      );
    }
    
    return UnifiedResponse.error('Failed to create payment intent');
  }
});