import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { AdvancedAgentOrchestrator } from '@/lib/agents/advanced-orchestrator';
import { StoryArchitectAgent } from '@/lib/agents/story-architect';
import { CharacterDeveloperAgent } from '@/lib/agents/character-developer';
import { ChapterPlannerAgent } from '@/lib/agents/chapter-planner';
import { VoiceAwareWritingAgent } from '@/lib/agents/voice-aware-writing-agent';
import { ContentGenerator } from '@/lib/services/content-generator';
import { AdaptivePlanningAgent } from '@/lib/agents/adaptive-planning-agent';
import { ContextManager } from '@/lib/services/context-manager';
import type { ProjectSettings } from '@/lib/types/project-settings';
import type { BookContext, AgentResponse } from '@/lib/agents/types';

// Mock all dependencies
jest.mock('@/lib/agents/story-architect');
jest.mock('@/lib/agents/character-developer');
jest.mock('@/lib/agents/chapter-planner');
jest.mock('@/lib/agents/voice-aware-writing-agent');
jest.mock('@/lib/services/content-generator');
jest.mock('@/lib/agents/adaptive-planning-agent');
jest.mock('@/lib/services/context-manager');

describe('AdvancedAgentOrchestrator', () => {
  let orchestrator: AdvancedAgentOrchestrator;
  let mockStoryArchitect: jest.Mocked<StoryArchitectAgent>;
  let mockCharacterDeveloper: jest.Mocked<CharacterDeveloperAgent>;
  let mockChapterPlanner: jest.Mocked<ChapterPlannerAgent>;
  let mockWritingAgent: jest.Mocked<VoiceAwareWritingAgent>;
  let mockContentGenerator: jest.Mocked<ContentGenerator>;
  let mockAdaptivePlanner: jest.Mocked<AdaptivePlanningAgent>;
  let mockContextManager: jest.Mocked<ContextManager>;

  const mockProjectSettings: ProjectSettings = {
    primaryGenre: 'fantasy',
    secondaryGenres: ['adventure'],
    targetAudience: 'adult',
    writingStyle: 'descriptive',
    narrativeVoice: 'third-person',
    tense: 'past',
    pacing: 'medium',
    violenceLevel: 'moderate',
    romanceLevel: 'low',
    profanityLevel: 'mild',
    themeDepth: 'deep',
    worldBuildingDepth: 'extensive',
    characterComplexity: 'complex',
    plotComplexity: 'complex',
    tone: 'serious',
    dialogueStyle: 'natural',
    descriptionLevel: 'detailed',
    useDeepPOV: true,
    showDontTell: true,
    varyProse: true,
    useSymbolism: true,
    useCliffhangers: true,
    useForeshadowing: true,
    useFlashbacks: false,
    useUnreliableNarrator: false,
    protagonistTypes: ['hero'],
    antagonistTypes: ['villain'],
    supportingRoles: ['mentor', 'sidekick'],
    majorThemes: ['courage', 'friendship'],
    minorThemes: ['sacrifice'],
    culturalElements: [],
    magicSystemType: 'soft',
    technologyLevel: 'medieval',
    politicalSystem: 'monarchy',
    economicSystem: 'feudal',
    geographyType: 'earth-like',
    pacingPreference: 'medium'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create orchestrator with max 2 concurrent tasks for testing
    orchestrator = new AdvancedAgentOrchestrator(2);

    // Setup mocks
    mockStoryArchitect = StoryArchitectAgent.prototype as jest.Mocked<StoryArchitectAgent>;
    mockCharacterDeveloper = CharacterDeveloperAgent.prototype as jest.Mocked<CharacterDeveloperAgent>;
    mockChapterPlanner = ChapterPlannerAgent.prototype as jest.Mocked<ChapterPlannerAgent>;
    mockWritingAgent = VoiceAwareWritingAgent.prototype as jest.Mocked<VoiceAwareWritingAgent>;
    mockContentGenerator = ContentGenerator.prototype as jest.Mocked<ContentGenerator>;
    mockAdaptivePlanner = AdaptivePlanningAgent.prototype as jest.Mocked<AdaptivePlanningAgent>;
    mockContextManager = ContextManager.prototype as jest.Mocked<ContextManager>;
  });

  afterEach(() => {
    // Clean up any pending async operations
    orchestrator.cancelOrchestration();
  });

  describe('Task Pipeline Creation', () => {
    it('should create a proper task pipeline with dependencies', () => {
      const pipeline = orchestrator['createTaskPipeline']({
        projectId: 'test-project',
        settings: mockProjectSettings,
        projectSelections: mockProjectSettings,
        storyPrompt: 'A hero saves the world',
        targetWordCount: 80000,
        targetChapters: 20
      });

      expect(pipeline).toBeDefined();
      expect(pipeline.length).toBeGreaterThan(0);

      // Check that foundation tasks have no dependencies
      const foundationTasks = pipeline.filter(task => 
        ['story_structure', 'world_building', 'theme_analysis'].includes(task.id)
      );
      foundationTasks.forEach(task => {
        expect(task.dependencies).toEqual([]);
      });

      // Check that character tasks depend on story structure
      const characterTasks = pipeline.filter(task => task.type === 'character_development');
      characterTasks.forEach(task => {
        if (task.id === 'main_characters') {
          expect(task.dependencies).toContain('story_structure');
        }
      });

      // Check that chapter planning depends on both story and characters
      const chapterTask = pipeline.find(task => task.id === 'chapter_structure');
      expect(chapterTask?.dependencies).toContain('story_structure');
      expect(chapterTask?.dependencies).toContain('main_characters');
    });

    it('should prioritize tasks correctly', () => {
      const pipeline = orchestrator['createTaskPipeline']({
        projectId: 'test-project',
        settings: mockProjectSettings,
        projectSelections: mockProjectSettings,
        storyPrompt: 'A hero saves the world',
        targetWordCount: 80000,
        targetChapters: 20
      });

      // Critical tasks should be processed first (when dependencies allow)
      const criticalTasks = pipeline.filter(task => task.priority === 'critical');
      expect(criticalTasks.length).toBeGreaterThan(0);
    });
  });

  describe('Task Execution', () => {
    it('should execute tasks respecting dependencies', async () => {
      // Setup mock responses
      mockStoryArchitect.generateStoryStructure = jest.fn().mockResolvedValue({
        success: true,
        data: {
          title: 'Test Story',
          premise: 'A test premise',
          genre: 'fantasy',
          themes: ['courage'],
          acts: [],
          conflicts: [],
          timeline: [],
          worldBuilding: {
            setting: {
              timeForPeriod: 'medieval',
              locations: [],
              culture: 'European-inspired',
              technology: 'pre-industrial'
            },
            rules: [],
            history: []
          },
          plotPoints: []
        }
      });

      mockCharacterDeveloper.generateCharacters = jest.fn().mockResolvedValue({
        success: true,
        data: {
          protagonists: [{ id: '1', name: 'Hero', role: 'protagonist' }],
          antagonists: [],
          supporting: [],
          relationships: []
        }
      });

      mockChapterPlanner.generateChapterOutlines = jest.fn().mockResolvedValue({
        success: true,
        data: {
          chapters: [],
          totalWordCount: 0,
          estimatedReadingTime: 0
        }
      });

      const result = await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(result.success).toBe(true);
      expect(mockStoryArchitect.generateStoryStructure).toHaveBeenCalled();
    });

    it('should handle task failures gracefully', async () => {
      // Make story architect fail
      mockStoryArchitect.generateStoryStructure = jest.fn().mockResolvedValue({
        success: false,
        error: 'Failed to generate story structure'
      });

      const result = await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      // The orchestrator should still complete, but some tasks may have failed
      expect(result.success).toBeDefined();
    });

    it('should respect max concurrent tasks limit', async () => {
      let runningTasks = 0;
      let maxRunning = 0;

      // Create a delayed mock to simulate concurrent execution
      const createDelayedMock = () => jest.fn().mockImplementation(() => {
        runningTasks++;
        maxRunning = Math.max(maxRunning, runningTasks);
        return new Promise(resolve => {
          setTimeout(() => {
            runningTasks--;
            resolve({
              success: true,
              data: { test: true }
            });
          }, 100);
        });
      });

      mockStoryArchitect.generateStoryStructure = createDelayedMock();
      mockContentGenerator.generateContent = createDelayedMock();

      await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      // Should not exceed max concurrent tasks (2)
      expect(maxRunning).toBeLessThanOrEqual(2);
    });
  });

  describe('Event Emission', () => {
    it('should emit orchestration events', async () => {
      const events: string[] = [];
      
      orchestrator.on('orchestration:started', () => events.push('started'));
      orchestrator.on('orchestration:progress', () => events.push('progress'));
      orchestrator.on('orchestration:completed', () => events.push('completed'));
      orchestrator.on('task:started', () => events.push('task-started'));

      // Setup minimal mocks
      mockStoryArchitect.generateStoryStructure = jest.fn().mockResolvedValue({
        success: true,
        data: { test: true }
      });

      await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(events).toContain('started');
      expect(events).toContain('progress');
      expect(events).toContain('completed');
      expect(events).toContain('task-started');
    });

    it('should emit task failure events', async () => {
      const failureEvents: any[] = [];
      
      orchestrator.on('task:failed', (event) => failureEvents.push(event));

      // Make a task fail
      mockStoryArchitect.generateStoryStructure = jest.fn().mockRejectedValue(
        new Error('Task failed')
      );

      await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(failureEvents.length).toBeGreaterThan(0);
      expect(failureEvents[0]).toHaveProperty('error');
    });
  });

  describe('Pause and Resume', () => {
    it('should pause and resume orchestration', async () => {
      let taskCount = 0;
      
      // Create slow mocks to allow time for pause
      const createSlowMock = () => jest.fn().mockImplementation(() => {
        taskCount++;
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({ success: true, data: { test: true } });
          }, 200);
        });
      });

      mockStoryArchitect.generateStoryStructure = createSlowMock();
      mockContentGenerator.generateContent = createSlowMock();

      // Start orchestration
      const orchestrationPromise = orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      // Pause after a short delay
      setTimeout(() => {
        orchestrator.pauseOrchestration();
        expect(orchestrator.isOrchestrationPaused()).toBe(true);
      }, 50);

      // Resume after another delay
      setTimeout(() => {
        orchestrator.resumeOrchestration();
        expect(orchestrator.isOrchestrationPaused()).toBe(false);
      }, 300);

      await orchestrationPromise;
      
      expect(taskCount).toBeGreaterThan(0);
    });
  });

  describe('Progress Tracking', () => {
    it('should track orchestration progress accurately', async () => {
      const progressUpdates: any[] = [];
      
      orchestrator.on('orchestration:progress', (progress) => {
        progressUpdates.push(progress);
      });

      // Setup mocks
      mockStoryArchitect.generateStoryStructure = jest.fn().mockResolvedValue({
        success: true,
        data: { test: true }
      });

      await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(progressUpdates.length).toBeGreaterThan(0);
      
      // Check progress structure
      const lastProgress = progressUpdates[progressUpdates.length - 1];
      expect(lastProgress).toHaveProperty('totalTasks');
      expect(lastProgress).toHaveProperty('completedTasks');
      expect(lastProgress).toHaveProperty('runningTasks');
      expect(lastProgress).toHaveProperty('failedTasks');
      expect(lastProgress).toHaveProperty('estimatedTimeRemaining');
      expect(lastProgress).toHaveProperty('currentPhase');
    });

    it('should provide real-time status', () => {
      const status = orchestrator.getOrchestrationStatus();
      
      expect(status).toHaveProperty('totalTasks');
      expect(status).toHaveProperty('completedTasks');
      expect(status).toHaveProperty('runningTasks');
      expect(status).toHaveProperty('failedTasks');
      expect(status).toHaveProperty('estimatedTimeRemaining');
      expect(status).toHaveProperty('currentPhase');
    });
  });

  describe('Task Retry Logic', () => {
    it('should retry failed tasks up to max retries', async () => {
      let attemptCount = 0;
      const retryEvents: any[] = [];
      
      orchestrator.on('task:retry', (event) => retryEvents.push(event));
      orchestrator.on('task:abandoned', (event) => retryEvents.push(event));

      // Fail first 2 attempts, succeed on 3rd
      mockStoryArchitect.generateStoryStructure = jest.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount <= 2) {
          return Promise.reject(new Error('Temporary failure'));
        }
        return Promise.resolve({
          success: true,
          data: { test: true }
        });
      });

      await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(attemptCount).toBe(3);
      expect(retryEvents.some(e => e.retryCount !== undefined)).toBe(true);
    });

    it('should abandon task after max retries exceeded', async () => {
      const abandonedEvents: any[] = [];
      
      orchestrator.on('task:abandoned', (event) => abandonedEvents.push(event));

      // Always fail
      mockStoryArchitect.generateStoryStructure = jest.fn().mockRejectedValue(
        new Error('Permanent failure')
      );

      await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(abandonedEvents.length).toBeGreaterThan(0);
      expect(abandonedEvents[0].finalError).toBeDefined();
    });
  });

  describe('Context Finalization', () => {
    it('should properly consolidate results into final context', async () => {
      // Setup comprehensive mocks
      const mockStoryData = {
        title: 'Test Story',
        premise: 'A test premise',
        genre: 'fantasy',
        themes: ['courage'],
        acts: [],
        conflicts: [],
        timeline: [],
        worldBuilding: {
          setting: {
            timeForPeriod: 'medieval',
            locations: [],
            culture: 'European-inspired',
            technology: 'pre-industrial'
          },
          rules: ['Magic exists'],
          history: []
        },
        plotPoints: []
      };

      const mockCharacterData = {
        protagonists: [{ id: '1', name: 'Hero', role: 'protagonist' }],
        antagonists: [{ id: '2', name: 'Villain', role: 'antagonist' }],
        supporting: [],
        relationships: []
      };

      const mockChapterData = {
        chapters: [
          {
            chapterNumber: 1,
            title: 'The Beginning',
            outline: 'Chapter outline',
            targetWordCount: 4000,
            scenes: []
          }
        ],
        totalWordCount: 80000,
        estimatedReadingTime: 240
      };

      mockStoryArchitect.generateStoryStructure = jest.fn().mockResolvedValue({
        success: true,
        data: mockStoryData
      });

      mockCharacterDeveloper.generateCharacters = jest.fn().mockResolvedValue({
        success: true,
        data: mockCharacterData
      });

      mockChapterPlanner.generateChapterOutlines = jest.fn().mockResolvedValue({
        success: true,
        data: mockChapterData
      });

      mockContentGenerator.generateContent = jest.fn().mockResolvedValue({
        worldRules: ['Dragons exist'],
        culturalNotes: ['Medieval society'],
        customEntries: { history: 'Ancient war' }
      });

      const result = await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      
      if (result.data) {
        expect(result.data.storyStructure).toBeDefined();
        expect(result.data.characters).toBeDefined();
        expect(result.data.chapterOutlines).toBeDefined();
        expect(result.data.storyBible).toBeDefined();
        expect(result.data.metadata).toBeDefined();
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors', async () => {
      // Mock constructor to throw
      const ConstructorError = new Error('Initialization failed');
      (StoryArchitectAgent as jest.MockedClass<typeof StoryArchitectAgent>).mockImplementation(() => {
        throw ConstructorError;
      });

      const result = await orchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle unknown agent types gracefully', async () => {
      // Inject a task with unknown agent
      const pipeline = orchestrator['createTaskPipeline']({
        projectId: 'test-project',
        settings: mockProjectSettings,
        projectSelections: mockProjectSettings,
        storyPrompt: 'A hero saves the world',
        targetWordCount: 80000,
        targetChapters: 20
      });

      // Add invalid task
      pipeline.push({
        id: 'invalid_task',
        type: 'story_analysis',
        dependencies: [],
        priority: 'low',
        estimatedDuration: 10,
        agent: 'unknown_agent',
        payload: {}
      });

      const results = await orchestrator['executeTaskPipeline'](pipeline);
      
      const invalidResult = results.find(r => r.taskId === 'invalid_task');
      expect(invalidResult?.success).toBe(false);
      expect(invalidResult?.error).toContain('Unknown agent');
    });
  });

  describe('Concurrency Control', () => {
    it('should handle zero max concurrent tasks gracefully', () => {
      const zeroTaskOrchestrator = new AdvancedAgentOrchestrator(0);
      expect(zeroTaskOrchestrator['maxConcurrentTasks']).toBe(0);
    });

    it('should handle very high concurrency', async () => {
      const highConcurrencyOrchestrator = new AdvancedAgentOrchestrator(100);
      
      // Create many quick tasks
      const quickMock = jest.fn().mockResolvedValue({
        success: true,
        data: { test: true }
      });

      mockStoryArchitect.generateStoryStructure = quickMock;
      mockContentGenerator.generateContent = quickMock;

      const result = await highConcurrencyOrchestrator.orchestrateProject(
        'test-project',
        mockProjectSettings,
        'A hero saves the world',
        80000,
        20
      );

      expect(result.success).toBeDefined();
    });
  });
});