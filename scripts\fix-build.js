#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Next.js build issues...\n');

// 1. Clear all caches
const cacheDirs = [
  '.next',
  'node_modules/.cache',
  '.turbo'
];

cacheDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (fs.existsSync(fullPath)) {
    console.log(`📁 Removing ${dir}...`);
    fs.rmSync(fullPath, { recursive: true, force: true });
  }
});

// 2. Create .env.local if missing
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env.local from .env.example...');
  const examplePath = path.join(process.cwd(), '.env.example');
  if (fs.existsSync(examplePath)) {
    fs.copyFileSync(examplePath, envPath);
  }
}

// 3. Check for problematic dependencies
console.log('\n🔍 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

// Check for conflicting versions
const supabasePackages = Object.keys(packageJson.dependencies).filter(pkg => 
  pkg.includes('@supabase')
);

console.log('Supabase packages found:', supabasePackages);

// 4. Create a next.config.js backup
const nextConfigPath = path.join(process.cwd(), 'next.config.js');
const backupPath = path.join(process.cwd(), 'next.config.js.backup');
console.log('\n💾 Creating next.config.js backup...');
fs.copyFileSync(nextConfigPath, backupPath);

console.log('\n✅ Build fix complete!');
console.log('\n📋 Next steps:');
console.log('1. Run: npm install');
console.log('2. Run: npm run dev');
console.log('\nIf issues persist:');
console.log('- Check for circular imports');
console.log('- Verify all environment variables are set');
console.log('- Try: npm install --force');