# BookScribe Achievement System

## Overview

The Achievement System gamifies the writing experience by rewarding users for reaching milestones, maintaining streaks, and exploring features. It provides motivation through a tiered reward system and tracks progress across multiple dimensions of writing activity.

## Architecture

### Database Schema

#### Achievements Table
Stores the master list of all available achievements:

```sql
achievements:
  - id: UUID (Primary Key)
  - code: VARCHAR(100) - Unique identifier
  - name: VARCHAR(255) - Display name
  - description: TEXT - Achievement description
  - points: INTEGER - Points awarded (default: 10)
  - tier: VARCHAR(20) - bronze, silver, gold, platinum
  - category: VARCHAR(50) - writing, streak, community, exploration, mastery
  - criteria: JSONB - Achievement unlock criteria
  - icon: VARCHAR(50) - Icon identifier
  - created_at: TIMESTAMPTZ
```

#### User Achievements Table
Tracks which achievements users have unlocked:

```sql
user_achievements:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - achievement_id: UUID - References achievements
  - unlocked_at: TIMESTAMPTZ - When achievement was earned
  - progress: INTEGER - Current progress value
  - metadata: JSONB - Additional achievement data
  - UNIQUE(user_id, achievement_id)
```

#### Achievement Progress Table
Tracks incremental progress toward achievements:

```sql
achievement_progress:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - achievement_code: VARCHAR(100)
  - progress_key: VARCHAR(100) - Type of progress
  - progress_value: INTEGER - Current value
  - updated_at: TIMESTAMPTZ
  - UNIQUE(user_id, achievement_code, progress_key)
```

## Achievement Categories

### 1. Writing Achievements
Track word count milestones and chapter completion:
- **First Words** (Bronze, 10pts): Write 100 words
- **Novice Writer** (Bronze, 25pts): Write 1,000 words
- **Apprentice Writer** (Silver, 50pts): Write 10,000 words
- **Journeyman Writer** (Gold, 100pts): Write 50,000 words
- **Master Writer** (Platinum, 200pts): Write 100,000 words
- **Chapter One** (Bronze, 20pts): Complete first chapter
- **Five Chapters** (Silver, 50pts): Complete 5 chapters
- **Ten Chapters** (Gold, 100pts): Complete 10 chapters

### 2. Exploration Achievements
Encourage feature discovery:
- **First Project** (Bronze, 15pts): Create first project
- **Multi-Project Author** (Silver, 40pts): Create 5 projects
- **Series Starter** (Silver, 30pts): Create first series
- **AI Explorer** (Bronze, 25pts): Use 5 different AI agents
- **First Export** (Bronze, 15pts): Export first project

### 3. Streak Achievements
Reward consistent writing habits:
- **Week Warrior** (Bronze, 30pts): 7-day writing streak
- **Monthly Marathon** (Gold, 100pts): 30-day writing streak

### 4. Community Achievements
Foster collaboration:
- **Collaborator** (Bronze, 20pts): Invite first team member
- **Team Leader** (Silver, 60pts): Work with 3 team members

### 5. Mastery Achievements
Advanced usage milestones:
- **Series Master** (Gold, 150pts): Complete 3-book series
- **AI Master** (Gold, 75pts): Generate 50,000 AI words
- **Export Master** (Silver, 50pts): Use all export formats

## API Endpoints

### GET /api/achievements
Fetch user achievements with optional filtering:

```typescript
// Request
GET /api/achievements?category=writing&unlocked=true

// Response
{
  achievements: [
    {
      id: "uuid",
      achievement_id: "uuid",
      user_id: "uuid",
      title: "Master Writer",
      description: "Write 100,000 words",
      category: "writing",
      tier: "platinum",
      icon: "book-open",
      current_value: 125000,
      target_value: 100000,
      unlocked_at: "2024-01-15T10:30:00Z",
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-15T10:30:00Z"
    }
  ],
  total: 15,
  unlocked: 8
}
```

### POST /api/achievements/check
Check and unlock achievements for current user:

```typescript
// Request
POST /api/achievements/check
{
  trigger: "chapter_completed",
  metadata: {
    project_id: "uuid",
    chapter_id: "uuid",
    word_count: 2500
  }
}

// Response
{
  newly_unlocked: [
    {
      achievement_id: "uuid",
      name: "Five Chapters",
      tier: "silver",
      points: 50
    }
  ],
  progress_updates: [
    {
      achievement_code: "ten_chapters",
      current: 5,
      target: 10
    }
  ]
}
```

## Achievement Processing

### Automatic Triggers
Achievements are automatically checked when:
1. **Chapter Updates**: Word count and completion achievements
2. **Project Creation**: Project and exploration achievements
3. **AI Usage**: AI-related achievements tracked via usage logs
4. **Team Invitations**: Collaboration achievements

### Database Triggers
```sql
-- Trigger on chapter updates
CREATE TRIGGER check_achievements_on_chapter_update
  AFTER INSERT OR UPDATE ON chapters
  FOR EACH ROW
  EXECUTE FUNCTION check_achievements_on_update();

-- Trigger on project creation
CREATE TRIGGER check_achievements_on_project_create
  AFTER INSERT ON projects
  FOR EACH ROW
  EXECUTE FUNCTION check_achievements_on_update();
```

### Progress Tracking Function
```sql
CREATE OR REPLACE FUNCTION track_achievement_progress(
  p_user_id UUID,
  p_achievement_code VARCHAR,
  p_progress_key VARCHAR,
  p_increment INTEGER DEFAULT 1
)
```

## UI Components

### Achievement Badge
Displays individual achievement:
```tsx
<AchievementBadge
  achievement={achievement}
  size="small" | "medium" | "large"
  showProgress={true}
  onClick={handleClick}
/>
```

### Achievement List
Shows all achievements with filtering:
```tsx
<AchievementList
  userId={userId}
  category="writing"
  showLocked={true}
  onAchievementClick={handleAchievementClick}
/>
```

### Achievement Notification
Toast notification for newly unlocked:
```tsx
<AchievementUnlockedToast
  achievement={newAchievement}
  onClose={handleClose}
  duration={5000}
/>
```

## Implementation Details

### Criteria Structure
Achievement criteria stored as JSONB:
```json
{
  "type": "word_count",
  "value": 50000,
  "scope": "total",
  "conditions": {
    "min_projects": 1
  }
}
```

### Progress Calculation
Different achievement types have specific progress calculations:
- **Word Count**: Sum of all chapter word counts
- **Chapter Count**: Count of completed chapters
- **Streak**: Consecutive days with writing activity
- **AI Usage**: Tokens generated or agent invocations

### Security

#### Row Level Security
All achievement tables have RLS enabled:
- Users can only view their own achievements
- System can create achievements for any user
- Users can update progress for their own achievements

#### API Security
- All endpoints require authentication
- User can only query their own achievements
- Admin endpoints for managing achievement definitions

## Performance Considerations

### Indexes
```sql
CREATE INDEX idx_user_achievements_user ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_unlocked ON user_achievements(unlocked_at DESC);
CREATE INDEX idx_achievement_progress_user ON achievement_progress(user_id);
```

### Caching Strategy
- Achievement definitions cached in memory (rarely change)
- User achievement status cached for 5 minutes
- Progress updates batched to reduce database writes

## Future Enhancements

1. **Social Features**
   - Achievement sharing
   - Leaderboards
   - Achievement comparisons

2. **Custom Achievements**
   - Project-specific achievements
   - User-created challenges
   - Time-limited events

3. **Rewards Integration**
   - Unlock themes/features
   - Badge customization
   - Profile flair

4. **Analytics**
   - Achievement completion rates
   - Popular achievement paths
   - User engagement metrics

## Troubleshooting

### Common Issues

1. **Achievements Not Unlocking**
   - Check trigger execution
   - Verify criteria calculation
   - Ensure RLS policies allow access

2. **Progress Not Updating**
   - Check progress tracking function
   - Verify unique constraints
   - Monitor trigger execution

3. **Performance Issues**
   - Review index usage
   - Optimize criteria queries
   - Consider progress batching

## Related Systems
- Analytics System (tracks user activity)
- Writing Goals (complementary motivation system)
- User Profiles (displays achievements)
- Notification System (achievement alerts)