/**
 * Typed Streaming API
 * Provides strongly-typed streaming responses with progress and quality tracking
 */

import { NextRequest } from 'next/server';
import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { withAuth } from '@/lib/api/auth-helpers';
import { aiLimiter, getClientIP, createRateLimitResponse } from '@/lib/rate-limiter-unified';
import { AI_MODELS, AI_TEMPERATURE, AI_MAX_TOKENS } from '@/lib/config/ai-settings';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';
import {
  StreamEventType,
  ContentType,
  createStreamId,
  calculateProgress,
  type StreamEvent,
  type StreamMetadata,
  type ChapterStreamData,
  type CharacterStreamData,
  type AnalysisStreamData,
  type AgentStreamData
} from '@/lib/types/streaming-types';
import { createServerSupabaseClient } from '@/lib/supabase';
import { trackFeatureUsage } from '@/lib/monitoring/sentry';
import { streamDataService } from '@/lib/services/stream-data-service';
import { RATE_LIMITS } from '@/lib/constants'

// Request validation schema
const typedStreamRequestSchema = z.object({
  prompt: z.string().min(1).max(10000),
  contentType: z.nativeEnum(ContentType),
  context: z.object({
    projectId: z.string(),
    chapterId: z.string().optional(),
    characterIds: z.array(z.string()).optional(),
    previousContent: z.string().optional(),
    storyBible: z.record(z.unknown()).optional(),
    voiceProfile: z.object({
      tone: z.string(),
      style: z.string(),
      vocabulary: z.array(z.string()),
      sentencePatterns: z.array(z.string()),
      narrativePerspective: z.enum(['first', 'second', 'third-limited', 'third-omniscient'])
    }).optional()
  }).optional(),
  model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(4000).optional(),
  estimatedTokens: z.number().min(1).max(4000).optional(),
  streamId: z.string().optional()
});

export const POST = withAuth(async (request: NextRequest) => {
  const encoder = new TextEncoder();
  let streamId: string;

  try {
    // Rate limiting
    const clientIP = getClientIP(request);
    const rateLimitResult = aiLimiter.check(RATE_LIMITS.SERVICE_ORCHESTRATOR_READ, clientIP);
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse();
    }

    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse and validate request
    const body = await request.json();
    const validatedData = typedStreamRequestSchema.parse(body);
    
    streamId = validatedData.streamId || createStreamId();
    const startTime = Date.now();

    const {
      prompt,
      contentType,
      context,
      model = AI_MODELS.PRIMARY,
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = AI_MAX_TOKENS.STANDARD,
      estimatedTokens = 2000
    } = validatedData;

    // Track feature usage
    trackFeatureUsage('typed_streaming', {
      contentType,
      model,
      hasContext: !!context
    });

    // Create stream metadata
    const metadata: StreamMetadata = {
      model,
      temperature,
      maxTokens,
      streamId,
      startTime
    };

    // Create readable stream
    const stream = new ReadableStream({
      async start(controller) {
        // Send start event
        const startEvent: StreamEvent = {
          type: StreamEventType.START,
          timestamp: Date.now(),
          streamId,
          metadata
        };
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(startEvent)}\n\n`));

        try {
          // Generate content based on type
          const systemPrompt = getSystemPrompt(contentType, context);
          
          let tokens = 0;
          const result = await streamText({
            model: openai(model),
            messages: [
              { role: 'system', content: systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature,
            maxTokens,
            onChunk: async ({ chunk }) => {
              if (chunk.type === 'text-delta') {
                tokens++;
                
                // Send token event
                const tokenEvent: StreamEvent = {
                  type: StreamEventType.TOKEN,
                  timestamp: Date.now(),
                  streamId,
                  data: {
                    token: chunk.textDelta,
                    timestamp: Date.now(),
                    index: tokens
                  }
                };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(tokenEvent)}\n\n`));

                // Send progress updates periodically
                if (tokens % 10 === 0) {
                  const progressEvent: StreamEvent = {
                    type: StreamEventType.PROGRESS,
                    timestamp: Date.now(),
                    streamId,
                    data: {
                      tokens,
                      estimatedTokens,
                      percentComplete: calculateProgress(tokens, estimatedTokens),
                      tokensPerSecond: (tokens / ((Date.now() - startTime) / TIME_MS.SECOND)),
                      estimatedTimeRemaining: ((estimatedTokens - tokens) / (tokens / ((Date.now() - startTime) / TIME_MS.SECOND)))
                    }
                  };
                  controller.enqueue(encoder.encode(`data: ${JSON.stringify(progressEvent)}\n\n`));
                }
              }
            },
            onFinish: async ({ text, finishReason, usage }) => {
              // Generate structured data based on content type
              const structuredData = await generateStructuredData(contentType, text, context);
              
              // Send structure event
              if (structuredData) {
                const structureEvent: StreamEvent = {
                  type: StreamEventType.STRUCTURE,
                  timestamp: Date.now(),
                  streamId,
                  data: {
                    partial: false,
                    structure: structuredData
                  }
                };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(structureEvent)}\n\n`));
              }

              // Calculate quality score
              const qualityScore = await calculateQualityScore(text, contentType, context);
              if (qualityScore) {
                const qualityEvent: StreamEvent = {
                  type: StreamEventType.QUALITY,
                  timestamp: Date.now(),
                  streamId,
                  data: qualityScore
                };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(qualityEvent)}\n\n`));
              }

              // Send complete event
              const completeEvent: StreamEvent = {
                type: StreamEventType.COMPLETE,
                timestamp: Date.now(),
                streamId,
                data: {
                  content: text,
                  tokens,
                  duration: Date.now() - startTime,
                  metadata: {
                    finishReason,
                    usage,
                    structuredData: !!structuredData,
                    qualityScore: qualityScore?.score
                  }
                }
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(completeEvent)}\n\n`));
              
              controller.close();
              
              logger.info(`Typed streaming completed for user ${user.id}`, {
                contentType,
                streamId,
                tokens,
                duration: Date.now() - startTime
              });
            }
          });
        } catch (error) {
          // Send error event
          const errorEvent: StreamEvent = {
            type: StreamEventType.ERROR,
            timestamp: Date.now(),
            streamId,
            data: {
              code: 'GENERATION_ERROR',
              message: error instanceof Error ? error.message : 'Unknown error',
              recoverable: false
            }
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorEvent)}\n\n`));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Stream-ID': streamId
      }
    });

  } catch (error) {
    logger.error('Error in typed streaming:', error);
    
    if (error instanceof z.ZodError) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid request data', 
          details: error.errors 
        }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
});

// Helper functions
function getSystemPrompt(contentType: ContentType, context?: any): string {
  const basePrompts = {
    [ContentType.CHAPTER]: `You are an expert creative writer specializing in novel chapters. Create engaging, well-paced chapters that advance the plot and develop characters. Maintain consistency with the established narrative voice and style.`,
    [ContentType.SCENE]: `You are an expert scene writer. Create vivid, immersive scenes with sensory details, character interactions, and emotional depth. Each scene should serve a purpose in advancing the story.`,
    [ContentType.DIALOGUE]: `You are an expert dialogue writer. Create natural, character-appropriate dialogue that reveals personality, advances the plot, and maintains the story's tone. Include subtext and emotional nuance.`,
    [ContentType.DESCRIPTION]: `You are an expert descriptive writer. Create rich, sensory descriptions that paint clear pictures without overwhelming the reader. Use varied sentence structure and vivid imagery.`,
    [ContentType.CHARACTER]: `You are an expert character developer. Create complex, believable characters with clear motivations, realistic flaws, and compelling backstories that serve the narrative.`,
    [ContentType.OUTLINE]: `You are an expert story structure specialist. Create detailed outlines that balance plot progression, character development, and thematic elements.`,
    [ContentType.SYNOPSIS]: `You are an expert synopsis writer. Create compelling, concise summaries that capture the essence of the story while maintaining intrigue.`,
    [ContentType.GENERAL]: `You are an expert creative writing assistant. Generate high-quality content that maintains consistency with the established narrative.`
  };

  let prompt = basePrompts[contentType];

  if (context?.voiceProfile) {
    prompt += `\n\nVoice Profile:
- Tone: ${context.voiceProfile.tone}
- Style: ${context.voiceProfile.style}
- Narrative Perspective: ${context.voiceProfile.narrativePerspective}
- Key Vocabulary: ${context.voiceProfile.vocabulary.join(', ')}`;
  }

  return prompt;
}

async function generateStructuredData(
  contentType: ContentType, 
  content: string, 
  context?: Record<string, unknown>
): Promise<ChapterStreamData | CharacterStreamData | AnalysisStreamData | null> {
  try {
    // Use the real stream data service
    const result = await streamDataService.generateStructuredData(
      contentType.toLowerCase() as ContentType,
      content,
      context
    );
    
    return result || null;
  } catch (error) {
    logger.error('Error generating structured data:', error);
    return null;
  }
}

async function calculateQualityScore(
  content: string, 
  contentType: ContentType,
  context?: Record<string, unknown>
): Promise<{ score: number; metrics: Record<string, number> } | null> {
  try {
    // Use the real stream data service for quality assessment
    const result = await streamDataService.calculateQualityScore(
      content,
      contentType.toLowerCase() as ContentType,
      context
    );
    
    return result || null;
  } catch (error) {
    logger.error('Error calculating quality score:', error);
    return null;
  }
}