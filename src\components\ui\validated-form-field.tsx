import * as React from 'react';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { requiredFieldProps, errorMessageProps, formFieldProps } from '@/lib/validation/form-utils';

interface BaseFieldProps {
  id: string;
  label: string;
  error?: string;
  touched?: boolean;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  description?: string;
  'aria-describedby'?: string;
}

interface InputFieldProps extends BaseFieldProps {
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'date';
  value: string | number;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  autoComplete?: string;
  min?: number;
  max?: number;
  step?: number;
}

interface TextareaFieldProps extends BaseFieldProps {
  type: 'textarea';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  rows?: number;
  maxLength?: number;
  showCharacterCount?: boolean;
}

interface SelectFieldProps extends BaseFieldProps {
  type: 'select';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  options: Array<{ value: string; label: string }>;
}

type ValidatedFormFieldProps = InputFieldProps | TextareaFieldProps | SelectFieldProps;

/**
 * Standardized form field component with consistent validation display
 * Supports input, textarea, and select field types
 */
export function ValidatedFormField(props: ValidatedFormFieldProps) {
  const {
    id,
    label,
    error,
    touched = false,
    required = false,
    disabled = false,
    className,
    description,
    'aria-describedby': ariaDescribedBy,
    ...fieldProps
  } = props;

  const showError = touched && error;
  const errorId = `${id}-error`;
  const descriptionId = `${id}-description`;
  
  const ariaProps = {
    'aria-invalid': showError ? 'true' : 'false',
    'aria-describedby': cn(
      description && descriptionId,
      showError && errorId,
      ariaDescribedBy
    ).trim() || undefined,
    'aria-required': required,
  };

  const renderField = () => {
    switch (fieldProps.type) {
      case 'textarea': {
        const { value, onChange, onBlur, placeholder, rows = 4, maxLength, showCharacterCount } = fieldProps;
        return (
          <>
            <Textarea
              id={id}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onBlur={onBlur}
              placeholder={placeholder}
              rows={rows}
              maxLength={maxLength}
              disabled={disabled}
              className={cn(
                showError && 'border-destructive focus:ring-destructive',
                'transition-colors'
              )}
              {...ariaProps}
            />
            {showCharacterCount && maxLength && (
              <div className="text-xs text-muted-foreground text-right mt-1">
                {value.length} / {maxLength}
              </div>
            )}
          </>
        );
      }

      case 'select': {
        const { value, onChange, onBlur, placeholder, options } = fieldProps;
        return (
          <Select value={value} onValueChange={onChange} disabled={disabled}>
            <SelectTrigger
              id={id}
              onBlur={onBlur}
              className={cn(
                showError && 'border-destructive focus:ring-destructive',
                'transition-colors'
              )}
              {...ariaProps}
            >
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }

      default: {
        const { 
          type, 
          value, 
          onChange, 
          onBlur, 
          placeholder, 
          autoComplete,
          min,
          max,
          step
        } = fieldProps as InputFieldProps;
        
        return (
          <Input
            id={id}
            type={type}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            placeholder={placeholder}
            autoComplete={autoComplete}
            min={min}
            max={max}
            step={step}
            disabled={disabled}
            className={cn(
              showError && 'border-destructive focus:ring-destructive',
              'transition-colors'
            )}
            {...ariaProps}
          />
        );
      }
    }
  };

  return (
    <div className={cn(formFieldProps.className, className)}>
      <Label 
        htmlFor={id} 
        className={cn(
          required && requiredFieldProps.className,
          showError && 'text-destructive'
        )}
      >
        {label}
      </Label>
      
      {description && (
        <p id={descriptionId} className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      
      {renderField()}
      
      {showError && (
        <p id={errorId} {...errorMessageProps}>
          {error}
        </p>
      )}
    </div>
  );
}

/**
 * Required field indicator component
 * Use this to show which fields are required in a form
 */
export function RequiredFieldIndicator() {
  return (
    <p className="text-sm text-muted-foreground">
      <span className="text-destructive">*</span> indicates required fields
    </p>
  );
}

/**
 * Form section component for grouping related fields
 */
interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function FormSection({ title, description, children, className }: FormSectionProps) {
  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <h3 className="text-lg font-medium">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      {children}
    </div>
  );
}

/**
 * Form error summary component
 * Displays all form errors in one place for accessibility
 */
interface FormErrorSummaryProps {
  errors: Array<{ field: string; message: string }>;
  title?: string;
}

export function FormErrorSummary({ errors, title = 'Please fix the following errors:' }: FormErrorSummaryProps) {
  if (errors.length === 0) return null;

  return (
    <div 
      role="alert" 
      aria-live="assertive"
      className="rounded-lg border border-destructive/50 bg-destructive/10 p-4"
    >
      <h3 className="font-medium text-destructive mb-2">{title}</h3>
      <ul className="list-disc list-inside space-y-1">
        {errors.map((error, index) => (
          <li key={`${error.field}-${index}`} className="text-sm text-destructive">
            {error.message}
          </li>
        ))}
      </ul>
    </div>
  );
}