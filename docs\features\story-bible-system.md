# BookScribe Story Bible System

## Overview

The Story Bible System serves as the central repository for all story-related information in a project. It maintains a comprehensive, AI-accessible database of story structure, character profiles, world-building elements, plot threads, and contextual information that ensures consistency across the entire narrative.

## Architecture

### Database Schema

#### Story Bibles Table
Central storage for comprehensive story context:

```sql
story_bibles:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - structure_data: JSONB - Complete story structure
  - character_data: JSONB - All character profiles and relationships
  - world_data: JSONB - World building elements
  - plot_data: JSONB - Plot threads and story arcs
  - theme_data: JSONB - Themes and motifs
  - timeline_data: JSONB - Chronological events
  - context_version: INTEGER - Version for updates
  - last_sync_at: TIMESTAMPTZ - Last AI sync time
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Story Bible Entries Table
Individual entries for granular management:

```sql
story_bible:
  - id: UUID (Primary Key)
  - project_id: UUID - References projects
  - entry_type: VARCHAR(50) - character, location, event, item, concept, rule
  - name: <PERSON><PERSON><PERSON><PERSON>(255) - Entry name/title
  - description: TEXT - Detailed description
  - attributes: JSONB - Type-specific attributes
  - relationships: JSONB - Links to other entries
  - chapter_introduced: INTEGER - First appearance
  - chapters_referenced: INTEGER[] - All appearances
  - importance: VARCHAR(20) - minor, supporting, major, critical
  - is_active: BOOLEAN - Currently relevant
  - tags: TEXT[] - Searchable tags
  - notes: TEXT - Author notes
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

## Story Bible Components

### 1. Structure Data
Overall story architecture:

```json
{
  "structure_data": {
    "genre": ["fantasy", "adventure"],
    "target_audience": "young adult",
    "narrative_structure": "three_act",
    "point_of_view": "third_person_limited",
    "tense": "past",
    "estimated_length": 85000,
    "acts": [
      {
        "number": 1,
        "title": "The Call",
        "description": "Hero discovers their destiny",
        "chapters": [1, 2, 3, 4, 5],
        "key_events": ["discovery", "refusal", "acceptance"],
        "word_count_target": 25000
      }
    ],
    "story_arc": {
      "setup": "Ordinary world introduction",
      "inciting_incident": "Mysterious letter arrives",
      "rising_action": ["Training", "First quest", "Betrayal"],
      "climax": "Final confrontation",
      "resolution": "New world order"
    }
  }
}
```

### 2. Character Data
Comprehensive character information:

```json
{
  "character_data": {
    "protagonists": [
      {
        "id": "char_001",
        "name": "Elena Starweaver",
        "role": "main_protagonist",
        "profile": {
          "age": 17,
          "occupation": "Apprentice Mage",
          "personality": ["curious", "impulsive", "loyal"],
          "physical": "Tall, auburn hair, green eyes",
          "backstory": "Orphaned at young age...",
          "motivation": "Find her true heritage",
          "fears": ["abandonment", "failure"],
          "skills": ["basic magic", "herbalism"]
        },
        "arc": {
          "start": "Naive apprentice",
          "middle": "Questioning beliefs",
          "end": "Confident leader"
        },
        "relationships": {
          "mentor": "char_002",
          "rival": "char_003",
          "love_interest": "char_004"
        }
      }
    ],
    "supporting_characters": [...],
    "antagonists": [...],
    "minor_characters": [...]
  }
}
```

### 3. World Data
World-building elements:

```json
{
  "world_data": {
    "setting": {
      "name": "Aethermoor",
      "type": "fantasy_realm",
      "time_period": "medieval_equivalent",
      "technology_level": "pre_industrial_with_magic"
    },
    "locations": {
      "kingdoms": [...],
      "cities": [...],
      "landmarks": [...]
    },
    "magic_system": {
      "name": "Elemental Weaving",
      "source": "Natural energy",
      "limitations": ["Physical exhaustion", "Training required"],
      "types": ["Fire", "Water", "Earth", "Air", "Spirit"]
    },
    "cultures": [...],
    "history": {
      "major_events": [...],
      "timeline": [...]
    },
    "rules": {
      "physical_laws": [...],
      "social_customs": [...],
      "magical_laws": [...]
    }
  }
}
```

### 4. Plot Data
Plot threads and story elements:

```json
{
  "plot_data": {
    "main_plot": {
      "title": "The Prophecy Fulfilled",
      "description": "Ancient prophecy coming true",
      "stages": [...],
      "resolution": "pending"
    },
    "subplots": [
      {
        "title": "The Lost Artifact",
        "related_characters": ["char_001", "char_002"],
        "chapters": [3, 7, 12, 15],
        "status": "active"
      }
    ],
    "mysteries": [...],
    "foreshadowing": [...]
  }
}
```

## API Endpoints

### Story Bible Management

#### GET /api/story-bible
Retrieve story bible entries:

```typescript
// Request
GET /api/story-bible?project_id=uuid&entry_type=character&is_active=true

// Response
{
  entries: [
    {
      id: "uuid",
      entry_type: "character",
      name: "Elena Starweaver",
      description: "The main protagonist...",
      attributes: {
        role: "protagonist",
        age: 17,
        affiliation: "Mage Guild"
      },
      relationships: {
        mentor: "uuid_mentor",
        rival: "uuid_rival"
      },
      chapter_introduced: 1,
      chapters_referenced: [1, 3, 5, 7, 9],
      importance: "critical",
      is_active: true
    }
  ],
  total: 45
}
```

#### POST /api/story-bible
Create new story bible entry:

```typescript
// Request
{
  project_id: "uuid",
  entry_type: "location",
  name: "The Crystal Tower",
  description: "Ancient tower of magical learning",
  attributes: {
    location_type: "landmark",
    region: "Northern Kingdoms",
    significance: "magical",
    accessibility: "restricted"
  },
  chapter_introduced: 3
}
```

#### PUT /api/story-bible/update
Bulk update story bible:

```typescript
// Request
{
  project_id: "uuid",
  updates: {
    structure_data: { /* updated structure */ },
    character_data: { /* updated characters */ }
  },
  sync_with_ai: true
}
```

### Story Bible Analysis

#### POST /api/story-bible/analyze
Analyze story bible for issues:

```typescript
// Request
{
  project_id: "uuid",
  analysis_type: "consistency" | "completeness" | "relationships"
}

// Response
{
  issues: [
    {
      type: "inconsistency",
      severity: "warning",
      entry_ids: ["uuid1", "uuid2"],
      description: "Character age inconsistency between entries",
      suggestion: "Update Elena's age to 18 in chapter 10"
    }
  ],
  stats: {
    total_entries: 156,
    characters: 45,
    locations: 38,
    completeness_score: 0.87
  }
}
```

#### POST /api/story-bible/sync
Sync with AI agents:

```typescript
// Request
{
  project_id: "uuid",
  sync_type: "full" | "incremental",
  include_chapters: true
}

// Response
{
  synced: true,
  entries_processed: 156,
  context_tokens: 45000,
  compression_applied: true
}
```

## Integration with AI Agents

### Context Provision
Story bible provides context to all AI agents:

```typescript
interface AgentContext {
  story_bible: {
    structure: StoryStructure;
    characters: CharacterMap;
    world: WorldData;
    plot: PlotThreads;
    relevant_entries: StoryBibleEntry[];
  };
  current_focus?: {
    chapter?: number;
    character?: string;
    location?: string;
  };
}
```

### Automatic Updates
AI agents can suggest story bible updates:

```typescript
interface StoryBibleUpdate {
  agent_id: string;
  entry_type: string;
  suggested_changes: {
    entry_id?: string;
    field: string;
    old_value: any;
    new_value: any;
    reason: string;
  }[];
  confidence: number;
}
```

## UI Components

### Story Bible Explorer
```tsx
<StoryBibleExplorer
  projectId={projectId}
  viewMode="tree" | "list" | "graph"
  filterType={entryType}
  onEntrySelect={handleSelect}
  searchEnabled={true}
/>
```

### Entry Editor
```tsx
<StoryBibleEntryEditor
  entry={selectedEntry}
  onChange={handleChange}
  showRelationships={true}
  aiSuggestions={true}
  validationEnabled={true}
/>
```

### Relationship Graph
```tsx
<StoryBibleGraph
  entries={entries}
  focusEntry={selectedEntry}
  showTypes={['character', 'location']}
  interactive={true}
  layout="force-directed"
/>
```

### Quick Reference Panel
```tsx
<StoryBibleQuickRef
  projectId={projectId}
  position="right"
  collapsible={true}
  searchable={true}
  pinnedEntries={pinnedIds}
/>
```

## Story Bible Features

### 1. Auto-population
Automatically extract entries from content:
- Character detection from chapters
- Location extraction
- Event timeline building
- Relationship mapping

### 2. Consistency Checking
Real-time validation:
- Character consistency across chapters
- Timeline validation
- Relationship logic
- World rule adherence

### 3. Smart Suggestions
AI-powered recommendations:
- Missing entry detection
- Relationship suggestions
- Plot hole identification
- Character development tracking

### 4. Version Control
Track story bible evolution:
- Entry history
- Change tracking
- Rollback capability
- Diff visualization

## Search & Discovery

### Full-text Search
```typescript
interface StoryBibleSearch {
  query: string;
  filters: {
    entry_types?: string[];
    importance?: string[];
    chapters?: number[];
    is_active?: boolean;
  };
  include_content?: boolean; // Search in chapter content
}
```

### Relationship Queries
Find connected entries:
- Character relationships
- Location hierarchies
- Event chains
- Thematic connections

## Performance Optimization

### Caching Strategy
- Full story bible cached in memory
- Incremental updates only
- Chapter-specific subsets
- Compressed storage for large projects

### Indexes
```sql
CREATE INDEX idx_story_bible_project_entry_type 
  ON story_bible(project_id, entry_type);
CREATE INDEX idx_story_bible_importance 
  ON story_bible(importance) WHERE is_active = true;
CREATE INDEX idx_story_bible_chapters_gin 
  ON story_bible USING GIN (chapters_referenced);
CREATE INDEX idx_story_bible_tags_gin 
  ON story_bible USING GIN (tags);
```

## Security & Access

### Row Level Security
```sql
-- Users can only access their project's story bible
CREATE POLICY "Project story bible access" ON story_bible
  FOR ALL USING (
    project_id IN (
      SELECT id FROM projects WHERE user_id = auth.uid()
    )
  );
```

### Collaboration
- Read access for viewers
- Edit access for editors
- Admin can manage all entries

## Best Practices

### 1. Entry Organization
- Use consistent naming conventions
- Tag extensively for discoverability
- Keep descriptions concise but complete
- Update importance as story evolves

### 2. Relationship Management
- Define all significant relationships
- Use bidirectional links
- Document relationship evolution
- Track relationship changes

### 3. Regular Maintenance
- Review and update after each chapter
- Sync with AI agents regularly
- Check consistency reports
- Archive inactive entries

## Future Enhancements

1. **Visual Timeline**
   - Interactive timeline view
   - Event relationship mapping
   - Parallel timeline support

2. **AI Enhancement**
   - Auto-generate entries from text
   - Predict plot developments
   - Suggest missing elements

3. **Export Features**
   - Story bible as PDF/website
   - Reader-friendly guides
   - Character sheets

4. **Templates**
   - Genre-specific templates
   - Pre-built story structures
   - Character archetypes

## Related Systems
- Character Management System
- Location System
- Timeline Events System
- AI Agent System (context consumer)