'use client'

import { <PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON><PERSON>Up, Zap, Crown } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { IntelligenceScaleVisual } from './intelligence-scale-visual'

const intelligenceLevels = [
  {
    tier: 'Starter',
    icon: Brain,
    title: 'Essential Intelligence',
    description: 'Perfect for first drafts and basic storytelling',
    capabilities: [
      'Understands basic plot structure',
      'Creates simple character outlines',
      'Maintains chapter-level consistency',
      'Handles up to 10k words effectively'
    ],
    color: 'text-success dark:text-green-400',
    bgColor: 'bg-success-light dark:bg-green-900/20'
  },
  {
    tier: 'Writer',
    icon: Sparkles,
    title: 'Enhanced Intelligence',
    description: 'Deeper understanding for complex narratives',
    capabilities: [
      'Analyzes multi-layered plot structures',
      'Develops nuanced character arcs',
      'Maintains consistency across 20+ chapters',
      'Handles up to 50k words with full context'
    ],
    color: 'text-info dark:text-blue-400',
    bgColor: 'bg-info-light dark:bg-blue-900/20'
  },
  {
    tier: 'Author',
    icon: TrendingUp,
    title: 'Premium Intelligence',
    description: 'Advanced AI for professional storytelling',
    capabilities: [
      'Masters complex narrative techniques',
      'Creates deeply interconnected character webs',
      'Maintains voice across 100k+ words',
      'Understands subtext and themes'
    ],
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20'
  },
  {
    tier: 'Professional',
    icon: Zap,
    title: 'Advanced Intelligence',
    description: 'Maximum capability for ambitious projects',
    capabilities: [
      'Handles epic multi-book series',
      'Tracks 50+ character arcs simultaneously',
      'Maintains consistency across 300k+ words',
      'Deep understanding of genre conventions'
    ],
    color: 'text-warning dark:text-orange-400',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20'
  },
  {
    tier: 'Studio',
    icon: Crown,
    title: 'Ultimate Intelligence',
    description: 'Unrestricted AI for the most demanding projects',
    capabilities: [
      'Manages shared universes with ease',
      'Cross-references unlimited content',
      'Priority processing for instant responses',
      'Handles any narrative complexity'
    ],
    color: 'text-primary',
    bgColor: 'bg-primary/10'
  }
]

export function AIIntelligenceShowcase() {
  return (
    <div className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold font-literary-display mb-4">
          AI That Grows With Your Story
        </h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Our adaptive AI intelligence scales with your project's complexity. 
          Simple stories need efficient AI, while epic narratives demand deeper understanding.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 max-w-7xl mx-auto">
        {intelligenceLevels.map((level) => {
          const Icon = level.icon
          return (
            <Card key={level.tier} className="relative hover:shadow-lg transition-all">
              <CardHeader className="space-y-2">
                <div className={`inline-flex p-3 rounded-lg ${level.bgColor} mb-2`}>
                  <Icon className={`w-6 h-6 ${level.color}`} />
                </div>
                <Badge variant="outline" className="absolute top-4 right-4">
                  {level.tier}
                </Badge>
                <CardTitle className="text-lg">{level.title}</CardTitle>
                <CardDescription>{level.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {level.capabilities.map((capability, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-start">
                      <span className="mr-2 text-primary">•</span>
                      {capability}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="mt-12 text-center">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full">
          <Sparkles className="w-4 h-4 text-primary" />
          <span className="text-sm font-medium">
            Higher tiers unlock deeper contextual understanding and narrative sophistication
          </span>
        </div>
      </div>

      {/* Intelligence Scale Visual */}
      <IntelligenceScaleVisual />
    </div>
  )
}