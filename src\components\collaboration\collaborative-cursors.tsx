'use client'

import { useEffect, useRef } from 'react'
import type { CollaborationUser } from '@/lib/services/unified-collaboration-service'
import type { editor } from 'monaco-editor'

interface CollaborativeCursorsProps {
  users: CollaborationUser[]
  editorInstance: editor.IStandaloneCodeEditor | null
}

export function CollaborativeCursors({ users, editorInstance }: CollaborativeCursorsProps) {
  const decorationsRef = useRef<string[]>([])

  useEffect(() => {
    if (!editorInstance) return

    // Clear previous decorations
    decorationsRef.current = editorInstance.deltaDecorations(
      decorationsRef.current,
      []
    )

    // Create decorations for each user's cursor and selection
    const newDecorations: editor.IModelDeltaDecoration[] = []

    users.forEach(user => {
      // Add cursor decoration
      if (user.cursor) {
        newDecorations.push({
          range: {
            startLineNumber: user.cursor.line,
            startColumn: user.cursor.column,
            endLineNumber: user.cursor.line,
            endColumn: user.cursor.column
          },
          options: {
            className: 'collaboration-cursor',
            hoverMessage: { value: user.name },
            stickiness: 1,
            zIndex: 100,
            beforeContentClassName: 'collaboration-cursor-before',
            afterContentClassName: 'collaboration-cursor-after',
            inlineClassName: `collaboration-cursor-${user.id}`,
            minimap: {
              color: user.color,
              position: 2
            }
          }
        })

        // Add custom CSS for this user's cursor
        const style = document.createElement('style')
        style.textContent = `
          .collaboration-cursor-${user.id}::before {
            content: '';
            position: absolute;
            top: 0;
            left: -2px;
            width: 2px;
            height: 100%;
            background-color: ${user.color};
            animation: cursor-blink 1s infinite;
          }
          
          .collaboration-cursor-${user.id}::after {
            content: '${user.name}';
            position: absolute;
            top: -20px;
            left: -2px;
            padding: 2px 6px;
            background-color: ${user.color};
            color: hsl(var(--background));
            font-size: 11px;
            font-weight: 500;
            border-radius: 3px;
            white-space: nowrap;
            pointer-events: none;
            z-index: 101;
          }
          
          @keyframes cursor-blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
          }
        `
        document.head.appendChild(style)
      }

      // Add selection decoration
      if (user.selection) {
        newDecorations.push({
          range: {
            startLineNumber: user.selection.startLine,
            startColumn: user.selection.startColumn,
            endLineNumber: user.selection.endLine,
            endColumn: user.selection.endColumn
          },
          options: {
            className: `collaboration-selection collaboration-selection-${user.id}`,
            hoverMessage: { value: `Selected by ${user.name}` },
            minimap: {
              color: user.color,
              position: 1
            }
          }
        })

        // Add custom CSS for this user's selection
        const selectionStyle = document.createElement('style')
        selectionStyle.textContent = `
          .collaboration-selection-${user.id} {
            background-color: ${user.color}20;
            border-left: 2px solid ${user.color};
          }
        `
        document.head.appendChild(selectionStyle)
      }
    })

    // Apply decorations
    decorationsRef.current = editorInstance.deltaDecorations(
      decorationsRef.current,
      newDecorations
    )

    // Cleanup function
    return () => {
      if (editorInstance) {
        decorationsRef.current = editorInstance.deltaDecorations(
          decorationsRef.current,
          []
        )
      }
    }
  }, [users, editorInstance])

  return null // This component doesn't render anything visible
}