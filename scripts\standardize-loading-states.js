#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Standardizing loading states across the application...\n');

// Find all loading.tsx files
const loadingFiles = [];

function findLoadingFiles(dir) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules')) {
      findLoadingFiles(fullPath);
    } else if (item === 'loading.tsx') {
      loadingFiles.push(fullPath);
    }
  }
}

const srcDir = path.join(process.cwd(), 'src');
findLoadingFiles(srcDir);

console.log(`Found ${loadingFiles.length} loading.tsx files\n`);

// Check which files need updates
let needsUpdate = 0;
let alreadyGood = 0;
let errors = 0;

const updateSummary = [];

for (const file of loadingFiles) {
  try {
    const content = fs.readFileSync(file, 'utf8');
    const relativePath = path.relative(process.cwd(), file);
    
    // Check if already using unified loading
    if (content.includes('@/components/ui/unified-loading')) {
      console.log(`✅ Already using unified loading: ${relativePath}`);
      alreadyGood++;
      updateSummary.push({
        file: relativePath,
        status: 'already-unified',
        imports: ['unified-loading']
      });
    } else if (content.includes('Skeleton') && !content.includes('SkeletonCard')) {
      console.log(`⚠️  Needs update: ${relativePath}`);
      needsUpdate++;
      updateSummary.push({
        file: relativePath,
        status: 'needs-update',
        imports: ['Skeleton'],
        recommendation: 'Convert to unified loading components'
      });
    } else {
      console.log(`ℹ️  Custom loading: ${relativePath}`);
      updateSummary.push({
        file: relativePath,
        status: 'custom',
        imports: []
      });
    }
  } catch (error) {
    console.error(`❌ Error reading ${file}:`, error.message);
    errors++;
  }
}

// Create a consolidated loading patterns guide
const loadingGuide = `# Loading States Standardization Guide

## Overview
All loading states should use the unified loading components from \`@/components/ui/unified-loading\`.

## Available Components

### Basic Loading States
\`\`\`tsx
import { Loading, PageLoading, SectionLoading, InlineLoading } from '@/components/ui/unified-loading'

// Basic loading spinner
<Loading size="md" variant="spinner" text="Loading..." />

// Full page loading
<PageLoading text="Loading dashboard..." />

// Section loading
<SectionLoading className="py-8" />

// Inline loading (for buttons, etc)
<InlineLoading />
\`\`\`

### Skeleton Presets
\`\`\`tsx
import { 
  SkeletonCard, 
  SkeletonList, 
  SkeletonTable, 
  SkeletonForm 
} from '@/components/ui/unified-loading'

// Card skeleton
<SkeletonCard className="w-full" />

// List skeleton
<SkeletonList items={5} />

// Table skeleton
<SkeletonTable rows={10} cols={4} />

// Form skeleton
<SkeletonForm fields={4} />
\`\`\`

### Context-Specific Loaders
\`\`\`tsx
import { 
  WritingLoading, 
  AILoading, 
  ProjectLoading, 
  CharacterLoading 
} from '@/components/ui/unified-loading'

// Writing-specific loading
<WritingLoading text="Saving your chapter..." />

// AI generation loading
<AILoading text="AI is generating content..." />

// Project loading
<ProjectLoading text="Loading project data..." />

// Character loading
<CharacterLoading text="Loading character profiles..." />
\`\`\`

### Loading Button
\`\`\`tsx
import { LoadingButton } from '@/components/ui/unified-loading'

<LoadingButton 
  isLoading={isSubmitting}
  loadingText="Saving..."
  variant="default"
  size="default"
>
  Save Changes
</LoadingButton>
\`\`\`

## Migration Examples

### Before (using bare Skeleton):
\`\`\`tsx
import { Skeleton } from '@/components/ui/skeleton'

export default function Loading() {
  return (
    <div>
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-4 w-64" />
    </div>
  )
}
\`\`\`

### After (using unified components):
\`\`\`tsx
import { PageLoading } from '@/components/ui/unified-loading'

export default function Loading() {
  return <PageLoading text="Loading..." />
}
\`\`\`

## Best Practices

1. **Use semantic loading components**: Choose the right preset for your use case
2. **Provide loading text**: Always include descriptive text for better UX
3. **Match loading to content**: Use context-specific loaders (WritingLoading, AILoading, etc)
4. **Consistent sizing**: Use the size prop consistently across your app
5. **Accessibility**: Loading states are announced to screen readers

## Loading State Patterns

### Dashboard Loading
\`\`\`tsx
export default function DashboardLoading() {
  return (
    <div className="container py-6 space-y-6">
      <SkeletonCard />
      <SkeletonTable rows={5} />
    </div>
  )
}
\`\`\`

### Form Page Loading
\`\`\`tsx
export default function SettingsLoading() {
  return (
    <div className="container py-6">
      <SkeletonForm fields={6} />
    </div>
  )
}
\`\`\`

### List Page Loading
\`\`\`tsx
export default function ProjectsLoading() {
  return (
    <div className="container py-6">
      <SkeletonList items={10} />
    </div>
  )
}
\`\`\`
`;

fs.writeFileSync(
  path.join(process.cwd(), 'docs/frontend/loading-states-guide.md'),
  loadingGuide
);

// Create migration script
const migrationScript = `// Example migration script for a loading.tsx file
import { PageLoading, SkeletonCard, SkeletonTable } from '@/components/ui/unified-loading'

export default function Loading() {
  // Option 1: Simple page loading
  return <PageLoading text="Loading content..." />
  
  // Option 2: Custom skeleton layout
  return (
    <div className="container py-6 space-y-6">
      <SkeletonCard />
      <SkeletonTable rows={5} />
    </div>
  )
}
`;

// Save summary report
const summaryReport = {
  timestamp: new Date().toISOString(),
  totalFiles: loadingFiles.length,
  alreadyUnified: alreadyGood,
  needsUpdate: needsUpdate,
  errors: errors,
  files: updateSummary
};

fs.writeFileSync(
  path.join(process.cwd(), 'scripts/loading-states-report.json'),
  JSON.stringify(summaryReport, null, 2)
);

// Summary
console.log('\n📊 Summary:');
console.log(`   Total loading files: ${loadingFiles.length}`);
console.log(`   Already using unified: ${alreadyGood}`);
console.log(`   Needs update: ${needsUpdate}`);
console.log(`   Errors: ${errors}`);

console.log('\n📝 Next Steps:');
console.log('1. Review files that need updates');
console.log('2. Use the migration guide at docs/frontend/loading-states-guide.md');
console.log('3. Update loading files to use unified components');
console.log('4. Test loading states across the application');

console.log('\n✅ Loading states analysis complete!');