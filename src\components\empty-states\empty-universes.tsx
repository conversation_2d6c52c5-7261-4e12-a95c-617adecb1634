'use client'

import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { 
  Globe, 
  Plus, 
  Network,
  Map,
  Users,
  ArrowRight
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export function EmptyUniverses() {
  const router = useRouter()

  return (
    <div className="max-w-4xl mx-auto py-12">
      <Card className="border-2 border-dashed border-muted-foreground/20">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/10 flex items-center justify-center">
            <Globe className="w-8 h-8 text-primary" />
          </div>
          
          <h2 className="text-2xl font-literary-display text-foreground mb-4">
            No Universes Created Yet
          </h2>
          
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto">
            Create shared universes to connect multiple series and standalone novels. Build expansive worlds with interconnected stories.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Network className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Connected Worlds</h3>
              <p className="text-sm text-muted-foreground">
                Link multiple series in the same fictional universe
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Map className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">World Building</h3>
              <p className="text-sm text-muted-foreground">
                Create detailed settings shared across stories
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                <Users className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-medium mb-2">Character Crossovers</h3>
              <p className="text-sm text-muted-foreground">
                Share characters between different series
              </p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 lg:gap-6 justify-center">
            <Button 
              size="lg" 
              onClick={() => router.push('/universes/new')}
              className="group"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Universe
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => router.push('/series')}
            >
              <Network className="w-4 h-4 mr-2" />
              View Series
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}