'use client'

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  MapPin, 
  Plus, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw,
  Globe,
  Mountain,
  Trees,
  Building,
  Home,
  Move,
  Edit,
  Trash2,
  Save,
  Layers,
  Settings2,
  Eye,
  EyeOff
} from 'lucide-react'
import type { Location } from './location-manager'
import { useMapPreferences } from '@/hooks/use-map-preferences'
import { useToast } from '@/hooks/use-toast'

// Performance optimization: Move constants outside component
const LOCATION_TYPE_ICONS = {
  world: Globe,
  continent: Mountain,
  country: Trees,
  region: Mountain,
  city: Building,
  building: Home,
  room: Home,
  other: MapPin
} as const

const LOCATION_TYPE_COLORS = {
  world: 'text-blue-600 bg-blue-50 border-blue-200',
  continent: 'text-green-600 bg-green-50 border-green-200',
  country: 'text-purple-600 bg-purple-50 border-purple-200',
  region: 'text-orange-600 bg-orange-50 border-orange-200',
  city: 'text-red-600 bg-red-50 border-red-200',
  building: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  room: 'text-gray-600 bg-gray-50 border-gray-200',
  other: 'text-indigo-600 bg-indigo-50 border-indigo-200'
} as const

// Viewport culling constants
const VIEWPORT_PADDING = 100 // Extra padding around viewport for smooth scrolling
const LOD_THRESHOLDS = {
  FULL: 1.0,    // Show all details
  MEDIUM: 0.5,  // Hide action buttons
  LOW: 0.3      // Show only essential info
} as const

interface LocationMapViewProps {
  locations: Location[]
  projectId: string
  onLocationSelect: (location: Location) => void
  onLocationEdit: (location: Location) => void
  onLocationDelete: (locationId: string) => void
  onAddChild: (parentLocation: Location) => void
  onUpdatePosition?: (locationId: string, position: { x: number; y: number }) => void
  selectedLocationId?: string
  readOnly?: boolean
  loading?: boolean
}

interface LocationPosition {
  id: string
  x: number
  y: number
  location: Location
}

interface ViewportBounds {
  minX: number
  maxX: number
  minY: number
  maxY: number
}

// Memoized location node component for better performance
const LocationNode = React.memo(({ 
  position, 
  isSelected, 
  isDragging,
  scale,
  readOnly,
  onMouseDown,
  onSelect,
  onAddChild,
  onEdit,
  onDelete
}: {
  position: LocationPosition
  isSelected: boolean
  isDragging: boolean
  scale: number
  readOnly: boolean
  onMouseDown: (e: React.MouseEvent, id: string) => void
  onSelect: (location: Location) => void
  onAddChild: (location: Location) => void
  onEdit: (location: Location) => void
  onDelete: (id: string) => void
}) => {
  const IconComponent = LOCATION_TYPE_ICONS[position.location.location_type]
  const showFullDetails = scale >= LOD_THRESHOLDS.FULL
  const showMediumDetails = scale >= LOD_THRESHOLDS.MEDIUM
  
  return (
    <g
      transform={`translate(${position.x}, ${position.y})`}
      className={`cursor-pointer ${isDragging ? 'opacity-75' : ''}`}
      onMouseDown={(e) => onMouseDown(e, position.id)}
      onClick={() => onSelect(position.location)}
    >
      {/* Selection indicator */}
      {isSelected && (
        <circle
          cx="0"
          cy="0"
          r="45"
          fill="none"
          stroke="hsl(var(--primary))"
          strokeWidth="3"
          className="animate-pulse"
        />
      )}
      
      {/* Location node */}
      <circle
        cx="0"
        cy="0"
        r="30"
        className={`${LOCATION_TYPE_COLORS[position.location.location_type]} border-2 transition-all hover:scale-110`}
        stroke={isSelected ? "hsl(var(--primary))" : "currentColor"}
        strokeWidth={isSelected ? "2" : "1"}
      />
      
      {/* Icon */}
      <foreignObject x="-10" y="-10" width="20" height="20">
        <IconComponent className="w-5 h-5" />
      </foreignObject>
      
      {/* Label - always show */}
      <text
        x="0"
        y="50"
        textAnchor="middle"
        className="text-sm font-medium fill-current"
        style={{ userSelect: 'none' }}
      >
        {position.location.name}
      </text>
      
      {/* Type badge - show at medium+ detail level */}
      {showMediumDetails && (
        <foreignObject x="-30" y="-45" width="60" height="20">
          <div className="flex justify-center">
            <Badge variant="outline" className="text-xs capitalize">
              {position.location.location_type}
            </Badge>
          </div>
        </foreignObject>
      )}
      
      {/* Action buttons - show at full detail level */}
      {!readOnly && showFullDetails && (
        <g className="opacity-0 group-hover:opacity-100 transition-opacity">
          <foreignObject x="35" y="-15" width="80" height="30">
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 rounded-full bg-background shadow-sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onAddChild(position.location)
                }}
              >
                <Plus className="w-3 h-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 rounded-full bg-background shadow-sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(position.location)
                }}
              >
                <Edit className="w-3 h-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 rounded-full bg-background shadow-sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete(position.id)
                }}
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
          </foreignObject>
        </g>
      )}
    </g>
  )
})

LocationNode.displayName = 'LocationNode'

export function LocationMapView({
  locations,
  projectId,
  onLocationSelect,
  onLocationEdit,
  onLocationDelete,
  onAddChild,
  onUpdatePosition,
  selectedLocationId,
  readOnly = false,
  loading = false
}: LocationMapViewProps) {
  const { toast } = useToast()
  const svgRef = useRef<SVGSVGElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const viewportSaveTimeoutRef = useRef<NodeJS.Timeout>()
  const animationFrameRef = useRef<number>()
  
  // Use map preferences hook
  const {
    preferences,
    loadingPreferences,
    savingPreferences,
    updateViewport,
    positions,
    loadingPositions,
    savingPositions,
    savePosition,
    savePositions,
    getPositionForLocation
  } = useMapPreferences({
    projectId,
    autoLoad: true,
    autoSave: true,
    saveDebounceMs: 500
  })

  // Local state
  const [scale, setScale] = useState(1)
  const [pan, setPan] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [isDraggingLocation, setIsDraggingLocation] = useState<string | null>(null)
  const [showAddLocationDialog, setShowAddLocationDialog] = useState(false)
  const [newLocationPosition, setNewLocationPosition] = useState<{ x: number; y: number } | null>(null)
  const [isPerformingAction, setIsPerformingAction] = useState(false)
  const [actionMessage, setActionMessage] = useState('')
  const [localPositions, setLocalPositions] = useState<LocationPosition[]>([])
  const [viewportBounds, setViewportBounds] = useState<ViewportBounds>({
    minX: -VIEWPORT_PADDING,
    maxX: 1000 + VIEWPORT_PADDING,
    minY: -VIEWPORT_PADDING,
    maxY: 800 + VIEWPORT_PADDING
  })
  const [showLabels, setShowLabels] = useState(true)
  const [performanceMode, setPerformanceMode] = useState(false)
  
  // Loading states
  const isInitializing = loadingPreferences || loadingPositions
  const isSavingPosition = savingPositions || savingPreferences

  // Performance: Memoize viewport bounds calculation
  const updateViewportBounds = useCallback(() => {
    if (!containerRef.current) return
    
    const rect = containerRef.current.getBoundingClientRect()
    const viewWidth = rect.width / scale
    const viewHeight = rect.height / scale
    
    setViewportBounds({
      minX: -pan.x / scale - VIEWPORT_PADDING,
      maxX: (-pan.x + rect.width) / scale + VIEWPORT_PADDING,
      minY: -pan.y / scale - VIEWPORT_PADDING,
      maxY: (-pan.y + rect.height) / scale + VIEWPORT_PADDING
    })
  }, [scale, pan])

  // Update viewport bounds when scale or pan changes
  useEffect(() => {
    updateViewportBounds()
  }, [updateViewportBounds])

  // Initialize viewport from preferences
  useEffect(() => {
    if (preferences?.viewport) {
      const { center, zoom } = preferences.viewport
      if (zoom) setScale(zoom / 10) // Convert from map zoom to scale
      if (center) {
        setPan({ 
          x: center.lng * 10, // Convert from lng to x
          y: center.lat * 10  // Convert from lat to y
        })
      }
    }
  }, [preferences])

  // Performance: Memoize merged positions
  const mergedPositions = useMemo(() => {
    return locations.map((location, index) => {
      const savedPosition = getPositionForLocation(location.id)
      if (savedPosition) {
        return {
          id: location.id,
          x: savedPosition.x,
          y: savedPosition.y,
          location
        }
      }
      // Default grid position for new locations
      return {
        id: location.id,
        x: (index % 5) * 200 + 100,
        y: Math.floor(index / 5) * 150 + 100,
        location
      }
    })
  }, [locations, getPositionForLocation])

  // Update local positions when merged positions change
  useEffect(() => {
    setLocalPositions(mergedPositions)
  }, [mergedPositions])

  // Performance: Memoize visible positions based on viewport
  const visiblePositions = useMemo(() => {
    if (performanceMode) {
      return localPositions.filter(pos => 
        pos.x >= viewportBounds.minX &&
        pos.x <= viewportBounds.maxX &&
        pos.y >= viewportBounds.minY &&
        pos.y <= viewportBounds.maxY
      )
    }
    return localPositions
  }, [localPositions, viewportBounds, performanceMode])

  // Performance: Memoize connections
  const visibleConnections = useMemo(() => {
    const connections: Array<{ from: LocationPosition; to: LocationPosition; key: string }> = []
    
    visiblePositions.forEach(position => {
      if (position.location.parent_location_id) {
        const parent = localPositions.find(p => p.id === position.location.parent_location_id)
        if (parent) {
          // Check if at least one endpoint is visible
          const parentVisible = !performanceMode || (
            parent.x >= viewportBounds.minX &&
            parent.x <= viewportBounds.maxX &&
            parent.y >= viewportBounds.minY &&
            parent.y <= viewportBounds.maxY
          )
          
          if (parentVisible || !performanceMode) {
            connections.push({
              from: parent,
              to: position,
              key: `${parent.id}-${position.id}`
            })
          }
        }
      }
    })
    
    return connections
  }, [visiblePositions, localPositions, viewportBounds, performanceMode])

  // Enable performance mode for large datasets
  useEffect(() => {
    setPerformanceMode(locations.length > 50)
  }, [locations.length])

  // Save viewport changes with debouncing
  useEffect(() => {
    if (!preferences) return
    
    if (viewportSaveTimeoutRef.current) {
      clearTimeout(viewportSaveTimeoutRef.current)
    }
    
    viewportSaveTimeoutRef.current = setTimeout(() => {
      updateViewport({
        center: { 
          lat: pan.y / 10,  // Convert from y to lat
          lng: pan.x / 10   // Convert from x to lng
        },
        zoom: scale * 10,   // Convert from scale to zoom
        bounds: {
          north: (pan.y + 400) / 10,
          south: (pan.y - 400) / 10,
          east: (pan.x + 500) / 10,
          west: (pan.x - 500) / 10
        }
      })
    }, 1000) // Debounce viewport saves
    
    return () => {
      if (viewportSaveTimeoutRef.current) {
        clearTimeout(viewportSaveTimeoutRef.current)
      }
    }
  }, [scale, pan, preferences, updateViewport])

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev * 1.2, 3))
  }

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev / 1.2, 0.3))
  }

  const handleReset = () => {
    setScale(1)
    setPan({ x: 0, y: 0 })
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0 && !isDraggingLocation) {
      setIsDragging(true)
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y })
    }
  }

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      // Use requestAnimationFrame for smoother panning
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      
      animationFrameRef.current = requestAnimationFrame(() => {
        setPan({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y
        })
      })
    }
  }, [isDragging, dragStart])

  const handleMouseUp = () => {
    setIsDragging(false)
    setIsDraggingLocation(null)
  }

  const handleLocationMouseDown = (e: React.MouseEvent, locationId: string) => {
    e.stopPropagation()
    if (!readOnly) {
      setIsDraggingLocation(locationId)
    }
  }

  const handleLocationDrag = useCallback((e: React.MouseEvent) => {
    if (isDraggingLocation && !readOnly) {
      const rect = svgRef.current?.getBoundingClientRect()
      if (rect) {
        const x = (e.clientX - rect.left - pan.x) / scale
        const y = (e.clientY - rect.top - pan.y) / scale
        
        setLocalPositions(prev => 
          prev.map(pos => 
            pos.id === isDraggingLocation 
              ? { ...pos, x, y }
              : pos
          )
        )
      }
    }
  }, [isDraggingLocation, readOnly, pan, scale])

  const handleLocationDragEnd = useCallback(async () => {
    if (isDraggingLocation) {
      const position = localPositions.find(pos => pos.id === isDraggingLocation)
      if (position) {
        try {
          // Save to database
          await savePosition({
            locationId: position.id,
            x: position.x,
            y: position.y
          })
          
          // Also call the optional callback
          if (onUpdatePosition) {
            await onUpdatePosition(isDraggingLocation, { x: position.x, y: position.y })
          }
          
          toast({
            title: "Position saved",
            description: "Location position has been updated"
          })
        } catch (error) {
          toast({
            title: "Error saving position",
            description: "Failed to save location position. Please try again.",
            variant: "destructive"
          })
          
          // Revert position on error
          const savedPosition = getPositionForLocation(isDraggingLocation)
          if (savedPosition) {
            setLocalPositions(prev => 
              prev.map(pos => 
                pos.id === isDraggingLocation 
                  ? { ...pos, x: savedPosition.x, y: savedPosition.y }
                  : pos
              )
            )
          }
        }
      }
    }
    setIsDraggingLocation(null)
  }, [isDraggingLocation, localPositions, savePosition, onUpdatePosition, toast, getPositionForLocation])

  const handleDoubleClick = (e: React.MouseEvent) => {
    if (readOnly) return
    
    const rect = svgRef.current?.getBoundingClientRect()
    if (rect) {
      const x = (e.clientX - rect.left - pan.x) / scale
      const y = (e.clientY - rect.top - pan.y) / scale
      setNewLocationPosition({ x, y })
      setShowAddLocationDialog(true)
    }
  }

  // Memoized callbacks for location actions
  const handleAddChild = useCallback((location: Location) => {
    setIsPerformingAction(true)
    setActionMessage('Adding child location...')
    onAddChild(location)
    setTimeout(() => setIsPerformingAction(false), 500)
  }, [onAddChild])

  const handleEdit = useCallback((location: Location) => {
    setIsPerformingAction(true)
    setActionMessage('Opening location editor...')
    onLocationEdit(location)
    setTimeout(() => setIsPerformingAction(false), 500)
  }, [onLocationEdit])

  const handleDelete = useCallback((locationId: string) => {
    setIsPerformingAction(true)
    setActionMessage('Deleting location...')
    onLocationDelete(locationId)
    setTimeout(() => setIsPerformingAction(false), 500)
  }, [onLocationDelete])

  if (locations.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center py-12">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Locations to Map</h3>
            <p className="text-muted-foreground">
              Create some locations to see them visualized on the map
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Loading state while initializing positions or fetching data
  if (loading || isInitializing) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-24">
            <div className="text-center">
              <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4 animate-pulse" />
              <p className="text-muted-foreground">
                {loading ? 'Loading locations...' : 'Initializing map...'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Location Map
            {performanceMode && (
              <Badge variant="secondary" className="ml-2">
                Performance Mode
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => setShowLabels(!showLabels)}
              title={showLabels ? "Hide labels" : "Show labels"}
            >
              {showLabels ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </Button>
            <Button size="sm" variant="outline" onClick={handleZoomIn}>
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleZoomOut}>
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleReset}>
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          {readOnly 
            ? 'Visual map of your story locations'
            : 'Drag locations to arrange them. Double-click to add new locations.'
          }
          {performanceMode && ' • Viewport culling enabled for performance.'}
        </p>
      </CardHeader>
      <CardContent>
        <div 
          ref={containerRef}
          className="relative w-full h-[600px] border rounded-lg overflow-hidden bg-gray-50/50"
          onMouseDown={handleMouseDown}
          onMouseMove={isDragging ? handleMouseMove : handleLocationDrag}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onDoubleClick={handleDoubleClick}
          style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        >
          {/* Action overlay */}
          {isPerformingAction && (
            <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
              <div className="bg-background border rounded-lg p-4 shadow-lg flex items-center gap-3">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                <span className="text-sm font-medium">{actionMessage || 'Processing...'}</span>
              </div>
            </div>
          )}
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            viewBox="0 0 1000 800"
            className="select-none"
          >
            <defs>
              <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#e2e8f0" strokeWidth="1" opacity="0.3"/>
              </pattern>
            </defs>
            
            <g transform={`translate(${pan.x}, ${pan.y}) scale(${scale})`}>
              {/* Grid background */}
              <rect width="1000" height="800" fill="url(#grid)" />
              
              {/* Connection lines */}
              {visibleConnections.map(({ from, to, key }) => (
                <line
                  key={key}
                  x1={from.x}
                  y1={from.y}
                  x2={to.x}
                  y2={to.y}
                  stroke="#cbd5e1"
                  strokeWidth="2"
                  strokeDasharray="5,5"
                  opacity="0.6"
                />
              ))}
              
              {/* Location nodes */}
              <g className="group">
                {visiblePositions.map(position => (
                  <LocationNode
                    key={position.id}
                    position={position}
                    isSelected={selectedLocationId === position.id}
                    isDragging={isDraggingLocation === position.id}
                    scale={scale}
                    readOnly={readOnly}
                    onMouseDown={handleLocationMouseDown}
                    onSelect={onLocationSelect}
                    onAddChild={handleAddChild}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                  />
                ))}
              </g>
            </g>
          </svg>
          
          {/* Scale indicator */}
          <div className="absolute bottom-4 left-4 bg-background/90 rounded px-2 py-1 text-xs font-mono">
            {Math.round(scale * 100)}%
          </div>
          
          {/* Performance stats */}
          {performanceMode && (
            <div className="absolute bottom-4 left-24 bg-background/90 rounded px-2 py-1 text-xs font-mono">
              {visiblePositions.length}/{localPositions.length} visible
            </div>
          )}
          
          {/* Position save indicator */}
          {isSavingPosition && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-background/90 rounded px-3 py-1 text-xs flex items-center gap-2">
              <div className="w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              Saving position...
            </div>
          )}
          
          {/* Instructions */}
          {!readOnly && (
            <div className="absolute top-4 right-4 bg-background/90 rounded p-2 text-xs text-muted-foreground max-w-48">
              {loadingPreferences ? (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 border-2 border-muted-foreground border-t-transparent rounded-full animate-spin" />
                  <span>Loading view...</span>
                </div>
              ) : (
                <>
                  <p><strong>Drag:</strong> Move map</p>
                  <p><strong>Drag location:</strong> Reposition</p>
                  <p><strong>Double-click:</strong> Add location</p>
                </>
              )}
            </div>
          )}
        </div>
      </CardContent>
      
      {/* Add Location Dialog */}
      <Dialog open={showAddLocationDialog} onOpenChange={setShowAddLocationDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Location at Position</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              This would open the create location dialog with the position pre-set.
              For now, use the "Add Location" button in the main interface.
            </p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowAddLocationDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowAddLocationDialog(false)}>
                OK
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  )
}