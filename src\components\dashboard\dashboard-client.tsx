'use client'

import { useState, useEffect } from 'react'
import { WelcomeTour } from '@/components/onboarding/welcome-tour'
import { Skeleton } from '@/components/ui/skeleton'

interface DashboardClientProps {
  hasProjects: boolean
  isNewUser: boolean
}

export function DashboardClient({ hasProjects, isNewUser }: DashboardClientProps) {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate initial load
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 500)
    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <WelcomeTour
      isNewUser={isNewUser}
      hasProjects={hasProjects}
      onComplete={() => {
        // Tour completion is handled internally by WelcomeTour component
      }}
    />
  )
}