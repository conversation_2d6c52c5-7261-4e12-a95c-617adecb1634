
# Consolidated Migration Plan

## 1. Core Schema (Combine into 001_core_schema.sql)
- Basic tables: users, projects, chapters, characters, series
- Essential relationships and constraints
- Core indexes

## 2. Feature Tables (Combine into 002_features.sql)
- Voice profiles
- Universe/world building
- Story bible
- Timeline events
- Locations
- Reference materials

## 3. Collaboration System (Combine into 003_collaboration.sql)
- Project collaborators
- Invitations
- Real-time collaboration tables
- Permissions

## 4. Analytics & Tracking (Combine into 004_analytics.sql)
- Writing sessions
- Word count history
- Usage tracking
- AI usage logs
- Quality metrics

## 5. Achievement System (Combine into 005_achievements.sql)
- Achievement definitions
- User achievements
- Progress tracking
- Notification triggers

## 6. Performance Optimizations (Combine into 006_performance.sql)
- All indexes
- Materialized views
- Partitioning (if needed)
- Query optimizations

## Benefits of Consolidation:
1. Faster initial setup
2. Clearer dependency order
3. Easier to understand schema
4. Reduced migration conflicts
5. Simpler rollback process
