/**
 * Animation timing constants (in seconds)
 */
export const ANIMATION_DURATION = {
  INSTANT: 0,
  FAST: 0.2,
  NORMAL: 0.3,
  SLOW: 0.5,
  VERY_SLOW: 1.2,
} as const

/**
 * Animation delay constants (in seconds)
 */
export const ANIMATION_DELAY = {
  NONE: 0,
  STAGGER_SMALL: 0.05,
  STAGGER_MEDIUM: 0.1,
  STAGGER_LARGE: 0.15,
  SMALL: 0.2,
  MEDIUM: 0.3,
  LARGE: 0.4,
} as const

/**
 * Animation scale values
 */
export const ANIMATION_SCALE = {
  HIDDEN: 0,
  SHRINK_SMALL: 0.8,
  SHRINK_MEDIUM: 0.5,
  NORMAL: 1,
  GROW_SMALL: 1.1,
  GROW_MEDIUM: 1.2,
  GROW_LARGE: 1.5,
} as const

/**
 * Animation opacity values
 */
export const ANIMATION_OPACITY = {
  HIDDEN: 0,
  VERY_FAINT: 0.2,
  FAINT: 0.3,
  HALF: 0.5,
  SUBTLE: 0.6,
  MUTED: 0.7,
  VISIBLE: 1,
} as const

/**
 * Animation easing curves
 */
export const ANIMATION_EASING = {
  LINEAR: 'linear',
  EASE_IN: 'ease-in',
  EASE_OUT: 'ease-out',
  EASE_IN_OUT: 'ease-in-out',
  SPRING: { type: 'spring', stiffness: 300, damping: 30 },
} as const