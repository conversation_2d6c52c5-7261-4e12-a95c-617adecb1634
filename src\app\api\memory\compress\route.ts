import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { UnifiedResponse } from '@/lib/utils/response'
import { getMemoryManager } from '@/lib/memory/memory-instances'
import { logger } from '@/lib/services/logger'

const compressionSchema = z.object({
  projectId: z.string().uuid(),
  strategy: z.enum(['aggressive', 'balanced', 'conservative']).optional().default('balanced'),
  options: z.object({
    preserveRecent: z.boolean().optional().default(true),
    mergeSimular: z.boolean().optional().default(true),
    archiveOld: z.boolean().optional().default(true)
  }).optional()
})

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const validation = compressionSchema.safeParse(body)
    
    if (!validation.success) {
      return UnifiedResponse.error('Invalid request data', 400, validation.error.errors)
    }

    const { projectId, strategy, options } = validation.data
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return UnifiedResponse.error('Project not found or access denied', 404)
    }

    // Get memory manager and perform compression
    const memoryManager = getMemoryManager(projectId)
    
    // Get stats before compression
    const statsBefore = memoryManager.getMemoryStats()
    
    // Perform compression based on strategy
    const compressionResult = await memoryManager.compressMemory({
      strategy,
      ...options
    })
    
    // Get stats after compression
    const statsAfter = memoryManager.getMemoryStats()
    
    // Calculate savings
    const savings = {
      tokensSaved: statsBefore.totalTokens - statsAfter.totalTokens,
      percentageSaved: ((statsBefore.totalTokens - statsAfter.totalTokens) / statsBefore.totalTokens) * 100,
      contextsRemoved: compressionResult.contextsRemoved || 0,
      contextsMerged: compressionResult.contextsMerged || 0
    }
    
    // Log compression event
    logger.info('Memory compression completed', {
      projectId,
      strategy,
      savings
    })
    
    return UnifiedResponse.success({
      message: 'Memory compression completed successfully',
      stats: statsAfter,
      savings,
      compressionDetails: compressionResult
    })
  } catch (error) {
    logger.error('Memory compression error:', error)
    return UnifiedResponse.error('Failed to compress memory')
  }
})