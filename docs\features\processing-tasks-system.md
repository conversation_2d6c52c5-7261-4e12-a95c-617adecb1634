# BookScribe Processing Tasks System

## Overview

The Processing Tasks System provides a robust infrastructure for managing long-running background operations such as AI generation, content export, bulk updates, and complex analysis. It ensures reliable task execution with progress tracking, error handling, and user visibility into ongoing operations.

## Architecture

### Database Schema

#### Processing Tasks Table
Core task queue and status tracking:

```sql
processing_tasks:
  - id: UUID (Primary Key)
  - user_id: UUID - References auth.users
  - project_id: UUID - References projects (optional)
  - task_type: TEXT - ai_generation, export, import, analysis, bulk_update, indexing
  - status: TEXT - pending, processing, completed, failed, cancelled
  - priority: INTEGER - Task priority (0-10, higher = more urgent)
  - input_data: JSONB - Task parameters and configuration
  - output_data: JSONB - Task results
  - error_message: TEXT - Error details if failed
  - metadata: JSONB - Additional task metadata
  - progress: INTEGER - Progress percentage (0-100)
  - started_at: TIMESTAMPTZ - Processing start time
  - completed_at: TIMESTAMPTZ - Completion time
  - estimated_duration: INTEGER - Estimated seconds to complete
  - retry_count: INTEGER - Number of retry attempts
  - max_retries: INTEGER - Maximum retry attempts allowed
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Task Progress Table
Granular progress tracking:

```sql
task_progress:
  - id: UUID (Primary Key)
  - task_id: UUID - References processing_tasks
  - progress: INTEGER - Current progress (0-100)
  - message: TEXT - Progress message
  - details: JSONB - Detailed progress data
  - checkpoint_data: JSONB - State for resumption
  - created_at: TIMESTAMPTZ
```

#### Task Dependencies Table
Task chaining and dependencies:

```sql
task_dependencies:
  - id: UUID (Primary Key)
  - task_id: UUID - References processing_tasks
  - depends_on_task_id: UUID - References processing_tasks
  - dependency_type: VARCHAR(20) - completion, success, data
  - created_at: TIMESTAMPTZ
  - UNIQUE(task_id, depends_on_task_id)
```

## Task Types

### 1. AI Generation Tasks
Long-running AI content generation:

```typescript
interface AIGenerationTask {
  type: 'ai_generation';
  subtype: 'full_book' | 'chapter_batch' | 'character_profiles' | 'story_analysis';
  input_data: {
    project_id: string;
    agent_pipeline: string[];
    generation_config: {
      target_words?: number;
      chapters?: number[];
      quality_threshold?: number;
    };
  };
  estimated_duration: number; // seconds
}
```

### 2. Export Tasks
Content export operations:

```typescript
interface ExportTask {
  type: 'export';
  subtype: 'docx' | 'pdf' | 'epub' | 'markdown' | 'full_project';
  input_data: {
    project_id: string;
    export_config: {
      include_metadata?: boolean;
      include_comments?: boolean;
      format_options: FormatSpecificOptions;
    };
  };
}
```

### 3. Import Tasks
Content import and parsing:

```typescript
interface ImportTask {
  type: 'import';
  subtype: 'docx' | 'pdf' | 'epub' | 'plain_text';
  input_data: {
    file_url: string;
    project_id?: string;
    import_options: {
      detect_chapters?: boolean;
      preserve_formatting?: boolean;
      extract_metadata?: boolean;
    };
  };
}
```

### 4. Analysis Tasks
Deep content analysis:

```typescript
interface AnalysisTask {
  type: 'analysis';
  subtype: 'consistency' | 'character_arcs' | 'pacing' | 'themes' | 'quality';
  input_data: {
    project_id: string;
    scope: {
      chapters?: number[];
      characters?: string[];
      date_range?: DateRange;
    };
    analysis_depth: 'quick' | 'standard' | 'deep';
  };
}
```

### 5. Bulk Update Tasks
Mass content modifications:

```typescript
interface BulkUpdateTask {
  type: 'bulk_update';
  subtype: 'find_replace' | 'style_update' | 'metadata_update';
  input_data: {
    project_id: string;
    updates: UpdateOperation[];
    validation: boolean;
    backup: boolean;
  };
}
```

### 6. Indexing Tasks
Search index updates:

```typescript
interface IndexingTask {
  type: 'indexing';
  subtype: 'embeddings' | 'search' | 'full_reindex';
  input_data: {
    project_id: string;
    content_types: ContentType[];
    force: boolean;
  };
}
```

## API Endpoints

### Task Management

#### POST /api/processing-tasks
Create new processing task:

```typescript
// Request
{
  task_type: "ai_generation",
  priority: 7,
  input_data: {
    project_id: "uuid",
    agent_pipeline: ["story-architect", "character-developer"],
    generation_config: {
      target_words: 80000
    }
  }
}

// Response
{
  task: {
    id: "uuid",
    task_type: "ai_generation",
    status: "pending",
    priority: 7,
    estimated_duration: 300,
    created_at: "2024-01-15T10:30:00Z"
  }
}
```

#### GET /api/processing-tasks
List user's tasks:

```typescript
// Request
GET /api/processing-tasks?status=processing&project_id=uuid

// Response
{
  tasks: [
    {
      id: "uuid",
      task_type: "export",
      status: "processing",
      progress: 45,
      message: "Generating chapter 12 of 25",
      started_at: "2024-01-15T10:30:00Z",
      estimated_completion: "2024-01-15T10:35:00Z"
    }
  ],
  stats: {
    pending: 3,
    processing: 1,
    completed_today: 15
  }
}
```

#### GET /api/processing-tasks/{id}
Get task details:

```typescript
// Response
{
  task: {
    id: "uuid",
    task_type: "analysis",
    status: "completed",
    progress: 100,
    input_data: {...},
    output_data: {
      consistency_score: 0.92,
      issues_found: 3,
      report_url: "https://..."
    },
    completed_at: "2024-01-15T10:45:00Z",
    duration_seconds: 180
  },
  progress_history: [
    {
      progress: 25,
      message: "Analyzing characters",
      timestamp: "2024-01-15T10:31:00Z"
    }
  ]
}
```

#### PUT /api/processing-tasks/{id}/cancel
Cancel a running task:

```typescript
// Response
{
  task_id: "uuid",
  status: "cancelled",
  cancelled_at: "2024-01-15T10:32:00Z"
}
```

### Task Progress

#### GET /api/processing-tasks/{id}/progress
Get real-time progress:

```typescript
// Response
{
  task_id: "uuid",
  current_progress: 67,
  current_message: "Processing chapter 15 of 22",
  sub_tasks: [
    {
      name: "Chapter generation",
      progress: 100,
      status: "completed"
    },
    {
      name: "Character consistency",
      progress: 45,
      status: "processing"
    }
  ],
  estimated_remaining: 120 // seconds
}
```

## Task Processing

### Task Queue Manager
```typescript
class TaskQueueManager {
  async processQueue() {
    // Get next task by priority and creation time
    const task = await this.getNextTask();
    
    if (!task) return;
    
    try {
      // Update status
      await this.updateTaskStatus(task.id, 'processing');
      
      // Process based on type
      const processor = this.getProcessor(task.task_type);
      const result = await processor.execute(task);
      
      // Store results
      await this.completeTask(task.id, result);
      
    } catch (error) {
      await this.handleTaskError(task, error);
    }
  }
  
  private async getNextTask(): Promise<ProcessingTask | null> {
    return await db.query(`
      SELECT * FROM processing_tasks
      WHERE status = 'pending'
      AND (depends_on_task_id IS NULL 
        OR depends_on_task_id IN (
          SELECT id FROM processing_tasks 
          WHERE status = 'completed'
        ))
      ORDER BY priority DESC, created_at ASC
      LIMIT 1
      FOR UPDATE SKIP LOCKED
    `);
  }
}
```

### Task Processors
```typescript
interface TaskProcessor {
  canProcess(taskType: string): boolean;
  execute(task: ProcessingTask): Promise<any>;
  estimateDuration(task: ProcessingTask): number;
  supportsResume(): boolean;
  resume(task: ProcessingTask, checkpoint: any): Promise<any>;
}

class AIGenerationProcessor implements TaskProcessor {
  async execute(task: ProcessingTask) {
    const { agent_pipeline, generation_config } = task.input_data;
    
    // Initialize progress
    await this.updateProgress(task.id, 0, 'Initializing AI pipeline');
    
    // Execute pipeline
    for (let i = 0; i < agent_pipeline.length; i++) {
      const agent = agent_pipeline[i];
      const progress = (i / agent_pipeline.length) * 100;
      
      await this.updateProgress(
        task.id, 
        progress, 
        `Running ${agent} agent`
      );
      
      // Run agent
      const result = await this.runAgent(agent, generation_config);
      
      // Store checkpoint
      await this.saveCheckpoint(task.id, {
        completed_agents: agent_pipeline.slice(0, i + 1),
        last_result: result
      });
    }
    
    return { success: true, generated_content: result };
  }
}
```

### Error Handling & Retry
```typescript
class TaskErrorHandler {
  async handleError(task: ProcessingTask, error: Error) {
    const isRetryable = this.isRetryableError(error);
    
    if (isRetryable && task.retry_count < task.max_retries) {
      // Schedule retry with exponential backoff
      const delay = Math.pow(2, task.retry_count) * 1000;
      
      await this.updateTask(task.id, {
        status: 'pending',
        retry_count: task.retry_count + 1,
        metadata: {
          ...task.metadata,
          next_retry_at: new Date(Date.now() + delay),
          last_error: error.message
        }
      });
    } else {
      // Mark as failed
      await this.updateTask(task.id, {
        status: 'failed',
        error_message: error.message,
        completed_at: new Date()
      });
      
      // Notify user
      await this.notifyTaskFailure(task, error);
    }
  }
  
  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'RATE_LIMIT_ERROR',
      'TEMPORARY_NETWORK_ERROR',
      'RESOURCE_TEMPORARILY_UNAVAILABLE'
    ];
    
    return retryableErrors.some(e => 
      error.message.includes(e)
    );
  }
}
```

## Progress Tracking

### Progress Updates
```typescript
interface ProgressUpdate {
  task_id: string;
  progress: number; // 0-100
  message?: string;
  sub_progress?: {
    [key: string]: {
      progress: number;
      message: string;
    };
  };
  estimated_remaining?: number; // seconds
}
```

### Real-time Updates
```typescript
// WebSocket progress streaming
class TaskProgressStream {
  streamProgress(taskId: string, userId: string) {
    const stream = new EventEmitter();
    
    // Subscribe to task progress
    db.subscribe(`task_progress:${taskId}`, (update) => {
      stream.emit('progress', {
        taskId,
        progress: update.progress,
        message: update.message,
        timestamp: new Date()
      });
    });
    
    return stream;
  }
}
```

## UI Components

### Task Queue Display
```tsx
<TaskQueue
  userId={userId}
  projectId={projectId}
  showCompleted={false}
  autoRefresh={true}
  refreshInterval={5000}
/>
```

### Task Progress Card
```tsx
<TaskProgressCard
  task={currentTask}
  showCancel={task.status === 'processing'}
  onCancel={handleCancel}
  expanded={true}
  showSubTasks={true}
/>
```

### Task History
```tsx
<TaskHistory
  userId={userId}
  dateRange="last_7_days"
  groupBy="task_type"
  showStats={true}
  onTaskClick={handleTaskClick}
/>
```

### Background Task Indicator
```tsx
<BackgroundTaskIndicator
  activeTasks={activeTasks}
  position="bottom-right"
  collapsible={true}
  showNotifications={true}
/>
```

## Performance Optimization

### Database Indexes
```sql
-- Status and priority for queue processing
CREATE INDEX idx_processing_tasks_queue 
ON processing_tasks(status, priority DESC, created_at ASC)
WHERE status IN ('pending', 'processing');

-- User tasks
CREATE INDEX idx_processing_tasks_user 
ON processing_tasks(user_id, created_at DESC);

-- Project tasks
CREATE INDEX idx_processing_tasks_project 
ON processing_tasks(project_id, created_at DESC)
WHERE project_id IS NOT NULL;

-- Task dependencies
CREATE INDEX idx_task_dependencies_depends_on 
ON task_dependencies(depends_on_task_id);
```

### Queue Optimization
```typescript
interface QueueConfig {
  max_concurrent_tasks: 5;
  max_tasks_per_user: 10;
  priority_boost_age: 300; // seconds before boosting priority
  task_timeout: 3600; // 1 hour default timeout
  cleanup_interval: 3600; // Clean old tasks hourly
}
```

## Security & Limits

### Task Validation
```typescript
class TaskValidator {
  validate(task: CreateTaskRequest, user: User): ValidationResult {
    // Check user permissions
    if (!this.canCreateTask(user, task.task_type)) {
      throw new ForbiddenError('Insufficient permissions');
    }
    
    // Check rate limits
    const userTaskCount = await this.getUserActiveTaskCount(user.id);
    if (userTaskCount >= this.getTaskLimit(user.subscription_tier)) {
      throw new RateLimitError('Task limit exceeded');
    }
    
    // Validate input data
    this.validateInputData(task);
    
    return { valid: true };
  }
}
```

### Resource Limits
```typescript
const TASK_LIMITS = {
  free: {
    concurrent_tasks: 1,
    daily_tasks: 10,
    max_task_duration: 300 // 5 minutes
  },
  pro: {
    concurrent_tasks: 3,
    daily_tasks: 100,
    max_task_duration: 1800 // 30 minutes
  },
  team: {
    concurrent_tasks: 10,
    daily_tasks: 1000,
    max_task_duration: 7200 // 2 hours
  }
};
```

## Monitoring & Analytics

### Task Metrics
```typescript
interface TaskMetrics {
  total_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  average_duration: number;
  success_rate: number;
  by_type: {
    [taskType: string]: {
      count: number;
      avg_duration: number;
      success_rate: number;
    };
  };
}
```

### Health Monitoring
```typescript
class TaskHealthMonitor {
  async checkHealth(): Promise<HealthStatus> {
    const metrics = await this.getMetrics();
    
    return {
      queue_depth: metrics.pending_tasks,
      processing_tasks: metrics.processing_tasks,
      avg_wait_time: metrics.avg_queue_time,
      oldest_pending_task: metrics.oldest_pending,
      worker_status: await this.checkWorkers(),
      alerts: this.generateAlerts(metrics)
    };
  }
}
```

## Future Enhancements

1. **Distributed Processing**
   - Multi-worker support
   - Task sharding
   - Cross-region processing

2. **Advanced Scheduling**
   - Cron-like scheduling
   - Task templates
   - Recurring tasks

3. **Enhanced Monitoring**
   - Real-time dashboards
   - Performance analytics
   - Cost tracking

4. **Task Workflows**
   - Visual workflow builder
   - Conditional branching
   - Parallel execution

## Related Systems
- AI Agent System (task execution)
- Export/Import System (file operations)
- Analytics System (task metrics)
- Notification System (task updates)