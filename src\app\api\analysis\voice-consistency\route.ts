import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { applyRateLimit } from '@/lib/rate-limiter-unified'

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'ai-analysis', cost: 2 })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const user = request.user!
    const supabase = await createTypedServerClient()
    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')

    // Build query
    let query = supabase
      .from('voice_consistency_checks')
      .select(`
        id,
        character_name,
        consistency_score,
        check_type,
        details,
        created_at,
        chapter_id,
        chapters (
          chapter_number,
          title,
          project_id
        )
      `)
      .order('created_at', { ascending: false })

    // Filter by project if specified
    if (projectId) {
      // Need to join through chapters table
      query = query.eq('chapters.project_id', projectId)
    }

    const { data: consistencyChecks, error } = await query

    if (error) {
      logger.error('Error fetching voice consistency data:', error)
      return NextResponse.json({ error: 'Failed to fetch voice consistency data' }, { status: 500 })
    }

    // Process data to calculate metrics per character
    const characterMetrics = new Map<string, {
      consistency: number
      trend: 'up' | 'down' | 'stable'
      samples: number
      lastAnalyzed: string
      scores: number[]
    }>()

    // Group by character and calculate metrics
    consistencyChecks?.forEach(check => {
      const characterName = check.character_name
      const score = check.consistency_score || 0
      
      if (!characterMetrics.has(characterName)) {
        characterMetrics.set(characterName, {
          consistency: 0,
          trend: 'stable',
          samples: 0,
          lastAnalyzed: check.created_at,
          scores: []
        })
      }
      
      const metrics = characterMetrics.get(characterName)!
      metrics.scores.push(score)
      metrics.samples++
      
      // Update last analyzed if more recent
      if (new Date(check.created_at) > new Date(metrics.lastAnalyzed)) {
        metrics.lastAnalyzed = check.created_at
      }
    })

    // Calculate averages and trends
    const metricsArray = Array.from(characterMetrics.entries()).map(([character, data]) => {
      const avgScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length
      
      // Calculate trend based on last 5 scores
      let trend: 'up' | 'down' | 'stable' = 'stable'
      if (data.scores.length >= 2) {
        const recentScores = data.scores.slice(-5)
        const oldAvg = recentScores.slice(0, Math.floor(recentScores.length / 2)).reduce((a, b) => a + b, 0) / Math.floor(recentScores.length / 2)
        const newAvg = recentScores.slice(Math.floor(recentScores.length / 2)).reduce((a, b) => a + b, 0) / (recentScores.length - Math.floor(recentScores.length / 2))
        
        if (newAvg > oldAvg + 2) trend = 'up'
        else if (newAvg < oldAvg - 2) trend = 'down'
      }
      
      // Format last analyzed time
      const lastAnalyzedDate = new Date(data.lastAnalyzed)
      const now = new Date()
      const diffMs = now.getTime() - lastAnalyzedDate.getTime()
      const diffHours = Math.floor(diffMs / (TIME_MS.SECOND * 60 * 60))
      const diffDays = Math.floor(diffMs / (TIME_MS.SECOND * 60 * 60 * 24))
      
      let lastAnalyzedStr = 'just now'
      if (diffDays > 0) {
        lastAnalyzedStr = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
      } else if (diffHours > 0) {
        lastAnalyzedStr = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
      }
      
      return {
        character,
        consistency: Math.round(avgScore),
        trend,
        samples: data.samples,
        lastAnalyzed: lastAnalyzedStr
      }
    })

    // Sort by consistency score descending
    metricsArray.sort((a, b) => b.consistency - a.consistency)

    return NextResponse.json({ 
      metrics: metricsArray,
      total: metricsArray.length
    })
  } catch (error) {
    logger.error('Error in voice consistency GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'ai-analysis', cost: 2 })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const body = await request.json()
    const { content, profileId, projectId, chapterId } = body

    if (!content || !profileId) {
      return handleAPIError(new ValidationError('Content and profile ID are required'))
    }

    if (content.length < 100) {
      return handleAPIError(new ValidationError('Content must be at least 100 characters'))
    }

    // Get voice profile and verify ownership
    const { data: voiceProfile, error: profileError } = await supabase
      .from('voice_profiles')
      .select('*')
      .eq('id', profileId)
      .single()

    if (profileError || !voiceProfile) {
      return handleAPIError(new ValidationError('Voice profile not found'))
    }

    // Check if user has access to this profile
    if (voiceProfile.user_id !== user.id && !voiceProfile.is_global) {
      return handleAPIError(new ValidationError('Unauthorized access to voice profile'))
    }

    // Perform voice consistency analysis
    // This is a simplified mock implementation - in production, this would use AI
    const consistencyScore = Math.floor(Math.random() * 30) + 70 // 70-100 range
    
    // Generate mock suggestions based on consistency score
    const suggestions = []
    const deviations = []
    
    if (consistencyScore < 90) {
      if (Math.random() > 0.5) {
        suggestions.push({
          text: "Consider using more descriptive language to match the established voice pattern",
          severity: 'info' as const,
          location: { start: 0, end: 50 }
        })
      }
      
      if (consistencyScore < 80) {
        suggestions.push({
          text: "This sentence structure differs significantly from the voice profile",
          severity: 'warning' as const,
          location: { start: 100, end: 150 }
        })
        
        deviations.push({
          metric: "Average Sentence Length",
          expected: 18,
          actual: 25,
          difference: 7
        })
      }
      
      if (consistencyScore < 75) {
        suggestions.push({
          text: "The tone in this section doesn't match the established character voice",
          severity: 'error' as const,
          location: { start: 200, end: 250 }
        })
        
        deviations.push({
          metric: "Formality Score",
          expected: 0.6,
          actual: 0.8,
          difference: 0.2
        })
      }
    }

    // Save the analysis if chapterId provided
    if (chapterId) {
      const { error: checkError } = await supabase
        .from('voice_consistency_checks')
        .insert({
          chapter_id: chapterId,
          voice_profile_id: profileId,
          consistency_score: consistencyScore / 100, // Store as decimal
          check_type: 'manual',
          details: {
            suggestions,
            deviations,
            wordCount: content.split(/\s+/).length
          }
        })

      if (checkError) {
        logger.error('Error saving voice consistency check:', checkError)
        // Don't fail the request if saving fails, just log
      }
    }

    return NextResponse.json({
      consistencyScore,
      suggestions,
      deviations
    })
  } catch (error) {
    logger.error('Error in voice consistency POST:', error)
    return handleAPIError(error as Error)
  }
})