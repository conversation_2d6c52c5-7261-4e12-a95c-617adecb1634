import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { createClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')

    // Build query
    let query = supabase
      .from('voice_consistency_checks')
      .select(`
        id,
        character_name,
        consistency_score,
        check_type,
        details,
        created_at,
        chapter_id,
        chapters (
          chapter_number,
          title,
          project_id
        )
      `)
      .order('created_at', { ascending: false })

    // Filter by project if specified
    if (projectId) {
      // Need to join through chapters table
      query = query.eq('chapters.project_id', projectId)
    }

    const { data: consistencyChecks, error } = await query

    if (error) {
      logger.error('Error fetching voice consistency data:', error)
      return NextResponse.json({ error: 'Failed to fetch voice consistency data' }, { status: 500 })
    }

    // Process data to calculate metrics per character
    const characterMetrics = new Map<string, {
      consistency: number
      trend: 'up' | 'down' | 'stable'
      samples: number
      lastAnalyzed: string
      scores: number[]
    }>()

    // Group by character and calculate metrics
    consistencyChecks?.forEach(check => {
      const characterName = check.character_name
      const score = check.consistency_score || 0
      
      if (!characterMetrics.has(characterName)) {
        characterMetrics.set(characterName, {
          consistency: 0,
          trend: 'stable',
          samples: 0,
          lastAnalyzed: check.created_at,
          scores: []
        })
      }
      
      const metrics = characterMetrics.get(characterName)!
      metrics.scores.push(score)
      metrics.samples++
      
      // Update last analyzed if more recent
      if (new Date(check.created_at) > new Date(metrics.lastAnalyzed)) {
        metrics.lastAnalyzed = check.created_at
      }
    })

    // Calculate averages and trends
    const metricsArray = Array.from(characterMetrics.entries()).map(([character, data]) => {
      const avgScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length
      
      // Calculate trend based on last 5 scores
      let trend: 'up' | 'down' | 'stable' = 'stable'
      if (data.scores.length >= 2) {
        const recentScores = data.scores.slice(-5)
        const oldAvg = recentScores.slice(0, Math.floor(recentScores.length / 2)).reduce((a, b) => a + b, 0) / Math.floor(recentScores.length / 2)
        const newAvg = recentScores.slice(Math.floor(recentScores.length / 2)).reduce((a, b) => a + b, 0) / (recentScores.length - Math.floor(recentScores.length / 2))
        
        if (newAvg > oldAvg + 2) trend = 'up'
        else if (newAvg < oldAvg - 2) trend = 'down'
      }
      
      // Format last analyzed time
      const lastAnalyzedDate = new Date(data.lastAnalyzed)
      const now = new Date()
      const diffMs = now.getTime() - lastAnalyzedDate.getTime()
      const diffHours = Math.floor(diffMs / (TIME_MS.SECOND * 60 * 60))
      const diffDays = Math.floor(diffMs / (TIME_MS.SECOND * 60 * 60 * 24))
      
      let lastAnalyzedStr = 'just now'
      if (diffDays > 0) {
        lastAnalyzedStr = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
      } else if (diffHours > 0) {
        lastAnalyzedStr = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
      }
      
      return {
        character,
        consistency: Math.round(avgScore),
        trend,
        samples: data.samples,
        lastAnalyzed: lastAnalyzedStr
      }
    })

    // Sort by consistency score descending
    metricsArray.sort((a, b) => b.consistency - a.consistency)

    return NextResponse.json({ 
      metrics: metricsArray,
      total: metricsArray.length
    })
  } catch (error) {
    logger.error('Error in voice consistency GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    const { chapterId, content, characterName } = body

    if (!chapterId || !content) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Get voice profile for character if specified
    let voiceProfile = null
    if (characterName) {
      const { data: profiles } = await supabase
        .from('voice_profiles')
        .select('*')
        .eq('character_name', characterName)
        .eq('user_id', user.id)
        .single()
      
      voiceProfile = profiles
    }

    // Perform voice consistency analysis
    // This is a simplified version - in production, this would use the AI service
    const consistencyScore = Math.floor(Math.random() * 20) + 80 // 80-100 range
    const analysis = {
      consistency_score: consistencyScore,
      character_name: characterName || 'General',
      check_type: 'manual',
      details: {
        sentencePatterns: 'Consistent with established patterns',
        vocabularyMatch: 'High vocabulary consistency',
        toneAlignment: 'Tone matches character profile',
        suggestions: []
      }
    }

    // Save the analysis
    const { data: checkData, error: checkError } = await supabase
      .from('voice_consistency_checks')
      .insert({
        chapter_id: chapterId,
        character_name: analysis.character_name,
        consistency_score: analysis.consistency_score,
        check_type: analysis.check_type,
        details: analysis.details,
        voice_profile_id: voiceProfile?.id
      })
      .select()
      .single()

    if (checkError) {
      logger.error('Error saving voice consistency check:', checkError)
      return NextResponse.json({ 
        error: 'Failed to save consistency check' 
      }, { status: 500 })
    }

    return NextResponse.json({
      analysis: {
        ...analysis,
        id: checkData.id,
        created_at: checkData.created_at
      }
    })
  } catch (error) {
    logger.error('Error in voice consistency POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}