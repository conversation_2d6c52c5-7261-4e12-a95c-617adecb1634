# Database Performance Indexes

This document outlines the database indexes added to optimize query performance for BookScribe's new features.

## Overview

Performance indexes have been added to support efficient querying of:
- Voice Profiles and Consistency Checking
- Location Management (hierarchical data)
- Story Bible Entries
- Writing Sessions and Goals
- Full-text Search capabilities

## Index Categories

### 1. Voice Profile System

#### voice_profiles table
- `idx_voice_profiles_user_id` - Filter profiles by user
- `idx_voice_profiles_project_id` - Filter profiles by project
- `idx_voice_profiles_series_id` - Filter profiles by series
- `idx_voice_profiles_character_id` - Link profiles to characters
- `idx_voice_profiles_is_global` - Quick access to global profiles
- `idx_voice_profiles_type` - Filter by profile type (author/character/narrator)
- `idx_voice_profiles_composite_user_project` - Optimize user+project queries
- `idx_voice_profiles_composite_user_global` - Optimize global profile access
- `idx_voice_profiles_patterns` (GIN) - JSON pattern searches

#### voice_consistency_checks table
- `idx_voice_consistency_checks_project_id` - Analytics by project
- `idx_voice_consistency_checks_voice_profile_id` - Link to profiles
- `idx_voice_consistency_checks_chapter_id` - Chapter-specific checks
- `idx_voice_consistency_checks_character_name` - Character analytics
- `idx_voice_consistency_checks_created_at` - Time-based queries
- `idx_voice_consistency_checks_composite_project_character` - Project character reports

### 2. Location Management

#### locations table
- `idx_locations_project_id` - Filter by project
- `idx_locations_parent_id` - Hierarchical navigation
- `idx_locations_location_type` - Filter by type
- `idx_locations_composite_project_parent` - Tree traversal optimization
- `idx_locations_composite_project_type` - Type-based project queries
- `idx_locations_name_search` (GIN) - Full-text location name search
- `idx_locations_description_search` (GIN) - Full-text description search
- `idx_locations_attributes` (GIN) - JSON attribute queries
- `idx_locations_coordinates` (GIN) - Coordinate-based queries

### 3. Story Bible

#### story_bible table
- `idx_story_bible_project_id` - Filter by project
- `idx_story_bible_entry_type` - Filter by type (world_rule, timeline_event, etc.)
- `idx_story_bible_entry_key` - Direct key access
- `idx_story_bible_composite_project_type` - Type queries within project
- `idx_story_bible_composite_project_type_key` - Specific entry lookup
- `idx_story_bible_updated_at` - Recent changes tracking
- `idx_story_bible_content_search` (GIN) - Full-text content search
- `idx_story_bible_entry_data` (GIN) - JSON data queries

#### story_bibles table (single bible per project)
- `idx_story_bibles_project_id` - Project lookup
- `idx_story_bibles_updated_at` - Recent updates

### 4. Writing Analytics

#### writing_sessions table
- `idx_writing_sessions_user_id` - User sessions
- `idx_writing_sessions_project_id` - Project sessions
- `idx_writing_sessions_started_at` - Time-based queries
- `idx_writing_sessions_composite_user_project` - User project activity
- `idx_writing_sessions_composite_user_started` - User timeline

#### writing_goals table
- `idx_writing_goals_user_id` - User goals
- `idx_writing_goals_project_id` - Project goals
- `idx_writing_goals_goal_type` - Goal type filtering
- `idx_writing_goals_is_active` - Active goals
- `idx_writing_goals_composite_user_type_active` - Active goals by type
- `idx_writing_goals_composite_project_active` - Active project goals

#### writing_goal_progress table
- `idx_writing_goal_progress_goal_id` - Progress by goal
- `idx_writing_goal_progress_date` - Time-based progress
- `idx_writing_goal_progress_composite_goal_date` - Goal timeline

### 5. Core Tables (Additional Indexes)

#### projects table
- `idx_projects_series_id` - Series navigation

#### chapters table
- `idx_chapters_project_id` - Project chapters
- `idx_chapters_chapter_number` - Chapter ordering
- `idx_chapters_composite_project_number` - Chapter lookup

#### characters table
- `idx_characters_project_id` - Project characters
- `idx_characters_character_type` - Character type filtering
- `idx_characters_composite_project_type` - Type-based queries

## Query Optimization Examples

### 1. Voice Profile Queries

```sql
-- Optimized by idx_voice_profiles_composite_user_project
SELECT * FROM voice_profiles 
WHERE user_id = ? AND project_id = ?;

-- Optimized by idx_voice_profiles_composite_user_global
SELECT * FROM voice_profiles 
WHERE user_id = ? AND is_global = true;
```

### 2. Location Hierarchy

```sql
-- Optimized by idx_locations_composite_project_parent
WITH RECURSIVE location_tree AS (
  SELECT * FROM locations 
  WHERE project_id = ? AND parent_id IS NULL
  UNION ALL
  SELECT l.* FROM locations l
  JOIN location_tree lt ON l.parent_id = lt.id
)
SELECT * FROM location_tree;
```

### 3. Story Bible Search

```sql
-- Optimized by idx_story_bible_content_search (GIN)
SELECT * FROM story_bible
WHERE project_id = ? 
AND to_tsvector('english', entry_data->>'content') @@ plainto_tsquery('english', ?);
```

### 4. Voice Consistency Analytics

```sql
-- Optimized by idx_voice_consistency_checks_composite_project_character
SELECT 
  character_name,
  AVG(consistency_score) as avg_score,
  COUNT(*) as check_count
FROM voice_consistency_checks
WHERE project_id = ?
GROUP BY character_name
ORDER BY avg_score DESC;
```

## Performance Monitoring

To verify index usage:

```sql
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM voice_profiles 
WHERE user_id = 'user-id' AND project_id = 'project-id';
```

To check index sizes:

```sql
SELECT 
  schemaname,
  tablename,
  indexname,
  pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
FROM pg_indexes
WHERE schemaname = 'public'
ORDER BY pg_relation_size(indexname::regclass) DESC;
```

## Maintenance

Indexes are automatically maintained by PostgreSQL, but for optimal performance:

1. Run `ANALYZE` after bulk data imports
2. Monitor slow query logs
3. Use `pg_stat_user_indexes` to track index usage
4. Consider `REINDEX` for heavily updated tables

## Migration

Apply indexes using the migration file:
```bash
supabase migration up 20250131_add_performance_indexes
```

Or run the analysis script to check existing indexes:
```bash
node scripts/db/analyze-indexes.js
```