/**
 * Accessibility Enhancement Hook
 * Provides utilities for improving component accessibility
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { announceToScreenReader } from '@/lib/accessibility/aria-labels';

interface UseAccessibilityOptions {
  // Focus management
  autoFocus?: boolean;
  restoreFocus?: boolean;
  focusTrap?: boolean;
  
  // Keyboard navigation
  enableArrowNavigation?: boolean;
  enableEscapeKey?: boolean;
  onEscape?: () => void;
  
  // Screen reader
  announceChanges?: boolean;
  liveRegion?: 'polite' | 'assertive' | 'off';
  
  // Reduced motion
  respectReducedMotion?: boolean;
}

export function useAccessibility(options: UseAccessibilityOptions = {}) {
  const {
    autoFocus = false,
    restoreFocus = true,
    focusTrap = false,
    enableArrowNavigation = false,
    enableEscapeKey = false,
    onEscape,
    announceChanges = false,
    liveRegion = 'polite',
    respectReducedMotion = true,
  } = options;

  const containerRef = useRef<HTMLElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  // Check for reduced motion preference
  useEffect(() => {
    if (!respectReducedMotion) return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [respectReducedMotion]);

  // Auto focus management
  useEffect(() => {
    if (!autoFocus || !containerRef.current) return;

    const focusableElements = getFocusableElements(containerRef.current);
    if (focusableElements.length > 0) {
      previousFocusRef.current = document.activeElement as HTMLElement;
      focusableElements[0].focus();
    }

    return () => {
      if (restoreFocus && previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    };
  }, [autoFocus, restoreFocus]);

  // Focus trap
  useEffect(() => {
    if (!focusTrap || !containerRef.current) return;

    const container = containerRef.current;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      const focusableElements = getFocusableElements(container);
      if (focusableElements.length === 0) return;

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  }, [focusTrap]);

  // Arrow navigation
  useEffect(() => {
    if (!enableArrowNavigation || !containerRef.current) return;

    const container = containerRef.current;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) return;

      const focusableElements = getFocusableElements(container);
      const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement);
      
      if (currentIndex === -1) return;

      let nextIndex: number;
      
      switch (e.key) {
        case 'ArrowDown':
        case 'ArrowRight':
          nextIndex = (currentIndex + 1) % focusableElements.length;
          break;
        case 'ArrowUp':
        case 'ArrowLeft':
          nextIndex = (currentIndex - 1 + focusableElements.length) % focusableElements.length;
          break;
        default:
          return;
      }

      e.preventDefault();
      focusableElements[nextIndex].focus();
    };

    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  }, [enableArrowNavigation]);

  // Escape key handling
  useEffect(() => {
    if (!enableEscapeKey || !onEscape) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onEscape();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [enableEscapeKey, onEscape]);

  // Announce function for screen readers
  const announce = useCallback((message: string, priority?: 'polite' | 'assertive') => {
    if (announceChanges) {
      announceToScreenReader(message, priority || liveRegion as 'polite' | 'assertive');
    }
  }, [announceChanges, liveRegion]);

  // Focus management utilities
  const focusFirst = useCallback(() => {
    if (!containerRef.current) return;
    const focusableElements = getFocusableElements(containerRef.current);
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }, []);

  const focusLast = useCallback(() => {
    if (!containerRef.current) return;
    const focusableElements = getFocusableElements(containerRef.current);
    if (focusableElements.length > 0) {
      focusableElements[focusableElements.length - 1].focus();
    }
  }, []);

  return {
    containerRef,
    prefersReducedMotion,
    announce,
    focusFirst,
    focusLast,
    ariaProps: {
      'aria-live': announceChanges ? liveRegion : undefined,
      'aria-atomic': announceChanges ? 'true' : undefined,
    },
  };
}

// Utility function to get focusable elements
function getFocusableElements(container: HTMLElement): HTMLElement[] {
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'textarea:not([disabled])',
    'select:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
  ].join(', ');

  return Array.from(container.querySelectorAll<HTMLElement>(focusableSelectors))
    .filter(el => el.offsetParent !== null); // Filter out hidden elements
}

// Hook for managing keyboard shortcuts
interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  meta?: boolean;
  handler: () => void;
  description?: string;
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[], enabled = true) {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      for (const shortcut of shortcuts) {
        const ctrlMatch = shortcut.ctrl ? e.ctrlKey || e.metaKey : !e.ctrlKey && !e.metaKey;
        const shiftMatch = shortcut.shift ? e.shiftKey : !e.shiftKey;
        const altMatch = shortcut.alt ? e.altKey : !e.altKey;
        const metaMatch = shortcut.meta ? e.metaKey : !e.metaKey;
        
        if (
          e.key.toLowerCase() === shortcut.key.toLowerCase() &&
          ctrlMatch &&
          shiftMatch &&
          altMatch &&
          metaMatch
        ) {
          e.preventDefault();
          shortcut.handler();
          break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts, enabled]);

  // Return shortcuts for displaying in UI
  return shortcuts.map(s => ({
    key: s.key,
    modifiers: [
      s.ctrl && 'Ctrl',
      s.shift && 'Shift',
      s.alt && 'Alt',
      s.meta && 'Cmd',
    ].filter(Boolean),
    description: s.description,
  }));
}

// Hook for managing focus within a list
export function useListNavigation<T extends HTMLElement>(items: T[]) {
  const [focusedIndex, setFocusedIndex] = useState(-1);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev < items.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev > 0 ? prev - 1 : items.length - 1
        );
        break;
      case 'Home':
        e.preventDefault();
        setFocusedIndex(0);
        break;
      case 'End':
        e.preventDefault();
        setFocusedIndex(items.length - 1);
        break;
    }
  }, [items.length]);

  useEffect(() => {
    if (focusedIndex >= 0 && focusedIndex < items.length) {
      items[focusedIndex]?.focus();
    }
  }, [focusedIndex, items]);

  return {
    focusedIndex,
    handleKeyDown,
    setFocusedIndex,
  };
}

// Hook for managing roving tabindex
export function useRovingTabIndex(count: number, initialIndex = 0) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  const getRovingProps = useCallback((index: number) => ({
    tabIndex: index === currentIndex ? 0 : -1,
    onFocus: () => setCurrentIndex(index),
  }), [currentIndex]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        e.preventDefault();
        setCurrentIndex(prev => (prev + 1) % count);
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        e.preventDefault();
        setCurrentIndex(prev => (prev - 1 + count) % count);
        break;
      case 'Home':
        e.preventDefault();
        setCurrentIndex(0);
        break;
      case 'End':
        e.preventDefault();
        setCurrentIndex(count - 1);
        break;
    }
  }, [count]);

  return {
    currentIndex,
    getRovingProps,
    handleKeyDown,
  };
}