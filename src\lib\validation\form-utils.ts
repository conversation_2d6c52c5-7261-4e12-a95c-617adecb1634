import { z } from 'zod';
import { TIME_MS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

/**
 * Standardized form validation utilities for BookScribe
 * Provides consistent validation patterns across all forms
 */

// ============================================
// Common Field Validators
// ============================================

/**
 * Required field validator with custom message
 */
export const required = (fieldName: string = 'This field') => 
  z.string().min(1, `${fieldName} is required`);

/**
 * Email validator with comprehensive checks
 */
export const email = (customMessage?: string) => 
  z.string()
    .min(1, 'Email is required')
    .email(customMessage || 'Please enter a valid email address')
    .toLowerCase()
    .trim();

/**
 * Password validator with security requirements
 */
export const password = (options?: {
  minLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
}) => {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = false
  } = options || {};

  let schema = z.string()
    .min(minLength, `Password must be at least ${minLength} characters long`);

  if (requireUppercase) {
    schema = schema.regex(/[A-Z]/, 'Password must contain at least one uppercase letter');
  }
  if (requireLowercase) {
    schema = schema.regex(/[a-z]/, 'Password must contain at least one lowercase letter');
  }
  if (requireNumbers) {
    schema = schema.regex(/[0-9]/, 'Password must contain at least one number');
  }
  if (requireSpecialChars) {
    schema = schema.regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character');
  }

  return schema;
};

/**
 * Username validator
 */
export const username = () =>
  z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, hyphens, and underscores');

/**
 * URL validator
 */
export const url = (customMessage?: string) =>
  z.string()
    .url(customMessage || 'Please enter a valid URL')
    .or(z.literal(''));

/**
 * Phone number validator (US format)
 */
export const phoneNumber = () =>
  z.string()
    .regex(/^\+?1?\d{10,14}$/, 'Please enter a valid phone number')
    .or(z.literal(''));

/**
 * Positive integer validator
 */
export const positiveInt = (fieldName: string = 'Value') =>
  z.number({
    required_error: `${fieldName} is required`,
    invalid_type_error: `${fieldName} must be a number`,
  })
    .int(`${fieldName} must be a whole number`)
    .positive(`${fieldName} must be positive`);

/**
 * Currency amount validator
 */
export const currency = (options?: { min?: number; max?: number }) => {
  let schema = z.number({
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number',
  }).multipleOf(0.01, 'Amount must have at most 2 decimal places');

  if (options?.min !== undefined) {
    schema = schema.min(options.min, `Amount must be at least $${options.min}`);
  }
  if (options?.max !== undefined) {
    schema = schema.max(options.max, `Amount must be at most $${options.max}`);
  }

  return schema;
};

// ============================================
// Text Content Validators
// ============================================

/**
 * Title validator with length constraints
 */
export const title = (options?: { min?: number; max?: number }) =>
  z.string()
    .min(options?.min || 1, 'Title is required')
    .max(options?.max || 200, `Title must be less than ${options?.max || 200} characters`)
    .trim();

/**
 * Description validator
 */
export const description = (options?: { required?: boolean; maxLength?: number }) =>
  options?.required
    ? z.string()
        .min(1, 'Description is required')
        .max(options?.maxLength || TIME_MS.SECOND, `Description must be less than ${options?.maxLength || TIME_MS.SECOND} characters`)
        .trim()
    : z.string()
        .max(options?.maxLength || TIME_MS.SECOND, `Description must be less than ${options?.maxLength || TIME_MS.SECOND} characters`)
        .trim()
        .optional();

/**
 * Rich text content validator
 */
export const richText = (options?: { required?: boolean; maxLength?: number }) => {
  const base = z.string().trim();
  
  if (options?.required) {
    return base
      .min(1, 'Content is required')
      .max(options?.maxLength || SIZE_LIMITS.MAX_DOCUMENT_CHARS, `Content must be less than ${options?.maxLength || SIZE_LIMITS.MAX_DOCUMENT_CHARS} characters`);
  }
  
  return base
    .max(options?.maxLength || SIZE_LIMITS.MAX_DOCUMENT_CHARS, `Content must be less than ${options?.maxLength || SIZE_LIMITS.MAX_DOCUMENT_CHARS} characters`)
    .optional();
};

// ============================================
// Date and Time Validators
// ============================================

/**
 * Date validator
 */
export const date = (options?: { min?: Date; max?: Date; required?: boolean }) => {
  let schema = z.date({
    required_error: 'Date is required',
    invalid_type_error: 'Invalid date',
  });

  if (options?.min) {
    schema = schema.min(options.min, `Date must be after ${options.min.toLocaleDateString()}`);
  }
  if (options?.max) {
    schema = schema.max(options.max, `Date must be before ${options.max.toLocaleDateString()}`);
  }

  return options?.required === false ? schema.optional() : schema;
};

/**
 * Date string validator (ISO format)
 */
export const dateString = () =>
  z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
    .refine((val) => !isNaN(Date.parse(val)), 'Invalid date');

// ============================================
// Array and Object Validators
// ============================================

/**
 * Tag array validator
 */
export const tags = (options?: { min?: number; max?: number; maxLength?: number }) =>
  z.array(
    z.string()
      .min(1, 'Tag cannot be empty')
      .max(options?.maxLength || 50, `Tag must be less than ${options?.maxLength || 50} characters`)
      .trim()
  )
    .min(options?.min || 0, `At least ${options?.min} tag(s) required`)
    .max(options?.max || 20, `Maximum ${options?.max || 20} tags allowed`)
    .default([]);

/**
 * File upload validator
 */
export const fileUpload = (options?: {
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
  required?: boolean;
}) => {
  const schema = z.instanceof(File, {
    message: 'Please select a file',
  }).refine(
    (file) => !options?.maxSize || file.size <= options.maxSize,
    `File size must be less than ${options?.maxSize ? (options.maxSize / 1024 / 1024).toFixed(1) : 10}MB`
  ).refine(
    (file) => !options?.acceptedTypes || options.acceptedTypes.includes(file.type),
    `File type must be one of: ${options?.acceptedTypes?.join(', ') || 'any'}`
  );

  return options?.required === false ? schema.optional() : schema;
};

// ============================================
// BookScribe-Specific Validators
// ============================================

/**
 * Word count validator
 */
export const wordCount = (options?: { min?: number; max?: number }) =>
  positiveInt('Word count')
    .min(options?.min || 1, `Word count must be at least ${options?.min || 1}`)
    .max(options?.max || 1000000, `Word count must be less than ${options?.max || 1000000}`);

/**
 * Chapter number validator
 */
export const chapterNumber = () =>
  positiveInt('Chapter number')
    .min(1, 'Chapter number must be at least 1')
    .max(999, 'Chapter number must be less than TIME_MS.SECOND');

/**
 * Genre validator
 */
export const genre = () =>
  z.enum([
    'fantasy',
    'science-fiction',
    'mystery',
    'romance',
    'thriller',
    'horror',
    'historical',
    'literary',
    'young-adult',
    'non-fiction',
    'other'
  ], {
    errorMap: () => ({ message: 'Please select a valid genre' })
  });

/**
 * Project status validator
 */
export const projectStatus = () =>
  z.enum(['planning', 'writing', 'editing', 'completed', 'on-hold'], {
    errorMap: () => ({ message: 'Please select a valid status' })
  });

// ============================================
// Form Field Helpers
// ============================================

/**
 * Create a required field indicator component props
 */
export const requiredFieldProps = {
  className: 'after:content-["*"] after:ml-1 after:text-destructive',
  'aria-required': true,
};

/**
 * Get consistent error message styling
 */
export const errorMessageProps = {
  className: 'text-sm text-destructive mt-1',
  role: 'alert',
  'aria-live': 'polite',
};

/**
 * Standard form field wrapper props
 */
export const formFieldProps = {
  className: 'space-y-2',
};

// ============================================
// Validation Helpers
// ============================================

/**
 * Check if a field has validation errors
 */
export const hasError = (fieldName: string, errors: Record<string, string | undefined>): boolean => {
  return !!errors[fieldName];
};

/**
 * Get field error message
 */
export const getErrorMessage = (fieldName: string, errors: Record<string, string | undefined>): string | undefined => {
  return errors[fieldName];
};

/**
 * Combine multiple schemas with proper error messages
 */
export const combineSchemas = <T extends z.ZodRawShape>(schemas: T): z.ZodObject<T> => {
  return z.object(schemas);
};

// ============================================
// Common Form Schemas
// ============================================

/**
 * Login form schema
 */
export const loginFormSchema = z.object({
  email: email(),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

/**
 * Registration form schema
 */
export const registrationFormSchema = z.object({
  email: email(),
  password: password(),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
  username: username().optional(),
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions',
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

/**
 * Project creation form schema
 */
export const projectFormSchema = z.object({
  title: title({ max: 200 }),
  description: description({ maxLength: TIME_MS.SECOND }),
  genre: genre(),
  status: projectStatus(),
  targetWordCount: wordCount({ min: TIME_MS.SECOND, max: 500000 }).optional(),
  targetChapters: chapterNumber().optional(),
  tags: tags({ max: 10 }),
});

// Export types
export type LoginFormData = z.infer<typeof loginFormSchema>;
export type RegistrationFormData = z.infer<typeof registrationFormSchema>;
export type ProjectFormData = z.infer<typeof projectFormSchema>;