'use client'

import { useRouter } from 'next/navigation'
import { 
  ChapterPreview,
  CharacterPreview,
  LocationPreview,
  StoryBiblePreview
} from './previews'
import type { SearchResult } from './content-search-interface'

interface SearchResultPreviewProps {
  result: SearchResult
  projectId: string
  onEdit?: (result: SearchResult) => void
  className?: string
}

export function SearchResultPreview({ 
  result, 
  projectId,
  onEdit,
  className 
}: SearchResultPreviewProps) {
  const router = useRouter()

  const handleNavigate = () => {
    switch (result.type) {
      case 'chapter':
        router.push(`/projects/${projectId}/chapters/${result.id}`)
        break
      case 'character':
        router.push(`/projects/${projectId}/characters/${result.id}`)
        break
      case 'location':
        router.push(`/projects/${projectId}/locations#${result.id}`)
        break
      case 'story_bible':
        router.push(`/projects/${projectId}/story-bible#${result.id}`)
        break
      case 'note':
        router.push(`/projects/${projectId}/notes/${result.id}`)
        break
    }
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit(result)
    } else {
      // Default edit behavior - navigate to edit page
      switch (result.type) {
        case 'chapter':
          router.push(`/projects/${projectId}/chapters/${result.id}/edit`)
          break
        case 'character':
          router.push(`/projects/${projectId}/characters/${result.id}/edit`)
          break
        case 'location':
          router.push(`/projects/${projectId}/locations/${result.id}/edit`)
          break
        case 'story_bible':
          router.push(`/projects/${projectId}/story-bible/${result.id}/edit`)
          break
        case 'note':
          router.push(`/projects/${projectId}/notes/${result.id}/edit`)
          break
      }
    }
  }

  switch (result.type) {
    case 'chapter':
      return (
        <ChapterPreview
          result={result}
          onNavigate={handleNavigate}
          onEdit={handleEdit}
          className={className}
        />
      )
    case 'character':
      return (
        <CharacterPreview
          result={result}
          onNavigate={handleNavigate}
          onEdit={handleEdit}
          className={className}
        />
      )
    case 'location':
      return (
        <LocationPreview
          result={result}
          onNavigate={handleNavigate}
          onEdit={handleEdit}
          className={className}
        />
      )
    case 'story_bible':
      return (
        <StoryBiblePreview
          result={result}
          onNavigate={handleNavigate}
          onEdit={handleEdit}
          className={className}
        />
      )
    default:
      // For notes or other types, use a generic preview (could create NotePreview component)
      return (
        <ChapterPreview
          result={result}
          onNavigate={handleNavigate}
          onEdit={handleEdit}
          className={className}
        />
      )
  }
}