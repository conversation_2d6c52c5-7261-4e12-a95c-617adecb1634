'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Users, Wifi, WifiOff, Settings, UserPlus, Copy, Check } from 'lucide-react';
import { CollaboratorAvatars } from './collaborator-cursors';
import { Collaborator } from '@/lib/collaboration/collaboration-manager';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface CollaborationStatusProps {
  isConnected: boolean;
  isConnecting: boolean;
  isSyncing: boolean;
  collaborators: Collaborator[];
  projectId: string;
  documentId: string;
  onConnect?: () => void;
  onDisconnect?: () => void;
  className?: string;
  variant?: 'default' | 'compact' | 'minimal';
}

export function CollaborationStatus({
  isConnected,
  isConnecting,
  isSyncing,
  collaborators,
  projectId,
  documentId,
  onConnect,
  onDisconnect,
  className,
  variant = 'default',
}: CollaborationStatusProps) {
  const { toast } = useToast();
  const [copied, setCopied] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const handleCopyLink = async () => {
    const collaborationUrl = `${window.location.origin}/projects/${projectId}/collaborate/${documentId}`;
    await navigator.clipboard.writeText(collaborationUrl);
    setCopied(true);
    
    toast({
      title: 'Collaboration link copied',
      description: 'Share this link with others to collaborate in real-time',
    });
    
    setTimeout(() => setCopied(false), 2000);
  };

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        {isConnected ? (
          <Wifi className="w-4 h-4 text-success" />
        ) : (
          <WifiOff className="w-4 h-4 text-muted-foreground" />
        )}
        {collaborators.length > 0 && (
          <Badge variant="secondary" className="text-xs">
            {collaborators.length}
          </Badge>
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn('h-8 px-2', className)}
          >
            <div className="flex items-center gap-2">
              {isConnecting ? (
                <div className="w-2 h-2 bg-warning rounded-full animate-pulse" />
              ) : isConnected ? (
                <Wifi className="w-3 h-3 text-success" />
              ) : (
                <WifiOff className="w-3 h-3 text-muted-foreground" />
              )}
              {collaborators.length > 0 && (
                <span className="text-xs font-medium">{collaborators.length}</span>
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent align="end" className="w-80">
          <CollaborationPanel
            isConnected={isConnected}
            isConnecting={isConnecting}
            isSyncing={isSyncing}
            collaborators={collaborators}
            onConnect={onConnect}
            onDisconnect={onDisconnect}
            onCopyLink={handleCopyLink}
            copied={copied}
          />
        </PopoverContent>
      </Popover>
    );
  }

  // Default variant - full status bar
  return (
    <Card className={cn('px-3 py-1.5 bg-background/95 backdrop-blur-sm border shadow-sm', className)}>
      <div className="flex items-center gap-2">
        {/* Connection status */}
        <div className="flex items-center gap-1.5">
          {isConnecting ? (
            <>
              <div className="w-2 h-2 bg-warning rounded-full animate-pulse" />
              <span className="text-xs text-muted-foreground">Connecting...</span>
            </>
          ) : isConnected ? (
            <>
              <Wifi className="w-3 h-3 text-success" />
              <span className="text-xs text-muted-foreground">Connected</span>
            </>
          ) : (
            <>
              <WifiOff className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Offline</span>
            </>
          )}
        </div>

        {/* Sync indicator */}
        {isSyncing && (
          <>
            <div className="h-3 w-px bg-border" />
            <Badge variant="secondary" className="text-xs px-1.5 py-0 animate-pulse">
              Syncing...
            </Badge>
          </>
        )}

        {/* Collaborator count */}
        {isConnected && collaborators.length > 0 && (
          <>
            <div className="h-3 w-px bg-border" />
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs font-medium">{collaborators.length}</span>
            </div>
          </>
        )}

        {/* Actions */}
        <div className="h-3 w-px bg-border" />
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2"
            onClick={() => isConnected ? onDisconnect?.() : onConnect?.()}
          >
            <span className="text-xs">
              {isConnected ? 'Disconnect' : 'Connect'}
            </span>
          </Button>
          
          {isConnected && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={handleCopyLink}
              >
                {copied ? (
                  <Check className="w-3 h-3 text-success" />
                ) : (
                  <UserPlus className="w-3 h-3" />
                )}
              </Button>
              
              <Popover open={showSettings} onOpenChange={setShowSettings}>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1"
                  >
                    <Settings className="w-3 h-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="end" className="w-80">
                  <CollaborationPanel
                    isConnected={isConnected}
                    isConnecting={isConnecting}
                    isSyncing={isSyncing}
                    collaborators={collaborators}
                    onConnect={onConnect}
                    onDisconnect={onDisconnect}
                    onCopyLink={handleCopyLink}
                    copied={copied}
                  />
                </PopoverContent>
              </Popover>
            </>
          )}
        </div>
      </div>
    </Card>
  );
}

// Collaboration panel component for popover
interface CollaborationPanelProps {
  isConnected: boolean;
  isConnecting: boolean;
  isSyncing: boolean;
  collaborators: Collaborator[];
  onConnect?: () => void;
  onDisconnect?: () => void;
  onCopyLink?: () => void;
  copied?: boolean;
}

function CollaborationPanel({
  isConnected,
  isConnecting,
  isSyncing,
  collaborators,
  onConnect,
  onDisconnect,
  onCopyLink,
  copied,
}: CollaborationPanelProps) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-semibold text-sm mb-2">Collaboration Status</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Connection</span>
            <div className="flex items-center gap-1.5">
              {isConnecting ? (
                <>
                  <div className="w-2 h-2 bg-warning rounded-full animate-pulse" />
                  <span className="text-sm">Connecting</span>
                </>
              ) : isConnected ? (
                <>
                  <Wifi className="w-3 h-3 text-success" />
                  <span className="text-sm text-success">Connected</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Disconnected</span>
                </>
              )}
            </div>
          </div>
          
          {isSyncing && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Sync Status</span>
              <Badge variant="secondary" className="text-xs animate-pulse">
                Syncing changes...
              </Badge>
            </div>
          )}
        </div>
      </div>

      {isConnected && collaborators.length > 0 && (
        <div>
          <h3 className="font-semibold text-sm mb-2">
            Active Collaborators ({collaborators.length})
          </h3>
          <div className="space-y-2">
            {collaborators.map((collaborator) => (
              <div
                key={collaborator.id}
                className="flex items-center justify-between p-2 rounded-md bg-muted/50"
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium text-white"
                    style={{ backgroundColor: collaborator.color }}
                  >
                    {collaborator.name.charAt(0).toUpperCase()}
                  </div>
                  <span className="text-sm font-medium">{collaborator.name}</span>
                </div>
                <Badge
                  variant={collaborator.status === 'active' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {collaborator.status}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex gap-2">
        <Button
          variant={isConnected ? 'outline' : 'default'}
          size="sm"
          className="flex-1"
          onClick={() => isConnected ? onDisconnect?.() : onConnect?.()}
          disabled={isConnecting}
        >
          {isConnected ? 'Disconnect' : 'Connect'}
        </Button>
        
        {isConnected && (
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={onCopyLink}
          >
            {copied ? (
              <>
                <Check className="w-3 h-3 mr-2" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="w-3 h-3 mr-2" />
                Share Link
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}