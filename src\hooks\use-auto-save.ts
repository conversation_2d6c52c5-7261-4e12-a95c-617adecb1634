'use client'

import { useEffect, useRef, useCallback } from 'react'
import { logger } from '@/lib/services/logger';

import { createClient } from '@/lib/supabase'
import { useAchievementTracker, useWordCountTracker } from '@/hooks/use-achievement-tracker'
import { TIME_MS } from '@/lib/constants'

interface AutoSaveOptions {
  delay?: number // Auto-save delay in milliseconds
  enabled?: boolean
  onSave?: (success: boolean) => void
  onError?: (error: Error) => void
}

interface AutoSaveData {
  chapterId: string
  title?: string
  content?: string
  wordCount?: number
}

export function useAutoSave(
  data: AutoSaveData, 
  options: AutoSaveOptions = {}
) {
  const {
    delay = TIME_MS.TYPING_TIMEOUT, // 3 seconds default
    enabled = true,
    onSave,
    onError
  } = options

  const supabase = createClient()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedRef = useRef<string>('')
  const isSavingRef = useRef(false)
  const { trackDailyWriting } = useAchievementTracker()
  const lastWordCountRef = useRef<number>(0)

  // Create a stable reference to the save function
  const saveChapter = useCallback(async (saveData: AutoSaveData) => {
    if (isSavingRef.current) return

    try {
      isSavingRef.current = true
      
      const updateData: Record<string, unknown> = {
        updated_at: new Date().toISOString()
      }

      if (saveData.title !== undefined) updateData.title = saveData.title
      if (saveData.content !== undefined) updateData.content = saveData.content
      if (saveData.wordCount !== undefined) updateData.actual_word_count = saveData.wordCount

      const { error } = await supabase
        .from('chapters')
        .update(updateData)
        .eq('id', saveData.chapterId)

      if (error) throw error

      // Track daily writing achievement if word count increased
      if (saveData.wordCount && saveData.wordCount > lastWordCountRef.current) {
        await trackDailyWriting()
        lastWordCountRef.current = saveData.wordCount
      }

      // Update last saved reference
      lastSavedRef.current = JSON.stringify(saveData)
      onSave?.(true)
    } catch (error) {
      logger.error('Auto-save error:', error);
      onError?.(error as Error)
      onSave?.(false)
    } finally {
      isSavingRef.current = false
    }
  }, [supabase, onSave, onError, trackDailyWriting])

  // Auto-save effect
  useEffect(() => {
    if (!enabled || !data.chapterId) return

    const currentDataString = JSON.stringify(data)
    
    // Don't save if data hasn't changed
    if (currentDataString === lastSavedRef.current) return

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      saveChapter(data)
    }, delay)

    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [data, delay, enabled, saveChapter])

  // Manual save function
  const saveNow = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    return saveChapter(data)
  }, [data, saveChapter])

  // Force save on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        // Note: We can't reliably save on unmount in React
        // Consider using beforeunload event in the component that uses this hook
      }
    }
  }, [])

  return {
    saveNow,
    isSaving: isSavingRef.current
  }
}

// Hook for manual version creation
export function useVersionControl(chapterId: string) {

  const createVersion = useCallback(async (changeSummary: string) => {
    try {
      // This would call the manual version creation API
      const response = await fetch('/api/chapters/create-version', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chapterId,
          changeSummary
        })
      })

      if (!response.ok) throw new Error('Failed to create version')
      
      return true
    } catch (error) {
      logger.error('Error creating version:', error);
      return false
    }
  }, [chapterId])

  const restoreVersion = useCallback(async (versionId: string) => {
    try {
      const response = await fetch('/api/chapters/restore-version', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chapterId,
          versionId
        })
      })

      if (!response.ok) throw new Error('Failed to restore version')
      
      return true
    } catch (error) {
      logger.error('Error restoring version:', error);
      return false
    }
  }, [chapterId])

  return {
    createVersion,
    restoreVersion
  }
}

// Utility hook for tracking unsaved changes
export function useUnsavedChanges() {
  const hasUnsavedChanges = useRef(false)

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges.current) {
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return 'You have unsaved changes. Are you sure you want to leave?'
      }
      return
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  const setUnsavedChanges = useCallback((hasChanges: boolean) => {
    hasUnsavedChanges.current = hasChanges
  }, [])

  return { setUnsavedChanges }
}