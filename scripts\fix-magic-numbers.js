const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Mapping of magic numbers to their constant replacements
const replacements = [
  // Animation durations
  { pattern: /duration:\s*0\.2(?!\d)/g, replacement: 'duration: ANIMATION_DURATION.FAST', import: "import { ANIMATION_DURATION } from '@/lib/constants'" },
  { pattern: /duration:\s*0\.3(?!\d)/g, replacement: 'duration: ANIMATION_DURATION.NORMAL', import: "import { ANIMATION_DURATION } from '@/lib/constants'" },
  { pattern: /duration:\s*0\.5(?!\d)/g, replacement: 'duration: ANIMATION_DURATION.SLOW', import: "import { ANIMATION_DURATION } from '@/lib/constants'" },
  { pattern: /duration:\s*1\.2(?!\d)/g, replacement: 'duration: ANIMATION_DURATION.VERY_SLOW', import: "import { ANIMATION_DURATION } from '@/lib/constants'" },
  
  // Animation delays
  { pattern: /delay:\s*0\.05(?!\d)/g, replacement: 'delay: ANIMATION_DELAY.STAGGER_SMALL', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  { pattern: /delay:\s*0\.1(?!\d)/g, replacement: 'delay: ANIMATION_DELAY.STAGGER_MEDIUM', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  { pattern: /delay:\s*0\.15(?!\d)/g, replacement: 'delay: ANIMATION_DELAY.STAGGER_LARGE', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  { pattern: /delay:\s*0\.2(?!\d)/g, replacement: 'delay: ANIMATION_DELAY.SMALL', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  { pattern: /delay:\s*0\.3(?!\d)/g, replacement: 'delay: ANIMATION_DELAY.MEDIUM', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  { pattern: /delay:\s*0\.4(?!\d)/g, replacement: 'delay: ANIMATION_DELAY.LARGE', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  { pattern: /delay:\s*index\s*\*\s*0\.05/g, replacement: 'delay: index * ANIMATION_DELAY.STAGGER_SMALL', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  { pattern: /delay:\s*i\s*\*\s*0\.1/g, replacement: 'delay: i * ANIMATION_DELAY.STAGGER_MEDIUM', import: "import { ANIMATION_DELAY } from '@/lib/constants'" },
  
  // Animation scales
  { pattern: /scale:\s*0\.5(?!\d)/g, replacement: 'scale: ANIMATION_SCALE.SHRINK_MEDIUM', import: "import { ANIMATION_SCALE } from '@/lib/constants'" },
  { pattern: /scale:\s*0\.8(?!\d)/g, replacement: 'scale: ANIMATION_SCALE.SHRINK_SMALL', import: "import { ANIMATION_SCALE } from '@/lib/constants'" },
  { pattern: /scale:\s*1\.1(?!\d)/g, replacement: 'scale: ANIMATION_SCALE.GROW_SMALL', import: "import { ANIMATION_SCALE } from '@/lib/constants'" },
  { pattern: /scale:\s*1\.2(?!\d)/g, replacement: 'scale: ANIMATION_SCALE.GROW_MEDIUM', import: "import { ANIMATION_SCALE } from '@/lib/constants'" },
  { pattern: /scale:\s*1\.5(?!\d)/g, replacement: 'scale: ANIMATION_SCALE.GROW_LARGE', import: "import { ANIMATION_SCALE } from '@/lib/constants'" },
  { pattern: /scale:\s*\[1,\s*1\.2,\s*1\]/g, replacement: 'scale: [ANIMATION_SCALE.NORMAL, ANIMATION_SCALE.GROW_MEDIUM, ANIMATION_SCALE.NORMAL]', import: "import { ANIMATION_SCALE } from '@/lib/constants'" },
  { pattern: /scale:\s*\[1,\s*1\.1,\s*1\]/g, replacement: 'scale: [ANIMATION_SCALE.NORMAL, ANIMATION_SCALE.GROW_SMALL, ANIMATION_SCALE.NORMAL]', import: "import { ANIMATION_SCALE } from '@/lib/constants'" },
  
  // Animation opacity
  { pattern: /opacity:\s*0\.2(?!\d)/g, replacement: 'opacity: ANIMATION_OPACITY.VERY_FAINT', import: "import { ANIMATION_OPACITY } from '@/lib/constants'" },
  { pattern: /opacity:\s*0\.3(?!\d)/g, replacement: 'opacity: ANIMATION_OPACITY.FAINT', import: "import { ANIMATION_OPACITY } from '@/lib/constants'" },
  { pattern: /opacity:\s*0\.5(?!\d)/g, replacement: 'opacity: ANIMATION_OPACITY.HALF', import: "import { ANIMATION_OPACITY } from '@/lib/constants'" },
  { pattern: /opacity:\s*0\.6(?!\d)/g, replacement: 'opacity: ANIMATION_OPACITY.SUBTLE', import: "import { ANIMATION_OPACITY } from '@/lib/constants'" },
  { pattern: /opacity:\s*0\.7(?!\d)/g, replacement: 'opacity: ANIMATION_OPACITY.MUTED', import: "import { ANIMATION_OPACITY } from '@/lib/constants'" },
  { pattern: /opacity:\s*\[0\.5,\s*1,\s*0\.5\]/g, replacement: 'opacity: [ANIMATION_OPACITY.HALF, ANIMATION_OPACITY.VISIBLE, ANIMATION_OPACITY.HALF]', import: "import { ANIMATION_OPACITY } from '@/lib/constants'" },
  
  // Rate limits
  { pattern: /\.check\(5,/g, replacement: '.check(RATE_LIMITS.AI_GENERATION,', import: "import { RATE_LIMITS } from '@/lib/constants'" },
  { pattern: /\.check\(10,/g, replacement: '.check(RATE_LIMITS.SERVICE_ORCHESTRATOR_WRITE,', import: "import { RATE_LIMITS } from '@/lib/constants'" },
  { pattern: /\.check\(15,/g, replacement: '.check(RATE_LIMITS.AI_CONTENT,', import: "import { RATE_LIMITS } from '@/lib/constants'" },
  { pattern: /\.check\(20,/g, replacement: '.check(RATE_LIMITS.SERVICE_HEALTH_CHECK,', import: "import { RATE_LIMITS } from '@/lib/constants'" },
  { pattern: /\.check\(30,/g, replacement: '.check(RATE_LIMITS.SERVICE_ORCHESTRATOR_READ,', import: "import { RATE_LIMITS } from '@/lib/constants'" },
  
  // UI dimensions
  { pattern: /viewportWidth\s*\*\s*0\.2/g, replacement: 'viewportWidth * UI_DIMENSIONS.PANEL_MIN_WIDTH_RATIO', import: "import { UI_DIMENSIONS } from '@/lib/constants'" },
  { pattern: /viewportWidth\s*\*\s*0\.6/g, replacement: 'viewportWidth * UI_DIMENSIONS.PANEL_MAX_WIDTH_RATIO', import: "import { UI_DIMENSIONS } from '@/lib/constants'" },
  { pattern: /Math\.min\(280,/g, replacement: 'Math.min(UI_DIMENSIONS.MOBILE_PANEL_MIN_WIDTH,', import: "import { UI_DIMENSIONS } from '@/lib/constants'" },
  
  // Celebration physics
  { pattern: /gravity:\s*0\.5/g, replacement: 'gravity: CELEBRATION_PHYSICS.GRAVITY', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /decay:\s*0\.94/g, replacement: 'decay: CELEBRATION_PHYSICS.DECAY_NORMAL', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /decay:\s*0\.91/g, replacement: 'decay: CELEBRATION_PHYSICS.DECAY_SLOW', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /decay:\s*0\.92/g, replacement: 'decay: CELEBRATION_PHYSICS.DECAY_FAST', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /scalar:\s*0\.75/g, replacement: 'scalar: CELEBRATION_PHYSICS.SCALAR_SMALL', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /scalar:\s*0\.8(?!\d)/g, replacement: 'scalar: CELEBRATION_PHYSICS.SCALAR_NORMAL', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /scalar:\s*1\.2/g, replacement: 'scalar: CELEBRATION_PHYSICS.SCALAR_LARGE', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /drift:\s*0\.5/g, replacement: 'drift: CELEBRATION_PHYSICS.DRIFT', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  { pattern: /origin:\s*{\s*y:\s*0\.7\s*}/g, replacement: 'origin: { y: CELEBRATION_PHYSICS.ORIGIN_Y }', import: "import { CELEBRATION_PHYSICS } from '@/lib/constants'" },
  
  // Achievement multipliers
  { pattern: /avgWords\s*\*\s*1\.5/g, replacement: 'avgWords * ACHIEVEMENT_MULTIPLIERS.DAILY_CHALLENGE', import: "import { ACHIEVEMENT_MULTIPLIERS } from '@/lib/constants'" },
  { pattern: /avgWords\s*\*\s*7\s*\*\s*1\.2/g, replacement: 'avgWords * 7 * ACHIEVEMENT_MULTIPLIERS.WEEKLY_CHALLENGE', import: "import { ACHIEVEMENT_MULTIPLIERS } from '@/lib/constants'" },
  { pattern: /streak\s*\*\s*0\.4/g, replacement: 'streak * ACHIEVEMENT_MULTIPLIERS.STREAK_CONTENT_BOOST', import: "import { ACHIEVEMENT_MULTIPLIERS } from '@/lib/constants'" },
  
  // Quality thresholds
  { pattern: /quality_score\s*>=\s*0\.5/g, replacement: 'quality_score >= QUALITY_THRESHOLDS.MINIMUM_ACCEPTABLE', import: "import { QUALITY_THRESHOLDS } from '@/lib/constants'" },
  { pattern: /quality_score\s*<\s*0\.5/g, replacement: 'quality_score < QUALITY_THRESHOLDS.MINIMUM_ACCEPTABLE', import: "import { QUALITY_THRESHOLDS } from '@/lib/constants'" },
  
  // Monitoring
  { pattern: /temperature:\s*0\.7/g, replacement: 'temperature: AI_MODEL_PARAMS.DEFAULT_TEMPERATURE', import: "import { AI_MODEL_PARAMS } from '@/lib/constants'" },
  { pattern: /tracesSampleRate:\s*isProduction\s*\?\s*0\.1\s*:\s*1\.0/g, replacement: 'tracesSampleRate: isProduction ? MONITORING.TRACES_SAMPLE_RATE_PROD : MONITORING.TRACES_SAMPLE_RATE_DEV', import: "import { MONITORING } from '@/lib/constants'" },
  { pattern: /replaysSessionSampleRate:\s*0\.1/g, replacement: 'replaysSessionSampleRate: MONITORING.REPLAY_SESSION_SAMPLE_RATE', import: "import { MONITORING } from '@/lib/constants'" },
  
  // Sitemap priority
  { pattern: /priority:\s*route\s*===\s*''\s*\?\s*1\s*:\s*0\.8/g, replacement: "priority: route === '' ? SITEMAP_PRIORITY.HOMEPAGE : SITEMAP_PRIORITY.IMPORTANT_PAGE", import: "import { SITEMAP_PRIORITY } from '@/lib/constants'" },
  
  // Line height
  { pattern: /line-height:\s*1\.2/g, replacement: 'line-height: LINE_HEIGHT.TIGHT', import: "import { LINE_HEIGHT } from '@/lib/constants'" },
  { pattern: /line-height:\s*1\.5/g, replacement: 'line-height: LINE_HEIGHT.NORMAL', import: "import { LINE_HEIGHT } from '@/lib/constants'" },
  
  // Chapter expansion
  { pattern: /expansionFactor\s*\|\|\s*1\.5/g, replacement: 'expansionFactor || CHAPTER_EXPANSION.DEFAULT_FACTOR', import: "import { CHAPTER_EXPANSION } from '@/lib/constants'" },
  { pattern: /\.min\(1\.2\)/g, replacement: '.min(CHAPTER_EXPANSION.MIN_FACTOR)', import: "import { CHAPTER_EXPANSION } from '@/lib/constants'" },
  { pattern: /\.max\(3\)/g, replacement: '.max(CHAPTER_EXPANSION.MAX_FACTOR)', import: "import { CHAPTER_EXPANSION } from '@/lib/constants'" },
  
  // Concurrency
  { pattern: /\.min\(1\)\.max\(5\)/g, replacement: '.min(CONCURRENCY_LIMITS.MIN_BATCH_SIZE).max(CONCURRENCY_LIMITS.MAX_BATCH_SIZE)', import: "import { CONCURRENCY_LIMITS } from '@/lib/constants'" },
  
  // Time constants
  { pattern: /86400000/g, replacement: 'RATE_LIMIT_WINDOWS.ONE_DAY', import: "import { RATE_LIMIT_WINDOWS } from '@/lib/constants'" },
];

function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf-8');
  const originalContent = content;
  let importsToAdd = new Set();
  let changesMade = false;
  
  // Apply replacements
  replacements.forEach(({ pattern, replacement, import: importStatement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      if (importStatement) {
        importsToAdd.add(importStatement);
      }
      changesMade = true;
    }
  });
  
  if (!changesMade) {
    return false;
  }
  
  // Add imports if needed
  if (importsToAdd.size > 0) {
    const importStatements = Array.from(importsToAdd).join('\n');
    
    // Find the last import statement
    const importRegex = /^import[\s\S]*?from[\s\S]*?$/gm;
    const imports = content.match(importRegex);
    
    if (imports && imports.length > 0) {
      const lastImport = imports[imports.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport);
      const insertPosition = lastImportIndex + lastImport.length;
      
      // Check if imports already exist
      const existingImports = imports.join('\n');
      const newImports = [];
      
      importsToAdd.forEach(imp => {
        if (!existingImports.includes(imp.match(/from\s+['"]([^'"]+)['"]/)[1])) {
          newImports.push(imp);
        }
      });
      
      if (newImports.length > 0) {
        content = content.slice(0, insertPosition) + '\n' + newImports.join('\n') + content.slice(insertPosition);
      }
    } else {
      // No imports found, add at the beginning
      content = importStatements + '\n\n' + content;
    }
  }
  
  fs.writeFileSync(filePath, content, 'utf-8');
  return true;
}

// Process all TypeScript files
const files = glob.sync('src/**/*.{ts,tsx}', {
  ignore: [
    'src/**/*.test.{ts,tsx}',
    'src/**/*.spec.{ts,tsx}',
    'src/**/*.d.ts',
    'src/lib/constants/**/*',
    'src/lib/db/types.ts',
  ]
});

let updatedCount = 0;
files.forEach(file => {
  if (updateFile(file)) {
    console.log(`Updated: ${file}`);
    updatedCount++;
  }
});

console.log(`\nTotal files updated: ${updatedCount}`);