import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase';
import { createAdminClient } from '@/lib/supabase/admin';
import { logger } from '@/lib/services/logger';
import { handleAPIError, AuthorizationError, ValidationError } from '@/lib/api/error-handler';

export interface AdminActionContext {
  adminUserId: string;
  action: string;
  targetResource?: string;
  targetId?: string;
  requestDetails?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  startTime: number;
}

export interface AdminSecurityConfig {
  requiredPermission?: string;
  maxSessionAgeMinutes?: number;
  requireMFA?: boolean;
  allowedActions?: string[];
  rateLimitPerHour?: number;
}

/**
 * Enhanced admin security middleware with comprehensive validation and audit logging
 */
export class AdminSecurityMiddleware {
  private static auditContext: Map<string, AdminActionContext> = new Map();

  /**
   * Validates admin access with enhanced security checks
   */
  static async validateAdminAccess(
    request: NextRequest,
    config: AdminSecurityConfig = {}
  ): Promise<{ user: any; adminClient: any } | NextResponse> {
    const startTime = Date.now();
    const ipAddress = this.getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    try {
      // 1. Basic authentication check
      const supabase = await createServerClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        await this.logAdminAction(null, 'admin_access_denied', 'authentication', null, {
          reason: 'authentication_failed',
          error: authError?.message
        }, ipAddress, userAgent, Date.now() - startTime, false, authError?.message);
        
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // 2. Admin role validation
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, mfa_enabled, require_mfa_for_admin, admin_permissions, session_timeout_minutes')
        .eq('id', user.id)
        .single();

      if (profileError || profile?.role !== 'admin') {
        await this.logAdminAction(user.id, 'admin_access_denied', 'authorization', null, {
          reason: 'insufficient_privileges',
          userRole: profile?.role || 'unknown'
        }, ipAddress, userAgent, Date.now() - startTime, false, 'User is not an admin');
        
        return NextResponse.json(
          { error: 'Admin privileges required' },
          { status: 403 }
        );
      }

      // 3. Enhanced session validation using database function
      const adminClient = createAdminClient();
      const { data: sessionValid, error: sessionError } = await adminClient
        .rpc('validate_admin_access', {
          required_permission: config.requiredPermission || null,
          max_session_age_minutes: config.maxSessionAgeMinutes || profile.session_timeout_minutes || 60
        });

      if (sessionError || !sessionValid) {
        await this.logAdminAction(user.id, 'admin_session_invalid', 'session_validation', null, {
          reason: 'session_expired_or_invalid',
          requiredPermission: config.requiredPermission,
          sessionError: sessionError?.message
        }, ipAddress, userAgent, Date.now() - startTime, false, 'Admin session validation failed');
        
        return NextResponse.json(
          { error: 'Admin session expired or invalid. Please re-authenticate.' },
          { status: 401 }
        );
      }

      // 4. MFA validation if required
      if (config.requireMFA && profile.require_mfa_for_admin && !profile.mfa_enabled) {
        await this.logAdminAction(user.id, 'admin_mfa_required', 'mfa_validation', null, {
          reason: 'mfa_not_enabled',
          requireMFA: config.requireMFA
        }, ipAddress, userAgent, Date.now() - startTime, false, 'MFA required but not enabled');
        
        return NextResponse.json(
          { error: 'Multi-factor authentication required for this admin operation' },
          { status: 403 }
        );
      }

      // 5. Permission-specific validation
      if (config.requiredPermission) {
        const hasPermission = profile.admin_permissions?.[config.requiredPermission] === true;
        if (!hasPermission) {
          await this.logAdminAction(user.id, 'admin_permission_denied', 'permission_check', null, {
            reason: 'insufficient_permission',
            requiredPermission: config.requiredPermission,
            userPermissions: profile.admin_permissions
          }, ipAddress, userAgent, Date.now() - startTime, false, `Missing permission: ${config.requiredPermission}`);
          
          return NextResponse.json(
            { error: `Insufficient permissions for this operation` },
            { status: 403 }
          );
        }
      }

      // 6. Rate limiting check
      if (config.rateLimitPerHour) {
        const recentActions = await this.getRecentAdminActions(user.id, 1); // Last hour
        if (recentActions >= config.rateLimitPerHour) {
          await this.logAdminAction(user.id, 'admin_rate_limited', 'rate_limit', null, {
            reason: 'rate_limit_exceeded',
            recentActions,
            limit: config.rateLimitPerHour
          }, ipAddress, userAgent, Date.now() - startTime, false, 'Admin rate limit exceeded');
          
          return NextResponse.json(
            { error: 'Admin action rate limit exceeded. Please try again later.' },
            { status: 429 }
          );
        }
      }

      // 7. Action allowlist validation
      if (config.allowedActions && config.allowedActions.length > 0) {
        // This would be validated when the action is logged
      }

      // Success - create admin client and return
      logger.info('Admin access validated successfully', {
        adminId: user.id,
        ipAddress,
        requiredPermission: config.requiredPermission,
        validationTime: Date.now() - startTime
      });

      return { user, adminClient };

    } catch (error) {
      logger.error('Admin access validation failed', error);
      
      await this.logAdminAction(null, 'admin_validation_error', 'system_error', null, {
        reason: 'validation_system_error',
        error: error instanceof Error ? error.message : 'Unknown error'
      }, ipAddress, userAgent, Date.now() - startTime, false, error instanceof Error ? error.message : 'System error');
      
      return NextResponse.json(
        { error: 'Admin validation system error' },
        { status: 500 }
      );
    }
  }

  /**
   * Creates an admin action context for audit logging
   */
  static startAdminAction(
    adminUserId: string,
    action: string,
    targetResource?: string,
    targetId?: string,
    requestDetails?: Record<string, unknown>,
    request?: NextRequest
  ): string {
    const contextId = `${adminUserId}_${Date.now()}_${Math.random()}`;
    
    this.auditContext.set(contextId, {
      adminUserId,
      action,
      targetResource,
      targetId,
      requestDetails,
      ipAddress: request ? this.getClientIP(request) : undefined,
      userAgent: request?.headers.get('user-agent') || 'unknown',
      startTime: Date.now()
    });

    return contextId;
  }

  /**
   * Completes an admin action with audit logging
   */
  static async completeAdminAction(
    contextId: string,
    success: boolean = true,
    errorMessage?: string,
    additionalDetails?: Record<string, unknown>
  ): Promise<void> {
    const context = this.auditContext.get(contextId);
    if (!context) {
      logger.warn('Admin action context not found', { contextId });
      return;
    }

    const executionTime = Date.now() - context.startTime;
    
    await this.logAdminAction(
      context.adminUserId,
      context.action,
      context.targetResource,
      context.targetId,
      { ...context.requestDetails, ...additionalDetails },
      context.ipAddress,
      context.userAgent,
      executionTime,
      success,
      errorMessage
    );

    // Clean up context
    this.auditContext.delete(contextId);
  }

  /**
   * Logs admin actions to the audit trail
   */
  private static async logAdminAction(
    adminUserId: string | null,
    action: string,
    targetResource?: string,
    targetId?: string,
    requestDetails?: Record<string, unknown>,
    ipAddress?: string,
    userAgent?: string,
    executionTimeMs?: number,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    try {
      if (!adminUserId) {
        // For failed authentication, log to application logs only
        logger.warn('Admin action failed - no user ID', {
          action,
          targetResource,
          ipAddress,
          success,
          errorMessage
        });
        return;
      }

      const adminClient = createAdminClient();
      
      // Use the log_admin_action function from the database
      await adminClient.rpc('log_admin_action', {
        action_name: action,
        target_resource: targetResource || null,
        target_id: targetId || null,
        request_details: requestDetails ? JSON.stringify(requestDetails) : null,
        ip_address: ipAddress || null,
        user_agent: userAgent || null,
        execution_time_ms: executionTimeMs || null,
        success,
        error_message: errorMessage || null
      });

      // Also log to application logs for immediate monitoring
      logger.info('Admin action logged', {
        adminUserId,
        action,
        targetResource,
        targetId,
        success,
        executionTimeMs,
        ipAddress: ipAddress ? this.maskIP(ipAddress) : undefined
      });

    } catch (error) {
      // Log to application logs if database logging fails
      logger.error('Failed to log admin action to database', {
        error,
        adminUserId,
        action,
        targetResource,
        success
      });
    }
  }

  /**
   * Gets recent admin action count for rate limiting
   */
  private static async getRecentAdminActions(adminUserId: string, hoursBack: number = 1): Promise<number> {
    try {
      const adminClient = createAdminClient();
      const { count, error } = await adminClient
        .from('admin_audit_logs')
        .select('*', { count: 'exact', head: true })
        .eq('admin_user_id', adminUserId)
        .gte('created_at', new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString());

      if (error) {
        logger.error('Failed to get recent admin actions', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      logger.error('Error checking recent admin actions', error);
      return 0;
    }
  }

  /**
   * Detects suspicious admin activity
   */
  static async detectSuspiciousActivity(): Promise<any[]> {
    try {
      const adminClient = createAdminClient();
      const { data, error } = await adminClient.rpc('detect_suspicious_admin_activity');

      if (error) {
        logger.error('Failed to detect suspicious admin activity', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error detecting suspicious admin activity', error);
      return [];
    }
  }

  /**
   * Extracts client IP address from request
   */
  private static getClientIP(request: NextRequest): string {
    // Check various headers that might contain the real IP
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const clientIP = request.headers.get('x-client-ip');
    
    if (forwardedFor) {
      // X-Forwarded-For can contain multiple IPs, take the first one
      return forwardedFor.split(',')[0].trim();
    }
    
    if (realIP) return realIP;
    if (clientIP) return clientIP;
    
    // Fallback to connection IP (though this might be a proxy)
    return request.ip || 'unknown';
  }

  /**
   * Masks IP address for logging (privacy)
   */
  private static maskIP(ip: string): string {
    if (ip.includes(':')) {
      // IPv6 - mask last 64 bits
      const parts = ip.split(':');
      return parts.slice(0, 4).join(':') + '::xxxx';
    } else {
      // IPv4 - mask last octet
      const parts = ip.split('.');
      return parts.slice(0, 3).join('.') + '.xxx';
    }
  }

  /**
   * Cleans up expired admin sessions
   */
  static async cleanupExpiredSessions(): Promise<number> {
    try {
      const adminClient = createAdminClient();
      const { data, error } = await adminClient.rpc('cleanup_expired_admin_sessions');

      if (error) {
        logger.error('Failed to cleanup expired admin sessions', error);
        return 0;
      }

      return data || 0;
    } catch (error) {
      logger.error('Error cleaning up expired admin sessions', error);
      return 0;
    }
  }
}

/**
 * Decorator for admin API routes that automatically handles security validation
 */
export function withAdminSecurity(config: AdminSecurityConfig = {}) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (request: NextRequest, ...args: any[]) {
      const validationResult = await AdminSecurityMiddleware.validateAdminAccess(request, config);
      
      // If validation failed, return the error response
      if (validationResult instanceof NextResponse) {
        return validationResult;
      }

      const { user, adminClient } = validationResult;
      
      // Start action context
      const contextId = AdminSecurityMiddleware.startAdminAction(
        user.id,
        `${target.constructor.name}.${propertyKey}`,
        config.requiredPermission,
        undefined,
        { method: request.method, url: request.url },
        request
      );

      try {
        // Call the original method with admin context
        const result = await originalMethod.call(this, request, { user, adminClient }, ...args);
        
        // Complete action successfully
        await AdminSecurityMiddleware.completeAdminAction(contextId, true);
        
        return result;
      } catch (error) {
        // Complete action with error
        await AdminSecurityMiddleware.completeAdminAction(
          contextId, 
          false, 
          error instanceof Error ? error.message : 'Unknown error'
        );
        
        throw error;
      }
    };

    return descriptor;
  };
}

export default AdminSecurityMiddleware;