import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { 
  Mail, 
  MessageSquare, 
  FileText, 
  Users, 
  Bug, 
  Lightbulb,
  ExternalLink,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

export default function ContactPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Get in Touch</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          We're here to help you get the most out of BookScribe AI. 
          Choose the best way to reach us based on your needs.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6 mb-12">
        {/* Help Center */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-primary" />
              Help Center
            </CardTitle>
            <CardDescription>
              Find answers to common questions and learn how to use BookScribe AI
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Our comprehensive help center covers everything from getting started to advanced features. 
              Most questions can be answered here instantly.
            </p>
            <Button asChild className="w-full">
              <Link href="/help">
                Browse Help Center
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Email Support */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-primary" />
              Email Support
            </CardTitle>
            <CardDescription>
              Get personalized help with your account or technical issues
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Our support team typically responds within 24 hours. 
              Include as much detail as possible to help us assist you quickly.
            </p>
            <Button asChild variant="outline" className="w-full">
              <a href="mailto:<EMAIL>">
                Email Support
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Contact Categories */}
      <div className="space-y-6 mb-12">
        <h2 className="text-2xl font-bold text-center">Specific Inquiries</h2>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Bug Reports */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Bug className="h-4 w-4 text-red-500" />
                Bug Reports
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-muted-foreground mb-3">
                Found a bug? Help us fix it by providing detailed information.
              </p>
              <Button variant="outline" size="sm" asChild className="w-full">
                <a href="mailto:<EMAIL>?subject=Bug Report">
                  Report Bug
                </a>
              </Button>
            </CardContent>
          </Card>

          {/* Feature Requests */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                Feature Requests
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-muted-foreground mb-3">
                Have an idea for a new feature? We'd love to hear it!
              </p>
              <Button variant="outline" size="sm" asChild className="w-full">
                <a href="https://feedback.bookscribe.ai" target="_blank" rel="noopener noreferrer">
                  Submit Idea
                  <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </Button>
            </CardContent>
          </Card>

          {/* Business Inquiries */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Users className="h-4 w-4 text-blue-500" />
                Business & Partnerships
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-muted-foreground mb-3">
                Interested in partnerships, bulk licenses, or media inquiries?
              </p>
              <Button variant="outline" size="sm" asChild className="w-full">
                <a href="mailto:<EMAIL>?subject=Business Inquiry">
                  Contact Sales
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Community */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-primary" />
            Join Our Community
          </CardTitle>
          <CardDescription>
            Connect with other writers and get the latest updates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Join our growing community of writers who are using AI to enhance their storytelling. 
            Share tips, get feedback, and stay updated on new features.
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button variant="outline" asChild>
              <a href="https://discord.gg/bookscribe" target="_blank" rel="noopener noreferrer">
                Discord Community
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href="https://twitter.com/bookscribeai" target="_blank" rel="noopener noreferrer">
                Follow on Twitter
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href="https://blog.bookscribe.ai" target="_blank" rel="noopener noreferrer">
                Read Our Blog
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Response Times */}
      <Card>
        <CardHeader>
          <CardTitle>Response Times</CardTitle>
          <CardDescription>
            What to expect when you contact us
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-green-600 mb-1">General Support</h4>
              <p className="text-muted-foreground">Within 24 hours</p>
            </div>
            <div>
              <h4 className="font-medium text-yellow-600 mb-1">Bug Reports</h4>
              <p className="text-muted-foreground">Within 12 hours</p>
            </div>
            <div>
              <h4 className="font-medium text-blue-600 mb-1">Urgent Issues</h4>
              <p className="text-muted-foreground">Within 4 hours</p>
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-4">
            Response times are for business days (Monday-Friday, 9 AM - 6 PM PST). 
            We do our best to respond faster, but these are our guaranteed maximums.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}