/**
 * Type definitions for performance monitoring
 */

// Web Vitals metric types
export interface WebVitalsMetric {
  id: string
  name: 'FCP' | 'LCP' | 'CLS' | 'FID' | 'TTFB' | 'INP'
  label: 'web-vital' | 'custom'
  value: number
  delta: number
  entries: PerformanceEntry[]
  navigationType: 'navigate' | 'reload' | 'back_forward' | 'prerender'
}

// Extend Window interface for gtag
declare global {
  interface Window {
    gtag?: (
      command: 'event' | 'config' | 'set',
      target: string,
      params?: Record<string, unknown>
    ) => void
  }
}

// Component import type
export type ComponentImport<T = React.ComponentType> = () => Promise<{ default: T }>