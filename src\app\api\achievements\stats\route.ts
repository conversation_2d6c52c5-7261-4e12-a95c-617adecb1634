import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return handleAPIError(new ValidationError('Invalid request'))
    }
    
    const supabase = await createTypedServerClient()
    
    // Get total achievements
    const { data: allAchievements, error: achievementsError } = await supabase
      .from('achievements')
      .select('id')

    if (achievementsError) {
      // If achievements table doesn't exist, return default stats
      if (achievementsError.code === '42P01') {
        logger.warn('Achievements table does not exist, returning default stats')
        return NextResponse.json({
          totalAchievements: 0,
          unlockedAchievements: 0,
          completionPercentage: 0,
          recentAchievements: []
        })
      }
      throw achievementsError
    }
    
    // Get user's unlocked achievements
    const { data: unlockedAchievements, error: unlockedError } = await supabase
      .from('user_achievements')
      .select(`
        achievement_id,
        unlocked_at,
        achievements(title)
      `)
      .eq('user_id', userId)
      .not('unlocked_at', 'is', null)
      .order('unlocked_at', { ascending: false })
    
    if (unlockedError) {
      // If user_achievements table doesn't exist, return default stats
      if (unlockedError.code === '42P01') {
        return NextResponse.json({
          totalAchievements: allAchievements?.length || 0,
          unlockedAchievements: 0,
          completionPercentage: 0,
          recentAchievements: []
        })
      }
      throw unlockedError
    }
    
    // Get next milestone achievement
    const { data: progressAchievements, error: progressError } = await supabase
      .from('user_achievements')
      .select(`
        achievement_id,
        progress,
        achievements(
          title,
          criteria,
          category
        )
      `)
      .eq('user_id', userId)
      .is('unlocked_at', null)
      .gt('progress', 0)
      .order('progress', { ascending: false })
      .limit(1)
    
    if (progressError) throw progressError
    
    const stats = {
      totalUnlocked: unlockedAchievements?.length || 0,
      totalAchievements: allAchievements?.length || 0,
      recentAchievement: unlockedAchievements?.[0] ? {
        name: unlockedAchievements[0].achievements?.title,
        unlockedAt: unlockedAchievements[0].unlocked_at
      } : undefined,
      nextMilestone: progressAchievements?.[0] ? {
        name: progressAchievements[0].achievements?.title,
        progress: progressAchievements[0].progress || 0,
        requirement: progressAchievements[0].achievements?.criteria?.value || 1
      } : undefined
    }
    
    return NextResponse.json(stats)
  } catch (error) {
    logger.error('Error fetching achievement stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch achievement stats' },
      { status: 500 }
    )
  }
}