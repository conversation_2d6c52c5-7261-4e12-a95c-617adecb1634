'use client'

import { useState, useEffect } from 'react'
import { MemoryDashboard } from '@/components/memory/memory-dashboard'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Brain, Zap, Database, Info, Settings } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'

interface Project {
  id: string
  title: string
  description?: string
}

export default function MemoryPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [selectedProjectId, setSelectedProjectId] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchProjects()
  }, [])

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (!response.ok) throw new Error('Failed to fetch projects')
      
      const data = await response.json()
      setProjects(data.projects || [])
      
      // Auto-select first project if available
      if (data.projects?.length > 0 && !selectedProjectId) {
        setSelectedProjectId(data.projects[0].id)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load projects',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleOptimize = () => {
    toast({
      title: 'Memory Optimized',
      description: 'Your project memory has been optimized successfully'
    })
  }

  return (
    <div className="container max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-4xl font-literary-display text-foreground mb-2">
              Memory Management
            </h1>
            <p className="text-lg text-muted-foreground">
              Monitor and optimize your AI context memory for better performance
            </p>
          </div>
          <div className="flex items-center gap-4">
            {loading ? (
              <Skeleton className="h-10 w-[200px]" />
            ) : (
              <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
      </div>

      {/* Info Card */}
      <Card className="mb-8 border-info bg-info/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-info" />
            Understanding Memory Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            BookScribe uses advanced memory management to maintain context across your entire novel. 
            As your story grows, the AI intelligently compresses and organizes information to stay within token limits while preserving essential details.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start gap-3">
              <Brain className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Smart Compression</h4>
                <p className="text-xs text-muted-foreground">
                  Automatically compresses older content while keeping recent chapters detailed
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Database className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Chunk Organization</h4>
                <p className="text-xs text-muted-foreground">
                  Groups related information together for efficient retrieval
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Zap className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Performance Optimization</h4>
                <p className="text-xs text-muted-foreground">
                  Maintains fast response times even with large manuscripts
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      ) : !selectedProjectId ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Project Selected</h3>
            <p className="text-muted-foreground mb-4">
              Select a project from the dropdown above to view its memory usage
            </p>
            <Button asChild>
              <Link href="/projects/new">
                Create Your First Project
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="dashboard" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="dashboard">Memory Dashboard</TabsTrigger>
            <TabsTrigger value="optimizer">Memory Optimizer</TabsTrigger>
          </TabsList>
          
          <TabsContent value="dashboard">
            <MemoryDashboard 
              projectId={selectedProjectId} 
              onOptimize={handleOptimize}
            />
          </TabsContent>
          
          <TabsContent value="optimizer">
            <Card>
              <CardHeader>
                <CardTitle>Memory Optimizer</CardTitle>
                <CardDescription>
                  Advanced memory optimization tools are coming soon
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  The memory optimizer will allow you to fine-tune your project's memory usage with advanced compression and cleanup options.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Tips Card */}
      {selectedProjectId && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Memory Optimization Tips
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Compress memory when token usage exceeds 70% for optimal performance</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Merge similar chunks regularly to reduce redundancy and improve context quality</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>High importance scores indicate crucial story elements that won't be compressed</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Monitor memory health regularly for novels exceeding 50,000 words</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  )
}