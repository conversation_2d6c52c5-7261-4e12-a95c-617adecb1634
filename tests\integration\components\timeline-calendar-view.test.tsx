import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TimelineCalendarView } from '@/components/timeline/timeline-calendar-view'
import type { TimelineEvent } from '@/components/timeline/timeline-calendar-view'

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}))

describe('TimelineCalendarView Component', () => {
  const mockEvents: TimelineEvent[] = [
    {
      id: 'event-1',
      type: 'plot',
      title: 'The Beginning',
      description: 'Story starts here',
      date: '2024-01-15T00:00:00Z',
      chapter: 1,
      scene: 1,
      characters: ['Hero', 'Mentor'],
      location: 'Village',
      importance: 'high',
      verified: true,
    },
    {
      id: 'event-2',
      type: 'character',
      title: 'Meeting the Villain',
      description: 'First encounter',
      date: '2024-01-20T00:00:00Z',
      chapter: 3,
      scene: 2,
      characters: ['Hero', 'Villain'],
      location: 'Dark Forest',
      importance: 'critical',
      verified: false,
    },
  ]

  const mockHandlers = {
    onEventSelect: jest.fn(),
    onEventCreate: jest.fn(),
    onEventUpdate: jest.fn(),
    onEventDelete: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Prop Interface Validation', () => {
    it('should render with all required props', () => {
      render(
        <TimelineCalendarView
          events={mockEvents}
          onEventSelect={mockHandlers.onEventSelect}
          onEventCreate={mockHandlers.onEventCreate}
          onEventUpdate={mockHandlers.onEventUpdate}
          onEventDelete={mockHandlers.onEventDelete}
          readOnly={false}
        />
      )

      expect(screen.getByText('Timeline Calendar')).toBeInTheDocument()
      expect(screen.getByText('The Beginning')).toBeInTheDocument()
    })

    it('should work with minimal props', () => {
      render(<TimelineCalendarView events={[]} />)
      expect(screen.getByText('Timeline Calendar')).toBeInTheDocument()
    })

    it('should handle readOnly mode correctly', () => {
      render(
        <TimelineCalendarView
          events={mockEvents}
          readOnly={true}
        />
      )

      // Click on an event
      const event = screen.getByText('The Beginning')
      fireEvent.click(event)

      // In readOnly mode, edit/delete buttons should not appear
      expect(screen.queryByText('Edit Event')).not.toBeInTheDocument()
      expect(screen.queryByText('Delete Event')).not.toBeInTheDocument()
    })
  })

  describe('Event Handlers', () => {
    it('should call onEventSelect when event is clicked', async () => {
      render(
        <TimelineCalendarView
          events={mockEvents}
          onEventSelect={mockHandlers.onEventSelect}
        />
      )

      const event = screen.getByText('The Beginning')
      await userEvent.click(event)

      expect(mockHandlers.onEventSelect).toHaveBeenCalledWith(mockEvents[0])
    })

    it('should call onEventCreate when double-clicking empty date', async () => {
      render(
        <TimelineCalendarView
          events={[]}
          onEventCreate={mockHandlers.onEventCreate}
          readOnly={false}
        />
      )

      // Find a calendar day cell (simplified test)
      const calendarDays = screen.getAllByText('15')
      if (calendarDays.length > 0) {
        await userEvent.dblClick(calendarDays[0].parentElement!)
        expect(mockHandlers.onEventCreate).toHaveBeenCalled()
      }
    })

    it('should show event details dialog with update/delete buttons', async () => {
      render(
        <TimelineCalendarView
          events={mockEvents}
          onEventUpdate={mockHandlers.onEventUpdate}
          onEventDelete={mockHandlers.onEventDelete}
          readOnly={false}
        />
      )

      // Click on an event
      const event = screen.getByText('The Beginning')
      await userEvent.click(event)

      // Dialog should open with event details
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument()
        expect(screen.getByText('Edit Event')).toBeInTheDocument()
        expect(screen.getByText('Delete Event')).toBeInTheDocument()
      })

      // Test update button
      const updateButton = screen.getByText('Edit Event')
      await userEvent.click(updateButton)
      expect(mockHandlers.onEventUpdate).toHaveBeenCalledWith(mockEvents[0])

      // Re-open dialog for delete test
      await userEvent.click(event)
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument()
      })

      // Test delete button
      const deleteButton = screen.getByText('Delete Event')
      await userEvent.click(deleteButton)
      expect(mockHandlers.onEventDelete).toHaveBeenCalledWith('event-1')
    })
  })

  describe('View Modes and Filtering', () => {
    it('should switch between month and week views', async () => {
      render(<TimelineCalendarView events={mockEvents} />)

      // Default is month view
      expect(screen.getByText(/January 2024/)).toBeInTheDocument()

      // Switch to week view
      const viewSelector = screen.getByRole('combobox')
      await userEvent.click(viewSelector)
      await userEvent.click(screen.getByText('Week'))

      // Week view indicator would be visible (implementation specific)
    })

    it('should filter events by type', async () => {
      render(<TimelineCalendarView events={mockEvents} />)

      // Open filter popover
      const filterButton = screen.getByRole('button', { name: /filter/i })
      await userEvent.click(filterButton)

      // Select plot events only
      const typeSelector = screen.getAllByRole('combobox')[0]
      await userEvent.click(typeSelector)
      await userEvent.click(screen.getByText('Plot Events'))

      // Only plot events should be visible
      expect(screen.getByText('The Beginning')).toBeInTheDocument()
      expect(screen.queryByText('Meeting the Villain')).not.toBeInTheDocument()
    })

    it('should filter events by importance', async () => {
      render(<TimelineCalendarView events={mockEvents} />)

      // Open filter popover
      const filterButton = screen.getByRole('button', { name: /filter/i })
      await userEvent.click(filterButton)

      // Select critical importance only
      const importanceSelector = screen.getAllByRole('combobox')[1]
      await userEvent.click(importanceSelector)
      await userEvent.click(screen.getByText('Critical'))

      // Only critical events should be visible
      expect(screen.queryByText('The Beginning')).not.toBeInTheDocument()
      expect(screen.getByText('Meeting the Villain')).toBeInTheDocument()
    })
  })

  describe('Calendar Navigation', () => {
    it('should navigate between months', async () => {
      render(<TimelineCalendarView events={mockEvents} />)

      // Check initial month
      expect(screen.getByText(/January 2024/)).toBeInTheDocument()

      // Navigate to next month
      const nextButton = screen.getByRole('button', { name: /next/i })
      await userEvent.click(nextButton)
      expect(screen.getByText(/February 2024/)).toBeInTheDocument()

      // Navigate to previous month
      const prevButton = screen.getByRole('button', { name: /previous/i })
      await userEvent.click(prevButton)
      expect(screen.getByText(/January 2024/)).toBeInTheDocument()
    })

    it('should return to today when today button is clicked', async () => {
      render(<TimelineCalendarView events={mockEvents} />)

      // Navigate away from current month
      const nextButton = screen.getByRole('button', { name: /next/i })
      await userEvent.click(nextButton)
      await userEvent.click(nextButton)

      // Click today button
      const todayButton = screen.getByRole('button', { name: /today/i })
      await userEvent.click(todayButton)

      // Should show current month/year
      const currentDate = new Date()
      const monthYear = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
      expect(screen.getByText(new RegExp(monthYear))).toBeInTheDocument()
    })
  })

  describe('Event Type Legend', () => {
    it('should display event type legend', () => {
      render(<TimelineCalendarView events={mockEvents} />)

      expect(screen.getByText('Event Types')).toBeInTheDocument()
      expect(screen.getByText('plot')).toBeInTheDocument()
      expect(screen.getByText('character')).toBeInTheDocument()
      expect(screen.getByText('world')).toBeInTheDocument()
    })
  })
})