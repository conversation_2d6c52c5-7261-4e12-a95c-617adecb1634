# BookScribe Services Layer Documentation

## Table of Contents
- [Overview](#overview)
- [Service Architecture](#service-architecture)
- [Core Services](#core-services)
- [AI Services](#ai-services)
- [Content Services](#content-services)
- [Analytics Services](#analytics-services)
- [Collaboration Services](#collaboration-services)
- [Service Patterns](#service-patterns)

## Overview

The BookScribe services layer implements the core business logic, providing a clean separation between API routes and data access. The architecture follows Domain-Driven Design principles with over 40 specialized services.

## Service Architecture

### Service Hierarchy

```mermaid
graph TB
    subgraph "Base Layer"
        BaseService[Base Service Class]
        ServiceManager[Service Manager]
        ErrorHandler[<PERSON><PERSON><PERSON> Handler]
        Logger[Logger Service]
    end
    
    subgraph "Core Services"
        AIService[AI Service Base]
        ContentService[Content Services]
        AnalyticsService[Analytics Engine]
        CollabService[Collaboration Service]
    end
    
    subgraph "Specialized Services"
        ChapterGen[Chapter Generator]
        QualityAnalyzer[Quality Analyzer]
        VoiceManager[Voice Profile Manager]
        SearchService[Search Service]
    end
    
    BaseService --> AIService
    BaseService --> ContentService
    ServiceManager --> Core Services
    Core Services --> Specialized Services
```

### Service Manager Pattern

```typescript
// src/lib/services/service-manager.ts
export class ServiceManager {
  private static instance: ServiceManager
  private services: Map<string, BaseService> = new Map()
  
  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager()
    }
    return ServiceManager.instance
  }
  
  register<T extends BaseService>(name: string, service: T): void {
    this.services.set(name, service)
  }
  
  get<T extends BaseService>(name: string): T {
    const service = this.services.get(name)
    if (!service) {
      throw new Error(`Service ${name} not found`)
    }
    return service as T
  }
}
```

## Core Services

### Base Service Class

```typescript
// src/lib/services/base-service.ts
export abstract class BaseService {
  protected logger: Logger
  protected circuitBreaker: CircuitBreaker
  
  constructor(serviceName: string) {
    this.logger = new Logger(serviceName)
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      resetTimeout: 60000
    })
  }
  
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    retries = 3
  ): Promise<T> {
    return this.circuitBreaker.execute(async () => {
      let lastError: Error
      
      for (let i = 0; i < retries; i++) {
        try {
          return await operation()
        } catch (error) {
          lastError = error as Error
          this.logger.warn(`Retry ${i + 1}/${retries} failed`, error)
          await this.delay(Math.pow(2, i) * 1000)
        }
      }
      
      throw lastError!
    })
  }
  
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
```

### Error Handler

```typescript
// src/lib/services/error-handler.ts
export class ErrorHandler {
  static handle(error: Error, context: ErrorContext): ErrorResponse {
    // Log error with context
    logger.error('Service error', {
      error: error.message,
      stack: error.stack,
      context
    })
    
    // Classify error
    const errorType = this.classifyError(error)
    
    // Return appropriate response
    return {
      success: false,
      error: {
        code: errorType.code,
        message: this.sanitizeMessage(error.message),
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    }
  }
  
  private static classifyError(error: Error): ErrorClassification {
    if (error.name === 'ValidationError') {
      return { code: 'VALIDATION_ERROR', status: 400 }
    }
    if (error.name === 'AuthenticationError') {
      return { code: 'AUTH_ERROR', status: 401 }
    }
    if (error.name === 'RateLimitError') {
      return { code: 'RATE_LIMITED', status: 429 }
    }
    return { code: 'INTERNAL_ERROR', status: 500 }
  }
}
```

## AI Services

### AI Service Base

```typescript
// src/lib/services/ai-service-base.ts
export abstract class AIServiceBase extends BaseService {
  protected modelSelector: AIModelSelector
  protected tokenManager: TokenManager
  
  constructor(serviceName: string) {
    super(serviceName)
    this.modelSelector = new AIModelSelector()
    this.tokenManager = new TokenManager()
  }
  
  protected async generateWithAI<T>(
    prompt: string,
    options: AIGenerationOptions
  ): Promise<T> {
    // Select appropriate model
    const model = this.modelSelector.selectModel(
      options.subscription,
      options.requestedModel,
      options.taskType
    )
    
    // Check token limits
    await this.tokenManager.checkLimit(
      options.userId,
      options.estimatedTokens
    )
    
    // Generate content
    const result = await this.executeWithRetry(() =>
      this.callAI(prompt, model, options)
    )
    
    // Track usage
    await this.tokenManager.trackUsage(
      options.userId,
      result.tokensUsed
    )
    
    return result.content
  }
}
```

### Chapter Generator Service

```typescript
// src/lib/services/chapter-generator.ts
export class ChapterGeneratorService extends AIServiceBase {
  constructor() {
    super('ChapterGenerator')
  }
  
  async generateChapter(
    projectId: string,
    chapterNumber: number,
    context: BookContext
  ): Promise<GeneratedChapter> {
    // Load chapter plan
    const plan = await this.loadChapterPlan(projectId, chapterNumber)
    
    // Build generation prompt
    const prompt = this.buildChapterPrompt(plan, context)
    
    // Generate content
    const content = await this.generateWithAI<string>(prompt, {
      subscription: context.subscription,
      requestedModel: 'gpt-4',
      taskType: 'content_generation',
      userId: context.userId,
      estimatedTokens: 4000
    })
    
    // Post-process content
    const processed = await this.postProcessChapter(content, plan)
    
    // Quality check
    const quality = await this.assessQuality(processed)
    
    return {
      content: processed,
      wordCount: this.countWords(processed),
      quality,
      metadata: {
        generatedAt: new Date(),
        model: 'gpt-4',
        planId: plan.id
      }
    }
  }
  
  private buildChapterPrompt(
    plan: ChapterPlan,
    context: BookContext
  ): string {
    return `
Write Chapter ${plan.chapterNumber}: ${plan.title}

Story Context:
${JSON.stringify(context.storyStructure, null, 2)}

Character States:
${JSON.stringify(plan.characterStates, null, 2)}

Chapter Outline:
${plan.outline}

Writing Style:
- Genre: ${context.settings.primaryGenre}
- Voice: ${context.settings.narrativeVoice}
- Tone: ${context.settings.tone?.join(', ')}

Target Word Count: ${plan.targetWordCount}

Please write the complete chapter following the outline and maintaining consistency with the story context.
    `
  }
}
```

### AI Model Selector

```typescript
// src/lib/services/ai-model-selector.ts
export class AIModelSelector {
  private modelHierarchy = {
    free: ['gpt-4o-mini'],
    starter: ['gpt-4o-mini', 'gpt-4-turbo'],
    professional: ['gpt-4o-mini', 'gpt-4-turbo', 'gpt-4'],
    enterprise: ['gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'claude-3-opus']
  }
  
  selectModel(
    subscription: SubscriptionTier | null,
    requested: string,
    taskType: string
  ): ModelSelection {
    const tier = subscription?.tier || 'free'
    const allowedModels = this.modelHierarchy[tier]
    
    // Check if requested model is allowed
    if (allowedModels.includes(requested)) {
      return {
        model: requested,
        isRestricted: false,
        reason: 'Model available for tier'
      }
    }
    
    // Select best available model for task
    const selected = this.selectBestModel(allowedModels, taskType)
    
    return {
      model: selected,
      isRestricted: true,
      reason: `Model restricted to ${tier} tier`,
      tierName: tier
    }
  }
  
  private selectBestModel(models: string[], taskType: string): string {
    // Task-specific model selection logic
    const taskPreferences: Record<string, string[]> = {
      content_generation: ['gpt-4', 'gpt-4-turbo', 'gpt-4o-mini'],
      analysis: ['gpt-4-turbo', 'gpt-4', 'gpt-4o-mini'],
      quick_response: ['gpt-4o-mini', 'gpt-4-turbo', 'gpt-4']
    }
    
    const preferred = taskPreferences[taskType] || models
    
    // Return first available preferred model
    for (const model of preferred) {
      if (models.includes(model)) {
        return model
      }
    }
    
    return models[0]
  }
}
```

## Content Services

### Content Analysis Service

```typescript
// src/lib/services/content-analysis-service.ts
export class ContentAnalysisService extends BaseService {
  private qualityAnalyzer: QualityAnalyzer
  private consistencyValidator: ConsistencyValidator
  
  constructor() {
    super('ContentAnalysis')
    this.qualityAnalyzer = new QualityAnalyzer()
    this.consistencyValidator = new ConsistencyValidator()
  }
  
  async analyzeContent(
    content: string,
    context: AnalysisContext
  ): Promise<ContentAnalysis> {
    const [quality, consistency, readability] = await Promise.all([
      this.qualityAnalyzer.analyze(content),
      this.consistencyValidator.validate(content, context),
      this.analyzeReadability(content)
    ])
    
    return {
      quality,
      consistency,
      readability,
      recommendations: this.generateRecommendations(
        quality,
        consistency,
        readability
      ),
      score: this.calculateOverallScore(quality, consistency, readability)
    }
  }
  
  private analyzeReadability(content: string): ReadabilityMetrics {
    // Flesch Reading Ease
    const fleschScore = this.calculateFleschScore(content)
    
    // Gunning Fog Index
    const fogIndex = this.calculateFogIndex(content)
    
    // Average sentence length
    const avgSentenceLength = this.getAverageSentenceLength(content)
    
    return {
      fleschReadingEase: fleschScore,
      gunningFogIndex: fogIndex,
      averageSentenceLength: avgSentenceLength,
      gradeLevel: this.calculateGradeLevel(fleschScore),
      recommendation: this.getReadabilityRecommendation(fleschScore)
    }
  }
}
```

### Quality Analyzer

```typescript
// src/lib/services/quality-analyzer.ts
export class QualityAnalyzer extends BaseService {
  private dimensions = [
    'grammar',
    'spelling',
    'style',
    'clarity',
    'engagement',
    'pacing',
    'dialogue',
    'description',
    'characterization',
    'plot_progression'
  ]
  
  async analyze(content: string): Promise<QualityScore> {
    const scores = await Promise.all(
      this.dimensions.map(dim => this.analyzeDimension(content, dim))
    )
    
    const dimensionScores = Object.fromEntries(
      this.dimensions.map((dim, i) => [dim, scores[i]])
    )
    
    return {
      overall: this.calculateOverall(scores),
      dimensions: dimensionScores,
      issues: await this.detectIssues(content),
      suggestions: await this.generateSuggestions(content, dimensionScores)
    }
  }
  
  private async analyzeDimension(
    content: string,
    dimension: string
  ): Promise<number> {
    // Dimension-specific analysis logic
    switch (dimension) {
      case 'grammar':
        return this.analyzeGrammar(content)
      case 'dialogue':
        return this.analyzeDialogue(content)
      case 'pacing':
        return this.analyzePacing(content)
      default:
        return 75 // Default score
    }
  }
}
```

## Analytics Services

### Analytics Engine

```typescript
// src/lib/services/analytics-engine.ts
export class AnalyticsEngine extends BaseService {
  constructor() {
    super('Analytics')
  }
  
  async trackEvent(event: AnalyticsEvent): Promise<void> {
    try {
      // Validate event
      this.validateEvent(event)
      
      // Enrich with metadata
      const enriched = this.enrichEvent(event)
      
      // Store event
      await this.storeEvent(enriched)
      
      // Process real-time metrics
      await this.updateRealTimeMetrics(enriched)
      
      // Trigger notifications if needed
      await this.checkTriggers(enriched)
    } catch (error) {
      this.logger.error('Failed to track event', error)
    }
  }
  
  async getMetrics(
    userId: string,
    timeRange: TimeRange
  ): Promise<UserMetrics> {
    const [
      writingStats,
      productivityMetrics,
      qualityTrends,
      aiUsage
    ] = await Promise.all([
      this.getWritingStats(userId, timeRange),
      this.getProductivityMetrics(userId, timeRange),
      this.getQualityTrends(userId, timeRange),
      this.getAIUsage(userId, timeRange)
    ])
    
    return {
      writingStats,
      productivityMetrics,
      qualityTrends,
      aiUsage,
      insights: this.generateInsights({
        writingStats,
        productivityMetrics,
        qualityTrends
      })
    }
  }
}
```

### Writing Session Tracker

```typescript
// src/lib/services/writing-session-tracker.ts
export class WritingSessionTracker extends BaseService {
  private activeSessions: Map<string, WritingSession> = new Map()
  
  async startSession(
    userId: string,
    projectId: string,
    chapterId?: string
  ): Promise<string> {
    const sessionId = generateId()
    
    const session: WritingSession = {
      id: sessionId,
      userId,
      projectId,
      chapterId,
      startTime: Date.now(),
      wordsWritten: 0,
      keystrokes: 0,
      activeTime: 0,
      lastActivity: Date.now()
    }
    
    this.activeSessions.set(sessionId, session)
    
    // Start activity monitor
    this.startActivityMonitor(sessionId)
    
    return sessionId
  }
  
  async updateSession(
    sessionId: string,
    update: SessionUpdate
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId)
    if (!session) return
    
    session.wordsWritten += update.wordsAdded || 0
    session.keystrokes += update.keystrokes || 0
    session.lastActivity = Date.now()
    
    // Update active time if within threshold
    const timeSinceLastActivity = Date.now() - session.lastActivity
    if (timeSinceLastActivity < 30000) { // 30 seconds
      session.activeTime += timeSinceLastActivity
    }
  }
  
  async endSession(sessionId: string): Promise<WritingSessionStats> {
    const session = this.activeSessions.get(sessionId)
    if (!session) throw new Error('Session not found')
    
    const stats: WritingSessionStats = {
      duration: Date.now() - session.startTime,
      activeTime: session.activeTime,
      wordsWritten: session.wordsWritten,
      wordsPerMinute: this.calculateWPM(session),
      productivityScore: this.calculateProductivity(session)
    }
    
    // Save to database
    await this.saveSession(session, stats)
    
    // Clean up
    this.activeSessions.delete(sessionId)
    
    return stats
  }
}
```

## Collaboration Services

### Unified Collaboration Service

```typescript
// src/lib/services/unified-collaboration-service.ts
export class UnifiedCollaborationService extends BaseService {
  private conflictResolver: CollaborationConflictResolver
  private operationalTransform: OperationalTransform
  private activeSessions: Map<string, CollaborationSession> = new Map()
  
  constructor() {
    super('Collaboration')
    this.conflictResolver = new CollaborationConflictResolver()
    this.operationalTransform = new OperationalTransform()
  }
  
  async joinSession(
    projectId: string,
    chapterId: string,
    userId: string
  ): Promise<CollaborationSession> {
    const sessionKey = `${projectId}:${chapterId}`
    
    let session = this.activeSessions.get(sessionKey)
    if (!session) {
      session = await this.createSession(projectId, chapterId)
      this.activeSessions.set(sessionKey, session)
    }
    
    // Add user to session
    await this.addUserToSession(session, userId)
    
    // Subscribe to real-time updates
    const subscription = await this.subscribeToUpdates(session, userId)
    
    return {
      ...session,
      subscription
    }
  }
  
  async handleEdit(
    sessionId: string,
    userId: string,
    operation: Operation
  ): Promise<void> {
    const session = this.getSession(sessionId)
    
    // Transform operation against concurrent edits
    const transformed = await this.operationalTransform.transform(
      operation,
      session.pendingOperations
    )
    
    // Apply operation
    await this.applyOperation(session, transformed)
    
    // Broadcast to other users
    await this.broadcastOperation(session, userId, transformed)
    
    // Update cursor positions
    await this.updateCursorPositions(session, transformed)
  }
  
  async resolveConflict(
    sessionId: string,
    conflict: EditConflict
  ): Promise<Resolution> {
    const session = this.getSession(sessionId)
    
    // Attempt automatic resolution
    const autoResolution = await this.conflictResolver.attemptAutoResolve(
      conflict
    )
    
    if (autoResolution.success) {
      await this.applyResolution(session, autoResolution.resolution)
      return autoResolution.resolution
    }
    
    // Manual resolution required
    return this.requestManualResolution(session, conflict)
  }
}
```

### Operational Transform

```typescript
// src/lib/services/operational-transform.ts
export class OperationalTransform {
  transform(
    operation: Operation,
    against: Operation[]
  ): Operation {
    let transformed = operation
    
    for (const other of against) {
      transformed = this.transformPair(transformed, other)
    }
    
    return transformed
  }
  
  private transformPair(op1: Operation, op2: Operation): Operation {
    // Insert vs Insert
    if (op1.type === 'insert' && op2.type === 'insert') {
      if (op1.position < op2.position) {
        return op1
      } else if (op1.position > op2.position) {
        return {
          ...op1,
          position: op1.position + op2.text.length
        }
      } else {
        // Same position - use user ID for consistency
        return op1.userId < op2.userId ? op1 : {
          ...op1,
          position: op1.position + op2.text.length
        }
      }
    }
    
    // Delete vs Insert
    if (op1.type === 'delete' && op2.type === 'insert') {
      if (op1.position < op2.position) {
        return op1
      } else {
        return {
          ...op1,
          position: op1.position + op2.text.length
        }
      }
    }
    
    // More transformation rules...
    return op1
  }
}
```

## Service Patterns

### Circuit Breaker Pattern

```typescript
// src/lib/services/circuit-breaker.ts
export class CircuitBreaker {
  private failures = 0
  private lastFailureTime?: number
  private state: 'closed' | 'open' | 'half-open' = 'closed'
  
  constructor(
    private options: {
      failureThreshold: number
      resetTimeout: number
      halfOpenRetries: number
    }
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (this.shouldAttemptReset()) {
        this.state = 'half-open'
      } else {
        throw new Error('Circuit breaker is open')
      }
    }
    
    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }
  
  private onSuccess(): void {
    this.failures = 0
    this.state = 'closed'
  }
  
  private onFailure(): void {
    this.failures++
    this.lastFailureTime = Date.now()
    
    if (this.failures >= this.options.failureThreshold) {
      this.state = 'open'
    }
  }
  
  private shouldAttemptReset(): boolean {
    return (
      this.lastFailureTime !== undefined &&
      Date.now() - this.lastFailureTime >= this.options.resetTimeout
    )
  }
}
```

### Streaming Service Pattern

```typescript
// src/lib/services/streaming-service.ts
export class StreamingService extends BaseService {
  async *streamContent(
    generator: AsyncGenerator<string>,
    options: StreamOptions
  ): AsyncGenerator<StreamChunk> {
    let buffer = ''
    let chunkCount = 0
    
    try {
      for await (const content of generator) {
        buffer += content
        
        // Yield complete sentences
        const sentences = this.extractCompleteSentences(buffer)
        buffer = sentences.remainder
        
        for (const sentence of sentences.complete) {
          yield {
            type: 'content',
            data: sentence,
            chunkNumber: ++chunkCount
          }
        }
      }
      
      // Yield any remaining content
      if (buffer) {
        yield {
          type: 'content',
          data: buffer,
          chunkNumber: ++chunkCount
        }
      }
      
      // Final chunk with metadata
      yield {
        type: 'complete',
        metadata: {
          totalChunks: chunkCount,
          duration: Date.now() - options.startTime
        }
      }
    } catch (error) {
      yield {
        type: 'error',
        error: error.message
      }
    }
  }
}
```

## Best Practices

### Service Design Principles

1. **Single Responsibility**: Each service handles one domain
2. **Dependency Injection**: Services receive dependencies via constructor
3. **Error Resilience**: All services implement retry and circuit breaking
4. **Observability**: Comprehensive logging and metrics
5. **Testability**: Services are easily mockable and testable

### Performance Optimization

1. **Caching**: Use embedding cache for repeated operations
2. **Batching**: Batch database operations where possible
3. **Async Operations**: Use Promise.all for parallel operations
4. **Connection Pooling**: Reuse database connections
5. **Lazy Loading**: Load data only when needed

### Error Handling

1. **Graceful Degradation**: Fallback to simpler operations
2. **User-Friendly Messages**: Sanitize technical errors
3. **Context Preservation**: Include relevant context in errors
4. **Recovery Strategies**: Implement retry with backoff
5. **Monitoring**: Alert on error patterns