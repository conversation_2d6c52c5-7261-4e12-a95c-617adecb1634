'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  FileText, 
  Users, 
  MapPin, 
  BookOpen, 
  Filter,
  X,
  Calendar,
  Hash,
  Loader2
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/contexts/auth-context'
import { searchService, type SearchResult } from '@/lib/services/search-service'
import { toast } from '@/hooks/use-toast'
import { debounce } from '@/lib/utils'

const searchFilters = [
  { id: 'all', label: 'All', icon: Search },
  { id: 'project', label: 'Projects', icon: BookOpen },
  { id: 'character', label: 'Characters', icon: Users },
  { id: 'location', label: 'Locations', icon: MapPin },
  { id: 'chapter', label: 'Chapters', icon: FileText }
]

export default function SearchPage() {
  const { user } = useAuth()
  const [query, setQuery] = useState('')
  const [activeFilter, setActiveFilter] = useState('all')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [hasSearched, setHasSearched] = useState(false)

  // Debounced search function
  const performSearch = useCallback(
    debounce(async (searchQuery: string, filter: string) => {
      if (!user?.id) return

      setIsSearching(true)
      setHasSearched(true)

      try {
        const results = await searchService.search({
          query: searchQuery,
          types: filter === 'all' ? ['all'] : [filter],
          userId: user.id,
          limit: 50
        })
        setSearchResults(results)
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'Search failed',
          description: 'Unable to perform search. Please try again.'
        })
        setSearchResults([])
      } finally {
        setIsSearching(false)
      }
    }, 300),
    [user]
  )

  // Auto-search as user types
  useEffect(() => {
    if (query.trim()) {
      performSearch(query, activeFilter)
    } else {
      setSearchResults([])
      setHasSearched(false)
    }
  }, [query, activeFilter, performSearch])

  const handleSearch = () => {
    if (query.trim()) {
      performSearch(query, activeFilter)
    }
  }

  const clearSearch = () => {
    setQuery('')
    setActiveFilter('all')
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'project': return BookOpen
      case 'character': return Users
      case 'location': return MapPin
      case 'chapter': return FileText
      default: return FileText
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Search</h1>
        <p className="text-muted-foreground">
          Search across your projects, characters, locations, and chapters
        </p>
      </div>

      {/* Search Bar */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search your writing projects..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
              {query && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="absolute right-1 top-1 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <Button onClick={handleSearch} disabled={isSearching}>
              {isSearching ? 'Searching...' : 'Search'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <div className="mb-6">
        <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-2">
          {searchFilters.map((filter) => {
            const Icon = filter.icon
            return (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveFilter(filter.id)}
                className="flex items-center gap-2 flex-shrink-0"
              >
                <Icon className="h-4 w-4" />
                {filter.label}
              </Button>
            )
          })}
          {(query || activeFilter !== 'all') && (
            <Button variant="ghost" size="sm" onClick={clearSearch} className="flex-shrink-0">
              <Filter className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {/* Results */}
      <div className="space-y-4">
        {hasSearched && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {isSearching ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Searching...
                </span>
              ) : (
                `${searchResults.length} result${searchResults.length !== 1 ? 's' : ''} for "${query}"`
              )}
            </p>
          </div>
        )}

        {searchResults.length === 0 && hasSearched && !isSearching ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No results found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search terms or filters
              </p>
              <Button variant="outline" onClick={clearSearch}>
                Clear Search
              </Button>
            </CardContent>
          </Card>
        ) : (
          searchResults.map((result) => {
            const Icon = getResultIcon(result.type)
            return (
              <Card key={result.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon className="h-4 w-4 text-muted-foreground" />
                        <Badge variant="secondary" className="text-xs">
                          {result.category}
                        </Badge>
                        {result.project && result.type !== 'project' && (
                          <span className="text-xs text-muted-foreground">
                            in {result.project}
                          </span>
                        )}
                      </div>
                      
                      <Link href={result.url} className="block group">
                        <h3 className="text-lg font-semibold group-hover:text-primary transition-colors mb-2">
                          {result.title}
                        </h3>
                        <p className="text-muted-foreground text-sm mb-3">
                          {result.description}
                        </p>
                      </Link>

                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        {result.lastModified && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(result.lastModified)}
                          </div>
                        )}
                        {result.wordCount && (
                          <div className="flex items-center gap-1">
                            <Hash className="h-3 w-3" />
                            {result.wordCount.toLocaleString()} words
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}

        {!hasSearched && searchResults.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Start Searching</h3>
              <p className="text-muted-foreground mb-4">
                Enter a search term to find projects, characters, locations, and chapters
              </p>
              <div className="flex flex-wrap gap-2 justify-center">
                <Badge variant="outline">Try "dragon"</Badge>
                <Badge variant="outline">Try "magic"</Badge>
                <Badge variant="outline">Try "chapter"</Badge>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}