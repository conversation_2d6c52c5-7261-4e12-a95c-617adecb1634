/**
 * API Routes Configuration
 * Centralized location for all API route paths used across the application
 */

export const API_ROUTES = {
  // Authentication
  AUTH: {
    SIGN_IN: '/api/auth/signin',
    SIGN_UP: '/api/auth/signup',
    SIGN_OUT: '/api/auth/signout',
    SESSION: '/api/auth/session',
    RESET_PASSWORD: '/api/auth/reset-password',
  },
  
  // Projects
  PROJECTS: {
    BASE: '/api/projects',
    BY_ID: (id: string) => `/api/projects/${id}`,
    INVITE: '/api/projects/invite',
    COLLABORATORS: (id: string) => `/api/projects/${id}/collaborators`,
    COLLABORATORS_INVITE: (id: string) => `/api/projects/${id}/collaborators/invite`,
    EXPORT: (id: string) => `/api/projects/${id}/export`,
    IMPORT: '/api/projects/import',
  },
  
  // Chapters
  CHAPTERS: {
    BASE: '/api/chapters',
    BY_ID: (id: string) => `/api/chapters/${id}`,
    GENERATE: '/api/chapters/generate',
    BATCH: '/api/chapters/batch',
  },
  
  // Story Bible
  STORY_BIBLE: {
    BASE: '/api/story-bible',
    BY_ID: (id: string) => `/api/story-bible/${id}`,
    UPDATE: '/api/story-bible/update',
    GENERATE: '/api/story-bible/generate',
  },
  
  // AI & Agents
  AI: {
    CHAT: '/api/ai/chat',
    STREAM_CONTENT: '/api/ai/stream-content',
    STRUCTURED_CONTENT: '/api/ai/structured-content',
    TYPED_STREAM: '/api/ai/typed-stream',
  },
  
  AGENTS: {
    GENERATE: '/api/agents/generate',
    EDIT: '/api/agents/edit',
    ADJUST_PLAN: '/api/agents/adjust-plan',
    INITIALIZE: '/api/agents/initialize',
  },
  
  // Analytics
  ANALYTICS: {
    BASE: '/api/analytics',
    EXPORT: '/api/analytics/export',
    CHAPTERS: '/api/analytics/chapters',
    PROFILES: {
      PERFORMANCE: '/api/analytics/profiles/performance',
    },
    SELECTIONS: {
      SUCCESS_PATTERNS: '/api/analytics/selections/success-patterns',
    },
  },
  
  // Billing
  BILLING: {
    CREATE_CHECKOUT: '/api/billing/create-checkout-session',
    CREATE_PORTAL: '/api/billing/create-portal-session',
    WEBHOOKS: {
      STRIPE: '/api/billing/webhooks/stripe',
    },
    PAYMENTS: {
      CHARGE: '/api/billing/payments/charge',
    },
  },
  
  // Content
  CONTENT: {
    ANALYSIS: '/api/content-analysis',
    GENERATE: '/api/content/generate',
    SUGGESTIONS: '/api/content/suggestions',
  },
  
  // References
  REFERENCES: {
    BY_ID: {
      SUMMARIZE: (id: string) => `/api/references/${id}/summarize`,
    },
  },
  
  // Orchestration
  ORCHESTRATION: {
    START: '/api/orchestration/start',
    STATUS: '/api/orchestration/status',
  },
  
  // Email
  EMAIL: {
    SEND: '/api/email/send',
    QUEUE: {
      PROCESS: '/api/email/queue/process',
    },
  },
  
  // Admin
  ADMIN: {
    EXPORT_DATA: '/api/admin/export-data',
    ANALYTICS: '/api/admin/analytics',
  },
  
  // Sample Projects
  SAMPLE_PROJECTS: '/api/sample-projects',
  
  // Import/Export
  IMPORT: {
    EPUB: '/api/import/epub',
  },
  
  // Webhooks
  WEBHOOKS: {
    STRIPE: '/api/webhooks/stripe',
  },
  
  // Processing Tasks
  PROCESSING_TASKS: {
    BY_ID: (id: string) => `/api/processing-tasks/${id}`,
  },
  
  // Project Collaborators
  PROJECT_COLLABORATORS: '/api/project-collaborators',
} as const;

// Helper to get full API URL
export function getApiUrl(route: string): string {
  // In production, this could include the full domain
  return route;
}

// Type for API route paths
export type ApiRoutePath = 
  | typeof API_ROUTES[keyof typeof API_ROUTES]
  | string; // For dynamic routes

// HTTP Headers Configuration
export const HTTP_HEADERS = {
  CONTENT_TYPE: {
    JSON: 'application/json',
    FORM_DATA: 'multipart/form-data',
    TEXT: 'text/plain',
    HTML: 'text/html',
  },
  ACCEPT: {
    JSON: 'application/json',
    ALL: '*/*',
  },
  AUTHORIZATION: (token: string) => `Bearer ${token}`,
} as const;

// Common request configurations
export const API_REQUEST_CONFIG = {
  DEFAULT_HEADERS: {
    'Content-Type': HTTP_HEADERS.CONTENT_TYPE.JSON,
    'Accept': HTTP_HEADERS.ACCEPT.JSON,
  },
  TIMEOUT: 30000, // 30 seconds
} as const;