import { NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { createTypedServerClient } from '@/lib/supabase'
import { embeddingService } from '@/lib/services/embedding-service'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { applyRateLimit } from '@/lib/rate-limiter-unified'

const semanticSearchSchema = z.object({
  query: z.string().min(1).max(500),
  projectId: z.string().uuid(),
  limit: z.number().int().min(1).max(50).optional().default(10),
  threshold: z.number().min(0).max(1).optional().default(0.7),
  contentTypes: z.array(z.string()).optional()
})

export const POST = UnifiedAuthService.withAuth(async (request) => {
  try {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'search', cost: 2 })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const body = await request.json()
    const validationResult = semanticSearchSchema.safeParse(body)
    
    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid search parameters',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400)
    }
    
    const { query, projectId, limit, threshold, contentTypes } = validationResult.data
    
    // Verify user has access to project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()
    
    if (!projectAccess) {
      const { data: collaboratorAccess } = await supabase
        .from('project_collaborators')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .single()
      
      if (!collaboratorAccess) {
        return UnifiedResponse.error({
          message: 'You do not have access to this project',
          code: 'FORBIDDEN'
        }, undefined, 403)
      }
    }
    
    // Perform semantic search
    const similarContent = await embeddingService.searchSimilar(
      projectId,
      query,
      limit,
      threshold
    )
    
    if (similarContent.length === 0) {
      return UnifiedResponse.success({
        results: [],
        message: 'No similar content found'
      })
    }
    
    // Fetch full content details for the similar items
    const contentIds = similarContent.map(item => item.contentId)
    const { data: contentDetails, error } = await supabase
      .from('content_index')
      .select('*')
      .in('id', contentIds)
      .in('content_type', contentTypes || ['chapter', 'character', 'location', 'story_bible', 'note'])
    
    if (error) {
      logger.error('Failed to fetch content details:', error)
      return UnifiedResponse.error({
        message: 'Failed to fetch content details',
        code: 'DATABASE_ERROR',
        details: error
      }, undefined, 500)
    }
    
    // Combine similarity scores with content details
    const results = similarContent
      .map(item => {
        const content = contentDetails?.find(c => c.id === item.contentId)
        if (!content) return null
        
        return {
          id: content.content_id,
          type: content.content_type,
          title: content.title,
          content: content.content,
          excerpt: content.content.slice(0, 200) + '...',
          similarity: item.similarity,
          metadata: {
            ...content.metadata,
            lastModified: content.updated_at
          }
        }
      })
      .filter(Boolean)
    
    // Track search analytics
    try {
      await supabase.from('search_analytics').insert({
        project_id: projectId,
        user_id: user.id,
        query: query.toLowerCase().trim(),
        result_count: results.length,
        timestamp: new Date()
      })
    } catch (error) {
      // Don't fail the search if analytics tracking fails
      logger.error('Failed to track search analytics:', error)
    }
    
    return UnifiedResponse.success({
      results,
      total: results.length,
      query,
      threshold
    })
  } catch (error) {
    logger.error('Error in semantic search:', error)
    return UnifiedResponse.error({
      message: 'Failed to perform semantic search',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500)
  }
})

// GET - Generate embeddings for a project
const embedProjectSchema = z.object({
  projectId: z.string().uuid()
})

export const GET = UnifiedAuthService.withAuth(async (request) => {
  try {
    // Apply rate limiting - embedding is expensive
    const rateLimitResponse = await applyRateLimit(request, { type: 'write', cost: 10 })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const user = request.user!
    const supabase = await createTypedServerClient()
    
    const searchParams = Object.fromEntries(request.nextUrl.searchParams)
    const validationResult = embedProjectSchema.safeParse(searchParams)
    
    if (!validationResult.success) {
      return UnifiedResponse.error({
        message: 'Invalid parameters',
        code: 'VALIDATION_ERROR',
        details: validationResult.error.errors
      }, undefined, 400)
    }
    
    const { projectId } = validationResult.data
    
    // Verify user owns the project (only owners can trigger embedding)
    const { data: project } = await supabase
      .from('projects')
      .select('id, user_id')
      .eq('id', projectId)
      .single()
    
    if (!project || project.user_id !== user.id) {
      return UnifiedResponse.error({
        message: 'Only project owners can generate embeddings',
        code: 'FORBIDDEN'
      }, undefined, 403)
    }
    
    // Start embedding generation (async)
    embeddingService.embedProject(projectId).catch(error => {
      logger.error('Background embedding generation failed:', error)
    })
    
    return UnifiedResponse.success({
      message: 'Embedding generation started',
      projectId
    })
  } catch (error) {
    logger.error('Error starting embedding generation:', error)
    return UnifiedResponse.error({
      message: 'Failed to start embedding generation',
      code: 'INTERNAL_ERROR',
      details: error
    }, undefined, 500)
  }
})