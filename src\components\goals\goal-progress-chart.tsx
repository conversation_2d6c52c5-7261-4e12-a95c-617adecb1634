'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Line, LineChart } from 'recharts'
import { TrendingUp, TrendingDown } from 'lucide-react'

interface ProgressData {
  date: string
  words: number
  goal: number
}

export function GoalProgressChart() {
  const [timeRange, setTimeRange] = useState('7days')
  const [progressData, setProgressData] = useState<ProgressData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchProgressData()
  }, [timeRange])

  const fetchProgressData = async () => {
    try {
      const response = await fetch(`/api/writing/goals/progress?range=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setProgressData(data.progress || [])
      }
    } catch (error) {
      console.error('Error fetching progress data:', error)
    } finally {
      setLoading(false)
    }
  }

  const totalWords = progressData.reduce((sum, day) => sum + day.words, 0)
  const averageWords = progressData.length > 0 ? Math.round(totalWords / progressData.length) : 0
  const daysMetGoal = progressData.filter(day => day.words >= day.goal).length
  const successRate = progressData.length > 0 ? Math.round((daysMetGoal / progressData.length) * 100) : 0

  // Calculate trend
  const recentDays = progressData.slice(-3)
  const olderDays = progressData.slice(-6, -3)
  const recentAvg = recentDays.reduce((sum, day) => sum + day.words, 0) / (recentDays.length || 1)
  const olderAvg = olderDays.reduce((sum, day) => sum + day.words, 0) / (olderDays.length || 1)
  const trend = recentAvg > olderAvg ? 'up' : 'down'

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Writing Progress</CardTitle>
            <CardDescription>
              Track your daily writing performance
            </CardDescription>
          </div>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="14days">Last 14 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Stats */}
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold">{totalWords.toLocaleString()}</p>
              <p className="text-xs text-muted-foreground">Total Words</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{averageWords.toLocaleString()}</p>
              <p className="text-xs text-muted-foreground">Daily Average</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{successRate}%</p>
              <p className="text-xs text-muted-foreground">Success Rate</p>
            </div>
            <div>
              <p className="text-2xl font-bold flex items-center justify-center">
                {trend === 'up' ? (
                  <TrendingUp className="h-6 w-6 text-green-500" />
                ) : (
                  <TrendingDown className="h-6 w-6 text-red-500" />
                )}
              </p>
              <p className="text-xs text-muted-foreground">Trend</p>
            </div>
          </div>

          {/* Chart */}
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={progressData}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="date" 
                  className="text-xs"
                  tick={{ fill: 'currentColor' }}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fill: 'currentColor' }}
                />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px'
                  }}
                />
                <Bar 
                  dataKey="words" 
                  fill="hsl(var(--primary))" 
                  radius={[4, 4, 0, 0]}
                />
                <Line 
                  type="monotone" 
                  dataKey="goal" 
                  stroke="hsl(var(--muted-foreground))" 
                  strokeDasharray="5 5"
                  dot={false}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}