/**
 * Authentication Middleware
 * Centralized authentication and authorization logic for API routes
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { AuthenticationError, AuthorizationError } from '@/lib/api/error-handler'
import { isPublicEndpoint } from '@/lib/config/api-endpoints'
import { logger } from '@/lib/services/logger'
import type { User } from '@supabase/supabase-js'

// User with additional app-specific properties
export interface AuthenticatedUser extends User {
  subscriptionTier?: 'free' | 'pro' | 'enterprise'
  isAdmin?: boolean
}

// Authentication result
export interface AuthResult {
  authenticated: boolean
  user?: AuthenticatedUser
  error?: string
}

/**
 * Authenticate a request and return user information
 */
export async function authenticateRequest(
  request: NextRequest
): Promise<AuthResult> {
  try {
    const supabase = createClient()
    
    // Check for bypass in development
    if (process.env.NODE_ENV === 'development' && 
        process.env.NEXT_PUBLIC_DEV_BYPASS_AUTH === 'true') {
      logger.warn('Auth bypass enabled in development')
      return {
        authenticated: true,
        user: {
          id: 'dev-user',
          email: '<EMAIL>',
          subscriptionTier: 'enterprise',
          isAdmin: true
        } as AuthenticatedUser
      }
    }
    
    // Get session from Supabase
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      logger.error('Error getting session:', error)
      return {
        authenticated: false,
        error: 'Failed to authenticate'
      }
    }
    
    if (!session?.user) {
      return {
        authenticated: false,
        error: 'No active session'
      }
    }
    
    // Get additional user data
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier, is_admin')
      .eq('id', session.user.id)
      .single()
    
    const user: AuthenticatedUser = {
      ...session.user,
      subscriptionTier: profile?.subscription_tier || 'free',
      isAdmin: profile?.is_admin || false
    }
    
    return {
      authenticated: true,
      user
    }
  } catch (error) {
    logger.error('Authentication error:', error)
    return {
      authenticated: false,
      error: 'Authentication failed'
    }
  }
}

/**
 * Require authentication for a request
 * Throws AuthenticationError if not authenticated
 */
export async function requireAuth(
  request: NextRequest
): Promise<AuthenticatedUser> {
  const { authenticated, user, error } = await authenticateRequest(request)
  
  if (!authenticated || !user) {
    throw new AuthenticationError(error || 'Authentication required')
  }
  
  return user
}

/**
 * Require specific subscription tier
 * Throws AuthorizationError if user doesn't have required tier
 */
export async function requireSubscriptionTier(
  request: NextRequest,
  requiredTiers: Array<'free' | 'pro' | 'enterprise'>
): Promise<AuthenticatedUser> {
  const user = await requireAuth(request)
  
  if (!user.subscriptionTier || !requiredTiers.includes(user.subscriptionTier)) {
    throw new AuthorizationError(
      `This feature requires ${requiredTiers.join(' or ')} subscription`
    )
  }
  
  return user
}

/**
 * Require admin privileges
 * Throws AuthorizationError if user is not admin
 */
export async function requireAdmin(
  request: NextRequest
): Promise<AuthenticatedUser> {
  const user = await requireAuth(request)
  
  if (!user.isAdmin) {
    throw new AuthorizationError('Admin access required')
  }
  
  return user
}

/**
 * Check if user owns a resource
 */
export async function requireResourceOwnership(
  request: NextRequest,
  resourceOwnerId: string
): Promise<AuthenticatedUser> {
  const user = await requireAuth(request)
  
  if (user.id !== resourceOwnerId && !user.isAdmin) {
    throw new AuthorizationError('You do not have access to this resource')
  }
  
  return user
}

/**
 * Check if user has access to a project
 */
export async function requireProjectAccess(
  request: NextRequest,
  projectId: string,
  requiredRole?: 'viewer' | 'commenter' | 'editor' | 'admin'
): Promise<AuthenticatedUser> {
  const user = await requireAuth(request)
  const supabase = createClient()
  
  // Check if user owns the project
  const { data: project } = await supabase
    .from('projects')
    .select('user_id')
    .eq('id', projectId)
    .single()
  
  if (project?.user_id === user.id || user.isAdmin) {
    return user
  }
  
  // Check if user is a collaborator
  const { data: collaborator } = await supabase
    .from('project_collaborators')
    .select('role')
    .eq('project_id', projectId)
    .eq('user_id', user.id)
    .single()
  
  if (!collaborator) {
    throw new AuthorizationError('You do not have access to this project')
  }
  
  // Check role if specified
  if (requiredRole) {
    const roleHierarchy = ['viewer', 'commenter', 'editor', 'admin']
    const userRoleIndex = roleHierarchy.indexOf(collaborator.role)
    const requiredRoleIndex = roleHierarchy.indexOf(requiredRole)
    
    if (userRoleIndex < requiredRoleIndex) {
      throw new AuthorizationError(
        `${requiredRole} access required for this action`
      )
    }
  }
  
  return user
}

/**
 * Middleware function for Next.js middleware.ts
 */
export async function authMiddleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  
  // Skip authentication for public endpoints
  if (isPublicEndpoint(pathname)) {
    return NextResponse.next()
  }
  
  // Authenticate request
  const { authenticated } = await authenticateRequest(request)
  
  if (!authenticated) {
    // Redirect to login for web pages
    if (!pathname.startsWith('/api/')) {
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }
    
    // Return 401 for API routes
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  }
  
  return NextResponse.next()
}

/**
 * Helper to extract user from request in API routes
 */
export async function getUserFromRequest(
  request: Request
): Promise<AuthenticatedUser | null> {
  const { authenticated, user } = await authenticateRequest(
    request as NextRequest
  )
  
  return authenticated ? user || null : null
}