import { createClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import { ProjectsWithSeries } from '@/components/projects/projects-with-series'

export default async function ProjectsPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Paper texture background */}
      <div className="fixed inset-0 paper-texture opacity-30" />
      
      <div className="relative z-10 py-4 sm:py-6 lg:py-6 sm:py-8 lg:py-10">
        <div className="container-wide">
          <ProjectsWithSeries />
        </div>
      </div>
    </div>
  )
}