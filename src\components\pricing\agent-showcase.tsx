'use client'

import { Brain, Users, BookOpen, Edit3, Layout, Lightbulb } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

const agents = [
  {
    name: 'Story Architect',
    icon: Brain,
    description: 'Creates comprehensive plot structures and story arcs',
    tier: 'starter',
    fullPotentialTier: 'writer',
    intelligenceNote: 'Enhanced intelligence unlocks deeper narrative structures'
  },
  {
    name: 'Writing Agent',
    icon: BookOpen,
    description: 'Generates engaging chapter content with your style',
    tier: 'starter',
    fullPotentialTier: 'author',
    intelligenceNote: 'Premium intelligence creates more nuanced prose'
  },
  {
    name: 'Character Developer',
    icon: Users,
    description: 'Builds deep, consistent character profiles and relationships',
    tier: 'writer',
    fullPotentialTier: 'author',
    intelligenceNote: 'Advanced intelligence develops complex character psychology'
  },
  {
    name: 'Chapter Planner',
    icon: Layout,
    description: 'Organizes scenes and maintains perfect pacing',
    tier: 'writer',
    fullPotentialTier: 'professional',
    intelligenceNote: 'Higher intelligence manages intricate plot threads'
  },
  {
    name: 'Adaptive Planning',
    icon: Lightbulb,
    description: 'Adjusts story elements based on your changes',
    tier: 'writer',
    fullPotentialTier: 'professional',
    intelligenceNote: 'Ultimate intelligence adapts to any narrative complexity'
  },
  {
    name: 'Editor Agent',
    icon: Edit3,
    description: 'Reviews for consistency, quality, and improvements',
    tier: 'author',
    fullPotentialTier: 'studio',
    intelligenceNote: 'Maximum intelligence catches subtle inconsistencies'
  }
]

const tierColors = {
  starter: 'bg-success-light text-success border-green-300 dark:bg-green-900/20 dark:text-green-400 dark:border-green-700',
  writer: 'bg-primary/10 text-primary border-primary/30',
  author: 'bg-purple-100 text-purple-700 border-purple-300 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-700'
}

const tierLabels = {
  starter: 'Free',
  writer: 'Writer+',
  author: 'Author+'
}

export function AgentShowcase() {
  return (
    <div className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold font-literary-display mb-4">
          Your AI Writing Team
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto">
          Six specialized AI agents work together to help you craft your masterpiece.
          Plus our Dynamic Memory System maintains consistency across 100k+ words.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 xl:grid-cols-4 gap-6 max-w-7xl xl:max-w-[1600px] 2xl:max-w-[1920px] mx-auto">
        {agents.map((agent) => {
          const Icon = agent.icon
          return (
            <div
              key={agent.name}
              className="relative p-6 rounded-lg border bg-card hover:shadow-md transition-all"
            >
              <Badge 
                className={`absolute top-4 right-4 text-xs font-mono ${tierColors[agent.tier]}`}
                variant="outline"
              >
                {tierLabels[agent.tier]}
              </Badge>
              
              <div className="flex flex-col items-center text-center space-y-3">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Icon className="w-6 h-6 text-primary" />
                </div>
                
                <h3 className="font-semibold text-lg">{agent.name}</h3>
                
                <p className="text-sm text-muted-foreground">
                  {agent.description}
                </p>
                
                {agent.fullPotentialTier && (
                  <div className="pt-2 mt-2 border-t w-full">
                    <p className="text-xs text-primary/80 italic">
                      {agent.intelligenceNote}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )
        })}
        
        {/* Bonus card for Studio */}
        <div className="relative p-6 rounded-lg border bg-gradient-to-br from-primary/5 to-primary/10 border-primary/30">
          <Badge 
            className="absolute top-4 right-4 text-xs font-mono bg-primary text-primary-foreground"
          >
            Studio
          </Badge>
          
          <div className="flex flex-col items-center text-center space-y-3">
            <div className="w-12 h-12 rounded-full bg-primary flex items-center justify-center">
              <Brain className="w-6 h-6 text-primary-foreground" />
            </div>
            
            <h3 className="font-semibold text-lg">Priority AI Access</h3>
            
            <p className="text-sm text-muted-foreground">
              Fastest processing & unrestricted GPT-4.1 access
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}