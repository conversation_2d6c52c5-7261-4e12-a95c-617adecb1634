import { z } from 'zod'
import {
  GENRES,
  WRITING_OPTIONS,
  STRUCTURE_OPTIONS,
  CHARACTER_OPTIONS,
  SETTING_OPTIONS,
  THEME_OPTIONS,
  CONTENT_OPTIONS,
  SCOPE_OPTIONS,
  TECHNICAL_OPTIONS,
  RESEARCH_OPTIONS,
} from '@/lib/types/project-config'
import { TIME_MS } from '@/lib/constants'

// Helper schemas for arrays and enums
const genreSchema = z.enum(GENRES.primary as [string, ...string[]])
const narrativeVoiceSchema = z.enum(WRITING_OPTIONS.narrativeVoice as [string, ...string[]])
const tenseSchema = z.enum(WRITING_OPTIONS.tense as [string, ...string[]])
const writingStyleSchema = z.enum(WRITING_OPTIONS.writingStyle as [string, ...string[]])
const toneOptionsSchema = z.array(z.enum(WRITING_OPTIONS.toneOptions as [string, ...string[]]))
const targetAudienceSchema = z.enum(CONTENT_OPTIONS.targetAudiences as [string, ...string[]])
const contentRatingSchema = z.enum(CONTENT_OPTIONS.contentRatings as [string, ...string[]])
const projectScopeSchema = z.enum(SCOPE_OPTIONS.projectScopes as [string, ...string[]])

// Project Basics Step Schema
export const projectBasicsSchema = z.object({
  name: z
    .string()
    .min(1, 'Project name is required')
    .max(255, 'Project name must be less than 255 characters')
    .trim(),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(TIME_MS.SECOND, 'Description must be less than TIME_MS.SECOND characters')
    .trim(),
  initialConcept: z
    .string()
    .max(2000, 'Initial concept must be less than 2000 characters')
    .optional(),
  targetAudience: targetAudienceSchema,
  contentRating: contentRatingSchema,
  projectScope: projectScopeSchema,
  seriesType: z
    .enum(SCOPE_OPTIONS.seriesTypes as [string, ...string[]])
    .optional(),
  interconnectionLevel: z
    .enum(SCOPE_OPTIONS.interconnectionLevels as [string, ...string[]])
    .optional(),
  customScopeDescription: z
    .string()
    .max(TIME_MS.SECOND, 'Custom scope description must be less than TIME_MS.SECOND characters')
    .optional(),
  contentWarnings: z
    .array(z.enum(CONTENT_OPTIONS.contentWarnings as [string, ...string[]]))
    .optional(),
  culturalSensitivityNotes: z
    .string()
    .max(TIME_MS.SECOND, 'Cultural sensitivity notes must be less than TIME_MS.SECOND characters')
    .optional(),
})

// Genre & Style Step Schema
export const genreStyleSchema = z.object({
  primaryGenre: genreSchema,
  subgenre: z.string().optional(),
  customGenre: z
    .string()
    .max(500, 'Custom genre description must be less than 500 characters')
    .optional(),
  narrativeVoice: narrativeVoiceSchema,
  tense: tenseSchema,
  writingStyle: writingStyleSchema,
  toneOptions: toneOptionsSchema.min(1, 'Please select at least one tone'),
  customStyleDescription: z
    .string()
    .max(TIME_MS.SECOND, 'Custom style description must be less than TIME_MS.SECOND characters')
    .optional(),
})

// Story Structure Step Schema
export const structureSchema = z.object({
  structureType: z.enum(STRUCTURE_OPTIONS.structureTypes as [string, ...string[]]),
  pacingPreference: z.enum(STRUCTURE_OPTIONS.pacingPreferences as [string, ...string[]]),
  chapterStructure: z.enum(STRUCTURE_OPTIONS.chapterStructures as [string, ...string[]]),
  timelineComplexity: z.enum(STRUCTURE_OPTIONS.timelineComplexity as [string, ...string[]]),
  customStructureNotes: z
    .string()
    .max(TIME_MS.SECOND, 'Custom structure notes must be less than TIME_MS.SECOND characters')
    .optional(),
})

// Character & World Step Schema
export const characterWorldSchema = z.object({
  protagonistTypes: z
    .array(z.enum(CHARACTER_OPTIONS.protagonistTypes as [string, ...string[]]))
    .min(1, 'Please select at least one protagonist type'),
  antagonistTypes: z
    .array(z.enum(CHARACTER_OPTIONS.antagonistTypes as [string, ...string[]]))
    .min(1, 'Please select at least one antagonist type'),
  characterComplexity: z.enum(CHARACTER_OPTIONS.characterComplexity as [string, ...string[]]),
  characterArcTypes: z
    .array(z.enum(CHARACTER_OPTIONS.characterArcTypes as [string, ...string[]]))
    .min(1, 'Please select at least one character arc type'),
  customCharacterConcepts: z
    .string()
    .max(TIME_MS.SECOND, 'Custom character concepts must be less than TIME_MS.SECOND characters')
    .optional(),
  timePeriod: z.enum(SETTING_OPTIONS.timePeriods as [string, ...string[]]),
  geographicSetting: z.enum(SETTING_OPTIONS.geographicSettings as [string, ...string[]]),
  worldType: z.enum(SETTING_OPTIONS.worldTypes as [string, ...string[]]),
  magicTechLevel: z.enum(SETTING_OPTIONS.magicTechLevels as [string, ...string[]]),
  customSettingDescription: z
    .string()
    .max(TIME_MS.SECOND, 'Custom setting description must be less than TIME_MS.SECOND characters')
    .optional(),
})

// Themes & Content Step Schema
export const themesContentSchema = z.object({
  majorThemes: z
    .array(z.enum(THEME_OPTIONS.majorThemes as [string, ...string[]]))
    .min(1, 'Please select at least one major theme'),
  philosophicalThemes: z
    .array(z.enum(THEME_OPTIONS.philosophicalThemes as [string, ...string[]]))
    .optional(),
  socialThemes: z
    .array(z.enum(THEME_OPTIONS.socialThemes as [string, ...string[]]))
    .optional(),
  customThemes: z
    .string()
    .max(TIME_MS.SECOND, 'Custom themes must be less than TIME_MS.SECOND characters')
    .optional(),
})

// Technical Specifications Step Schema
export const technicalSpecsSchema = z.object({
  targetWordCount: z
    .number()
    .min(10000, 'Target word count must be at least 10,000 words')
    .max(1000000, 'Target word count must be less than 1,000,000 words'),
  targetChapters: z
    .number()
    .min(1, 'Must have at least 1 chapter')
    .max(200, 'Cannot exceed 200 chapters'),
  chapterCountType: z.enum(TECHNICAL_OPTIONS.chapterCountTypes as [string, ...string[]]),
  povCharacterCount: z
    .number()
    .min(1, 'Must have at least 1 POV character')
    .max(20, 'Cannot exceed 20 POV characters'),
  povCharacterType: z.enum(TECHNICAL_OPTIONS.povCharacterTypes as [string, ...string[]]),
  researchNeeds: z
    .array(z.enum(RESEARCH_OPTIONS.researchNeeds as [string, ...string[]]))
    .optional(),
  factCheckingLevel: z.enum(RESEARCH_OPTIONS.factCheckingLevels as [string, ...string[]]),
  customResearchNotes: z
    .string()
    .max(TIME_MS.SECOND, 'Custom research notes must be less than TIME_MS.SECOND characters')
    .optional(),
})

// Complete project configuration schema
export const projectConfigSchema = projectBasicsSchema
  .merge(genreStyleSchema)
  .merge(structureSchema)
  .merge(characterWorldSchema)
  .merge(themesContentSchema)
  .merge(technicalSpecsSchema)
  .refine(
    (data) => {
      // Conditional validation: series fields required for multi-book projects
      const isMultiBook = !data.projectScope.includes('Standalone') && 
                         !data.projectScope.includes('Anthology')
      
      if (isMultiBook && !data.seriesType) {
        return false
      }
      return true
    },
    {
      message: 'Series type is required for multi-book projects',
      path: ['seriesType'],
    }
  )
  .refine(
    (data) => {
      // Word count should be reasonable for target chapters
      const avgWordsPerChapter = data.targetWordCount / data.targetChapters
      if (avgWordsPerChapter < TIME_MS.SECOND || avgWordsPerChapter > 15000) {
        return false
      }
      return true
    },
    {
      message: 'Word count and chapter count combination results in unrealistic chapter length (should be 1,000-15,000 words per chapter)',
      path: ['targetChapters'],
    }
  )

// Individual step validation functions
export const validateProjectBasics = (data: unknown) => {
  return projectBasicsSchema.safeParse(data)
}

export const validateGenreStyle = (data: unknown) => {
  return genreStyleSchema.safeParse(data)
}

export const validateStructure = (data: unknown) => {
  return structureSchema.safeParse(data)
}

export const validateCharacterWorld = (data: unknown) => {
  return characterWorldSchema.safeParse(data)
}

export const validateThemesContent = (data: unknown) => {
  return themesContentSchema.safeParse(data)
}

export const validateTechnicalSpecs = (data: unknown) => {
  return technicalSpecsSchema.safeParse(data)
}

// Type exports
export type ProjectBasicsData = z.infer<typeof projectBasicsSchema>
export type GenreStyleData = z.infer<typeof genreStyleSchema>
export type StructureData = z.infer<typeof structureSchema>
export type CharacterWorldData = z.infer<typeof characterWorldSchema>
export type ThemesContentData = z.infer<typeof themesContentSchema>
export type TechnicalSpecsData = z.infer<typeof technicalSpecsSchema>
export type ProjectConfigData = z.infer<typeof projectConfigSchema>