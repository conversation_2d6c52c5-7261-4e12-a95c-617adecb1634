'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  AccessibleButton, 
  AccessibleInput, 
  AccessibleTextarea,
  AccessibleIconButton,
  AccessibleSpinner,
  AccessibleProgress,
  SkipToContent,
  AccessibleAlert
} from '@/components/accessibility/accessible-wrapper';
import { 
  AccessibleModal, 
  AccessibleConfirmDialog, 
  AccessibleAlertDialog 
} from '@/components/accessibility/accessible-modal';
import { useKeyboardShortcuts, useRovingTabIndex } from '@/hooks/use-accessibility';
import { Save, Trash2, Edit, Plus, Search, Settings } from 'lucide-react';

export default function AccessibilityDemoPage() {
  const [formData, setFormData] = useState({ email: '', message: '' });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [modalOpen, setModalOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertType, setAlertType] = useState<'info' | 'warning' | 'error' | 'success'>('info');

  // Keyboard shortcuts
  const shortcuts = useKeyboardShortcuts([
    { key: 's', ctrl: true, handler: () => setModalOpen(true), description: 'Save document' },
    { key: 'n', ctrl: true, handler: () => setFormData({ email: '', message: '' }), description: 'Create new' },
    { key: '/', ctrl: true, handler: () => document.getElementById('search-input')?.focus(), description: 'Focus search' },
  ]);

  // Roving tab index for button group
  const { currentIndex, getRovingProps, handleKeyDown } = useRovingTabIndex(4);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: Record<string, string> = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    }
    if (!formData.message) {
      newErrors.message = 'Message is required';
    }
    
    setErrors(newErrors);
    
    if (Object.keys(newErrors).length === 0) {
      setLoading(true);
      // Simulate progress
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setLoading(false);
            return 100;
          }
          return prev + 10;
        });
      }, 200);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <SkipToContent />
      
      <main id="main-content">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Accessibility Demo</h1>
          <p className="text-muted-foreground">
            Demonstrating accessible components and patterns for BookScribe
          </p>
        </div>

        <Tabs defaultValue="components" className="space-y-4">
          <TabsList>
            <TabsTrigger value="components">Components</TabsTrigger>
            <TabsTrigger value="forms">Forms</TabsTrigger>
            <TabsTrigger value="modals">Modals</TabsTrigger>
            <TabsTrigger value="keyboard">Keyboard</TabsTrigger>
          </TabsList>

          <TabsContent value="components" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Accessible Buttons</CardTitle>
                <CardDescription>
                  Buttons with proper ARIA labels and states
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <AccessibleButton label="Save document">
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </AccessibleButton>
                  
                  <AccessibleButton 
                    variant="outline" 
                    loading={loading}
                    label="Processing request"
                  >
                    Process
                  </AccessibleButton>
                  
                  <AccessibleButton 
                    variant="destructive"
                    ariaLabel="Delete selected items"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </AccessibleButton>
                </div>

                <div className="flex gap-2">
                  <AccessibleIconButton
                    icon={<Edit className="w-4 h-4" />}
                    screenReaderText="Edit document"
                    variant="outline"
                    size="sm"
                  />
                  
                  <AccessibleIconButton
                    icon={<Plus className="w-4 h-4" />}
                    screenReaderText="Create new item"
                    variant="outline"
                    size="sm"
                  />
                  
                  <AccessibleIconButton
                    icon={<Search className="w-4 h-4" />}
                    screenReaderText="Search"
                    variant="outline"
                    size="sm"
                  />
                  
                  <AccessibleIconButton
                    icon={<Settings className="w-4 h-4" />}
                    screenReaderText="Settings"
                    variant="outline"
                    size="sm"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Loading & Progress</CardTitle>
                <CardDescription>
                  Accessible loading indicators and progress bars
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <AccessibleSpinner size="sm" />
                  <AccessibleSpinner size="md" />
                  <AccessibleSpinner size="lg" label="Loading data..." />
                </div>

                <AccessibleProgress 
                  value={progress} 
                  label="Upload progress"
                  showValue
                />
                
                <AccessibleProgress 
                  value={75} 
                  max={100}
                  label="Project completion"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Alerts & Notifications</CardTitle>
                <CardDescription>
                  Accessible alert messages with proper roles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <AccessibleAlert type="info" title="Information">
                  This is an informational message for the user.
                </AccessibleAlert>
                
                <AccessibleAlert type="success" title="Success" onClose={() => {}}>
                  Your changes have been saved successfully!
                </AccessibleAlert>
                
                <AccessibleAlert type="warning" title="Warning">
                  Please review your changes before proceeding.
                </AccessibleAlert>
                
                <AccessibleAlert type="error" title="Error" onClose={() => {}}>
                  An error occurred while processing your request.
                </AccessibleAlert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="forms" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Accessible Form</CardTitle>
                <CardDescription>
                  Form with proper labels, hints, and error messages
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4 max-w-md">
                  <AccessibleInput
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    error={errors.email}
                    hint="We'll never share your email with anyone else."
                    required
                  />
                  
                  <AccessibleTextarea
                    label="Message"
                    placeholder="Enter your message here..."
                    value={formData.message}
                    onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                    error={errors.message}
                    hint="Maximum 500 characters"
                    characterCount
                    maxCharacters={500}
                    rows={4}
                    required
                  />
                  
                  <AccessibleButton type="submit" loading={loading}>
                    Submit Form
                  </AccessibleButton>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="modals" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Accessible Modals</CardTitle>
                <CardDescription>
                  Modal dialogs with focus management and ARIA attributes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <AccessibleButton onClick={() => setModalOpen(true)}>
                    Open Modal
                  </AccessibleButton>
                  
                  <AccessibleButton 
                    variant="outline"
                    onClick={() => setConfirmOpen(true)}
                  >
                    Open Confirm Dialog
                  </AccessibleButton>
                  
                  <AccessibleButton 
                    variant="outline"
                    onClick={() => {
                      setAlertType('success');
                      setAlertOpen(true);
                    }}
                  >
                    Success Alert
                  </AccessibleButton>
                  
                  <AccessibleButton 
                    variant="outline"
                    onClick={() => {
                      setAlertType('error');
                      setAlertOpen(true);
                    }}
                  >
                    Error Alert
                  </AccessibleButton>
                </div>

                <AccessibleModal
                  isOpen={modalOpen}
                  onClose={() => setModalOpen(false)}
                  title="Example Modal"
                  description="This is an accessible modal with focus management"
                >
                  <div className="space-y-4">
                    <p>
                      This modal traps focus, can be closed with Escape, and returns
                      focus to the trigger element when closed.
                    </p>
                    <AccessibleInput
                      label="Name"
                      placeholder="Enter your name"
                    />
                  </div>
                </AccessibleModal>

                <AccessibleConfirmDialog
                  isOpen={confirmOpen}
                  onClose={() => setConfirmOpen(false)}
                  onConfirm={() => setConfirmOpen(false)}
                  title="Confirm Action"
                  description="Are you sure you want to proceed with this action?"
                  confirmLabel="Yes, proceed"
                  cancelLabel="Cancel"
                  confirmVariant="destructive"
                />

                <AccessibleAlertDialog
                  isOpen={alertOpen}
                  onClose={() => setAlertOpen(false)}
                  title={alertType === 'success' ? 'Success!' : 'Error!'}
                  description={
                    alertType === 'success'
                      ? 'Your action was completed successfully.'
                      : 'An error occurred. Please try again.'
                  }
                  type={alertType}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="keyboard" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Keyboard Navigation</CardTitle>
                <CardDescription>
                  Keyboard shortcuts and navigation patterns
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Available Shortcuts</h3>
                  <div className="space-y-2">
                    {shortcuts.map((shortcut, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                        <span className="text-sm">{shortcut.description}</span>
                        <kbd className="px-2 py-1 text-xs bg-background rounded border">
                          {shortcut.modifiers.join(' + ')} + {shortcut.key.toUpperCase()}
                        </kbd>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Roving Tab Index</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Use arrow keys to navigate between buttons
                  </p>
                  <div 
                    className="flex gap-2"
                    role="toolbar"
                    aria-label="Text formatting"
                    onKeyDown={handleKeyDown}
                  >
                    {['Bold', 'Italic', 'Underline', 'Link'].map((label, index) => (
                      <button
                        key={label}
                        className={`px-3 py-1 border rounded ${
                          currentIndex === index ? 'ring-2 ring-primary' : ''
                        }`}
                        {...getRovingProps(index)}
                      >
                        {label}
                      </button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}