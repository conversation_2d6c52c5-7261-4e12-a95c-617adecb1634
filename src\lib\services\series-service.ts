import { createClient } from '@/lib/supabase/client'
import { createClient as createServerClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'
import type { 
  Series, 
  SeriesBook, 
  SeriesCharacterArc, 
  SeriesUniverseRule, 
  SeriesContinuityIssue,
  Project
} from '@/lib/db/types'

export class SeriesService {
  private supabase: ReturnType<typeof createClient> | ReturnType<typeof createServerClient>
  
  constructor(isServer = false) {
    this.supabase = isServer ? createServerClient() : createClient()
  }

  // Series CRUD operations
  async createSeries(data: Partial<Series>) {
    try {
      const { data: series, error } = await this.supabase
        .from('series')
        .insert(data)
        .select()
        .single()

      if (error) throw error
      return { series, error: null }
    } catch (error) {
      logger.error('Error creating series:', error)
      return { series: null, error }
    }
  }

  async getSeries(seriesId: string) {
    try {
      const { data: series, error } = await this.supabase
        .from('series')
        .select('*')
        .eq('id', seriesId)
        .single()

      if (error) throw error
      return { series, error: null }
    } catch (error) {
      logger.error('Error fetching series:', error)
      return { series: null, error }
    }
  }

  async updateSeries(seriesId: string, updates: Partial<Series>) {
    try {
      const { data: series, error } = await this.supabase
        .from('series')
        .update(updates)
        .eq('id', seriesId)
        .select()
        .single()

      if (error) throw error
      return { series, error: null }
    } catch (error) {
      logger.error('Error updating series:', error)
      return { series: null, error }
    }
  }

  async deleteSeries(seriesId: string) {
    try {
      // First remove all books from series
      await this.supabase
        .from('series_books')
        .delete()
        .eq('series_id', seriesId)

      // Then delete the series
      const { error } = await this.supabase
        .from('series')
        .delete()
        .eq('id', seriesId)

      if (error) throw error
      return { error: null }
    } catch (error) {
      logger.error('Error deleting series:', error)
      return { error }
    }
  }

  // Series Books management
  async addBookToSeries(seriesId: string, projectId: string, bookData: Partial<SeriesBook>) {
    try {
      const { data: seriesBook, error } = await this.supabase
        .from('series_books')
        .insert({
          series_id: seriesId,
          project_id: projectId,
          ...bookData
        })
        .select()
        .single()

      if (error) throw error

      // Update series book count
      await this.updateSeriesBookCount(seriesId)

      return { seriesBook, error: null }
    } catch (error) {
      logger.error('Error adding book to series:', error)
      return { seriesBook: null, error }
    }
  }

  async removeBookFromSeries(seriesId: string, projectId: string) {
    try {
      const { error } = await this.supabase
        .from('series_books')
        .delete()
        .eq('series_id', seriesId)
        .eq('project_id', projectId)

      if (error) throw error

      // Update series book count
      await this.updateSeriesBookCount(seriesId)

      return { error: null }
    } catch (error) {
      logger.error('Error removing book from series:', error)
      return { error }
    }
  }

  async getSeriesBooks(seriesId: string) {
    try {
      const { data: books, error } = await this.supabase
        .from('series_books')
        .select(`
          *,
          projects (
            id,
            title,
            description,
            status,
            current_word_count,
            target_word_count,
            primary_genre,
            created_at,
            updated_at
          )
        `)
        .eq('series_id', seriesId)
        .order('book_number', { ascending: true })

      if (error) throw error
      return { books, error: null }
    } catch (error) {
      logger.error('Error fetching series books:', error)
      return { books: null, error }
    }
  }

  async updateBookOrder(seriesId: string, bookOrders: { project_id: string, book_number: number }[]) {
    try {
      const updates = bookOrders.map(order => 
        this.supabase
          .from('series_books')
          .update({ book_number: order.book_number })
          .eq('series_id', seriesId)
          .eq('project_id', order.project_id)
      )

      await Promise.all(updates)
      return { error: null }
    } catch (error) {
      logger.error('Error updating book order:', error)
      return { error }
    }
  }

  // Character Arc management
  async createCharacterArc(arcData: Partial<SeriesCharacterArc>) {
    try {
      const { data: arc, error } = await this.supabase
        .from('series_character_arcs')
        .insert(arcData)
        .select()
        .single()

      if (error) throw error
      return { arc, error: null }
    } catch (error) {
      logger.error('Error creating character arc:', error)
      return { arc: null, error }
    }
  }

  async getSeriesCharacterArcs(seriesId: string) {
    try {
      const { data: arcs, error } = await this.supabase
        .from('series_character_arcs')
        .select('*')
        .eq('series_id', seriesId)
        .order('starts_in_book', { ascending: true })

      if (error) throw error
      return { arcs, error: null }
    } catch (error) {
      logger.error('Error fetching character arcs:', error)
      return { arcs: null, error }
    }
  }

  async updateCharacterArc(arcId: string, updates: Partial<SeriesCharacterArc>) {
    try {
      const { data: arc, error } = await this.supabase
        .from('series_character_arcs')
        .update(updates)
        .eq('id', arcId)
        .select()
        .single()

      if (error) throw error
      return { arc, error: null }
    } catch (error) {
      logger.error('Error updating character arc:', error)
      return { arc: null, error }
    }
  }

  // Universe Rules management
  async createUniverseRule(ruleData: Partial<SeriesUniverseRule>) {
    try {
      const { data: rule, error } = await this.supabase
        .from('series_universe_rules')
        .insert(ruleData)
        .select()
        .single()

      if (error) throw error
      return { rule, error: null }
    } catch (error) {
      logger.error('Error creating universe rule:', error)
      return { rule: null, error }
    }
  }

  async getSeriesUniverseRules(seriesId: string, category?: string) {
    try {
      let query = this.supabase
        .from('series_universe_rules')
        .select('*')
        .eq('series_id', seriesId)

      if (category) {
        query = query.eq('rule_category', category)
      }

      const { data: rules, error } = await query.order('rule_category', { ascending: true })

      if (error) throw error
      return { rules, error: null }
    } catch (error) {
      logger.error('Error fetching universe rules:', error)
      return { rules: null, error }
    }
  }

  // Continuity Issues management
  async createContinuityIssue(issueData: Partial<SeriesContinuityIssue>) {
    try {
      const { data: issue, error } = await this.supabase
        .from('series_continuity_issues')
        .insert(issueData)
        .select()
        .single()

      if (error) throw error
      return { issue, error: null }
    } catch (error) {
      logger.error('Error creating continuity issue:', error)
      return { issue: null, error }
    }
  }

  async getSeriesContinuityIssues(seriesId: string, status?: string) {
    try {
      let query = this.supabase
        .from('series_continuity_issues')
        .select('*')
        .eq('series_id', seriesId)

      if (status) {
        query = query.eq('status', status)
      }

      const { data: issues, error } = await query
        .order('severity', { ascending: false })
        .order('created_at', { ascending: false })

      if (error) throw error
      return { issues, error: null }
    } catch (error) {
      logger.error('Error fetching continuity issues:', error)
      return { issues: null, error }
    }
  }

  async resolveContinuityIssue(issueId: string, resolution: string, bookNumber?: number) {
    try {
      const { data: issue, error } = await this.supabase
        .from('series_continuity_issues')
        .update({
          status: 'resolved',
          resolution_notes: resolution,
          resolved_in_book: bookNumber,
          resolved_at: new Date().toISOString()
        })
        .eq('id', issueId)
        .select()
        .single()

      if (error) throw error
      return { issue, error: null }
    } catch (error) {
      logger.error('Error resolving continuity issue:', error)
      return { issue: null, error }
    }
  }

  // Helper methods
  private async updateSeriesBookCount(seriesId: string) {
    try {
      const { data: books } = await this.supabase
        .from('series_books')
        .select('book_number')
        .eq('series_id', seriesId)

      const bookCount = books?.length || 0
      
      await this.supabase
        .from('series')
        .update({ current_book_count: bookCount })
        .eq('id', seriesId)
    } catch (error) {
      logger.error('Error updating series book count:', error)
    }
  }

  // Analytics and insights
  async getSeriesAnalytics(seriesId: string) {
    try {
      // Get series details
      const { data: series } = await this.supabase
        .from('series')
        .select('*')
        .eq('id', seriesId)
        .single()

      // Get all books in series with their word counts
      const { data: books } = await this.supabase
        .from('series_books')
        .select('*, projects(*)')
        .eq('series_id', seriesId)

      // Get character arcs
      const { data: arcs } = await this.supabase
        .from('series_character_arcs')
        .select('*')
        .eq('series_id', seriesId)

      // Get continuity issues
      const { data: issues } = await this.supabase
        .from('series_continuity_issues')
        .select('*')
        .eq('series_id', seriesId)

      // Calculate analytics
      const totalWords = books?.reduce((sum, book) => 
        sum + (book.projects?.current_word_count || 0), 0) || 0
      
      const targetWords = books?.reduce((sum, book) => 
        sum + (book.projects?.target_word_count || 0), 0) || 0
      
      const completionRate = targetWords > 0 ? (totalWords / targetWords) * 100 : 0

      const openIssues = issues?.filter(i => i.status !== 'resolved').length || 0
      const resolvedIssues = issues?.filter(i => i.status === 'resolved').length || 0

      const activeArcs = arcs?.filter(a => 
        !a.concludes_in_book || a.concludes_in_book > (series?.current_book_count || 0)
      ).length || 0

      return {
        analytics: {
          series,
          bookCount: books?.length || 0,
          totalWords,
          targetWords,
          completionRate,
          characterArcs: {
            total: arcs?.length || 0,
            active: activeArcs,
            completed: (arcs?.length || 0) - activeArcs
          },
          continuityIssues: {
            total: issues?.length || 0,
            open: openIssues,
            resolved: resolvedIssues,
            bySeverity: {
              critical: issues?.filter(i => i.severity === 'critical').length || 0,
              high: issues?.filter(i => i.severity === 'high').length || 0,
              medium: issues?.filter(i => i.severity === 'medium').length || 0,
              low: issues?.filter(i => i.severity === 'low').length || 0
            }
          }
        },
        error: null
      }
    } catch (error) {
      logger.error('Error calculating series analytics:', error)
      return { analytics: null, error }
    }
  }

  // Cross-book character tracking
  async getCharacterJourney(seriesId: string, characterName: string) {
    try {
      // Get character arc
      const { data: arc } = await this.supabase
        .from('series_character_arcs')
        .select('*')
        .eq('series_id', seriesId)
        .eq('character_name', characterName)
        .single()

      // Get all books in series
      const { data: books } = await this.supabase
        .from('series_books')
        .select('*, projects(id, title)')
        .eq('series_id', seriesId)
        .order('book_number')

      // Get character appearances in each book
      const characterAppearances = await Promise.all(
        books?.map(async (book) => {
          const { data: character } = await this.supabase
            .from('characters')
            .select('*')
            .eq('project_id', book.project_id)
            .eq('name', characterName)
            .single()

          return {
            bookNumber: book.book_number,
            bookTitle: book.projects?.title,
            character
          }
        }) || []
      )

      return {
        journey: {
          arc,
          appearances: characterAppearances.filter(a => a.character)
        },
        error: null
      }
    } catch (error) {
      logger.error('Error fetching character journey:', error)
      return { journey: null, error }
    }
  }
}

// Export singleton instance for client-side use
export const seriesService = new SeriesService()

// Export function for server-side use
export const getSeriesService = (isServer = false) => new SeriesService(isServer)