import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware';
import { GDPRService, gdprRequestSchema, GDPRRequestType } from '@/lib/privacy/gdpr-service';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

// Extended schema with email verification
const createRequestSchema = gdprRequestSchema.extend({
  emailVerification: z.string().email('Valid email required for verification')
});

export const GET = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'authenticated',
    rateLimitCost: 1,
    maxRequestSize: 1024
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const user = request.user!;
    
    // Get user's GDPR requests
    const requests = await GDPRService.getUserRequests(user.id);

    logger.info('GDPR requests retrieved', {
      userId: user.id,
      requestCount: requests.length,
      clientIP: context.clientIP
    });

    return NextResponse.json({
      success: true,
      requests,
      activeRequests: requests.filter(r => 
        r.status === 'pending' || r.status === 'in_progress'
      ).length
    });

  } catch (error) {
    logger.error('Failed to retrieve GDPR requests', {
      error,
      userId: request.user?.id,
      clientIP: context.clientIP
    });

    return NextResponse.json(
      { error: 'Failed to retrieve GDPR requests' },
      { status: 500 }
    );
  }
});

export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation with GDPR-specific checks
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: createRequestSchema,
    rateLimitKey: 'authenticated',
    rateLimitCost: 10, // Higher cost for GDPR requests
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      // Check for existing pending requests
      const existingRequests = await GDPRService.getUserRequests(user.id);
      const pendingRequests = existingRequests.filter(r => 
        r.status === 'pending' || r.status === 'in_progress'
      );

      // Limit concurrent GDPR requests
      if (pendingRequests.length >= 2) {
        return {
          valid: false,
          error: 'You have reached the maximum number of pending GDPR requests. Please wait for existing requests to complete.'
        };
      }

      // Special validation for erasure requests
      const body = await req.json();
      if (body.type === GDPRRequestType.ERASURE) {
        // Check if user has active subscriptions
        const hasActiveSubscription = await checkActiveSubscriptions(user.id);
        if (hasActiveSubscription) {
          return {
            valid: false,
            error: 'Please cancel your active subscription before requesting account deletion.'
          };
        }
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const user = request.user!;
    const requestData = context.body;

    // Verify email matches for security
    if (requestData.emailVerification !== user.email) {
      return NextResponse.json(
        { error: 'Email verification failed. Please enter your account email.' },
        { status: 400 }
      );
    }

    // Create GDPR request
    const gdprRequest = await GDPRService.createRequest(user.id, {
      type: requestData.type,
      reason: requestData.reason,
      dataCategories: requestData.dataCategories,
      additionalInfo: {
        ipAddress: context.clientIP,
        userAgent: context.userAgent,
        requestedAt: new Date().toISOString()
      }
    });

    logger.info('GDPR request created successfully', {
      requestId: gdprRequest.id,
      userId: user.id,
      type: requestData.type,
      clientIP: context.clientIP
    });

    // Start processing immediately for certain request types
    if ([GDPRRequestType.ACCESS, GDPRRequestType.PORTABILITY].includes(requestData.type)) {
      // Process async in background
      GDPRService.processRequest(gdprRequest.id).catch(error => {
        logger.error('Failed to process GDPR request', {
          error,
          requestId: gdprRequest.id
        });
      });
    }

    return NextResponse.json({
      success: true,
      request: gdprRequest,
      message: getRequestMessage(requestData.type),
      estimatedCompletionTime: getEstimatedTime(requestData.type)
    }, { status: 201 });

  } catch (error) {
    logger.error('Failed to create GDPR request', {
      error,
      userId: request.user?.id,
      clientIP: context.clientIP,
      requestBody: context.body
    });

    return NextResponse.json(
      { error: 'Failed to create GDPR request', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
});

// Helper function to check active subscriptions
async function checkActiveSubscriptions(userId: string): Promise<boolean> {
  // Implementation would check Stripe/billing system
  return false; // Placeholder
}

// Helper function to get user-friendly message
function getRequestMessage(type: GDPRRequestType): string {
  const messages = {
    [GDPRRequestType.ACCESS]: 'Your data access request has been submitted. You will receive an email with a download link within 72 hours.',
    [GDPRRequestType.ERASURE]: 'Your account deletion request has been submitted. This action cannot be undone. You will receive a confirmation email shortly.',
    [GDPRRequestType.RECTIFICATION]: 'Your data correction request has been submitted. We will review and process it within 30 days.',
    [GDPRRequestType.PORTABILITY]: 'Your data portability request has been submitted. You will receive your data in a machine-readable format within 72 hours.',
    [GDPRRequestType.CONSENT_WITHDRAWAL]: 'Your consent withdrawal has been processed. Your preferences have been updated.',
    [GDPRRequestType.PROCESSING_RESTRICTION]: 'Your processing restriction request has been submitted. We will review and apply restrictions within 72 hours.'
  };

  return messages[type] || 'Your request has been submitted successfully.';
}

// Helper function to get estimated completion time
function getEstimatedTime(type: GDPRRequestType): string {
  const times = {
    [GDPRRequestType.ACCESS]: '72 hours',
    [GDPRRequestType.ERASURE]: '30 days',
    [GDPRRequestType.RECTIFICATION]: '30 days',
    [GDPRRequestType.PORTABILITY]: '72 hours',
    [GDPRRequestType.CONSENT_WITHDRAWAL]: 'Immediate',
    [GDPRRequestType.PROCESSING_RESTRICTION]: '72 hours'
  };

  return times[type] || '30 days';
}