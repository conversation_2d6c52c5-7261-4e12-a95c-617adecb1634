import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { VoiceConsistencyChecker } from '@/components/editor/voice-consistency-checker'

// Mock the hooks
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}))

jest.mock('@/hooks/use-voice-profiles', () => ({
  useVoiceProfiles: jest.fn(() => ({
    profiles: [
      {
        id: 'profile-1',
        name: 'Author Voice',
        type: 'author',
        confidence: 0.85,
        is_global: false,
        project_id: 'test-project-123'
      },
      {
        id: 'profile-2',
        name: 'Character - Hero',
        type: 'character',
        confidence: 0.92,
        is_global: false,
        project_id: 'test-project-123'
      }
    ],
    loading: false,
    error: null,
  })),
  useVoiceConsistency: jest.fn(() => ({
    checking: false,
    checkConsistency: jest.fn().mockResolvedValue({
      consistencyScore: 85,
      suggestions: [
        {
          text: 'Consider using more descriptive language',
          severity: 'info',
          location: { start: 0, end: 50 }
        }
      ],
      deviations: [
        {
          metric: 'Average Sentence Length',
          expected: 18,
          actual: 22,
          difference: 4
        }
      ]
    })
  }))
}))

describe('VoiceConsistencyChecker', () => {
  const mockProps = {
    content: 'This is a test content that is long enough to be analyzed. It contains multiple sentences and should pass the minimum character requirement for voice consistency analysis.',
    projectId: 'test-project-123',
    chapterId: 'test-chapter-456',
    onSuggestionApply: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Authentication and API Integration', () => {
    it('should load voice profiles for authenticated user', async () => {
      render(<VoiceConsistencyChecker {...mockProps} />)

      // Wait for profiles to load
      await waitFor(() => {
        expect(screen.getByText('Author Voice')).toBeInTheDocument()
        expect(screen.getByText('Character - Hero')).toBeInTheDocument()
      })
    })

    it('should display profile selector with loaded profiles', async () => {
      render(<VoiceConsistencyChecker {...mockProps} />)

      // Check the select dropdown exists
      const select = screen.getByLabelText('Voice Profile')
      expect(select).toBeInTheDocument()

      // Click to open dropdown
      await userEvent.click(select)

      // Check profiles are shown
      await waitFor(() => {
        expect(screen.getByText('Author Voice')).toBeInTheDocument()
        expect(screen.getByText('Character - Hero')).toBeInTheDocument()
      })
    })

    it('should call consistency check API with authentication', async () => {
      const { useVoiceConsistency } = require('@/hooks/use-voice-profiles')
      const mockCheckConsistency = jest.fn().mockResolvedValue({
        consistencyScore: 85,
        suggestions: [],
        deviations: []
      })
      
      useVoiceConsistency.mockReturnValue({
        checking: false,
        checkConsistency: mockCheckConsistency
      })

      render(<VoiceConsistencyChecker {...mockProps} />)

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText('Check Consistency')).toBeInTheDocument()
      })

      // Click check consistency button
      const checkButton = screen.getByRole('button', { name: /check consistency/i })
      await userEvent.click(checkButton)

      // Verify API was called with correct parameters
      await waitFor(() => {
        expect(mockCheckConsistency).toHaveBeenCalledWith(
          mockProps.content,
          'profile-1', // First profile should be auto-selected
          mockProps.projectId,
          mockProps.chapterId
        )
      })
    })
  })

  describe('Loading States', () => {
    it('should show loading state when fetching profiles', () => {
      const { useVoiceProfiles } = require('@/hooks/use-voice-profiles')
      useVoiceProfiles.mockReturnValue({
        profiles: [],
        loading: true,
        error: null
      })

      render(<VoiceConsistencyChecker {...mockProps} />)

      expect(screen.getByText('Loading profiles...')).toBeInTheDocument()
    })

    it('should show checking state when analyzing', async () => {
      const { useVoiceConsistency } = require('@/hooks/use-voice-profiles')
      useVoiceConsistency.mockReturnValue({
        checking: true,
        checkConsistency: jest.fn()
      })

      render(<VoiceConsistencyChecker {...mockProps} />)

      const checkButton = screen.getByRole('button', { name: /checking/i })
      expect(checkButton).toBeDisabled()
      expect(screen.getByText('Checking...')).toBeInTheDocument()
    })
  })

  describe('Results Display', () => {
    it('should display consistency score and results', async () => {
      render(<VoiceConsistencyChecker {...mockProps} />)

      // Click check consistency
      const checkButton = screen.getByRole('button', { name: /check consistency/i })
      await userEvent.click(checkButton)

      // Wait for results
      await waitFor(() => {
        expect(screen.getByText('85%')).toBeInTheDocument()
        expect(screen.getByText('Overall Consistency')).toBeInTheDocument()
      })
    })

    it('should display suggestions when available', async () => {
      render(<VoiceConsistencyChecker {...mockProps} />)

      // Click check consistency
      const checkButton = screen.getByRole('button', { name: /check consistency/i })
      await userEvent.click(checkButton)

      // Wait for suggestions
      await waitFor(() => {
        expect(screen.getByText('Consider using more descriptive language')).toBeInTheDocument()
      })
    })

    it('should display metric deviations', async () => {
      render(<VoiceConsistencyChecker {...mockProps} />)

      // Click check consistency
      const checkButton = screen.getByRole('button', { name: /check consistency/i })
      await userEvent.click(checkButton)

      // Wait for deviations
      await waitFor(() => {
        expect(screen.getByText('Average Sentence Length')).toBeInTheDocument()
        expect(screen.getByText('Expected: 18')).toBeInTheDocument()
        expect(screen.getByText('Actual: 22')).toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    it('should show error when no profile selected', async () => {
      const { useVoiceProfiles } = require('@/hooks/use-voice-profiles')
      useVoiceProfiles.mockReturnValue({
        profiles: [],
        loading: false,
        error: null
      })

      const mockToast = jest.fn()
      jest.mocked(require('@/hooks/use-toast').useToast).mockReturnValue({
        toast: mockToast
      })

      render(<VoiceConsistencyChecker {...mockProps} />)

      const checkButton = screen.getByRole('button', { name: /check consistency/i })
      await userEvent.click(checkButton)

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Please select a voice profile first',
        variant: 'destructive'
      })
    })

    it('should show error for short content', async () => {
      const mockToast = jest.fn()
      jest.mocked(require('@/hooks/use-toast').useToast).mockReturnValue({
        toast: mockToast
      })

      render(
        <VoiceConsistencyChecker
          {...mockProps}
          content="Too short"
        />
      )

      await waitFor(() => {
        expect(screen.getByText('Check Consistency')).toBeInTheDocument()
      })

      const checkButton = screen.getByRole('button', { name: /check consistency/i })
      await userEvent.click(checkButton)

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Please write at least 100 characters to check consistency',
        variant: 'destructive'
      })
    })
  })

  describe('Suggestion Application', () => {
    it('should call onSuggestionApply when clicking apply button', async () => {
      render(<VoiceConsistencyChecker {...mockProps} />)

      // Click check consistency
      const checkButton = screen.getByRole('button', { name: /check consistency/i })
      await userEvent.click(checkButton)

      // Wait for suggestions and find apply button
      await waitFor(() => {
        const applyButton = screen.getByRole('button', { name: /apply/i })
        expect(applyButton).toBeInTheDocument()
      })

      // Click apply button
      const applyButton = screen.getByRole('button', { name: /apply/i })
      await userEvent.click(applyButton)

      expect(mockProps.onSuggestionApply).toHaveBeenCalledWith(
        'Consider using more descriptive language'
      )
    })
  })
})