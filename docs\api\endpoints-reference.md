# BookScribe API Endpoints Reference

## Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [API Response Format](#api-response-format)
- [Rate Limiting](#rate-limiting)
- [Error Handling](#error-handling)
- [Endpoint Categories](#endpoint-categories)
- [Complete Endpoint Reference](#complete-endpoint-reference)

## Overview

The BookScribe API is a RESTful API built with Next.js API Routes. All endpoints follow consistent patterns for authentication, error handling, and response formats.

### Base URL
```
Production: https://bookscribe.ai/api
Development: http://localhost:3000/api
```

### Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Authentication

All API endpoints (except public endpoints) require JWT authentication via Supabase Auth.

### Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Supabase Auth
    participant Database
    
    Client->>API: Request with Bearer token
    API->>Supabase Auth: Verify JWT
    Supabase Auth-->>API: User session
    API->>Database: Check permissions
    Database-->>API: User authorized
    API-->>Client: Response
```

### Public Endpoints
- `GET /api/health` - Health check
- `POST /api/webhooks/stripe` - Stripe webhooks
- `GET /api/demo/*` - Demo endpoints

## API Response Format

### Success Response
```typescript
{
  success: true,
  data: T,
  metadata?: {
    page?: number;
    limit?: number;
    total?: number;
    hasMore?: boolean;
  }
}
```

### Error Response
```typescript
{
  success: false,
  error: {
    code: string;
    message: string;
    details?: any;
  }
}
```

## Rate Limiting

Rate limits are enforced based on subscription tier:

| Tier | Requests/Hour | AI Generations/Day |
|------|--------------|-------------------|
| Free | 100 | 10 |
| Starter | 1,000 | 100 |
| Professional | 10,000 | 1,000 |
| Enterprise | Unlimited | Unlimited |

## Error Handling

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Rate Limited
- `500` - Internal Server Error

### Error Codes
```typescript
enum ErrorCodes {
  AUTH_REQUIRED = 'AUTH_REQUIRED',
  INVALID_REQUEST = 'INVALID_REQUEST',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RATE_LIMITED = 'RATE_LIMITED',
  SUBSCRIPTION_REQUIRED = 'SUBSCRIPTION_REQUIRED',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}
```

## Endpoint Categories

### 1. AI & Agents
Endpoints for AI-powered content generation and agent orchestration.

### 2. Projects & Content
CRUD operations for projects, chapters, and content management.

### 3. Analytics
Writing analytics, productivity tracking, and insights.

### 4. Collaboration
Real-time collaboration and team features.

### 5. Search & Discovery
Semantic search and content discovery.

### 6. Billing & Subscriptions
Payment processing and subscription management.

### 7. Admin
Administrative endpoints for system management.

## Complete Endpoint Reference

### AI & Agent Endpoints

#### Initialize Agent Pipeline
```http
POST /api/agents/initialize
```
Initialize the multi-agent pipeline for content generation.

**Request Body:**
```json
{
  "projectId": "uuid",
  "agentType": "story_architect",
  "context": {
    "storyPrompt": "string",
    "targetWordCount": 80000,
    "targetChapters": 20
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "pipelineId": "uuid",
    "status": "initialized",
    "agents": ["story_architect", "character_developer", "chapter_planner"]
  }
}
```

#### Generate Content
```http
POST /api/agents/generate
```
Generate content using specified agent.

**Request Body:**
```json
{
  "projectId": "uuid",
  "agentType": "writing_agent",
  "chapterNumber": 1,
  "streaming": true
}
```

**Response:** Server-sent events stream or JSON

#### Edit Content
```http
POST /api/agents/edit
```
Edit existing content with AI assistance.

**Request Body:**
```json
{
  "content": "string",
  "instructions": "string",
  "agentType": "editor_agent"
}
```

#### AI Chat
```http
POST /api/ai/chat
```
General AI chat for writing assistance.

**Request Body:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Help me with character development"
    }
  ],
  "projectId": "uuid",
  "model": "gpt-4"
}
```

### Project Management Endpoints

#### List Projects
```http
GET /api/projects
GET /api/projects?page=1&limit=20&status=active
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "My Novel",
      "status": "active",
      "wordCount": 45000,
      "chapterCount": 15,
      "lastUpdated": "2025-01-30T10:00:00Z"
    }
  ],
  "metadata": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "hasMore": true
  }
}
```

#### Create Project
```http
POST /api/projects
```

**Request Body:**
```json
{
  "title": "New Novel",
  "description": "A thrilling adventure",
  "settings": {
    "primaryGenre": "fantasy",
    "targetWordCount": 80000,
    "narrativeVoice": "third_person"
  }
}
```

#### Get Project
```http
GET /api/projects/{id}
```

#### Update Project
```http
PUT /api/projects/{id}
```

#### Delete Project
```http
DELETE /api/projects/{id}
```

#### Export Project
```http
GET /api/projects/{id}/export?format=docx
```
Export project in various formats (docx, pdf, epub, markdown).

### Chapter Management

#### List Chapters
```http
GET /api/projects/{id}/chapters
```

#### Create Chapter
```http
POST /api/chapters
```

**Request Body:**
```json
{
  "projectId": "uuid",
  "title": "Chapter 1: The Beginning",
  "content": "string",
  "metadata": {
    "wordCount": 3000,
    "sceneCount": 3
  }
}
```

#### Update Chapter
```http
PUT /api/chapters/{id}
```

#### Create Chapter Version
```http
POST /api/chapters/create-version
```
Create a new version of a chapter for version control.

### Character Management

#### List Characters
```http
GET /api/projects/{id}/characters
```

#### Create Character
```http
POST /api/characters
```

**Request Body:**
```json
{
  "projectId": "uuid",
  "name": "Jane Doe",
  "role": "protagonist",
  "profile": {
    "age": 28,
    "occupation": "Detective",
    "personality": {
      "traits": ["determined", "analytical"],
      "mbti": "INTJ"
    }
  }
}
```

#### Get Character Relationships
```http
GET /api/relationships/graph?projectId={id}
```
Get character relationship graph data.

### Analytics Endpoints

#### Writing Sessions
```http
GET /api/analytics/sessions?projectId={id}&days=30
```
Get writing session analytics.

**Response:**
```json
{
  "success": true,
  "data": {
    "totalSessions": 45,
    "totalWords": 35000,
    "averageWordsPerSession": 778,
    "sessionsPerDay": [
      {
        "date": "2025-01-30",
        "sessions": 3,
        "words": 2500
      }
    ]
  }
}
```

#### Productivity Metrics
```http
GET /api/analytics/productivity?userId={id}
```

#### Quality Analysis
```http
POST /api/analytics/quality
```
Analyze content quality across multiple dimensions.

**Request Body:**
```json
{
  "content": "string",
  "analysisType": "comprehensive"
}
```

#### AI Usage Stats
```http
GET /api/analytics/ai-usage?userId={id}&period=month
```

### Collaboration Endpoints

#### Join Session
```http
POST /api/collaboration/join
```
Join a collaborative editing session.

**Request Body:**
```json
{
  "projectId": "uuid",
  "chapterId": "uuid"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "uuid",
    "participants": [
      {
        "id": "uuid",
        "name": "John Doe",
        "cursor": {
          "line": 10,
          "column": 45
        }
      }
    ]
  }
}
```

#### Send Change
```http
POST /api/collaboration/change
```
Send document change in real-time session.

**Request Body:**
```json
{
  "sessionId": "uuid",
  "operation": {
    "type": "insert",
    "position": 100,
    "text": "new text"
  }
}
```

### Search Endpoints

#### Semantic Search
```http
POST /api/search/semantic
```
Search content using semantic similarity.

**Request Body:**
```json
{
  "query": "scenes with emotional conflict",
  "projectId": "uuid",
  "limit": 10
}
```

#### Character Moments
```http
POST /api/search/character-moments
```
Find specific character moments and interactions.

#### Theme Search
```http
POST /api/search/theme
```
Search for thematic elements in content.

### Series Management

#### Create Series
```http
POST /api/series
```

**Request Body:**
```json
{
  "title": "The Chronicles",
  "description": "Epic fantasy series",
  "universeId": "uuid"
}
```

#### Get Series Analytics
```http
GET /api/series/{id}/analytics
```
Get analytics across all books in a series.

#### Series Word Counts
```http
GET /api/series/{id}/word-counts
```
Track word counts across series books.

### Universe Management

#### Create Universe
```http
POST /api/universes
```
Create a shared universe for multiple projects.

**Request Body:**
```json
{
  "name": "Fantasy World",
  "description": "Shared world for fantasy series",
  "rules": {
    "magic_system": "string",
    "technology_level": "medieval"
  }
}
```

### Billing & Subscription Endpoints

#### Get Subscription
```http
GET /api/billing/subscriptions
```

#### Create Checkout Session
```http
POST /api/billing/subscriptions/checkout
```

**Request Body:**
```json
{
  "priceId": "price_xxx",
  "successUrl": "https://app.bookscribe.ai/success",
  "cancelUrl": "https://app.bookscribe.ai/cancel"
}
```

#### Stripe Webhook
```http
POST /api/webhooks/stripe
```
Handle Stripe webhook events (requires webhook signature).

### Admin Endpoints

#### Export User Data
```http
GET /api/admin/export-data?userId={id}
```
Export all user data for GDPR compliance.

#### System Health
```http
GET /api/health
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "services": {
      "database": "connected",
      "ai": "operational",
      "storage": "available"
    },
    "timestamp": "2025-01-30T10:00:00Z"
  }
}
```

## Streaming Endpoints

Several endpoints support streaming responses for real-time data:

### AI Stream Content
```http
POST /api/ai/stream-content
```
Stream AI-generated content with Server-Sent Events.

**Headers:**
```http
Accept: text/event-stream
```

**Event Format:**
```
event: content
data: {"text": "Generated text chunk..."}

event: done
data: {"usage": {"tokens": 1500, "words": 1200}}
```

### Typed Streaming
```http
POST /api/ai/typed-stream
```
Stream structured data with type information.

## File Upload Endpoints

### Upload Reference Material
```http
POST /api/references/upload
```
Upload reference documents (PDF, DOCX, TXT).

**Request:** Multipart form data
```
file: <binary>
projectId: uuid
type: research|character|world
```

### Import Document
```http
POST /api/import/docx
POST /api/import/pdf
POST /api/import/epub
```
Import existing manuscripts.

## Batch Operations

### Bulk Character Update
```http
PUT /api/characters/bulk
```
Update multiple characters at once.

**Request Body:**
```json
{
  "updates": [
    {
      "id": "uuid",
      "changes": {
        "profile.age": 29
      }
    }
  ]
}
```

### Bulk Story Bible Update
```http
PUT /api/story-bible/bulk
```
Update multiple story bible entries.

## WebSocket Endpoints

### Real-time Subscriptions
```ws
wss://app.bookscribe.ai/realtime
```

**Subscribe to changes:**
```json
{
  "event": "subscribe",
  "channel": "project:uuid",
  "token": "jwt_token"
}
```

**Receive updates:**
```json
{
  "event": "update",
  "type": "chapter_changed",
  "data": {
    "chapterId": "uuid",
    "changes": {}
  }
}
```

## Usage Examples

### Complete Project Creation Flow

```javascript
// 1. Create project
const project = await fetch('/api/projects', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'My Novel',
    settings: { /* ... */ }
  })
});

// 2. Initialize AI pipeline
const pipeline = await fetch('/api/agents/initialize', {
  method: 'POST',
  headers: { /* ... */ },
  body: JSON.stringify({
    projectId: project.id,
    context: { /* ... */ }
  })
});

// 3. Generate story structure
const story = await fetch('/api/agents/generate', {
  method: 'POST',
  headers: { /* ... */ },
  body: JSON.stringify({
    projectId: project.id,
    agentType: 'story_architect'
  })
});

// 4. Create characters
const characters = await fetch('/api/agents/generate', {
  method: 'POST',
  headers: { /* ... */ },
  body: JSON.stringify({
    projectId: project.id,
    agentType: 'character_developer'
  })
});
```

## Best Practices

1. **Always include authentication** for protected endpoints
2. **Handle rate limiting** with exponential backoff
3. **Use streaming** for long-running operations
4. **Implement proper error handling** for all status codes
5. **Cache responses** where appropriate
6. **Use pagination** for list endpoints
7. **Validate input** before sending requests