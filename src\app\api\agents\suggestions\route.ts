import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { z } from 'zod'
import { AI_MODELS } from '@/lib/config/ai-settings'
import { getClientIP } from '@/lib/rate-limiter-unified'
import { rateLimiters, createRateLimitResponse } from '@/lib/rate-limiter-unified'
import { logger } from '@/lib/services/logger'
import { RATE_LIMITS } from '@/lib/constants'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check
    const clientIP = getClientIP(request)
    const rateLimitResult = aiLimiter.check(RATE_LIMITS.SERVICE_HEALTH_CHECK, clientIP) // 20 suggestions per hour
    
    if (!rateLimitResult.success) {
      return createRateLimitResponse()
    }
    
    const supabase = await createTypedServerClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const body = await request.json()
    const { type, beforeCursor } = body

    if (!type || !beforeCursor) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const suggestions = await generateSuggestions(type, beforeCursor)

    return NextResponse.json({ 
      success: true, 
      suggestions
    })

  } catch (error) {
    logger.error('Error generating suggestions:', error)
    return NextResponse.json(
      { error: 'Failed to generate suggestions' },
      { status: 500 }
    )
  }
}

async function generateSuggestions(
  type: string, 
  beforeCursor: string, 
) {
  // const suggestions: string[] = []

  try {
    let prompt = ''
    const systemPrompt = 'You are a creative writing assistant helping authors improve their work.'

    switch (type) {
      case 'completion':
        prompt = `
Given this text context:
"${beforeCursor}"

Generate 3 different ways to continue this sentence or paragraph. Each completion should be 1-3 sentences long and maintain the style and tone. Return only the completion text, not the original context.

The completions should:
1. Be natural continuations of the existing text
2. Maintain consistent character voice and style
3. Advance the story or develop the current scene
4. Be between 10-50 words each

Return as a JSON array of objects with: text, confidence (0-1), context (brief explanation)
`
        break

      case 'improvement':
        const lastSentence = beforeCursor.split('.').slice(-2).join('.').trim()
        prompt = `
Analyze this text and suggest improvements:
"${lastSentence}"

Generate 2-3 alternative ways to write this text that improve:
- Clarity and readability
- Emotional impact
- Prose quality
- Show vs tell

Return as a JSON array of objects with: text, confidence (0-1), context (what was improved)
`
        break

      case 'continuation':
        prompt = `
Given this story context:
"${beforeCursor.slice(-200)}"

Suggest 2-3 ways the story could continue next. Each suggestion should be 1-2 paragraphs that:
1. Advance the plot naturally
2. Maintain character consistency
3. Create intrigue or develop conflict
4. Match the existing tone and style

Return as a JSON array of objects with: text, confidence (0-1), context (brief description of the direction)
`
        break

      default:
        throw new Error('Invalid suggestion type')
    }

    const suggestionSchema = z.array(z.object({
      text: z.string(),
      confidence: z.number().min(0).max(1).optional(),
      context: z.string().optional()
    }))
    
    try {
      const parsedSuggestions = await vercelAIClient.generateObjectWithFallback(
        prompt,
        suggestionSchema,
        {
          model: AI_MODELS.FAST,
          temperature: 0.8,
          maxTokens: 800,
          systemPrompt
        }
      )
      
      if (parsedSuggestions && parsedSuggestions.length > 0) {
        return parsedSuggestions.map((suggestion, index: number) => ({
          id: `${type}-${Date.now()}-${index}`,
          type,
          text: suggestion.text,
          context: suggestion.context || 'AI-generated suggestion',
          confidence: suggestion.confidence || 0.8
        }))
      } else {
        // Return empty if no suggestions
        return []
      }
    } catch (error) {
      logger.error('Error generating suggestions:', error)
      // Return a default suggestion on error
      return [{
        id: `${type}-${Date.now()}`,
        type,
        text: 'Unable to generate suggestions at this time.',
        context: 'Error occurred',
        confidence: 0.5
      }]
    }

  } catch (error) {
    logger.error('Error in suggestion generation:', error)
    return []
  }
}