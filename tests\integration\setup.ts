import '@testing-library/jest-dom'
import { TextEncoder, TextDecoder } from 'util'
import { Request, Response, Headers } from 'node-fetch'

// Polyfill for Next.js Edge Runtime
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder as any

// Mock fetch for tests
global.fetch = jest.fn()

// Mock Request/Response for Edge Runtime compatibility
global.Request = Request as any
global.Response = Response as any
global.Headers = Headers as any

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}

// Mock environment variables
process.env = {
  ...process.env,
  NEXT_PUBLIC_SUPABASE_URL: 'https://test.supabase.co',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: 'test-anon-key',
  SUPABASE_SERVICE_ROLE_KEY: 'test-service-key',
  OPENAI_API_KEY: 'test-openai-key',
  NODE_ENV: 'test',
}

// Setup test timeouts
jest.setTimeout(10000)

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks()
})

// Global test utilities
export const createMockRequest = (url: string, options?: RequestInit) => {
  return new Request(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

export const createMockUser = (overrides?: Partial<any>) => ({
  id: 'test-user-123',
  email: '<EMAIL>',
  user_metadata: {},
  app_metadata: {},
  created_at: new Date().toISOString(),
  ...overrides,
})

export const createMockProject = (overrides?: Partial<any>) => ({
  id: 'test-project-456',
  user_id: 'test-user-123',
  title: 'Test Project',
  description: 'A test project',
  genre: 'fantasy',
  status: 'planning',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
})

export const createMockLocation = (overrides?: Partial<any>) => ({
  id: 'test-location-789',
  project_id: 'test-project-456',
  name: 'Test Location',
  description: 'A test location',
  location_type: 'city',
  features: [],
  is_shareable: false,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
})

// Supabase mock helpers
export const createMockSupabaseClient = () => {
  const mockClient = {
    auth: {
      getUser: jest.fn(),
      getSession: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
    },
    from: jest.fn(),
    rpc: jest.fn(),
    storage: {
      from: jest.fn(),
    },
  }

  // Default query builder mock
  const queryBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
  }

  mockClient.from.mockReturnValue(queryBuilder)

  return { mockClient, queryBuilder }
}

// API response assertion helpers
export const expectSuccessResponse = (response: Response, statusCode = 200) => {
  expect(response.status).toBe(statusCode)
  return response.json().then(data => {
    expect(data.success).toBe(true)
    expect(data.data).toBeDefined()
    expect(data.meta).toBeDefined()
    expect(data.meta.timestamp).toBeDefined()
    expect(data.meta.version).toBeDefined()
    return data
  })
}

export const expectErrorResponse = (response: Response, expectedCode: string, statusCode = 500) => {
  expect(response.status).toBe(statusCode)
  return response.json().then(data => {
    expect(data.success).toBe(false)
    expect(data.error).toBeDefined()
    expect(data.error.code).toBe(expectedCode)
    expect(data.error.message).toBeDefined()
    expect(data.meta).toBeDefined()
    return data
  })
}

// Database transaction mock helpers
export const mockTransaction = (supabaseClient: any) => {
  const txMock = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    commit: jest.fn().mockResolvedValue(true),
    rollback: jest.fn().mockResolvedValue(true),
  }

  supabaseClient.transaction = jest.fn().mockImplementation(async (callback: any) => {
    try {
      const result = await callback(txMock)
      await txMock.commit()
      return { data: result, error: null }
    } catch (error) {
      await txMock.rollback()
      return { data: null, error }
    }
  })

  return txMock
}

// Rate limiter mock
export const mockRateLimiter = () => ({
  limit: jest.fn().mockResolvedValue({ success: true }),
  reset: jest.fn().mockResolvedValue(true),
})

// Logger mock
export const mockLogger = () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
})