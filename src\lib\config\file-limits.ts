import { TIME_MS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

/**
 * Centralized file size limits and type restrictions
 * All sizes are in bytes unless otherwise specified
 */

// Helper to convert sizes
const MB = 1024 * 1024
const GB = 1024 * MB

export const FILE_LIMITS = {
  // Maximum file sizes by type
  MAX_SIZES: {
    // Images
    AVATAR: 5 * MB,              // 5MB for profile avatars
    COVER_IMAGE: 10 * MB,        // 10MB for book covers
    IMAGE_GENERAL: 10 * MB,      // 10MB for general images
    IMAGE_REFERENCE: 25 * MB,    // 25MB for reference images
    
    // Documents
    DOCUMENT: 50 * MB,           // 50MB for PDFs, Word docs
    EPUB: 100 * MB,              // 100MB for EPUB files
    MANUSCRIPT: 200 * MB,        // 200MB for full manuscripts
    
    // Exports
    EXPORT_SINGLE: 50 * MB,      // 50MB for single file exports
    EXPORT_BUNDLE: 500 * MB,     // 500MB for bundled exports
    BACKUP: 1 * GB,              // 1GB for full backups
    
    // Reference materials
    REFERENCE_DOC: 25 * MB,      // 25MB for reference documents
    RESEARCH_FILE: 50 * MB,      // 50MB for research files
    
    // Audio (for future voice features)
    AUDIO_SAMPLE: 10 * MB,       // 10MB for voice samples
    AUDIO_NARRATION: 100 * MB,   // 100MB for narration files
    
    // Other
    ATTACHMENT: 25 * MB,         // 25MB for general attachments
    IMPORT: 100 * MB,            // 100MB for import files
  },
  
  // Allowed MIME types by category
  ALLOWED_TYPES: {
    IMAGE: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/gif',
      'image/svg+xml',
    ],
    
    DOCUMENT: [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'application/vnd.oasis.opendocument.text', // .odt
      'text/plain', // .txt
      'text/markdown', // .md
      'application/rtf', // .rtf
    ],
    
    EBOOK: [
      'application/epub+zip', // .epub
      'application/x-mobipocket-ebook', // .mobi
      'application/vnd.amazon.ebook', // .azw
    ],
    
    SPREADSHEET: [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
      'application/vnd.oasis.opendocument.spreadsheet', // .ods
    ],
    
    AUDIO: [
      'audio/mpeg', // .mp3
      'audio/wav',
      'audio/ogg',
      'audio/webm',
      'audio/mp4',
    ],
    
    ARCHIVE: [
      'application/zip',
      'application/x-zip-compressed',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
    ],
  },
  
  // File extension mappings
  EXTENSIONS: {
    IMAGE: ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.svg'],
    DOCUMENT: ['.pdf', '.doc', '.docx', '.odt', '.txt', '.md', '.rtf'],
    EBOOK: ['.epub', '.mobi', '.azw', '.azw3'],
    SPREADSHEET: ['.xlsx', '.xls', '.csv', '.ods'],
    AUDIO: ['.mp3', '.wav', '.ogg', '.webm', '.m4a'],
    ARCHIVE: ['.zip', '.rar', '.7z'],
  },
  
  // Content-specific limits
  CONTENT_LIMITS: {
    // Text content
    CHAPTER_LENGTH: SIZE_LIMITS.MAX_DOCUMENT_CHARS,        // 50k characters per chapter
    CHARACTER_BIO: 10000,         // 10k characters for character bios
    SCENE_DESCRIPTION: SIZE_LIMITS.SIZE_CHANGE_THRESHOLD,      // 5k characters for scene descriptions
    NOTE_LENGTH: 2000,            // 2k characters for notes
    
    // Rich text content (with formatting)
    RICH_TEXT_CHAPTER: SIZE_LIMITS.LARGE_DOCUMENT_THRESHOLD,    // 100k characters with formatting
    RICH_TEXT_NOTES: 10000,       // 10k characters for rich notes
    
    // Metadata
    TITLE_LENGTH: 200,            // 200 characters for titles
    DESCRIPTION_LENGTH: TIME_MS.SECOND,     // 1k characters for descriptions
    TAG_LENGTH: 50,               // 50 characters per tag
    MAX_TAGS: 20,                 // Maximum 20 tags per item
  },
  
  // Upload restrictions by user tier
  TIER_LIMITS: {
    FREE: {
      STORAGE_TOTAL: 1 * GB,      // 1GB total storage
      FILE_SIZE_MAX: 10 * MB,     // 10MB per file
      MONTHLY_BANDWIDTH: 5 * GB,   // 5GB monthly bandwidth
    },
    BASIC: {
      STORAGE_TOTAL: 10 * GB,     // 10GB total storage
      FILE_SIZE_MAX: 50 * MB,     // 50MB per file
      MONTHLY_BANDWIDTH: 50 * GB,  // 50GB monthly bandwidth
    },
    PRO: {
      STORAGE_TOTAL: 100 * GB,    // 100GB total storage
      FILE_SIZE_MAX: 200 * MB,    // 200MB per file
      MONTHLY_BANDWIDTH: 500 * GB, // 500GB monthly bandwidth
    },
    ENTERPRISE: {
      STORAGE_TOTAL: TIME_MS.SECOND * GB,   // 1TB total storage
      FILE_SIZE_MAX: 1 * GB,      // 1GB per file
      MONTHLY_BANDWIDTH: -1,       // Unlimited bandwidth
    },
  },
} as const

// Utility functions for file validation
export const fileValidation = {
  // Check if file size is within limits
  isValidSize: (fileSize: number, maxSize: number): boolean => {
    return fileSize > 0 && fileSize <= maxSize
  },
  
  // Check if file type is allowed
  isValidType: (mimeType: string, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(mimeType.toLowerCase())
  },
  
  // Get human-readable file size
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },
  
  // Get file extension from filename
  getExtension: (filename: string): string => {
    const parts = filename.split('.')
    return parts.length > 1 ? `.${parts.pop()?.toLowerCase()}` : ''
  },
  
  // Validate file before upload
  validateFile: (
    file: File,
    category: keyof typeof FILE_LIMITS.ALLOWED_TYPES,
    maxSizeKey: keyof typeof FILE_LIMITS.MAX_SIZES = 'ATTACHMENT'
  ): { valid: boolean; error?: string } => {
    // Check file size
    const maxSize = FILE_LIMITS.MAX_SIZES[maxSizeKey]
    if (!fileValidation.isValidSize(file.size, maxSize)) {
      return {
        valid: false,
        error: `File size exceeds maximum allowed size of ${fileValidation.formatFileSize(maxSize)}`
      }
    }
    
    // Check file type
    const allowedTypes = FILE_LIMITS.ALLOWED_TYPES[category]
    if (!fileValidation.isValidType(file.type, allowedTypes)) {
      const allowedExtensions = FILE_LIMITS.EXTENSIONS[category].join(', ')
      return {
        valid: false,
        error: `File type not allowed. Accepted types: ${allowedExtensions}`
      }
    }
    
    return { valid: true }
  },
  
  // Get appropriate size limit for file type
  getSizeLimit: (mimeType: string): number => {
    // Images
    if (FILE_LIMITS.ALLOWED_TYPES.IMAGE.includes(mimeType)) {
      return FILE_LIMITS.MAX_SIZES.IMAGE_GENERAL
    }
    
    // Documents
    if (FILE_LIMITS.ALLOWED_TYPES.DOCUMENT.includes(mimeType)) {
      return FILE_LIMITS.MAX_SIZES.DOCUMENT
    }
    
    // Ebooks
    if (FILE_LIMITS.ALLOWED_TYPES.EBOOK.includes(mimeType)) {
      return FILE_LIMITS.MAX_SIZES.EPUB
    }
    
    // Default
    return FILE_LIMITS.MAX_SIZES.ATTACHMENT
  },
}