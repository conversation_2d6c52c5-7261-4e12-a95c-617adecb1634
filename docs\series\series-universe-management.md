# Series & Universe Management Documentation

## Overview

BookScribe's Series and Universe Management system enables authors to create interconnected stories within shared worlds, maintain consistency across multiple books, and track character development throughout series. This system is essential for authors writing multi-book sagas, shared universes, or interconnected storylines.

## Architecture

### System Components

```mermaid
graph TB
    subgraph "Universe Layer"
        Universe[Universe Core]
        Rules[Universe Rules]
        Timeline[Master Timeline]
        Locations[Shared Locations]
    end
    
    subgraph "Series Layer"
        Series[Series Manager]
        Continuity[Continuity Tracker]
        CharacterArcs[Arc Manager]
        Books[Book Registry]
    end
    
    subgraph "Consistency Engine"
        Validator[Consistency Validator]
        Analyzer[Continuity Analyzer]
        Resolver[Conflict Resolver]
        Suggestions[Smart Suggestions]
    end
    
    subgraph "Data Layer"
        UniverseDB[(universes)]
        SeriesDB[(series)]
        ContinuityDB[(continuity_checks)]
        TimelineDB[(timeline_events)]
    end
    
    Universe --> Series
    Series --> Books
    Rules --> Validator
    Timeline --> Analyzer
    CharacterArcs --> Continuity
    
    Validator --> ContinuityDB
    Analyzer --> Suggestions
```

## Core Concepts

### 1. Universe Definition

#### Universe Structure
```typescript
interface Universe {
  id: string;
  name: string;
  description: string;
  created_by: string;
  rules: UniverseRules;
  timeline: MasterTimeline;
  locations: SharedLocation[];
  species: Species[];
  magic_systems?: MagicSystem[];
  technology_level: TechnologyLevel;
  physics_rules: PhysicsRules;
  shared_elements: SharedElement[];
}

interface UniverseRules {
  magic: {
    exists: boolean;
    system?: MagicSystem;
    limitations: string[];
    cost: string;
  };
  technology: {
    level: 'primitive' | 'medieval' | 'modern' | 'futuristic' | 'custom';
    restrictions: string[];
    unique_tech: Technology[];
  };
  physics: {
    earth_like: boolean;
    modifications: PhysicsModification[];
  };
  society: {
    government_types: GovernmentType[];
    economic_systems: EconomicSystem[];
    social_structures: SocialStructure[];
  };
}
```

#### Universe Creation
```typescript
class UniverseManager {
  async createUniverse(data: CreateUniverseInput): Promise<Universe> {
    // Validate universe rules for internal consistency
    const validation = await this.validateUniverseRules(data.rules);
    if (!validation.isValid) {
      throw new UniverseValidationError(validation.errors);
    }
    
    // Create universe with foundational elements
    const universe = await supabase.from('universes').insert({
      name: data.name,
      description: data.description,
      rules: data.rules,
      created_by: data.userId,
      settings: {
        allow_modifications: data.allowModifications,
        require_approval: data.requireApproval,
        track_canon: true
      }
    }).select().single();
    
    // Initialize timeline
    await this.initializeTimeline(universe.id);
    
    // Set up consistency tracking
    await this.setupConsistencyTracking(universe.id);
    
    return universe;
  }
}
```

### 2. Series Management

#### Series Structure
```typescript
interface Series {
  id: string;
  title: string;
  description: string;
  universe_id: string;
  author_id: string;
  planned_books: number;
  current_book: number;
  overarching_plot: PlotStructure;
  series_arc: SeriesArc;
  recurring_characters: Character[];
  central_conflict: Conflict;
  themes: Theme[];
  reading_order: ReadingOrder;
}

interface SeriesArc {
  setup_books: number[];
  rising_action_books: number[];
  climax_book: number;
  resolution_books: number[];
  character_arcs: CharacterSeriesArc[];
  plot_threads: PlotThread[];
}

interface ReadingOrder {
  type: 'linear' | 'branching' | 'standalone' | 'custom';
  required_order: number[];
  optional_books: number[];
  entry_points: number[];
}
```

#### Series Creation & Planning
```typescript
class SeriesPlanner {
  async planSeries(input: SeriesPlanInput): Promise<SeriesPlan> {
    // Analyze overarching story
    const storyAnalysis = await this.analyzeOverarchingStory(input);
    
    // Distribute plot across books
    const plotDistribution = this.distributePlot(
      storyAnalysis,
      input.plannedBooks
    );
    
    // Plan character arcs
    const characterArcs = this.planCharacterArcs(
      input.characters,
      input.plannedBooks
    );
    
    // Create series timeline
    const timeline = this.createSeriesTimeline(
      plotDistribution,
      input.timeSpan
    );
    
    return {
      plotDistribution,
      characterArcs,
      timeline,
      bookOutlines: this.generateBookOutlines(plotDistribution),
      continuityCheckpoints: this.identifyCheckpoints(plotDistribution)
    };
  }
  
  private distributePlot(
    story: StoryAnalysis,
    bookCount: number
  ): PlotDistribution {
    // Identify major plot points
    const majorPoints = story.plotPoints.filter(p => p.importance === 'major');
    
    // Distribute across books
    const distribution: PlotDistribution = {
      books: []
    };
    
    for (let i = 0; i < bookCount; i++) {
      distribution.books[i] = {
        bookNumber: i + 1,
        plotPoints: this.allocatePlotPoints(majorPoints, i, bookCount),
        subplots: this.allocateSubplots(story.subplots, i, bookCount),
        characterFocus: this.determineCharacterFocus(i, bookCount),
        themes: this.distributeThemes(story.themes, i, bookCount)
      };
    }
    
    return distribution;
  }
}
```

### 3. Continuity Tracking

#### Continuity System
```typescript
interface ContinuityTracker {
  trackElement(element: ContinuityElement): Promise<void>;
  validateContinuity(context: ValidationContext): Promise<ValidationResult>;
  findInconsistencies(scope: ContinuityScope): Promise<Inconsistency[]>;
  suggestFixes(inconsistency: Inconsistency): Promise<Fix[]>;
}

interface ContinuityElement {
  id: string;
  type: 'character' | 'location' | 'event' | 'object' | 'rule';
  name: string;
  first_appearance: BookReference;
  properties: ElementProperties;
  history: ElementHistory[];
  relationships: ElementRelationship[];
}

class ContinuityEngine {
  async validateCharacterContinuity(
    character: Character,
    books: Book[]
  ): Promise<CharacterContinuityReport> {
    const issues: ContinuityIssue[] = [];
    
    // Track character state across books
    let characterState = character.initialState;
    
    for (const book of books) {
      const appearances = await this.getCharacterAppearances(
        character.id,
        book.id
      );
      
      for (const appearance of appearances) {
        // Check physical consistency
        if (!this.isPhysicallyConsistent(characterState, appearance)) {
          issues.push({
            type: 'physical_inconsistency',
            book: book.id,
            chapter: appearance.chapter,
            description: this.describeInconsistency(characterState, appearance)
          });
        }
        
        // Check knowledge consistency
        const knowledgeIssues = this.checkKnowledgeConsistency(
          characterState.knowledge,
          appearance.demonstrates_knowledge
        );
        issues.push(...knowledgeIssues);
        
        // Update state
        characterState = this.updateCharacterState(characterState, appearance);
      }
    }
    
    return {
      character,
      issues,
      timeline: this.buildCharacterTimeline(character, books),
      suggestions: await this.generateSuggestions(issues)
    };
  }
}
```

#### Continuity Rules
```typescript
interface ContinuityRules {
  character: {
    physical: {
      track: ['age', 'appearance', 'injuries', 'modifications'];
      validate: CharacterPhysicalValidator;
    };
    knowledge: {
      track: ['learned_information', 'relationships', 'experiences'];
      validate: CharacterKnowledgeValidator;
    };
    personality: {
      track: ['traits', 'beliefs', 'goals', 'fears'];
      validate: CharacterPersonalityValidator;
      allow_growth: boolean;
    };
  };
  
  world: {
    geography: {
      immutable: ['continents', 'oceans', 'mountain_ranges'];
      mutable: ['cities', 'borders', 'infrastructure'];
      validate: GeographyValidator;
    };
    history: {
      fixed_events: Event[];
      validate: HistoryValidator;
    };
    rules: {
      physics: PhysicsRules;
      magic: MagicRules;
      validate: RulesValidator;
    };
  };
}
```

### 4. Timeline Management

#### Master Timeline
```typescript
interface MasterTimeline {
  universe_id: string;
  events: TimelineEvent[];
  eras: Era[];
  calendar_system: CalendarSystem;
  parallel_timelines?: ParallelTimeline[];
}

interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  date: TimelineDate;
  duration?: Duration;
  location: Location;
  participants: Character[];
  impact: {
    scope: 'local' | 'regional' | 'global' | 'universal';
    severity: 'minor' | 'moderate' | 'major' | 'catastrophic';
    affected_elements: string[];
  };
  book_references: BookReference[];
  canon_status: 'canon' | 'disputed' | 'alternate';
}

class TimelineManager {
  async addEvent(event: TimelineEventInput): Promise<TimelineEvent> {
    // Validate event placement
    const conflicts = await this.checkTimelineConflicts(event);
    if (conflicts.length > 0) {
      throw new TimelineConflictError(conflicts);
    }
    
    // Check character availability
    const characterIssues = await this.validateCharacterPresence(
      event.participants,
      event.date,
      event.location
    );
    if (characterIssues.length > 0) {
      throw new CharacterTimelineError(characterIssues);
    }
    
    // Insert event
    const timelineEvent = await this.insertTimelineEvent(event);
    
    // Update affected elements
    await this.updateAffectedElements(timelineEvent);
    
    // Trigger continuity check
    await this.triggerContinuityCheck(timelineEvent);
    
    return timelineEvent;
  }
  
  async validateTimeline(
    timeline: MasterTimeline
  ): Promise<TimelineValidation> {
    const issues: TimelineIssue[] = [];
    
    // Check for paradoxes
    issues.push(...await this.checkParadoxes(timeline));
    
    // Validate character lifespans
    issues.push(...await this.validateLifespans(timeline));
    
    // Check travel time feasibility
    issues.push(...await this.validateTravelTimes(timeline));
    
    // Verify cause and effect
    issues.push(...await this.validateCausality(timeline));
    
    return {
      isValid: issues.length === 0,
      issues,
      suggestions: await this.generateTimelineFixes(issues)
    };
  }
}
```

### 5. Character Arc Management

#### Cross-Book Character Arcs
```typescript
interface CharacterSeriesArc {
  character_id: string;
  arc_type: 'redemption' | 'corruption' | 'growth' | 'discovery' | 'custom';
  starting_point: CharacterState;
  milestones: ArcMilestone[];
  ending_point: CharacterState;
  book_progression: BookProgression[];
}

interface ArcMilestone {
  book_number: number;
  chapter_range?: [number, number];
  description: string;
  state_change: StateChange;
  required_events: string[];
  impact_on_plot: string;
}

class CharacterArcManager {
  async planSeriesArc(
    character: Character,
    seriesLength: number,
    arcType: ArcType
  ): Promise<CharacterSeriesArc> {
    // Define arc trajectory
    const trajectory = this.defineArcTrajectory(character, arcType);
    
    // Create milestones
    const milestones = this.createArcMilestones(
      trajectory,
      seriesLength
    );
    
    // Validate arc feasibility
    const validation = await this.validateArcFeasibility(
      character,
      milestones
    );
    
    if (!validation.isFeasible) {
      // Adjust arc to be feasible
      milestones = await this.adjustArcMilestones(
        milestones,
        validation.issues
      );
    }
    
    return {
      character_id: character.id,
      arc_type: arcType,
      starting_point: character.currentState,
      milestones,
      ending_point: this.projectEndState(character, milestones),
      book_progression: this.mapToBooks(milestones, seriesLength)
    };
  }
  
  async trackArcProgress(
    arc: CharacterSeriesArc,
    currentBook: number,
    currentChapter: number
  ): Promise<ArcProgress> {
    const progress = {
      percentage: this.calculateProgress(arc, currentBook, currentChapter),
      currentMilestone: this.getCurrentMilestone(arc, currentBook),
      nextMilestone: this.getNextMilestone(arc, currentBook),
      deviations: await this.detectDeviations(arc, currentBook),
      suggestions: []
    };
    
    if (progress.deviations.length > 0) {
      progress.suggestions = await this.generateArcCorrections(
        arc,
        progress.deviations
      );
    }
    
    return progress;
  }
}
```

### 6. Cross-Reference System

#### Reference Management
```typescript
interface CrossReference {
  id: string;
  source: {
    book_id: string;
    chapter: number;
    paragraph: number;
  };
  target: {
    book_id: string;
    chapter: number;
    event: string;
  };
  type: 'foreshadowing' | 'callback' | 'parallel' | 'contradiction';
  description: string;
  importance: 'minor' | 'moderate' | 'major';
}

class ReferenceManager {
  async createReference(
    source: ReferencePoint,
    target: ReferencePoint,
    type: ReferenceType
  ): Promise<CrossReference> {
    // Validate reference
    const validation = await this.validateReference(source, target, type);
    
    if (!validation.isValid) {
      throw new InvalidReferenceError(validation.errors);
    }
    
    // Create reference
    const reference = await this.insertReference({
      source,
      target,
      type,
      description: await this.generateDescription(source, target, type)
    });
    
    // Update reference maps
    await this.updateReferenceMaps(reference);
    
    // Check for reference loops
    await this.checkReferenceLoops(reference);
    
    return reference;
  }
  
  async findBrokenReferences(
    seriesId: string
  ): Promise<BrokenReference[]> {
    const references = await this.getAllReferences(seriesId);
    const broken: BrokenReference[] = [];
    
    for (const ref of references) {
      // Check if target still exists
      const targetExists = await this.verifyTarget(ref.target);
      
      if (!targetExists) {
        broken.push({
          reference: ref,
          issue: 'target_missing',
          suggestion: await this.suggestFix(ref)
        });
      }
      
      // Check if reference still makes sense
      const stillValid = await this.validateContext(ref);
      
      if (!stillValid) {
        broken.push({
          reference: ref,
          issue: 'context_changed',
          suggestion: await this.suggestContextFix(ref)
        });
      }
    }
    
    return broken;
  }
}
```

## Features

### 1. Universe Consistency Checker

#### Automated Validation
```typescript
class UniverseConsistencyChecker {
  async performFullCheck(
    universeId: string
  ): Promise<ConsistencyReport> {
    const universe = await this.getUniverse(universeId);
    const books = await this.getAllBooks(universeId);
    
    const report: ConsistencyReport = {
      universe,
      checkDate: new Date(),
      categories: {}
    };
    
    // Check physical laws consistency
    report.categories.physics = await this.checkPhysicsConsistency(
      universe.rules.physics,
      books
    );
    
    // Check magic system consistency
    if (universe.rules.magic.exists) {
      report.categories.magic = await this.checkMagicConsistency(
        universe.rules.magic,
        books
      );
    }
    
    // Check timeline consistency
    report.categories.timeline = await this.checkTimelineConsistency(
      universe.timeline,
      books
    );
    
    // Check character consistency
    report.categories.characters = await this.checkCharacterConsistency(
      universe,
      books
    );
    
    // Check geographical consistency
    report.categories.geography = await this.checkGeographyConsistency(
      universe.locations,
      books
    );
    
    // Generate fix suggestions
    report.suggestions = await this.generateFixSuggestions(report);
    
    return report;
  }
}
```

### 2. Series Planning Tools

#### Series Outliner
```typescript
class SeriesOutliner {
  async createSeriesOutline(
    concept: SeriesConcept
  ): Promise<SeriesOutline> {
    // Generate overarching plot
    const overarchingPlot = await this.generateOverarchingPlot(concept);
    
    // Create book breakdowns
    const bookBreakdowns = await this.createBookBreakdowns(
      overarchingPlot,
      concept.plannedBooks
    );
    
    // Plan character journeys
    const characterJourneys = await this.planCharacterJourneys(
      concept.mainCharacters,
      bookBreakdowns
    );
    
    // Identify connection points
    const connectionPoints = this.identifyConnectionPoints(bookBreakdowns);
    
    // Create reading paths
    const readingPaths = this.generateReadingPaths(
      bookBreakdowns,
      concept.seriesType
    );
    
    return {
      concept,
      overarchingPlot,
      bookBreakdowns,
      characterJourneys,
      connectionPoints,
      readingPaths,
      timeline: await this.generateSeriesTimeline(bookBreakdowns)
    };
  }
}
```

### 3. Character Journey Tracker

#### Visual Journey Mapping
```typescript
class CharacterJourneyMapper {
  async mapCharacterJourney(
    characterId: string,
    seriesId: string
  ): Promise<CharacterJourney> {
    const appearances = await this.getCharacterAppearances(
      characterId,
      seriesId
    );
    
    const journey: CharacterJourney = {
      character: await this.getCharacter(characterId),
      books: [],
      totalArc: {
        start: null,
        end: null,
        milestones: []
      },
      relationships: {
        formed: [],
        broken: [],
        evolved: []
      },
      growth: {
        skills: [],
        knowledge: [],
        personality: []
      }
    };
    
    // Build journey through each book
    for (const appearance of appearances) {
      const bookJourney = await this.analyzeBookAppearance(appearance);
      journey.books.push(bookJourney);
      
      // Track relationships
      journey.relationships = this.updateRelationships(
        journey.relationships,
        bookJourney.relationships
      );
      
      // Track growth
      journey.growth = this.updateGrowth(
        journey.growth,
        bookJourney.changes
      );
    }
    
    // Calculate total arc
    journey.totalArc = this.calculateTotalArc(journey.books);
    
    return journey;
  }
}
```

## Best Practices

### Universe Design
1. **Start with Core Rules**: Define physics, magic, and technology first
2. **Document Everything**: Keep detailed universe bible
3. **Leave Room for Growth**: Don't over-specify initially
4. **Consider Implications**: Every rule has consequences
5. **Maintain Consistency**: Regular consistency checks

### Series Planning
1. **Plan the Ending First**: Know where you're going
2. **Character Arcs Matter**: Plan character growth across books
3. **Leave Breadcrumbs**: Set up future books early
4. **Maintain Momentum**: Each book should advance overall plot
5. **Standalone Satisfaction**: Each book needs its own arc

### Continuity Management
1. **Track from the Start**: Don't wait until book 2
2. **Use Timeline Tools**: Visual timelines prevent errors
3. **Character Sheets**: Maintain detailed character states
4. **Regular Reviews**: Check continuity after each book
5. **Beta Reader Focus**: Have readers check for inconsistencies

## Troubleshooting

### Common Issues

1. **Timeline Conflicts**
   - Problem: Character in two places at once
   - Solution: Adjust timeline or add travel time

2. **Character Age Inconsistencies**
   - Problem: Character ages don't match timeline
   - Solution: Use age calculator tool

3. **Power Creep**
   - Problem: Characters become too powerful
   - Solution: Establish clear power ceilings

4. **Forgotten Plot Threads**
   - Problem: Subplot abandoned between books
   - Solution: Use thread tracking system

5. **World Rule Violations**
   - Problem: Breaking established universe rules
   - Solution: Regular rule validation checks

## Future Enhancements

### Planned Features
1. **AI Continuity Assistant**: Proactive inconsistency detection
2. **Visual Timeline Builder**: Drag-and-drop timeline creation
3. **Character Relationship Graphs**: Interactive relationship mapping
4. **Universe Simulator**: Test rule implications
5. **Reader Journey Tracker**: Track reader experience paths

### Integration Goals
- Connect with fan wikis
- Import from other writing tools
- Export to series marketing tools
- Collaborative universe building
- Fan theory integration