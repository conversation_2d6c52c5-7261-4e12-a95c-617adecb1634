# Supabase Client Migration Guide

## Overview
We're migrating from legacy database clients to a unified Supabase helper for better performance and type safety.

## Old Pattern (Deprecated)
```typescript
import { supabase } from '@/lib/db/client'

// Direct usage
const { data } = await supabase.from('projects').select('*')
```

## New Pattern (Recommended)

### For Client Components
```typescript
import { createBrowserClient } from '@/lib/supabase'

export function MyComponent() {
  const supabase = createBrowserClient()
  
  const fetchData = async () => {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      
    if (error) {
      // Handle error using our error handler
      const { message } = handleSupabaseError(error)
      toast.error(message)
    }
  }
}
```

### For Server Components
```typescript
import { createServerClient } from '@/lib/supabase'

export default async function Page() {
  const supabase = await createServerClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select('*')
    
  if (error) {
    // Handle error
    const { message } = handleSupabaseError(error)
    return <ErrorMessage message={message} />
  }
  
  return <ProjectList projects={data} />
}
```

### For API Routes
```typescript
import { createServerClient } from '@/lib/supabase'
import { handleSupabaseError, withRetry } from '@/lib/supabase/errors'

export async function GET(request: Request) {
  const supabase = await createServerClient()
  
  try {
    // Use retry for critical operations
    const { data, error } = await withRetry(
      () => supabase.from('projects').select('*'),
      { maxRetries: 3 }
    )
    
    if (error) throw error
    
    return NextResponse.json({ data })
  } catch (error) {
    const errorResponse = handleSupabaseError(error)
    return NextResponse.json(
      { error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
```

## Key Benefits

1. **SSR Optimization**: Server components get fresh auth tokens automatically
2. **Type Safety**: Full TypeScript support with generated types
3. **Error Handling**: Unified error handling with user-friendly messages
4. **Connection Monitoring**: Built-in connection state tracking
5. **Retry Logic**: Automatic retry for transient failures
6. **Performance**: Connection pooling and rate limiting

## Migration Checklist

- [ ] Update all imports from `@/lib/db/client` to `@/lib/supabase`
- [ ] Replace direct `supabase` usage with `createBrowserClient()` or `createServerClient()`
- [ ] Add error handling using `handleSupabaseError`
- [ ] Add retry logic for critical operations
- [ ] Update RLS policies to remove redundant user_id checks
- [ ] Generate TypeScript types from Supabase CLI
- [ ] Test all CRUD operations
- [ ] Monitor connection status in production

## Common Patterns

### Realtime Subscriptions
```typescript
const supabase = createBrowserClient()

useEffect(() => {
  const channel = supabase
    .channel(`project:${projectId}`)
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'projects',
      filter: `id=eq.${projectId}`
    }, (payload) => {
      // Handle realtime updates
    })
    .on('system', { event: 'error' }, (error) => {
      console.error('Subscription error:', error)
      // Implement reconnection logic
    })
    .subscribe()
    
  return () => {
    supabase.removeChannel(channel)
  }
}, [projectId])
```

### File Upload with Progress
```typescript
const uploadFile = async (file: File) => {
  const supabase = createBrowserClient()
  
  const { data, error } = await supabase.storage
    .from('documents')
    .upload(`${projectId}/${file.name}`, file, {
      cacheControl: '3600',
      upsert: false,
      onUploadProgress: (progress) => {
        const percent = (progress.loaded / progress.total) * 100
        setUploadProgress(percent)
      }
    })
    
  if (error) {
    const { message } = handleSupabaseError(error)
    throw new Error(message)
  }
  
  return data
}
```

## Next Steps

1. Run `npx supabase gen types typescript --project-id your-project-id > src/lib/db/database.types.ts`
2. Update `src/lib/db/types.ts` to export the generated types
3. Gradually migrate components and API routes
4. Remove the old client file once migration is complete