'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  Plus,
  Layers,
  Copy,
  Settings,
  ArrowRight
} from 'lucide-react'

interface EmptyTemplatesProps {
  isAdmin?: boolean
}

export function EmptyTemplates({ isAdmin = false }: EmptyTemplatesProps) {
  return (
    <div className="max-w-4xl mx-auto py-12">
      <Card className="border-2 border-dashed border-muted-foreground/20">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/10 flex items-center justify-center">
            <FileText className="w-8 h-8 text-primary" />
          </div>
          
          <h2 className="text-2xl font-literary-display text-foreground mb-4">
            No Templates Available
          </h2>
          
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto">
            {isAdmin 
              ? "Create project templates to help users get started with pre-configured story structures."
              : "No templates have been created yet. Start with a blank project or check back later for templates."
            }
          </p>
          
          {isAdmin && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                    <Layers className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-medium mb-2">Genre Templates</h3>
                  <p className="text-sm text-muted-foreground">
                    Create templates for different genres and styles
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                    <Copy className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-medium mb-2">Reusable Structures</h3>
                  <p className="text-sm text-muted-foreground">
                    Save time with pre-built story frameworks
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center">
                    <Settings className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-medium mb-2">Custom Settings</h3>
                  <p className="text-sm text-muted-foreground">
                    Configure AI prompts and generation settings
                  </p>
                </div>
              </div>
              
              <Button 
                size="lg" 
                className="group"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create First Template
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </>
          )}
          
          {!isAdmin && (
            <Button 
              size="lg" 
              href="/projects/new"
              className="group"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create New Project
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  )
}