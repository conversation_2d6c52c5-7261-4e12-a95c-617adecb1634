# BookScribe Operations Guide

## Overview

This guide covers the operational aspects of BookScribe including deployment strategies, performance optimization, monitoring, scaling, and incident response. It serves as a comprehensive reference for DevOps engineers and system administrators managing BookScribe in production.

## Deployment Architecture

### Infrastructure Overview

```mermaid
graph TB
    subgraph "Frontend (Vercel)"
        NextJS[Next.js App]
        EdgeFunctions[Edge Functions]
        CDN[Global CDN]
    end
    
    subgraph "Backend (Supabase)"
        PostgreSQL[(PostgreSQL DB)]
        RealtimeEngine[Realtime Engine]
        EdgeFunctionsDB[Edge Functions]
        Storage[Object Storage]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Stripe[Stripe]
        EmailService[Email Service]
        Sentry[Sentry]
    end
    
    CDN --> NextJS
    NextJS --> PostgreSQL
    NextJS --> RealtimeEngine
    NextJS --> OpenAI
    NextJS --> Stripe
```

### Deployment Environments

#### Development
```yaml
Environment: Development
URL: http://localhost:3000
Database: Local Supabase or Docker
Features: Hot reload, debug mode, mock services
```

#### Staging
```yaml
Environment: Staging
URL: https://staging.bookscribe.ai
Database: Staging Supabase project
Features: Production-like, test data, preview deploys
```

#### Production
```yaml
Environment: Production
URL: https://bookscribe.ai
Database: Production Supabase project
Features: Full monitoring, auto-scaling, CDN
```

## Vercel Deployment

### Configuration

```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "functions": {
    // Long-running AI functions (5 min timeout)
    "src/app/api/agents/*/route.ts": {
      "maxDuration": 300
    },
    // Standard API routes (1 min timeout)
    "src/app/api/*/route.ts": {
      "maxDuration": 60
    }
  },
  "env": {
    "NODE_OPTIONS": "--max-old-space-size=4096"
  },
  "regions": ["iad1"], // US East for low latency
  "git": {
    "deploymentEnabled": true
  }
}
```

### Deployment Process

#### Automatic Deployments
```bash
# Production deployment (main branch)
git push origin main

# Preview deployment (feature branches)
git push origin feature/new-feature
```

#### Manual Deployment
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to production
vercel --prod

# Deploy to preview
vercel

# Deploy with specific environment
vercel --env production
```

### Environment Variables

```bash
# Production variables in Vercel dashboard
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=xxx
SUPABASE_SERVICE_ROLE_KEY=xxx
OPENAI_API_KEY=sk-xxx
STRIPE_SECRET_KEY=sk_live_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
SENTRY_DSN=https://<EMAIL>/xxx
```

## Performance Optimization

### Build Optimization

#### Next.js Configuration
```javascript
// next.config.js
module.exports = {
  // Enable SWC minification
  swcMinify: true,
  
  // Optimize images
  images: {
    domains: ['supabase.co'],
    formats: ['image/avif', 'image/webp'],
  },
  
  // Compress assets
  compress: true,
  
  // Generate sitemap
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap'
      }
    ]
  },
  
  // Bundle analyzer (dev only)
  webpack: (config, { isServer }) => {
    if (process.env.ANALYZE === 'true') {
      const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
      config.plugins.push(new BundleAnalyzerPlugin())
    }
    return config
  }
}
```

#### Bundle Size Optimization
```bash
# Analyze bundle size
ANALYZE=true npm run build

# Check bundle size limits
npm run build:analyze
```

### Runtime Performance

#### API Route Optimization
```typescript
// Implement caching for expensive operations
import { unstable_cache } from 'next/cache'

const getCachedAnalytics = unstable_cache(
  async (userId: string) => {
    return await calculateExpensiveAnalytics(userId)
  },
  ['analytics'],
  {
    revalidate: 3600, // 1 hour
    tags: ['analytics']
  }
)

// Use streaming for large responses
export async function GET(request: Request) {
  const stream = new ReadableStream({
    async start(controller) {
      for await (const chunk of generateLargeContent()) {
        controller.enqueue(chunk)
      }
      controller.close()
    }
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
      'Transfer-Encoding': 'chunked'
    }
  })
}
```

#### Database Query Optimization
```sql
-- Add indexes for common queries
CREATE INDEX idx_chapters_project_user 
ON chapters(project_id, user_id);

CREATE INDEX idx_ai_generations_user_created 
ON ai_generations(user_id, created_at DESC);

-- Use materialized views for complex aggregations
CREATE MATERIALIZED VIEW user_statistics AS
SELECT 
  user_id,
  COUNT(DISTINCT project_id) as project_count,
  SUM(word_count) as total_words,
  MAX(updated_at) as last_activity
FROM chapters
GROUP BY user_id;

-- Refresh periodically
REFRESH MATERIALIZED VIEW CONCURRENTLY user_statistics;
```

### Frontend Performance

#### Code Splitting
```typescript
// Dynamic imports for heavy components
const MonacoEditor = dynamic(
  () => import('@/components/editor/monaco-editor'),
  { 
    loading: () => <EditorSkeleton />,
    ssr: false 
  }
)

// Route-based code splitting (automatic with App Router)
// Each route gets its own bundle
```

#### Image Optimization
```typescript
import Image from 'next/image'

// Optimized image loading
<Image
  src="/hero-image.jpg"
  alt="Hero"
  width={1200}
  height={600}
  priority // Load immediately for above-fold
  placeholder="blur"
  blurDataURL={blurDataUrl}
/>
```

#### Performance Monitoring
```typescript
// Web Vitals tracking
export function reportWebVitals(metric: NextWebVitalsMetric) {
  if (metric.label === 'web-vital') {
    // Send to analytics
    analytics.track('Web Vital', {
      metric: metric.name,
      value: Math.round(metric.value),
      rating: metric.rating
    })
  }
}
```

## Monitoring & Observability

### Application Monitoring

#### Sentry Configuration
```typescript
// sentry.client.config.ts
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
  
  integrations: [
    new Sentry.Replay({
      maskAllText: false,
      blockAllMedia: false,
    }),
  ],
  
  beforeSend(event, hint) {
    // Filter out non-critical errors
    if (event.exception) {
      const error = hint.originalException
      if (error?.message?.includes('ResizeObserver')) {
        return null
      }
    }
    return event
  }
})
```

#### Custom Error Tracking
```typescript
// Track specific errors with context
export function trackError(
  error: Error,
  context: Record<string, any>
) {
  Sentry.captureException(error, {
    tags: {
      component: context.component,
      action: context.action
    },
    extra: context
  })
}

// Track performance issues
export function trackPerformance(
  operation: string,
  duration: number
) {
  if (duration > PERFORMANCE_THRESHOLD[operation]) {
    Sentry.captureMessage(
      `Slow operation: ${operation}`,
      'warning',
      {
        extra: { duration, operation }
      }
    )
  }
}
```

### Infrastructure Monitoring

#### Health Checks
```typescript
// app/api/health/route.ts
export async function GET() {
  const checks = {
    api: 'healthy',
    database: 'unknown',
    redis: 'unknown',
    openai: 'unknown'
  }
  
  // Check database
  try {
    await supabase.from('health_check').select('id').single()
    checks.database = 'healthy'
  } catch {
    checks.database = 'unhealthy'
  }
  
  // Check OpenAI
  try {
    await openai.models.list()
    checks.openai = 'healthy'
  } catch {
    checks.openai = 'unhealthy'
  }
  
  const allHealthy = Object.values(checks).every(v => v === 'healthy')
  
  return NextResponse.json(
    { 
      status: allHealthy ? 'healthy' : 'degraded',
      checks,
      timestamp: new Date().toISOString()
    },
    { status: allHealthy ? 200 : 503 }
  )
}
```

#### Monitoring Dashboard
```typescript
// Custom monitoring endpoint
export async function GET() {
  const [
    activeUsers,
    aiUsage,
    errorRate,
    responseTime
  ] = await Promise.all([
    getActiveUserCount(),
    getAIUsageMetrics(),
    getErrorRate(),
    getAverageResponseTime()
  ])
  
  return NextResponse.json({
    metrics: {
      activeUsers,
      aiUsage,
      errorRate,
      responseTime,
      timestamp: Date.now()
    }
  })
}
```

### Logging Strategy

#### Structured Logging
```typescript
// lib/services/logger.ts
export const logger = {
  info: (message: string, meta?: any) => {
    console.log(JSON.stringify({
      level: 'info',
      message,
      ...meta,
      timestamp: new Date().toISOString()
    }))
  },
  
  error: (message: string, error?: Error, meta?: any) => {
    console.error(JSON.stringify({
      level: 'error',
      message,
      error: error?.message,
      stack: error?.stack,
      ...meta,
      timestamp: new Date().toISOString()
    }))
  },
  
  metric: (name: string, value: number, tags?: Record<string, string>) => {
    console.log(JSON.stringify({
      level: 'metric',
      metric: name,
      value,
      tags,
      timestamp: new Date().toISOString()
    }))
  }
}
```

## Scaling Strategy

### Horizontal Scaling

#### Vercel Auto-Scaling
```yaml
# Automatic scaling based on traffic
- Serverless functions scale to 0 when idle
- Auto-scales up to handle load
- Regional edge functions for global distribution
- Automatic load balancing
```

#### Database Scaling
```sql
-- Connection pooling configuration
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';

-- Read replicas for heavy queries
-- Configure in Supabase dashboard
```

### Caching Strategy

#### Edge Caching
```typescript
// Cache static assets at edge
export const config = {
  runtime: 'edge',
}

export async function GET(request: Request) {
  return new Response('Static content', {
    headers: {
      'Cache-Control': 'public, max-age=3600, s-maxage=86400',
      'CDN-Cache-Control': 'max-age=86400'
    }
  })
}
```

#### Redis Caching (Future)
```typescript
// Implement Redis for session and data caching
import Redis from 'ioredis'

const redis = new Redis(process.env.REDIS_URL)

export async function getCachedData(key: string) {
  const cached = await redis.get(key)
  if (cached) return JSON.parse(cached)
  
  const data = await fetchExpensiveData()
  await redis.setex(key, 3600, JSON.stringify(data))
  
  return data
}
```

## Security Operations

### Security Headers
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const response = NextResponse.next()
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
  )
  
  return response
}
```

### Rate Limiting
```typescript
// lib/rate-limiter.ts
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, '10 s'),
  analytics: true,
})

export async function rateLimitCheck(
  identifier: string
): Promise<{ success: boolean; limit: number; remaining: number }> {
  const { success, limit, remaining } = await ratelimit.limit(identifier)
  return { success, limit, remaining }
}
```

## Incident Response

### Incident Levels

#### Level 1: Critical
- Complete service outage
- Data loss or corruption
- Security breach
- Payment processing failure

#### Level 2: Major
- Partial service degradation
- Performance issues affecting >50% users
- Third-party service outages

#### Level 3: Minor
- Individual feature failures
- Performance issues affecting <50% users
- Non-critical third-party issues

### Response Procedures

#### 1. Detection
```yaml
Monitoring Alerts:
- Uptime monitoring (5-minute checks)
- Error rate threshold (>5% triggers alert)
- Response time threshold (>2s triggers alert)
- Failed payment webhook
```

#### 2. Communication
```yaml
Internal:
- Slack: #incidents channel
- PagerDuty: On-call rotation
- Email: <EMAIL>

External:
- Status page: status.bookscribe.ai
- Twitter: @bookscribe_ai
- Email: Affected users only
```

#### 3. Resolution Steps
```bash
# 1. Assess impact
curl https://bookscribe.ai/api/health

# 2. Check recent deployments
vercel list --prod

# 3. Rollback if needed
vercel rollback [deployment-url]

# 4. Check logs
vercel logs --prod

# 5. Scale resources if needed
# Adjust in Vercel dashboard
```

#### 4. Post-Mortem Template
```markdown
## Incident Post-Mortem

**Date**: YYYY-MM-DD
**Duration**: XX minutes
**Impact**: XX users affected
**Severity**: Level X

### Timeline
- HH:MM - Issue detected
- HH:MM - Investigation started
- HH:MM - Root cause identified
- HH:MM - Fix deployed
- HH:MM - Service restored

### Root Cause
[Detailed explanation]

### Resolution
[Steps taken to resolve]

### Action Items
- [ ] Preventive measure 1
- [ ] Preventive measure 2
- [ ] Monitoring improvement

### Lessons Learned
[Key takeaways]
```

## Backup & Recovery

### Database Backups

#### Automated Backups
```yaml
Supabase Backups:
- Daily automated backups (30-day retention)
- Point-in-time recovery (7 days)
- Cross-region backup replication
```

#### Manual Backup Process
```bash
# Export database
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Export specific tables
pg_dump $DATABASE_URL -t projects -t chapters > content_backup.sql

# Backup to S3
aws s3 cp backup_$(date +%Y%m%d).sql s3://bookscribe-backups/
```

### Recovery Procedures

#### Database Recovery
```bash
# Restore from backup
psql $DATABASE_URL < backup_20240115.sql

# Restore specific tables
psql $DATABASE_URL < content_backup.sql
```

#### Application Recovery
```bash
# Rollback to previous deployment
vercel rollback

# Deploy specific commit
vercel --prod --force --build-env COMMIT_SHA=abc123
```

## Performance Benchmarks

### Target Metrics

```yaml
Response Times:
- API endpoints: <200ms p95
- Page load: <3s (LCP)
- First paint: <1.5s (FCP)
- Time to interactive: <3.5s (TTI)

Availability:
- Uptime: 99.9% monthly
- Error rate: <0.1%
- Success rate: >99.9%

Scalability:
- Concurrent users: 10,000+
- Requests/second: 1,000+
- Database connections: 200 max
```

### Load Testing

```javascript
// k6 load test script
import http from 'k6/http'
import { check } from 'k6'

export const options = {
  stages: [
    { duration: '5m', target: 100 },
    { duration: '10m', target: 100 },
    { duration: '5m', target: 0 },
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],
    http_req_failed: ['rate<0.01'],
  },
}

export default function() {
  const res = http.get('https://bookscribe.ai/api/health')
  check(res, {
    'status is 200': (r) => r.status === 200,
  })
}
```

## Maintenance Procedures

### Scheduled Maintenance

#### Planning
```yaml
Schedule:
- Monthly security updates
- Quarterly dependency updates
- Annual infrastructure review

Timing:
- Weekend mornings (lowest traffic)
- 2-week advance notice
- 2-hour maintenance window
```

#### Maintenance Mode
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  if (process.env.MAINTENANCE_MODE === 'true') {
    return NextResponse.rewrite(new URL('/maintenance', request.url))
  }
}
```

### Emergency Procedures

#### Service Degradation
```typescript
// Graceful degradation for critical issues
export const config = {
  // Disable non-critical features
  features: {
    ai_generation: process.env.ENABLE_AI !== 'false',
    real_time: process.env.ENABLE_REALTIME !== 'false',
    analytics: process.env.ENABLE_ANALYTICS !== 'false'
  }
}
```

## Cost Optimization

### Resource Monitoring

```yaml
Track Monthly Costs:
- Vercel: Function invocations, bandwidth
- Supabase: Database size, bandwidth, functions
- OpenAI: Token usage per tier
- Stripe: Transaction fees
```

### Optimization Strategies

1. **Function Optimization**
   - Reduce cold starts
   - Optimize bundle sizes
   - Use edge functions where possible

2. **Database Optimization**
   - Regular vacuum and analyze
   - Archive old data
   - Optimize queries

3. **CDN Usage**
   - Cache static assets
   - Optimize images
   - Use regional edges

## Documentation & Runbooks

### Operational Runbooks

1. **Deployment Runbook**
2. **Incident Response Runbook**
3. **Database Maintenance Runbook**
4. **Security Patch Runbook**
5. **Scaling Runbook**

### Knowledge Base

- Architecture diagrams
- Service dependencies
- API documentation
- Troubleshooting guides
- Contact information

## Future Improvements

### Planned Enhancements

1. **Multi-Region Deployment**
   - Active-active configuration
   - Global database replication
   - Regional failover

2. **Enhanced Monitoring**
   - APM integration
   - Custom dashboards
   - Predictive alerts

3. **Automation**
   - Auto-scaling policies
   - Self-healing systems
   - Automated testing

4. **Security Enhancements**
   - WAF implementation
   - DDoS protection
   - Security scanning