import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { createAdminClient } from '@/lib/supabase/admin';
import { logger } from '@/lib/services/logger';

export const GET = UnifiedAuthService.withAuth(async (request, { params }) => {
  try {
    const user = request.user!;
    const exportId = params.id;

    if (!exportId) {
      return NextResponse.json(
        { error: 'Export ID required' },
        { status: 400 }
      );
    }

    const adminClient = createAdminClient();

    // Get the export record
    const { data: exportData, error } = await adminClient
      .from('gdpr_exports')
      .select('*')
      .eq('id', exportId)
      .eq('user_id', user.id)
      .single();

    if (error || !exportData) {
      logger.warn('GDPR export not found or unauthorized', {
        exportId,
        userId: user.id,
        error
      });
      
      return NextResponse.json(
        { error: 'Export not found or unauthorized' },
        { status: 404 }
      );
    }

    // Check if export has expired
    if (new Date(exportData.expires_at) < new Date()) {
      logger.warn('GDPR export expired', {
        exportId,
        userId: user.id,
        expiresAt: exportData.expires_at
      });
      
      return NextResponse.json(
        { error: 'Export link has expired. Please request a new export.' },
        { status: 410 }
      );
    }

    // Decrypt the data (in production, use proper decryption)
    const decryptedData = Buffer.from(exportData.data, 'base64').toString('utf8');
    const parsedData = JSON.parse(decryptedData);

    // Log the download
    logger.info('GDPR export downloaded', {
      exportId,
      userId: user.id,
      dataSize: decryptedData.length
    });

    // Mark as downloaded
    await adminClient
      .from('gdpr_exports')
      .update({
        downloaded_at: new Date().toISOString(),
        download_count: (exportData.download_count || 0) + 1
      })
      .eq('id', exportId);

    // Return the data as a downloadable file
    const filename = `bookscribe-data-export-${user.id}-${new Date().toISOString().split('T')[0]}.json`;

    return new NextResponse(JSON.stringify(parsedData, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'X-Export-Format': 'json',
        'X-Export-Version': '1.0'
      }
    });

  } catch (error) {
    logger.error('Failed to download GDPR export', {
      error,
      exportId: params.id,
      userId: request.user?.id
    });

    return NextResponse.json(
      { error: 'Failed to download export' },
      { status: 500 }
    );
  }
});