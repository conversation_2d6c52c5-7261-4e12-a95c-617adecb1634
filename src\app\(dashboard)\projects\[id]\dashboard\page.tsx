'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { LoadingSkeleton } from '@/components/ui/loading-skeleton'

interface ProjectDashboardPageProps {
  params: {
    id: string;
  };
}

export default function ProjectDashboardPage({ params }: ProjectDashboardPageProps) {
  const router = useRouter()
  
  useEffect(() => {
    // Redirect to the main project page
    router.replace(`/projects/${params.id}`)
  }, [params.id, router])
  
  // Show loading state while redirecting
  return (
    <div className="container-wide mx-auto p-6">
      <LoadingSkeleton variant="card" count={1} />
    </div>
  )
}