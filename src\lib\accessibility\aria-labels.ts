/**
 * ARIA Labels Configuration
 * Centralized ARIA labels for consistent accessibility across the application
 */

export const ARIA_LABELS = {
  // Navigation
  navigation: {
    mainMenu: 'Main navigation menu',
    sidebar: 'Application sidebar',
    breadcrumb: 'Breadcrumb navigation',
    pagination: 'Pagination controls',
    backToTop: 'Back to top',
    skipToContent: 'Skip to main content',
    userMenu: 'User account menu',
    settingsMenu: 'Settings menu',
  },

  // Forms
  forms: {
    email: 'Email address',
    password: 'Password',
    confirmPassword: 'Confirm password',
    rememberMe: 'Remember me on this device',
    search: 'Search',
    searchProjects: 'Search projects',
    searchChapters: 'Search chapters',
    searchCharacters: 'Search characters',
    submit: 'Submit form',
    cancel: 'Cancel',
    save: 'Save changes',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create new',
    upload: 'Upload file',
    download: 'Download',
  },

  // Editor
  editor: {
    mainEditor: 'Main text editor',
    chapterTitle: 'Chapter title',
    chapterContent: 'Chapter content',
    wordCount: 'Word count',
    characterCount: 'Character count',
    saveButton: 'Save document',
    autoSaveIndicator: 'Auto-save status',
    focusMode: 'Toggle focus mode',
    fullscreen: 'Toggle fullscreen',
    formatting: 'Text formatting options',
    bold: 'Bold',
    italic: 'Italic',
    underline: 'Underline',
    heading: 'Heading level',
    bulletList: 'Bullet list',
    numberedList: 'Numbered list',
    blockquote: 'Block quote',
    link: 'Insert link',
    image: 'Insert image',
  },

  // Projects
  projects: {
    projectCard: 'Project card',
    createProject: 'Create new project',
    deleteProject: 'Delete project',
    editProject: 'Edit project details',
    projectSettings: 'Project settings',
    exportProject: 'Export project',
    shareProject: 'Share project',
    projectStatus: 'Project status',
    projectProgress: 'Project progress',
  },

  // Characters
  characters: {
    characterList: 'Character list',
    createCharacter: 'Create new character',
    editCharacter: 'Edit character',
    deleteCharacter: 'Delete character',
    characterProfile: 'Character profile',
    characterRelationships: 'Character relationships',
    characterArc: 'Character arc',
    voiceProfile: 'Voice profile',
  },

  // AI Features
  ai: {
    generateContent: 'Generate AI content',
    stopGeneration: 'Stop AI generation',
    regenerate: 'Regenerate content',
    aiSuggestions: 'AI suggestions',
    acceptSuggestion: 'Accept suggestion',
    rejectSuggestion: 'Reject suggestion',
    aiAssistant: 'AI writing assistant',
    voiceAnalysis: 'Voice consistency analysis',
    qualityScore: 'Content quality score',
  },

  // Collaboration
  collaboration: {
    inviteCollaborator: 'Invite collaborator',
    shareLink: 'Copy share link',
    collaboratorList: 'Active collaborators',
    connectionStatus: 'Collaboration connection status',
    syncStatus: 'Synchronization status',
    presenceIndicator: 'User presence indicator',
    cursorPosition: 'Collaborator cursor position',
  },

  // Analytics
  analytics: {
    dashboard: 'Analytics dashboard',
    writingStats: 'Writing statistics',
    progressChart: 'Progress chart',
    goalTracking: 'Goal tracking',
    exportAnalytics: 'Export analytics data',
    timeRange: 'Select time range',
    metricType: 'Select metric type',
  },

  // Settings
  settings: {
    themeToggle: 'Toggle theme',
    darkMode: 'Toggle dark mode',
    fontSize: 'Adjust font size',
    fontFamily: 'Select font family',
    lineHeight: 'Adjust line height',
    autoSave: 'Toggle auto-save',
    notifications: 'Toggle notifications',
    privacy: 'Privacy settings',
    accessibility: 'Accessibility settings',
  },

  // Modals and Dialogs
  modals: {
    close: 'Close dialog',
    minimize: 'Minimize dialog',
    maximize: 'Maximize dialog',
    confirmAction: 'Confirm action',
    cancelAction: 'Cancel action',
    dialogTitle: 'Dialog window',
  },

  // Status and Feedback
  status: {
    loading: 'Loading',
    success: 'Success message',
    error: 'Error message',
    warning: 'Warning message',
    info: 'Information message',
    progress: 'Progress indicator',
    complete: 'Complete',
    pending: 'Pending',
  },

  // Tables
  tables: {
    sortAscending: 'Sort ascending',
    sortDescending: 'Sort descending',
    filter: 'Filter table',
    selectAll: 'Select all items',
    selectRow: 'Select row',
    expandRow: 'Expand row details',
    collapseRow: 'Collapse row details',
    actionsMenu: 'Row actions menu',
  },

  // Media
  media: {
    playVideo: 'Play video',
    pauseVideo: 'Pause video',
    muteAudio: 'Mute audio',
    unmuteAudio: 'Unmute audio',
    fullscreenVideo: 'Fullscreen video',
    closeFullscreen: 'Exit fullscreen',
    imageAlt: 'Image description',
  },
};

/**
 * Get ARIA label for a specific key path
 * @param path - Dot notation path (e.g., 'forms.email')
 * @param fallback - Fallback label if path not found
 */
export function getAriaLabel(path: string, fallback?: string): string {
  const keys = path.split('.');
  let current: any = ARIA_LABELS;
  
  for (const key of keys) {
    if (current[key] === undefined) {
      return fallback || path;
    }
    current = current[key];
  }
  
  return current || fallback || path;
}

/**
 * ARIA label builder for dynamic content
 */
export const ariaLabelBuilder = {
  // Dynamic project labels
  project: (name: string) => `Project: ${name}`,
  projectCard: (name: string, status: string) => `${name} project, status: ${status}`,
  projectProgress: (percent: number) => `Project progress: ${percent}% complete`,
  
  // Dynamic character labels
  character: (name: string) => `Character: ${name}`,
  characterCard: (name: string, role: string) => `${name}, ${role}`,
  
  // Dynamic chapter labels
  chapter: (number: number, title?: string) => 
    title ? `Chapter ${number}: ${title}` : `Chapter ${number}`,
  chapterProgress: (words: number, target: number) => 
    `Chapter progress: ${words} of ${target} words`,
  
  // Dynamic status labels
  saveStatus: (status: 'saving' | 'saved' | 'error') => {
    switch (status) {
      case 'saving': return 'Saving changes...';
      case 'saved': return 'All changes saved';
      case 'error': return 'Error saving changes';
    }
  },
  
  connectionStatus: (connected: boolean) => 
    connected ? 'Connected to server' : 'Disconnected from server',
  
  // Dynamic count labels
  count: (item: string, count: number) => 
    `${count} ${item}${count !== 1 ? 's' : ''}`,
  
  selected: (count: number, total: number) => 
    `${count} of ${total} selected`,
  
  // Dynamic time labels
  timeAgo: (time: string) => `Last updated ${time}`,
  duration: (minutes: number) => `Duration: ${minutes} minutes`,
  
  // Dynamic action labels
  deleteItem: (item: string) => `Delete ${item}`,
  editItem: (item: string) => `Edit ${item}`,
  createItem: (item: string) => `Create new ${item}`,
  shareItem: (item: string) => `Share ${item}`,
  exportItem: (item: string) => `Export ${item}`,
};

/**
 * Screen reader only text utility
 * Note: This returns a string with a class name. Use in JSX components.
 */
export function srOnly(text: string): string {
  return text; // The actual sr-only class should be applied in the component
}

/**
 * Announce to screen readers
 */
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div');
  announcement.setAttribute('role', 'status');
  announcement.setAttribute('aria-live', priority);
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}