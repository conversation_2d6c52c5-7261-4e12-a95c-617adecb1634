import { 
  EmailTemplate, 
  EmailContent, 
  IEmailTemplateEngine,
  WelcomeEmailData,
  PasswordResetEmailData,
  SubscriptionEmailData,
  WritingMilestoneEmailData,
  CollaborationInviteEmailData,
  WeeklyProgressEmailData
} from '../types'
import { baseStyles } from './styles'
import * as templates from './index'
import { TIME_MS } from '@/lib/constants'
import { SIZE_LIMITS } from '@/lib/constants'

/**
 * Email Template Engine
 * Renders email templates with data
 */
type TemplateDataMap = {
  'welcome': WelcomeEmailData
  'password-reset': PasswordResetEmailData
  'password-reset-confirmation': PasswordResetEmailData
  'subscription-confirmation': SubscriptionEmailData
  'subscription-cancelled': SubscriptionEmailData
  'subscription-updated': SubscriptionEmailData
  'writing-milestone': WritingMilestoneEmailData
  'collaboration-invitation': CollaborationInviteEmailData
  'collaboration-accepted': CollaborationInviteEmailData
  'collaboration-removed': CollaborationInviteEmailData
  'weekly-progress-report': WeeklyProgressEmailData
  'project-export-ready': { userName: string; email: string; projectTitle: string; downloadUrl: string; expiresIn: string }
  'ai-credits-low': { userName: string; email: string; remainingCredits: number; usagePercentage: number; upgradeUrl: string }
  'ai-credits-depleted': { userName: string; email: string; upgradeUrl: string }
}

export class EmailTemplateEngine implements IEmailTemplateEngine {
  private templates: Map<EmailTemplate, (data: TemplateDataMap[EmailTemplate]) => EmailContent> = new Map()

  constructor() {
    this.registerTemplates()
  }

  async render(template: EmailTemplate, data: Record<string, unknown>): Promise<EmailContent> {
    const templateFn = this.templates.get(template)
    
    if (!templateFn) {
      throw new Error(`Unknown email template: ${template}`)
    }

    try {
      const content = templateFn(data)
      return {
        html: this.wrapInLayout(content.html, data),
        text: content.text || this.stripHtml(content.html),
        amp: content.amp
      }
    } catch (error) {
      throw new Error(`Failed to render template ${template}: ${(error as Error).message}`)
    }
  }

  async previewTemplate(template: EmailTemplate, data?: Record<string, unknown>): Promise<EmailContent> {
    const sampleData = data || this.getSampleData(template)
    return this.render(template, sampleData)
  }

  getAvailableTemplates(): EmailTemplate[] {
    return Array.from(this.templates.keys())
  }

  validateTemplateData(template: EmailTemplate, data: Record<string, unknown>): boolean {
    try {
      // Attempt to render the template with the data
      const templateFn = this.templates.get(template)
      if (!templateFn) return false
      
      templateFn(data)
      return true
    } catch {
      return false
    }
  }

  private registerTemplates() {
    this.templates.set('welcome', (data: WelcomeEmailData) => templates.welcomeTemplate(data))
    this.templates.set('password-reset', (data: PasswordResetEmailData) => templates.passwordResetTemplate(data))
    this.templates.set('password-reset-confirmation', (data: PasswordResetEmailData) => templates.passwordResetConfirmationTemplate(data))
    this.templates.set('subscription-confirmation', (data: SubscriptionEmailData) => templates.subscriptionConfirmationTemplate(data))
    this.templates.set('subscription-cancelled', (data: SubscriptionEmailData) => templates.subscriptionCancelledTemplate(data))
    this.templates.set('subscription-updated', (data: SubscriptionEmailData) => templates.subscriptionUpdatedTemplate(data))
    this.templates.set('writing-milestone', (data: WritingMilestoneEmailData) => templates.writingMilestoneTemplate(data))
    this.templates.set('collaboration-invitation', (data: CollaborationInviteEmailData) => templates.collaborationInviteTemplate(data))
    this.templates.set('collaboration-accepted', (data: CollaborationInviteEmailData) => templates.collaborationAcceptedTemplate(data))
    this.templates.set('collaboration-removed', (data: CollaborationInviteEmailData) => templates.collaborationRemovedTemplate(data))
    this.templates.set('weekly-progress-report', (data: WeeklyProgressEmailData) => templates.weeklyProgressTemplate(data))
    this.templates.set('project-export-ready', (data) => templates.projectExportReadyTemplate(data))
    this.templates.set('ai-credits-low', (data) => templates.aiCreditsLowTemplate(data))
    this.templates.set('ai-credits-depleted', (data) => templates.aiCreditsDepletedTemplate(data))
  }

  private wrapInLayout(content: string, data: Record<string, unknown>): string {
    const currentYear = new Date().getFullYear()
    const unsubscribeUrl = data.unsubscribeUrl || 'https://bookscribe.ai/unsubscribe'
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>${data.subject || 'BookScribe AI'}</title>
  ${baseStyles}
</head>
<body>
  <div class="email-wrapper">
    <div class="email-container">
      ${content}
      
      <div class="footer">
        <p class="footer-text">
          © ${currentYear} BookScribe AI. All rights reserved.
        </p>
        <p class="footer-links">
          <a href="https://bookscribe.ai/privacy">Privacy Policy</a> |
          <a href="https://bookscribe.ai/terms">Terms of Service</a> |
          <a href="${unsubscribeUrl}">Unsubscribe</a>
        </p>
        <p class="footer-address">
          BookScribe AI<br>
          123 Writers Lane<br>
          San Francisco, CA 94105
        </p>
      </div>
    </div>
  </div>
</body>
</html>`
  }

  private stripHtml(html: string): string {
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'")
      .replace(/\s+/g, ' ')
      .trim()
  }

  private getSampleData(template: EmailTemplate): Record<string, unknown> {
    const baseData = {
      userName: 'John Doe',
      email: '<EMAIL>'
    }

    const sampleData: Record<EmailTemplate, Record<string, unknown>> = {
      'welcome': {
        ...baseData,
        loginUrl: 'https://bookscribe.ai/login',
        hasFreeTrial: true,
        trialDays: 14
      },
      'password-reset': {
        ...baseData,
        resetUrl: 'https://bookscribe.ai/reset-password?token=abc123',
        expiresIn: '1 hour',
        ipAddress: '***********',
        browserInfo: 'Chrome on Windows'
      },
      'password-reset-confirmation': {
        ...baseData,
        loginUrl: 'https://bookscribe.ai/login'
      },
      'subscription-confirmation': {
        ...baseData,
        planName: 'Professional',
        billingCycle: 'monthly',
        amount: 29.99,
        currency: 'USD',
        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * TIME_MS.SECOND),
        features: ['Unlimited Projects', 'AI Writing Tools', 'Export Options'],
        manageUrl: 'https://bookscribe.ai/account/subscription'
      },
      'subscription-cancelled': {
        ...baseData,
        planName: 'Professional',
        billingCycle: 'monthly',
        amount: 29.99,
        currency: 'USD',
        features: [],
        manageUrl: 'https://bookscribe.ai/account/subscription'
      },
      'subscription-updated': {
        ...baseData,
        planName: 'Enterprise',
        billingCycle: 'yearly',
        amount: 499.99,
        currency: 'USD',
        nextBillingDate: new Date(Date.now() + 365 * 24 * 60 * 60 * TIME_MS.SECOND),
        features: ['Everything in Pro', 'Priority Support', 'Custom Integrations'],
        manageUrl: 'https://bookscribe.ai/account/subscription'
      },
      'writing-milestone': {
        ...baseData,
        projectTitle: 'The Great Novel',
        milestone: '50,000 words',
        achievement: 'Novel Length Reached!',
        stats: {
          wordsWritten: SIZE_LIMITS.MAX_DOCUMENT_CHARS,
          chaptersCompleted: 20,
          timeSpent: '120 hours',
          currentStreak: 30
        },
        projectUrl: 'https://bookscribe.ai/projects/123'
      },
      'collaboration-invitation': {
        recipientEmail: '<EMAIL>',
        inviterName: 'Jane Smith',
        projectTitle: 'The Mystery Novel',
        role: 'editor',
        inviteUrl: 'https://bookscribe.ai/invite/abc123',
        expiresIn: '7 days',
        projectDescription: 'A thrilling mystery set in Victorian London'
      },
      'collaboration-accepted': {
        ...baseData,
        collaboratorName: 'Jane Smith',
        projectTitle: 'The Mystery Novel',
        role: 'editor',
        projectUrl: 'https://bookscribe.ai/projects/123'
      },
      'collaboration-removed': {
        ...baseData,
        projectTitle: 'The Mystery Novel',
        reason: 'Project completed'
      },
      'weekly-progress-report': {
        ...baseData,
        weekStartDate: new Date(Date.now() - 7 * 24 * 60 * 60 * TIME_MS.SECOND),
        weekEndDate: new Date(),
        projects: [{
          title: 'The Great Novel',
          wordsWritten: TIME_MS.TOAST_DURATION,
          chaptersEdited: 3,
          timeSpent: '10 hours',
          lastActiveDate: new Date(),
          url: 'https://bookscribe.ai/projects/123'
        }],
        totalWords: TIME_MS.TOAST_DURATION,
        totalTime: '10 hours',
        currentStreak: 7,
        achievements: ['Daily Writer', 'Chapter Master'],
        tips: ['Try writing at the same time each day', 'Set smaller daily goals']
      },
      'project-export-ready': {
        ...baseData,
        projectTitle: 'The Great Novel',
        exportFormat: 'PDF',
        downloadUrl: 'https://bookscribe.ai/downloads/abc123',
        expiresIn: '48 hours'
      },
      'ai-credits-low': {
        ...baseData,
        creditsRemaining: 100,
        estimatedDaysRemaining: 3,
        upgradeUrl: 'https://bookscribe.ai/account/upgrade'
      },
      'ai-credits-depleted': {
        ...baseData,
        upgradeUrl: 'https://bookscribe.ai/account/upgrade'
      }
    }

    return sampleData[template] || baseData
  }
}