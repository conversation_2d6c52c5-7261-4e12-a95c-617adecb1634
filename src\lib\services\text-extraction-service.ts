import { createServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'

interface ExtractionResult {
  success: boolean
  text?: string
  error?: string
  wordCount?: number
  pages?: number
}

export class TextExtractionService {
  /**
   * Extract text from a reference material
   */
  static async extractFromMaterial(materialId: string): Promise<ExtractionResult> {
    try {
      const supabase = await createServerClient()
      
      // Get material details
      const { data: material, error } = await supabase
        .from('reference_materials')
        .select('*')
        .eq('id', materialId)
        .single()

      if (error || !material) {
        return {
          success: false,
          error: 'Material not found'
        }
      }

      // Extract based on type
      switch (material.type) {
        case 'note':
          return this.extractFromNote(material)
        case 'url':
          return this.extractFromUrl(material)
        case 'document':
          return this.extractFromDocument(material)
        default:
          return {
            success: false,
            error: `Unsupported material type: ${material.type}`
          }
      }
    } catch (error) {
      logger.error('Text extraction error:', error)
      return {
        success: false,
        error: 'Extraction failed'
      }
    }
  }

  /**
   * Extract text from a note (already in content field)
   */
  private static async extractFromNote(material: any): Promise<ExtractionResult> {
    if (!material.content) {
      return {
        success: false,
        error: 'No content found in note'
      }
    }

    const text = material.content
    const wordCount = text.split(/\s+/).filter(Boolean).length

    return {
      success: true,
      text,
      wordCount
    }
  }

  /**
   * Extract text from a URL (fetch and extract)
   */
  private static async extractFromUrl(material: any): Promise<ExtractionResult> {
    if (!material.content) {
      return {
        success: false,
        error: 'No URL content stored'
      }
    }

    // For URLs, we expect the content to be pre-fetched and stored
    const text = material.content
    const wordCount = text.split(/\s+/).filter(Boolean).length

    return {
      success: true,
      text,
      wordCount
    }
  }

  /**
   * Extract text from a document file
   */
  private static async extractFromDocument(material: any): Promise<ExtractionResult> {
    const mimeType = material.mime_type

    // Handle plain text files
    if (mimeType === 'text/plain' || mimeType === 'text/markdown') {
      return this.extractFromTextFile(material)
    }

    // For other document types (PDF, DOCX, etc.), we would need
    // specialized libraries or services
    return {
      success: false,
      error: `Document type ${mimeType} extraction not yet implemented`
    }
  }

  /**
   * Extract text from plain text file
   */
  private static async extractFromTextFile(material: any): Promise<ExtractionResult> {
    try {
      if (!material.file_url) {
        return {
          success: false,
          error: 'No file URL found'
        }
      }

      // Fetch the file content
      const response = await fetch(material.file_url)
      if (!response.ok) {
        return {
          success: false,
          error: 'Failed to fetch file'
        }
      }

      const text = await response.text()
      const wordCount = text.split(/\s+/).filter(Boolean).length

      // Store extracted text in the content field
      const supabase = await createServerClient()
      await supabase
        .from('reference_materials')
        .update({
          content: text,
          is_processed: true,
          processing_status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', material.id)

      return {
        success: true,
        text,
        wordCount
      }
    } catch (error) {
      logger.error('Text file extraction error:', error)
      return {
        success: false,
        error: 'Failed to extract text from file'
      }
    }
  }

  /**
   * Process all pending materials for a project
   */
  static async processPendingMaterials(projectId: string): Promise<void> {
    try {
      const supabase = await createServerClient()
      
      // Get all unprocessed materials
      const { data: materials, error } = await supabase
        .from('reference_materials')
        .select('*')
        .eq('project_id', projectId)
        .eq('is_processed', false)
        .in('type', ['document', 'note', 'url'])

      if (error || !materials) {
        logger.error('Failed to fetch pending materials:', error)
        return
      }

      // Process each material
      for (const material of materials) {
        logger.info(`Processing material: ${material.id} (${material.title})`)
        
        const result = await this.extractFromMaterial(material.id)
        
        if (result.success) {
          logger.info(`Successfully extracted text from ${material.id}: ${result.wordCount} words`)
        } else {
          logger.error(`Failed to extract text from ${material.id}: ${result.error}`)
          
          // Mark as failed
          await supabase
            .from('reference_materials')
            .update({
              processing_status: 'failed',
              updated_at: new Date().toISOString()
            })
            .eq('id', material.id)
        }
      }
    } catch (error) {
      logger.error('Material processing error:', error)
    }
  }
}