-- Update reference_materials table to match component expectations
BEGIN;

-- Add missing columns
ALTER TABLE reference_materials
ADD COLUMN IF NOT EXISTS type VARCHAR(50),
ADD COLUMN IF NOT EXISTS title VARCHAR(255),
ADD COLUMN IF NOT EXISTS content TEXT,
ADD COLUMN IF NOT EXISTS ai_summary TEXT;

-- Update existing data
UPDATE reference_materials 
SET type = COALESCE(file_type, 'document'),
    title = COALESCE(name, 'Untitled')
WHERE type IS NULL OR title IS NULL;

-- Make type NOT NULL after updating
ALTER TABLE reference_materials
ALTER COLUMN type SET NOT NULL;

-- Drop old columns if they exist
ALTER TABLE reference_materials
DROP COLUMN IF EXISTS name,
DROP COLUMN IF EXISTS file_type;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_reference_materials_type ON reference_materials(type);
CREATE INDEX IF NOT EXISTS idx_reference_materials_user_id ON reference_materials(user_id);

COMMIT;