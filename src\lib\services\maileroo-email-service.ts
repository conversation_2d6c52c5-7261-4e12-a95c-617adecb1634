import { createServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { config } from '@/lib/config/unified-env'

// Email template types (same as existing)
export const EmailTemplates = {
  WELCOME: 'welcome',
  ACHIEVEMENT_UNLOCKED: 'achievement_unlocked',
  GOAL_REMINDER: 'goal_reminder',
  COLLABORATION_INVITE: 'collaboration_invite',
  EXPORT_READY: 'export_ready',
  SUBSCRIPTION_RENEWED: 'subscription_renewed',
  SUBSCRIPTION_CANCELLED: 'subscription_cancelled',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification',
  WEEKLY_PROGRESS: 'weekly_progress',
  PROJECT_SHARED: 'project_shared',
  COMMENT_NOTIFICATION: 'comment_notification',
  ACCOUNT_DELETED: 'account_deleted'
} as const

export type EmailTemplate = typeof EmailTemplates[keyof typeof EmailTemplates]

// Email data schemas for each template
const emailDataSchemas = {
  welcome: z.object({
    userName: z.string(),
    loginUrl: z.string()
  }),
  achievement_unlocked: z.object({
    userName: z.string(),
    achievementName: z.string(),
    achievementDescription: z.string(),
    achievementTier: z.enum(['bronze', 'silver', 'gold', 'platinum'])
  }),
  goal_reminder: z.object({
    userName: z.string(),
    goalName: z.string(),
    currentProgress: z.number(),
    targetProgress: z.number(),
    dueDate: z.string()
  }),
  collaboration_invite: z.object({
    inviterName: z.string(),
    projectName: z.string(),
    inviteUrl: z.string(),
    role: z.enum(['viewer', 'editor', 'admin'])
  }),
  export_ready: z.object({
    userName: z.string(),
    projectName: z.string(),
    exportFormat: z.string(),
    downloadUrl: z.string(),
    expiresAt: z.string()
  }),
  subscription_renewed: z.object({
    userName: z.string(),
    planName: z.string(),
    nextBillingDate: z.string(),
    amount: z.number()
  }),
  subscription_cancelled: z.object({
    userName: z.string(),
    planName: z.string(),
    expiresAt: z.string()
  }),
  password_reset: z.object({
    resetUrl: z.string()
  }),
  email_verification: z.object({
    verificationUrl: z.string()
  }),
  weekly_progress: z.object({
    userName: z.string(),
    wordsWritten: z.number(),
    chaptersCompleted: z.number(),
    writingStreak: z.number(),
    projectsWorkedOn: z.array(z.string())
  }),
  account_deleted: z.object({
    userName: z.string()
  }),
  project_shared: z.object({
    sharedByName: z.string(),
    projectName: z.string(),
    projectUrl: z.string(),
    message: z.string().optional()
  }),
  comment_notification: z.object({
    commenterName: z.string(),
    projectName: z.string(),
    chapterTitle: z.string(),
    commentPreview: z.string(),
    commentUrl: z.string()
  })
}

export type EmailData<T extends EmailTemplate> = z.infer<typeof emailDataSchemas[T]>

// Maileroo configuration
interface MailerooConfig {
  apiKey: string
  endpoint: string
  fromEmail: string
  fromName: string
}

// Email request interface for Maileroo
interface MailerooEmailRequest {
  from: {
    email: string
    name: string
  }
  to: string | string[]
  subject: string
  html: string
  text?: string
  reply_to?: string
  tags?: string[]
  tracking?: {
    opens: boolean
    clicks: boolean
  }
}

export class MailerooEmailService {
  private static instance: MailerooEmailService
  private config: MailerooConfig
  private initialized = false

  private constructor() {
    this.config = {
      apiKey: config.email?.apiKey || '',
      endpoint: config.email?.endpoint || '',
      fromEmail: config.email?.from || '',
      fromName: config.email?.fromName || ''
    }
  }

  static getInstance(): MailerooEmailService {
    if (!MailerooEmailService.instance) {
      MailerooEmailService.instance = new MailerooEmailService()
    }
    return MailerooEmailService.instance
  }

  private async initialize() {
    if (this.initialized) return

    if (!this.config.apiKey || !this.config.endpoint) {
      logger.error('Email service not properly configured')
      throw new Error('Email service not properly configured')
    }

    this.initialized = true
    logger.info('Maileroo email service initialized')
  }

  async sendEmail<T extends EmailTemplate>(
    to: string | string[],
    template: T,
    data: EmailData<T>,
    options?: {
      scheduledFor?: Date
      userId?: string
    }
  ): Promise<void> {
    try {
      await this.initialize()

      // Validate data against schema
      const schema = emailDataSchemas[template]
      if (schema) {
        schema.parse(data)
      }

      // Check email preferences if userId provided
      if (options?.userId) {
        const shouldSend = await this.checkEmailPreferences(options.userId, template)
        if (!shouldSend) {
          logger.info(`Email ${template} not sent due to user preferences`)
          return
        }
      }

      // Generate email content
      const { subject, html, text } = this.generateEmailContent(template, data)

      // Queue or send immediately
      if (options?.scheduledFor) {
        await this.queueEmail(to, template, data, options.scheduledFor)
      } else {
        await this.sendImmediately(to, subject, html, text, template)
      }

    } catch (error) {
      logger.error('Error sending email:', error)
      throw error
    }
  }

  private async sendImmediately(
    to: string | string[],
    subject: string,
    html: string,
    text: string,
    template: EmailTemplate
  ): Promise<void> {
    const request: MailerooEmailRequest = {
      from: {
        email: this.config.fromEmail,
        name: this.config.fromName
      },
      to: Array.isArray(to) ? to : [to],
      subject,
      html,
      text,
      tags: [template, 'bookscribe'],
      tracking: {
        opens: true,
        clicks: true
      }
    }

    try {
      const response = await fetch(this.config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.config.apiKey
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Maileroo API error: ${error}`)
      }

      const result = await response.json()
      logger.info('Email sent successfully via Maileroo', { messageId: result.message_id })
    } catch (error) {
      logger.error('Failed to send email via Maileroo:', error)
      throw error
    }
  }

  private generateEmailContent(template: EmailTemplate, data: any): {
    subject: string
    html: string
    text: string
  } {
    // Template content generation
    const templates = {
      welcome: {
        subject: 'Welcome to BookScribe AI!',
        html: `
          <h1>Welcome to BookScribe AI, ${data.userName}!</h1>
          <p>We're excited to have you join our community of authors.</p>
          <p>Start writing your masterpiece today!</p>
          <a href="${data.loginUrl}" style="display: inline-block; padding: 12px 24px; background-color: #6366f1; color: white; text-decoration: none; border-radius: 6px;">Get Started</a>
        `,
        text: `Welcome to BookScribe AI, ${data.userName}! Start writing at: ${data.loginUrl}`
      },
      password_reset: {
        subject: 'Reset Your BookScribe Password',
        html: `
          <h1>Password Reset Request</h1>
          <p>Click the link below to reset your password:</p>
          <a href="${data.resetUrl}" style="display: inline-block; padding: 12px 24px; background-color: #6366f1; color: white; text-decoration: none; border-radius: 6px;">Reset Password</a>
          <p>This link will expire in 1 hour.</p>
        `,
        text: `Reset your password: ${data.resetUrl}`
      },
      achievement_unlocked: {
        subject: `🎉 Achievement Unlocked: ${data.achievementName}!`,
        html: `
          <h1>Congratulations, ${data.userName}!</h1>
          <p>You've unlocked a ${data.achievementTier} achievement:</p>
          <h2>${data.achievementName}</h2>
          <p>${data.achievementDescription}</p>
        `,
        text: `Congratulations! You've unlocked ${data.achievementName}: ${data.achievementDescription}`
      },
      weekly_progress: {
        subject: 'Your Weekly Writing Progress',
        html: `
          <h1>Weekly Progress Report</h1>
          <p>Hi ${data.userName},</p>
          <p>Here's your writing progress for this week:</p>
          <ul>
            <li>Words Written: ${data.wordsWritten.toLocaleString()}</li>
            <li>Chapters Completed: ${data.chaptersCompleted}</li>
            <li>Writing Streak: ${data.writingStreak} days</li>
            <li>Projects: ${data.projectsWorkedOn.join(', ')}</li>
          </ul>
          <p>Keep up the great work!</p>
        `,
        text: `Weekly Progress: ${data.wordsWritten} words, ${data.chaptersCompleted} chapters, ${data.writingStreak} day streak`
      },
      account_deleted: {
        subject: 'Your BookScribe Account Has Been Deleted',
        html: `
          <h1>Account Deleted</h1>
          <p>Hi ${data.userName},</p>
          <p>Your BookScribe account and all associated data have been permanently deleted as requested.</p>
          <p>We're sorry to see you go. If you change your mind, you're always welcome back!</p>
        `,
        text: `Your BookScribe account has been deleted. We're sorry to see you go!`
      }
    }

    const template_content = templates[template as keyof typeof templates] || {
      subject: 'BookScribe Notification',
      html: '<p>You have a new notification from BookScribe.</p>',
      text: 'You have a new notification from BookScribe.'
    }

    // Wrap in base template
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            h1 { color: #6366f1; }
            a.button { display: inline-block; padding: 12px 24px; background-color: #6366f1; color: white; text-decoration: none; border-radius: 6px; }
          </style>
        </head>
        <body>
          <div class="container">
            ${template_content.html}
            <hr style="margin-top: 40px; border: none; border-top: 1px solid #e5e7eb;">
            <p style="font-size: 12px; color: #6b7280; text-align: center;">
              BookScribe AI - Your AI-Powered Writing Companion<br>
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe" style="color: #6b7280;">Unsubscribe</a>
            </p>
          </div>
        </body>
      </html>
    `

    return {
      subject: template_content.subject,
      html,
      text: template_content.text
    }
  }

  private async checkEmailPreferences(userId: string, template: EmailTemplate): Promise<boolean> {
    const supabase = await createServerClient()
    
    const { data: preferences } = await supabase
      .from('email_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (!preferences) return true // Default to sending if no preferences

    // Map templates to preference categories
    const preferenceMap: Record<EmailTemplate, keyof typeof preferences> = {
      welcome: 'marketing',
      achievement_unlocked: 'achievements',
      goal_reminder: 'progress',
      collaboration_invite: 'collaboration',
      export_ready: 'progress',
      subscription_renewed: 'marketing',
      subscription_cancelled: 'marketing',
      password_reset: 'marketing', // Always send
      email_verification: 'marketing', // Always send
      weekly_progress: 'progress',
      project_shared: 'collaboration',
      comment_notification: 'collaboration',
      account_deleted: 'marketing' // Always send
    }

    // Always send critical emails
    if (['password_reset', 'email_verification', 'account_deleted'].includes(template)) {
      return true
    }

    const category = preferenceMap[template]
    return preferences[category] === true
  }

  private async queueEmail(
    to: string | string[],
    template: EmailTemplate,
    data: any,
    scheduledFor: Date
  ): Promise<void> {
    const supabase = await createServerClient()
    
    const { error } = await supabase
      .from('email_queue')
      .insert({
        to: Array.isArray(to) ? to.join(',') : to,
        template,
        data,
        scheduled_for: scheduledFor,
        status: 'pending'
      })

    if (error) {
      logger.error('Error queueing email:', error)
      throw error
    }
  }

  async processEmailQueue(): Promise<void> {
    const supabase = await createServerClient()
    
    // Get pending emails scheduled for now or earlier
    const { data: emails, error } = await supabase
      .from('email_queue')
      .select('*')
      .eq('status', 'pending')
      .lte('scheduled_for', new Date().toISOString())
      .limit(10)

    if (error) {
      logger.error('Error fetching email queue:', error)
      return
    }

    for (const email of emails || []) {
      try {
        // Send the email
        await this.sendEmail(
          email.to.split(','),
          email.template,
          email.data
        )

        // Mark as sent
        await supabase
          .from('email_queue')
          .update({ 
            status: 'sent',
            sent_at: new Date().toISOString()
          })
          .eq('id', email.id)

      } catch (error) {
        logger.error(`Error processing queued email ${email.id}:`, error)
        
        // Update status to failed
        await supabase
          .from('email_queue')
          .update({ 
            status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unknown error',
            attempts: (email.attempts || 0) + 1
          })
          .eq('id', email.id)
      }
    }
  }

  // Batch send for multiple recipients
  async sendBulkEmail<T extends EmailTemplate>(
    recipients: Array<{ email: string; data: EmailData<T> }>,
    template: T,
    options?: {
      scheduledFor?: Date
      batchSize?: number
    }
  ): Promise<void> {
    const batchSize = options?.batchSize || 50
    
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize)
      
      await Promise.all(
        batch.map(recipient => 
          this.sendEmail(recipient.email, template, recipient.data, options)
        )
      )
      
      // Add delay between batches to avoid rate limits
      if (i + batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
  }

  // Get email statistics
  async getEmailStats(userId?: string): Promise<{
    sent: number
    failed: number
    pending: number
    opened: number
    clicked: number
  }> {
    const supabase = await createServerClient()
    
    let query = supabase.from('email_queue').select('status', { count: 'exact' })
    
    if (userId) {
      query = query.eq('user_id', userId)
    }
    
    const { data, error } = await query
    
    if (error) {
      logger.error('Error fetching email stats:', error)
      return { sent: 0, failed: 0, pending: 0, opened: 0, clicked: 0 }
    }
    
    // This would need to be integrated with Maileroo's webhook system for open/click tracking
    return {
      sent: data?.filter(e => e.status === 'sent').length || 0,
      failed: data?.filter(e => e.status === 'failed').length || 0,
      pending: data?.filter(e => e.status === 'pending').length || 0,
      opened: 0, // Would come from webhook data
      clicked: 0  // Would come from webhook data
    }
  }
}

// Export singleton instance
export const mailerooEmailService = MailerooEmailService.getInstance()