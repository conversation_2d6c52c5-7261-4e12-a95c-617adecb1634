import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyCollaborationAccess } from '@/lib/api/collaboration-middleware'
import { ServiceManager } from '@/lib/services/service-manager'
import { logger } from '@/lib/services/logger'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, section } = body

    if (!sessionId || !section) {
      return NextResponse.json(
        { error: 'Session ID and section are required' },
        { status: 400 }
      )
    }

    // Verify user has edit access to the collaboration session
    const authResult = await verifyCollaborationAccess(sessionId, 'edit')
    if (!authResult.success) {
      return authResult.response!
    }

    // Use the serverless collaboration hub
    const serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()
    
    const collaborationService = await serviceManager.getCollaborationHub()
    if (!collaborationService) {
      return NextResponse.json(
        { error: 'Collaboration service not available' },
        { status: 503 }
      )
    }

    const result = await collaborationService.lockSection(
      sessionId,
      authResult.user!.id,
      section
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to lock section' },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true, locked: true })
  } catch (error) {
    logger.error('Collaboration lock error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}