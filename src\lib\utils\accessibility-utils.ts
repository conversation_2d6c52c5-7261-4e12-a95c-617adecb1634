import { TIME_MS } from '@/lib/constants'

/**
 * Accessibility Utilities
 * Helper functions and constants for improved accessibility
 */

// ARIA live region politeness settings
export const ARIA_LIVE = {
  OFF: 'off',
  POLITE: 'polite',
  ASSERTIVE: 'assertive',
} as const

// Common ARIA roles
export const ARIA_ROLES = {
  NAVIGATION: 'navigation',
  MAIN: 'main',
  COMPLEMENTARY: 'complementary',
  BANNER: 'banner',
  CONTENTINFO: 'contentinfo',
  SEARCH: 'search',
  FORM: 'form',
  REGION: 'region',
  ALERT: 'alert',
  STATUS: 'status',
  DIALOG: 'dialog',
  ALERTDIALOG: 'alertdialog',
  BUTTON: 'button',
  LINK: 'link',
  MENU: 'menu',
  MENUITEM: 'menuitem',
  TAB: 'tab',
  TABLIST: 'tablist',
  TABPANEL: 'tabpanel',
} as const

// Screen reader only text utility
export const SR_ONLY = 'sr-only' as const

// Focus management utilities
export const FOCUS_CLASSES = {
  VISIBLE: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
  WITHIN: 'focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
} as const

// Keyboard navigation keys
export const KEYS = {
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  TAB: 'Tab',
} as const

// Helper function to generate unique IDs for ARIA relationships
let idCounter = 0
export function generateId(prefix = 'bookscribe'): string {
  return `${prefix}-${++idCounter}`
}

// Helper to create ARIA label from multiple sources
export function createAriaLabel(...labels: (string | undefined | null)[]): string | undefined {
  const validLabels = labels.filter((label): label is string => Boolean(label))
  return validLabels.length > 0 ? validLabels.join(', ') : undefined
}

// Helper to manage focus trap
export function trapFocus(element: HTMLElement) {
  const focusableElements = element.querySelectorAll<HTMLElement>(
    'a[href], button:not([disabled]), textarea:not([disabled]), input:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])'
  )
  
  const firstFocusable = focusableElements[0]
  const lastFocusable = focusableElements[focusableElements.length - 1]
  
  function handleKeyDown(e: KeyboardEvent) {
    if (e.key !== KEYS.TAB) return
    
    if (e.shiftKey) {
      if (document.activeElement === firstFocusable) {
        e.preventDefault()
        lastFocusable?.focus()
      }
    } else {
      if (document.activeElement === lastFocusable) {
        e.preventDefault()
        firstFocusable?.focus()
      }
    }
  }
  
  element.addEventListener('keydown', handleKeyDown)
  firstFocusable?.focus()
  
  return () => {
    element.removeEventListener('keydown', handleKeyDown)
  }
}

// Helper to announce to screen readers
export function announce(message: string, politeness: keyof typeof ARIA_LIVE = 'POLITE') {
  const announcement = document.createElement('div')
  announcement.setAttribute('role', 'status')
  announcement.setAttribute('aria-live', ARIA_LIVE[politeness])
  announcement.className = SR_ONLY
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, TIME_MS.SECOND)
}

// Helper to handle keyboard navigation
export function handleArrowKeyNavigation(
  e: React.KeyboardEvent,
  currentIndex: number,
  totalItems: number,
  onChange: (index: number) => void,
  orientation: 'horizontal' | 'vertical' = 'vertical'
) {
  const prevKey = orientation === 'vertical' ? KEYS.ARROW_UP : KEYS.ARROW_LEFT
  const nextKey = orientation === 'vertical' ? KEYS.ARROW_DOWN : KEYS.ARROW_RIGHT
  
  switch (e.key) {
    case prevKey:
      e.preventDefault()
      onChange(currentIndex > 0 ? currentIndex - 1 : totalItems - 1)
      break
    case nextKey:
      e.preventDefault()
      onChange(currentIndex < totalItems - 1 ? currentIndex + 1 : 0)
      break
    case KEYS.HOME:
      e.preventDefault()
      onChange(0)
      break
    case KEYS.END:
      e.preventDefault()
      onChange(totalItems - 1)
      break
  }
}

// Skip to content link component props
export interface SkipLinkProps {
  href?: string
  children?: React.ReactNode
}

// Accessible icon props
export interface AccessibleIconProps {
  label: string
  children: React.ReactNode
}

// Focus visible mixin for custom components
export const focusVisible = (additionalClasses = '') => `
  ${FOCUS_CLASSES.VISIBLE}
  ${additionalClasses}
`

// Reduced motion utilities
export const MOTION = {
  REDUCE: 'motion-reduce:transition-none motion-reduce:transform-none',
  SAFE: 'motion-safe:transition-all motion-safe:duration-200',
} as const

// Color contrast utilities
export const CONTRAST = {
  HIGH: 'contrast-more:border-2 contrast-more:font-bold',
  BORDERS: 'contrast-more:border-current',
} as const

// Helper to check if user prefers reduced motion
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

// Helper to format text for screen readers
export function formatForScreenReader(text: string, context?: string): string {
  // Remove special characters that might confuse screen readers
  let formatted = text.replace(/[^\w\s.,!?-]/g, ' ')
  
  // Add context if provided
  if (context) {
    formatted = `${context}: ${formatted}`
  }
  
  return formatted
}