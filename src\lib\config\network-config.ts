/**
 * Network Configuration
 * Centralized configuration for all network-related settings
 */

// Timeout configurations (in milliseconds)
export const TIMEOUTS = {
  // API request timeouts
  API: {
    DEFAULT: 30000, // 30 seconds
    SHORT: 5000, // 5 seconds
    MEDIUM: 15000, // 15 seconds
    LONG: 60000, // 1 minute
    UPLOAD: 300000, // 5 minutes for file uploads
    EXPORT: 600000, // 10 minutes for exports
    AI_GENERATION: 120000, // 2 minutes for AI generation
  },
  
  // WebSocket timeouts
  WEBSOCKET: {
    CONNECTION: 10000, // 10 seconds to establish connection
    HEARTBEAT: 30000, // 30 seconds heartbeat interval
    RECONNECT_DELAY: 1000, // 1 second initial reconnect delay
    MAX_RECONNECT_DELAY: 30000, // 30 seconds max reconnect delay
  },
  
  // UI timeouts
  UI: {
    DEBOUNCE: 300, // 300ms for input debouncing
    THROTTLE: 1000, // 1 second for throttling
    AUTOSAVE: 30000, // 30 seconds for autosave
    IDLE_WARNING: 1740000, // 29 minutes (warn before 30 min timeout)
    SESSION_TIMEOUT: 1800000, // 30 minutes session timeout
    TOAST_DURATION: 5000, // 5 seconds for toast notifications
  },
  
  // Polling intervals
  POLLING: {
    COLLABORATION: 5000, // 5 seconds for collaboration updates
    ANALYTICS: 60000, // 1 minute for analytics refresh
    NOTIFICATIONS: 30000, // 30 seconds for notifications
    HEALTH_CHECK: 300000, // 5 minutes for health checks
  },
} as const

// Retry configurations
export const RETRY_CONFIG = {
  // Default retry configuration
  DEFAULT: {
    MAX_ATTEMPTS: 3,
    INITIAL_DELAY: 1000, // 1 second
    MAX_DELAY: 10000, // 10 seconds
    BACKOFF_FACTOR: 2, // Exponential backoff multiplier
    JITTER: true, // Add random jitter to prevent thundering herd
  },
  
  // Specific retry configurations
  AI_REQUESTS: {
    MAX_ATTEMPTS: 5,
    INITIAL_DELAY: 2000, // 2 seconds
    MAX_DELAY: 30000, // 30 seconds
    BACKOFF_FACTOR: 2,
    JITTER: true,
  },
  
  UPLOAD: {
    MAX_ATTEMPTS: 3,
    INITIAL_DELAY: 5000, // 5 seconds
    MAX_DELAY: 60000, // 1 minute
    BACKOFF_FACTOR: 2,
    JITTER: false,
  },
  
  WEBSOCKET: {
    MAX_ATTEMPTS: 10,
    INITIAL_DELAY: 1000, // 1 second
    MAX_DELAY: 30000, // 30 seconds
    BACKOFF_FACTOR: 1.5,
    JITTER: true,
  },
} as const

// Rate limiting configurations
export const RATE_LIMITS = {
  // API rate limits (requests per window)
  API: {
    GLOBAL: { requests: 1000, window: 60000 }, // 1000 requests per minute
    AUTH: { requests: 5, window: 300000 }, // 5 auth attempts per 5 minutes
    AI_GENERATION: { requests: 100, window: 3600000 }, // 100 AI requests per hour
    EXPORT: { requests: 10, window: 3600000 }, // 10 exports per hour
    SEARCH: { requests: 30, window: 60000 }, // 30 searches per minute
  },
  
  // UI rate limits
  UI: {
    TYPING: { requests: 1, window: 300 }, // Debounce typing (300ms)
    SAVE: { requests: 1, window: 1000 }, // Throttle saves (1 second)
    SEARCH: { requests: 1, window: 500 }, // Debounce search (500ms)
  },
} as const

// Request configurations
export const REQUEST_CONFIG = {
  // Default headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // File upload headers
  UPLOAD_HEADERS: {
    // Content-Type will be set automatically for FormData
    'Accept': 'application/json',
  },
  
  // Streaming headers
  STREAMING_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream',
    'Cache-Control': 'no-cache',
  },
} as const

// Cache configurations
export const CACHE_CONFIG = {
  // Cache TTL (time to live) in milliseconds
  TTL: {
    SHORT: 60000, // 1 minute
    MEDIUM: 300000, // 5 minutes
    LONG: 3600000, // 1 hour
    DAY: 86400000, // 24 hours
  },
  
  // Cache keys
  KEYS: {
    USER_PROFILE: 'user-profile',
    PROJECTS: 'projects',
    TEMPLATES: 'templates',
    VOICE_PROFILES: 'voice-profiles',
    ANALYTICS: 'analytics',
  },
} as const

// Helper functions

/**
 * Calculate exponential backoff delay
 */
export const calculateBackoffDelay = (
  attempt: number,
  config: typeof RETRY_CONFIG[keyof typeof RETRY_CONFIG]
): number => {
  const baseDelay = config.INITIAL_DELAY * Math.pow(config.BACKOFF_FACTOR, attempt - 1)
  const delay = Math.min(baseDelay, config.MAX_DELAY)
  
  if (config.JITTER) {
    // Add random jitter (±25% of delay)
    const jitter = delay * 0.25
    return delay + (Math.random() * 2 - 1) * jitter
  }
  
  return delay
}

/**
 * Create a timeout promise
 */
export const withTimeout = <T>(
  promise: Promise<T>,
  timeoutMs: number,
  errorMessage = 'Request timed out'
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(errorMessage)), timeoutMs)
    ),
  ])
}

/**
 * Create a retry wrapper for async functions
 */
export const withRetry = async <T>(
  fn: () => Promise<T>,
  config: typeof RETRY_CONFIG[keyof typeof RETRY_CONFIG] = RETRY_CONFIG.DEFAULT
): Promise<T> => {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= config.MAX_ATTEMPTS; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === config.MAX_ATTEMPTS) {
        throw lastError
      }
      
      const delay = calculateBackoffDelay(attempt, config)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError || new Error('Retry failed')
}

/**
 * Check if a request should be retried based on the error
 */
export const shouldRetry = (error: unknown): boolean => {
  // Type guard for error with code property
  const errorWithCode = error as { code?: string }
  if (errorWithCode.code === 'ECONNABORTED' || errorWithCode.code === 'ETIMEDOUT') {
    return true
  }
  
  // Type guard for error with response property
  const errorWithResponse = error as { response?: { status?: number } }
  const retryableStatuses = [408, 429, 500, 502, 503, 504]
  if (errorWithResponse.response?.status && retryableStatuses.includes(errorWithResponse.response.status)) {
    return true
  }
  
  return false
}

/**
 * Parse retry-after header
 */
export const parseRetryAfter = (retryAfterHeader: string | null): number => {
  if (!retryAfterHeader) {
    return RETRY_CONFIG.DEFAULT.INITIAL_DELAY
  }
  
  // Check if it's a delay in seconds
  const seconds = parseInt(retryAfterHeader, 10)
  if (!isNaN(seconds)) {
    return seconds * 1000
  }
  
  // Check if it's an HTTP date
  const retryDate = new Date(retryAfterHeader)
  if (!isNaN(retryDate.getTime())) {
    const delay = retryDate.getTime() - Date.now()
    return Math.max(0, delay)
  }
  
  return RETRY_CONFIG.DEFAULT.INITIAL_DELAY
}