'use client'

import { useState, useEffect, useCallback } from 'react'
import { logger } from '@/lib/services/logger';
import { useDebounce } from '@/hooks/use-debounce'

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { PaginationControls } from '@/components/ui/pagination'
import { SeriesCard } from './series-card'
import { CreateSeriesModal } from './create-series-modal'
import { AddBookToSeriesModal } from './add-book-to-series-modal'
import { EditSeriesModal } from './edit-series-modal'
import { EmptyProjects, EmptySearch } from '@/components/empty-states'
import { 
  Plus, 
  FolderPlus, 
  Search, 
  AlertCircle,
  BookOpen,
  FileText
} from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useToast } from '@/hooks/use-toast'
import type { Series, GroupedProjectsData } from '@/types/series'

interface PaginationData {
  page: number
  seriesLimit: number
  standaloneLimit: number
  seriesPages: number
  standalonePages: number
  maxPages: number
  hasMore: boolean
}

interface ExtendedGroupedProjectsData extends GroupedProjectsData {
  pagination?: PaginationData
}

export function ProjectsWithSeries() {
  const [data, setData] = useState<ExtendedGroupedProjectsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const debouncedSearchQuery = useDebounce(searchQuery, 300)
  
  // Modal states
  const [createSeriesOpen, setCreateSeriesOpen] = useState(false)
  const [addBookModalOpen, setAddBookModalOpen] = useState(false)
  const [editSeriesModalOpen, setEditSeriesModalOpen] = useState(false)
  const [deleteSeriesOpen, setDeleteSeriesOpen] = useState(false)
  
  // Selected items for modals
  const [selectedSeriesId, setSelectedSeriesId] = useState<string | null>(null)
  const [selectedSeries, setSelectedSeries] = useState<Series | null>(null)
  
  const { toast } = useToast()

  useEffect(() => {
    fetchGroupedProjects()
  }, [currentPage, debouncedSearchQuery])

  const fetchGroupedProjects = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        seriesLimit: '10',
        standaloneLimit: '12',
        ...(debouncedSearchQuery && { search: debouncedSearchQuery })
      })

      const response = await fetch(`/api/projects/grouped?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch projects')
      }

      const result = await response.json()
      setData(result)
    } catch (err) {
      logger.error('Error fetching projects:', err);
      setError(err instanceof Error ? err.message : 'Failed to load projects')
    } finally {
      setLoading(false)
    }
  }, [currentPage, debouncedSearchQuery])

  const handleAddBook = (seriesId: string) => {
    const series = data?.series.find(s => s.id === seriesId)
    if (series) {
      setSelectedSeriesId(seriesId)
      setSelectedSeries(series)
      setAddBookModalOpen(true)
    }
  }

  const handleEditSeries = (series: Series) => {
    setSelectedSeries(series)
    setEditSeriesModalOpen(true)
  }

  const handleDeleteSeries = (seriesId: string) => {
    const series = data?.series.find(s => s.id === seriesId)
    if (series) {
      setSelectedSeriesId(seriesId)
      setSelectedSeries(series)
      setDeleteSeriesOpen(true)
    }
  }
  
  const confirmDeleteSeries = async () => {
    if (!selectedSeriesId) return
    
    try {
      const response = await fetch(`/api/series/${selectedSeriesId}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete series')
      }
      
      toast({
        title: "Success",
        description: "Series deleted successfully",
      })
      
      fetchGroupedProjects()
    } catch (error) {
      logger.error('Error deleting series:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete series",
        variant: "destructive",
      })
    } finally {
      setDeleteSeriesOpen(false)
      setSelectedSeriesId(null)
      setSelectedSeries(null)
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'planning': return 'secondary'
      case 'in_progress': return 'default'
      case 'completed': return 'success'
      default: return 'secondary'
    }
  }

  // Since filtering is now handled server-side, we can use data directly
  const filteredData = data

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    setCurrentPage(1) // Reset to first page when searching
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            className="ml-4"
            onClick={fetchGroupedProjects}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  if (!filteredData) {
    return null
  }

  const hasNoProjects = filteredData.stats.totalProjects === 0

  return (
    <div className="space-y-6 sm:space-y-8 lg:space-y-10">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold font-literary-display">Your Projects</h1>
          <p className="text-muted-foreground mt-1">
            {filteredData.stats.totalSeries} series, {filteredData.stats.totalProjects} books
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setCreateSeriesOpen(true)}
          >
            <FolderPlus className="h-4 w-4 mr-2" />
            New Series
          </Button>
          <Link href="/projects/new">
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Book
            </Button>
          </Link>
        </div>
      </div>

      {/* Search */}
      {!hasNoProjects && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-9"
          />
        </div>
      )}

      {hasNoProjects ? (
        <EmptyProjects />
      ) : filteredData.series.length === 0 && filteredData.standaloneProjects.length === 0 && searchQuery ? (
        <EmptySearch 
          searchQuery={searchQuery}
          onClearSearch={() => setSearchQuery('')}
        />
      ) : (
        <>
          {/* Series Section */}
          {filteredData.series.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-2">
                <FolderPlus className="h-5 w-5 text-muted-foreground" />
                <h2 className="text-xl font-semibold">Series</h2>
                <Badge variant="secondary">{filteredData.series.length}</Badge>
              </div>
              <div className="space-y-3">
                {filteredData.series.map((series) => (
                  <SeriesCard
                    key={series.id}
                    series={series}
                    onAddBook={handleAddBook}
                    onEditSeries={handleEditSeries}
                    onDeleteSeries={handleDeleteSeries}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Standalone Projects Section */}
          {filteredData.standalone.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-5 w-5 text-muted-foreground" />
                <h2 className="text-xl font-semibold">Standalone Books</h2>
                <Badge variant="secondary">{filteredData.standalone.length}</Badge>
              </div>
              <div className="grid gap-4 sm:gap-5 lg:gap-6 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 xl:grid-cols-4 2xl:grid-cols-5">
                {filteredData.standalone.map((project) => (
                  <Link key={project.id} href={`/projects/${project.id}/write`}>
                    <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-lg">{project.title}</CardTitle>
                          <Badge variant={getStatusBadgeVariant(project.status)}>
                            {project.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        {project.primary_genre && (
                          <Badge variant="outline" className="text-xs w-fit">
                            {project.primary_genre}
                          </Badge>
                        )}
                      </CardHeader>
                      <CardContent>
                        {project.description && (
                          <CardDescription className="line-clamp-2 mb-3">
                            {project.description}
                          </CardDescription>
                        )}
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>{project.current_word_count.toLocaleString()} words</span>
                          <span>/ {project.target_word_count.toLocaleString()}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* No results */}
          {filteredData.series.length === 0 && filteredData.standalone.length === 0 && searchQuery && (
            <Card className="text-center py-6 sm:py-8 lg:py-10">
              <CardContent>
                <p className="text-muted-foreground">No projects found matching "{searchQuery}"</p>
              </CardContent>
            </Card>
          )}

          {/* Pagination Controls */}
          {filteredData.pagination && filteredData.pagination.maxPages > 1 && (
            <PaginationControls
              currentPage={currentPage}
              totalPages={filteredData.pagination.maxPages}
              onPageChange={handlePageChange}
              showInfo={true}
              totalItems={filteredData.stats.totalProjects}
              itemsPerPage={filteredData.pagination.seriesLimit + filteredData.pagination.standaloneLimit}
              className="mt-8"
            />
          )}
        </>
      )}
      
      {/* Modals */}
      <CreateSeriesModal
        open={createSeriesOpen}
        onOpenChange={setCreateSeriesOpen}
        onSuccess={fetchGroupedProjects}
      />
      
      {selectedSeries && (
        <>
          <AddBookToSeriesModal
            open={addBookModalOpen}
            onOpenChange={setAddBookModalOpen}
            seriesId={selectedSeries.id}
            seriesTitle={selectedSeries.title}
            currentBookCount={selectedSeries.current_book_count}
            onSuccess={fetchGroupedProjects}
          />
          
          <EditSeriesModal
            open={editSeriesModalOpen}
            onOpenChange={setEditSeriesModalOpen}
            series={selectedSeries}
            onSuccess={fetchGroupedProjects}
          />
        </>
      )}
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteSeriesOpen} onOpenChange={setDeleteSeriesOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Series</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{selectedSeries?.title}"? 
              {selectedSeries?.books.length ? 
                `This series has ${selectedSeries.books.length} book(s). You must remove all books before deleting the series.` :
                'This action cannot be undone.'
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteSeries}
              disabled={!!(selectedSeries?.books.length)}
              className={selectedSeries?.books.length ? 'opacity-50 cursor-not-allowed' : ''}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}