import { BaseEmailProvider } from './base-provider'
import { EmailRequest, EmailResponse, EmailStatus } from '../types'
import { logger } from '../../services/logger'
import { TIME_MS } from '@/lib/constants'

interface SendGridConfig {
  apiKey: string
  defaultFrom: string
  defaultReplyTo?: string
  sandboxMode?: boolean
}

interface SendGridPersonalization {
  to: Array<{ email: string; name?: string }>
  cc?: Array<{ email: string; name?: string }>
  bcc?: Array<{ email: string; name?: string }>
  subject?: string
  headers?: Record<string, string>
  substitutions?: Record<string, string>
  custom_args?: Record<string, string>
  send_at?: number
}

interface SendGridMailData {
  personalizations: SendGridPersonalization[]
  from: { email: string; name?: string }
  reply_to?: { email: string; name?: string }
  subject: string
  content: Array<{ type: string; value: string }>
  attachments?: Array<{
    content: string
    filename: string
    type?: string
    disposition?: string
    content_id?: string
  }>
  categories?: string[]
  send_at?: number
  batch_id?: string
  asm?: {
    group_id: number
    groups_to_display?: number[]
  }
  ip_pool_name?: string
  mail_settings?: {
    sandbox_mode?: { enable: boolean }
    bypass_list_management?: { enable: boolean }
    footer?: { enable: boolean; text?: string; html?: string }
  }
  tracking_settings?: {
    click_tracking?: { enable: boolean; enable_text: boolean }
    open_tracking?: { enable: boolean; substitution_tag?: string }
    subscription_tracking?: { enable: boolean }
    ganalytics?: { enable: boolean }
  }
}

/**
 * SendGrid Email Provider
 * Documentation: https://docs.sendgrid.com/api-reference/mail-send/mail-send
 */
export class SendGridProvider extends BaseEmailProvider {
  private apiKey: string
  private defaultFrom: string
  private defaultReplyTo?: string
  private sandboxMode: boolean
  private endpoint = 'https://api.sendgrid.com/v3/mail/send'

  constructor(config: SendGridConfig) {
    super('sendgrid', config)
    this.apiKey = config.apiKey
    this.defaultFrom = config.defaultFrom
    this.defaultReplyTo = config.defaultReplyTo
    this.sandboxMode = config.sandboxMode || false
  }

  async send(request: EmailRequest): Promise<EmailResponse> {
    try {
      const mailData = this.buildMailData(request)
      
      const response = await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(mailData)
      })

      const responseText = await response.text()
      
      if (response.ok) {
        const messageId = response.headers.get('X-Message-Id') || 
                         `sendgrid-${Date.now()}`
        
        this.logEmail('sent', request, { 
          success: true, 
          messageId,
          provider: this.name 
        })
        
        return {
          success: true,
          messageId,
          provider: this.name,
          timestamp: new Date()
        }
      } else {
        let errorMessage = `SendGrid API error: ${response.status}`
        try {
          const errorData = JSON.parse(responseText)
          errorMessage = errorData.errors?.map((e: any) => e.message).join(', ') || errorMessage
        } catch {
          errorMessage += ` - ${responseText}`
        }
        
        throw new Error(errorMessage)
      }
    } catch (error) {
      this.logEmail('failed', request, undefined, error as Error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send email',
        provider: this.name
      }
    }
  }

  async sendBatch(requests: EmailRequest[]): Promise<EmailResponse[]> {
    // SendGrid supports batch sending with personalizations
    if (requests.length <= 1) {
      return super.sendBatch(requests)
    }

    try {
      // Group requests by similar properties for batch optimization
      const batchGroups = this.groupRequestsForBatch(requests)
      const results: EmailResponse[] = []

      for (const group of batchGroups) {
        const batchResult = await this.sendBatchGroup(group)
        results.push(...batchResult)
      }

      return results
    } catch (error) {
      logger.error('SendGrid batch send failed', error as Error)
      // Fallback to sequential sending
      return super.sendBatch(requests)
    }
  }

  async getStatus(messageId: string): Promise<EmailStatus> {
    try {
      // SendGrid doesn't provide direct message status API
      // You would need to use webhooks or the Activity API
      // For now, return null as status checking requires additional setup
      return null as any
    } catch (error) {
      logger.error('Failed to get SendGrid message status', error as Error, { messageId })
      return null as any
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test the API key by fetching account details
      const response = await fetch('https://api.sendgrid.com/v3/user/account', {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      })

      if (response.ok) {
        logger.info('SendGrid configuration validated successfully')
        return true
      }

      logger.error('SendGrid configuration validation failed', {
        status: response.status,
        statusText: response.statusText
      })
      return false
    } catch (error) {
      logger.error('SendGrid configuration validation error', error as Error)
      return false
    }
  }

  private buildMailData(request: EmailRequest): SendGridMailData {
    const recipients = this.normalizeEmails(request.to)
    const from = request.from || this.defaultFrom
    
    const mailData: SendGridMailData = {
      personalizations: [{
        to: recipients.map(email => ({ email })),
        ...(request.cc && { cc: request.cc.map(email => ({ email })) }),
        ...(request.bcc && { bcc: request.bcc.map(email => ({ email })) }),
        ...(request.headers && { headers: request.headers })
      }],
      from: this.parseEmail(from),
      subject: request.subject,
      content: [
        { type: 'text/plain', value: request.text || this.stripHtml(request.html) },
        { type: 'text/html', value: request.html }
      ]
    }

    // Add reply-to
    const replyTo = request.replyTo || this.defaultReplyTo
    if (replyTo) {
      mailData.reply_to = this.parseEmail(replyTo)
    }

    // Add attachments
    if (request.attachments?.length) {
      mailData.attachments = request.attachments.map(att => ({
        content: typeof att.content === 'string' ? att.content : att.content.toString('base64'),
        filename: att.filename,
        type: att.type,
        disposition: att.disposition,
        content_id: att.contentId
      }))
    }

    // Add categories (tags)
    if (request.tags?.length) {
      mailData.categories = request.tags
    }

    // Add scheduled send
    if (request.scheduledFor) {
      mailData.send_at = Math.floor(request.scheduledFor.getTime() / TIME_MS.SECOND)
    }

    // Configure settings
    mailData.mail_settings = {
      ...(this.sandboxMode && { sandbox_mode: { enable: true } })
    }

    // Configure tracking
    mailData.tracking_settings = {
      click_tracking: { 
        enable: request.trackClicks !== false, 
        enable_text: request.trackClicks !== false 
      },
      open_tracking: { 
        enable: request.trackOpens !== false 
      }
    }

    return mailData
  }

  private parseEmail(email: string): { email: string; name?: string } {
    const match = email.match(/^(.+?)\s*<(.+)>$/)
    if (match) {
      return { email: match[2], name: match[1].trim() }
    }
    return { email }
  }

  private groupRequestsForBatch(requests: EmailRequest[]): EmailRequest[][] {
    // Group by subject and from address for batch optimization
    const groups = new Map<string, EmailRequest[]>()
    
    for (const request of requests) {
      const key = `${request.subject}|${request.from || this.defaultFrom}`
      const group = groups.get(key) || []
      group.push(request)
      groups.set(key, group)
    }

    // Split large groups to respect SendGrid's limits (TIME_MS.SECOND personalizations per request)
    const batchGroups: EmailRequest[][] = []
    for (const group of groups.values()) {
      for (let i = 0; i < group.length; i += 900) {
        batchGroups.push(group.slice(i, i + 900))
      }
    }

    return batchGroups
  }

  private async sendBatchGroup(requests: EmailRequest[]): Promise<EmailResponse[]> {
    if (requests.length === 0) return []
    if (requests.length === 1) return [await this.send(requests[0])]

    // Use the first request as the template
    const template = requests[0]
    const personalizations: SendGridPersonalization[] = requests.map(req => ({
      to: this.normalizeEmails(req.to).map(email => ({ email })),
      ...(req.cc && { cc: req.cc.map(email => ({ email })) }),
      ...(req.bcc && { bcc: req.bcc.map(email => ({ email })) }),
      ...(req.metadata && { custom_args: req.metadata as Record<string, string> })
    }))

    const batchData: SendGridMailData = {
      ...this.buildMailData(template),
      personalizations
    }

    try {
      const response = await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(batchData)
      })

      if (response.ok) {
        const messageId = response.headers.get('X-Message-Id') || `sendgrid-batch-${Date.now()}`
        
        return requests.map((req, index) => ({
          success: true,
          messageId: `${messageId}-${index}`,
          provider: this.name,
          timestamp: new Date()
        }))
      } else {
        const errorText = await response.text()
        throw new Error(`SendGrid batch API error: ${response.status} - ${errorText}`)
      }
    } catch (error) {
      // Return individual failures
      return requests.map(req => ({
        success: false,
        error: error instanceof Error ? error.message : 'Batch send failed',
        provider: this.name
      }))
    }
  }
}