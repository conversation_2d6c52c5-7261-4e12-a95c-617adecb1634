'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { UnifiedErrorBoundary } from '@/components/error/unified-error-boundary'
import { UnifiedLoadingState } from '@/components/ui/unified-loading'
import { UnifiedEmptyState } from '@/components/ui/unified-empty-state'
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  User, 
  Calendar,
  BookOpen,
  Heart,
  Swords,
  Lightbulb,
  Target,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { logger } from '@/lib/services/logger'
import { createClient } from '@/lib/supabase'

interface CharacterArcEvent {
  id: string
  characterId: string
  chapterId: string
  chapterNumber: number
  type: 'growth' | 'setback' | 'relationship' | 'conflict' | 'revelation' | 'achievement'
  title: string
  description: string
  emotionalState?: string
  intensity: number // 1-5 scale
  timestamp: string
}

interface CharacterArcData {
  characterId: string
  characterName: string
  events: CharacterArcEvent[]
  overallTrajectory: 'rising' | 'falling' | 'stable' | 'volatile'
  keyMoments: CharacterArcEvent[]
  relationships: {
    characterId: string
    characterName: string
    evolution: 'strengthening' | 'weakening' | 'complicated'
  }[]
}

interface CharacterArcTimelineProps {
  projectId: string
  className?: string
}

const eventTypeConfig = {
  growth: {
    icon: TrendingUp,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
    label: 'Growth'
  },
  setback: {
    icon: TrendingDown,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-100 dark:bg-red-900/20',
    label: 'Setback'
  },
  relationship: {
    icon: Heart,
    color: 'text-pink-600 dark:text-pink-400',
    bgColor: 'bg-pink-100 dark:bg-pink-900/20',
    label: 'Relationship'
  },
  conflict: {
    icon: Swords,
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-100 dark:bg-orange-900/20',
    label: 'Conflict'
  },
  revelation: {
    icon: Lightbulb,
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
    label: 'Revelation'
  },
  achievement: {
    icon: Target,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
    label: 'Achievement'
  }
}

export function CharacterArcTimeline({ projectId, className }: CharacterArcTimelineProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [characters, setCharacters] = useState<any[]>([])
  const [selectedCharacterId, setSelectedCharacterId] = useState<string>('')
  const [arcData, setArcData] = useState<CharacterArcData | null>(null)
  const [timelineScale, setTimelineScale] = useState<'chapters' | 'events'>('chapters')
  const [selectedEvent, setSelectedEvent] = useState<CharacterArcEvent | null>(null)

  // Load characters
  useEffect(() => {
    const loadCharacters = async () => {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('characters')
          .select('id, name, role')
          .eq('project_id', projectId)
          .order('name')

        if (error) throw error

        setCharacters(data || [])
        if (data && data.length > 0 && !selectedCharacterId) {
          setSelectedCharacterId(data[0].id)
        }
      } catch (error) {
        logger.error('Failed to load characters:', error)
      }
    }

    loadCharacters()
  }, [projectId, selectedCharacterId])

  // Load character arc data
  const loadArcData = useCallback(async () => {
    if (!selectedCharacterId) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/analysis/character-arc?projectId=${projectId}&characterId=${selectedCharacterId}`)
      if (!response.ok) throw new Error('Failed to load arc data')
      
      const data = await response.json()
      setArcData(data.data)
    } catch (error) {
      logger.error('Failed to load character arc:', error)
      setArcData(null)
    } finally {
      setIsLoading(false)
    }
  }, [projectId, selectedCharacterId])

  useEffect(() => {
    loadArcData()
  }, [loadArcData])

  const renderTimeline = () => {
    if (!arcData || arcData.events.length === 0) {
      return (
        <UnifiedEmptyState
          title="No arc events found"
          description="Character development events will appear here as you write"
          icon={Activity}
        />
      )
    }

    const maxChapter = Math.max(...arcData.events.map(e => e.chapterNumber))
    const minChapter = Math.min(...arcData.events.map(e => e.chapterNumber))
    const chapterRange = maxChapter - minChapter + 1

    return (
      <div className="relative">
        {/* Timeline header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              Chapter {minChapter} - {maxChapter}
            </Badge>
            <Badge 
              variant="secondary"
              className={cn(
                arcData.overallTrajectory === 'rising' && 'bg-green-100 text-green-700',
                arcData.overallTrajectory === 'falling' && 'bg-red-100 text-red-700',
                arcData.overallTrajectory === 'volatile' && 'bg-orange-100 text-orange-700'
              )}
            >
              {arcData.overallTrajectory} arc
            </Badge>
          </div>
          <Select value={timelineScale} onValueChange={(v) => setTimelineScale(v as any)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="chapters">By Chapter</SelectItem>
              <SelectItem value="events">By Event</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Timeline visualization */}
        <ScrollArea className="w-full">
          <div className="relative min-h-[400px] pb-4">
            {/* Timeline line */}
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-border" />

            {/* Events */}
            <div className="relative flex gap-8 px-4">
              {timelineScale === 'chapters' ? (
                // Group by chapters
                Array.from({ length: chapterRange }, (_, i) => {
                  const chapter = minChapter + i
                  const chapterEvents = arcData.events.filter(e => e.chapterNumber === chapter)
                  
                  return (
                    <div key={chapter} className="flex flex-col items-center min-w-[120px]">
                      <div className="text-sm text-muted-foreground mb-2">
                        Chapter {chapter}
                      </div>
                      <div className="flex flex-col gap-2">
                        {chapterEvents.map((event, idx) => {
                          const config = eventTypeConfig[event.type]
                          const Icon = config.icon
                          
                          return (
                            <button
                              key={event.id}
                              onClick={() => setSelectedEvent(event)}
                              className={cn(
                                "relative group cursor-pointer",
                                "transform transition-all hover:scale-110"
                              )}
                              style={{
                                marginTop: idx === 0 ? '0' : `${event.intensity * 10}px`
                              }}
                            >
                              <div className={cn(
                                "w-10 h-10 rounded-full flex items-center justify-center",
                                config.bgColor,
                                selectedEvent?.id === event.id && "ring-2 ring-primary"
                              )}>
                                <Icon className={cn("w-5 h-5", config.color)} />
                              </div>
                              <div className="absolute top-full mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <div className="bg-popover text-popover-foreground text-xs p-2 rounded shadow-lg whitespace-nowrap">
                                  {event.title}
                                </div>
                              </div>
                            </button>
                          )
                        })}
                      </div>
                    </div>
                  )
                })
              ) : (
                // Show all events linearly
                arcData.events.map((event, idx) => {
                  const config = eventTypeConfig[event.type]
                  const Icon = config.icon
                  
                  return (
                    <div key={event.id} className="flex flex-col items-center min-w-[100px]">
                      <button
                        onClick={() => setSelectedEvent(event)}
                        className={cn(
                          "relative group cursor-pointer mb-2",
                          "transform transition-all hover:scale-110"
                        )}
                      >
                        <div className={cn(
                          "w-12 h-12 rounded-full flex items-center justify-center",
                          config.bgColor,
                          selectedEvent?.id === event.id && "ring-2 ring-primary"
                        )}>
                          <Icon className={cn("w-6 h-6", config.color)} />
                        </div>
                      </button>
                      <div className="text-xs text-center">
                        <div className="font-medium">Ch {event.chapterNumber}</div>
                        <div className="text-muted-foreground">{config.label}</div>
                      </div>
                    </div>
                  )
                })
              )}
            </div>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        {/* Event details */}
        {selectedEvent && (
          <Card className="mt-6">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {(() => {
                    const config = eventTypeConfig[selectedEvent.type]
                    const Icon = config.icon
                    return (
                      <>
                        <div className={cn("p-2 rounded", config.bgColor)}>
                          <Icon className={cn("w-4 h-4", config.color)} />
                        </div>
                        <div>
                          <h4 className="font-medium">{selectedEvent.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            Chapter {selectedEvent.chapterNumber} • {config.label}
                          </p>
                        </div>
                      </>
                    )
                  })()}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedEvent(null)}
                >
                  ×
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm mb-3">{selectedEvent.description}</p>
              {selectedEvent.emotionalState && (
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    Emotional State: {selectedEvent.emotionalState}
                  </Badge>
                  <Badge variant="outline">
                    Intensity: {selectedEvent.intensity}/5
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  return (
    <UnifiedErrorBoundary>
      <Card className={cn("h-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Character Arc Timeline
            </CardTitle>
            <Select 
              value={selectedCharacterId} 
              onValueChange={setSelectedCharacterId}
              disabled={characters.length === 0}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select character" />
              </SelectTrigger>
              <SelectContent>
                {characters.map(character => (
                  <SelectItem key={character.id} value={character.id}>
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      {character.name}
                      {character.role && (
                        <span className="text-xs text-muted-foreground">
                          ({character.role})
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <UnifiedLoadingState message="Analyzing character arc..." />
          ) : (
            renderTimeline()
          )}
        </CardContent>
      </Card>
    </UnifiedErrorBoundary>
  )
}