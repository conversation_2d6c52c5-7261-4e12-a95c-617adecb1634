'use client';

import { useEffect, useCallback } from 'react';
import { trackPerformance } from '@/lib/monitoring/sentry';
import { logger } from '@/lib/services/logger';
import { TIME_MS } from '@/lib/constants'

interface PerformanceMonitorProps {
  children: React.ReactNode;
  routeName: string;
}

export function PerformanceMonitor({ children, routeName }: PerformanceMonitorProps) {
  // Track page load performance
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const measurePageLoad = () => {
      // Wait for the page to be fully loaded
      if (window.performance && window.performance.timing) {
        const timing = window.performance.timing;
        const loadTime = timing.loadEventEnd - timing.navigationStart;
        const domContentLoadedTime = timing.domContentLoadedEventEnd - timing.navigationStart;
        const firstPaintTime = performance.getEntriesByType('paint')
          .find(entry => entry.name === 'first-contentful-paint')?.startTime || 0;

        if (loadTime > 0) {
          trackPerformance(`page_load_${routeName}`, loadTime, {
            domContentLoaded: domContentLoadedTime,
            firstPaint: firstPaintTime,
            route: routeName,
          });

          // Log slow page loads
          if (loadTime > TIME_MS.TYPING_TIMEOUT) {
            logger.warn(`Slow page load detected on ${routeName}: ${loadTime}ms`);
          }
        }
      }
    };

    // Check if page is already loaded
    if (document.readyState === 'complete') {
      measurePageLoad();
    } else {
      window.addEventListener('load', measurePageLoad);
      return () => window.removeEventListener('load', measurePageLoad);
    }
  }, [routeName]);

  // Track route changes
  useEffect(() => {
    const startTime = performance.now();

    return () => {
      const duration = performance.now() - startTime;
      trackPerformance(`route_duration_${routeName}`, duration, {
        route: routeName,
      });
    };
  }, [routeName]);

  // Track long tasks (blocking the main thread)
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // Tasks longer than 50ms
            trackPerformance('long_task', entry.duration, {
              route: routeName,
              taskName: entry.name,
            });
          }
        }
      });

      observer.observe({ entryTypes: ['longtask'] });

      return () => observer.disconnect();
    } catch (error) {
      // Some browsers don't support longtask observer
      logger.debug('Long task observer not supported');
    }
  }, [routeName]);

  // Track layout shifts (CLS)
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      let cumulativeLayoutShift = 0;

      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            cumulativeLayoutShift += (entry as any).value;
          }
        }
      });

      observer.observe({ entryTypes: ['layout-shift'] });

      return () => {
        observer.disconnect();
        if (cumulativeLayoutShift > 0.1) { // Poor CLS threshold
          trackPerformance('cumulative_layout_shift', cumulativeLayoutShift, {
            route: routeName,
          });
        }
      };
    } catch (error) {
      // Some browsers don't support layout-shift observer
      logger.debug('Layout shift observer not supported');
    }
  }, [routeName]);

  // Track interaction performance
  const trackInteraction = useCallback((interactionName: string, startTime: number) => {
    const duration = performance.now() - startTime;
    trackPerformance(`interaction_${interactionName}`, duration, {
      route: routeName,
      interaction: interactionName,
    });
  }, [routeName]);

  // Provide the tracking function to children through a data attribute
  return (
    <div data-performance-track={trackInteraction.toString()}>
      {children}
    </div>
  );
}

// Hook to track specific operations
export function usePerformanceTracking() {
  const trackOperation = useCallback(async <T,>(
    operationName: string,
    operation: () => Promise<T>,
    metadata?: Record<string, unknown>
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      trackPerformance(operationName, duration, metadata);
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      trackPerformance(`${operationName}_error`, duration, {
        ...metadata,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw error;
    }
  }, []);

  return { trackOperation };
}