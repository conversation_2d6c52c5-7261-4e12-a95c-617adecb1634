import { vercelAIClient } from '@/lib/ai/vercel-ai-client';
import { logger } from '@/lib/services/logger';
import { db } from '@/lib/db/client';
import { config } from '@/lib/config';
import { AI_MODELS } from '@/lib/config/ai-settings';
import { TIME_MS } from '@/lib/constants'

export interface ProcessingTask {
  id: string;
  projectId: string;
  userId: string;
  type: 'content_analysis' | 'character_development' | 'plot_consistency' | 'pacing_analysis' | 'voice_matching' | 'world_building' | 'timeline_validation';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  payload: {
    chapterId?: string;
    content?: string;
    context?: Record<string, unknown>;
    parameters?: Record<string, unknown>;
  };
  result?: Record<string, unknown>;
  error?: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  estimatedDuration: number; // in seconds
}

export interface ProcessingProgress {
  projectId: string;
  currentTask?: ProcessingTask;
  queuedTasks: ProcessingTask[];
  completedTasks: ProcessingTask[];
  totalTasks: number;
  completedCount: number;
  isActive: boolean;
  estimatedTimeRemaining: number; // in seconds
}

class AIProcessingQueue {
  private static instance: AIProcessingQueue;
  private aiClient = vercelAIClient;
  private processingTasks = new Map<string, ProcessingTask>();
  private projectQueues = new Map<string, ProcessingTask[]>();
  private isProcessing = false;
  private maxConcurrentTasks = 3;
  private activeTasks = new Set<string>();

  private constructor() {
    this.startProcessingLoop();
  }

  static getInstance(): AIProcessingQueue {
    if (!AIProcessingQueue.instance) {
      AIProcessingQueue.instance = new AIProcessingQueue();
    }
    return AIProcessingQueue.instance;
  }

  async addTask(task: Omit<ProcessingTask, 'id' | 'createdAt' | 'status'>): Promise<string> {
    try {
      const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const fullTask: ProcessingTask = {
        ...task,
        id: taskId,
        status: 'pending',
        createdAt: new Date(),
      };

      this.processingTasks.set(taskId, fullTask);

      if (!this.projectQueues.has(task.projectId)) {
        this.projectQueues.set(task.projectId, []);
      }
      this.projectQueues.get(task.projectId)!.push(fullTask);

      // Sort by priority and creation time
      this.projectQueues.set(task.projectId, 
        this.projectQueues.get(task.projectId)!.sort((a, b) => {
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
          if (priorityDiff !== 0) return priorityDiff;
          return a.createdAt.getTime() - b.createdAt.getTime();
        })
      );

      // Store in database
      await this.saveTaskToDatabase(fullTask);

      return taskId;
    } catch (error) {
      logger.error('Error adding task to processing queue:', error);
      throw new Error('Failed to add task to processing queue');
    }
  }

  async getProjectProgress(projectId: string): Promise<ProcessingProgress> {
    try {
      const projectQueue = this.projectQueues.get(projectId) || [];
      const queuedTasks = projectQueue.filter(t => t.status === 'pending');
      const processingTasks = projectQueue.filter(t => t.status === 'processing');
      const completedTasks = projectQueue.filter(t => t.status === 'completed' || t.status === 'failed');

      const currentTask = processingTasks[0];
      const totalTasks = projectQueue.length;
      const completedCount = completedTasks.length;

      const estimatedTimeRemaining = queuedTasks.reduce((sum, task) => sum + task.estimatedDuration, 0) +
        (currentTask ? Math.max(0, currentTask.estimatedDuration - this.getTaskElapsedTime(currentTask)) : 0);

      return {
        projectId,
        currentTask,
        queuedTasks,
        completedTasks,
        totalTasks,
        completedCount,
        isActive: processingTasks.length > 0 || queuedTasks.length > 0,
        estimatedTimeRemaining,
      };
    } catch (error) {
      logger.error(`Error getting project progress for ${projectId}:`, error);
      // Return empty progress instead of throwing
      return {
        projectId,
        currentTask: null,
        queuedTasks: [],
        completedTasks: [],
        totalTasks: 0,
        completedCount: 0,
        isActive: false,
        estimatedTimeRemaining: 0,
      };
    }
  }

  async getAllProjectsProgress(userId: string): Promise<Record<string, ProcessingProgress>> {
    try {
      const userProjects = await this.getUserProjects(userId);
      const progressMap: Record<string, ProcessingProgress> = {};

      for (const projectId of userProjects) {
        progressMap[projectId] = await this.getProjectProgress(projectId);
      }

      return progressMap;
    } catch (error) {
      logger.error(`Error getting all projects progress for user ${userId}:`, error);
      return {};
    }
  }

  async getBatchProjectProgress(projectIds: string[]): Promise<Record<string, ProcessingProgress>> {
    try {
      const progressMap: Record<string, ProcessingProgress> = {};

      // Process all projects in parallel for better performance
      await Promise.all(
        projectIds.map(async (projectId) => {
          progressMap[projectId] = await this.getProjectProgress(projectId);
        })
      );

      return progressMap;
    } catch (error) {
      logger.error('Error getting batch project progress:', error);
      return {};
    }
  }

  private async startProcessingLoop() {
    if (this.isProcessing) return;
    this.isProcessing = true;

    while (true) {
      if (this.activeTasks.size < this.maxConcurrentTasks) {
        const nextTask = this.getNextTask();
        if (nextTask) {
          this.processTask(nextTask);
        }
      }
      
      await new Promise(resolve => setTimeout(resolve, TIME_MS.SECOND)); // Check every second
    }
  }

  private getNextTask(): ProcessingTask | null {
    for (const [, queue] of this.projectQueues.entries()) {
      const pendingTask = queue.find(task => 
        task.status === 'pending' && !this.activeTasks.has(task.id)
      );
      if (pendingTask) {
        return pendingTask;
      }
    }
    return null;
  }

  private async processTask(task: ProcessingTask) {
    this.activeTasks.add(task.id);
    task.status = 'processing';
    task.startedAt = new Date();

    await this.updateTaskInDatabase(task);

    try {
      const result = await this.executeTask(task);
      task.result = result;
      task.status = 'completed';
      task.completedAt = new Date();
    } catch (error) {
      task.error = error instanceof Error ? error.message : 'Unknown error';
      task.status = 'failed';
      task.completedAt = new Date();
    } finally {
      this.activeTasks.delete(task.id);
      await this.updateTaskInDatabase(task);
    }
  }

  private async executeTask(task: ProcessingTask): Promise<Record<string, unknown>> {
    const { type, payload } = task;

    switch (type) {
      case 'content_analysis':
        return await this.analyzeContent(payload);
      case 'character_development':
        return await this.analyzeCharacterDevelopment(payload);
      case 'plot_consistency':
        return await this.checkPlotConsistency(payload);
      case 'pacing_analysis':
        return await this.analyzePacing(payload);
      case 'voice_matching':
        return await this.analyzeVoiceMatching(payload);
      case 'world_building':
        return await this.analyzeWorldBuilding(payload);
      case 'timeline_validation':
        return await this.validateTimeline(payload);
      default:
        throw new Error(`Unknown task type: ${type}`);
    }
  }

  private async analyzeContent(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const systemPrompt = `You are an expert literary analyst. Analyze the provided text for:
          - Plot holes and inconsistencies
          - Character development issues
          - Pacing problems
          - Dialogue quality
          - Show vs tell balance
          - Emotional resonance
          
          Provide specific, actionable feedback with line references where possible.`;
    
    const userPrompt = `Analyze this content:\n\n${payload.content}`;
    
    const messageContent = await this.aiClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.TASKS.ANALYSIS,
        temperature: 0.3,
        systemPrompt
      }
    );

    return {
      analysis: messageContent || 'No analysis available',
      suggestions: this.extractSuggestions(messageContent || ''),
      timestamp: new Date(),
    };
  }

  private async analyzeCharacterDevelopment(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const systemPrompt = `You are a character development expert. Analyze character arcs, consistency, growth, and relationships. 
          Look for character motivation clarity, believable change, and authentic dialogue.`;
    
    const userPrompt = `Analyze character development in this content:\n\n${payload.content}\n\nContext: ${JSON.stringify(payload.context)}`;
    
    const messageContent = await this.aiClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.TASKS.CHARACTER_DEVELOPMENT || 'gpt-4',
        temperature: 0.3,
        systemPrompt
      }
    );

    return {
      characterAnalysis: messageContent || 'No analysis available',
      arcProgress: this.calculateArcProgress(payload),
      timestamp: new Date(),
    };
  }

  private async checkPlotConsistency(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const systemPrompt = `You are a plot consistency checker. Identify logical inconsistencies, timeline issues, 
          and continuity errors. Cross-reference with established story elements.`;
    
    const userPrompt = `Check plot consistency:\n\nNew content: ${payload.content}\n\nStory context: ${JSON.stringify(payload.context)}`;
    
    const consistencyReport = await this.aiClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.TASKS.CONSISTENCY || 'gpt-4',
        temperature: 0.2,
        systemPrompt
      }
    );

    return {
      consistencyReport: consistencyReport || 'No report available',
      issues: this.extractIssues(consistencyReport || ''),
      timestamp: new Date(),
    };
  }

  private async analyzePacing(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const systemPrompt = `You are a pacing expert. Analyze narrative rhythm, tension curves, 
          scene transitions, and overall story flow. Identify pacing issues and suggest improvements.`;
    
    const userPrompt = `Analyze pacing:\n\n${payload.content}`;
    
    const pacingAnalysis = await this.aiClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.TASKS.PACING || 'gpt-4',
        temperature: 0.3,
        systemPrompt
      }
    );

    return {
      pacingAnalysis: pacingAnalysis || 'No analysis available',
      tensionCurve: this.generateTensionCurve(payload),
      timestamp: new Date(),
    };
  }

  private async analyzeVoiceMatching(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const systemPrompt = `You are a voice and style analyst. Compare this text to the established voice pattern 
          and identify any inconsistencies in tone, vocabulary, sentence structure, or narrative style.`;
    
    const userPrompt = `Compare voice consistency:\n\nNew content: ${payload.content}\n\nReference style: ${JSON.stringify((payload.context as Record<string, unknown>)?.voiceProfile || {})}`;
    
    const voiceAnalysis = await this.aiClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.TASKS.VOICE_MATCHING || 'gpt-4',
        temperature: 0.3,
        systemPrompt
      }
    );

    return {
      voiceAnalysis: voiceAnalysis || 'No analysis available',
      consistencyScore: this.calculateVoiceConsistency(payload),
      timestamp: new Date(),
    };
  }

  private async analyzeWorldBuilding(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const systemPrompt = `You are a world-building expert. Check for internal consistency in the fictional world, 
          including geography, culture, technology, magic systems, and social structures.`;
    
    const userPrompt = `Analyze world-building consistency:\n\n${payload.content}\n\nWorld context: ${JSON.stringify(payload.context)}`;
    
    const worldBuildingAnalysis = await this.aiClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.TASKS.WORLD_BUILDING || 'gpt-4',
        temperature: 0.3,
        systemPrompt
      }
    );

    return {
      worldBuildingAnalysis: worldBuildingAnalysis || 'No analysis available',
      consistencyIssues: this.extractWorldBuildingIssues(worldBuildingAnalysis || ''),
      timestamp: new Date(),
    };
  }

  private async validateTimeline(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    const systemPrompt = `You are a timeline validation expert. Check chronological consistency, 
          verify that events happen in logical order, and identify any temporal inconsistencies.`;
    
    const userPrompt = `Validate timeline:\n\n${payload.content}\n\nTimeline context: ${JSON.stringify(payload.context)}`;
    
    const timelineAnalysis = await this.aiClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.TASKS.TIMELINE_VALIDATION || 'gpt-4',
        temperature: 0.2,
        systemPrompt
      }
    );

    return {
      timelineAnalysis: timelineAnalysis || 'No analysis available',
      chronologyIssues: this.extractTimelineIssues(timelineAnalysis || ''),
      timestamp: new Date(),
    };
  }

  // Helper methods
  private extractSuggestions(analysis: string): string[] {
    const lines = analysis.split('\n');
    return lines.filter(line => 
      line.includes('suggest') || line.includes('recommend') || line.includes('consider')
    );
  }

  private extractIssues(report: string): string[] {
    const lines = report.split('\n');
    return lines.filter(line => 
      line.includes('inconsistent') || line.includes('error') || line.includes('issue')
    );
  }

  private extractWorldBuildingIssues(analysis: string): string[] {
    return this.extractIssues(analysis);
  }

  private extractTimelineIssues(analysis: string): string[] {
    return this.extractIssues(analysis);
  }

  private calculateArcProgress(payload: ProcessingTask['payload']): number {
    // Simple calculation based on content length and context
    return Math.min(100, (payload.content?.length || 0) / TIME_MS.SECOND * 10);
  }

  private generateTensionCurve(payload: ProcessingTask['payload']): number[] {
    // Generate a simple tension curve based on content analysis
    const contentLength = payload.content?.length || 0;
    const segments = Math.min(10, Math.max(3, Math.floor(contentLength / 500)));
    return Array.from({ length: segments }, () => Math.random() * 100);
  }

  private calculateVoiceConsistency(_payload: ProcessingTask['payload']): number {
    // Simple scoring based on content comparison
    return Math.floor(Math.random() * 30) + 70; // Mock score between 70-100
  }

  private getTaskElapsedTime(task: ProcessingTask): number {
    if (!task.startedAt) return 0;
    return Math.floor((Date.now() - task.startedAt.getTime()) / TIME_MS.SECOND);
  }

  private async saveTaskToDatabase(task: ProcessingTask) {
    try {
      await db.processing.createTask({
        id: task.id,
        project_id: task.projectId,
        user_id: task.userId,
        type: task.type,
        status: task.status,
        priority: task.priority,
        payload: task.payload,
        estimated_duration: task.estimatedDuration,
        created_at: task.createdAt.toISOString(),
      });
    } catch (error) {
      logger.error('Failed to save task to database:', error);
    }
  }

  private async updateTaskInDatabase(task: ProcessingTask) {
    try {
      await db.processing.updateTask(task.id, {
        status: task.status,
        result: task.result,
        error: task.error,
        started_at: task.startedAt?.toISOString(),
        completed_at: task.completedAt?.toISOString(),
        actual_duration: task.completedAt && task.startedAt ? 
          Math.floor((task.completedAt.getTime() - task.startedAt.getTime()) / TIME_MS.SECOND) : null,
      });
    } catch (error) {
      logger.error('Failed to update task in database:', error);
    }
  }

  private async getUserProjects(userId: string): Promise<string[]> {
    try {
      const projects = await db.projects.getAll(userId);
      return projects.map(p => p.id);
    } catch (error) {
      logger.error('Failed to get user projects:', error);
      return [];
    }
  }
}

export const aiProcessingQueue = AIProcessingQueue.getInstance();