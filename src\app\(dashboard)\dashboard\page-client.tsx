'use client'

import { LazyAnalyticsDashboard } from '@/components/analytics/lazy-analytics'
import { AnalyticsErrorBoundary } from '@/components/analytics/analytics-error-boundary'
import { FirstTimeUserWrapper } from '@/components/onboarding/first-time-user-wrapper'
import { StoryCreationFAB } from '@/components/story/story-creation-fab'
import type { Project } from '@/lib/db/types'

interface DashboardPageClientProps {
  userId: string
  projects: Project[]
}

export default function DashboardPageClient({ userId, projects }: DashboardPageClientProps) {
  return (
    <FirstTimeUserWrapper hasProjects={(projects?.length || 0) > 0}>
      <div className="min-h-screen bg-background">
        <div className="fixed inset-0 paper-texture opacity-30" />
        
        <div className="relative z-10 py-6 sm:py-6 sm:py-8 lg:py-10 lg:py-10">
          <div className="container-wide">
            <div className="mb-6 sm:mb-8 lg:mb-10">
              <h1 className="text-3xl font-literary-display text-foreground mb-2">
                Writing Dashboard
              </h1>
              <p className="text-muted-foreground">
                Track your writing progress, analyze your productivity, and improve your craft
              </p>
            </div>
            
            <AnalyticsErrorBoundary>
              <LazyAnalyticsDashboard 
                userId={userId} 
                projects={projects || []}
              />
            </AnalyticsErrorBoundary>
          </div>
        </div>
      </div>
      
      <StoryCreationFAB />
    </FirstTimeUserWrapper>
  )
}