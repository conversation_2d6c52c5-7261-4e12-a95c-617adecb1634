'use client'

import { useState } from 'react'
import { logger } from '@/lib/services/logger';

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Select as _Select, SelectContent as _SelectContent, SelectItem as _SelectItem, SelectTrigger as _SelectTrigger, SelectValue as _SelectValue } from '@/components/ui/select'
import { toast } from '@/hooks/use-toast'
import { Upload, FileText, Book, FileCode, AlertCircle, CheckCircle2, Loader2 } from 'lucide-react'

interface BookImportDialogProps {
  projectId: string
  onImportComplete?: (chapters: unknown[]) => void
  trigger?: React.ReactNode
}

interface ImportOptions {
  parseChapters: boolean
  startPage?: number
  endPage?: number
}

export function BookImportDialog({ projectId, onImportComplete, trigger }: BookImportDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [fileType, setFileType] = useState<'docx' | 'epub' | 'pdf' | null>(null)
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [importResult, setImportResult] = useState<{ chaptersImported: number; totalWordCount: number } | null>(null)
  const [options, setOptions] = useState<ImportOptions>({
    parseChapters: true
  })

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const extension = file.name.split('.').pop()?.toLowerCase()
    if (!['docx', 'epub', 'pdf'].includes(extension || '')) {
      toast({
        variant: 'destructive',
        title: 'Invalid file type',
        description: 'Please select a .docx, .epub, or .pdf file'
      })
      return
    }

    setSelectedFile(file)
    setFileType(extension as 'docx' | 'epub' | 'pdf')
    setImportResult(null)
  }

  const handleImport = async () => {
    if (!selectedFile || !fileType) return

    setIsImporting(true)
    setImportProgress(20)

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('projectId', projectId)
      formData.append('parseChapters', options.parseChapters.toString())
      
      if (fileType === 'pdf' && options.startPage) {
        formData.append('startPage', options.startPage.toString())
      }
      if (fileType === 'pdf' && options.endPage) {
        formData.append('endPage', options.endPage.toString())
      }

      setImportProgress(40)

      const response = await fetch(`/api/import/${fileType}`, {
        method: 'POST',
        body: formData
      })

      setImportProgress(80)

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Import failed')
      }

      const result = await response.json()
      setImportProgress(100)
      setImportResult(result)

      toast({
        title: 'Import successful',
        description: `Imported ${result.chaptersImported} chapters with ${result.totalWordCount.toLocaleString()} words`
      })

      // Call callback if provided
      if (onImportComplete) {
        onImportComplete(result.chapters)
      }

      // Close dialog after delay
      setTimeout(() => {
        setIsOpen(false)
        resetForm()
      }, 2000)

    } catch (error) {
      logger.error('Import error:', error);
      toast({
        variant: 'destructive',
        title: 'Import failed',
        description: error instanceof Error ? error.message : 'An error occurred during import'
      })
    } finally {
      setIsImporting(false)
    }
  }

  const resetForm = () => {
    setSelectedFile(null)
    setFileType(null)
    setImportResult(null)
    setImportProgress(0)
    setOptions({ parseChapters: true })
  }

  const getFileIcon = () => {
    switch (fileType) {
      case 'docx':
        return <FileText className="h-12 w-12 text-info" />
      case 'epub':
        return <Book className="h-12 w-12 text-success" />
      case 'pdf':
        return <FileCode className="h-12 w-12 text-error" />
      default:
        return <Upload className="h-12 w-12 text-muted-foreground" />
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import Book
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Import Book</DialogTitle>
          <DialogDescription>
            Import your manuscript from Word (.docx), EPUB, or PDF files. The system will automatically detect chapters and import them into your project.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* File Selection */}
          <div className="space-y-2">
            <Label htmlFor="file">Select File</Label>
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  {getFileIcon()}
                  <p className="mt-2 text-sm text-muted-foreground">
                    {selectedFile ? selectedFile.name : 'Click to upload or drag and drop'}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    DOCX, EPUB, or PDF (MAX. 50MB)
                  </p>
                </div>
                <Input
                  id="file"
                  type="file"
                  className="hidden"
                  accept=".docx,.epub,.pdf"
                  onChange={handleFileSelect}
                  disabled={isImporting}
                />
              </label>
            </div>
          </div>

          {/* Import Options */}
          {selectedFile && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="parse-chapters">Auto-detect Chapters</Label>
                  <p className="text-xs text-muted-foreground">
                    Automatically split the document into chapters
                  </p>
                </div>
                <Switch
                  id="parse-chapters"
                  checked={options.parseChapters}
                  onCheckedChange={(checked) => setOptions({ ...options, parseChapters: checked })}
                  disabled={isImporting}
                />
              </div>

              {/* PDF-specific options */}
              {fileType === 'pdf' && (
                <div className="space-y-2">
                  <Label>Page Range (Optional)</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Start page"
                      min="1"
                      value={options.startPage || ''}
                      onChange={(e) => setOptions({ ...options, startPage: parseInt(e.target.value) || undefined })}
                      disabled={isImporting}
                    />
                    <Input
                      type="number"
                      placeholder="End page"
                      min="1"
                      value={options.endPage || ''}
                      onChange={(e) => setOptions({ ...options, endPage: parseInt(e.target.value) || undefined })}
                      disabled={isImporting}
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Import Progress */}
          {isImporting && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Importing your book...</span>
              </div>
              <Progress value={importProgress} />
            </div>
          )}

          {/* Import Result */}
          {importResult && (
            <Alert>
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>
                Successfully imported {importResult.chaptersImported} chapters with{' '}
                {importResult.totalWordCount.toLocaleString()} total words.
              </AlertDescription>
            </Alert>
          )}

          {/* Info Alert */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              For best results, ensure your document has clear chapter markers (e.g., &ldquo;Chapter 1&rdquo;, &ldquo;CHAPTER ONE&rdquo;).
              The importer will preserve formatting and structure.
            </AlertDescription>
          </Alert>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isImporting}>
            Cancel
          </Button>
          <Button 
            onClick={handleImport} 
            disabled={!selectedFile || isImporting}
          >
            {isImporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Importing...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Import
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}