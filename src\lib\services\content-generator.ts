import { logger } from '@/lib/services/logger';
import { AIServiceBase, AIGenerationOptions } from './ai-service-base';
import { WritingTask, ServiceResponse } from './types';
import { getAIConfig, AI_QUALITY_THRESHOLDS, AI_TEMPERATURE } from '../config/ai-settings';
import { qualityAnalyzer } from './quality-analyzer';
import { StreamingOptions } from '@/lib/ai/vercel-ai-client';
import {
  sceneOutlineSchema,
  dialogueResponseSchema,
  characterProfileGenerationSchema,
  worldBuildingSchema,
  CharacterProfileGeneration,
  WorldBuilding
} from '../schemas/content-schemas';

interface GenerationResult {
  content?: string;
  metadata?: Record<string, unknown>;
  quality?: number;
}

export class ContentGenerator extends AIServiceBase {
  private generationQueue: WritingTask[] = [];
  private activeGenerations: Map<string, { task: WritingTask; startTime: number }> = new Map();

  constructor() {
    super({
      name: 'content-generator',
      version: '1.0.0',
      endpoints: ['/api/content/generate', '/api/content/templates'],
      dependencies: ['ai-orchestrator'],
      healthCheck: '/api/content/health',
      useSharedClient: true
    });
  }

  async initialize(): Promise<void> {
    this.startGenerationProcessor();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    return this.createResponse(true, {
      status: `${this.generationQueue.length} queued, ${this.activeGenerations.size} processing`,
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
    });
  }

  async shutdown(): Promise<void> {
    this.generationQueue = [];
    this.activeGenerations.clear();
    this.setStatus('inactive');
  }

  async generateContent(request: {
    type: 'scene' | 'dialogue' | 'description' | 'chapter' | 'character' | 'plot-outline';
    prompt: string;
    context?: Record<string, unknown>;
    style?: string;
    length?: 'short' | 'medium' | 'long';
    tone?: string;
    projectId: string;
  }): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const task: WritingTask = {
        id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId: request.projectId,
        type: 'generate',
        priority: 'medium',
        input: {
          prompt: request.prompt,
          context: {
            type: request.type,
            style: request.style,
            length: request.length,
            tone: request.tone,
            ...request.context
          }
        },
        status: 'pending',
        createdAt: Date.now()
      };

      this.generationQueue.push(task);
      
      // Wait for completion
      return new Promise((resolve, reject) => {
        const checkCompletion = () => {
          if (task.status === 'completed' && task.output) {
            resolve(task.output.content);
          } else if (task.status === 'failed') {
            reject(new Error('Generation failed'));
          } else {
            setTimeout(checkCompletion, 500);
          }
        };
        
        setTimeout(checkCompletion, 100);
        
        // Timeout after 5 minutes for complex generation tasks
        setTimeout(() => {
          if (task.status === 'processing' || task.status === 'pending') {
            reject(new Error('Generation timeout'));
          }
        }, 300000);
      });
    });
  }

  async generateSceneOutline(request: {
    sceneGoal: string;
    characters: string[];
    setting: string;
    previousEvents: string;
    projectId: string;
  }): Promise<ServiceResponse<{
    outline: string;
    keyEvents: string[];
    conflicts: string[];
    emotionalBeats: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create a detailed scene outline with the following parameters:

Scene Goal: ${request.sceneGoal}
Characters Present: ${request.characters.join(', ')}
Setting: ${request.setting}
Previous Events: ${request.previousEvents}

Provide:
1. A structured scene outline (3-5 key beats)
2. List of key events that must happen
3. Potential conflicts or tensions
4. Emotional beats for character development

Format as JSON with keys: outline, keyEvents, conflicts, emotionalBeats
      `;

      const aiConfig = getAIConfig('CONTENT_GENERATION');
      
      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: 'You are an expert story architect. Create detailed, engaging scene outlines that drive plot and character development forward.',
        responseFormat: 'structured',
        structuredSchema: sceneOutlineSchema
      };

      const result = await this.generateWithAI<{
        outline: string;
        keyEvents: string[];
        conflicts: string[];
        emotionalBeats: string[];
      }>(prompt, options);

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate scene outline');
      }

      return result.data || {
        outline: '',
        keyEvents: [],
        conflicts: [],
        emotionalBeats: []
      }
    });
  }

  async generateDialogue(request: {
    characters: { name: string; personality: string; goal: string }[];
    context: string;
    tone: string;
    length: number;
    projectId: string;
  }): Promise<ServiceResponse<{
    dialogue: { speaker: string; line: string; emotion?: string }[];
    tags: string[];
    subtext: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const characterDescriptions = request.characters
        .map(char => `${char.name}: ${char.personality} (Goal: ${char.goal})`)
        .join('\n');

      const prompt = `
Write a dialogue scene between these characters:
${characterDescriptions}

Context: ${request.context}
Tone: ${request.tone}
Target length: Approximately ${request.length} lines

Requirements:
- Each character should speak in their distinct voice
- Include subtle subtext and character motivations
- Show personality through speech patterns
- Create natural flow and realistic interruptions

Format as JSON with:
{
  "dialogue": [{"speaker": "Name", "line": "dialogue text", "emotion": "optional"}],
  "tags": ["relevant", "story", "tags"],
  "subtext": ["underlying meanings", "character motivations"]
}
      `;

      const aiConfig = getAIConfig('CONTENT_GENERATION');
      
      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: 'You are an expert dialogue writer. Create realistic, character-driven dialogue that advances plot and reveals personality.',
        responseFormat: 'structured',
        structuredSchema: dialogueResponseSchema
      };

      const result = await this.generateWithAI<{
        dialogue: { speaker: string; line: string; emotion?: string }[];
        tags: string[];
        subtext: string[];
      }>(prompt, options);

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate dialogue');
      }

      return result.data || {
        dialogue: [],
        tags: [],
        subtext: []
      }
    });
  }

  async generateCharacterProfile(request: {
    name: string;
    role: 'protagonist' | 'antagonist' | 'supporting';
    age?: number;
    background?: string;
    goals?: string[];
    conflicts?: string[];
    projectId: string;
  }): Promise<ServiceResponse<{
    profile: {
      name: string;
      age: number;
      appearance: string;
      personality: string;
      background: string;
      motivations: string[];
      fears: string[];
      strengths: string[];
      weaknesses: string[];
      relationships: { character: string; relationship: string }[];
      arc: string;
      voice: string;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create a comprehensive character profile for:

Name: ${request.name}
Role: ${request.role}
${request.age ? `Age: ${request.age}` : ''}
${request.background ? `Background: ${request.background}` : ''}
${request.goals ? `Goals: ${request.goals.join(', ')}` : ''}
${request.conflicts ? `Conflicts: ${request.conflicts.join(', ')}` : ''}

Generate a detailed character profile including:
- Physical appearance
- Personality traits and quirks
- Background and history
- Core motivations and goals
- Deepest fears
- Key strengths and weaknesses
- Important relationships
- Character arc potential
- Distinctive voice/speech patterns

Format as JSON matching the profile structure.
      `;

      const aiConfig = getAIConfig('CHARACTER_DEVELOPMENT');
      
      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: 'You are an expert character developer. Create rich, complex characters with depth, flaws, and compelling motivations.',
        responseFormat: 'structured',
        structuredSchema: characterProfileGenerationSchema
      };

      const result = await this.generateWithAI<CharacterProfileGeneration>(prompt, options);

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate character profile');
      }

      return result.data || {
        profile: {
          name: request.name,
          age: request.age || 25,
          appearance: 'To be determined',
          personality: 'To be determined',
          background: request.background || 'To be determined',
          motivations: request.goals || [],
          fears: [],
          strengths: [],
          weaknesses: [],
          relationships: [],
          arc: 'To be determined',
          voice: 'To be determined'
        }
      }
    });
  }

  async generateWorldBuilding(request: {
    type: 'location' | 'culture' | 'history' | 'magic-system' | 'technology';
    name: string;
    description: string;
    genre: string;
    projectId: string;
  }): Promise<ServiceResponse<{
    worldElement: {
      name: string;
      type: string;
      description: string;
      details: Record<string, unknown>;
      connections: string[];
      atmosphere: string;
      significance: string;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create detailed world-building for a ${request.genre} story:

Type: ${request.type}
Name: ${request.name}
Description: ${request.description}

Generate comprehensive details including:
- Physical/conceptual characteristics
- Cultural or functional significance
- Historical context
- Rules or limitations (if applicable)
- Atmospheric elements
- Connections to other story elements
- Story significance and potential

Format as JSON with the worldElement structure.
      `;

      const aiConfig = getAIConfig('CONTENT_GENERATION');
      
      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: 'You are an expert world-builder. Create rich, internally consistent world elements that enhance storytelling.',
        responseFormat: 'structured',
        structuredSchema: worldBuildingSchema
      };

      const result = await this.generateWithAI<WorldBuilding>(prompt, options);

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate world building');
      }

      return result.data || {
        worldElement: {
          name: request.name,
          type: request.type,
          description: request.description,
          details: {},
          connections: [],
          atmosphere: 'To be determined',
          significance: 'To be determined'
        }
      }
    });
  }

  private async processGenerationQueue(): Promise<void> {
    if (this.generationQueue.length === 0 || this.activeGenerations.size >= 3) {
      return; // No tasks or max concurrent generations reached
    }

    const task = this.generationQueue.shift();
    if (!task) return;

    task.status = 'processing';
    this.activeGenerations.set(task.id, { task, startTime: Date.now() });

    try {
      const result = await this.executeGeneration(task);
      const generationResult = result as GenerationResult;
      task.output = {
        content: String(generationResult.content || ''),
        metadata: generationResult.metadata || {},
        quality: Number(generationResult.quality || 0.8)
      };
      task.status = 'completed';
      task.completedAt = Date.now();
    } catch (error) {
      task.status = 'failed';
      logger.error(`Generation failed for task ${task.id}:`, error);
    } finally {
      this.activeGenerations.delete(task.id);
    }
  }

  private async executeGeneration(task: WritingTask): Promise<Record<string, unknown>> {
    const context = task.input.context || {};
    const type = context.type || 'general';
    
    // Use base class system prompt
    const systemPrompt = this.getSystemPrompt(type === 'plot-outline' ? 'plot' : type);

    let userPrompt = task.input.prompt || '';
    
    if (context.style) userPrompt += `\n\nStyle: ${context.style}`;
    if (context.tone) userPrompt += `\nTone: ${context.tone}`;
    if (context.length) userPrompt += `\nLength: ${context.length}`;

    const aiConfig = getAIConfig('CONTENT_GENERATION');
    const temperature = type === 'dialogue' ? AI_TEMPERATURE.CREATIVE_MEDIUM : aiConfig.temperature;
    const maxTokens = context.length === 'long' ? 3000 : context.length === 'medium' ? 1500 : 1000;
    
    const options: AIGenerationOptions = {
      model: aiConfig.model,
      temperature,
      maxTokens: Math.min(maxTokens, aiConfig.max_tokens),
      systemPrompt,
      responseFormat: 'text'
    };

    const result = await this.generateWithAI<string>(userPrompt, options);
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to generate content');
    }

    const content = result.data || '';
    
    const quality = await this.assessGenerationQuality(content, String(type));
    
    return {
      content,
      metadata: {
        type: type,
        model: aiConfig.model,
        processingTime: Date.now() - (this.activeGenerations.get(task.id)?.startTime || Date.now()),
        tokensUsed: 0, // Token usage tracked in base class
        style: String(context.style || ''),
        tone: String(context.tone || '')
      },
      quality
    };
  }

  private async assessGenerationQuality(content: string, type: string): Promise<number> {
    // Try to use the quality analyzer if it's available
    if (qualityAnalyzer.getStatus() === 'active') {
      try {
        const analysisResult = await qualityAnalyzer.analyzeContentQuality(
          content,
          type as 'chapter' | 'dialogue' | 'description' | 'scene' | 'character'
        );
        
        if (analysisResult.success && analysisResult.data) {
          return analysisResult.data.metrics.overall;
        }
      } catch (error) {
        logger.warn('Quality analyzer failed, falling back to basic assessment:', error);
      }
    }

    // Use base class quality assessment
    return this.assessQuality(content, type);
  }

  private startGenerationProcessor(): void {
    setInterval(() => {
      this.processGenerationQueue();
    }, 1000);
  }

  /**
   * Generate content with streaming support
   */
  async generateContentStream(
    request: {
      type: 'scene' | 'dialogue' | 'description' | 'chapter' | 'character' | 'plot-outline';
      prompt: string;
      context?: Record<string, unknown>;
      style?: string;
      length?: 'short' | 'medium' | 'long';
      tone?: string;
      projectId: string;
    },
    streamingOptions?: StreamingOptions
  ): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const aiConfig = getAIConfig('CONTENT_GENERATION');

      const systemPrompt = this.buildSystemPrompt(request.type, request.style, request.tone);
      const prompt = this.buildPrompt(request);

      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt,
        streaming: true,
        streamingOptions
      };

      const result = await this.generateWithAI<string>(prompt, options);
      return result;
    });
  }

  /**
   * Generate scene outline with streaming
   */
  async generateSceneOutlineStream(
    request: {
      chapterTitle: string;
      chapterSummary: string;
      previousScenes?: string[];
      tone?: string;
      style?: string;
      projectId: string;
    },
    streamingOptions?: StreamingOptions
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const prompt = `Create a detailed scene outline for a chapter titled "${request.chapterTitle}".

Chapter Summary: ${request.chapterSummary}
${request.previousScenes ? `Previous scenes: ${request.previousScenes.join(', ')}` : ''}
Tone: ${request.tone || 'balanced'}
Style: ${request.style || 'literary'}

Create a structured scene outline with clear plot points, character actions, and emotional beats.`;

      const aiConfig = getAIConfig('CONTENT_GENERATION');

      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: 'You are an expert story structure specialist. Create detailed, engaging scene outlines that advance the plot and develop characters.',
        responseFormat: 'structured',
        structuredSchema: sceneOutlineSchema,
        streaming: true,
        streamingOptions
      };

      const result = await this.generateWithAI<SceneOutline>(prompt, options);
      return result;
    });
  }

  /**
   * Generate dialogue with streaming
   */
  async generateDialogueStream(
    request: {
      characters: string[];
      context: string;
      tone?: string;
      length?: 'short' | 'medium' | 'long';
      projectId: string;
    },
    streamingOptions?: StreamingOptions
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const prompt = `Generate dialogue between characters: ${request.characters.join(', ')}

Context: ${request.context}
Tone: ${request.tone || 'natural'}
Length: ${request.length || 'medium'}

Create natural, character-appropriate dialogue that reveals personality and advances the story.`;

      const aiConfig = getAIConfig('CONTENT_GENERATION');

      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: 'You are an expert dialogue writer. Create realistic, character-driven dialogue that advances plot and reveals personality.',
        responseFormat: 'structured',
        structuredSchema: dialogueResponseSchema,
        streaming: true,
        streamingOptions
      };

      const result = await this.generateWithAI<DialogueResponse>(prompt, options);
      return result;
    });
  }

  /**
   * Generate character profile with streaming
   */
  async generateCharacterProfileStream(
    request: {
      name: string;
      role: string;
      genre: string;
      traits?: string[];
      background?: string;
      projectId: string;
    },
    streamingOptions?: StreamingOptions
  ): Promise<ServiceResponse<CharacterProfileGeneration>> {
    return this.withErrorHandling(async () => {
      const prompt = `Create a detailed character profile for "${request.name}" who is a ${request.role} in a ${request.genre} story.

${request.traits ? `Key traits: ${request.traits.join(', ')}` : ''}
${request.background ? `Background: ${request.background}` : ''}

Develop a rich, complex character with clear motivations, flaws, and growth potential.`;

      const aiConfig = getAIConfig('CONTENT_GENERATION');

      const options: AIGenerationOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.max_tokens,
        systemPrompt: 'You are an expert character development specialist. Create rich, complex characters with clear motivations and compelling backstories.',
        responseFormat: 'structured',
        structuredSchema: characterProfileGenerationSchema,
        streaming: true,
        streamingOptions
      };

      const result = await this.generateWithAI<CharacterProfileGeneration>(prompt, options);
      return result;
    });
  }

  private buildSystemPrompt(type: string, style?: string, tone?: string): string {
    const basePrompt = 'You are an expert creative writing assistant specializing in high-quality content generation.';

    const typePrompts: Record<string, string> = {
      scene: `${basePrompt} Create vivid, engaging scenes that advance the plot and develop characters.`,
      dialogue: `${basePrompt} Write natural, character-appropriate dialogue that reveals personality and advances the story.`,
      description: `${basePrompt} Create immersive, sensory descriptions that enhance the story without overwhelming the reader.`,
      chapter: `${basePrompt} Write compelling chapters that maintain pacing, develop characters, and advance the plot.`,
      character: `${basePrompt} Develop rich, complex characters with clear motivations, flaws, and growth arcs.`,
      'plot-outline': `${basePrompt} Create structured, engaging plot outlines that maintain narrative tension and character development.`
    };

    let prompt = typePrompts[type] || basePrompt;

    if (style) {
      prompt += ` Writing style: ${style}.`;
    }

    if (tone) {
      prompt += ` Tone: ${tone}.`;
    }

    return prompt;
  }

  private buildPrompt(request: WritingTask['input']): string {
    let prompt = request.prompt || '';

    if (request.context) {
      prompt += '\n\nContext:\n';
      Object.entries(request.context).forEach(([key, value]) => {
        if (key !== 'type' && value) {
          prompt += `- ${key}: ${value}\n`;
        }
      });
    }

    return prompt;
  }
}