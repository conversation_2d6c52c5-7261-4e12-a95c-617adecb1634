'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { 
  ChevronLeft, 
  ChevronRight, 
  Sparkles, 
  BookOpen, 
  Users, 
  Globe,
  Target,
  Settings,
  CheckCircle,
  Plus,
  Wand2,
  Lightbulb,
  HelpCircle,
  ArrowRight,
  ArrowLeft,
  CreditCard,
  Palette,
  Building,
  Hash,
  Check
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCelebration } from '@/contexts/celebration-context';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/lib/services/logger';
import { ANIMATION_DURATION } from '@/lib/constants'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Wizard mode types
export type WizardMode = 'live' | 'demo';
export type WizardDisplay = 'page' | 'modal' | 'guided';

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface FormData {
  title: string;
  description: string;
  genre: string;
  subgenre: string;
  targetAudience: string;
  wordCount: string;
  chapters: string;
  tones: string[];
  themes: string[];
  contentElements: string[];
  protagonist: string;
  antagonist: string;
  setting: string;
  structure: string;
  pacing: string;
  narrativeVoice: string;
  tense: string;
  timePeriod: string;
  protagonistName: string;
  universeId: string;
  seriesId: string;
  isStandalone: string;
  [key: string]: string | string[];
}

const wizardSteps: WizardStep[] = [
  { id: "foundation", title: "Story Foundation", description: "Title, genre, style, and audience", icon: BookOpen },
  { id: "framework", title: "Story Framework", description: "Structure, pacing, and technical specs", icon: Building },
  { id: "world", title: "World & Characters", description: "Characters, setting, and world-building", icon: Users },
  { id: "elements", title: "Themes & Elements", description: "Themes, content, and story elements", icon: Palette },
  { id: "review", title: "Review & Create", description: "Review and create your project", icon: Sparkles },
];

// Demo data for different themes/genres
const demoDataSets = {
  fantasy: {
    title: "The Crystal Kingdoms",
    description: "A fantasy epic about a young mage discovering ancient crystal magic",
    genre: "fantasy",
    subgenre: "epic-fantasy",
    tones: ["Epic", "Adventurous", "Mysterious"],
    themes: ["Power", "Destiny", "Friendship", "Sacrifice", "Good vs Evil"],
    contentElements: ["Magic", "Adventure", "Politics", "War", "Prophecy"],
    targetAudience: "young-adult",
    protagonist: "Aria Stormwind",
    antagonist: "Lord Shadowmere",
    setting: "Mystical realm of Aethermoor",
    wordCount: "80000",
    chapters: "25",
    structure: "three-act",
    pacing: "medium",
    narrativeVoice: "third-limited",
    tense: "past",
    timePeriod: "medieval-fantasy",
    protagonistName: "Aria",
    universeId: "demo-universe-1",
    seriesId: "demo-series-1",
    isStandalone: "false"
  },
  scifi: {
    title: "Neural Networks",
    description: "A cyberpunk thriller about AI consciousness and human identity",
    genre: "sci-fi",
    subgenre: "cyberpunk",
    tones: ["Dark", "Gritty", "Philosophical", "Suspenseful"],
    themes: ["Identity", "Technology", "Freedom", "Truth", "Change"],
    contentElements: ["Technology", "Action", "Mystery", "Philosophy", "Dystopia"],
    targetAudience: "adult",
    protagonist: "Alex Chen",
    antagonist: "The Collective",
    setting: "Neo-Tokyo 2087",
    wordCount: "90000",
    chapters: "30",
    structure: "seven-point",
    pacing: "fast",
    narrativeVoice: "first",
    tense: "present",
    timePeriod: "near-future",
    protagonistName: "Alex",
    universeId: "demo-universe-2",
    seriesId: "none",
    isStandalone: "true"
  },
  mystery: {
    title: "The Lighthouse Keeper's Secret",
    description: "A cozy mystery about secrets hidden in a coastal lighthouse",
    genre: "mystery",
    subgenre: "cozy-mystery",
    tones: ["Mysterious", "Cozy", "Suspenseful", "Contemplative"],
    themes: ["Justice", "Truth", "Secrets", "Community", "Trust"],
    contentElements: ["Mystery", "Crime", "Drama", "Exploration"],
    targetAudience: "adult",
    protagonist: "Detective Sarah Mills",
    antagonist: "The Anonymous Caller",
    setting: "Coastal Maine village",
    wordCount: "70000",
    chapters: "20",
    structure: "three-act",
    pacing: "medium",
    narrativeVoice: "third-limited",
    tense: "past",
    timePeriod: "contemporary",
    protagonistName: "Sarah",
    universeId: "none",
    seriesId: "demo-series-2",
    isStandalone: "false"
  }
};

// Guided mode step guidance
const stepGuidance = [
  {
    title: "Let's Start with the Basics",
    description: "Give your story a title and brief description. Don't worry about making it perfect - you can always change it later!",
    tips: [
      "Choose a working title that captures the essence of your story",
      "The description helps AI understand your vision - be descriptive but concise",
      "Think about what makes your story unique or interesting"
    ]
  },
  {
    title: "Choose Your Story's Genre and Style",
    description: "These choices will guide how the AI develops your story structure, characters, and writing style.",
    tips: [
      "Genre affects plot structure and character archetypes",
      "Narrative voice determines who tells the story (first person, third person, etc.)",
      "Tense choice impacts the story's immediacy and perspective"
    ]
  },
  {
    title: "Define Your Story Structure",
    description: "How your story unfolds affects reader engagement and pacing throughout your novel.",
    tips: [
      "Three-act structure is classic and reliable for most genres",
      "Pacing preference affects how quickly events unfold",
      "Consider your target audience when choosing structure complexity"
    ]
  },
  {
    title: "Build Your World and Characters",
    description: "Rich characters and immersive settings are the heart of compelling fiction.",
    tips: [
      "Character complexity affects how much development each character gets",
      "Setting influences mood, conflict, and plot possibilities",
      "Time period impacts available technology, social norms, and conflicts"
    ]
  },
  {
    title: "Establish Themes and Content Guidelines",
    description: "Themes give your story depth, while content ratings help target the right audience.",
    tips: [
      "Themes provide deeper meaning beyond the surface plot",
      "Content rating affects language, violence, and romantic content",
      "Consider what message or experience you want readers to take away"
    ]
  },
  {
    title: "Set Your Technical Specifications",
    description: "These details help the AI create appropriately sized chapters and structure the narrative.",
    tips: [
      "Word count affects story scope - more words allow for more subplots and development",
      "Chapter count determines pacing - more chapters create faster pacing",
      "POV characters affect narrative complexity and reader connection"
    ]
  },
  {
    title: "Complete Your Project Setup",
    description: "You're almost there! Complete the payment to unlock all of BookScribe's AI-powered writing features.",
    tips: [
      "One-time project fee includes unlimited AI assistance for this book",
      "Your payment information is secure and encrypted",
      "You'll have immediate access to all features after payment"
    ]
  }
];

interface UnifiedProjectWizardProps {
  mode?: WizardMode;
  display?: WizardDisplay;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  guided?: boolean;
  onComplete?: (projectId: string) => void;
  demoTheme?: keyof typeof demoDataSets;
}

export function UnifiedProjectWizard({ 
  mode = 'live',
  display = 'page',
  open = true,
  onOpenChange,
  guided = false,
  onComplete,
  demoTheme = 'fantasy'
}: UnifiedProjectWizardProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const { celebrate } = useCelebration();
  const supabase = createClient();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Initialize form data based on mode
  const [formData, setFormData] = useState<FormData>(() => {
    if (mode === 'demo') {
      return demoDataSets[demoTheme];
    }
    return {
      title: "",
      description: "",
      genre: "",
      subgenre: "",
      targetAudience: "adult",
      wordCount: "80000",
      chapters: "25",
      tones: [],
      themes: [],
      contentElements: [],
      protagonist: "",
      antagonist: "",
      setting: "",
      structure: "three-act",
      pacing: "medium",
      narrativeVoice: "third-limited",
      tense: "past",
      timePeriod: "",
      protagonistName: "",
      universeId: "none",
      seriesId: "none",
      isStandalone: "true"
    };
  });

  const progress = ((currentStep + 1) / wizardSteps.length) * 100;

  const nextStep = useCallback(() => {
    if (currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep]);

  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const updateFormData = useCallback((field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const canProceed = useCallback(() => {
    switch (currentStep) {
      case 0: // Foundation
        return formData.title.trim().length > 0 && formData.description.trim().length > 0 && formData.genre;
      case 1: // Framework
        return !!(formData.structure && formData.pacing && formData.wordCount && formData.chapters);
      case 2: // World & Characters
        return !!(formData.protagonist || formData.setting);
      case 3: // Elements
        return true; // Themes are optional
      case 4: // Review
        return mode === 'demo' || user; // Demo mode doesn't need auth
      default:
        return false;
    }
  }, [currentStep, formData, mode, user]);

  const handleGenerate = async () => {
    if (mode === 'demo') {
      // Demo mode: simulate success
      setIsGenerating(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsGenerating(false);
      
      toast({
        title: "Demo project created!",
        description: "This is a demonstration - no actual project was created.",
      });
      
      if (display === 'modal') {
        onOpenChange?.(false);
      }
      
      if (onComplete) {
        onComplete('demo-project-id');
      } else {
        router.push('/demo');
      }
      
      return;
    }

    // Live mode: actual project creation
    if (!user) {
      toast({
        title: "Not authenticated",
        description: "Please sign in to create a project",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    
    try {
      // Create the project with structured data
      const projectData = {
        user_id: user.id,
        title: formData.title,
        description: formData.description,
        primary_genre: formData.genre,
        sub_genre: formData.subgenre,
        tone_style: formData.tones.join(', '),
        target_audience: formData.targetAudience,
        target_word_count: parseInt(formData.wordCount),
        target_chapters: parseInt(formData.chapters),
        narrative_voice: formData.narrativeVoice,
        tense: formData.tense,
        structure_type: formData.structure,
        pacing_preference: formData.pacing,
        time_period: formData.timePeriod,
        protagonist_name: formData.protagonistName,
        themes: formData.themes.join(', '),
        status: 'active'
      };

      const { data: project, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (error) throw error;

      // Link to series if selected and not standalone
      if (formData.isStandalone === 'false' && formData.seriesId && formData.seriesId !== 'none') {
        // Get current book count to determine book number
        const { data: existingBooks } = await supabase
          .from('series_books')
          .select('book_number')
          .eq('series_id', formData.seriesId)
          .order('book_number', { ascending: false })
          .limit(1);

        const nextBookNumber = existingBooks && existingBooks.length > 0 
          ? existingBooks[0].book_number + 1 
          : 1;

        const { error: seriesError } = await supabase
          .from('series_books')
          .insert([{
            series_id: formData.seriesId,
            project_id: project.id,
            book_number: nextBookNumber,
            book_role: 'main',
            chronological_order: nextBookNumber,
            publication_order: nextBookNumber
          }]);

        if (seriesError) {
          logger.error('Error linking project to series:', seriesError);
          // Don't fail the entire creation, just log the error
        }
      }

      // Trigger celebration
      celebrate({
        id: 'project-created',
        type: 'achievement',
        title: 'Project Created!',
        message: `${formData.title} is ready for your imagination`,
        icon: BookOpen,
        level: 'medium'
      });

      // Close modal if in modal mode
      if (display === 'modal') {
        onOpenChange?.(false);
      }

      // Navigate or callback
      if (onComplete) {
        onComplete(project.id);
      } else {
        router.push(`/projects/${project.id}`);
      }
      
      toast({
        title: "Project created!",
        description: "Your AI agents are preparing your story structure...",
      });
      
    } catch (error) {
      logger.error('Error creating project:', error);
      toast({
        title: "Error",
        description: "Failed to create project. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClose = useCallback(() => {
    if (currentStep > 0 && mode === 'live') {
      const confirmed = window.confirm("Are you sure you want to cancel? Your progress will be lost.");
      if (!confirmed) return;
    }
    setCurrentStep(0);
    onOpenChange?.(false);
  }, [currentStep, mode, onOpenChange]);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return <StepFoundation formData={formData} updateFormData={updateFormData} setFormData={setFormData} mode={mode} />;
      case 1:
        return <StepFramework formData={formData} updateFormData={updateFormData} mode={mode} />;
      case 2:
        return <StepWorldCharacters formData={formData} updateFormData={updateFormData} mode={mode} />;
      case 3:
        return <StepThemesElements formData={formData} updateFormData={updateFormData} setFormData={setFormData} mode={mode} />;
      case 4:
        return <StepReview 
          formData={formData} 
          mode={mode} 
          onComplete={handleGenerate}
          isGenerating={isGenerating}
        />;
      default:
        return null;
    }
  };

  const renderGuidedWrapper = (children: React.ReactNode) => {
    if (!guided) return children;

    const currentGuidance = stepGuidance[currentStep];
    
    return (
      <div className="space-y-8">
        {/* Progress indicator */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">Guided Setup</Badge>
              <span className="text-sm text-muted-foreground">
                Step {currentStep + 1} of {wizardSteps.length}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/projects/new')}
              className="text-muted-foreground"
            >
              Switch to Advanced Mode
            </Button>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="h-2 bg-muted rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Guided step content */}
        {currentGuidance && (
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h2 className="text-2xl font-bold">{currentGuidance.title}</h2>
                {canProceed() && <CheckCircle className="h-5 w-5 text-success" />}
              </div>
              <p className="text-muted-foreground">{currentGuidance.description}</p>
            </div>

            {currentGuidance.tips.length > 0 && (
              <Alert>
                <Lightbulb className="h-4 w-4" />
                <AlertDescription>
                  <strong>Tips:</strong>
                  <ul className="mt-2 space-y-1">
                    {currentGuidance.tips.map((tip, index) => (
                      <li key={index} className="text-sm">• {tip}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            <Card>
              <CardContent className="pt-6">
                {children}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navigation */}
        <div className="flex items-center justify-between pt-6 border-t">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex gap-2">
            {currentStep < wizardSteps.length - 1 ? (
              <Button
                onClick={nextStep}
                disabled={!canProceed()}
              >
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={handleGenerate}
                disabled={!canProceed() || isGenerating}
              >
                {isGenerating ? 'Creating Project...' : 'Create Project'}
              </Button>
            )}
          </div>
        </div>

        {/* Help hint */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground flex items-center justify-center gap-1">
            <HelpCircle className="h-3 w-3" />
            Need help? Each step includes tips to guide your choices.
          </p>
        </div>
      </div>
    );
  };

  const renderWizardContent = () => {
    const content = (
      <div className={display === 'modal' ? 'max-w-5xl xl:max-w-6xl' : 'max-w-5xl xl:max-w-6xl 2xl:max-w-7xl mx-auto'}>
        <Card className="border-border bg-gradient-to-br from-card via-card to-card/95 backdrop-blur-sm shadow-xl">
          <CardHeader className="pb-6 border-b bg-gradient-to-r from-primary/5 to-accent/5">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl md:text-3xl lg:text-4xl flex items-center gap-3">
                <Wand2 className="w-7 h-7 md:w-8 md:h-8 text-primary animate-pulse" />
                {mode === 'demo' ? 'Project Creation Demo' : 'Create Your Story'}
              </CardTitle>
              <Badge variant={mode === 'demo' ? 'outline' : 'secondary'} className="text-sm px-4 py-2">
                {mode === 'demo' ? 'Interactive Demo' : 'AI-Powered Setup'}
              </Badge>
            </div>
            
            {/* Progress Bar */}
            <div className="space-y-3 mt-6">
              <div className="flex justify-between text-sm md:text-base text-muted-foreground font-medium">
                <span>Step {currentStep + 1} of {wizardSteps.length}</span>
                <span>{Math.round(progress)}% Complete</span>
              </div>
              <Progress value={progress} className="h-3 shadow-inner" />
            </div>

            {/* Step Navigation */}
            <TooltipProvider>
              <div className="flex items-center justify-center gap-4 md:gap-6 mt-6 overflow-x-auto pb-3 px-1">
                {wizardSteps.map((step, index) => {
                  const StepIcon = step.icon;
                  const isActive = index === currentStep;
                  const isCompleted = index < currentStep;
                  
                  return (
                    <Tooltip key={step.id}>
                      <TooltipTrigger asChild>
                        <button
                          className={`relative p-4 rounded-full transition-all duration-300 transform hover:scale-110 ${
                            isActive 
                              ? 'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-lg scale-110' 
                              : isCompleted
                              ? 'bg-primary/20 text-primary hover:bg-primary/30 shadow-md'
                              : 'bg-muted text-muted-foreground hover:bg-muted/80 hover:shadow-md'
                          }`}
                          onClick={() => isCompleted ? setCurrentStep(index) : null}
                          disabled={!isCompleted && !isActive}
                        >
                          <StepIcon className="w-6 h-6 md:w-7 md:h-7" />
                          {isCompleted && (
                            <CheckCircle className="absolute -top-1 -right-1 w-5 h-5 text-success bg-background rounded-full" />
                          )}
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="font-medium">{step.title}</p>
                        <p className="text-xs opacity-90">{step.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  );
                })}
              </div>
            </TooltipProvider>
          </CardHeader>

          <div className="px-6 sm:px-8 lg:px-12 xl:px-16 py-6 lg:py-8 overflow-y-auto" style={{ maxHeight: display === 'modal' ? 'calc(90vh - 320px)' : 'auto' }}>
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: ANIMATION_DURATION.FAST }}
                className="min-h-[450px] lg:min-h-[500px]"
              >
                <div className="mb-8">
                  <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-3 text-foreground">{wizardSteps[currentStep]?.title}</h3>
                  <p className="text-muted-foreground text-base md:text-lg">{wizardSteps[currentStep]?.description}</p>
                </div>

                {renderCurrentStep()}
              </motion.div>
            </AnimatePresence>
          </div>

          <div className="flex justify-between p-6 lg:p-8 border-t bg-gradient-to-r from-muted/30 to-muted/10">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="text-base px-6 py-6 hover:scale-105 transition-transform"
            >
              <ChevronLeft className="w-5 h-5 mr-2" />
              Previous
            </Button>
            
            {currentStep < wizardSteps.length - 1 ? (
              <Button
                onClick={nextStep}
                disabled={!canProceed()}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                Next
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={handleGenerate}
                disabled={!canProceed() || isGenerating}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                {isGenerating ? (
                  <>
                    <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                    {mode === 'demo' ? 'Generating Demo...' : 'Generating...'}
                  </>
                ) : (
                  <>
                    <Sparkles className="w-5 h-5 mr-2" />
                    {mode === 'demo' ? 'Create Demo Story' : 'Create Story'}
                  </>
                )}
              </Button>
            )}
          </div>
        </Card>
      </div>
    );

    if (guided) {
      return renderGuidedWrapper(content);
    }

    return content;
  };

  // Modal display
  if (display === 'modal') {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-5xl xl:max-w-6xl max-h-[95vh] overflow-hidden p-0">
          {renderWizardContent()}
        </DialogContent>
      </Dialog>
    );
  }

  // Page display
  return renderWizardContent();
}

// Theme and tone options
const themeOptions = [
  // Core Themes
  'Love', 'Power', 'Justice', 'Freedom', 'Sacrifice', 'Betrayal', 'Redemption', 'Identity',
  'Coming of Age', 'Good vs Evil', 'Hope', 'Survival', 'Family', 'Friendship', 'Truth', 'Honor',
  'Revenge', 'Forgiveness', 'Loyalty', 'Courage', 'Faith', 'Destiny', 'Fate', 'Change',
  // Additional Themes
  'Loss', 'Grief', 'Healing', 'Discovery', 'Adventure', 'Mystery', 'Secrets', 'Trust',
  'Ambition', 'Corruption', 'Innocence', 'Experience', 'Nature', 'Technology', 'Progress', 'Tradition',
  'War', 'Peace', 'Unity', 'Division', 'Leadership', 'Rebellion', 'Conformity', 'Individuality',
  'Time', 'Memory', 'Legacy', 'Mortality', 'Immortality', 'Transformation'
];

const toneOptions = [
  'Dark', 'Light', 'Gritty', 'Whimsical', 'Serious', 'Humorous', 'Epic', 'Intimate',
  'Mysterious', 'Suspenseful', 'Romantic', 'Melancholic', 'Hopeful', 'Cynical', 'Nostalgic', 'Futuristic',
  'Satirical', 'Philosophical', 'Action-packed', 'Contemplative', 'Thrilling', 'Cozy', 'Intense', 'Dreamlike'
];

const contentElements = [
  'Magic', 'Technology', 'Romance', 'Action', 'Politics', 'War', 'Exploration', 'Mystery',
  'Supernatural', 'Science', 'Philosophy', 'Religion', 'Crime', 'Adventure', 'Horror', 'Comedy',
  'Drama', 'Intrigue', 'Espionage', 'Heist', 'Prophecy', 'Time Travel', 'Alternate History', 'Dystopia'
];

// Step Components
function StepFoundation({ formData, updateFormData, setFormData, mode }: { 
  formData: FormData; 
  updateFormData: (field: string, value: string) => void;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  mode: WizardMode;
}) {
  const [universes, setUniverses] = useState<Array<{ id: string; name: string; description?: string }>>([]);
  const [series, setSeries] = useState<Array<{ id: string; title: string; universe_id?: string }>>([]);
  const [isLoadingUniverses, setIsLoadingUniverses] = useState(false);
  const [isLoadingSeries, setIsLoadingSeries] = useState(false);

  // Load universes and series on component mount
  useEffect(() => {
    if (mode === 'live') {
      loadUniverses();
      loadSeries();
    } else {
      // Set demo data for universe and series
      setUniverses([
        { id: "demo-universe-1", name: "The Crystal Realms", description: "A fantasy universe of magical kingdoms and ancient powers" },
        { id: "demo-universe-2", name: "Neo-Future Earth", description: "A cyberpunk universe of AI consciousness and digital mysteries" }
      ]);
      setSeries([
        { id: "demo-series-1", title: "The Crystal Saga", universe_id: "demo-universe-1" },
        { id: "demo-series-2", title: "The Mystery Chronicles", universe_id: undefined },
        { id: "demo-series-3", title: "Neural Web Trilogy", universe_id: "demo-universe-2" }
      ]);
    }
  }, [mode]);

  const loadUniverses = async () => {
    setIsLoadingUniverses(true);
    try {
      const response = await fetch('/api/universes');
      if (response.ok) {
        const data = await response.json();
        setUniverses(data.universes || []);
      }
    } catch (error) {
      logger.error('Error loading universes:', error);
    } finally {
      setIsLoadingUniverses(false);
    }
  };

  const loadSeries = async () => {
    setIsLoadingSeries(true);
    try {
      const response = await fetch('/api/series');
      if (response.ok) {
        const data = await response.json();
        setSeries(data.series || []);
      }
    } catch (error) {
      logger.error('Error loading series:', error);
    } finally {
      setIsLoadingSeries(false);
    }
  };

  // Filter series based on selected universe
  const filteredSeries = formData.universeId && formData.universeId !== 'none'
    ? series.filter(s => s.universe_id === formData.universeId)
    : series.filter(s => !s.universe_id);

  const genres = [
    { id: "fantasy", name: "Fantasy", subgenres: ["Epic Fantasy", "Urban Fantasy", "Dark Fantasy", "High Fantasy"] },
    { id: "sci-fi", name: "Science Fiction", subgenres: ["Space Opera", "Cyberpunk", "Dystopian", "Hard Sci-Fi"] },
    { id: "romance", name: "Romance", subgenres: ["Contemporary", "Historical", "Paranormal", "Romantic Suspense"] },
    { id: "mystery", name: "Mystery", subgenres: ["Cozy Mystery", "Police Procedural", "Noir", "Psychological Thriller"] },
    { id: "thriller", name: "Thriller", subgenres: ["Action", "Psychological", "Political", "Techno-thriller"] },
    { id: "literary", name: "Literary Fiction", subgenres: ["Contemporary", "Historical", "Experimental", "Philosophical"] }
  ];

  return (
    <div className="space-y-8">
      {/* Title and Audience on same line */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="title" className="text-base md:text-lg font-semibold">Project Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => updateFormData("title", e.target.value)}
            placeholder="Enter your story title..."
            className="text-lg md:text-xl h-12 md:h-14 px-4 md:px-6 border-2 focus:border-primary transition-colors"
            disabled={mode === 'demo'}
          />
        </div>
        
        <div className="space-y-3">
          <Label htmlFor="audience" className="text-base md:text-lg font-semibold">Target Audience</Label>
          <Select 
            value={formData.targetAudience} 
            onValueChange={(value) => updateFormData("targetAudience", value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger className="h-12 md:h-14 text-base md:text-lg border-2 hover:border-primary/50 transition-colors">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="middle-grade" className="text-base md:text-lg py-3">Middle Grade (8-12)</SelectItem>
              <SelectItem value="young-adult" className="text-base md:text-lg py-3">Young Adult (13-17)</SelectItem>
              <SelectItem value="new-adult" className="text-base md:text-lg py-3">New Adult (18-25)</SelectItem>
              <SelectItem value="adult" className="text-base md:text-lg py-3">Adult (25+)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Description full width */}
      <div className="space-y-3">
        <Label htmlFor="description" className="text-base md:text-lg font-semibold">Story Concept</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => updateFormData("description", e.target.value)}
          className="min-h-[120px] text-base md:text-lg p-4 md:p-6 border-2 focus:border-primary transition-colors resize-none"
          placeholder="Describe your story idea in a few sentences..."
          disabled={mode === 'demo'}
        />
      </div>

      {/* Genre Selection */}
      <div className="space-y-4">
        <Label className="text-lg md:text-xl font-semibold">Primary Genre</Label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {genres.map((genre) => (
            <Card
              key={genre.id}
              className={`cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-xl ${
                formData.genre === genre.id
                  ? 'border-2 border-primary bg-gradient-to-br from-primary/20 to-primary/10 shadow-lg scale-105'
                  : 'hover:border-primary/50 border-2 border-transparent'
              } ${mode === 'demo' ? 'pointer-events-none' : ''}`}
              onClick={() => mode !== 'demo' && updateFormData("genre", genre.id)}
            >
              <CardContent className="p-4 md:p-6 text-center">
                <h4 className="font-semibold text-base md:text-lg">{genre.name}</h4>
                {formData.genre === genre.id && (
                  <CheckCircle className="w-5 h-5 text-primary mx-auto mt-2" />
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Subgenre and Series/Standalone on same line */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {formData.genre && (
          <div className="space-y-3">
            <Label>Subgenre</Label>
            <Select 
              value={formData.subgenre} 
              onValueChange={(value) => updateFormData("subgenre", value)}
              disabled={mode === 'demo'}
            >
              <SelectTrigger className="h-12 md:h-14 text-base md:text-lg border-2 hover:border-primary/50 transition-colors">
                <SelectValue placeholder="Select a subgenre" />
              </SelectTrigger>
              <SelectContent>
                {genres.find(g => g.id === formData.genre)?.subgenres.map((sub) => (
                  <SelectItem key={sub} value={sub.toLowerCase().replace(/\s+/g, '-')} className="text-base md:text-lg py-3">
                    {sub}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="space-y-3">
          <Label className="text-base md:text-lg font-semibold">Project Type</Label>
          <div className="flex gap-4">
            <div className={`flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer flex-1 border-2 border-transparent hover:border-primary/30 ${
              formData.isStandalone === "true" ? 'bg-primary/10 border-primary' : ''
            }`} onClick={() => {
                  updateFormData("isStandalone", "true");
                  updateFormData("universeId", "none");
                  updateFormData("seriesId", "none");
                }}>
              <input
                type="radio"
                id="standalone"
                name="projectType"
                value="true"
                checked={formData.isStandalone === "true"}
                onChange={(e) => {
                  updateFormData("isStandalone", e.target.value);
                  updateFormData("universeId", "none");
                  updateFormData("seriesId", "none");
                }}
                disabled={false}
                className="h-5 w-5 text-primary"
              />
              <Label htmlFor="standalone" className="text-sm md:text-base cursor-pointer">Standalone</Label>
            </div>
            <div className={`flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer flex-1 border-2 border-transparent hover:border-primary/30 ${
              formData.isStandalone === "false" ? 'bg-primary/10 border-primary' : ''
            }`} onClick={() => updateFormData("isStandalone", "false")}>
              <input
                type="radio"
                id="series"
                name="projectType"
                value="false"
                checked={formData.isStandalone === "false"}
                onChange={(e) => updateFormData("isStandalone", e.target.value)}
                disabled={false}
                className="h-5 w-5 text-primary"
              />
              <Label htmlFor="series" className="text-sm md:text-base cursor-pointer">Series/Universe</Label>
            </div>
          </div>
        </div>
      </div>

      {/* Universe Selection */}
      {formData.isStandalone === "false" && (
        <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="universe">Universe (Optional)</Label>
              <Select 
                value={formData.universeId} 
                onValueChange={(value) => {
                  updateFormData("universeId", value);
                  // Clear series if universe changed
                  if (formData.seriesId && formData.seriesId !== 'none' && value !== formData.universeId) {
                    updateFormData("seriesId", "none");
                  }
                }}
                disabled={isLoadingUniverses}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a shared universe (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Universe</SelectItem>
                  {universes.map((universe) => (
                    <SelectItem key={universe.id} value={universe.id}>
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        <span>{universe.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formData.universeId && formData.universeId !== 'none' && (
                <p className="text-xs text-muted-foreground">
                  {universes.find(u => u.id === formData.universeId)?.description}
                </p>
              )}
            </div>

            {/* Series Selection */}
            <div className="space-y-2">
              <Label htmlFor="series">Series (Optional)</Label>
              <Select 
                value={formData.seriesId} 
                onValueChange={(value) => updateFormData("seriesId", value)}
                disabled={isLoadingSeries}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an existing series (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">New Series / Standalone</SelectItem>
                  {filteredSeries.map((seriesItem) => (
                    <SelectItem key={seriesItem.id} value={seriesItem.id}>
                      <div className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        <span>{seriesItem.title}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

      {mode === 'demo' && (
        <div className="p-4 bg-info-light border border-blue-200 rounded-lg">
          <div className="flex items-start gap-3">
            <Sparkles className="w-5 h-5 text-info mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800">Demo Mode</h4>
              <p className="text-sm text-blue-700 mt-1">
                This demo showcases the full project creation experience with universe and series integration. All fields are interactive to demonstrate the complete workflow.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Tone Selection with Checkboxes */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-lg md:text-xl font-semibold">Tone & Mood</Label>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, tones: toneOptions.slice() }))}
              className="text-xs"
            >
              Select All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, tones: [] }))}
              className="text-xs"
            >
              Clear All
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 p-4 border-2 rounded-xl max-h-64 overflow-y-auto">
          {toneOptions.map((tone) => (
            <label
              key={tone}
              className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-2 rounded-lg transition-colors"
            >
              <input
                type="checkbox"
                checked={formData.tones.includes(tone)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setFormData(prev => ({ ...prev, tones: [...prev.tones, tone] }));
                  } else {
                    setFormData(prev => ({ ...prev, tones: prev.tones.filter(t => t !== tone) }));
                  }
                }}
                className="h-4 w-4 text-primary rounded border-2"
                disabled={mode === 'demo'}
              />
              <span className="text-sm md:text-base">{tone}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
}

// Step 2: Story Framework (Structure, Pacing, Technical)
function StepFramework({ formData, updateFormData, mode }: { 
  formData: FormData; 
  updateFormData: (field: string, value: string) => void;
  mode: WizardMode;
}) {
  const structures = [
    { id: "three-act", name: "Three-Act Structure", desc: "Classic beginning, middle, end", icon: "📖" },
    { id: "heros-journey", name: "Hero's Journey", desc: "Campbell's monomyth structure", icon: "🗡️" },
    { id: "seven-point", name: "Seven-Point Story", desc: "Dan Wells' structure method", icon: "7️⃣" },
    { id: "save-the-cat", name: "Save the Cat", desc: "Blake Snyder's beat sheet", icon: "🐱" },
    { id: "freytag", name: "Freytag's Pyramid", desc: "Five-act dramatic structure", icon: "🔺" },
    { id: "kishotenketsu", name: "Kishōtenketsu", desc: "Four-act Asian narrative structure", icon: "🏯" }
  ];

  return (
    <div className="space-y-8">
      {/* Story Structure */}
      <div className="space-y-4">
        <Label className="text-lg md:text-xl font-semibold">Story Structure</Label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {structures.map((structure) => (
            <Card
              key={structure.id}
              className={`cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-xl ${
                formData.structure === structure.id
                  ? 'border-2 border-primary bg-gradient-to-br from-primary/20 to-primary/10 shadow-lg scale-105'
                  : 'hover:border-primary/50 border-2 border-transparent'
              } ${mode === 'demo' ? 'pointer-events-none' : ''}`}
              onClick={() => mode !== 'demo' && updateFormData("structure", structure.id)}
            >
              <CardContent className="p-4 md:p-6 text-center">
                <div className="text-2xl md:text-3xl mb-2">{structure.icon}</div>
                <h4 className="font-semibold text-sm md:text-base">{structure.name}</h4>
                <p className="text-xs text-muted-foreground mt-1">{structure.desc}</p>
                {formData.structure === structure.id && (
                  <CheckCircle className="w-5 h-5 text-primary mx-auto mt-2" />
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Pacing, Voice, and Tense on same line */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-3">
          <Label className="text-base font-semibold">Pacing</Label>
          <Select 
            value={formData.pacing} 
            onValueChange={(value) => updateFormData("pacing", value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger className="h-12 text-base border-2 hover:border-primary/50 transition-colors">
              <SelectValue placeholder="Select pacing" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="slow" className="text-base py-2">Slow Burn</SelectItem>
              <SelectItem value="medium" className="text-base py-2">Moderate</SelectItem>
              <SelectItem value="fast" className="text-base py-2">Fast-Paced</SelectItem>
              <SelectItem value="variable" className="text-base py-2">Variable</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <Label className="text-base font-semibold">Narrative Voice</Label>
          <Select 
            value={formData.narrativeVoice} 
            onValueChange={(value) => updateFormData("narrativeVoice", value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger className="h-12 text-base border-2 hover:border-primary/50 transition-colors">
              <SelectValue placeholder="Select voice" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="first" className="text-base py-2">First Person</SelectItem>
              <SelectItem value="third-limited" className="text-base py-2">Third Limited</SelectItem>
              <SelectItem value="third-omniscient" className="text-base py-2">Third Omniscient</SelectItem>
              <SelectItem value="second" className="text-base py-2">Second Person</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <Label className="text-base font-semibold">Tense</Label>
          <Select 
            value={formData.tense} 
            onValueChange={(value) => updateFormData("tense", value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger className="h-12 text-base border-2 hover:border-primary/50 transition-colors">
              <SelectValue placeholder="Select tense" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="past" className="text-base py-2">Past Tense</SelectItem>
              <SelectItem value="present" className="text-base py-2">Present Tense</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Word Count and Chapters on same line */}
      <div className="space-y-4">
        <Label className="text-lg md:text-xl font-semibold">Technical Specifications</Label>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-3">
            <Label className="text-base font-semibold">Target Word Count</Label>
            <Select 
              value={formData.wordCount} 
              onValueChange={(value) => updateFormData("wordCount", value)}
              disabled={mode === 'demo'}
            >
              <SelectTrigger className="h-12 text-base border-2 hover:border-primary/50 transition-colors">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="20000" className="text-base py-2">20,000 words (Novella)</SelectItem>
                <SelectItem value="50000" className="text-base py-2">50,000 words (Short Novel)</SelectItem>
                <SelectItem value="80000" className="text-base py-2">80,000 words (Standard)</SelectItem>
                <SelectItem value="120000" className="text-base py-2">120,000 words (Epic)</SelectItem>
                <SelectItem value="200000" className="text-base py-2">200,000+ words (Series)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label className="text-base font-semibold">Estimated Chapters</Label>
            <Input
              type="number"
              value={formData.chapters}
              onChange={(e) => updateFormData("chapters", e.target.value)}
              min="1"
              max="100"
              className="h-12 text-base px-4 border-2 focus:border-primary transition-colors"
              disabled={mode === 'demo'}
            />
          </div>
        </div>
        
        {/* Chapter Length Calculator */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground">
            <span className="font-medium">Average Chapter Length:</span> Approximately {Math.round(parseInt(formData.wordCount) / parseInt(formData.chapters)).toLocaleString()} words per chapter
          </p>
        </div>
      </div>
    </div>
  );
}

// Step 3: World & Characters
function StepWorldCharacters({ formData, updateFormData, mode }: { 
  formData: FormData; 
  updateFormData: (field: string, value: string) => void;
  mode: WizardMode;
}) {
  return (
    <div className="space-y-8">
      {/* Characters on same line */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="protagonist" className="text-base md:text-lg font-semibold">Main Protagonist</Label>
          <Input
            id="protagonist"
            value={formData.protagonist}
            onChange={(e) => updateFormData("protagonist", e.target.value)}
            placeholder="e.g., Sarah Chen, a brilliant detective"
            className="h-12 md:h-14 text-base md:text-lg px-4 md:px-6 border-2 focus:border-primary transition-colors"
            disabled={mode === 'demo'}
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="antagonist" className="text-base md:text-lg font-semibold">Antagonist (Optional)</Label>
          <Input
            id="antagonist"
            value={formData.antagonist}
            onChange={(e) => updateFormData("antagonist", e.target.value)}
            placeholder="e.g., The Shadow Corporation"
            className="h-12 md:h-14 text-base md:text-lg px-4 md:px-6 border-2 focus:border-primary transition-colors"
            disabled={mode === 'demo'}
          />
        </div>
      </div>

      {/* Setting and Time Period on same line */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="setting" className="text-base md:text-lg font-semibold">Primary Setting</Label>
          <Input
            id="setting"
            value={formData.setting}
            onChange={(e) => updateFormData("setting", e.target.value)}
            placeholder="e.g., Neo-Tokyo 2087, Victorian London"
            className="h-12 md:h-14 text-base md:text-lg px-4 md:px-6 border-2 focus:border-primary transition-colors"
            disabled={mode === 'demo'}
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="timePeriod" className="text-base md:text-lg font-semibold">Time Period</Label>
          <Select 
            value={formData.timePeriod} 
            onValueChange={(value) => updateFormData("timePeriod", value)}
            disabled={mode === 'demo'}
          >
            <SelectTrigger className="h-12 md:h-14 text-base md:text-lg border-2 hover:border-primary/50 transition-colors">
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="prehistoric" className="text-base md:text-lg py-3">Prehistoric</SelectItem>
              <SelectItem value="ancient" className="text-base md:text-lg py-3">Ancient (Before 500 CE)</SelectItem>
              <SelectItem value="medieval" className="text-base md:text-lg py-3">Medieval (500-1500 CE)</SelectItem>
              <SelectItem value="renaissance" className="text-base md:text-lg py-3">Renaissance (1400-1600)</SelectItem>
              <SelectItem value="early-modern" className="text-base md:text-lg py-3">Early Modern (1600-1800)</SelectItem>
              <SelectItem value="industrial" className="text-base md:text-lg py-3">Industrial (1800-1900)</SelectItem>
              <SelectItem value="modern" className="text-base md:text-lg py-3">Modern (1900-2000)</SelectItem>
              <SelectItem value="contemporary" className="text-base md:text-lg py-3">Contemporary (2000-Present)</SelectItem>
              <SelectItem value="near-future" className="text-base md:text-lg py-3">Near Future (2025-2100)</SelectItem>
              <SelectItem value="far-future" className="text-base md:text-lg py-3">Far Future (Beyond 2100)</SelectItem>
              <SelectItem value="alternate-history" className="text-base md:text-lg py-3">Alternate History</SelectItem>
              <SelectItem value="timeless" className="text-base md:text-lg py-3">Timeless/Fantasy</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="p-6 md:p-8 bg-gradient-to-r from-primary/10 to-accent/10 border-2 border-primary/20 rounded-xl shadow-lg">
        <div className="flex items-start gap-4">
          <Users className="w-6 h-6 md:w-7 md:h-7 text-primary mt-0.5 animate-pulse" />
          <div>
            <h4 className="font-semibold text-primary text-lg md:text-xl">AI Character Development</h4>
            <p className="text-sm md:text-base text-muted-foreground mt-2 leading-relaxed">
              Our Character Developer agent will create detailed profiles with backstories, motivations, and character arcs.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Step 4: Themes & Elements
function StepThemesElements({ formData, updateFormData, setFormData, mode }: { 
  formData: FormData; 
  updateFormData: (field: string, value: string) => void;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  mode: WizardMode;
}) {


  return (
    <div className="space-y-8">
      {/* Themes Checkbox Grid */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-lg md:text-xl font-semibold">Core Themes</Label>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, themes: themeOptions.slice() }))}
              className="text-xs"
            >
              Select All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, themes: [] }))}
              className="text-xs"
            >
              Clear All
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 p-4 border-2 rounded-xl max-h-96 overflow-y-auto">
          {themeOptions.map((theme) => (
            <label
              key={theme}
              className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-2 rounded-lg transition-colors"
            >
              <input
                type="checkbox"
                checked={formData.themes.includes(theme)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setFormData(prev => ({ ...prev, themes: [...prev.themes, theme] }));
                  } else {
                    setFormData(prev => ({ ...prev, themes: prev.themes.filter(t => t !== theme) }));
                  }
                }}
                className="h-4 w-4 text-primary rounded border-2"
                disabled={mode === 'demo'}
              />
              <span className="text-sm md:text-base">{theme}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Content Elements Checkbox Grid */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-lg md:text-xl font-semibold">Content Elements</Label>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, contentElements: contentElements.slice() }))}
              className="text-xs"
            >
              Select All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, contentElements: [] }))}
              className="text-xs"
            >
              Clear All
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 p-4 border-2 rounded-xl max-h-64 overflow-y-auto">
          {contentElements.map((element) => (
            <label
              key={element}
              className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-2 rounded-lg transition-colors"
            >
              <input
                type="checkbox"
                checked={formData.contentElements.includes(element)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setFormData(prev => ({ ...prev, contentElements: [...prev.contentElements, element] }));
                  } else {
                    setFormData(prev => ({ ...prev, contentElements: prev.contentElements.filter(e => e !== element) }));
                  }
                }}
                className="h-4 w-4 text-primary rounded border-2"
                disabled={mode === 'demo'}
              />
              <span className="text-sm md:text-base">{element}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="p-6 md:p-8 bg-gradient-to-r from-success/10 to-success/5 border-2 border-success/20 rounded-xl shadow-lg">
        <div className="flex items-start gap-4">
          <Globe className="w-6 h-6 md:w-7 md:h-7 text-success mt-0.5 animate-pulse" />
          <div>
            <h4 className="font-semibold text-success text-lg md:text-xl">Theme & Content Integration</h4>
            <p className="text-sm md:text-base text-muted-foreground mt-2 leading-relaxed">
              AI will weave your selected themes and elements throughout the story, creating meaningful character arcs and plot developments.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Step 5: Review & Create
function StepReview({ formData, mode, onComplete, isGenerating }: { 
  formData: FormData; 
  mode: WizardMode;
  onComplete: () => void;
  isGenerating: boolean;
}) {
  const avgChapterLength = Math.round(parseInt(formData.wordCount) / parseInt(formData.chapters));
  
  return (
    <div className="space-y-8">
      <div className="text-center mb-6">
        <CheckCircle className="w-16 h-16 text-primary mx-auto mb-4" />
        <h3 className="text-2xl md:text-3xl font-bold mb-2">Ready to Create Your Story!</h3>
        <p className="text-muted-foreground text-lg">Review your project details below</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Story Foundation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><span className="text-muted-foreground">Title:</span> <span className="font-medium">{formData.title || 'Untitled'}</span></div>
            <div><span className="text-muted-foreground">Genre:</span> <span className="font-medium">{formData.genre}</span></div>
            <div><span className="text-muted-foreground">Audience:</span> <span className="font-medium">{formData.targetAudience}</span></div>
            <div><span className="text-muted-foreground">Tones:</span> <span className="font-medium">{formData.tones.slice(0, 3).join(', ')}{formData.tones.length > 3 && '...'}</span></div>
          </CardContent>
        </Card>

        <Card className="border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Technical Specs</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><span className="text-muted-foreground">Word Count:</span> <span className="font-medium">{parseInt(formData.wordCount).toLocaleString()}</span></div>
            <div><span className="text-muted-foreground">Chapters:</span> <span className="font-medium">{formData.chapters}</span></div>
            <div><span className="text-muted-foreground">Avg Chapter:</span> <span className="font-medium">{avgChapterLength.toLocaleString()} words</span></div>
            <div><span className="text-muted-foreground">Structure:</span> <span className="font-medium">{formData.structure}</span></div>
          </CardContent>
        </Card>

        <Card className="border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">World & Characters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><span className="text-muted-foreground">Protagonist:</span> <span className="font-medium">{formData.protagonist || 'To be developed'}</span></div>
            <div><span className="text-muted-foreground">Setting:</span> <span className="font-medium">{formData.setting || 'To be developed'}</span></div>
            <div><span className="text-muted-foreground">Time Period:</span> <span className="font-medium">{formData.timePeriod || 'Not specified'}</span></div>
          </CardContent>
        </Card>

        <Card className="border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Themes & Elements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><span className="text-muted-foreground">Themes:</span> <span className="font-medium">{formData.themes.length} selected</span></div>
            <div><span className="text-muted-foreground">Elements:</span> <span className="font-medium">{formData.contentElements.length} selected</span></div>
          </CardContent>
        </Card>
      </div>

      <div className="p-6 bg-gradient-to-r from-primary/10 to-primary/5 border-2 border-primary/20 rounded-xl">
        <div className="text-center">
          <Sparkles className="w-8 h-8 text-primary mx-auto mb-3 animate-pulse" />
          <h4 className="font-semibold text-primary text-xl mb-2">AI Generation Process</h4>
          <p className="text-sm md:text-base text-muted-foreground mb-4">
            Our AI agents will create your complete story structure, character profiles, and chapter outlines.
          </p>
          <div className="flex flex-wrap justify-center gap-3 text-xs">
            <Badge variant="secondary">Story Architecture</Badge>
            <Badge variant="secondary">Character Development</Badge>
            <Badge variant="secondary">World Building</Badge>
            <Badge variant="secondary">Chapter Planning</Badge>
          </div>
        </div>
      </div>

      {mode === 'demo' && (
        <div className="p-4 bg-info-light border border-blue-200 rounded-lg">
          <div className="flex items-start gap-3">
            <Sparkles className="w-5 h-5 text-info mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800">Demo Mode Active</h4>
              <p className="text-sm text-blue-700 mt-1">
                This demonstration shows the complete project creation flow. No payment or actual project creation will occur.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}


// Remove old StepPayment - now part of StepReview
function OldStepPayment({ formData, mode, onPaymentComplete, isGenerating }: { 
  formData: FormData; 
  mode: WizardMode;
  onPaymentComplete: () => void;
  isGenerating: boolean;
}) {
  if (mode === 'demo') {
    return (
      <div className="space-y-6">
        <div className="text-center mb-6">
          <CheckCircle className="w-16 h-16 text-primary mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Demo Project Ready!</h3>
          <p className="text-muted-foreground">This demo showcases the project creation process</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Project Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div><span className="text-muted-foreground">Title:</span> <span className="font-medium">{formData.title}</span></div>
              <div><span className="text-muted-foreground">Genre:</span> <span className="font-medium">{formData.genre}</span></div>
              <div><span className="text-muted-foreground">Tone:</span> <span className="font-medium">{formData.tone}</span></div>
              <div><span className="text-muted-foreground">Audience:</span> <span className="font-medium">{formData.targetAudience}</span></div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Technical Specs</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div><span className="text-muted-foreground">Word Count:</span> <span className="font-medium">{parseInt(formData.wordCount).toLocaleString()}</span></div>
              <div><span className="text-muted-foreground">Chapters:</span> <span className="font-medium">{formData.chapters}</span></div>
              <div><span className="text-muted-foreground">Structure:</span> <span className="font-medium">{formData.structure}</span></div>
              <div><span className="text-muted-foreground">Pacing:</span> <span className="font-medium">{formData.pacing}</span></div>
            </CardContent>
          </Card>
        </div>

        <div className="p-6 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg">
          <div className="text-center">
            <Sparkles className="w-8 h-8 text-primary mx-auto mb-3" />
            <h4 className="font-semibold text-primary mb-2">Demo Generation Process</h4>
            <p className="text-sm text-muted-foreground mb-4">
              In live mode, this would create your complete story structure, character profiles, and chapter outlines.
            </p>
            <div className="flex flex-wrap justify-center gap-3 text-xs">
              <Badge variant="secondary">Story Architecture</Badge>
              <Badge variant="secondary">Character Development</Badge>
              <Badge variant="secondary">World Building</Badge>
              <Badge variant="secondary">Chapter Planning</Badge>
            </div>
          </div>
        </div>

        <div className="p-4 bg-info-light border border-blue-200 rounded-lg">
          <div className="flex items-start gap-3">
            <Sparkles className="w-5 h-5 text-info mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800">Demo Mode Active</h4>
              <p className="text-sm text-blue-700 mt-1">
                This demonstration shows the complete project creation flow. No payment or actual project creation will occur.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Live mode payment step (placeholder - would integrate with actual payment system)
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <CheckCircle className="w-16 h-16 text-primary mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">Ready to Create Your Story!</h3>
        <p className="text-muted-foreground">Complete payment to unlock AI-powered writing assistance</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Project Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><span className="text-muted-foreground">Title:</span> <span className="font-medium">{formData.title}</span></div>
            <div><span className="text-muted-foreground">Genre:</span> <span className="font-medium">{formData.genre}</span></div>
            <div><span className="text-muted-foreground">Word Count:</span> <span className="font-medium">{parseInt(formData.wordCount).toLocaleString()}</span></div>
            <div><span className="text-muted-foreground">Chapters:</span> <span className="font-medium">{formData.chapters}</span></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">What's Included</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div>✓ AI Story Architecture</div>
            <div>✓ Character Development</div>
            <div>✓ Chapter Outlines</div>
            <div>✓ Writing Assistance</div>
          </CardContent>
        </Card>
      </div>

      {/* Payment form placeholder */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Payment Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-8 text-center border-2 border-dashed border-muted rounded-lg">
            <CreditCard className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Payment integration would be implemented here
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              (Stripe payment form component)
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="p-6 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg">
        <div className="text-center">
          <Sparkles className="w-8 h-8 text-primary mx-auto mb-3" />
          <h4 className="font-semibold text-primary mb-2">AI Generation Process</h4>
          <p className="text-sm text-muted-foreground mb-4">
            Our AI agents will create your complete story structure, character profiles, and chapter outlines.
          </p>
          <div className="flex flex-wrap justify-center gap-3 text-xs">
            <Badge variant="secondary">Story Architecture</Badge>
            <Badge variant="secondary">Character Development</Badge>
            <Badge variant="secondary">World Building</Badge>
            <Badge variant="secondary">Chapter Planning</Badge>
          </div>
        </div>
      </div>
    </div>
  );
}