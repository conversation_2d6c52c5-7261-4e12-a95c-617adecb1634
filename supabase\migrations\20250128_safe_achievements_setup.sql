-- Safe achievement system setup that handles any existing state

-- 1. Clean up any existing policies (using DO block to catch errors)
DO $$ 
BEGIN
    -- Drop policies if they exist (ignore errors if tables don't exist)
    BEGIN
        DROP POLICY IF EXISTS "Achievements are public" ON achievements;
    EXCEPTION WHEN undefined_table THEN NULL;
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Users can view their own achievements" ON user_achievements;
    EXCEPTION WHEN undefined_table THEN NULL;
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "System can insert user achievements" ON user_achievements;
    EXCEPTION WHEN undefined_table THEN NULL;
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Users can view their own progress" ON achievement_progress;
    EXCEPTION WHEN undefined_table THEN NULL;
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "System can manage achievement progress" ON achievement_progress;
    EXCEPTION WHEN undefined_table THEN NULL;
    END;
END $$;

-- 2. Drop functions if they exist
DROP FUNCTION IF EXISTS check_and_unlock_achievements(UUID) CASCADE;
DROP FUNCTION IF EXISTS track_achievement_progress(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>HA<PERSON>, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS check_achievements_on_update() CASCADE;

-- 3. Drop triggers if they exist
DO $$ 
BEGIN
    DROP TRIGGER IF EXISTS check_achievements_on_chapter_update ON chapters;
EXCEPTION WHEN undefined_table THEN NULL;
END $$;

DO $$ 
BEGIN
    DROP TRIGGER IF EXISTS check_achievements_on_project_create ON projects;
EXCEPTION WHEN undefined_table THEN NULL;
END $$;

DO $$ 
BEGIN
    DROP TRIGGER IF EXISTS check_achievements_on_ai_usage ON ai_usage_logs;
EXCEPTION WHEN undefined_table THEN NULL;
END $$;

-- 4. Drop tables if they exist
DROP TABLE IF EXISTS user_achievements CASCADE;
DROP TABLE IF EXISTS achievement_progress CASCADE;
DROP TABLE IF EXISTS achievements CASCADE;

-- 5. Create achievement system from scratch

-- Achievement definitions table
CREATE TABLE achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  points INTEGER DEFAULT 10,
  tier VARCHAR(20) CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')) DEFAULT 'bronze',
  category VARCHAR(50) CHECK (category IN ('writing', 'streak', 'community', 'exploration', 'mastery')),
  criteria JSONB NOT NULL,
  icon VARCHAR(50),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User achievements tracking
CREATE TABLE user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ DEFAULT NOW(),
  progress INTEGER DEFAULT 0,
  metadata JSONB,
  UNIQUE(user_id, achievement_id)
);

-- Achievement progress tracking
CREATE TABLE achievement_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_code VARCHAR(100) NOT NULL,
  progress_key VARCHAR(100) NOT NULL,
  progress_value INTEGER DEFAULT 0,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, achievement_code, progress_key)
);

-- Create indexes
CREATE INDEX idx_user_achievements_user ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_unlocked ON user_achievements(unlocked_at DESC);
CREATE INDEX idx_achievement_progress_user ON achievement_progress(user_id);

-- Enable RLS
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievement_progress ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies
CREATE POLICY "Achievements are public" ON achievements
  FOR SELECT USING (true);

CREATE POLICY "Users can view their own achievements" ON user_achievements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert user achievements" ON user_achievements
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view their own progress" ON achievement_progress
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage achievement progress" ON achievement_progress
  FOR ALL USING (true);

-- Insert initial achievements
INSERT INTO achievements (code, title, description, points, tier, category, criteria, icon) VALUES
  ('first_words', 'First Words', 'Write your first 100 words', 10, 'bronze', 'writing', '{"type": "word_count", "value": 100}', 'edit'),
  ('novice_writer', 'Novice Writer', 'Write 1,000 words', 25, 'bronze', 'writing', '{"type": "word_count", "value": 1000}', 'edit-2'),
  ('first_chapter', 'Chapter One', 'Complete your first chapter', 20, 'bronze', 'writing', '{"type": "chapters_completed", "value": 1}', 'bookmark'),
  ('first_project', 'First Project', 'Create your first project', 15, 'bronze', 'exploration', '{"type": "projects_created", "value": 1}', 'folder-plus');

-- Create the check function
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE (newly_unlocked UUID[]) AS $$
DECLARE
  v_newly_unlocked UUID[];
BEGIN
  -- For now, just return empty array
  -- This prevents errors while still allowing the API to work
  v_newly_unlocked := ARRAY[]::UUID[];
  RETURN QUERY SELECT v_newly_unlocked;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION check_and_unlock_achievements(UUID) TO authenticated;
GRANT SELECT ON achievements TO authenticated;
GRANT SELECT ON user_achievements TO authenticated;
GRANT SELECT, INSERT, UPDATE ON achievement_progress TO authenticated;

-- Verify installation
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'achievements') THEN
        RAISE NOTICE 'Success: achievements table created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_achievements') THEN
        RAISE NOTICE 'Success: user_achievements table created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'check_and_unlock_achievements') THEN
        RAISE NOTICE 'Success: check_and_unlock_achievements function created';
    END IF;
END $$;