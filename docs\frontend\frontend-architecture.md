# BookScribe Frontend Architecture Documentation

## Overview

BookScribe's frontend is built with Next.js 15 App Router, utilizing React Server Components, TypeScript, and a sophisticated theming system. The architecture emphasizes performance, accessibility, and a literary aesthetic that creates an immersive writing environment.

## Architecture Overview

### Technology Stack

- **Framework**: Next.js 15+ with App Router
- **UI Library**: Shadcn/ui components with custom styling
- **Styling**: Tailwind CSS with CSS custom properties
- **State Management**: Zustand for complex state, React Context for providers
- **Theming**: Custom multi-theme system with Writer's Sanctuary design
- **Type Safety**: TypeScript with strict mode
- **Forms**: React Hook Form with Zod validation
- **Animation**: Framer Motion and CSS transitions

### Directory Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (auth)/            # Authentication pages group
│   ├── (dashboard)/       # Dashboard pages group
│   ├── (marketing)/       # Marketing pages group
│   ├── (app)/            # Application pages
│   ├── api/              # API routes
│   ├── layout.tsx        # Root layout
│   └── globals.css       # Global styles
├── components/           # React components
│   ├── ui/              # Base UI components (Shadcn)
│   ├── editor/          # Editor components
│   ├── analytics/       # Analytics components
│   ├── auth/            # Authentication components
│   ├── layout/          # Layout components
│   └── ...              # Feature-specific components
├── hooks/               # Custom React hooks
├── contexts/            # React contexts
├── stores/              # Zustand stores
├── lib/                 # Utilities and services
│   ├── themes/          # Theme system
│   ├── utils/           # Helper functions
│   └── config/          # Configuration
└── types/               # TypeScript definitions
```

## Route Groups Architecture

### App Router Structure

BookScribe uses Next.js 15's route groups to organize pages:

```typescript
// Root Layout (src/app/layout.tsx)
export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
```

### Route Groups

#### 1. (auth) - Authentication Pages
- `/login` - Login page
- `/signup` - Sign up page
- `/forgot-password` - Password reset
- `/reset-password` - Password reset confirmation

#### 2. (dashboard) - Application Pages
- `/dashboard` - Main dashboard
- `/projects` - Projects list
- `/projects/[id]` - Project workspace
- `/analytics` - Analytics dashboard
- `/settings` - User settings
- `/billing` - Subscription management

#### 3. (marketing) - Public Pages
- `/` - Landing page
- `/pricing` - Pricing plans
- `/demo` - Interactive demo
- `/docs` - Documentation
- `/about` - About page

## State Management

### 1. Zustand Stores

BookScribe uses Zustand for complex application state:

#### Editor Store
```typescript
// src/stores/editor-store.ts
interface EditorState {
  // Content state
  content: string
  selectedText: string
  currentChapter: number
  
  // UI state
  showAiChat: boolean
  showStoryBible: boolean
  showChapterNavigator: boolean
  
  // Actions
  setContent: (content: string) => void
  toggleAiChat: () => void
  toggleStoryBible: () => void
}

export const useEditorStore = create<EditorState>((set) => ({
  // State implementation
}))
```

#### Project Store
```typescript
// src/stores/project-store.ts
interface ProjectState {
  currentProject: Project | null
  projects: Project[]
  isLoading: boolean
  
  // Actions
  setCurrentProject: (project: Project) => void
  updateProject: (id: string, updates: Partial<Project>) => void
  fetchProjects: () => Promise<void>
}
```

### 2. React Context Providers

Context is used for cross-cutting concerns:

#### Auth Context
```typescript
// src/contexts/auth-context.tsx
interface AuthContextType {
  user: User | null
  session: Session | null
  isLoading: boolean
  signIn: (credentials: SignInCredentials) => Promise<void>
  signOut: () => Promise<void>
}
```

#### Settings Context
```typescript
// src/contexts/settings-context.tsx
interface SettingsContextType {
  theme: string
  editorSettings: EditorSettings
  updateSettings: (settings: Partial<Settings>) => void
}
```

## Component Architecture

### Component Categories

#### 1. UI Components (Shadcn/ui)
Base components following Radix UI patterns:

```typescript
// src/components/ui/button.tsx
const buttonVariants = cva(
  "inline-flex items-center justify-center...",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground...",
        destructive: "bg-destructive text-destructive-foreground...",
        outline: "border border-input bg-background...",
        secondary: "bg-secondary text-secondary-foreground...",
        ghost: "hover:bg-accent hover:text-accent-foreground...",
        link: "text-primary underline-offset-4...",
        literary: "bg-gradient-to-r from-primary..." // Custom variant
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10"
      }
    }
  }
)
```

#### 2. Editor Components
Specialized components for the writing interface:

- `MonacoEditor` - Main editor with syntax highlighting
- `CollaborativeEditor` - Real-time collaborative editing
- `FormattingToolbar` - Text formatting controls
- `SelectionMenu` - Context menu for text selection
- `StoryBiblePanel` - Story reference sidebar
- `AIAssistantChat` - AI writing assistant interface

#### 3. Analytics Components
Data visualization and metrics:

- `AnalyticsPanel` - Main analytics dashboard
- `WritingProgressChart` - Progress visualization
- `QualityMetricsDisplay` - Content quality indicators
- `ProductivityStats` - Writing productivity metrics

#### 4. Layout Components
Application structure components:

- `Navbar` - Top navigation
- `Sidebar` - Dashboard sidebar
- `PageHeader` - Consistent page headers
- `Container` - Responsive content wrapper

### Component Patterns

#### 1. Compound Components
```typescript
// Example: Card compound component
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    Content here
  </CardContent>
  <CardFooter>
    Actions
  </CardFooter>
</Card>
```

#### 2. Render Props Pattern
```typescript
// Example: DataTable with render props
<DataTable
  data={projects}
  columns={columns}
  renderRow={(project) => (
    <ProjectRow key={project.id} project={project} />
  )}
/>
```

#### 3. Custom Hooks Pattern
```typescript
// Example: useProject hook
function ProjectComponent() {
  const { project, isLoading, error, updateProject } = useProject(id)
  
  if (isLoading) return <Skeleton />
  if (error) return <ErrorMessage />
  
  return <ProjectEditor project={project} onSave={updateProject} />
}
```

## Theme System

### Theme Architecture

BookScribe's theme system is built on CSS custom properties:

#### 1. Theme Structure
```css
/* src/app/globals.css */
:root {
  /* Writer's Sanctuary Light (Default) */
  --background: 45 50% 97%; /* Warm paper white */
  --foreground: 25 30% 10%; /* Dark charcoal text */
  --primary: 25 75% 45%; /* Rich amber-brown */
  
  /* Literary-specific colors */
  --literary-gold: 45 85% 55%;
  --literary-amber: 35 75% 50%;
  --literary-parchment: 45 40% 92%;
  --literary-ink: 25 40% 15%;
  
  /* Semantic status colors */
  --status-completed: 120 60% 40%;
  --status-in-progress: 210 70% 50%;
  --status-draft: 45 90% 50%;
}
```

#### 2. Available Themes
- **Writer's Sanctuary Light** - Warm paper tones, literary aesthetic
- **Evening Study Dark** - Rich dark browns, comfortable for night
- **Forest Manuscript Light** - Green-tinted, nature-inspired
- **Midnight Ink Dark** - Deep blue-black, high contrast

#### 3. Theme Provider
```typescript
// src/components/theme-provider.tsx
export function ThemeProvider({ children }) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="writers-sanctuary-light"
      enableSystem={false}
      storageKey="bookscribe-theme"
      themes={[
        'writers-sanctuary-light',
        'evening-study-dark',
        'forest-manuscript-light',
        'midnight-ink-dark'
      ]}
    >
      {children}
    </NextThemesProvider>
  )
}
```

## Form Handling

### React Hook Form Integration

```typescript
// Example: Project creation form
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { projectSchema } from '@/lib/validation/schemas'

function CreateProjectForm() {
  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      title: '',
      genre: '',
      targetWordCount: 80000
    }
  })
  
  const onSubmit = async (data: ProjectFormData) => {
    // Handle submission
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Project Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}
```

## Error Handling

### Error Boundaries

```typescript
// src/components/error/api-error-boundary.tsx
export class APIErrorBoundary extends React.Component {
  state = { hasError: false, error: null }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />
    }
    
    return this.props.children
  }
}
```

### Custom Error Components
- `EditorErrorFallback` - Editor-specific error handling
- `ServiceError` - Service unavailable errors
- `UnifiedErrorBoundary` - Global error boundary

## Performance Optimization

### 1. Code Splitting
```typescript
// Dynamic imports for heavy components
const MonacoEditor = dynamic(
  () => import('@/components/editor/monaco-editor'),
  { 
    loading: () => <EditorSkeleton />,
    ssr: false 
  }
)
```

### 2. React Server Components
```typescript
// Server component for data fetching
async function ProjectList() {
  const projects = await getProjects() // Server-side fetch
  
  return (
    <div>
      {projects.map(project => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  )
}
```

### 3. Optimistic Updates
```typescript
// Optimistic UI updates with Zustand
const updateProject = async (updates: Partial<Project>) => {
  // Optimistically update UI
  set(state => ({
    projects: state.projects.map(p => 
      p.id === projectId ? { ...p, ...updates } : p
    )
  }))
  
  try {
    await api.updateProject(projectId, updates)
  } catch (error) {
    // Revert on error
    set(state => ({ projects: originalProjects }))
  }
}
```

## Accessibility

### Built-in Features

1. **Semantic HTML**: Proper heading hierarchy and ARIA labels
2. **Keyboard Navigation**: Full keyboard support for all interactions
3. **Screen Reader Support**: Announcements and live regions
4. **Focus Management**: Visible focus indicators and focus trapping
5. **Color Contrast**: WCAG AA compliant color combinations

### Accessibility Components
```typescript
// src/components/ui/accessible-icon.tsx
export function AccessibleIcon({ label, icon: Icon, ...props }) {
  return (
    <>
      <Icon aria-hidden="true" {...props} />
      <span className="sr-only">{label}</span>
    </>
  )
}
```

## Responsive Design

### Breakpoint System
```typescript
// Tailwind breakpoints
const breakpoints = {
  sm: '640px',   // Mobile landscape
  md: '768px',   // Tablet
  lg: '1024px',  // Desktop
  xl: '1280px',  // Large desktop
  '2xl': '1536px' // Extra large
}
```

### Responsive Components
```typescript
// Responsive grid example
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  {items.map(item => (
    <Card key={item.id}>{item.content}</Card>
  ))}
</div>
```

## Best Practices

### 1. Component Guidelines
- Keep components under 200 lines
- Use TypeScript for all components
- Implement proper error boundaries
- Include loading and error states
- Follow accessibility guidelines

### 2. State Management
- Use Zustand for complex shared state
- Use React Context for cross-cutting concerns
- Keep component state local when possible
- Implement optimistic updates
- Handle loading and error states

### 3. Performance
- Use React Server Components where possible
- Implement code splitting for large components
- Optimize re-renders with React.memo
- Use virtual scrolling for long lists
- Implement proper caching strategies

### 4. Styling
- Use Tailwind utility classes
- Extend with CSS custom properties
- Maintain consistent spacing and sizing
- Follow the literary design theme
- Ensure responsive design

## Common Patterns

### 1. Data Fetching
```typescript
// Custom hook for data fetching
export function useProject(id: string) {
  const [project, setProject] = useState<Project | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  
  useEffect(() => {
    fetchProject(id)
      .then(setProject)
      .catch(setError)
      .finally(() => setIsLoading(false))
  }, [id])
  
  return { project, isLoading, error }
}
```

### 2. Form Validation
```typescript
// Zod schema for validation
const projectSchema = z.object({
  title: z.string().min(1, "Title is required").max(100),
  description: z.string().optional(),
  genre: z.enum(['fantasy', 'sci-fi', 'mystery', 'romance']),
  targetWordCount: z.number().min(1000).max(500000)
})
```

### 3. Toast Notifications
```typescript
// Toast usage
import { toast } from '@/hooks/use-toast'

toast({
  title: "Project created",
  description: "Your new project has been created successfully.",
  duration: 3000
})
```

## Future Enhancements

### Planned Features
1. **Progressive Web App**: Offline capability
2. **Gesture Support**: Touch gestures for mobile
3. **Voice Commands**: Voice-activated features
4. **Advanced Animations**: Page transitions
5. **Internationalization**: Multi-language support

### Architecture Improvements
1. **Micro-frontends**: Module federation
2. **GraphQL Integration**: Type-safe API layer
3. **State Machines**: XState for complex flows
4. **Design System**: Storybook documentation
5. **Performance Monitoring**: Real user metrics