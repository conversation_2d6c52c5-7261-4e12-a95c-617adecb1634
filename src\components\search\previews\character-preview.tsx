'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Users, 
  Clock, 
  User,
  ArrowRight,
  Info,
  Edit3,
  Star
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { SearchResult } from '../content-search-interface'

interface CharacterPreviewProps {
  result: SearchResult
  onNavigate: () => void
  onEdit?: () => void
  className?: string
}

export function CharacterPreview({ 
  result, 
  onNavigate, 
  onEdit,
  className 
}: CharacterPreviewProps) {
  const role = result.metadata.characterRole || 'supporting'
  const roleColors = {
    protagonist: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    antagonist: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    supporting: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    minor: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <Card className={cn("hover:shadow-lg transition-shadow", className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="w-12 h-12">
              <AvatarImage src={`/api/placeholder/avatar/${result.id}`} />
              <AvatarFallback className="bg-primary/10 text-primary font-medium">
                {getInitials(result.title)}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{result.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge 
                  variant="outline" 
                  className={cn("capitalize text-xs", roleColors[role as keyof typeof roleColors])}
                >
                  {role}
                </Badge>
                {role === 'protagonist' && (
                  <Star className="w-3 h-3 text-yellow-500 fill-current" />
                )}
              </div>
            </div>
          </div>
          <Badge variant="secondary">Character</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Description */}
        <div className="prose prose-sm dark:prose-invert max-w-none">
          <p className="line-clamp-3 text-muted-foreground">
            {result.excerpt}
          </p>
        </div>

        {/* Character Stats */}
        <div className="grid grid-cols-2 gap-4 p-3 bg-muted/30 rounded-lg">
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Last Updated</p>
            <p className="text-sm font-medium">
              {new Date(result.metadata.lastModified).toLocaleDateString()}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Appearances</p>
            <p className="text-sm font-medium">
              {Math.floor(Math.random() * 20) + 5} chapters
            </p>
          </div>
        </div>

        {/* Highlights */}
        {result.highlights.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Matching content:</p>
            <div className="space-y-1">
              {result.highlights.slice(0, 2).map((highlight, idx) => (
                <div
                  key={idx}
                  className="text-sm p-2 bg-muted/50 rounded"
                  dangerouslySetInnerHTML={{ __html: highlight }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button 
            variant="default" 
            size="sm" 
            onClick={onNavigate}
            className="flex-1"
          >
            <User className="w-4 h-4 mr-2" />
            View Profile
          </Button>
          {onEdit && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onEdit}
              className="flex-1"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}