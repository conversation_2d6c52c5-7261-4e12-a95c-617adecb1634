import { describe, it, expect, beforeEach } from '@jest/globals';
import { performance } from 'perf_hooks';
import { useCollaborationStore } from '@/stores/collaboration-store';
import { createClient } from '@/lib/supabase/client';

// Mock dependencies
jest.mock('@/lib/supabase/client');

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  connectionSetup: 1000, // 1 second to establish connection
  messageDelivery: 50, // 50ms for message delivery
  presenceUpdate: 100, // 100ms for presence updates
  conflictDetection: 10, // 10ms to detect conflicts
  conflictResolution: 100, // 100ms to resolve conflicts
  largeDocumentSync: 2000, // 2 seconds for large documents
};

describe('Collaboration Performance', () => {
  let mockSupabase: any;
  let mockChannel: any;
  let store: any;

  beforeEach(() => {
    // Reset store
    useCollaborationStore.setState({
      collaborators: new Map(),
      presence: new Map(),
      conflicts: [],
      isConnected: false,
      channel: null,
    });

    // Mock channel
    mockChannel = {
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn((cb) => {
        if (cb) cb({ status: 'SUBSCRIBED' });
        return mockChannel;
      }),
      unsubscribe: jest.fn().mockResolvedValue({ error: null }),
      track: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    mockSupabase = {
      channel: jest.fn().mockReturnValue(mockChannel),
      removeChannel: jest.fn().mockResolvedValue({ error: null }),
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'test-user', email: '<EMAIL>' } },
          error: null,
        }),
      },
    };

    (createClient as jest.Mock).mockReturnValue(mockSupabase);
    store = useCollaborationStore.getState();
  });

  describe('Connection Performance', () => {
    it('should establish connection quickly', async () => {
      const start = performance.now();
      await store.joinProject('project-123');
      const end = performance.now();

      const duration = end - start;
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.connectionSetup);
      expect(store.isConnected).toBe(true);

      console.log(`Connection setup: ${duration.toFixed(2)}ms`);
    });

    it('should handle multiple connection attempts efficiently', async () => {
      const attempts = 5;
      const durations = [];

      for (let i = 0; i < attempts; i++) {
        // Disconnect first
        await store.leaveProject();
        
        const start = performance.now();
        await store.joinProject(`project-${i}`);
        const end = performance.now();
        
        durations.push(end - start);
      }

      const avgDuration = durations.reduce((a, b) => a + b, 0) / attempts;
      expect(avgDuration).toBeLessThan(PERFORMANCE_THRESHOLDS.connectionSetup);

      console.log(`Average connection time (${attempts} attempts): ${avgDuration.toFixed(2)}ms`);
    });
  });

  describe('Message Delivery Performance', () => {
    it('should deliver cursor updates quickly', async () => {
      await store.joinProject('project-123');

      const positions = Array(100).fill(null).map((_, i) => ({
        line: Math.floor(Math.random() * 1000),
        ch: Math.floor(Math.random() * 100),
      }));

      const durations = [];

      for (const position of positions) {
        const start = performance.now();
        store.updateCursor(position);
        const end = performance.now();
        
        durations.push(end - start);
      }

      const avgDuration = durations.reduce((a, b) => a + b, 0) / positions.length;
      expect(avgDuration).toBeLessThan(PERFORMANCE_THRESHOLDS.messageDelivery);

      console.log(`Average cursor update time: ${avgDuration.toFixed(2)}ms`);
    });

    it('should handle edit operations efficiently', async () => {
      await store.joinProject('project-123');

      const edits = Array(50).fill(null).map((_, i) => ({
        id: `edit-${i}`,
        type: 'insert' as const,
        position: { line: i, ch: 0 },
        content: `Line ${i} content`,
        timestamp: Date.now(),
      }));

      const start = performance.now();
      
      for (const edit of edits) {
        store.sendEdit(edit);
      }

      const end = performance.now();
      const totalDuration = end - start;
      const perEdit = totalDuration / edits.length;

      expect(perEdit).toBeLessThan(PERFORMANCE_THRESHOLDS.messageDelivery);

      console.log(`Sending ${edits.length} edits: ${totalDuration.toFixed(2)}ms (${perEdit.toFixed(2)}ms per edit)`);
    });
  });

  describe('Presence Management Performance', () => {
    it('should update presence for many users efficiently', async () => {
      await store.joinProject('project-123');

      const userCounts = [10, 50, 100];

      for (const count of userCounts) {
        const presenceData = Array(count).fill(null).map((_, i) => ({
          [`user-${i}`]: {
            user_id: `user-${i}`,
            user_email: `user${i}@example.com`,
            cursor: { line: i, ch: i },
            color: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
          },
        })).reduce((acc, curr) => ({ ...acc, ...curr }), {});

        const start = performance.now();
        
        // Simulate presence sync
        mockChannel.on.mock.calls
          .find(call => call[0] === 'presence')?.[1]({
            event: 'sync',
            payload: presenceData,
          });

        const end = performance.now();
        const duration = end - start;

        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.presenceUpdate * Math.log(count));

        console.log(`Syncing ${count} users' presence: ${duration.toFixed(2)}ms`);
      }
    });

    it('should handle rapid presence updates', async () => {
      await store.joinProject('project-123');

      const updateCount = 1000;
      const start = performance.now();

      for (let i = 0; i < updateCount; i++) {
        const userId = `user-${i % 10}`; // Simulate 10 users
        store.presence.set(userId, {
          cursor: { line: i, ch: i },
          lastUpdate: Date.now(),
        });
      }

      const end = performance.now();
      const duration = end - start;
      const perUpdate = duration / updateCount;

      expect(perUpdate).toBeLessThan(0.1); // Less than 0.1ms per update

      console.log(`${updateCount} presence updates: ${duration.toFixed(2)}ms (${perUpdate.toFixed(3)}ms per update)`);
    });
  });

  describe('Conflict Detection Performance', () => {
    it('should detect conflicts quickly', async () => {
      await store.joinProject('project-123');

      const conflictScenarios = [
        { edits: 10, positions: 5 },
        { edits: 50, positions: 20 },
        { edits: 100, positions: 50 },
      ];

      for (const scenario of conflictScenarios) {
        store.conflicts = []; // Reset conflicts

        const edits = Array(scenario.edits).fill(null).map((_, i) => ({
          id: `edit-${i}`,
          type: 'insert' as const,
          position: { 
            line: Math.floor(i / 2), // Create conflicts
            ch: i % 2 * 10,
          },
          content: `Content ${i}`,
          timestamp: Date.now() + i,
        }));

        const start = performance.now();

        // Simulate receiving edits that might conflict
        for (const edit of edits) {
          // Check for conflicts with existing edits
          const hasConflict = edits.some(e => 
            e !== edit && 
            e.position.line === edit.position.line &&
            Math.abs(e.position.ch - edit.position.ch) < 5
          );

          if (hasConflict) {
            store.conflicts.push({
              id: `conflict-${edit.id}`,
              type: 'position',
              position: edit.position,
              localEdit: edit,
              remoteEdit: edits.find(e => e !== edit && e.position.line === edit.position.line)!,
            });
          }
        }

        const end = performance.now();
        const duration = end - start;
        const perEdit = duration / scenario.edits;

        expect(perEdit).toBeLessThan(PERFORMANCE_THRESHOLDS.conflictDetection);

        console.log(
          `Detecting conflicts in ${scenario.edits} edits: ${duration.toFixed(2)}ms ` +
          `(${store.conflicts.length} conflicts found)`
        );
      }
    });
  });

  describe('Conflict Resolution Performance', () => {
    it('should resolve conflicts efficiently', async () => {
      const conflictCounts = [10, 50, 100];

      for (const count of conflictCounts) {
        // Create conflicts
        const conflicts = Array(count).fill(null).map((_, i) => ({
          id: `conflict-${i}`,
          type: 'position' as const,
          position: { line: i, ch: 0 },
          localEdit: {
            id: `local-${i}`,
            type: 'insert' as const,
            content: 'Local content',
            position: { line: i, ch: 0 },
            timestamp: Date.now(),
          },
          remoteEdit: {
            id: `remote-${i}`,
            type: 'insert' as const,
            content: 'Remote content',
            position: { line: i, ch: 0 },
            timestamp: Date.now() - 100,
          },
        }));

        store.conflicts = conflicts;

        const start = performance.now();

        // Resolve all conflicts
        for (const conflict of conflicts) {
          store.resolveConflict(conflict.id, 'local', jest.fn());
        }

        const end = performance.now();
        const duration = end - start;
        const perConflict = duration / count;

        expect(perConflict).toBeLessThan(PERFORMANCE_THRESHOLDS.conflictResolution);

        console.log(
          `Resolving ${count} conflicts: ${duration.toFixed(2)}ms ` +
          `(${perConflict.toFixed(2)}ms per conflict)`
        );
      }
    });
  });

  describe('Large Document Synchronization', () => {
    it('should sync large documents within threshold', async () => {
      await store.joinProject('project-123');

      const documentSizes = [
        { lines: 1000, avgLineLength: 80 },
        { lines: 5000, avgLineLength: 80 },
        { lines: 10000, avgLineLength: 80 },
      ];

      for (const size of documentSizes) {
        const document = Array(size.lines).fill(null).map((_, i) => 
          'x'.repeat(size.avgLineLength)
        ).join('\n');

        const start = performance.now();

        // Simulate syncing document
        const chunkSize = 100; // Lines per chunk
        for (let i = 0; i < size.lines; i += chunkSize) {
          const chunk = document.split('\n').slice(i, i + chunkSize).join('\n');
          
          store.sendEdit({
            id: `sync-${i}`,
            type: 'insert',
            position: { line: i, ch: 0 },
            content: chunk,
            timestamp: Date.now(),
          });
        }

        const end = performance.now();
        const duration = end - start;
        const bytesPerSecond = (document.length / duration) * 1000;

        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.largeDocumentSync * (size.lines / 1000));

        console.log(
          `Syncing ${size.lines}-line document: ${duration.toFixed(2)}ms ` +
          `(${(bytesPerSecond / 1024 / 1024).toFixed(2)} MB/s)`
        );
      }
    });
  });

  describe('Scalability Tests', () => {
    it('should handle many concurrent collaborators', async () => {
      const collaboratorCounts = [5, 10, 20, 50];

      for (const count of collaboratorCounts) {
        // Reset store
        store.collaborators.clear();
        store.presence.clear();

        const start = performance.now();

        // Simulate many collaborators joining
        for (let i = 0; i < count; i++) {
          store.collaborators.set(`user-${i}`, {
            id: `user-${i}`,
            email: `user${i}@example.com`,
            color: '#' + Math.floor(Math.random() * 16777215).toString(16),
            isOnline: true,
          });

          store.presence.set(`user-${i}`, {
            cursor: { line: i, ch: 0 },
            lastUpdate: Date.now(),
          });
        }

        // Simulate activity from all collaborators
        for (let i = 0; i < count * 10; i++) {
          const userId = `user-${i % count}`;
          store.updateCursor({ 
            line: Math.floor(Math.random() * 1000), 
            ch: Math.floor(Math.random() * 100),
          });
        }

        const end = performance.now();
        const duration = end - start;

        expect(duration).toBeLessThan(1000 * Math.log(count)); // Logarithmic scaling

        console.log(`Handling ${count} concurrent collaborators: ${duration.toFixed(2)}ms`);
      }
    });
  });

  describe('Memory Efficiency', () => {
    it('should maintain reasonable memory usage with many operations', () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform many operations
      for (let i = 0; i < 10000; i++) {
        store.presence.set(`user-${i % 100}`, {
          cursor: { line: i, ch: i % 100 },
          lastUpdate: Date.now(),
        });

        if (i % 1000 === 0) {
          // Periodically clean up old presence data
          const now = Date.now();
          store.presence.forEach((data, userId) => {
            if (now - data.lastUpdate > 60000) { // 1 minute old
              store.presence.delete(userId);
            }
          });
        }
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB

      console.log(`Memory usage after 10k operations: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });
  });
});