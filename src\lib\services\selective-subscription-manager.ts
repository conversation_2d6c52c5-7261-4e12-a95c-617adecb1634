'use client'

import { createClient } from '@/lib/supabase'
import type { RealtimeChannel } from '@supabase/supabase-js'
import { logger } from './logger'

export interface SubscriptionConfig {
  id: string
  type: 'chapter' | 'project' | 'presence' | 'session' | 'collaboration'
  target: string // chapter id, project id, etc.
  filters?: Record<string, string>
  priority: 'high' | 'medium' | 'low'
  maxRetries?: number
  autoReconnect?: boolean
}

export interface SubscriptionMetrics {
  subscriptionId: string
  messageCount: number
  lastActivity: Date
  latency: number
  errors: number
  retries: number
}

/**
 * Manages selective real-time subscriptions with optimization and resource management
 */
export class SelectiveSubscriptionManager {
  private static instance: SelectiveSubscriptionManager
  private subscriptions = new Map<string, RealtimeChannel>()
  private metrics = new Map<string, SubscriptionMetrics>()
  private connectionQueue: SubscriptionConfig[] = []
  private maxConcurrentConnections = 10 // Supabase limit
  private isProcessingQueue = false
  
  private constructor() {}

  static getInstance(): SelectiveSubscriptionManager {
    if (!SelectiveSubscriptionManager.instance) {
      SelectiveSubscriptionManager.instance = new SelectiveSubscriptionManager()
    }
    return SelectiveSubscriptionManager.instance
  }

  /**
   * Subscribe to a specific resource with optimization
   */
  async subscribe(config: SubscriptionConfig): Promise<string> {
    const existingSubscription = this.subscriptions.get(config.id)
    if (existingSubscription) {
      logger.debug('Subscription already exists', { subscriptionId: config.id })
      return config.id
    }

    // Add to queue if at connection limit
    if (this.subscriptions.size >= this.maxConcurrentConnections) {
      this.connectionQueue.push(config)
      logger.info('Subscription queued due to connection limit', { 
        subscriptionId: config.id,
        queueSize: this.connectionQueue.length 
      })
      return config.id
    }

    return this.createSubscription(config)
  }

  /**
   * Create and manage a subscription
   */
  private async createSubscription(config: SubscriptionConfig): Promise<string> {
    const supabase = createClient()
    const channelName = this.generateChannelName(config)
    const channel = supabase.channel(channelName)

    // Initialize metrics
    this.metrics.set(config.id, {
      subscriptionId: config.id,
      messageCount: 0,
      lastActivity: new Date(),
      latency: 0,
      errors: 0,
      retries: 0
    })

    try {
      // Configure subscription based on type
      switch (config.type) {
        case 'chapter':
          this.configureChapterSubscription(channel, config)
          break
        case 'project':
          this.configureProjectSubscription(channel, config)
          break
        case 'presence':
          this.configurePresenceSubscription(channel, config)
          break
        case 'session':
          this.configureSessionSubscription(channel, config)
          break
        case 'collaboration':
          this.configureCollaborationSubscription(channel, config)
          break
        default:
          throw new Error(`Unknown subscription type: ${config.type}`)
      }

      // Add connection monitoring
      this.addConnectionMonitoring(channel, config)

      // Subscribe with retry logic
      await this.subscribeWithRetry(channel, config)
      
      this.subscriptions.set(config.id, channel)
      logger.info('Subscription created successfully', { 
        subscriptionId: config.id,
        type: config.type,
        target: config.target 
      })

      return config.id
    } catch (error) {
      logger.error('Failed to create subscription', error)
      this.updateMetrics(config.id, { errors: 1 })
      throw error
    }
  }

  /**
   * Configure chapter-specific subscription
   */
  private configureChapterSubscription(channel: RealtimeChannel, config: SubscriptionConfig) {
    channel.on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'chapters',
        filter: `id=eq.${config.target}`,
        ...config.filters
      },
      (payload) => {
        this.updateMetrics(config.id, { messageCount: 1, lastActivity: new Date() })
        this.emit(`chapter:${config.target}:update`, payload)
      }
    )
  }

  /**
   * Configure project-wide subscription
   */
  private configureProjectSubscription(channel: RealtimeChannel, config: SubscriptionConfig) {
    // Subscribe to multiple project-related tables
    const tables = ['chapters', 'characters', 'locations', 'references', 'story_bible']
    
    tables.forEach(table => {
      channel.on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter: `project_id=eq.${config.target}`,
          ...config.filters
        },
        (payload) => {
          this.updateMetrics(config.id, { messageCount: 1, lastActivity: new Date() })
          this.emit(`project:${config.target}:${table}:${payload.eventType}`, payload)
        }
      )
    })
  }

  /**
   * Configure presence subscription
   */
  private configurePresenceSubscription(channel: RealtimeChannel, config: SubscriptionConfig) {
    const events = ['sync', 'join', 'leave']
    
    events.forEach(event => {
      channel.on('presence', { event }, (payload) => {
        this.updateMetrics(config.id, { messageCount: 1, lastActivity: new Date() })
        this.emit(`presence:${config.target}:${event}`, payload)
      })
    })
  }

  /**
   * Configure writing session subscription
   */
  private configureSessionSubscription(channel: RealtimeChannel, config: SubscriptionConfig) {
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'writing_sessions',
        filter: `project_id=eq.${config.target}`,
        ...config.filters
      },
      (payload) => {
        this.updateMetrics(config.id, { messageCount: 1, lastActivity: new Date() })
        this.emit(`session:${config.target}:${payload.eventType}`, payload)
      }
    )
  }

  /**
   * Configure collaboration subscription
   */
  private configureCollaborationSubscription(channel: RealtimeChannel, config: SubscriptionConfig) {
    // Subscribe to collaboration events including conflicts, comments, etc.
    const tables = ['project_collaborators', 'chapter_comments', 'version_history']
    
    tables.forEach(table => {
      channel.on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter: `project_id=eq.${config.target}`,
          ...config.filters
        },
        (payload) => {
          this.updateMetrics(config.id, { messageCount: 1, lastActivity: new Date() })
          this.emit(`collaboration:${config.target}:${table}:${payload.eventType}`, payload)
        }
      )
    })
  }

  /**
   * Add connection monitoring and health checks
   */
  private addConnectionMonitoring(channel: RealtimeChannel, config: SubscriptionConfig) {
    // Monitor connection status
    channel.on('system', { event: 'disconnect' }, () => {
      logger.warn('Subscription disconnected', { subscriptionId: config.id })
      this.updateMetrics(config.id, { errors: 1 })
      
      // Auto-reconnect if configured
      if (config.autoReconnect !== false) {
        this.handleReconnection(config)
      }
    })

    channel.on('system', { event: 'error' }, (error) => {
      logger.error('Subscription error', error)
      this.updateMetrics(config.id, { errors: 1 })
    })
  }

  /**
   * Subscribe with exponential backoff retry
   */
  private async subscribeWithRetry(channel: RealtimeChannel, config: SubscriptionConfig) {
    const maxRetries = config.maxRetries || 3
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        const status = await new Promise<string>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Subscription timeout'))
          }, 10000) // 10 second timeout

          channel.subscribe((status) => {
            clearTimeout(timeout)
            if (status === 'SUBSCRIBED') {
              resolve(status)
            } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
              reject(new Error(`Subscription failed with status: ${status}`))
            }
          })
        })

        if (status === 'SUBSCRIBED') {
          return
        }
      } catch (error) {
        attempt++
        this.updateMetrics(config.id, { retries: 1 })
        
        if (attempt >= maxRetries) {
          throw error
        }
        
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000
        await new Promise(resolve => setTimeout(resolve, delay))
        
        logger.warn('Retrying subscription', { 
          subscriptionId: config.id, 
          attempt, 
          maxRetries, 
          delay 
        })
      }
    }
  }

  /**
   * Handle reconnection logic
   */
  private async handleReconnection(config: SubscriptionConfig) {
    // Remove old subscription
    await this.unsubscribe(config.id)
    
    // Add back to queue with delay
    setTimeout(() => {
      this.connectionQueue.unshift(config) // High priority for reconnections
      this.processQueue()
    }, 2000)
  }

  /**
   * Unsubscribe from a resource
   */
  async unsubscribe(subscriptionId: string): Promise<void> {
    const channel = this.subscriptions.get(subscriptionId)
    if (!channel) {
      logger.warn('Subscription not found for unsubscribe', { subscriptionId })
      return
    }

    try {
      await channel.unsubscribe()
      this.subscriptions.delete(subscriptionId)
      this.metrics.delete(subscriptionId)
      
      logger.info('Subscription removed', { subscriptionId })
      
      // Process queue if space available
      this.processQueue()
    } catch (error) {
      logger.error('Failed to unsubscribe', error)
    }
  }

  /**
   * Process queued subscriptions
   */
  private async processQueue() {
    if (this.isProcessingQueue || this.connectionQueue.length === 0) return
    if (this.subscriptions.size >= this.maxConcurrentConnections) return

    this.isProcessingQueue = true

    try {
      // Sort queue by priority
      this.connectionQueue.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      })

      while (
        this.connectionQueue.length > 0 && 
        this.subscriptions.size < this.maxConcurrentConnections
      ) {
        const config = this.connectionQueue.shift()!
        await this.createSubscription(config)
      }
    } finally {
      this.isProcessingQueue = false
    }
  }

  /**
   * Get subscription metrics
   */
  getMetrics(): SubscriptionMetrics[] {
    return Array.from(this.metrics.values())
  }

  /**
   * Get active subscriptions
   */
  getActiveSubscriptions(): string[] {
    return Array.from(this.subscriptions.keys())
  }

  /**
   * Update subscription priority
   */
  updatePriority(subscriptionId: string, priority: 'high' | 'medium' | 'low') {
    // Find in queue and update priority
    const queueItem = this.connectionQueue.find(item => item.id === subscriptionId)
    if (queueItem) {
      queueItem.priority = priority
      this.processQueue() // Re-process with new priority
    }
  }

  /**
   * Clean up stale subscriptions
   */
  cleanup() {
    const now = new Date()
    const staleThreshold = 30 * 60 * 1000 // 30 minutes

    for (const [subscriptionId, metrics] of this.metrics.entries()) {
      const timeSinceActivity = now.getTime() - metrics.lastActivity.getTime()
      
      if (timeSinceActivity > staleThreshold) {
        logger.info('Cleaning up stale subscription', { 
          subscriptionId, 
          timeSinceActivity: `${Math.round(timeSinceActivity / 60000)}m` 
        })
        this.unsubscribe(subscriptionId)
      }
    }
  }

  /**
   * Set maximum concurrent connections
   */
  setMaxConnections(max: number) {
    this.maxConcurrentConnections = Math.max(1, Math.min(max, 20)) // Reasonable bounds
    logger.info('Updated max concurrent connections', { maxConnections: this.maxConcurrentConnections })
  }

  // Event emitter functionality
  private listeners = new Map<string, Function[]>()

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(callback)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          logger.error('Error in event listener', error)
        }
      })
    }
  }

  /**
   * Generate optimized channel name
   */
  private generateChannelName(config: SubscriptionConfig): string {
    return `${config.type}_${config.target}_${config.id.slice(-8)}`
  }

  /**
   * Update metrics atomically
   */
  private updateMetrics(subscriptionId: string, updates: Partial<SubscriptionMetrics>) {
    const existing = this.metrics.get(subscriptionId)
    if (existing) {
      this.metrics.set(subscriptionId, {
        ...existing,
        ...updates,
        messageCount: existing.messageCount + (updates.messageCount || 0),
        errors: existing.errors + (updates.errors || 0),
        retries: existing.retries + (updates.retries || 0),
        lastActivity: updates.lastActivity || existing.lastActivity
      })
    }
  }
}

// Export singleton instance
export const subscriptionManager = SelectiveSubscriptionManager.getInstance()