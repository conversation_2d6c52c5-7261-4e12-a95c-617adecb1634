'use client'

import { useEffect, useState, useCallback, memo } from 'react'
import { LazyMonacoEditor } from './lazy-monaco-editor'
import { CollaborativeEditorWrapper } from './collaborative-editor-wrapper'
import { EditorErrorBoundary, CollaborationErrorBoundary } from '@/components/error-boundary'
import { Database } from '@/lib/db/types'
import { useWordCountTracker } from '@/hooks/use-achievement-tracker'
import { logger } from '@/lib/services/logger'
import type { CollaborationUser } from '@/types/collaboration'

type Chapter = Database['public']['Tables']['chapters']['Row']

interface WritePageEditorProps {
  projectId: string
  chapterId: string | null
  content: string
  collaborationEnabled: boolean
  collaborationUsers: CollaborationUser[]
  currentChapterData: Chapter | null
  onChange: (value: string) => void
  onSelectionChange?: (selection: string) => void
  userId?: string
}

export const WritePageEditor = memo(function WritePageEditor({
  projectId,
  chapterId,
  content,
  collaborationEnabled,
  collaborationUsers,
  currentChapterData,
  onChange,
  onSelectionChange,
  userId
}: WritePageEditorProps) {
  const [editorReady, setEditorReady] = useState(false)
  const wordCountTracker = useWordCountTracker()

  // Track word count changes
  useEffect(() => {
    if (currentChapterData && userId) {
      const wordCount = content.split(/\s+/).filter(word => word.length > 0).length
      wordCountTracker.trackWordCount(wordCount)
    }
  }, [content, currentChapterData, userId])

  const handleEditorMount = useCallback(() => {
    setEditorReady(true)
    logger.info('Editor mounted successfully')
  }, [])

  const handleContentChange = useCallback((value: string | undefined) => {
    if (value !== undefined) {
      onChange(value)
    }
  }, [onChange])

  const handleSelectionChange = useCallback((selection: unknown) => {
    if (onSelectionChange && selection) {
      onSelectionChange(selection)
    }
  }, [onSelectionChange])

  if (collaborationEnabled && projectId && chapterId) {
    return (
      <CollaborationErrorBoundary>
        <CollaborativeEditorWrapper
          projectId={projectId}
          chapterId={chapterId}
          documentId={chapterId}
          value={content}
          onChange={handleContentChange}
          onMount={handleEditorMount}
          height="100%"
          theme="vs-dark"
          options={{
            fontSize: 14,
            lineHeight: 24,
            padding: { top: 20, bottom: 20 },
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            automaticLayout: true
          }}
          enableCollaboration={true}
          showCollaborators={true}
        />
      </CollaborationErrorBoundary>
    )
  }

  return (
    <EditorErrorBoundary>
      <LazyMonacoEditor
        value={content}
        onChange={handleContentChange}
        onMount={handleEditorMount}
        onSelectionChange={handleSelectionChange}
        height="100%"
        theme="vs-dark"
        language="markdown"
        options={{
          fontSize: 14,
          lineHeight: 24,
          padding: { top: 20, bottom: 20 },
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          wordWrap: 'on',
          automaticLayout: true
        }}
      />
    </EditorErrorBoundary>
  )
})