# BookScribe Sample Projects System

## Overview

The Sample Projects System provides new users with pre-built example projects that demonstrate BookScribe's features and capabilities. These templates serve as learning tools, inspiration sources, and quick-start options for different genres and project types.

## Architecture

### Database Schema

#### Sample Projects Table
Stores template project definitions:

```sql
sample_projects:
  - id: UUID (Primary Key)
  - title: VARCHAR(255) - Project title
  - description: TEXT - Project description
  - genre: VARCHAR(50) - fantasy, mystery, romance, sci-fi, etc.
  - project_type: VARCHAR(50) - novel, series, short_story, anthology
  - difficulty_level: VARCHAR(20) - beginner, intermediate, advanced
  - estimated_length: INTEGER - Word count estimate
  - preview_content: TEXT - Sample excerpt
  - features_demonstrated: TEXT[] - Features shown
  - tags: TEXT[] - Searchable tags
  - thumbnail_url: TEXT - Preview image
  - template_data: JSONB - Complete project structure
  - popularity_score: INTEGER - Usage count
  - rating: DECIMAL(3,2) - User rating (0-5)
  - is_featured: BOOLEAN - Featured status
  - is_active: BOOLEAN - Available for use
  - created_at: TIMESTAMPTZ
  - updated_at: TIMESTAMPTZ
```

#### Sample Project Usage Table
Tracks template usage:

```sql
sample_project_usage:
  - id: UUID (Primary Key)
  - sample_project_id: UUID - References sample_projects
  - user_id: UUID - References auth.users
  - created_project_id: UUID - References projects
  - customization_level: VARCHAR(20) - as_is, modified, heavily_modified
  - feedback: TEXT - User feedback
  - rating: INTEGER - 1-5 rating
  - created_at: TIMESTAMPTZ
```

## Sample Project Types

### 1. Genre Templates
Pre-built projects for specific genres:

```typescript
interface GenreTemplate {
  genre: 'fantasy' | 'mystery' | 'romance' | 'sci-fi' | 'thriller' | 'literary';
  template_data: {
    story_structure: StoryStructure;
    character_archetypes: CharacterArchetype[];
    world_building?: WorldElements;
    plot_template: PlotStructure;
    chapter_outlines: ChapterOutline[];
    style_guide: WritingStyle;
  };
  sample_content: {
    opening_chapter: string;
    character_profiles: CharacterProfile[];
    world_description?: string;
    key_scenes: SceneExample[];
  };
}
```

### 2. Structure Templates
Different narrative structures:

```typescript
interface StructureTemplate {
  structure_type: 'three_act' | 'heros_journey' | 'save_the_cat' | 'freytag' | 'kishōtenketsu';
  template_data: {
    act_breakdown: ActStructure[];
    plot_points: PlotPoint[];
    pacing_guide: PacingGuide;
    conflict_escalation: ConflictMap;
  };
  educational_content: {
    structure_explanation: string;
    best_practices: string[];
    common_pitfalls: string[];
  };
}
```

### 3. Series Templates
Multi-book project templates:

```typescript
interface SeriesTemplate {
  series_length: number;
  series_type: 'sequential' | 'episodic' | 'anthology';
  template_data: {
    series_arc: SeriesArc;
    book_outlines: BookOutline[];
    character_evolution: CharacterProgression[];
    world_expansion: WorldGrowth;
    recurring_elements: RecurringElement[];
  };
  continuity_tools: {
    series_bible: SeriesBible;
    timeline_template: TimelineStructure;
    character_tracker: CharacterTracker;
  };
}
```

### 4. Learning Templates
Educational projects for new users:

```typescript
interface LearningTemplate {
  learning_focus: 'ai_features' | 'character_development' | 'world_building' | 'plotting';
  template_data: {
    tutorial_steps: TutorialStep[];
    exercise_prompts: WritingExercise[];
    feature_demonstrations: FeatureDemo[];
    progress_milestones: Milestone[];
  };
  interactive_elements: {
    guided_tours: GuidedTour[];
    practice_scenarios: Scenario[];
    feedback_points: FeedbackTrigger[];
  };
}
```

## API Endpoints

### Sample Project Discovery

#### GET /api/sample-projects
List available sample projects:

```typescript
// Request
GET /api/sample-projects?genre=fantasy&difficulty=beginner

// Response
{
  projects: [
    {
      id: "uuid",
      title: "The Dragon's Quest",
      description: "Classic fantasy adventure template",
      genre: "fantasy",
      difficulty_level: "beginner",
      estimated_length: 80000,
      features_demonstrated: [
        "AI story generation",
        "Character development",
        "World building"
      ],
      preview_content: "In a land where dragons...",
      popularity_score: 1250,
      rating: 4.5,
      is_featured: true
    }
  ],
  categories: {
    by_genre: {
      fantasy: 5,
      mystery: 3,
      romance: 4
    },
    by_difficulty: {
      beginner: 8,
      intermediate: 4,
      advanced: 2
    }
  }
}
```

#### GET /api/sample-projects/{id}
Get detailed sample project:

```typescript
// Response
{
  project: {
    id: "uuid",
    title: "The Dragon's Quest",
    full_description: "Complete fantasy adventure...",
    template_data: {
      story_structure: {
        genre: "fantasy",
        themes: ["heroism", "friendship", "sacrifice"],
        tone: "adventurous",
        target_audience: "young_adult"
      },
      chapters: [
        {
          number: 1,
          title: "The Call to Adventure",
          outline: "Hero receives mysterious message...",
          key_scenes: ["tavern meeting", "dragon sighting"],
          word_count_target: 3000
        }
      ],
      characters: [
        {
          name: "Arin Stormblade",
          role: "protagonist",
          profile: {
            age: 19,
            background: "Farmhand with hidden destiny",
            personality: ["brave", "impulsive", "loyal"],
            arc: "Zero to hero journey"
          }
        }
      ]
    }
  },
  usage_stats: {
    times_used: 1250,
    avg_rating: 4.5,
    completion_rate: 0.72
  }
}
```

### Sample Project Usage

#### POST /api/sample-projects/{id}/use
Create project from template:

```typescript
// Request
{
  customization: {
    title: "My Dragon Adventure",
    protagonist_name: "Elena",
    setting_adjustments: {
      world_name: "Mythoria"
    },
    keep_ai_suggestions: true
  }
}

// Response
{
  project: {
    id: "new_project_uuid",
    title: "My Dragon Adventure",
    based_on_template: "uuid",
    customization_applied: true
  },
  setup_tasks: [
    {
      task: "ai_generation",
      status: "pending",
      description: "Generating customized content"
    }
  ]
}
```

#### POST /api/sample-projects/{id}/feedback
Submit template feedback:

```typescript
// Request
{
  rating: 5,
  feedback: "Great starting point for my fantasy novel!",
  customization_level: "modified",
  completion_status: "completed_project"
}
```

## Template Features

### 1. Interactive Setup Wizard
Guide users through customization:

```typescript
interface SetupWizard {
  steps: [
    {
      step: 'genre_confirmation',
      prompt: 'Is fantasy the right genre for your story?',
      options: ['Yes', 'Show me others', 'Mix genres']
    },
    {
      step: 'protagonist_customization',
      prompt: 'Customize your main character',
      fields: {
        name: string;
        gender: string;
        age: number;
        background: string;
      }
    },
    {
      step: 'world_adjustments',
      prompt: 'Adjust the world settings',
      options: {
        magic_level: 'low' | 'medium' | 'high';
        technology: 'medieval' | 'renaissance' | 'steampunk';
        scope: 'local' | 'kingdom' | 'continental' | 'global';
      }
    },
    {
      step: 'ai_preferences',
      prompt: 'How much AI assistance do you want?',
      options: {
        generation_level: 'minimal' | 'moderate' | 'extensive';
        suggestion_frequency: 'rare' | 'occasional' | 'frequent';
      }
    }
  ];
}
```

### 2. Learning Mode
Educational features for templates:

```typescript
interface LearningMode {
  tooltips: {
    [element: string]: {
      explanation: string;
      best_practice: string;
      example: string;
    };
  };
  
  progress_tracking: {
    features_explored: string[];
    exercises_completed: string[];
    milestones_reached: string[];
  };
  
  guided_exercises: [
    {
      exercise: 'character_voice',
      instruction: 'Write a paragraph in your protagonist\'s voice',
      ai_feedback: boolean;
      success_criteria: string[];
    }
  ];
}
```

### 3. Customization Engine
Deep customization options:

```typescript
class TemplateCustomizer {
  async customize(
    template: SampleProject,
    preferences: UserPreferences
  ): Promise<CustomizedProject> {
    // Apply user preferences
    let customized = this.applyBasicCustomization(template, preferences);
    
    // AI-powered adjustments
    if (preferences.use_ai_customization) {
      customized = await this.aiCustomize(customized, preferences);
    }
    
    // Generate unique content
    if (preferences.generate_unique_content) {
      customized = await this.generateUniqueElements(customized);
    }
    
    return customized;
  }
  
  private async aiCustomize(project: Project, prefs: UserPreferences) {
    // Use AI to adjust template based on preferences
    const adjusted = await ai.adjustTemplate({
      template: project,
      user_style: prefs.writing_style,
      tone_preferences: prefs.tone,
      complexity_level: prefs.complexity
    });
    
    return adjusted;
  }
}
```

## UI Components

### Template Gallery
```tsx
<TemplateGallery
  filters={{
    genre: selectedGenre,
    difficulty: difficultyLevel,
    features: requiredFeatures
  }}
  viewMode="cards" | "list"
  sortBy="popularity" | "rating" | "newest"
  onTemplateSelect={handleSelect}
/>
```

### Template Preview
```tsx
<TemplatePreview
  template={selectedTemplate}
  showFullPreview={true}
  showFeatureList={true}
  showUsageStats={true}
  onUseTemplate={handleUse}
  onCustomize={handleCustomize}
/>
```

### Setup Wizard
```tsx
<TemplateSetupWizard
  template={selectedTemplate}
  onComplete={handleSetupComplete}
  allowSkip={true}
  showProgress={true}
  aiAssistanceLevel="moderate"
/>
```

### Learning Dashboard
```tsx
<LearningDashboard
  template={learningTemplate}
  progress={userProgress}
  showExercises={true}
  showTips={true}
  onExerciseComplete={handleExerciseComplete}
/>
```

## Template Management

### Template Creation
For admin/content creators:

```typescript
interface TemplateBuilder {
  metadata: {
    title: string;
    description: string;
    genre: string;
    difficulty: string;
    features: string[];
  };
  
  content: {
    story_structure: StoryStructure;
    chapters: ChapterTemplate[];
    characters: CharacterTemplate[];
    world_elements?: WorldTemplate;
  };
  
  educational: {
    learning_objectives: string[];
    exercises: Exercise[];
    tips: Tip[];
  };
  
  validation: {
    completeness_check: () => boolean;
    quality_review: () => QualityScore;
    test_generation: () => Promise<TestResult>;
  };
}
```

### Quality Assurance
```typescript
class TemplateQualityChecker {
  async validateTemplate(template: SampleProject): Promise<ValidationResult> {
    const checks = await Promise.all([
      this.checkCompleteness(template),
      this.checkConsistency(template),
      this.checkEducationalValue(template),
      this.testGeneration(template)
    ]);
    
    return {
      passed: checks.every(c => c.passed),
      issues: checks.flatMap(c => c.issues),
      score: this.calculateQualityScore(checks)
    };
  }
}
```

## Analytics & Improvement

### Usage Analytics
```typescript
interface TemplateAnalytics {
  usage_count: number;
  completion_rate: number;
  customization_patterns: {
    most_changed_elements: string[];
    common_adjustments: Adjustment[];
  };
  user_feedback: {
    average_rating: number;
    common_praise: string[];
    common_complaints: string[];
  };
  feature_adoption: {
    [feature: string]: number;
  };
}
```

### Template Optimization
```typescript
class TemplateOptimizer {
  async optimizeTemplate(
    template: SampleProject,
    analytics: TemplateAnalytics
  ): Promise<OptimizationSuggestions> {
    return {
      content_adjustments: this.suggestContentChanges(analytics),
      feature_additions: this.suggestNewFeatures(analytics),
      difficulty_tuning: this.adjustDifficulty(analytics),
      customization_improvements: this.improveCustomization(analytics)
    };
  }
}
```

## Security & Licensing

### Content Protection
- Templates are read-only
- No user can modify original templates
- Customizations create new projects
- Original attribution maintained

### Usage Rights
```typescript
interface TemplateRights {
  license: 'free' | 'premium' | 'exclusive';
  attribution_required: boolean;
  commercial_use_allowed: boolean;
  modification_allowed: boolean;
  redistribution_allowed: boolean;
}
```

## Future Enhancements

1. **Community Templates**
   - User-submitted templates
   - Template marketplace
   - Revenue sharing
   - Quality curation

2. **AI Template Generation**
   - Generate templates from prompts
   - Personalized recommendations
   - Style matching
   - Adaptive difficulty

3. **Interactive Tutorials**
   - Video walkthroughs
   - Live coaching
   - Progress achievements
   - Certification system

4. **Template Chains**
   - Progressive difficulty
   - Skill building paths
   - Genre exploration
   - Writing challenges

## Related Systems
- Project Creation System
- AI Agent System (customization)
- Learning/Tutorial System
- Analytics System (usage tracking)