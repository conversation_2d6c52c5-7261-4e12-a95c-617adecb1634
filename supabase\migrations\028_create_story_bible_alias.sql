-- Create a view to handle the story_bible vs story_bibles naming inconsistency
-- This allows code using 'story_bible' to work without modification

CREATE OR REPLACE VIEW public.story_bible AS
SELECT * FROM public.story_bibles;

-- Create INSTEAD OF triggers to make the view updatable
CREATE OR REPLACE FUNCTION story_bible_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO story_bibles 
  SELECT NEW.*;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER story_bible_insert_trigger
  INSTEAD OF INSERT ON story_bible
  FOR EACH ROW
  EXECUTE FUNCTION story_bible_insert();

CREATE OR REPLACE FUNCTION story_bible_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE story_bibles 
  SET 
    project_id = NEW.project_id,
    content = NEW.content,
    version = NEW.version,
    created_at = NEW.created_at,
    updated_at = NEW.updated_at,
    metadata = NEW.metadata
  WHERE id = NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER story_bible_update_trigger
  INSTEAD OF UPDATE ON story_bible
  FOR EACH ROW
  EXECUTE FUNCTION story_bible_update();

CREATE OR REPLACE FUNCTION story_bible_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM story_bibles WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER story_bible_delete_trigger
  INSTEAD OF DELETE ON story_bible
  FOR EACH ROW
  EXECUTE FUNCTION story_bible_delete();

-- Grant same permissions on the view as the table
GRANT SELECT, INSERT, UPDATE, DELETE ON story_bible TO authenticated;

-- Create a users view that maps to auth.users for code compatibility
CREATE OR REPLACE VIEW public.users AS
SELECT 
  id,
  email,
  created_at,
  updated_at,
  last_sign_in_at,
  raw_app_meta_data->>'provider' as provider,
  raw_user_meta_data->>'full_name' as full_name,
  raw_user_meta_data->>'avatar_url' as avatar_url
FROM auth.users;

-- Grant read permissions on users view
GRANT SELECT ON users TO authenticated;

-- Add missing columns to achievements table to fully match API expectations
ALTER TABLE achievements 
  ADD COLUMN IF NOT EXISTS description TEXT;

-- Ensure all expected columns exist with proper names
DO $$ 
BEGIN
  -- Check if 'name' column exists and 'title' doesn't, then rename
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'achievements' AND column_name = 'name'
  ) AND NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'achievements' AND column_name = 'title'
  ) THEN
    ALTER TABLE achievements RENAME COLUMN name TO title;
  END IF;
END $$;