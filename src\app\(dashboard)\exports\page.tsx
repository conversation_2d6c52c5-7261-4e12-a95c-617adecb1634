import { createServerClient } from '@/lib/supabase'
import { redirect } from 'next/navigation'
import { ExportStatus } from '@/components/export/export-status'
import { ExportPanel } from '@/components/export/export-panel'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PageHeader } from '@/components/layout/page-header'
import { FileText } from 'lucide-react'

export default async function ExportsPage() {
  const supabase = await createServerClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')
  
  // Get user's projects for quick export
  const { data: projects } = await supabase
    .from('projects')
    .select('id, title, current_word_count, target_word_count')
    .eq('user_id', user.id)
    .order('updated_at', { ascending: false })
    .limit(5)
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageHeader
        title="Export Center"
        description="Manage and track all your project exports in one place"
        icon={FileText}
      />

      <Tabs defaultValue="status" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
          <TabsTrigger value="status">Export Status</TabsTrigger>
          <TabsTrigger value="quick">Quick Export</TabsTrigger>
        </TabsList>

        <TabsContent value="status">
          <ExportStatus userId={user.id} showAll />
        </TabsContent>

        <TabsContent value="quick" className="space-y-6">
          {projects && projects.length > 0 ? (
            <>
              <div className="text-lg font-medium">Recent Projects</div>
              <div className="grid gap-6">
                {projects.map(project => (
                  <ExportPanel
                    key={project.id}
                    projectId={project.id}
                    projectTitle={project.title}
                  />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No projects found</p>
              <p className="text-sm text-muted-foreground mt-1">
                Create a project to start exporting
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}