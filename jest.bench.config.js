/** @type {import('jest').Config} */
module.exports = {
  displayName: 'Performance Benchmarks',
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/tests/performance/**/*.bench.ts'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: {
        jsx: 'react',
        module: 'commonjs',
        target: 'es2017',
        lib: ['es2017', 'dom'],
        allowJs: true,
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        skipLibCheck: true,
        resolveJsonModule: true,
        moduleResolution: 'node',
        isolatedModules: true,
      },
    }],
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup/jest-setup.ts'],
  globals: {
    'ts-jest': {
      isolatedModules: true,
    },
  },
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'coverage/benchmarks',
      outputName: 'benchmark-results.xml',
    }],
  ],
  coverageDirectory: 'coverage/benchmarks',
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{ts,tsx}',
    '!src/**/*.stories.tsx',
  ],
  // Performance specific settings
  testTimeout: 30000, // 30 seconds for performance tests
  maxWorkers: 1, // Run benchmarks sequentially for accurate timing
};