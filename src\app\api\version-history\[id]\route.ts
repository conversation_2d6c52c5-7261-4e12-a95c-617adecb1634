import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { logger } from '@/lib/services/logger'

interface ChapterWithProject {
  projects?: {
    user_id: string;
  } | {
    user_id: string;
  }[];
}

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const supabase = await createTypedServerClient()

    // Get version with ownership verification
    const { data: version, error } = await supabase
      .from('chapter_versions')
      .select(`
        *,
        chapters!inner (
          id,
          title,
          chapter_number,
          projects!inner (
            id,
            title,
            user_id
          )
        )
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return handleAPIError(new NotFoundError('Resource'))
      }
      logger.error('Error fetching version:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Check access rights
    const isOwner = version.chapters.projects.user_id === authResult.user?.id
    
    if (!isOwner) {
      return handleAPIError(new AuthorizationError())
    }

    // Clean up response
    const { chapters, ...versionData } = version
    
    return NextResponse.json({ 
      version: {
        ...versionData,
        chapter_title: chapters.title,
        chapter_number: chapters.chapter_number,
        project_title: chapters.projects.title
      }
    })

  } catch (error) {
    return handleRouteError(error, 'Version History GET')
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const supabase = await createTypedServerClient()

    // Get version to check ownership
    const { data: version } = await supabase
      .from('chapter_versions')
      .select(`
        id,
        version_number,
        chapters!inner (
          id,
          projects!inner (
            id,
            user_id
          )
        )
      `)
      .eq('id', params.id)
      .single()

    if (!version) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Only project owner can delete versions
    const chapters = version.chapters as ChapterWithProject
    const projectData = Array.isArray(chapters) ? chapters[0]?.projects : chapters?.projects
    const ownerId = Array.isArray(projectData) ? projectData[0]?.user_id : projectData?.user_id
    if (ownerId !== authResult.user?.id) {
      return handleAPIError(new AuthorizationError())
    }

    // Don't allow deletion of version 1 (original)
    if (version.version_number === 1) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Delete version
    const { error } = await supabase
      .from('chapter_versions')
      .delete()
      .eq('id', params.id)

    if (error) {
      logger.error('Error deleting version:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    return handleRouteError(error, 'Version History DELETE')
  }
}