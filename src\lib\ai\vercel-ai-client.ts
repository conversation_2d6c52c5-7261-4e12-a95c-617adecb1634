/**
 * Vercel AI SDK Client
 * Enhanced AI client using Vercel AI SDK with preserved architecture patterns
 */

import { openai } from '@ai-sdk/openai'
import { anthropic } from '@ai-sdk/anthropic'
import { xai } from '@ai-sdk/xai'
import { generateText, generateObject, streamText, streamObject, CoreMessage, LanguageModel } from 'ai'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { config } from '@/lib/config'
import { AI_MODELS, AI_TEMPERATURE, AI_MAX_TOKENS } from '@/lib/config/ai-settings'
import { withRetry, ErrorContext } from '@/lib/services/error-handler'
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '@/lib/services/circuit-breaker'
import { SIZE_LIMITS } from '@/lib/constants'

// Provider configuration
const providers = {
  openai: openai,
  anthropic: anthropic,
  xai: xai,
} as const

type AIProvider = keyof typeof providers

export interface VercelAIConfig {
  provider?: AIProvider
  model?: string
  temperature?: number
  maxTokens?: number
  systemPrompt?: string
}

export interface StreamingOptions {
  onToken?: (token: string) => void
  onProgress?: (progress: { tokens: number; content: string }) => void
  onComplete?: (content: string) => void
  onError?: (error: Error) => void
}

interface FallbackStrategy {
  primary: AIProvider
  fallback: AIProvider
  maxFallbackAttempts: number
}

export class VercelAIClient {
  private defaultProvider: AIProvider = 'openai'
  private apiKey: string
  private xaiApiKey: string | undefined
  private fallbackStrategy: FallbackStrategy = {
    primary: 'openai',
    fallback: 'xai',
    maxFallbackAttempts: 2
  }

  constructor() {
    this.apiKey = config.openai.apiKey
    this.xaiApiKey = config.xai.apiKey
    
    if (!this.apiKey || !this.apiKey.startsWith('sk-')) {
      const errorMsg = 'OpenAI API key not configured or invalid. AI features will not work without a valid API key.'
      logger.error('⚠️  ' + errorMsg)
      
      if (process.env.NODE_ENV === 'production') {
        throw new Error(errorMsg)
      }
    }
  }

  /**
   * Check if xAI API key is configured
   */
  private hasXAIApiKey(): boolean {
    return !!(this.xaiApiKey && config.xai.enabled)
  }

  /**
   * Map OpenAI models to xAI fallback models
   */
  private mapToFallbackModel(model?: string): string {
    const fallbackMap: Record<string, string> = {
      'gpt-4.1-2025-04-14': 'grok-3',       // GPT 4.1 maps to grok-3
      'gpt-4o-mini': 'grok-3-mini',         // 4o-mini maps to grok-3-mini
      'gpt-4-turbo': 'grok-3',              // Turbo models map to grok-3
      'gpt-4-0125-preview': 'grok-3',       // Legacy GPT-4 maps to grok-3
      'gpt-3.5-turbo': 'grok-3-mini',       // GPT-3.5 maps to grok-3-mini
    }
    return fallbackMap[model || AI_MODELS.PRIMARY] || 'grok-3'
  }

  /**
   * Get the appropriate language model based on configuration
   */
  private getModel(config: VercelAIConfig = {}): LanguageModel {
    const modelName = config.model || AI_MODELS.PRIMARY

    // Auto-detect provider based on model name
    let provider = config.provider || this.defaultProvider
    if (modelName.startsWith('grok')) {
      provider = 'xai'
    } else if (modelName.startsWith('claude')) {
      provider = 'anthropic'
    } else {
      provider = 'openai'
    }

    // Map our model names to Vercel AI SDK format
    const modelMap: Record<string, string> = {
      'gpt-4.1-2025-04-14': 'gpt-4-turbo',
      'gpt-4o-mini': 'gpt-4o-mini',
      'gpt-4-turbo': 'gpt-4-turbo',
      'gpt-4-0125-preview': 'gpt-4-turbo',
      'gpt-3.5-turbo': 'gpt-3.5-turbo',
      'grok-beta': 'grok-beta',
      'grok-vision-beta': 'grok-vision-beta',
      'grok-3': 'grok-3',
      'grok-3-mini': 'grok-3-mini',
    }

    const mappedModel = modelMap[modelName] || modelName

    switch (provider) {
      case 'openai':
        return providers.openai(mappedModel)
      case 'anthropic':
        return providers.anthropic(mappedModel.startsWith('claude') ? mappedModel : 'claude-3-sonnet-20240229')
      case 'xai':
        return providers.xai(mappedModel.startsWith('grok') ? mappedModel : 'grok-3')
      default:
        return providers.openai(mappedModel)
    }
  }

  /**
   * Convert messages to Vercel AI SDK format
   */
  private formatMessages(systemPrompt: string, userPrompt: string): CoreMessage[] {
    const messages: CoreMessage[] = []
    
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt })
    }
    
    messages.push({ role: 'user', content: userPrompt })
    
    return messages
  }

  /**
   * Generate text with error handling and retries
   */
  async generateText(
    prompt: string,
    config: VercelAIConfig = {},
    errorContext?: ErrorContext
  ): Promise<string> {
    const {
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = AI_MAX_TOKENS.STANDARD,
      systemPrompt = 'You are a helpful AI assistant.',
    } = config

    const model = this.getModel(config)
    const messages = this.formatMessages(systemPrompt, prompt)

    const context: ErrorContext = {
      operation: 'VercelAIClient.generateText',
      ...errorContext,
      metadata: {
        model: config.model || AI_MODELS.PRIMARY,
        promptLength: prompt.length,
        ...errorContext?.metadata
      }
    }

    return withRetry(
      async () => withCircuitBreaker(
        'vercel-ai-generate-text',
        async () => {
          const result = await generateText({
            model,
            messages,
            temperature,
            maxTokens,
          })
          
          return result.text
        },
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
      {
        maxRetries: 3,
        onRetry: (error, attempt) => {
          logger.info(`Text generation retry ${attempt}:`, error.message)
        }
      },
      context
    )
  }

  /**
   * Generate text with automatic fallback to xAI if primary provider fails
   */
  async generateTextWithFallback(
    prompt: string,
    config: VercelAIConfig = {},
    errorContext?: ErrorContext
  ): Promise<string> {
    const primaryProvider = config.provider || this.fallbackStrategy.primary
    
    try {
      // Try primary provider (OpenAI by default)
      return await this.generateText(prompt, config, errorContext)
    } catch (primaryError) {
      logger.warn('Primary provider failed, attempting fallback to xAI', { 
        error: primaryError,
        provider: primaryProvider,
        model: config.model 
      })
      
      // If xAI is configured and we haven't already tried it, use it as fallback
      if (this.hasXAIApiKey() && primaryProvider !== 'xai') {
        const fallbackConfig: VercelAIConfig = {
          ...config,
          provider: 'xai',
          model: this.mapToFallbackModel(config.model)
        }
        
        try {
          const result = await this.generateText(prompt, fallbackConfig, {
            ...errorContext,
            metadata: {
              ...errorContext?.metadata,
              fallbackAttempt: true,
              originalProvider: primaryProvider
            }
          })
          
          logger.info('Successfully used xAI fallback', {
            model: fallbackConfig.model,
            originalError: primaryError
          })
          
          return result
        } catch (fallbackError) {
          logger.error('Fallback provider also failed', { 
            error: fallbackError,
            provider: 'xai',
            model: fallbackConfig.model
          })
          // Throw original error as it's more relevant
          throw primaryError
        }
      }
      
      throw primaryError
    }
  }

  /**
   * Generate structured object with schema validation
   */
  async generateObject<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    config: VercelAIConfig = {},
    errorContext?: ErrorContext
  ): Promise<T> {
    const {
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = AI_MAX_TOKENS.STANDARD,
      systemPrompt = 'You are a helpful AI assistant. Respond with valid JSON that matches the required schema.',
    } = config

    const model = this.getModel(config)
    const messages = this.formatMessages(systemPrompt, prompt)

    const context: ErrorContext = {
      operation: 'VercelAIClient.generateObject',
      ...errorContext,
      metadata: {
        model: config.model || AI_MODELS.PRIMARY,
        promptLength: prompt.length,
        hasSchema: true,
        ...errorContext?.metadata
      }
    }

    return withRetry(
      async () => withCircuitBreaker(
        'vercel-ai-generate-object',
        async () => {
          const result = await generateObject({
            model,
            messages,
            schema,
            temperature,
            maxTokens,
          })
          
          return result.object
        },
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
      {
        maxRetries: 3,
        onRetry: (error, attempt) => {
          logger.info(`Object generation retry ${attempt}:`, error.message)
        }
      },
      context
    )
  }

  /**
   * Generate structured object with fallback to xAI if primary provider fails
   */
  async generateObjectWithFallback<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    config: VercelAIConfig = {},
    errorContext?: ErrorContext
  ): Promise<T> {
    const primaryProvider = config.provider || this.fallbackStrategy.primary
    
    try {
      // Try primary provider (OpenAI by default)
      return await this.generateObject(prompt, schema, config, errorContext)
    } catch (primaryError) {
      logger.warn('Primary provider failed for structured output, attempting fallback to xAI', { 
        error: primaryError,
        provider: primaryProvider,
        model: config.model 
      })
      
      // If xAI is configured and we haven't already tried it, use it as fallback
      if (this.hasXAIApiKey() && primaryProvider !== 'xai') {
        const fallbackConfig: VercelAIConfig = {
          ...config,
          provider: 'xai',
          model: this.mapToFallbackModel(config.model)
        }
        
        try {
          const result = await this.generateObject(prompt, schema, fallbackConfig, {
            ...errorContext,
            metadata: {
              ...errorContext?.metadata,
              fallbackAttempt: true,
              originalProvider: primaryProvider
            }
          })
          
          logger.info('Successfully used xAI fallback for structured output', {
            model: fallbackConfig.model,
            originalError: primaryError
          })
          
          return result
        } catch (fallbackError) {
          logger.error('Fallback provider also failed for structured output', { 
            error: fallbackError,
            provider: 'xai',
            model: fallbackConfig.model
          })
          // Throw original error as it's more relevant
          throw primaryError
        }
      }
      
      throw primaryError
    }
  }

  /**
   * Stream text generation with progress callbacks
   */
  async streamText(
    prompt: string,
    config: VercelAIConfig = {},
    streamingOptions: StreamingOptions = {},
    errorContext?: ErrorContext
  ): Promise<string> {
    const {
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = AI_MAX_TOKENS.STANDARD,
      systemPrompt = 'You are a helpful AI assistant.',
    } = config

    const model = this.getModel(config)
    const messages = this.formatMessages(systemPrompt, prompt)

    const context: ErrorContext = {
      operation: 'VercelAIClient.streamText',
      ...errorContext,
      metadata: {
        model: config.model || AI_MODELS.PRIMARY,
        promptLength: prompt.length,
        streaming: true,
        ...errorContext?.metadata
      }
    }

    return withRetry(
      async () => withCircuitBreaker(
        'vercel-ai-stream-text',
        async () => {
          const result = await streamText({
            model,
            messages,
            temperature,
            maxTokens,
          })

          let fullContent = ''
          let tokenCount = 0

          try {
            for await (const textPart of result.textStream) {
              fullContent += textPart
              tokenCount++

              // Call streaming callbacks
              if (streamingOptions.onToken) {
                streamingOptions.onToken(textPart)
              }

              if (streamingOptions.onProgress) {
                streamingOptions.onProgress({
                  tokens: tokenCount,
                  content: fullContent
                })
              }
            }

            if (streamingOptions.onComplete) {
              streamingOptions.onComplete(fullContent)
            }

            return fullContent
          } catch (error) {
            if (streamingOptions.onError) {
              streamingOptions.onError(error as Error)
            }
            throw error
          }
        },
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
      {
        maxRetries: 2, // Fewer retries for streaming
        onRetry: (error, attempt) => {
          logger.info(`Stream text retry ${attempt}:`, error.message)
        }
      },
      context
    )
  }

  /**
   * Stream text generation with fallback to xAI if primary provider fails
   */
  async streamTextWithFallback(
    prompt: string,
    config: VercelAIConfig = {},
    streamingOptions: StreamingOptions = {},
    errorContext?: ErrorContext
  ): Promise<string> {
    const primaryProvider = config.provider || this.fallbackStrategy.primary
    
    try {
      // Try primary provider (OpenAI by default)
      return await this.streamText(prompt, config, streamingOptions, errorContext)
    } catch (primaryError) {
      logger.warn('Primary provider failed for streaming, attempting fallback to xAI', { 
        error: primaryError,
        provider: primaryProvider,
        model: config.model 
      })
      
      // If xAI is configured and we haven't already tried it, use it as fallback
      if (this.hasXAIApiKey() && primaryProvider !== 'xai') {
        const fallbackConfig: VercelAIConfig = {
          ...config,
          provider: 'xai',
          model: this.mapToFallbackModel(config.model)
        }
        
        try {
          const result = await this.streamText(prompt, fallbackConfig, streamingOptions, {
            ...errorContext,
            metadata: {
              ...errorContext?.metadata,
              fallbackAttempt: true,
              originalProvider: primaryProvider
            }
          })
          
          logger.info('Successfully used xAI fallback for streaming', {
            model: fallbackConfig.model,
            originalError: primaryError
          })
          
          return result
        } catch (fallbackError) {
          logger.error('Fallback provider also failed for streaming', { 
            error: fallbackError,
            provider: 'xai',
            model: fallbackConfig.model
          })
          // Throw original error as it's more relevant
          throw primaryError
        }
      }
      
      throw primaryError
    }
  }

  /**
   * Stream object generation with schema validation
   */
  async streamObject<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    config: VercelAIConfig = {},
    streamingOptions: StreamingOptions = {},
    errorContext?: ErrorContext
  ): Promise<T> {
    const {
      temperature = AI_TEMPERATURE.BALANCED,
      maxTokens = AI_MAX_TOKENS.STANDARD,
      systemPrompt = 'You are a helpful AI assistant. Respond with valid JSON that matches the required schema.',
    } = config

    const model = this.getModel(config)
    const messages = this.formatMessages(systemPrompt, prompt)

    const context: ErrorContext = {
      operation: 'VercelAIClient.streamObject',
      ...errorContext,
      metadata: {
        model: config.model || AI_MODELS.PRIMARY,
        promptLength: prompt.length,
        streaming: true,
        hasSchema: true,
        ...errorContext?.metadata
      }
    }

    return withRetry(
      async () => withCircuitBreaker(
        'vercel-ai-stream-object',
        async () => {
          const result = await streamObject({
            model,
            messages,
            schema,
            temperature,
            maxTokens,
          })

          let tokenCount = 0

          try {
            for await (const partialObject of result.partialObjectStream) {
              tokenCount++

              if (streamingOptions.onProgress) {
                streamingOptions.onProgress({
                  tokens: tokenCount,
                  content: JSON.stringify(partialObject, null, 2)
                })
              }
            }

            const finalObject = await result.object

            if (streamingOptions.onComplete) {
              streamingOptions.onComplete(JSON.stringify(finalObject, null, 2))
            }

            return finalObject
          } catch (error) {
            if (streamingOptions.onError) {
              streamingOptions.onError(error as Error)
            }
            throw error
          }
        },
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
      {
        maxRetries: 2,
        onRetry: (error, attempt) => {
          logger.info(`Stream object retry ${attempt}:`, error.message)
        }
      },
      context
    )
  }

  /**
   * Generate embeddings using OpenAI API directly
   * Note: Vercel AI SDK doesn't support embeddings yet, so we use OpenAI client
   */
  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.apiKey || !this.apiKey.startsWith('sk-')) {
      throw new Error('OpenAI API key required for embeddings')
    }
    
    // Clean and truncate text if necessary
    const cleanText = text.trim().substring(0, SIZE_LIMITS.EMBEDDING_TEXT_LIMIT)
    
    try {
      // Use openai provider directly for embeddings
      const openaiProvider = providers.openai as any
      const response = await withCircuitBreaker(
        'openai-embeddings',
        async () => {
          // Direct API call since Vercel AI SDK doesn't support embeddings
          const apiResponse = await fetch('https://api.openai.com/v1/embeddings', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: AI_MODELS.EMBEDDING,
              input: cleanText,
              dimensions: 1536,
            }),
          })
          
          if (!apiResponse.ok) {
            throw new Error(`Embedding API error: ${apiResponse.status} ${apiResponse.statusText}`)
          }
          
          return apiResponse.json()
        },
        CIRCUIT_BREAKER_PRESETS.OPENAI
      )
      
      if (!response.data || response.data.length === 0) {
        throw new Error('No embedding data received')
      }
      
      return response.data[0].embedding
    } catch (error) {
      logger.error('Error generating embedding:', error)
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Health check for the AI client
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details: string }> {
    try {
      const result = await this.generateText('Hello', { maxTokens: 10 })
      return {
        status: result ? 'healthy' : 'unhealthy',
        details: result ? 'AI client responding normally' : 'No response from AI client'
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        details: `AI client error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
}

// Export singleton instance
export const vercelAIClient = new VercelAIClient()
