/**
 * Middleware helper for collaboration endpoints
 * Provides reusable authorization checks for collaboration routes
 */

import { NextResponse } from 'next/server'
import { authenticateUser } from '@/lib/auth/server'
import { checkProjectAccess, type CollaborationPermissions } from '@/lib/auth/collaboration-auth'
import { createClient } from '@/lib/supabase/server'
import { logger } from '@/lib/services/logger'

export interface CollaborationAuthResult {
  success: boolean
  user?: {
    id: string
    email: string
  }
  session?: {
    id: string
    project_id: string
  }
  permissions?: CollaborationPermissions
  response?: NextResponse
}

/**
 * Verifies user authentication and authorization for collaboration endpoints
 * @param sessionId - The collaboration session ID
 * @param requiredPermission - The permission level required ('view' or 'edit')
 * @returns Auth result with user, session, and permissions data
 */
export async function verifyCollaborationAccess(
  sessionId: string,
  requiredPermission: 'view' | 'edit' = 'view'
): Promise<CollaborationAuthResult> {
  try {
    // Step 1: Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return {
        success: false,
        response: authResult.response || NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
    }

    // Step 2: Get session details
    const supabase = await createClient()
    const { data: session, error: sessionError } = await supabase
      .from('collaboration_sessions')
      .select('id, project_id, is_active')
      .eq('id', sessionId)
      .single()

    if (sessionError || !session) {
      logger.warn('Session not found', { sessionId, error: sessionError })
      return {
        success: false,
        response: NextResponse.json(
          { error: 'Session not found' },
          { status: 404 }
        )
      }
    }

    if (!session.is_active) {
      return {
        success: false,
        response: NextResponse.json(
          { error: 'Session is no longer active' },
          { status: 410 }
        )
      }
    }

    // Step 3: Check project permissions
    const permissions = await checkProjectAccess(authResult.user.id, session.project_id)
    
    // Verify required permission level
    if (requiredPermission === 'edit' && !permissions.canEdit) {
      return {
        success: false,
        response: NextResponse.json(
          { error: 'You do not have permission to make changes to this project' },
          { status: 403 }
        )
      }
    } else if (requiredPermission === 'view' && !permissions.canView) {
      return {
        success: false,
        response: NextResponse.json(
          { error: 'You do not have permission to view this project' },
          { status: 403 }
        )
      }
    }

    // Success - return all relevant data
    return {
      success: true,
      user: authResult.user,
      session: {
        id: session.id,
        project_id: session.project_id
      },
      permissions
    }
  } catch (error) {
    logger.error('Error in collaboration access verification', { error, sessionId })
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Verifies user can create a collaboration session for a project
 */
export async function verifyProjectOwnership(
  projectId: string
): Promise<CollaborationAuthResult> {
  try {
    // Authenticate user
    const authResult = await authenticateUser()
    if (!authResult.success || !authResult.user) {
      return {
        success: false,
        response: authResult.response || NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
    }

    // Check project ownership/edit permissions
    const permissions = await checkProjectAccess(authResult.user.id, projectId)
    
    if (!permissions.canEdit) {
      return {
        success: false,
        response: NextResponse.json(
          { error: 'You do not have permission to create collaboration sessions for this project' },
          { status: 403 }
        )
      }
    }

    return {
      success: true,
      user: authResult.user,
      permissions
    }
  } catch (error) {
    logger.error('Error in project ownership verification', { error, projectId })
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}