'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Plus, X, MapPin } from 'lucide-react'
import type { Location } from './location-manager'

interface CreateLocationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLocationCreate: (locationData: any) => Promise<void>
  parentLocation?: Location | null
  existingLocations: Location[]
}

export function CreateLocationDialog({
  open,
  onOpenChange,
  onLocationCreate,
  parentLocation,
  existingLocations
}: CreateLocationDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    locationType: 'other' as Location['location_type'],
    features: [] as string[],
    significance: '',
    isShareable: false
  })
  const [loading, setLoading] = useState(false)
  const [newFeature, setNewFeature] = useState('')

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setFormData({
        name: '',
        description: '',
        locationType: parentLocation ? getRecommendedChildType(parentLocation.location_type) : 'other',
        features: [],
        significance: '',
        isShareable: false
      })
      setNewFeature('')
    }
  }, [open, parentLocation])

  const getRecommendedChildType = (parentType: Location['location_type']): Location['location_type'] => {
    const typeHierarchy: Record<Location['location_type'], Location['location_type']> = {
      world: 'continent',
      continent: 'country',
      country: 'region',
      region: 'city',
      city: 'building',
      building: 'room',
      room: 'other',
      other: 'other'
    }
    return typeHierarchy[parentType] || 'other'
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) return
    
    setLoading(true)
    try {
      await onLocationCreate({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        locationType: formData.locationType,
        features: formData.features,
        significance: formData.significance.trim() || undefined,
        isShareable: formData.isShareable
      })
      
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
    } finally {
      setLoading(false)
    }
  }

  const handleAddFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }))
      setNewFeature('')
    }
  }

  const handleRemoveFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(f => f !== feature)
    }))
  }

  const getLocationTypeDescription = (type: Location['location_type']) => {
    const descriptions = {
      world: 'An entire world or planet',
      continent: 'A large landmass within a world',
      country: 'A nation or territory within a continent',
      region: 'A district or area within a country',
      city: 'A settlement or urban area',
      building: 'A structure within a city or location',
      room: 'A space within a building',
      other: 'Any other type of location'
    }
    return descriptions[type]
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Create New Location
          </DialogTitle>
          <DialogDescription>
            {parentLocation 
              ? `Create a child location within "${parentLocation.name}"`
              : 'Create a new location for your story world'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Parent Location Info */}
          {parentLocation && (
            <div className="p-3 bg-muted rounded-lg">
              <Label className="text-sm font-medium">Parent Location</Label>
              <p className="text-sm text-muted-foreground mt-1">
                {parentLocation.name} ({parentLocation.location_type})
              </p>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Location Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter location name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="type">Location Type</Label>
              <Select value={formData.locationType} onValueChange={(value) => setFormData(prev => ({ ...prev, locationType: value as Location['location_type'] }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="world">
                    <div>
                      <div className="font-medium">World</div>
                      <div className="text-xs text-muted-foreground">An entire world or planet</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="continent">
                    <div>
                      <div className="font-medium">Continent</div>
                      <div className="text-xs text-muted-foreground">A large landmass within a world</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="country">
                    <div>
                      <div className="font-medium">Country</div>
                      <div className="text-xs text-muted-foreground">A nation or territory</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="region">
                    <div>
                      <div className="font-medium">Region</div>
                      <div className="text-xs text-muted-foreground">A district or area</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="city">
                    <div>
                      <div className="font-medium">City</div>
                      <div className="text-xs text-muted-foreground">A settlement or urban area</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="building">
                    <div>
                      <div className="font-medium">Building</div>
                      <div className="text-xs text-muted-foreground">A structure</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="room">
                    <div>
                      <div className="font-medium">Room</div>
                      <div className="text-xs text-muted-foreground">A space within a building</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="other">
                    <div>
                      <div className="font-medium">Other</div>
                      <div className="text-xs text-muted-foreground">Any other type of location</div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe this location..."
              rows={3}
            />
          </div>

          {/* Features */}
          <div className="space-y-2">
            <Label>Features</Label>
            
            {/* Existing Features */}
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.features.map((feature) => (
                <Badge key={feature} variant="secondary" className="text-sm">
                  {feature}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                    onClick={() => handleRemoveFeature(feature)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </Badge>
              ))}
            </div>
            
            {/* Add New Feature */}
            <div className="flex gap-2">
              <Input
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                placeholder="Add a feature (e.g., 'ancient ruins', 'busy marketplace')"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddFeature())}
              />
              <Button type="button" variant="outline" onClick={handleAddFeature}>
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Features help describe what makes this location unique or notable
            </p>
          </div>

          {/* Significance */}
          <div className="space-y-2">
            <Label htmlFor="significance">Significance (Optional)</Label>
            <Textarea
              id="significance"
              value={formData.significance}
              onChange={(e) => setFormData(prev => ({ ...prev, significance: e.target.value }))}
              placeholder="Why is this location important to your story?"
              rows={2}
            />
          </div>

          {/* Shareable Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              checked={formData.isShareable}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isShareable: checked }))}
            />
            <div>
              <Label className="text-sm">Make this location shareable</Label>
              <p className="text-xs text-muted-foreground">
                Shareable locations can be used across multiple series in your universe
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.name.trim()}>
              {loading ? 'Creating...' : 'Create Location'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}