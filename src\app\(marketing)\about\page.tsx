import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { 
  BookOpen, 
  Users, 
  Zap, 
  Target, 
  Heart, 
  Code, 
  Feather,
  Brain,
  Sparkles,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
          <Feather className="w-8 h-8 text-primary" />
        </div>
        <h1 className="text-5xl font-bold mb-6">About BookScribe AI</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          We're building the future of novel writing with AI agents that understand your creative vision 
          and help you craft epic stories with consistency across hundreds of thousands of words.
        </p>
      </div>

      {/* Mission */}
      <Card className="mb-12">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-2xl">
            <Target className="h-6 w-6" />
            Our Mission
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-lg text-muted-foreground max-w-4xl mx-auto">
            Every writer deserves access to powerful tools that amplify their creativity without compromising their unique voice. 
            BookScribe AI combines cutting-edge artificial intelligence with deep respect for the art of storytelling, 
            creating a sanctuary where authors can bring their most ambitious literary visions to life.
          </p>
        </CardContent>
      </Card>

      {/* Key Features */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">What Makes BookScribe Special</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-primary" />
                Multi-Agent AI System
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Seven specialized AI agents work together - from Story Architect to Writing Agent - 
                each focused on different aspects of novel creation while maintaining perfect consistency.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-primary" />
                Context-Aware Writing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Our AI maintains awareness of your entire story universe - characters, plot threads, 
                and world-building details - ensuring consistency across even the longest novels.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Writer's Sanctuary Theme
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                A beautiful, distraction-free environment inspired by the greatest writing tools, 
                with warm paper tones and typography that makes long writing sessions a pleasure.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                Collaborative Writing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Real-time collaboration features let you work with co-authors, editors, and beta readers 
                while maintaining version control and editorial oversight.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-primary" />
                Series & Universe Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Plan and write multi-book series with shared characters and continuity. 
                Build expansive fictional universes that span multiple storylines.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5 text-primary" />
                Export Everything
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Your work belongs to you. Export to EPUB, PDF, DOCX, and other formats. 
                No vendor lock-in - take your stories anywhere.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Technology */}
      <Card className="mb-12">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Brain className="h-6 w-6" />
            Built with Cutting-Edge Technology
          </CardTitle>
          <CardDescription>
            BookScribe AI leverages the latest advances in artificial intelligence and web technology
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">AI & Machine Learning</h3>
              <ul className="space-y-1 text-muted-foreground">
                <li>• OpenAI GPT-4.1 and GPT-o4 mini</li>
                <li>• Custom agent orchestration system</li>
                <li>• Advanced context management</li>
                <li>• Specialized writing prompts</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Platform & Infrastructure</h3>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Next.js 15 with App Router</li>
                <li>• Supabase for real-time data</li>
                <li>• TypeScript for type safety</li>
                <li>• Vercel for global deployment</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Values */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Our Values</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="text-center">
              <Heart className="h-8 w-8 text-primary mx-auto mb-2" />
              <CardTitle>Writer-First</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground">
                Every feature is designed with writers in mind. We enhance your creativity, never replace it.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <BookOpen className="h-8 w-8 text-primary mx-auto mb-2" />
              <CardTitle>Your Content, Your Rights</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground">
                You own everything you create. We're here to help you write, not claim your work.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Sparkles className="h-8 w-8 text-primary mx-auto mb-2" />
              <CardTitle>Quality Over Quantity</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground">
                We focus on building powerful, reliable tools rather than rushing features to market.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Call to Action */}
      <Card className="text-center bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
        <CardContent className="p-8">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Writing?</h2>
          <p className="text-lg text-muted-foreground mb-6 max-w-2xl mx-auto">
            Join thousands of authors who are already using BookScribe AI to bring their stories to life. 
            Start your free trial today and experience the future of novel writing.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/signup">
                Start Writing Free
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/demo">
                Try the Demo
              </Link>
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            No credit card required • 7-day free trial • Cancel anytime
          </p>
        </CardContent>
      </Card>
    </div>
  )
}