#!/usr/bin/env node
/**
 * <PERSON><PERSON><PERSON> to identify and optionally remove unused exports from the codebase
 * Run with: npx tsx scripts/cleanup-unused-exports.ts
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

const EXCLUDED_PATTERNS = [
  'node_modules',
  '.next',
  'public',
  'tests',
  'scripts',
  'coverage',
  'dist',
  'build',
  // Files that are entry points or configs
  'instrumentation.ts',
  'middleware.ts',
  'next.config.js',
  'tailwind.config.ts',
  'playwright.config.ts',
  // API routes (Next.js expects specific exports)
  '/api/',
  // Page files (Next.js expects default export)
  '/app/',
  // Keep all UI components as they might be used
  '/components/ui/',
];

interface UnusedExport {
  file: string;
  exports: string[];
}

function shouldExcludeFile(filePath: string): boolean {
  const normalizedPath = filePath.replace(/\\/g, '/');
  return EXCLUDED_PATTERNS.some(pattern => normalizedPath.includes(pattern));
}

function getUnusedExports(): UnusedExport[] {
  try {
    console.log('🔍 Analyzing unused exports...');
    
    // Run ts-unused-exports
    const output = execSync(
      'npx ts-unused-exports tsconfig.json --excludePathsFromReport=node_modules --excludePathsFromReport=tests --excludePathsFromReport=public --excludePathsFromReport=scripts --excludePathsFromReport=.next',
      { encoding: 'utf-8', stdio: 'pipe' }
    );

    const lines = output.split('\n').filter(line => line.trim());
    const results: UnusedExport[] = [];
    let currentFile: string | null = null;
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [file, exportsStr] = line.split(':').map(s => s.trim());
        if (file && !shouldExcludeFile(file)) {
          const exports = exportsStr ? exportsStr.split(',').map(e => e.trim()) : [];
          if (exports.length > 0) {
            results.push({ file, exports });
          }
        }
      }
    }
    
    return results;
  } catch (error) {
    console.error('Error running ts-unused-exports:', error);
    return [];
  }
}

function categorizeUnusedExports(unusedExports: UnusedExport[]) {
  const categories = {
    services: [] as UnusedExport[],
    utils: [] as UnusedExport[],
    hooks: [] as UnusedExport[],
    components: [] as UnusedExport[],
    types: [] as UnusedExport[],
    lib: [] as UnusedExport[],
    other: [] as UnusedExport[],
  };

  for (const item of unusedExports) {
    if (item.file.includes('/services/')) {
      categories.services.push(item);
    } else if (item.file.includes('/utils/') || item.file.includes('/lib/utils/')) {
      categories.utils.push(item);
    } else if (item.file.includes('/hooks/')) {
      categories.hooks.push(item);
    } else if (item.file.includes('/components/')) {
      categories.components.push(item);
    } else if (item.file.includes('/types/') || item.file.includes('types.ts')) {
      categories.types.push(item);
    } else if (item.file.includes('/lib/')) {
      categories.lib.push(item);
    } else {
      categories.other.push(item);
    }
  }

  return categories;
}

function generateReport(categories: ReturnType<typeof categorizeUnusedExports>) {
  const report: string[] = ['# Unused Exports Report\n'];
  
  let totalExports = 0;
  let totalFiles = 0;

  for (const [category, items] of Object.entries(categories)) {
    if (items.length === 0) continue;
    
    report.push(`\n## ${category.charAt(0).toUpperCase() + category.slice(1)} (${items.length} files)\n`);
    
    for (const item of items) {
      totalFiles++;
      totalExports += item.exports.length;
      report.push(`### ${item.file}`);
      report.push(`Unused exports: ${item.exports.join(', ')}\n`);
    }
  }

  report.unshift(`Total files with unused exports: ${totalFiles}`);
  report.unshift(`Total unused exports: ${totalExports}`);
  report.unshift(`Generated on: ${new Date().toISOString()}\n`);

  return report.join('\n');
}

function main() {
  console.log('🧹 BookScribe Unused Export Cleanup Tool\n');

  const unusedExports = getUnusedExports();
  
  if (unusedExports.length === 0) {
    console.log('✅ No unused exports found!');
    return;
  }

  const categories = categorizeUnusedExports(unusedExports);
  const report = generateReport(categories);
  
  // Write report to file
  const reportPath = path.join(process.cwd(), 'unused-exports-report.md');
  fs.writeFileSync(reportPath, report);
  
  console.log(`\n📊 Report generated: ${reportPath}`);
  
  // Print summary to console
  let totalExports = 0;
  let totalFiles = 0;
  
  for (const items of Object.values(categories)) {
    totalFiles += items.length;
    totalExports += items.reduce((sum, item) => sum + item.exports.length, 0);
  }
  
  console.log(`\n📈 Summary:`);
  console.log(`   - Total files with unused exports: ${totalFiles}`);
  console.log(`   - Total unused exports: ${totalExports}`);
  
  console.log('\n🔍 Breakdown by category:');
  for (const [category, items] of Object.entries(categories)) {
    if (items.length > 0) {
      const exportCount = items.reduce((sum, item) => sum + item.exports.length, 0);
      console.log(`   - ${category}: ${items.length} files, ${exportCount} exports`);
    }
  }
  
  console.log('\n💡 Next steps:');
  console.log('   1. Review the report at unused-exports-report.md');
  console.log('   2. Identify exports that are actually used (false positives)');
  console.log('   3. Remove truly unused exports to reduce bundle size');
  console.log('   4. Consider converting some exports to internal functions if only used in the same file');
}

main();