'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { 
  BookOpen, 
  Clock, 
  ScrollText,
  ArrowRight,
  Lightbulb,
  Edit3,
  Calendar,
  Globe,
  Sparkles,
  Feather
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { SearchResult } from '../content-search-interface'

interface StoryBiblePreviewProps {
  result: SearchResult
  onNavigate: () => void
  onEdit?: () => void
  className?: string
}

export function StoryBiblePreview({ 
  result, 
  onNavigate, 
  onEdit,
  className 
}: StoryBiblePreviewProps) {
  const entryType = result.metadata.entryType || 'general'
  
  const entryIcons = {
    world_rule: Globe,
    timeline_event: Calendar,
    theme: Lightbulb,
    plot_thread: Feather,
    general: BookOpen
  }

  const entryColors = {
    world_rule: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
    timeline_event: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    theme: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
    plot_thread: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    general: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
  }

  const Icon = entryIcons[entryType as keyof typeof entryIcons] || BookOpen

  return (
    <Card className={cn("hover:shadow-lg transition-shadow", className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              "p-2 rounded-lg",
              entryColors[entryType as keyof typeof entryColors]
            )}>
              <Icon className="w-5 h-5" />
            </div>
            <div>
              <CardTitle className="text-lg">{result.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge 
                  variant="outline" 
                  className="capitalize text-xs"
                >
                  {entryType.replace('_', ' ')}
                </Badge>
                <Sparkles className="w-3 h-3 text-primary" />
              </div>
            </div>
          </div>
          <Badge variant="secondary">Story Bible</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Content */}
        <div className="prose prose-sm dark:prose-invert max-w-none">
          <p className="line-clamp-3 text-muted-foreground">
            {result.excerpt}
          </p>
        </div>

        {/* Entry-specific content */}
        {entryType === 'timeline_event' && (
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Timeline Position</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Occurs in the story timeline
            </p>
          </div>
        )}

        {entryType === 'world_rule' && (
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <Globe className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">World Building</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Defines the rules and physics of your world
            </p>
          </div>
        )}

        {entryType === 'theme' && (
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <Lightbulb className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Thematic Element</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Core theme that runs through the narrative
            </p>
          </div>
        )}

        {/* Metadata */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span>Updated {new Date(result.metadata.lastModified).toLocaleDateString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <ScrollText className="w-3 h-3" />
            <span>Story Bible Entry</span>
          </div>
        </div>

        {/* Highlights */}
        {result.highlights.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Relevant content:</p>
            <div className="space-y-1">
              {result.highlights.slice(0, 2).map((highlight, idx) => (
                <div
                  key={idx}
                  className="text-sm p-2 bg-muted/50 rounded"
                  dangerouslySetInnerHTML={{ __html: highlight }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button 
            variant="default" 
            size="sm" 
            onClick={onNavigate}
            className="flex-1"
          >
            <BookOpen className="w-4 h-4 mr-2" />
            View in Story Bible
          </Button>
          {onEdit && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onEdit}
              className="flex-1"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}