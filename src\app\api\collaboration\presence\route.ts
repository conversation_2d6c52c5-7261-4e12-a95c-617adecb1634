import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler'
import { createTypedServerClient } from '@/lib/supabase'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'

const UpdatePresenceSchema = z.object({
  projectId: baseSchemas.uuid,
  chapterId: baseSchemas.uuid.optional(),
  status: z.enum(['online', 'away', 'busy', 'offline']).default('online'),
  currentPage: z.string().max(100).optional(),
  currentSection: z.string().max(200).optional(),
  isWriting: z.boolean().default(false),
  cursorPosition: z.object({
    line: z.number().min(0).max(1000000),
    column: z.number().min(0).max(10000)
  }).optional(),
  selectionRange: z.object({
    start: z.object({ line: z.number().min(0).max(1000000), column: z.number().min(0).max(10000) }),
    end: z.object({ line: z.number().min(0).max(1000000), column: z.number().min(0).max(10000) })
  }).optional(),
  viewport: z.object({
    top: z.number().min(0),
    bottom: z.number().min(0),
    scrollTop: z.number().min(0)
  }).optional(),
  sessionId: z.string().max(100).optional()
})

const presenceQuerySchema = z.object({
  projectId: baseSchemas.uuid
})

/**
 * GET /api/collaboration/presence
 * Get presence information for a project
 */
export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId')
  };

  // Validate query parameters
  const parseResult = presenceQuerySchema.safeParse(queryParams);
  if (!parseResult.success) {
    return UnifiedResponse.error('Invalid query parameters', 400, parseResult.error.errors);
  }

  const { projectId } = parseResult.data;

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'collaboration-presence',
    rateLimitCost: 1,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const supabase = await createTypedServerClient();
      
      // Check if user has access to this project
      const { data: hasAccess } = await supabase
        .from('project_collaborators')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (!hasAccess) {
        // Check if user owns the project
        const { data: project } = await supabase
          .from('projects')
          .select('user_id')
          .eq('id', projectId)
          .eq('user_id', user.id)
          .single();
          
        if (!project) {
          return { valid: false, error: 'Access denied to this project' };
        }
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const supabase = await createTypedServerClient();

    // Get presence data using the function
    const { data: presence, error } = await supabase
      .rpc('get_project_presence', { p_project_id: projectId })

    if (error) {
      logger.error('Failed to get project presence', error)
      return handleAPIError(new Error('Failed to get presence data'))
    }

    logger.debug('Retrieved project presence', {
      userId: user.id,
      projectId,
      presenceCount: presence?.length || 0,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      presence: presence || [],
      projectId
    });

  } catch (error) {
    logger.error('Error getting presence', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to get presence information');
  }
});

/**
 * POST /api/collaboration/presence
 * Update user presence information
 */
export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: UpdatePresenceSchema,
    rateLimitKey: 'collaboration-presence-update',
    rateLimitCost: 1, // Low cost for frequent updates
    maxBodySize: 5 * 1024, // 5KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const body = await req.json();
      const { projectId } = body;

      const supabase = await createTypedServerClient();
      
      // Check if user has access to this project
      const { data: hasAccess } = await supabase
        .from('project_collaborators')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (!hasAccess) {
        // Check if user owns the project
        const { data: project } = await supabase
          .from('projects')
          .select('user_id')
          .eq('id', projectId)
          .eq('user_id', user.id)
          .single();
          
        if (!project) {
          return { valid: false, error: 'Access denied to this project' };
        }
      }

      // Validate chapter access if provided
      if (body.chapterId) {
        const { data: chapter } = await supabase
          .from('chapters')
          .select('id')
          .eq('id', body.chapterId)
          .eq('project_id', projectId)
          .single();

        if (!chapter) {
          return { valid: false, error: 'Chapter not found in this project' };
        }
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const validatedData = context.body;

  try {
    const supabase = await createTypedServerClient();

    // Update presence using the function
    const { data: presence, error } = await supabase
      .rpc('update_user_presence', {
        p_user_id: request.user.id,
        p_project_id: validatedData.projectId,
        p_chapter_id: validatedData.chapterId || null,
        p_status: validatedData.status,
        p_current_page: validatedData.currentPage || null,
        p_current_section: validatedData.currentSection || null,
        p_is_writing: validatedData.isWriting,
        p_cursor_position: validatedData.cursorPosition ? JSON.stringify(validatedData.cursorPosition) : null,
        p_selection_range: validatedData.selectionRange ? JSON.stringify(validatedData.selectionRange) : null,
        p_viewport: validatedData.viewport ? JSON.stringify(validatedData.viewport) : null,
        p_session_id: validatedData.sessionId || null
      })

    if (error) {
      logger.error('Failed to update presence', error)
      return handleAPIError(new Error('Failed to update presence'))
    }

    logger.debug('Updated user presence', {
      userId: user.id,
      projectId: validatedData.projectId,
      status: validatedData.status,
      isWriting: validatedData.isWriting,
      clientIP: context.clientIP
    })

    return UnifiedResponse.success({
      presence,
      message: 'Presence updated successfully'
    });

  } catch (error) {
    logger.error('Error updating presence', error, {
      userId: user.id,
      projectId: validatedData.projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to update presence');
  }
});

/**
 * DELETE /api/collaboration/presence
 * Mark user as offline for a project
 */
export const DELETE = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId')
  };

  // Validate query parameters
  const parseResult = presenceQuerySchema.safeParse(queryParams);
  if (!parseResult.success) {
    return UnifiedResponse.error('Invalid query parameters', 400, parseResult.error.errors);
  }

  const { projectId } = parseResult.data;

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'collaboration-presence',
    rateLimitCost: 1,
    maxRequestSize: 1024,
    validateCSRF: true
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const supabase = await createTypedServerClient();
    
    // Update presence to offline
    const { error } = await supabase
      .from('user_presence')
      .update({
        status: 'offline',
        is_writing: false,
        is_idle: true,
        last_seen: new Date().toISOString()
      })
      .eq('user_id', request.user.id)
      .eq('project_id', projectId)

    if (error) {
      logger.error('Failed to set presence offline', error)
      return handleAPIError(new Error('Failed to update presence'))
    }

    logger.debug('User presence set to offline', {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      message: 'Presence set to offline'
    });

  } catch (error) {
    logger.error('Error setting presence offline', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to update presence status');
  }
});